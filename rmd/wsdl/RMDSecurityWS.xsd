<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:dev="http://homecredit.net/manhattan-rmd/ws/cz/device/"
        targetNamespace="http://homecredit.net/manhattan-rmd/ws/cz/security/"
        xmlns="http://homecredit.net/manhattan-rmd/ws/cz/security/"
>

    <xs:import namespace="http://homecredit.net/manhattan-rmd/ws/cz/device/" schemaLocation="../xsd/RMD.xsd"/>

    <xs:element name="logSuccessfulLoginRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceType" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Type of device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="os" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Operation system of device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="secretType" type="dev:SecretType" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Type of secret</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="sessionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Session identifier for the logged device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ipAddress" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Devices ip address which is get from http header in MAS</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="employeeNumber" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Logged employee</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="branchClientCuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Client who tried to log into PobMA</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="logSuccessfulLoginResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="logUnsuccessfulLoginRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="secretType" type="dev:SecretType" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Type of secret</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="sessionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Session identifier for unsuccessful login</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ipAddress" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Devices ip address which is get from http header in MAS</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="employeeNumber" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Information about logged in employee</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="branchClientCuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Information about client who tried to log into PobMA</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="logUnsuccessfulLoginResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceRegistrationInfoRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idRegistration" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Registration ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="algorithmVersion" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Algorithm version, one of: R3, R4.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceRegistrationInfoResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deviceRegistrationInfo" type="dev:DeviceRegistrationInfo">
                    <xs:annotation>
                        <xs:documentation>Device registration info</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceInfoRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="idRegistration" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Registration ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceInfoResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deviceInfo" type="dev:DeviceInfo">
                    <xs:annotation>
                        <xs:documentation>Device info</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="storeServerSecretsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="masterSecretVerifier" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Master secret verifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="masterSalt" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Master salt</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="secretType" type="dev:SecretType">
                    <xs:annotation>
                        <xs:documentation>Type of secret</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="storeServerSecretsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="deleteServerSecretsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="secretType" type="dev:SecretType">
                    <xs:annotation>
                        <xs:documentation>Type of secret</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deleteServerSecretsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="unpairDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="forceDiscardPrimaryElement" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = unpairing of primary authorizing device is required, false = if device is set as primary authorizing device
                            than error code will be returned
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="forceUnpairWithActivePush" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = unpairing of primary authorizing device is required, false = if device is the only device where push
                            notifications come than error code will be returned
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="unpairDeviceResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceOperationsSettingsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceOperationsSettingsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deviceOperationsSettings" type="dev:DeviceOperationsSettings">
                    <xs:annotation>
                        <xs:documentation>Device operations settings</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceLoginDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="sessionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Session identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceLoginDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="installationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Installation identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="success" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Successfully logged</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="secretType" type="dev:SecretType" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Type of the credentials used</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="loggedIn" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Logging time</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ipAddress" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Devices ip address provided from MAS</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="isEligibleForSecuritySensitiveOperationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="installationId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Installation identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>client's id</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="operationType" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Operation type, for example: CHANGE_LIMIT</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="isEligibleForSecuritySensitiveOperationResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="isEligible" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>shows if sensitive (payment etc) operations are eligible for device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="securityOperationsGracePeriodInMinutes" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>grace period settings after sensitive (payment etc) operations are not eligible for newly created device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="securityOperationsEligibleFrom" type="xs:dateTime" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>timestamp since when sensitive (payment etc) operations will be eligible for newly created device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="installationId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>installationId of device eligibility was computed for</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="eligibilityGrantedReason" type="EligibilityReasonETO" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="EligibilityReasonETO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ELIGIBILITY_CHECK_TURNED_OFF"/>
            <xs:enumeration value="DEVICE_NOT_FOUND"/>
            <xs:enumeration value="GRACE_PERIOD"/>
            <xs:enumeration value="OWNERSHIP_CONFIRMED"/>
            <xs:enumeration value="BIOMETRIC_PROVIDED"/>
            <xs:enumeration value="CONTRACT_SIGNED_RECENTLY"/>
            <xs:enumeration value="ICC_CONTACTED_RECENTLY"/>
            <xs:enumeration value="CLIENT_WHITE_LIST"/>
            <xs:enumeration value="GRACE_PERIOD_ALLOWED_OPERATION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="confirmLastDeviceOwnershipRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>customer's id</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="confirmLastDeviceOwnershipResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="installationId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>installationId of device whose ownership has been confirmed</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="logSuccessfulDeviceAccessRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="installationId" type="xs:string"/>
                <xs:element name="sessionId" type="xs:string"/>
                <xs:element name="authId" type="xs:string" minOccurs="0"/>
                <xs:element name="authStatus" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="logSuccessfulDeviceAccessResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="DeviceAccessResult"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="logUnsuccessfulDeviceAccessRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="installationId" type="xs:string"/>
                <xs:element name="sessionId" type="xs:string"/>
                <xs:element name="authId" type="xs:string" minOccurs="0"/>
                <xs:element name="authStatus" type="xs:string" minOccurs="0"/>
                <xs:element name="authRejectReason" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="logUnsuccessfulDeviceAccessResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="DeviceAccessResult"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceAccessDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="sessionId" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceAccessDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="DeviceAccessResult"/>
                <xs:element name="authId" type="xs:string" minOccurs="0"/>
                <xs:element name="authStatus" type="xs:string" minOccurs="0"/>
                <xs:element name="authRejectReason" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="DeviceAccessResult">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OK"/>
            <xs:enumeration value="NOK"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
