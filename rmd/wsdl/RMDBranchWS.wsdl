<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:sch="http://homecredit.net/manhattan-rmd/ws/cz/branch/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:dev="http://homecredit.net/manhattan-rmd/ws/cz/device/"
                  name="rmdBranchWS"
                  targetNamespace="http://homecredit.net/manhattan-rmd/ws/cz/branch/">
    <wsdl:types>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
                   xmlns="http://homecredit.net/manhattan-rmd/ws/cz/branch/"
                   elementFormDefault="qualified"
                   targetNamespace="http://homecredit.net/manhattan-rmd/ws/cz/branch/">
            <xs:include schemaLocation="RMDBranchWS.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="generateBranchVInstallationIdRequest">
        <wsdl:part name="generateBranchVInstallationIdRequest" element="sch:generateBranchVInstallationIdRequest"/>
    </wsdl:message>
    <wsdl:message name="generateBranchVInstallationIdResponse">
        <wsdl:part name="generateBranchVInstallationIdResponse" element="sch:generateBranchVInstallationIdResponse"/>
    </wsdl:message>

    <wsdl:message name="clearBranchVInstallationIdRequest">
        <wsdl:part name="clearBranchVInstallationIdRequest" element="sch:clearBranchVInstallationIdRequest"/>
    </wsdl:message>
    <wsdl:message name="clearBranchVInstallationIdResponse">
        <wsdl:part name="clearBranchVInstallationIdResponse" element="sch:clearBranchVInstallationIdResponse"/>
    </wsdl:message>

    <wsdl:message name="RMDServiceFault">
        <wsdl:part name="fault" element="dev:RMDServiceFault"/>
    </wsdl:message>

    <wsdl:portType name="RMDBranchWS">
        <wsdl:documentation>
            Service for branch devices management.
        </wsdl:documentation>

        <wsdl:operation name="generateBranchVInstallationId">
            <wsdl:documentation>
                Creates new virtual installation for branch device. virtual id purpose is to initiate CASE SDK.
                Request: installation id of branch device
                Response: Newly generated virtual installation id
                Possible errors:
                - DEVICE_NOT_FOUND when device with given installation id wasn't found.
            </wsdl:documentation>
            <wsdl:input name="generateBranchVInstallationIdRequest" message="sch:generateBranchVInstallationIdRequest"/>
            <wsdl:output name="generateBranchVInstallationIdResponse" message="sch:generateBranchVInstallationIdResponse"/>
            <wsdl:fault name="fault" message="sch:RMDServiceFault"/>
        </wsdl:operation>

        <wsdl:operation name="clearBranchVInstallationId">
            <wsdl:documentation>
                Clears virtual installation id on branch device.
                Request: installation id of branch device
                Possible errors:
                - DEVICE_NOT_FOUND when device with given installation id wasn't found.
            </wsdl:documentation>
            <wsdl:input name="clearBranchVInstallationIdRequest" message="sch:clearBranchVInstallationIdRequest"/>
            <wsdl:output name="clearBranchVInstallationIdResponse" message="sch:clearBranchVInstallationIdResponse"/>
            <wsdl:fault name="fault" message="sch:RMDServiceFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="RMDBranchWSSOAP" type="sch:RMDBranchWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="generateBranchVInstallationId">
            <soap:operation soapAction=""/>
            <wsdl:input name="generateBranchVInstallationIdRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="generateBranchVInstallationIdResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="clearBranchVInstallationId">
            <soap:operation soapAction=""/>
            <wsdl:input name="clearBranchVInstallationIdRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="clearBranchVInstallationIdResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="RMDBranchWS">
        <wsdl:port binding="sch:RMDBranchWSSOAP" name="RMDBranchWSSOAP">
            <soap:address location="http://TO-BE-SPECIFIED/rmd/"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>