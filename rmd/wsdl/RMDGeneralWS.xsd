<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://homecredit.net/manhattan-rmd/ws/cz/general/"
           xmlns:dev="http://homecredit.net/manhattan-rmd/ws/cz/device/"
           targetNamespace="http://homecredit.net/manhattan-rmd/ws/cz/general/">

    <xs:import namespace="http://homecredit.net/manhattan-rmd/ws/cz/device/" schemaLocation="../xsd/RMD.xsd"/>

    <xs:element name="createDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuidDevice" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Customer ID who wants to register (pair) new mobile device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="cuidAuth" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Customer ID who authorizes device and who's other than the cuidDevice (e.g. a parent authorizing the pairing of a child's device)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceName" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>User defined device name</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="regKey" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>6-digit registration key</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="activeOperations" type="xs:boolean" default="true">
                    <xs:annotation>
                        <xs:documentation>Whether the device can be used for active operations. Default value is true.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="operationType" type="xs:string" default="MA_REG">
                    <xs:annotation>
                        <xs:documentation>Predefined operation type</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="initialPreviewMode" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Whether the device will be used in preview mode.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="createDeviceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="rotp" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device Registration One Time Password</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="installationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID (deviceId)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="createDeviceForApplicantRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceName" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>User defined device name</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="activeOperations" type="xs:boolean" default="true">
                    <xs:annotation>
                        <xs:documentation>Whether the device can be used for active operations. Default value is true.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="createDeviceForApplicantResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="rotp" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device Registration One Time Password</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="installationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID (deviceId)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="updateDeviceNameRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceName" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>User defined device name</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="updateDeviceNameResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="updateDeviceInfoRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Just one of "Installation ID" or "Registration ID" can be used.
                    If "Registration ID" is used, then device must be in REGISTERED state.
                </xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element minOccurs="0" name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device Installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="idRegistration" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device Registration ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceType" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device type: model of mobile phone (e.g.: GT-I9195)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="os" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Operation system on device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="appVersion" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Installed version of mobile application</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="platform" type="dev:Platform">
                    <xs:annotation>
                        <xs:documentation>Mobile device platform</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceAbilityInfo" type="dev:DeviceAbilityInfo" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Device ability info</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="devicePairingInfo" type="dev:DevicePairingInfo" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Device pairing info, specific data which identifies hardware device - android\ios hw token, etc.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="updateDeviceInfoResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDevicesRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="dev:DeviceFilter">
                    <xs:annotation>
                        <xs:documentation>Device filter</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDevicesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="device" type="dev:Device" maxOccurs="unbounded" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>List of devices</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="fullInfo" type="xs:boolean" minOccurs="0" >
                    <xs:annotation>
                        <xs:documentation>If true, detailed info about device will be returned</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="device" type="dev:Device">
                    <xs:annotation>
                        <xs:documentation>Detail of device</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDevicePairingDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDevicesPairingDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="dev:DeviceFilter"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDevicesPairingDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="devicesDetail" type="dev:DeviceDetail" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDevicePairingDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="deviceDetail" type="dev:DeviceDetail">
                    <xs:annotation>
                        <xs:documentation>Detail of device including pairing info</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="deleteDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="forceDiscardPrimaryElement" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = delete primary authorizing device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="forceUnpairWithActivePush" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = deleting of primary authorizing device is required, false = if device is the only device where push notifications come than error code will be returned</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deleteDeviceResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="checkChannelRequest">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="checkChannelResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="dev:Result">
                    <xs:annotation>
                        <xs:documentation>Result of operation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="checkCreateDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Cuid identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="checkCreateDeviceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="dev:Result">
                    <xs:annotation>
                        <xs:documentation>Result of operation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="checkCreateDeviceForApplicantRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="checkCreateDeviceForApplicantResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="dev:Result">
                    <xs:annotation>
                        <xs:documentation>Result of operation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="checkDeviceNameRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deviceName" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device name</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="checkDeviceNameResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="dev:Result">
                    <xs:annotation>
                        <xs:documentation>Result of operation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="enableDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="enableDevice" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = enable device, false = block device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="forceDiscardPrimaryElement" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>only for enableDevice = false: true = blocking of primary authorizing device is required, false = if device is set as primary authorizing device than error code will be returned</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="enableDeviceResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="activateOfflineUploadRequest">
        <xs:complexType>
            <xs:all>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ID of the device to register an upload token for.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The selected contract to register an upload token for. Can be left unfilled in case initOnly is true. RMD will then determine the appropriate contract ID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="initOnly" type="xs:boolean" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>If set to true, the activation will occur only if an upload token was never activated for this device before. If set
    to true and an upload token was already activated for this device before, the UPLOAD_TOKEN_ALREADY_INITIALIZED error is returned.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>
    <xs:element name="activateOfflineUploadResponse">
        <xs:complexType>
            <xs:all>
                <xs:element name="uploadToken" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Token generated for the authenticated user.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>The selected contract the upload token was registered for.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="verifyUploadTokenRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="uploadToken" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Token generated for the authenticated user.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="verifyUploadTokenResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Customer ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Contract ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deactivateOfflineUploadRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ID of the device to unregister an upload token for.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deactivateOfflineUploadResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="setPushNtfUsageRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pushNtfUsageAllowed" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = accept push notifications from Air Bank, false = disable push notifications from Air Bank</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="setPushNtfUsageResponse">
        <xs:complexType>
            <xs:sequence />
        </xs:complexType>
    </xs:element>

    <xs:element name="setNotificationSoundSettingsRequest">
        <xs:annotation>
            <xs:documentation>
                Request to set push notification sound settings for a mobile device.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deviceNotificationSoundSetting" type="dev:DeviceNotificationSoundSetting">
                    <xs:annotation>
                        <xs:documentation>Notification sound settings</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="setNotificationSoundSettingsResponse">
        <xs:annotation>
            <xs:documentation>
                Empty response.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:all />
        </xs:complexType>
    </xs:element>

    <xs:element name="getNotificationSoundSettingsRequest">
        <xs:annotation>
            <xs:documentation>
                Request to get push notification sound settings for mobile devices.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" name="deviceIds" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation IDs</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getNotificationSoundSettingsResponse">
        <xs:annotation>
            <xs:documentation>
                Push notification sound settings for given mobile devices.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" name="deviceNotificationSoundSetting" type="dev:DeviceNotificationSoundSettingWithDefaults">
                    <xs:annotation>
                        <xs:documentation>Notification sound settings (if any)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="assignDevicesToCustomerRequest">
        <xs:annotation>
            <xs:documentation>
                Binds devices from application to customer
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Application identifier used during the on-boarding</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Customer identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="assignDevicesToCustomerResponse">
        <xs:annotation>
            <xs:documentation>Empty response</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="initializeDevicesForLoginRequest">
        <xs:annotation>
            <xs:documentation>
                Initializes devices to be usable by the customer for login
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Customer identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="initializeDevicesForLoginResponse">
        <xs:annotation>
            <xs:documentation>Empty response</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="deleteDevicesForApplicantRequest">
        <xs:annotation>
            <xs:documentation>Delete all devices bound to application</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Application identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deleteDevicesForApplicantResponse">
        <xs:annotation>
            <xs:documentation>Empty response</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence />
        </xs:complexType>
    </xs:element>
    <xs:element name="setGooglePayAppIdRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="googlePayAppId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Google Pay App id, last  24 chars of paymentAppInstanceId of Google Pay token .</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="setGooglePayAppIdResponse">
        <xs:annotation>
            <xs:documentation>Empty response</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
    <xs:element name="findGoogleCoexistingAppRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="googlePayAppId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Google Pay App id, last  24 chars of paymentAppInstanceId of Google Pay token .</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="findGoogleCoexistingAppResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="device" type="dev:Device">
                    <xs:annotation>
                        <xs:documentation>Detail of device</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:element name="disableActiveOperationsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="disableActiveOperationsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="enableChannelRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Customer ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="enableChannel" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true = enable channel, false = block channel</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="forceDiscardPrimaryElement" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>only for enableChannel = false: true = blocking of primary authorizing device is required, false = if any device is set as primary authorizing device than error code will be returned</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="enableChannelResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="resetDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="resetDeviceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="rotp" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ROTP</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="disableConfirmationPasswordRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="disableConfirmationPasswordResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceHistoryRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="deviceHistory" type="dev:DeviceHistory">
                    <xs:annotation>
                        <xs:documentation>Device history detail</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDeviceStatusHistoryRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDeviceStatusHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="deviceStatusHistory"
                            type="dev:DeviceStatusHistory">
                    <xs:annotation>
                        <xs:documentation>Device status history detail</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getLoginHistoryRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getLoginHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="loginHistory" type="dev:DeviceLoginHistory">
                    <xs:annotation>
                        <xs:documentation>Device Login history</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="setBiometricDataProvidedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="isDataProvided" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="setBiometricDataProvidedResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="createBranchDeviceRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deviceType" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device type: model of mobile phone (e.g.: GT-I9195)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="os" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Operation system on device</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="appVersion" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Installed version of mobile application</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="platform" type="dev:Platform">
                    <xs:annotation>
                        <xs:documentation>Mobile device platform</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="threatsAvoidanceToken" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Threats avoidance token</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="createBranchDeviceResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idInstallation" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Device installation ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>
