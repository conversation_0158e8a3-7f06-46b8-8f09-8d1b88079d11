<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:fault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/sas360be/ws/campaign"
                  targetNamespace="http://airbank.cz/sas360be/ws/campaign">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/sas360be/ws/campaign">
            <xsd:include schemaLocation="../xsd/CampaignWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="GetCommunicationEntitiesRequest">
        <wsdl:part element="GetCommunicationEntitiesRequest" name="GetCommunicationEntitiesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCommunicationEntitiesResponse">
        <wsdl:part element="GetCommunicationEntitiesResponse" name="GetCommunicationEntitiesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCommunicationEntitiesFaultMessage">
        <wsdl:part element="fault:CoreFaultElement" name="GetCommunicationEntitiesFault"/>
    </wsdl:message>

    <wsdl:message name="ReceiveCommunicationEntityChangesRequest">
        <wsdl:part element="ReceiveCommunicationEntityChangesRequest" name="ReceiveCommunicationEntityChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="ReceiveCommunicationEntityChangesResponse">
        <wsdl:part element="ReceiveCommunicationEntityChangesResponse" name="ReceiveCommunicationEntityChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="ReceiveCommunicationEntityChangesFaultMessage">
        <wsdl:part element="fault:CoreFaultElement" name="ReceiveCommunicationEntityChangesFault"/>
    </wsdl:message>


    <wsdl:portType name="CampaignPort">
        <wsdl:operation name="GetCommunicationEntities">
            <wsdl:documentation>
                Get Communication entities for cuid and task version id
            </wsdl:documentation>
            <wsdl:input message="GetCommunicationEntitiesRequest" name="GetCommunicationEntitiesRequest"/>
            <wsdl:output message="GetCommunicationEntitiesResponse" name="GetCommunicationEntitiesResponse"/>
            <wsdl:fault message="GetCommunicationEntitiesFaultMessage" name="GetCommunicationEntitiesFaultMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ReceiveCommunicationEntityChanges">
            <wsdl:documentation>
                Receive communication entity changes and resend them to kafka
            </wsdl:documentation>
            <wsdl:input message="ReceiveCommunicationEntityChangesRequest" name="ReceiveCommunicationEntityChangesRequest"/>
            <wsdl:output message="ReceiveCommunicationEntityChangesResponse" name="ReceiveCommunicationEntityChangesResponse"/>
            <wsdl:fault message="ReceiveCommunicationEntityChangesFaultMessage" name="ReceiveCommunicationEntityChangesFaultMessage"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CampaignBinding" type="CampaignPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="GetCommunicationEntities">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetCommunicationEntitiesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetCommunicationEntitiesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCommunicationEntitiesFaultMessage">
                <soap:fault name="GetCommunicationEntitiesFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ReceiveCommunicationEntityChanges">
            <soap:operation soapAction=""/>
            <wsdl:input name="ReceiveCommunicationEntityChangesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ReceiveCommunicationEntityChangesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ReceiveCommunicationEntityChangesFaultMessage">
                <soap:fault name="ReceiveCommunicationEntityChangesFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="CampaignService">
        <wsdl:documentation>
            Campaign service
        </wsdl:documentation>
        <wsdl:port name="CampaignPort" binding="CampaignBinding">
            <soap:address location="http://localhost:8000/ws"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>