<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/sasadp/ws/sasnotification"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="SASNotificationWS"
                  targetNamespace="http://homecredit.net/sasadp/ws/sasnotification">
    <wsdl:documentation>v0.5 SAS-ADP receives a notification messages about data status in SAS system.</wsdl:documentation>
    <wsdl:types>
        <xsd:schema
                attributeFormDefault="unqualified"
                elementFormDefault="qualified" targetNamespace="http://homecredit.net/sasadp/ws/sasnotification/dto">
            <xsd:import namespace="http://homecredit.net/sasadp/ws/sasnotification/dto" schemaLocation="../xsd/SASNotification.xsd"/>
        </xsd:schema>
        <xsd:schema
                attributeFormDefault="unqualified"
                elementFormDefault="qualified" targetNamespace="http://homecredit.net/sasadp/ws/sasnotification">
            <xsd:import namespace="http://homecredit.net/sasadp/ws/sasnotification" schemaLocation="../xsd/SASNotificationWS.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="NotificationRequest">
        <wsdl:part element="tns:NotificationRequest" name="parameters"/>
    </wsdl:message>
    <wsdl:message name="NotificationResponse">
        <wsdl:part element="tns:NotificationResponse" name="parameters"/>
    </wsdl:message>
    <wsdl:portType name="SASNotificationWS">
        <wsdl:operation name="Notification">
            <wsdl:documentation>Notification message related to SAS data output which is relevant to SAS-ADP.
            </wsdl:documentation>
            <wsdl:input message="tns:NotificationRequest"/>
            <wsdl:output message="tns:NotificationResponse"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="SASNotificationWSSOAP" type="tns:SASNotificationWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="Notification">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="SASNotificationWS">
        <wsdl:port binding="tns:SASNotificationWSSOAP" name="SASNotificationWSSOAP">
            <soap:address location="http://localhost:8087/ws/SASNotificationWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
