<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:tns="http://homecredit.net/sasadp/ws/rtdmrevokesubjects"
           targetNamespace="http://homecredit.net/sasadp/ws/rtdmrevokesubjects"
           attributeFormDefault="unqualified"
           elementFormDefault="qualified">

    <xs:element name="RevokeSubjectsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="subject" type="tns:Subject" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Subject">
        <xs:sequence>
            <xs:element name="communicationSK" type="xs:string"/>
            <xs:element name="personId" type="xs:long"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="RevokeSubjectsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>
