<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://airbank.cz/osb/osbPropositionWS"
           xmlns:tns="http://airbank.cz/osb/osbPropositionWS"
           elementFormDefault="qualified">

    <!-- Definice elementu getMonthDetailRequest -->
    <xs:element name="getMonthDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="generalContractNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="benefitHeaderGeneralContractId" type="xs:int" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="monthYear" type="xs:gYearMonth">
                    <xs:annotation>
                        <xs:documentation>Rok a měsíc detailu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getMonthOverviewRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="generalContractNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="requiredMonthYearFrom" type="xs:gYearMonth" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="requiredMonthYearTo" type="xs:gYearMonth" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="tns:BenefitHeaderTypeTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <!-- getMonthDetailResponse -->
    <xs:element name="getMonthDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="benefitHeader" type="tns:BenefitHeaderGeneralContractTO" minOccurs="1" maxOccurs="1"/>
                <xs:element name="cardTransactionDetail" type="tns:CardTransactionDetailTO"/>
                <xs:element name="contractTurnoverDetail" type="tns:ContractTurnoverDetailTO"/>
                <xs:element name="bestBenefitHeaderConditions" type="tns:BenefitHeaderConditionTO" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- getMonthDetailResponse -->
    <xs:element name="getMonthOverviewResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="benefitPackageList" type="tns:benefitPackageTO" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <!-- getMonthDetailFault -->
    <xs:complexType name="getMonthDetailFault">
        <xs:sequence>
            <xs:element name="errorMessage" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="getMonthOverviewFault">
        <xs:sequence>
            <xs:element name="errorMessage" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <!-- BenefitHeaderGeneralContractTO  -->
    <xs:complexType name="BenefitHeaderGeneralContractTO">
        <xs:sequence>
            <xs:element name="benefitHeaderGeneralContractId" type="xs:int" minOccurs="1" maxOccurs="1"/>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="conditionGroupCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="entitledFrom" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
            <xs:element name="entitledTo" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
            <xs:element name="monthYear" type="xs:gYearMonth" minOccurs="1" maxOccurs="1"/>
            <xs:element name="evaluatedOn" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
            <xs:element name="manuallyInserted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Jde o ručně vložený záznam?</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- CardTransactionDetailTO -->
    <xs:complexType name="CardTransactionDetailTO">
        <xs:sequence>
            <xs:element name="contractTransCount" type="xs:int" minOccurs="1" maxOccurs="1"/>
            <xs:element name="contractGroupTransCount" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="threshold" type="xs:int" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>


    <!-- BenefitHeaderConditionTO -->
    <xs:complexType name="ContractTurnoverDetailTO">
        <xs:sequence>
            <xs:element name="contractTurnover" type="tns:MonetaryAmount" minOccurs="1" maxOccurs="1"/>
            <xs:element name="contractGroupTurnover" type="tns:MonetaryAmount" minOccurs="0" maxOccurs="1"/>
            <xs:element name="threshold" type="tns:MonetaryAmount" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- BenefitHeaderConditionTO -->
    <xs:complexType name="BenefitHeaderConditionTO">
        <xs:sequence>
            <xs:element name="ageCondition" type="xs:boolean" minOccurs="1" maxOccurs="1"/>
            <xs:element name="freeTrial" type="xs:boolean" minOccurs="1" maxOccurs="1"/>
            <xs:element name="campaign" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- MonetaryAmount -->
    <xs:complexType name="MonetaryAmount">
        <xs:sequence>
            <xs:element name="currency" type="tns:CurrencyCode"/>
            <xs:element name="amount" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>    
    
    <!-- CurrencyCode -->
    <xs:simpleType name="CurrencyCode">
        <xs:restriction base="xs:string">
            <xs:minLength value="3"/>
            <xs:maxLength value="3"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="BenefitHeaderTypeTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BUSINESS_SEGMENT"/>
            <xs:enumeration value="CAMPAIGN"/>
            <xs:enumeration value="OTHER"/>
        </xs:restriction>
    </xs:simpleType>
    
    <xs:complexType name="benefitPackageTO">
        <xs:sequence>
            <xs:element name="benefitHeaderGeneralContractId" type="xs:int" minOccurs="1" maxOccurs="1"/>
            <xs:element name="monthYear" type="xs:gYearMonth" minOccurs="1" maxOccurs="1"/>
            <xs:element name="benefitHeaderName" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="conditionName" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="evaluatedOn" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
            <xs:element name="entitledFrom" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
            <xs:element name="entitledTo" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>