<?xml version="1.0" encoding="UTF-8" ?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
                  xmlns="http://airbank.cz/osb/osbPropositionWS" targetNamespace="http://airbank.cz/osb/osbPropositionWS">

  <wsdl:types>
    <xsd:schema targetNamespace="http://airbank.cz/osb/osbPropositionWS">
      <xsd:include schemaLocation="osbPropositionWS.xsd" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="getMonthDetailRequest">
    <wsdl:part name="getMonthDetailRequest" element="getMonthDetailRequest"></wsdl:part>
  </wsdl:message>  
  <wsdl:message name="getMonthOverviewRequest">
    <wsdl:part name="getMonthOverviewRequest" element="getMonthOverviewRequest"></wsdl:part>
  </wsdl:message>  
  <wsdl:message name="getMonthDetailResponse">
    <wsdl:part name="getMonthDetailResponse" element="getMonthDetailResponse"></wsdl:part>
  </wsdl:message>  
  <wsdl:message name="getMonthOverviewResponse">
    <wsdl:part name="getMonthOverviewResponse" element="getMonthOverviewResponse"></wsdl:part>
  </wsdl:message>  
  <wsdl:portType name="osbPropositionWSPortType">
    <wsdl:operation name="getMonthDetail">
      <wsdl:input message="getMonthDetailRequest"/>
      <wsdl:output message="getMonthDetailResponse"/>
    </wsdl:operation>    
    <wsdl:operation name="getMonthOverview">
      <wsdl:input message="getMonthOverviewRequest"/>
      <wsdl:output message="getMonthOverviewResponse"/>
    </wsdl:operation>    
  </wsdl:portType>
  <wsdl:binding name="PropositionWSSoap11" type="osbPropositionWSPortType">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getMonthDetail">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>    
    <wsdl:operation name="getMonthOverview">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>    
  </wsdl:binding>
  <wsdl:service name="PropositionWSService">
    <wsdl:port binding="PropositionWSSoap11" name="PropositionWSSoap11">
      <soap:address location="http://TO-BE-SPECIFIED/osbPropositionWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
