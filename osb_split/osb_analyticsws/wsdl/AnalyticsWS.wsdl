<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" 
    xmlns:sch="http://airbank.cz/ib/ws/analytics/"
    xmlns:tns="http://airbank.cz/ib/ws/analytics/" 
    xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" 
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" 
    name="AnalyticsWS" 
    targetNamespace="http://airbank.cz/ib/ws/analytics/">
        
    <wsdl:types>
        <xsd:schema xmlns:analytics="http://airbank.cz/ib/ws/analytics/" targetNamespace="http://airbank.cz/ib/ws/analytics/">
            <xsd:include schemaLocation="AnalyticsWS.xsd"/>
        </xsd:schema>
    </wsdl:types>            
    
    <wsdl:message name="sendAnalyticsRequest">
        <wsdl:part element="tns:sendAnalyticsRequest" name="sendAnalyticsRequest" />
    </wsdl:message>
    <wsdl:message name="sendAnalyticsResponse">
        <wsdl:part element="tns:sendAnalyticsResponse" name="sendAnalyticsResponse" />
    </wsdl:message>
    <wsdl:portType name="AnalyticsWS">
        <wsdl:documentation>Services for sending analytics.</wsdl:documentation>
        <wsdl:operation name="sendAnalytics">
            <wsdl:documentation>Send analytics about client behaviour in internet banking.

                This operation has no defined
                error codes.
			</wsdl:documentation>
            <wsdl:input name="sendAnalyticsRequest" message="sch:sendAnalyticsRequest" />
            <wsdl:output name="sendAnalyticsResponse" message="sch:sendAnalyticsResponse" />
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="AnalyticsWSSOAP" type="tns:AnalyticsWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="sendAnalytics">
            <soap:operation soapAction="" />
            <wsdl:input name="sendAnalyticsRequest">
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output name="sendAnalyticsResponse">
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="AnalyticsWS">
        <wsdl:documentation>Services for sending analytics.</wsdl:documentation>
        <wsdl:port binding="tns:AnalyticsWSSOAP" name="AnalyticsWSSOAP">
            <soap:address location="http://localhost:9081/analytics/" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
