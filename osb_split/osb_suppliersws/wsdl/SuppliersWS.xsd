<xs:schema targetNamespace="http://osb.banka.hci/OSB/SuppliersWS" attributeFormDefault="unqualified" elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:sup="http://osb.banka.hci/OSB/Suppliers/data"
>
  <xs:import namespace="http://osb.banka.hci/OSB/Suppliers/data" schemaLocation="../xsd/Suppliers.xsd" />

  <xs:element name="findRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="contractId" type="xs:long">
          <xs:annotation>
            <xs:documentation>
              Contract (completion which created general contract)
              identification for counterparties to be returned.
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="regNumber" type="xs:string" minOccurs="0">
          <xs:annotation>
            <xs:documentation>
              Registration number for counterparties to be returned.
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="taxNumber" type="xs:string" minOccurs="0">
          <xs:annotation>
            <xs:documentation>
              Tax identification number (TIN) for counterparties to be returned.
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="name" type="xs:string" minOccurs="0">
          <xs:annotation>
            <xs:documentation>
              Name for counterparties to be returned.
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="externalId" type="xs:long" minOccurs="0">
          <xs:annotation>
            <xs:documentation>
              External counterparty (RCM) id for counterparties to be returned.
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="account" type="sup:BankAccount" minOccurs="0">
          <xs:annotation>
            <xs:documentation>
               Bank account for filtering
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="visibleOnly" type="xs:boolean" minOccurs="0">
          <xs:annotation>
            <xs:documentation>
               if false, includes invisible cp in result set
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="notActiveSubjects" type="xs:boolean" minOccurs="0" maxOccurs="1">
          <xs:annotation>
            <xs:documentation>
               if true, includes not active subjects from RCM into response. 
               Default false/NULL - only active are returned
            </xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="findResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="economicalSubjectWrapper" type="sup:EconomicalSubjectWrapper" minOccurs="0" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>Wrapper for OBS and RCM/BisNode economical subjects/counterparties</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  
  <xs:element name="getRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="counterPartyIds" type="xs:long" minOccurs="0" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>IDs of OBS counterparties</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  
  <xs:element name="getResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="economicalSubjectWrapper" type="sup:EconomicalSubjectWrapper" minOccurs="0" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>Wrapper for OBS and/or RCM/BisNode economical subjects/counterparties</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  
  <xs:element name="listRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="filter" type="sup:SuppliersFilter" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  
  <xs:element name="listResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="economicalSubjectWrapper" type="sup:EconomicalSubjectWrapper" minOccurs="0" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>Wrapper for OBS and/or RCM/BisNode economical subjects/counterparties</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  
  <xs:element name="customAndUsedRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="authorCuid" type="xs:long" minOccurs="0" maxOccurs="1">
            <xs:annotation>
                <xs:documentation>cuid of user which created custom supplier</xs:documentation>
            </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="customAndUsedResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="supplier" type="sup:EconomicalSubjectWrapperExt" minOccurs="0" maxOccurs="unbounded">
          <xs:annotation>
            <xs:documentation>supplier</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
