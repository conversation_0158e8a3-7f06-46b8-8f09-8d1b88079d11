<?xml version="1.0" encoding="UTF-8" ?>
    <!-- $Id: 41ab0005dff5af26e949e2074ac4223bf56359e7 $ -->
    <definitions targetNamespace="http://osb.abank.cz/customer/notification" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://osb.abank.cz/customer/notification" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
        <types>
            <xsd:schema targetNamespace="http://osb.abank.cz/customer/notification" elementFormDefault="qualified">
                <xsd:include schemaLocation="GpeCifNotificationWS.xsd" />

                <xsd:element name="ProcessCustomerNotificationListRequest">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="notificationList" type="tns:ModificationNotification" minOccurs="0" maxOccurs="unbounded" />
                            <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1" />
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>

                <xsd:element name="ProcessCustomerNotificationListResponse">
                    <xsd:complexType>
                    <xsd:sequence>
                    </xsd:sequence>
                </xsd:complexType>
                </xsd:element>
            </xsd:schema>
        </types>

        <message name="ProcessCustomerNotificationListRequest">
            <part name="ProcessCustomerNotificationListRequest" element="tns:ProcessCustomerNotificationListRequest" />
        </message>
        <message name="ProcessCustomerNotificationListResponse">
            <part name="ProcessCustomerNotificationListResponse" element="tns:ProcessCustomerNotificationListResponse" />
        </message>

        <portType name="CustomerNotificationListPort">
            <operation name="ProcessCustomerNotificationList">
                <input message="tns:ProcessCustomerNotificationListRequest" />
                <output message="tns:ProcessCustomerNotificationListResponse" />
            </operation>
        </portType>

        <binding name="CustomerNotificationListSoap11" type="tns:CustomerNotificationListPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <operation name="ProcessCustomerNotificationList">
                <soap:operation soapAction="" />
                <input name="ProcessCustomerNotificationListRequest">
                <soap:body use="literal" />
                </input>
                <output name="ProcessCustomerNotificationListResponse">
                    <soap:body use="literal" />
                </output>
            </operation>
        </binding>

        <service name="CustomerNotificationListService">
            <port binding="tns:CustomerNotificationListSoap11" name="CustomerNotificationListSoap11">
                <soap:address location="https://osb-int.banka.hci/ProcessCustomerNotificationListWS" />
            </port>
        </service>
    </definitions>
