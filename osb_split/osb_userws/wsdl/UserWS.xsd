<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://osb.airbank.cz/idm/UserWS/" xmlns:tns="http://osb.airbank.cz/idm/UserWS/" xmlns:idm="http://osb.airbank.cz/idm/"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

    <xsd:import schemaLocation="../xsd/OpenDJ.xsd" namespace="http://osb.airbank.cz/idm/"/>

    <xsd:element name="getAllUsersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <!-- empty request -->
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getAllUsersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="user" type="tns:User" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>All users</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getUserRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="uid" type="xsd:string" minOccurs="0"/>
                <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getUserResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="user" type="tns:User" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>user or nothing if nothing is found</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:element name="getDepartmentRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="departmentDn" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getDepartmentResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="department" type="tns:Department" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>department or nothing if nothing is found</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:element name="getEmployeeLoginTimeRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="employeeNumber" type="xsd:string" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getEmployeeLoginTimeResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="employeeLoginTime" type="tns:EmployeeLoginTime" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>actually a map with employee number and login time</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>


    <xsd:complexType name="User">
        <xsd:annotation>
            <xsd:documentation>User as a record in identity management (IDM)</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="idm:IdmRecord">
                <xsd:sequence>
                    <xsd:element name="uid" type="xsd:string" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Unique business identifier (login)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="employeeNumber" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="givenName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="surname" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="mail" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="employedSince" type="xsd:dateTime" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="employedUntil" type="xsd:dateTime" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="status" type="tns:Status" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="accountDisabled" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="activatedBy" type="idm:Reference" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="department" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="branch" type="idm:Reference" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="role" type="idm:Reference" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="meta" type="idm:Meta" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="telephoneNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="lastLoginTime" type="xsd:dateTime" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="citizenship" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="birthDate" type="xsd:date" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="birthNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    
    <xsd:complexType name="Department">
        <xsd:annotation>
            <xsd:documentation>Department as a record in identity management (IDM)</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="ou" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>Unique department identifier</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="businessCategory" type="xsd:string" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="destinationIndicator" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="facsimileTelephoneNumber" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="internationalISDNNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="localityName" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="physicalDeliveryOfficeName" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="postalAddress" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="postalCode" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>         
    </xsd:complexType>
    
    <xsd:complexType name="EmployeeLoginTime">
      <xsd:annotation>
         <xsd:documentation>map for employee (by number) and s/hes last login time</xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
        <xsd:element name="employeeNumber" type="xsd:string" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="lastLoginTime" type="xsd:dateTime" minOccurs="1" maxOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="Status">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Active"/>
            <xsd:enumeration value="Inactive"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>