<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/ws/DocumentVerificationNotification" elementFormDefault="qualified" targetNamespace="http://airbank.cz/osb/ws/DocumentVerificationNotification">


    <xs:element name="ProcessIdentificationDocumentVerificationNotificationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Přeposílá notifikace o verifikaci dodaného dokladu klienta z AMS</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="document" type="VerifiedIdentificationDocument" minOccurs="1" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessIdentificationDocumentVerificationNotificationResponse">
        <xs:complexType>
        </xs:complexType>
    </xs:element>



    <xs:complexType name="VerifiedIdentificationDocument">
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1" />
            <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="number" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1" />
            <xs:element name="issueDate" type="xs:date" minOccurs="0" maxOccurs="1" />
            <xs:element name="validTo" type="xs:date" minOccurs="0" maxOccurs="1" />
            <xs:element name="cuid" type="xs:long" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
    </xs:complexType>


</xs:schema>
