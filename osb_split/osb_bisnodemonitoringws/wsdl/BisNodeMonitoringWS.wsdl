<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/bisnode/monitoring" targetNamespace="http://airbank.cz/osb/bisnode/monitoring">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/bisnode/monitoring">
                <xsd:include schemaLocation="../xsd/BisNodeMonitoringWS.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xsd:schema>
        </wsdl:types>


        <wsdl:message name="MonitoringRequest">
            <wsdl:part element="MonitoringRequest" name="MonitoringRequest" />
        </wsdl:message>
        <wsdl:message name="MonitoringResponse">
            <wsdl:part element="MonitoringResponse" name="MonitoringResponse" />
        </wsdl:message>
        <wsdl:message name="MonitoringFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="MonitoringFault" />
        </wsdl:message>

        <wsdl:portType name="MonitoringWS">

            <wsdl:operation name="Monitoring">
                <wsdl:input message="MonitoringRequest" />
                <wsdl:output message="MonitoringResponse" />
                <wsdl:fault name="MonitoringFault" message="MonitoringFault" />
            </wsdl:operation>

        </wsdl:portType>

        <wsdl:binding name="MonitoringBinding" type="MonitoringWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="Monitoring">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="MonitoringFault">
                    <soap:fault name="MonitoringFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="MonitoringWSBinding">
            <wsdl:port binding="MonitoringBinding" name="MonitoringBinding">
                <soap:address location="/ws/MonitoringWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
