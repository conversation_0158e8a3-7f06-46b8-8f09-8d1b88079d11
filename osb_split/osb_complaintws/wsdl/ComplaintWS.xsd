<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:cl="http://airbank.cz/complaint/ws/complaintWS"
    xmlns="http://airbank.cz/complaint/ws/complaintWS"
    targetNamespace="http://airbank.cz/complaint/ws/complaintWS"
    elementFormDefault="qualified" version="1.0">
  <xsd:element name="createComplaintRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="IDContact" type="xsd:string"/>
        <xsd:element name="createdBy" type="xsd:string"  >
          <xsd:annotation>
            <xsd:documentation>employee username registered in Jira</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="createClientComplaintRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ticketName" type="xsd:string"/>
        <xsd:element name="IDContact" type="xsd:string"/>
        <xsd:element name="channel" type="xsd:string"/>
        <xsd:element name="createdBy" type="xsd:string"/>
        <xsd:element name="createdAt" type="xsd:date"/>
        <xsd:element name="description" type="xsd:string"/>        
        <xsd:element name="name" type="xsd:string"/>
        <xsd:element name="surname" type="xsd:string"/>
        <xsd:element name="cuid" type="xsd:long"/>        
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="createNonClientComplaintRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ticketName" type="xsd:string"/>
        <xsd:element name="IDContact" type="xsd:string"/>
        <xsd:element name="channel" type="xsd:string"/>
        <xsd:element name="createdBy" type="xsd:string"/>
        <xsd:element name="createdAt" type="xsd:date"/>
        <xsd:element name="description" type="xsd:string"/>
        <xsd:element name="nonClientName" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="createComplaintResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ticketID" type="xsd:string"/>
        <xsd:element name="url" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getComplaintsRequest">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="IDContact" type="xsd:string"/>
        <xsd:element name="cuid" type="xsd:long"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ComplaintBase">
    <xsd:sequence>
      <xsd:element name="assignee" type="xsd:string" minOccurs="0" />
      <xsd:element name="businessOwner" type="xsd:string" minOccurs="0" />
      <xsd:element name="type" type="xsd:string" minOccurs="0" />
      <xsd:element name="systemErrorType" type="xsd:string" minOccurs="0" />
      <xsd:element name="amount" type="xsd:string" minOccurs="0" />
      <xsd:element name="currency" type="xsd:string" minOccurs="0" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CardComplaint">
    <xsd:sequence>
      <xsd:element name="type" type="xsd:string" minOccurs="0" />
      <xsd:element name="note" type="xsd:string" minOccurs="0" />
      <xsd:element name="isCashIn" type="xsd:boolean" minOccurs="0" />
      <xsd:element name="ATMErrorType" type="xsd:string" minOccurs="0" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Solved">
    <xsd:sequence>
      <xsd:element name="branch" type="xsd:string" minOccurs="0" />
      <xsd:element name="memo" type="xsd:string" minOccurs="0" />
      <xsd:element name="accountedAt" type="xsd:date" minOccurs="0" />
      <xsd:element name="canceledAt" type="xsd:date" minOccurs="0" />
      <xsd:element name="compensationOfferType" type="xsd:string" minOccurs="0" />
      <xsd:element name="compensationAmount" type="xsd:string" minOccurs="0" />
      <xsd:element name="riskType" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>      
      <xsd:element name="authorization" type="xsd:string" minOccurs="0" />
      <xsd:element name="requiredContactDate" type="xsd:date" minOccurs="0" />
      <xsd:element name="escalationDate" type="xsd:date" minOccurs="0" />
      <xsd:element name="finishDate" type="xsd:dateTime" minOccurs="0" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Complaint">
    <xsd:sequence>
      <xsd:element name="IDContact" type="xsd:string"/>
      <xsd:element name="state" type="xsd:string"/>
      <xsd:element name="type" type="xsd:string"/>
      <xsd:element name="IDticket" type="xsd:string"/>
      <xsd:element name="relationship" type="xsd:string" minOccurs="0"/>
      <xsd:element name="cuid" type="xsd:long" minOccurs="0"/>      
      <xsd:element name="name" type="xsd:string" minOccurs="0"/>
      <xsd:element name="surname" type="xsd:string" minOccurs="0"/>
      <xsd:element name="nonClientName" type="xsd:string" minOccurs="0"/>
      <xsd:element name="RSNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="accountNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="loanNumber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="channel" type="xsd:string"/>
      <xsd:element name="branch" type="xsd:string" minOccurs="0"/>
      <xsd:element name="description" type="xsd:string"/>
      <xsd:element name="createdBy" type="xsd:string"/>
      <xsd:element name="createdAt" type="xsd:dateTime"/>
      <xsd:element name="ticketCreatedAt" type="xsd:dateTime"/>
      <xsd:element name="ticketName" type="xsd:string"/>
      <xsd:element name="url" type="xsd:anyURI" minOccurs="0" />      
      <xsd:element name="complaint" type="ComplaintBase" minOccurs="0" />      
      <xsd:element name="card" type="CardComplaint" minOccurs="0" />
      <xsd:element name="solved" type="Solved" minOccurs="0" />
      <xsd:element name="IDExternal" type="xsd:string" minOccurs="0" />
      <xsd:element name="fraud" type="xsd:boolean" minOccurs="0" />
      <xsd:element name="bankFault" type="xsd:boolean" minOccurs="0" />
      <xsd:element name="chargebackAt" type="xsd:date" minOccurs="0" />
      <xsd:element name="proplacenoZMankaASkod" type="xsd:boolean" minOccurs="0" />
      <xsd:element name="clientSatisfaction" type="xsd:string" minOccurs="0" />    
      <xsd:element name="anotherOperator" type="xsd:boolean" minOccurs="0" />
      <xsd:element name="phase" type="xsd:string" minOccurs="0" />          
      <xsd:element name="conflictType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="complaintSolvedAt" type="xsd:dateTime" minOccurs="0"/>
      <xsd:element name="riskResportedAt" type="xsd:dateTime" minOccurs="0"/>
      <xsd:element name="unauthorizedForward" type="xsd:boolean" minOccurs="0" />
      <xsd:element name="attachment" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>      
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="JiraComment">
    <xsd:sequence>
      <xsd:element name="ticketId" type="xsd:string"/>
      <xsd:element name="wmComment" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AddCommentResult">
    <xsd:sequence>
      <xsd:element name="ticketId" type="xsd:string"/>
      <xsd:element name="status" type="xsd:string"/>
      <xsd:element name="commentId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="commentUrl" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="getComplaintsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="url" type="xsd:anyURI"/>
        <xsd:element name="complaints" type="Complaint" minOccurs="0" maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getOverviewURLRequest">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="IDContact" type="xsd:string"/>
        <xsd:element name="cuid" type="xsd:long"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getOverviewURLResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="url" type="xsd:anyURI"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getDetailURLRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ticketID" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getDetailURLResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="url" type="xsd:anyURI"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="addCommentRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="comment" type="JiraComment" minOccurs="1" maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="addCommentResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="AddCommentResult" minOccurs="1" maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
