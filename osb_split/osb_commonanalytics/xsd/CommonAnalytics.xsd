<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="unqualified" 
    targetNamespace="http://osb.airbank.cz/analytics/common/"
    xmlns:tns="http://osb.airbank.cz/analytics/common/">

    <element name="collectRequest">
        <complexType>
            <sequence>
                <element name="clientId" type="string">
                    <annotation>
                        <documentation>Uniquely identifies a user instance of a web client.</documentation>
                    </annotation>
                </element>
                <element name="userId" type="string" minOccurs="0">
                    <annotation>
                        <documentation>A unique identifier for a user.</documentation>
                    </annotation>
                </element>
                <element name="timestampMicros" type="string" minOccurs="0">
                    <annotation>
                        <documentation>A Unix timestamp (in microseconds) for the time to associate with the event. This should only be set to record events that happened in the past.</documentation>
                    </annotation>
                </element>
                <element name="userProperty" type="tns:Property" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>The user properties for the measurement.</documentation>
                    </annotation>
                </element>
                <element name="nonPersonalizedAds" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>Set to true to indicate these events should not be used for personalized ads.</documentation>
                    </annotation>
                </element>
                <element name="event" type="tns:Event" maxOccurs="unbounded">
                    <annotation>
                        <documentation>An array of event items.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="collectResponse">
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>
    
    <complexType name="Event">
        <sequence>
            <element name="name" type="string">
                <annotation>
                    <documentation>The name for the event.</documentation>
                </annotation>
            </element>
            <element name="param" type="tns:Property" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>The parameters for the event.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
    
    <complexType name="Property">
        <simpleContent>
            <extension base="string">
                <attribute name="name" type="string" use="required" />
            </extension>
        </simpleContent>
    </complexType>

</schema>