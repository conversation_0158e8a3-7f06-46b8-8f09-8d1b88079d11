<definitions targetNamespace="http://osb.abank.cz/semaphoreOffer/" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://osb.abank.cz/semaphoreOffer/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
  <types>
    <xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://osb.abank.cz/semaphoreOffer/">
      <xsd:include schemaLocation="osbSemaphoreOfferWS.xsd"/>
    </xsd:schema>
  </types>
  <message name="SetSemaphoreOfferReactionRequest">
    <part element="tns:SetSemaphoreOfferReactionRequest" name="SetSemaphoreOfferReactionRequest"/>
  </message>
  <message name="SetSemaphoreOfferReactionResponse">
    <part element="tns:SetSemaphoreOfferReactionResponse" name="SetSemaphoreOfferReactionResponse"/>
  </message>
  <portType name="semaphoreOfferWS">
    <operation name="SetSemaphoreOfferReaction">
      <documentation>
                Sets semaphore offer reaction from GENESYS (WDE)

                Error codes:
                - SemaphoreOffer.DoesNotExist - semaphore offer does not exist (semaphoreOfferId was not found)
                - SemaphoreOffer.FinalState - semaphore offer is already in final state
                - SemaphoreOffer.RetentionNotAcceptInappropriateOffering - you can not set meaning INAPPROPRIATE_OFFERING for retention offer
                - Contact.DoesNotExist - contact that was called does not exist in CML
            </documentation>
      <input message="tns:SetSemaphoreOfferReactionRequest" name="SetSemaphoreOfferReactionRequest"/>
      <output message="tns:SetSemaphoreOfferReactionResponse" name="SetSemaphoreOfferReactionResponse"/>
    </operation>
  </portType>
  <binding name="SemaphoreOfferWSSoap11" type="tns:semaphoreOfferWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="SetSemaphoreOfferReaction">
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="SemaphoreOfferWSService">
    <port binding="tns:SemaphoreOfferWSSoap11" name="SemaphoreOfferWSSoap11">
      <soap:address location="http://localhost:8087/ws/SemaphoreOfferWS"/>
    </port>
  </service>
</definitions>