<?xml version="1.0" encoding="UTF-8" ?>
<wsdl:definitions targetNamespace="http://mdes.abank.cz/mpamanagement/" xmlns="http://mdes.abank.cz/mpamanagement/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://mdes.abank.cz/mpamanagement/"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
  <wsdl:types>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:import namespace="http://mdes.abank.cz/mpamanagement/" schemaLocation="MPAManagementWS.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="reqisterRequest">
    <wsdl:part name="registerRequest" element="RegisterRequest"/>
  </wsdl:message>
  <wsdl:message name="registerResponse">
    <wsdl:part name="registerResponse" element="RegisterResponse"/>
  </wsdl:message>
  <wsdl:message name="pkCertificateRequest">
    <wsdl:part name="pkCertificateRequest" element="PkCertificateRequest"/>
  </wsdl:message>
  <wsdl:message name="pkCertificateResponse">
    <wsdl:part name="pkCertificateResponse" element="PkCertificateResponse"/>
  </wsdl:message>
  <wsdl:message name="setMobilePinRequest">
    <wsdl:part name="setMobilePinRequest" element="SetMobilePinRequest"/>
  </wsdl:message>
  <wsdl:message name="setMobilePinResponse">
    <wsdl:part name="setMobilePinResponse" element="SetMobilePinResponse"/>
  </wsdl:message>
  <wsdl:message name="unregisterRequest">
    <wsdl:part name="unregisterRequest" element="UnregisterRequest"/>
  </wsdl:message>
  <wsdl:message name="unregisterResponse">
    <wsdl:part name="unregisterResponse" element="UnregisterResponse"/>
  </wsdl:message>
  <wsdl:portType name="MPAManagementWS">
    <wsdl:operation name="register">
      <wsdl:input message="reqisterRequest"/>
      <wsdl:output message="registerResponse"/>
    </wsdl:operation>
    <wsdl:operation name="pkCertificate">
      <wsdl:input message="pkCertificateRequest"/>
      <wsdl:output message="pkCertificateResponse"/>
    </wsdl:operation>
    <wsdl:operation name="setMobilePin">
      <wsdl:input message="setMobilePinRequest"/>
      <wsdl:output message="setMobilePinResponse"/>
    </wsdl:operation>
    <wsdl:operation name="unregister">
      <wsdl:input message="unregisterRequest"/>
      <wsdl:output message="unregisterResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="MPAManagementWS-binding" type="MPAManagementWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="register">
      <soap:operation soapAction="register"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="pkCertificate">
      <soap:operation soapAction="pkCertificate"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setMobilePin">
      <soap:operation soapAction="setMobilePin"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="unregister">
      <soap:operation soapAction="unregister"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="MPAManagementWS">
    <wsdl:port binding="MPAManagementWS-binding" name="MPAManagementWSSoap11">
      <soap:address location="http://TO-BE-SPECIFIED/MPAManagementWS/"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>