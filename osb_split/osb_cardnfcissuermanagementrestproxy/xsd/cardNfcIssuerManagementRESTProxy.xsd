<?xml version = '1.0' encoding = 'UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://osb.airbank.cz/mdes/card/nfc/issuer"
            targetNamespace="http://osb.airbank.cz/mdes/card/nfc/issuer" elementFormDefault="qualified"
            xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" 
            xmlns:dig="http://osb.abank.cz/digitization/rest"
            xmlns:com="http://osb.abank.cz/mdes/rest/common"
            nxsd:encoding="UTF-8">
  <xsd:import schemaLocation="digitization.xsd" namespace="http://osb.abank.cz/digitization/rest"/>
  <xsd:import schemaLocation="mdes_common.xsd" namespace="http://osb.abank.cz/mdes/rest/common"/>
  
  <xsd:element name="authorizeServiceRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requestId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseHost" type="xsd:string"  minOccurs="0"/>
                <xsd:element name="services" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="cardInfo" type="com:CardInfo"/>
                <xsd:element name="correlationId" type="xsd:string"/>
                <xsd:element name="tokenRequestorId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="walletId" type="xsd:string"/>
                <xsd:element name="paymentAppInstanceId" type="xsd:string"/>
                <xsd:element name="accountIdHash" type="xsd:base64Binary" minOccurs="0"/>
                <xsd:element name="mobileNumberSuffix" type="xsd:string" minOccurs="0"/>
                <xsd:element name="deviceInfo" type="dig:DeviceInfo" minOccurs="0"/>
                <xsd:element name="walletProviderDecisioningInfo" type="WalletProviderDecisioningInfo" minOccurs="0"/>
                <xsd:element name="activeTokenCount" type="xsd:string" minOccurs="0"/>
                <xsd:element name="tokenType" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="authorizeServiceResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="responseHost" type="xsd:string"  minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string"/>
                <xsd:element name="errorCode" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorDescription" type="xsd:string" minOccurs="0"/>
                <xsd:element name="services" type="xsd:string" maxOccurs="unbounded"/>
                <xsd:element name="decision" type="xsd:string"/>
                <xsd:element name="activationMethods" type="ActivationMethod" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="panSequenceNumber" type="xsd:string" minOccurs="0"/>
                <xsd:element name="issuerProductConfigId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="encryptedPayload" type="com:EncryptedPayload" minOccurs="0"/>
                <xsd:element name="cvcResponse" type="xsd:string" minOccurs="0"/>
                <xsd:element name="avsResponse" type="xsd:string" minOccurs="0"/>
                <xsd:element name="tokenRequestorId" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:element name="notifyServiceActivatedRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requestId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseHost" type="xsd:string"  minOccurs="0"/>
                <xsd:element name="cardAndToken" type="com:CardInfo" minOccurs="0"/>
                <xsd:element name="deviceInfo" type="dig:DeviceInfo" minOccurs="0"/>
                <xsd:element name="correlationId" type="xsd:string"/>
                <xsd:element name="tokenRequestorId" type="xsd:string"/>
                <xsd:element name="walletId" type="xsd:string"/>
                <xsd:element name="paymentAppInstanceId" type="xsd:string"/>
                <xsd:element name="accountPanSuffix" type="xsd:string"/>
		<xsd:element name="secureElementId" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="notifyServiceActivatedResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorCode" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorDescription" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:element name="notifyTokenUpdatedRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requestId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
                <xsd:element name="tokens" type="Token" maxOccurs="unbounded"/>
                <xsd:element name="reasonCode" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="notifyTokenUpdatedResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorCode" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorDescription" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:element name="deliverActivationCodeRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requestId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
                <xsd:element name="tokenUniqueReference" type="xsd:string"/>
                <xsd:element name="correlationId" type="xsd:string"/>
                <xsd:element name="activationCode" type="xsd:string" minOccurs="0"/>
                <xsd:element name="expirationDateTime" type="xsd:dateTime" minOccurs="0"/>
                <xsd:element name="activationMethod" type="ActivationMethod"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="deliverActivationCodeResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
                <xsd:element name="responseId" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorCode" type="xsd:string" minOccurs="0"/>
                <xsd:element name="errorDescription" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:complexType name="WalletProviderDecisioningInfo">
        <xsd:sequence>
            <xsd:element name="recommendedDecision" type="xsd:string" minOccurs="0"/>
            <xsd:element name="recommendationStandardVersion" type="xsd:string" minOccurs="0"/>
            <xsd:element name="deviceScore" type="xsd:string" minOccurs="0"/>
            <xsd:element name="accountScore" type="xsd:string" minOccurs="0"/>
            <xsd:element name="phoneNumberScore" type="xsd:string" minOccurs="0"/>
            <xsd:element name="recommendationReasons" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ActivationMethod">
        <xsd:sequence>
            <xsd:element name="type" type="xsd:string"/>
            <xsd:element name="value" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="Token">
        <xsd:sequence>
            <xsd:element name="tokenUniqueReference" type="xsd:string"/>
            <xsd:element name="status" type="xsd:string"/>
            <xsd:element name="suspendedBy" minOccurs="0" maxOccurs="unbounded" type="xsd:string"/>
            <xsd:element name="tokenExpiry" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>
    
  <xsd:annotation xmlns="">
    <xsd:appinfo>NXSDSAMPLE=</xsd:appinfo>
    <xsd:appinfo>USEHEADER=false</xsd:appinfo>
  </xsd:annotation>
</xsd:schema>
