<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/bisnode/subscription" targetNamespace="http://airbank.cz/osb/bisnode/subscription">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/bisnode/subscription">
                <xsd:include schemaLocation="../xsd/BisNodeSubscriptionWS.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xsd:schema>
        </wsdl:types>


        <wsdl:message name="SubscribeRequest">
            <wsdl:part element="SubscribeRequest" name="SubscribeRequest" />
        </wsdl:message>
        <wsdl:message name="SubscribeResponse">
            <wsdl:part element="SubscribeResponse" name="SubscribeResponse" />
        </wsdl:message>
        <wsdl:message name="SubscribeFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="SubscribeFault" />
        </wsdl:message>

        <wsdl:message name="UnsubscribeRequest">
            <wsdl:part element="UnsubscribeRequest" name="UnsubscribeRequest" />
        </wsdl:message>
        <wsdl:message name="UnsubscribeResponse">
            <wsdl:part element="UnsubscribeResponse" name="UnsubscribeResponse" />
        </wsdl:message>
        <wsdl:message name="UnsubscribeFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="UnsubscribeFault" />
        </wsdl:message>

        <wsdl:message name="GetMembersRequest">
            <wsdl:part element="GetMembersRequest" name="GetMembersRequest" />
        </wsdl:message>
        <wsdl:message name="GetMembersResponse">
            <wsdl:part element="GetMembersResponse" name="GetMembersResponse" />
        </wsdl:message>
        <wsdl:message name="GetMembersFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="GetMembersFault" />
        </wsdl:message>

        <wsdl:portType name="SubscriptionWS">

            <wsdl:operation name="Subscribe">
                <wsdl:input message="SubscribeRequest" />
                <wsdl:output message="SubscribeResponse" />
                <wsdl:fault name="SubscribeFault" message="SubscribeFault" />
            </wsdl:operation>

            <wsdl:operation name="Unsubscribe">
                <wsdl:input message="UnsubscribeRequest" />
                <wsdl:output message="UnsubscribeResponse" />
                <wsdl:fault name="UnsubscribeFault" message="UnsubscribeFault" />
            </wsdl:operation>

            <wsdl:operation name="GetMembers">
                <wsdl:input message="GetMembersRequest" />
                <wsdl:output message="GetMembersResponse" />
                <wsdl:fault name="GetMembersFault" message="GetMembersFault" />
            </wsdl:operation>

        </wsdl:portType>

        <wsdl:binding name="SubscriptionBinding" type="SubscriptionWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="Subscribe">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="SubscribeFault">
                    <soap:fault name="SubscribeFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="Unsubscribe">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="UnsubscribeFault">
                    <soap:fault name="UnsubscribeFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="GetMembers">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="GetMembersFault">
                    <soap:fault name="GetMembersFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="SubscriptionWSBinding">
            <wsdl:port binding="SubscriptionBinding" name="SubscriptionBinding">
                <soap:address location="/ws/SubscriptionWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
