<?xml version = '1.0' encoding = 'UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:cns="http://airbank.cz/osb/openapi/accessManagement" 
targetNamespace="http://airbank.cz/osb/openapi/accessManagement" elementFormDefault="qualified" xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" 
nxsd:version="JSON" nxsd:jsonTopLevelArray="true" nxsd:encoding="UTF-8">
   <xsd:element name="getProductApprovedApplicationsRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="code" type="xsd:string" minOccurs="1"></xsd:element>
            <xsd:element name="limit" type="xsd:int" minOccurs="0"></xsd:element>
            <xsd:element name="filter" type="xsd:string" minOccurs="0"></xsd:element>
            <xsd:element name="sort" type="xsd:string" minOccurs="0"></xsd:element>
          </xsd:sequence>      
        </xsd:complexType>
      </xsd:element>
    <xsd:element name="getProductApprovedApplicationsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="application" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="id" type="xsd:long" minOccurs="1"></xsd:element>
                            <xsd:element name="name" type="xsd:string" minOccurs="1"></xsd:element>
                            <xsd:element name="description" type="xsd:string" minOccurs="0"></xsd:element>
                            <xsd:element name="active" type="xsd:boolean" minOccurs="0"></xsd:element>
                            <xsd:element name="developerId" type="xsd:long" minOccurs="0"></xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element name="total" type="xsd:integer"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>