<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="https://www.airbank.cz/travelInsurance/v1/travelInsurance"
                  targetNamespace="https://www.airbank.cz/travelInsurance/v1/travelInsurance">

    <wsdl:types>
        <xs:schema targetNamespace="https://www.airbank.cz/travelInsurance/v1/travelInsurance"
                   xmlns="https://www.airbank.cz/travelInsurance/v1/travelInsurance">
            <xs:include schemaLocation="TravelInsurance.xsd"/>
            <xs:element name="createInsuranceRequest" type="TravelInsurance"/>
            <xs:element name="createInsuranceResult" type="DeliverySuccess"/>
            <xs:element name="faultElement" type="FaultResult"/>
            <xs:element name="cancelInsuranceRequest" type="TravelInsuranceCancellation"/>
            <xs:element name="cancelInsuranceResult" type="DeliverySuccess"/>


        </xs:schema>
    </wsdl:types>

    <wsdl:message name="CreateInsuranceRequest">
        <wsdl:part element="createInsuranceRequest" name="CreateInsuranceRequest"/>
    </wsdl:message>
    <wsdl:message name="CreateInsuranceResponse">
        <wsdl:part element="createInsuranceResult" name="CreateInsuranceResult"/>
    </wsdl:message>
    <wsdl:message name="CreateInsuranceFault">
        <wsdl:part element="faultElement" name="CreateInsuranceFault"/>
    </wsdl:message>
    
    <wsdl:message name="CancelInsuranceRequest">
        <wsdl:part element="cancelInsuranceRequest" name="CancelInsuranceRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelInsuranceResponse">
        <wsdl:part element="cancelInsuranceResult" name="CancelInsuranceResult"/>
    </wsdl:message>
    <wsdl:message name="CancelInsuranceFault">
        <wsdl:part element="faultElement" name="CancelInsuranceFault"/>
    </wsdl:message>

    <wsdl:portType name="TravelInsuranceServicePort">

        <wsdl:operation name="createInsurance">
            <wsdl:documentation>
                Creates signed insurance in insurance company.
            </wsdl:documentation>
            <wsdl:input message="CreateInsuranceRequest"/>
            <wsdl:output message="CreateInsuranceResponse"/>
            <wsdl:fault name="CreateInsuranceFault" message="CreateInsuranceFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="cancelInsurance">
            <wsdl:documentation>
                Cancels insurance in insurance company.
            </wsdl:documentation>
            <wsdl:input message="CancelInsuranceRequest"/>
            <wsdl:output message="CancelInsuranceResponse"/>
            <wsdl:fault name="CancelInsuranceFault" message="CancelInsuranceFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="TravelInsuranceServiceBinding" type="TravelInsuranceServicePort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="createInsurance">
          <soap:operation soapAction=""/>                            
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CreateInsuranceFault">
                <soap:fault name="CreateInsuranceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="cancelInsurance">
          <soap:operation soapAction="EnqueueRequest"/>                        
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelInsuranceFault">
                <soap:fault name="CancelInsuranceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="TravelInsuranceService">
        <wsdl:documentation>Synchronizes new travel insurances and changes to travel insurances to insurance company</wsdl:documentation>
        <wsdl:port name="TravelInsuranceServicePort" binding="TravelInsuranceServiceBinding">
            <!-- default localhost addr -->
            <soap:address location="http://localhost:8090/ws/"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
