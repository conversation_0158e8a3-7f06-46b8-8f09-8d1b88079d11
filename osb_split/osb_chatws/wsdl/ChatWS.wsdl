<?xml version="1.0" encoding="UTF-8" ?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://osb.airbank.cz/addai/ws/chat" targetNamespace="http://osb.airbank.cz/addai/ws/chat">

        <wsdl:types>
            <xsd:schema targetNamespace="http://osb.airbank.cz/addai/ws/chat">
                <xsd:include schemaLocation="ChatWS.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="initSessionV1Request">
            <wsdl:part element="initSessionV1Request" name="initSessionV1Request" />
        </wsdl:message>
        <wsdl:message name="initSessionV1Response">
            <wsdl:part element="initSessionV1Response" name="initSessionV1Response" />
        </wsdl:message>
        <wsdl:message name="initSessionV1Fault">
            <wsdl:part element="errorList" name="initSessionV1Fault" />
        </wsdl:message>

        <wsdl:message name="processMessageV1Request">
            <wsdl:part element="processMessageV1Request" name="processMessageV1Request" />
        </wsdl:message>
        <wsdl:message name="processMessageV1Response">
            <wsdl:part element="processMessageV1Response" name="processMessageV1Response" />
        </wsdl:message>
        <wsdl:message name="processMessageV1Fault">
            <wsdl:part element="errorList" name="processMessageV1Fault" />
        </wsdl:message>

        <wsdl:message name="keepSessionV1Request">
            <wsdl:part element="keepSessionV1Request" name="keepSessionV1Request" />
        </wsdl:message>
        <wsdl:message name="keepSessionV1Response">
            <wsdl:part element="keepSessionV1Response" name="keepSessionV1Response" />
        </wsdl:message>
        <wsdl:message name="keepSessionV1Fault">
            <wsdl:part element="errorList" name="keepSessionV1Fault" />
        </wsdl:message>

        <wsdl:message name="getConfigV1Request">
            <wsdl:part element="getConfigV1Request" name="getConfigV1Request" />
        </wsdl:message>
        <wsdl:message name="getConfigV1Response">
            <wsdl:part element="getConfigV1Response" name="getConfigV1Response" />
        </wsdl:message>
        <wsdl:message name="getConfigV1Fault">
            <wsdl:part element="errorList" name="getConfigV1Fault" />
        </wsdl:message>

        <wsdl:message name="initIdentifiedSessionV1Request">
            <wsdl:part element="initIdentifiedSessionV1Request" name="initIdentifiedSessionV1Request" />
        </wsdl:message>
        <wsdl:message name="initIdentifiedSessionV1Response">
            <wsdl:part element="initIdentifiedSessionV1Response" name="initIdentifiedSessionV1Response" />
        </wsdl:message>
        <wsdl:message name="initIdentifiedSessionV1Fault">
            <wsdl:part element="errorList" name="initIdentifiedSessionV1Fault" />
        </wsdl:message>

        <wsdl:message name="closeSessionV1Request">
            <wsdl:part element="closeSessionV1Request" name="closeSessionV1Request" />
        </wsdl:message>
        <wsdl:message name="closeSessionV1Response">
            <wsdl:part element="closeSessionV1Response" name="closeSessionV1Response" />
        </wsdl:message>
        <wsdl:message name="closeSessionV1Fault">
            <wsdl:part element="errorList" name="closeSessionV1Fault" />
        </wsdl:message>


        <wsdl:portType name="ChatWSPort">

            <wsdl:operation name="initSessionV1">
                <wsdl:input message="initSessionV1Request" />
                <wsdl:output message="initSessionV1Response" />
                <wsdl:fault message="initSessionV1Fault" name="initSessionV1Fault" />
            </wsdl:operation>

            <wsdl:operation name="processMessageV1">
                <wsdl:input message="processMessageV1Request" />
                <wsdl:output message="processMessageV1Response" />
                <wsdl:fault message="processMessageV1Fault" name="processMessageV1Fault" />
            </wsdl:operation>

            <wsdl:operation name="keepSessionV1">
                <wsdl:input message="keepSessionV1Request" />
                <wsdl:output message="keepSessionV1Response" />
                <wsdl:fault message="keepSessionV1Fault" name="keepSessionV1Fault" />
            </wsdl:operation>

            <wsdl:operation name="getConfigV1">
                <wsdl:input message="getConfigV1Request" />
                <wsdl:output message="getConfigV1Response" />
                <wsdl:fault message="getConfigV1Fault" name="getConfigV1Fault" />
            </wsdl:operation>

            <wsdl:operation name="initIdentifiedSessionV1">
                <wsdl:input message="initIdentifiedSessionV1Request" />
                <wsdl:output message="initIdentifiedSessionV1Response" />
                <wsdl:fault message="initIdentifiedSessionV1Fault" name="initIdentifiedSessionV1Fault" />
            </wsdl:operation>

            <wsdl:operation name="closeSessionV1">
                <wsdl:input message="closeSessionV1Request" />
                <wsdl:output message="closeSessionV1Response" />
                <wsdl:fault message="closeSessionV1Fault" name="closeSessionV1Fault" />
            </wsdl:operation>

        </wsdl:portType>

        <wsdl:binding name="ChatWSBinding" type="ChatWSPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="initSessionV1">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="initSessionV1Fault">
                    <soap:fault use="literal" name="initSessionV1Fault" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="processMessageV1">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="processMessageV1Fault">
                    <soap:fault use="literal" name="processMessageV1Fault" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="keepSessionV1">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="keepSessionV1Fault">
                    <soap:fault use="literal" name="keepSessionV1Fault" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="getConfigV1">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="getConfigV1Fault">
                    <soap:fault use="literal" name="getConfigV1Fault" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="initIdentifiedSessionV1">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="initIdentifiedSessionV1Fault">
                    <soap:fault use="literal" name="initIdentifiedSessionV1Fault" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="closeSessionV1">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="closeSessionV1Fault">
                    <soap:fault use="literal" name="closeSessionV1Fault" />
                </wsdl:fault>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="ChatWS">
            <wsdl:port binding="ChatWSBinding" name="ChatWSBinding" />
        </wsdl:service>
    </wsdl:definitions>
