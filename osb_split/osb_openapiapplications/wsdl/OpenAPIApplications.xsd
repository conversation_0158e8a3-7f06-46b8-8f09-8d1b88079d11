<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://homecredit.eu/openapi/applications"
           targetNamespace="http://homecredit.eu/openapi/applications">
  <xs:annotation>
    <xs:documentation>Calendar manager</xs:documentation>
  </xs:annotation>
  <xs:element name="getProductApprovedApplicationsRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="code" type="xs:string" minOccurs="1"></xs:element>
        <xs:element name="limit" type="xs:int" minOccurs="0"></xs:element>
        <xs:element name="filter" type="xs:string" minOccurs="0"></xs:element>
        <xs:element name="sort" type="xs:string" minOccurs="0"></xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getProductApprovedApplicationsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="application" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="id" type="xs:long" minOccurs="1"></xs:element>
              <xs:element name="name" type="xs:string" minOccurs="1"></xs:element>
              <xs:element name="description" type="xs:string" minOccurs="0"></xs:element>
              <xs:element name="active" type="xs:boolean" minOccurs="0"></xs:element>
              <xs:element name="developerId" type="xs:long" minOccurs="0"></xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="total" type="xs:integer"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="simpleFault">
    <xs:complexType>
      <xs:annotation>
        <xs:documentation>common http fault</xs:documentation>
      </xs:annotation>
      <xs:sequence>
        <xs:element name="status_code" type="xs:unsignedShort" minOccurs="1">
          <xs:annotation>
            <xs:documentation>http status code</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="body" type="xs:string" minOccurs="0">
          <xs:annotation>
            <xs:documentation>http response body</xs:documentation>
          </xs:annotation>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
