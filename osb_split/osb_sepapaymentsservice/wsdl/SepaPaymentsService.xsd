<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://airbank.cz/sepa/payment/" xmlns:tns="http://airbank.cz/sepa/payment/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" xmlns:S2SCTIcf="urn:S2SCTIcf:xsd:$SCTIcfBlkCredTrf" xmlns:S2SCTIqf="urn:S2SCTIqf:xsd:$SCTIqfBlkCredTrf" xmlns:S2SCTOqf="urn:S2SCTOqf:xsd:$SCTOqfBlkCredTrf">

	<xsd:import schemaLocation="../xsd/pacsEnvelope.xsd" namespace="urn:S2SCTIcf:xsd:$SCTIcfBlkCredTrf" />
	<xsd:import schemaLocation="../xsd/SCTIqfBlkCredTrf.xsd" namespace="urn:S2SCTIqf:xsd:$SCTIqfBlkCredTrf" />
	<xsd:import schemaLocation="../xsd/SCTOqfBlkCredTrf.xsd" namespace="urn:S2SCTOqf:xsd:$SCTOqfBlkCredTrf" />


	<xsd:element name="SendSepaMessageRequest">
		<xsd:complexType>
			<xsd:sequence>				
				<xsd:element ref="S2SCTIcf:SCTIcfBlkCredTrf" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendSepaMessageResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="SendSepaFileRequest">
		<xsd:complexType>
			<xsd:sequence>				
				<xsd:element name="fileContent" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendSepaFileResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	
	<xsd:element name="ReceiveSepaMessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
				<xsd:element ref="S2SCTIcf:SCTIcfBlkCredTrf" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
  <xsd:element name="ReceiveSepaMessageResponse">
      <xsd:complexType>
          <xsd:sequence>

          </xsd:sequence>
      </xsd:complexType>
  </xsd:element>

	<xsd:element name="ReceiveSepaIqfMessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
				<xsd:element ref="S2SCTIqf:SCTIqfBlkCredTrf" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveSepaIqfMessageResponse">
		<xsd:complexType>
			<xsd:sequence>

			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>


	<xsd:element name="ReceiveSepaOqfMessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
				<xsd:element ref="S2SCTOqf:SCTOqfBlkCredTrf" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveSepaOqfMessageResponse">
		<xsd:complexType>
			<xsd:sequence>

			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

</xsd:schema>
