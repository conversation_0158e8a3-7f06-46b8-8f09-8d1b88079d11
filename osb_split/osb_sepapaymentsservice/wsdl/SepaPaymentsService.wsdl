<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions name="SepaPaymentService" targetNamespace="http://airbank.cz/sepa/payment/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://airbank.cz/sepa/payment/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsdl:documentation>
		Web Service for send and receive sepa payments messages.

		OBS provide ReceiveSepaMessage operation and OSB calls this service when sepa message ocur in ftp server.
		OBS calls SendSepaMessage operation when want to send sepa message to sepa provider.
	</wsdl:documentation>
	<wsdl:types>
		<xsd:schema targetNamespace="http://airbank.cz/sepa/payment/" elementFormDefault="qualified">
			<xsd:include schemaLocation="SepaPaymentsService.xsd" />
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="ReceiveSepaMessageRequest">
		<wsdl:part element="tns:ReceiveSepaMessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveSepaMessageResponse">
		<wsdl:part element="tns:ReceiveSepaMessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveSepaIqfMessageRequest">
		<wsdl:part element="tns:ReceiveSepaIqfMessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveSepaIqfMessageResponse">
		<wsdl:part element="tns:ReceiveSepaIqfMessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveSepaOqfMessageRequest">
		<wsdl:part element="tns:ReceiveSepaOqfMessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveSepaOqfMessageResponse">
		<wsdl:part element="tns:ReceiveSepaOqfMessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendSepaMessageRequest">
		<wsdl:part element="tns:SendSepaMessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendSepaMessageResponse">
		<wsdl:part element="tns:SendSepaMessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendSepaFileRequest">
		<wsdl:part element="tns:SendSepaFileRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendSepaFileResponse">
		<wsdl:part element="tns:SendSepaFileResponse" name="parameters" />
	</wsdl:message>
	<wsdl:portType name="SepaPaymentService">
		<wsdl:operation name="SendSepaMessage">
			<wsdl:input message="tns:SendSepaMessageRequest" />
			<wsdl:output message="tns:SendSepaMessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="SendSepaFile">
			<wsdl:input message="tns:SendSepaFileRequest" />
			<wsdl:output message="tns:SendSepaFileResponse" />
		</wsdl:operation>
		<wsdl:operation name="ReceiveSepaMessage">
			<wsdl:input message="tns:ReceiveSepaMessageRequest" />
			<wsdl:output message="tns:ReceiveSepaMessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="ReceiveSepaIqfMessage">
			<wsdl:input message="tns:ReceiveSepaIqfMessageRequest" />
			<wsdl:output message="tns:ReceiveSepaIqfMessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="ReceiveSepaOqfMessage">
			<wsdl:input message="tns:ReceiveSepaOqfMessageRequest" />
			<wsdl:output message="tns:ReceiveSepaOqfMessageResponse" />
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="SepaPaymentServiceSOAP" type="tns:SepaPaymentService">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="SendSepaMessage">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="SendSepaFile">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReceiveSepaMessage">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReceiveSepaIqfMessage">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReceiveSepaOqfMessage">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>

	</wsdl:binding>
	<wsdl:service name="SepaPaymentService">
		<wsdl:port binding="tns:SepaPaymentServiceSOAP" name="SepaPaymentServiceSOAP">
			<soap:address location="https://localhost:7002/" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>