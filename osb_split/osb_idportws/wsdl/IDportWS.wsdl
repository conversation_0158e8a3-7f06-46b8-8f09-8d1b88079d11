<?xml version="1.0" encoding="UTF-8" ?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://airbank.cz/IDport/ws/IDportWS" targetNamespace="http://airbank.cz/IDport/ws/IDportWS">

        <wsdl:types>
            <xsd:schema>
                <xsd:import namespace="http://airbank.cz/IDport/ws/IDportWS" schemaLocation="../xsd/IDport.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="getConsentsRequest">
            <wsdl:part element="getConsentsRequest" name="getConsentsRequest" />
        </wsdl:message>
        <wsdl:message name="getConsentsResponse">
            <wsdl:part element="getConsentsResponse" name="getConsentsResponse" />
        </wsdl:message>
        <wsdl:message name="getConsentsError">
            <wsdl:part element="getConsentsError" name="getConsentsError" />
        </wsdl:message>

        <wsdl:message name="removeConsentRequest">
            <wsdl:part element="removeConsentRequest" name="removeConsentRequest" />
        </wsdl:message>
        <wsdl:message name="removeConsentResponse">
            <wsdl:part element="removeConsentResponse" name="removeConsentResponse" />
        </wsdl:message>
        <wsdl:message name="removeConsentError">
            <wsdl:part element="removeConsentError" name="removeConsentError" />
        </wsdl:message>

        <wsdl:message name="notifyRequest">
            <wsdl:part element="notifyRequest" name="notifyRequest" />
        </wsdl:message>
        <wsdl:message name="notifyResponse">
            <wsdl:part element="notifyResponse" name="notifyResponse" />
        </wsdl:message>
        <wsdl:message name="notifyError">
            <wsdl:part element="notifyError" name="notifyError" />
        </wsdl:message>

        <wsdl:portType name="IDportWS">
            <wsdl:operation name="getConsents">
                <wsdl:input message="getConsentsRequest" />
                <wsdl:output message="getConsentsResponse" />
                <wsdl:fault message="getConsentsError" name="getConsentsError" />
            </wsdl:operation>

            <wsdl:operation name="removeConsent">
                <wsdl:input message="removeConsentRequest" />
                <wsdl:output message="removeConsentResponse" />
                <wsdl:fault message="removeConsentError" name="removeConsentError" />
            </wsdl:operation>

            <wsdl:operation name="notify">
                <wsdl:input message="notifyRequest" />
                <wsdl:output message="notifyResponse" />
                <wsdl:fault message="notifyError" name="notifyError" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="IDportWSBinding" type="IDportWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="getConsents">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="getConsentsError">
                    <soap:fault use="literal" name="getConsentsError" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="removeConsent">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="removeConsentError">
                    <soap:fault use="literal" name="removeConsentError" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="notify">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="notifyError">
                    <soap:fault use="literal" name="notifyError" />
                </wsdl:fault>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="IDportWS">
            <wsdl:port binding="IDportWSBinding" name="IDportWSBinding">
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
