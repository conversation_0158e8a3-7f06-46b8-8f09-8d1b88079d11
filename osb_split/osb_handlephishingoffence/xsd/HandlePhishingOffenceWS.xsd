<?xml version="1.0" encoding="UTF-8"?>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://osb.abank.cz/osb/ws/HandlePhishingOffence" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://osb.abank.cz/osb/ws/HandlePhishingOffence" jxb:version="2.1" elementFormDefault="qualified">

        <xs:simpleType name="BusinessProcessEvent">
            <xs:annotation>
                <xs:documentation>Enumerace definující podmnožinu hodnot z MDM číselníku BusinessProcessEvent.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="ANTIPHISHING_HACKER_LOGGEDIN_KNOWN_IP">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Úspěšné přihlášení útočníka do IB z evidované IP adresy.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="ANTIPHISHING_HACKER_NOT_LOGGEDIN_VALID_USERNAME_KNOWN_IP">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Neúspěšné přihlášení útočníka s platným userName do IB z evidované IP adresy</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="ANTIPHISHING_MANUAL_BLOCKING_BY_CLIENT_CALL">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Obsluha rozhodla o blokaci přístupu klienta do banky na základě volání klienta s podezřením na útok hackera (phishing)</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="ANTIPHISHING_MANUAL_BLOCKING_BY_ALERT">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Obsluha manuálně rozhodla o blokaci přístupu klienta automatickém upozornění na útok hackera (phishing).</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>

                <xs:enumeration value="ANTIPHISHING_HACKER_LOGIN_REJECTED_KNOWN_IP">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc></jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>

                <xs:enumeration value="ANTIPHISHING_CLIENT_LOGGEDIN_ASSURANCE_NOTIFICATION">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc></jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>

                <xs:enumeration value="ANTIPHISHING_DEVICE_ACCESS_REJECTED">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc></jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:enumeration>

            </xs:restriction>
        </xs:simpleType>

        <xs:element name="HandlePhishingOffenceRequest">
            <xs:annotation>
                <xs:documentation>Request operace PhishingOffenceWS.savePhishingOffence obsahující informace o důvodu a reakci na phishingový útok.</xs:documentation>
            </xs:annotation>
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="affectedCuid" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor klienta, na jehož účty byl proveden phishingový útok</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="businessProcessEvent" type="BusinessProcessEvent">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Modnožina hodnot MDM číselníku</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>

                    <xs:choice>
                        <xs:element name="sessionId" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:appinfo>
                                    <jxb:property>
                                        <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR (generuje se v IB).</jxb:javadoc>
                                    </jxb:property>
                                </xs:appinfo>
                            </xs:annotation>
                        </xs:element>

                        <xs:element name="installationId" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:appinfo>
                                    <jxb:property>
                                        <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR (generuje se v IB).</jxb:javadoc>
                                    </jxb:property>
                                </xs:appinfo>
                            </xs:annotation>
                        </xs:element>
                    </xs:choice>
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="HandlePhishingOffenceResponse">
            <xs:annotation>
                <xs:documentation>Response operace PhishingOffenceWS.savePhishingOffence obsahující informace o důvodu a reakci na phishingový útok.</xs:documentation>
            </xs:annotation>
            <xs:complexType/>
        </xs:element>

        <xs:element name="VerifyPhishingOffenceRequest">
            <xs:annotation>
                <xs:documentation>Request operace AntiphishingWS.verifyPhishingOffence obsahující informace o přihlášení k ověření podezření na phishingový útok.
                </xs:documentation>
            </xs:annotation>
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor klienta.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="sessionId" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="installationId" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR (generuje se v IB).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="VerifyPhishingOffenceResponse">
            <xs:annotation>
                <xs:documentation>Response operace AntiphishingWS.verifyPhishingOffence.</xs:documentation>
            </xs:annotation>
            <xs:complexType/>
        </xs:element>

        <xs:element name="GetIpAddressEvaluationRequest">
            <xs:annotation>
                <xs:documentation>HandlePhishingOffence request.</xs:documentation>
            </xs:annotation>
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="ip" type="xs:string" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="GetIpAddressEvaluationResponse">
            <xs:annotation>
                <xs:documentation>HandlePhishingOffence response.</xs:documentation>
            </xs:annotation>
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="fraudScore" type="xs:decimal" minOccurs="0" maxOccurs="1" />
                    <xs:element name="proxy" type="xs:boolean" minOccurs="0" maxOccurs="1" />
                    <xs:element name="vpn" type="xs:boolean" minOccurs="0" maxOccurs="1" />
                    <xs:element name="tor" type="xs:boolean" minOccurs="0" maxOccurs="1" />
                    <xs:element name="recentAbuse" type="xs:boolean" minOccurs="0" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

    </xs:schema>
