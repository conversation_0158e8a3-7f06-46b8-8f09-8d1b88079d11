<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://airbank.cz/swift/payment/" xmlns:tns="http://airbank.cz/swift/payment/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	<xsd:element name="SendMT103MessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="mt103" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendMT103MessageResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveMT103MessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
				<xsd:element name="mt103" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveMT103MessageResponse">
		<xsd:complexType>
			<xsd:sequence>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>	
	<xsd:element name="SendMT950MessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="mt950" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendMT950MessageResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveMT950MessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
				<xsd:element name="mt950" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveMT950MessageResponse">
		<xsd:complexType>
			<xsd:sequence>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>	
	<xsd:element name="SendMT199MessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="mt199" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendMT199MessageResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveMT199MessageRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
				<xsd:element name="mt199" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ReceiveMT199MessageResponse">
		<xsd:complexType>
			<xsd:sequence>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendSwiftFileRequest">
		<xsd:complexType>
			<xsd:sequence>				
				<xsd:element name="fileContent" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="SendSwiftFileResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="fileName" type="xsd:string" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
