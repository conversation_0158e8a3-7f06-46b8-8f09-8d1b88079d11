<wsdl:definitions name="SwiftPaymentService" targetNamespace="http://airbank.cz/swift/payment/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://airbank.cz/swift/payment/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsdl:documentation>
		Web Service for send and receive swift payments messages.

		OBS provide ReceiveSwiftMessage operation and OSB calls this service when swift
		message ocur in ftp server.
		OBS calls SendSwiftMessage operation when want to send swift message to swift provider.
	</wsdl:documentation>
	<wsdl:types>
		<xsd:schema targetNamespace="http://airbank.cz/swift/payment/" elementFormDefault="qualified">
			<xsd:include schemaLocation="SwiftPaymentsService.xsd" />
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="ReceiveMT103MessageRequest">
		<wsdl:part element="tns:ReceiveMT103MessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveMT103MessageResponse">
		<wsdl:part element="tns:ReceiveMT103MessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveMT199MessageRequest">
		<wsdl:part element="tns:ReceiveMT199MessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveMT199MessageResponse">
		<wsdl:part element="tns:ReceiveMT199MessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveMT950MessageRequest">
		<wsdl:part element="tns:ReceiveMT950MessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="ReceiveMT950MessageResponse">
		<wsdl:part element="tns:ReceiveMT950MessageResponse" name="parameters" />
	</wsdl:message>	
	<wsdl:message name="SendMT103MessageRequest">
		<wsdl:part element="tns:SendMT103MessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendMT103MessageResponse">
		<wsdl:part element="tns:SendMT103MessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendMT199MessageRequest">
		<wsdl:part element="tns:SendMT199MessageRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendMT199MessageResponse">
		<wsdl:part element="tns:SendMT199MessageResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendSwiftFileRequest">
		<wsdl:part element="tns:SendSwiftFileRequest" name="parameters" />
	</wsdl:message>
	<wsdl:message name="SendSwiftFileResponse">
		<wsdl:part element="tns:SendSwiftFileResponse" name="parameters" />
	</wsdl:message>
	<wsdl:portType name="SwiftPaymentService">
		<wsdl:operation name="SendMT103Message">
			<wsdl:input message="tns:SendMT103MessageRequest" />
			<wsdl:output message="tns:SendMT103MessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="ReceiveMT103Message">
			<wsdl:input message="tns:ReceiveMT103MessageRequest" />
			<wsdl:output message="tns:ReceiveMT103MessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="SendMT199Message">
			<wsdl:input message="tns:SendMT199MessageRequest" />
			<wsdl:output message="tns:SendMT199MessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="ReceiveMT199Message">
			<wsdl:input message="tns:ReceiveMT199MessageRequest" />
			<wsdl:output message="tns:ReceiveMT199MessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="ReceiveMT950Message">
			<wsdl:input message="tns:ReceiveMT950MessageRequest" />
			<wsdl:output message="tns:ReceiveMT950MessageResponse" />
		</wsdl:operation>
		<wsdl:operation name="SendSwiftFile">
			<wsdl:input message="tns:SendSwiftFileRequest" />
			<wsdl:output message="tns:SendSwiftFileResponse" />
		</wsdl:operation>	
	</wsdl:portType>
	<wsdl:binding name="SwiftPaymentServiceSOAP" type="tns:SwiftPaymentService">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="SendMT103Message">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReceiveMT103Message">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="SendMT199Message">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReceiveMT199Message">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReceiveMT950Message">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="SendSwiftFile">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>	
	</wsdl:binding>
	<wsdl:service name="SwiftPaymentService">
		<wsdl:port binding="tns:SwiftPaymentServiceSOAP" name="SwiftPaymentServiceSOAP">
			<soap:address location="https://localhost:7002/" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
