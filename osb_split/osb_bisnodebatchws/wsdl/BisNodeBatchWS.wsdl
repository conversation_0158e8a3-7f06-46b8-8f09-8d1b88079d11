<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/bisnode/batch" targetNamespace="http://airbank.cz/osb/bisnode/batch">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/bisnode/batch">
                <xsd:include schemaLocation="../xsd/BisNodeBatchWS.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xsd:schema>
        </wsdl:types>


        <wsdl:message name="GetBatchRequest">
            <wsdl:part element="GetBatchRequest" name="GetBatchRequest" />
        </wsdl:message>
        <wsdl:message name="GetBatchResponse">
            <wsdl:part element="GetBatchResponse" name="GetBatchResponse" />
        </wsdl:message>

        <wsdl:message name="ListBatchRequest">
            <wsdl:part element="ListBatchRequest" name="ListBatchRequest" />
        </wsdl:message>
        <wsdl:message name="ListBatchResponse">
            <wsdl:part element="ListBatchResponse" name="ListBatchResponse" />
        </wsdl:message>
        
        <wsdl:message name="BatchFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="BatchFault" />
        </wsdl:message>

        <wsdl:portType name="BatchWS">

            <wsdl:operation name="GetBatch">
                <wsdl:input message="GetBatchRequest" />
                <wsdl:output message="GetBatchResponse" />
                <wsdl:fault name="BatchFault" message="BatchFault" />
            </wsdl:operation>

            <wsdl:operation name="ListBatch">
                <wsdl:input message="ListBatchRequest" />
                <wsdl:output message="ListBatchResponse" />
                <wsdl:fault name="BatchFault" message="BatchFault" />
            </wsdl:operation>

        </wsdl:portType>

        <wsdl:binding name="BatchBinding" type="BatchWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="GetBatch">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="BatchFault">
                    <soap:fault name="BatchFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

            <wsdl:operation name="ListBatch">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="BatchFault">
                    <soap:fault name="BatchFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>

        </wsdl:binding>

        <wsdl:service name="BatchWSBinding">
            <wsdl:port binding="BatchBinding" name="BatchBinding">
                <soap:address location="/ws/BatchWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
