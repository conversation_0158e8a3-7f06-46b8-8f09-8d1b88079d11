<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema targetNamespace="http://osb.abank.cz/customer/crm" xmlns:tns="http://osb.abank.cz/customer/crm"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xsd:element name="getOfferSegmentRequest">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="cuid" type="xsd:long"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getOfferSegmentResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="offerSegment" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
