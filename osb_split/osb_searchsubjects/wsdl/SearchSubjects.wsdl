<?xml version= '1.0' encoding= 'UTF-8' ?>
<wsdl:definitions name="SearchSubjects" targetNamespace="http://osb.airbank.cz/osb/bisnode/search"
                  xmlns:tns="http://osb.airbank.cz/osb/bisnode/search" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://osb.airbank.cz/osb/bisnode/search"
            elementFormDefault="qualified">
      <include schemaLocation="SearchSubjects.xsd"/>
    </schema>
  </wsdl:types>
  <wsdl:message name="findResponse">
    <wsdl:part name="findResponse" element="tns:findResponse"/>
  </wsdl:message>
  <wsdl:message name="getResponse">
    <wsdl:part name="getResponse" element="tns:getResponse"/>
  </wsdl:message>
  <wsdl:message name="findSubjectsRequest">
    <wsdl:part name="findSubjectsRequest" element="tns:findSubjectsRequest"/>
  </wsdl:message>
  <wsdl:message name="getSubjectRequest">
    <wsdl:part name="getSubjectRequest" element="tns:getSubjectsRequest"/>
  </wsdl:message>
  <wsdl:portType name="SearchPort">
    <wsdl:operation name="findSubjects">
      <wsdl:input message="tns:findSubjectsRequest"/>
      <wsdl:output message="tns:findResponse"/>
    </wsdl:operation>
    <wsdl:operation name="getSubjects">
      <wsdl:input message="tns:getSubjectRequest"/>
      <wsdl:output message="tns:getResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SearchSOAP" type="tns:SearchPort">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="findSubjects">
      <soap:operation style="document" soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubjects">
      <soap:operation style="document" soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SearchService">
    <wsdl:port binding="tns:SearchSOAP" name="CurrencySOAP">
      <soap:address location="http://loclhost/search"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
