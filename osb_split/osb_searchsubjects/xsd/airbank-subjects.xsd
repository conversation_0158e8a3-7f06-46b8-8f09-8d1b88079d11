<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="https://airbank.bisnode.cz/subjests" attributeFormDefault="unqualified"
           elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="query_result">
    <xs:complexType>
      <xs:all>
        <xs:element name="head">
          <xs:complexType>
            <xs:all>
              <xs:element type="xs:int" name="total_count"/>
              <xs:element name="performance_info">
                <xs:complexType>
                  <xs:all>
                    <xs:element type="xs:int" name="filter"/>
                    <xs:element type="xs:int" name="output"/>
                  </xs:all>
                </xs:complexType>
              </xs:element>
            </xs:all>
          </xs:complexType>
        </xs:element>
        <xs:element name="data">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="subject" maxOccurs="unbounded" minOccurs="0">
                <xs:complexType>
                  <xs:all>
                    <xs:element type="xs:long" name="entity_id"/>
                    <xs:element type="xs:string" name="registration_number"/>
                    <xs:element type="xs:string" name="vat_identification_number"/>
                    <xs:element type="xs:string" name="group_vat_identification_number"/>
                    <xs:element type="xs:string" name="name"/>
		    <xs:element type="xs:date" name="date_of_establishment" nillable="true"/>
                    <xs:element name="nace">
                      <xs:complexType>
			<xs:all minOccurs="0" maxOccurs="1">
                          <xs:element type="xs:string" name="code"/>
                          <xs:element type="xs:string" name="text"/>
                        </xs:all>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="type">
                      <xs:complexType>
                        <xs:all>
                          <xs:element type="xs:string" name="code"/>
                          <xs:element type="xs:string" name="text"/>
                        </xs:all>
                      </xs:complexType>
                    </xs:element>
                    <xs:element type="xs:boolean" name="active"/>
                    <xs:element type="xs:string" name="country_code"/>
                    <xs:element type="xs:string" name="locality"/>
                    <xs:element type="xs:string" name="town"/>
                    <xs:element type="xs:string" name="street"/>
                    <xs:element type="xs:string" name="house_number"/>
                    <xs:element type="xs:string" name="postcode"/>
                    <xs:element type="xs:string" name="phone"/>
                  </xs:all>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:all>
    </xs:complexType>
  </xs:element>
</xs:schema>
