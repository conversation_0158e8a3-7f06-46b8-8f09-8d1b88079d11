<?xml version = '1.0' encoding = 'UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://fts.airbank.cz/ws"
            targetNamespace="http://fts.airbank.cz/ws" elementFormDefault="qualified"
            xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" nxsd:version="JSON" nxsd:encoding="US-ASCII">
    <xsd:element name="suggestResult">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="version" minOccurs="0" type="xsd:string"/>
                <xsd:element name="texts" maxOccurs="unbounded" type="xsd:string"/>
                <xsd:element name="indexCompleted" minOccurs="0" type="xsd:boolean"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="suggestRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="accountId" type="xsd:string"/>
                <xsd:element name="count" type="xsd:string"/>
                <xsd:element name="searchText" type="xsd:string"/>
                <xsd:element name="profileId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="searchResult">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="version" minOccurs="0" type="xsd:string"/>
                <xsd:element name="ids" maxOccurs="unbounded" type="xsd:integer"/>
                <xsd:element name="indexCompleted" minOccurs="0" type="xsd:boolean"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="searchRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="searchText" type="xsd:string"/>
                <xsd:element name="accountId" type="xsd:string"/>
                <xsd:element name="count" type="xsd:string"/>
                <xsd:element name="profileId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="warmUpResult">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="texts" maxOccurs="unbounded" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="warmUpRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="accountId" type="xsd:string"/>
                <xsd:element name="profileId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="updateRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="accountId" type="xsd:string"/>
                <xsd:element name="profileId" type="xsd:string"/>
                <xsd:element name="idRealizedTransaction" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="updateResult">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:annotation xmlns="">
        <xsd:appinfo>NXSDSAMPLE=</xsd:appinfo>
        <xsd:appinfo>USEHEADER=false</xsd:appinfo>
    </xsd:annotation>
</xsd:schema>