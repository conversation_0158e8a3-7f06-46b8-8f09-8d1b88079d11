<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/UnidentifiedCustomer" targetNamespace="http://airbank.cz/osb/UnidentifiedCustomer" xmlns:ident="http://airbank.cz/osb/ws/identity" elementFormDefault="qualified">

        <xsd:import namespace="http://airbank.cz/osb/ws/identity" schemaLocation="../xsd/OsbIdentity.xsd" />

        <xsd:element name="ProcessUnidentifiedCustomerRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="unidentifiedCustomerData" type="LightCustomerData" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="eventIdentification" type="xsd:string" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="ProcessUnidentifiedCustomerResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="validationResult" type="ValidationResult" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="identificationResult" type="CustomerIdentificationResult" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="verificationResult" type="IdentityVerificationResult" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        
        <xsd:element name="AddLightCustomerAddressRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                    <xsd:element name="customerAddress" type="CustomerAddress" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="AddLightCustomerAddressResponse">
            <xsd:complexType>
                <xsd:sequence>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>


        <xsd:complexType name="LightCustomerData">
            <xsd:sequence>
                <xsd:element name="identification" type="Identification" minOccurs="1" maxOccurs="1" />
                <xsd:element name="otherPersonalData" type="OtherPersonalData" minOccurs="1" maxOccurs="1" />
                <xsd:element name="customerAddress" type="CustomerAddress" minOccurs="0" maxOccurs="unbounded" />
                <xsd:element name="customerContact" type="CustomerContact" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="Identification">
            <xsd:choice>
                <xsd:element name="simpleIdentification" type="SimpleIdentification" />
                <xsd:element name="standardIdentification" type="StandardIdentification" />
                <xsd:element name="fullIdentification" type="FullIdentification" />
            </xsd:choice>
        </xsd:complexType>


        <xsd:complexType name="SimpleIdentification">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="quintupleIdentity" type="ident:QuintupleIdentity" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="StandardIdentification">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="quintupleIdentity" type="ident:QuintupleIdentity" />
                    <xsd:element name="aifoIdentity" type="ident:AifoIdentity" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="FullIdentification">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="quintupleIdentity" type="ident:QuintupleIdentity" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="birthNumberIdentity" type="ident:BirthNumberIdentity" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="aifoIdentity" type="ident:AifoIdentity" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="OtherPersonalData">
            <xsd:sequence>
                <xsd:element name="salutation" type="Salutation" minOccurs="0" maxOccurs="1" />
                <xsd:element name="gender" type="Gender" minOccurs="1" maxOccurs="1" />
                <xsd:element name="honourBeforeName" type="xsd:string" minOccurs="0" />
                <xsd:element name="honourAfterName" type="xsd:string" minOccurs="0" />
                <xsd:element name="citizenship" type="xsd:string" minOccurs="1" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="CustomerAddress">
            <xsd:sequence>
                <xsd:element name="role" type="Role" minOccurs="1" maxOccurs="1" />
                <xsd:element name="municipalAddress" type="xsd:boolean" minOccurs="0" maxOccurs="1" />
                <xsd:element name="address" type="Address" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="CustomerContact">
            <xsd:sequence>
                <xsd:element name="role" type="CustomerContactRole" minOccurs="1" maxOccurs="1" />
                <xsd:element name="contact" type="Contact" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="Contact">
            <xsd:sequence>
                <xsd:element name="contactValue" type="xsd:string" />
                <xsd:element name="callingCode" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="Address">
            <xsd:sequence>
                <xsd:element name="streetOrLocality" type="xsd:string" minOccurs="0" />
                <xsd:element name="streetOrLocalityToDisplay" type="xsd:string" minOccurs="0" />
                <xsd:element name="house" type="xsd:string" minOccurs="0" />
                <xsd:element name="town" type="xsd:string" />
                <xsd:element name="townToDisplay" type="xsd:string" minOccurs="0" />
                <xsd:element name="zip" type="xsd:string" minOccurs="0" />
                <xsd:element name="countryAlpha2Code" type="xsd:string" />
                <xsd:element name="addressFormat" type="AddressFormat" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            List of allowed address format. See enumerator documentation on wiki page - https://wiki.airbank.cz/x/9ARRBQ
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="ValidationResult">
            <xsd:sequence>
                <xsd:element name="validationType" type="CustomerDataValidation" minOccurs="1" maxOccurs="1" />
                <xsd:element name="attributes" type="xsd:string" minOccurs="0" maxOccurs="unbounded" />
                <xsd:element name="resultDetail" type="xsd:string" minOccurs="0" maxOccurs="1" />
                <xsd:element name="validationCode" type="xsd:string" minOccurs="0" maxOccurs="1" />
                <xsd:element name="values" type="Values" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="Values">
            <xsd:sequence>
                <xsd:element name="key" type="xsd:string" />
                <xsd:element name="values" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:simpleType name="Salutation">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="P" />
                <xsd:enumeration value="S" />
                <xsd:enumeration value="Z" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="Gender">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="M" />
                <xsd:enumeration value="Z" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="Role">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="FOREIGN_PERMANENT" />
                <xsd:enumeration value="PERMANENT" />
                <xsd:enumeration value="REPORTS" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="AddressFormat">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="0" />
                <xsd:enumeration value="1" />
                <xsd:enumeration value="2" />
                <xsd:enumeration value="3" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="CustomerContactRole">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="PRIMARY_EMAIL" />
                <xsd:enumeration value="PRIMARY_MOBILE" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="CustomerDataValidation">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="CROSS_VALIDATION" />
                <xsd:enumeration value="EXCLUSIVE_ATTRIBUTES" />
                <xsd:enumeration value="FORBIDDEN_VALUE" />
                <xsd:enumeration value="GEN_SPEC_VIOLATION" />
                <xsd:enumeration value="WRONG_COLLECTION_FEATURE" />
                <xsd:enumeration value="WRONG_FORMAT" />
                <xsd:enumeration value="WRONG_REGISTER_VALUE" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="CustomerIdentificationResult">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="NOT_EXISTS" />
                <xsd:enumeration value="AMBIGUOUS" />
                <xsd:enumeration value="EXISTS" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="IdentityVerificationResult">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="OK" />
                <xsd:enumeration value="EXISTS" />
                <xsd:enumeration value="NOT_ALLOWED" />
                <xsd:enumeration value="FORBIDDEN" />
            </xsd:restriction>
        </xsd:simpleType>

    </xsd:schema>
