<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/ws/ZonkyTranNotifWS" elementFormDefault="qualified"
           targetNamespace="http://airbank.cz/osb/ws/ZonkyTranNotifWS" xmlns:not="http://airbank.cz/osb/ws/ZonkyTranNotifWS">

        <xs:element name="NewTrnRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="transaction" type="not:Transaction" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="NewTrnResponse">
            <xs:complexType>
            </xs:complexType>
        </xs:element>
    
    
    <xs:complexType name="Transaction">
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:documentation>unikátní identifikátor pohybu</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="externalId" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>externí identifikátor platby, pokud tento pohyb vznikl na základě platby zadané pomocí API createLocalPayments, jinak prázdné</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="debitCredit" type="DebitCreditEnum">
                <xs:annotation>
                    <xs:documentation>debet/kredit</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>částka transakce v měně účtu</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>číslo našeho účtu (ve formátu ABO)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contraAccountIBAN" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>číslo protiúčtu ve formátu IBAN</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contraAccountNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>číslo protiúčtu ve volném formátu</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contraAccountName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>jméno protiúčtu</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contraBankCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>kód banky protiúčtu</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bookingDate" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>datum a čas zúčtování</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="valueDate" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>datum a čas provedení (valuta)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="vs" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>variabilní symbol</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ss" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>specifický symbol</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ks" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>konstantní symbol</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="text" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>text transakce</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="DebitCreditEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DB">
                <xs:annotation>
                    <xs:documentation>Debet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CR">
                <xs:annotation>
                    <xs:documentation>Kredit</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>