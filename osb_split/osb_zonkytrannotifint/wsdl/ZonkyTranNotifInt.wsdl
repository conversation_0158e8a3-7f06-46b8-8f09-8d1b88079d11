<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/osb/ws/ZonkyTranNotifInt"
                  xmlns:tns="http://airbank.cz/osb/ws/ZonkyTranNotifWS"
                  targetNamespace="http://airbank.cz/osb/ws/ZonkyTranNotifInt">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/osb/ws/ZonkyTranNotifWS">
            <xs:include schemaLocation="ZonkyTranNotifInt.xsd"/>
        </xs:schema>
    </wsdl:types>

     <wsdl:message name="NewTrnRequest">
        <wsdl:part element="tns:Transaction" name="transaction"/>
    </wsdl:message>
    <wsdl:message name="NewTrnResponse">
        <wsdl:part element="tns:NewTrnResponse" name="NewTrnResponse"/>
    </wsdl:message>

    <wsdl:portType name="ZonkyTranNotifPortType">
        <wsdl:operation name="NewTrn">
            <wsdl:documentation>
                Notify Zonky
            </wsdl:documentation>
            <wsdl:input message="NewTrnRequest" name="NewTrnRequest"/>
            <wsdl:output message="NewTrnResponse" name="NewTrnResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ZonkyTranNotifBinding" type="ZonkyTranNotifPortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="NewTrn">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ZonkyTranNotifService">
        <wsdl:documentation>OBS web service.</wsdl:documentation>
        <wsdl:port name="ZonkyTranNotifPort" binding="ZonkyTranNotifBinding">
            <soap:address location="http://localhost:8000/ws/"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>