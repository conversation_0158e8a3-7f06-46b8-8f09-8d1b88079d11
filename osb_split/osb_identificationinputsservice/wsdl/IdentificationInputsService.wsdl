<wsdl:definitions targetNamespace="http://airbank.cz/osb/InputsService" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://airbank.cz/osb/InputsService">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/osb/InputsService">
            <xs:include schemaLocation="IdentificationInputsService.xsd" />
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="IsPhysicallyIdentifiedRequest">
        <wsdl:part element="IsPhysicallyIdentifiedRequest" name="IsPhysicallyIdentifiedRequest" />
    </wsdl:message>
    <wsdl:message name="IsPhysicallyIdentifiedResponse">
        <wsdl:part element="IsPhysicallyIdentifiedResponse" name="IsPhysicallyIdentifiedResponse" />
    </wsdl:message>


    <wsdl:portType name="InputsServicePort">
        <wsdl:operation name="IsPhysicallyIdentified">
            <wsdl:input message="IsPhysicallyIdentifiedRequest" />
            <wsdl:output message="IsPhysicallyIdentifiedResponse" />
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="InputsServiceBinding" type="InputsServicePort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="IsPhysicallyIdentified">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="InputsServiceWS">
        <wsdl:port binding="InputsServiceBinding" name="InputsServicePort">
            <soap:address location="http://TO-BE-SPECIFIED/osb/InputsServiceWS" />
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
