<?xml version="1.0" encoding="UTF-8"?>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/InputsService" targetNamespace="http://airbank.cz/osb/InputsService">

        <xs:element name="IsPhysicallyIdentifiedRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="IsPhysicallyIdentifiedResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="physicallyIdentified" type="xs:boolean" minOccurs="1" maxOccurs="1" />
                    <xs:element name="identifiedBy" type="IdentifiedBy" minOccurs="0" maxOccurs="3" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>


        <xs:simpleType name="IdentifiedBy">
            <xs:restriction base="xs:string">
                <xs:enumeration value="BRANCH_VISIT" />
                <xs:enumeration value="BRANCH_SIGNED_CONTRACT" />
                <xs:enumeration value="MESSENGER_SIGNED_CONTRACT" />
            </xs:restriction>
        </xs:simpleType>

    </xs:schema>
