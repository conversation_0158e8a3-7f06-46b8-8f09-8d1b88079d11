<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://osb.airbank.cz/queueProcessing"
           targetNamespace="http://osb.airbank.cz/queueProcessing"
           elementFormDefault="unqualified">
           
    <xs:element name="MessageProcessedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="request" type="Request"/>
                <xs:element name="response" type="Response" minOccurs="0"/>
                <xs:element name="service" type="xs:string" minOccurs="0"/>
                <xs:element name="operation" type="xs:string" minOccurs="0"/>
                <xs:element name="result" type="MessageDeliveryResult"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="MessageProcessedResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:simpleType name="MessageDeliveryResult">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DELIVERED">
                <xs:annotation>
                   <xs:documentation>The message was delivered</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_DELIVERED">
                <xs:annotation>
                   <xs:documentation>The message could not be delivered</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    
    <xs:complexType name="Transport">
        <xs:sequence>
            <xs:element name="httpHeader" type="NameValue" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="body" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="Request">
        <xs:complexContent>
            <xs:extension base="Transport">
                <xs:sequence>
                    <xs:element name="method" type="xs:string"/>
                    <xs:element name="relativeUri" type="xs:string" minOccurs="0"/>
                    <xs:element name="queryParameter" type="NameValue" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <xs:complexType name="Response">
        <xs:complexContent>
            <xs:extension base="Transport">
                <xs:sequence>
                    <xs:element name="statusCode" type="xs:int"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <xs:complexType name="NameValue">
        <xs:attribute name="name" type="xs:string" use="required"/>
        <xs:attribute name="value" type="xs:string" use="required"/>
    </xs:complexType>
    
</xs:schema>