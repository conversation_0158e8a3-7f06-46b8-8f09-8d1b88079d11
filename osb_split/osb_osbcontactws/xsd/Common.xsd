<?xml version="1.0"?>
    <!-- $Id: 5b27320b663faf30efabe6958d2606e56db6cdb0 $ -->
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.airbank.cz/contact/common/dto" targetNamespace="http://osb.airbank.cz/contact/common/dto" elementFormDefault="qualified" attributeFormDefault="unqualified">
        <xs:complexType name="PersonIdentification">
            <xs:annotation>
                <xs:documentation>identifikace klienta</xs:documentation>
            </xs:annotation>
            <xs:choice>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>CIF identifikátor</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="idLead" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>idLead- identifikovat z webu</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:complexType>
        <xs:simpleType name="FeedbackType">
            <xs:annotation>
                <xs:documentation>Typ požadované zpětné vazby</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="CHECKBOX" />
                <xs:enumeration value="COMBOBOX" />
                <xs:enumeration value="FREE_TEXT" />
                <xs:enumeration value="RADIOBUTTON" />
                <xs:enumeration value="STARS" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="MessageResultType">
            <xs:annotation>
                <xs:documentation>Typy výsledku zprávy. CLIENT_CLICK = proklik CLIENT_INFO_FEEDBACK - zpětná vazba klienta
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="CLIENT_CLICK" />
                <xs:enumeration value="CLIENT_INFO_FEEDBACK" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="MessageDisplayStatus">
            <xs:annotation>
                <xs:documentation>Substav některých zpráv</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="DISPLAYED" />
                <xs:enumeration value="NONE" />
                <xs:enumeration value="READ" />
                <xs:enumeration value="USED" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="Status">
            <xs:restriction base="xs:string">
                <xs:enumeration value="ACTIVE" />
                <xs:enumeration value="INACTIVE" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="CreatorDiscriminator">
            <xs:annotation>
                <xs:documentation>Who created the record
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="OPERATOR" />
                <xs:enumeration value="AUTOMAT" />
            </xs:restriction>
        </xs:simpleType>
        <xs:complexType name="DateIntervalTO">
            <xs:annotation>
                <xs:documentation>Define date interval</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="from" type="xs:date" minOccurs="0" />
                <xs:element name="to" type="xs:date" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="DateValidityTO">
            <xs:annotation>
                <xs:documentation>Define date validity. "from" element is required</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="from" type="xs:date" />
                <xs:element name="to" type="xs:date" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="CommunicationBatchBaseTO">
            <xs:annotation>
                <xs:documentation>
                    common complex type for communication batch
                </xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="campaignCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            SAS campaign code
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="campaignName" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            SAS campaign name
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="communicationCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            code of communication - important to resolve sas queue
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="communicationName" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            name of communication - attribute for Genesys
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="communicationKind" type="tns:CommunicationKind" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="CommunicationBatchBaseDetailTO">
            <xs:complexContent>
                <xs:extension base="tns:CommunicationBatchBaseTO">
                    <xs:sequence>
                        <xs:element name="communicationBatchId" type="tns:CommunicationBatchIdTO" />
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
        <xs:simpleType name="CommunicationBatchIdTO">
            <xs:annotation>
                <xs:documentation>
                    Communication Batch identification in CML. Composite attribute system,idCommunicationBatch must be globally unique. Source system is responsible for idCommunicationBatch uniqueness inside in system.
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string" />
        </xs:simpleType>
        <xs:complexType name="CommunicationBatchTO">
            <xs:complexContent>
                <xs:extension base="tns:CommunicationBatchBaseTO">
                    <xs:sequence>
                        <xs:element name="communicationSk" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>
                                    main identifier in SAS
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="communicationBatchProducts" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    product codes like BU, HYSA, DK, CP
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="survey" type="tns:Survey" minOccurs="0" />
                        <xs:element name="businessSummaryCauseCode" type="xs:string" />
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
        <xs:complexType name="CommunicationBatchCampaignTypeTO">
            <xs:complexContent>
                <xs:extension base="tns:CommunicationBatchTO">
                    <xs:sequence>
                        <xs:element name="campaignCommunicationType" type="tns:CampaignCommunicationType" />
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
        <xs:complexType name="CommunicationBatchOfflineTO">
            <xs:complexContent>
                <xs:extension base="tns:CommunicationBatchBaseTO">
                    <xs:sequence>
                        <xs:element name="communicationSk" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>
                                    main identifier in SAS
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="communicationBatchProducts" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    product codes like BU, HYSA, DK, CP
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="businessSummaryCauseCode" type="xs:string" />
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
        <xs:complexType name="CommunicationBatchDetailTO">
            <xs:annotation>
                <xs:documentation>
                    Detail of existing communication batch object
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="tns:CommunicationBatchTO">
                    <xs:sequence>
                        <xs:element name="communicationBatchId" type="tns:CommunicationBatchIdTO" />
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
        <xs:complexType name="Survey">
            <xs:sequence>
                <xs:choice>
                    <xs:element name="surveyChoice" type="tns:SurveyChoice" />
                    <xs:element name="stars" type="tns:Stars" />
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="SurveyChoice">
            <xs:sequence>
                <xs:choice>
                    <xs:element name="singleChoice" type="tns:SurveySingleChoice" />
                    <xs:element name="multiChoice" type="tns:SurveyMultiChoice">
                        <xs:annotation>
                            <xs:documentation>
                                survey has only checkboxes
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:choice>
                <xs:element name="answer" type="tns:SurveyChoiceAnswer" minOccurs="2" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="SurveySingleChoice">
            <xs:sequence>
                <xs:element name="singleChoiceType" type="tns:SingleChoiceType" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="SurveyMultiChoice">
            <xs:sequence>
                <xs:element name="multiChoiceType" type="tns:MultiChoiceType" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="SurveyChoiceAnswer">
            <xs:sequence>
                <xs:element name="orderNumber" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            position in combobox
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="sentiment" type="tns:AnswerChoiceSentiment" />
                <xs:element name="text" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            text of answer
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="Stars">
            <xs:annotation>
                <xs:documentation>
                    Stars survey type
                </xs:documentation>
            </xs:annotation>
        </xs:complexType>
        <xs:simpleType name="SingleChoiceType">
            <xs:restriction base="xs:string">
                <xs:enumeration value="COMBOBOX" />
                <xs:enumeration value="RADIOBUTTON" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="MultiChoiceType">
            <xs:restriction base="xs:string">
                <xs:enumeration value="CHECKBOX" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="AnswerChoiceSentiment">
            <xs:annotation>
                <xs:documentation>
                    type of answer division
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="POSITIVE" />
                <xs:enumeration value="NEGATIVE" />
                <xs:enumeration value="NEUTRAL" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="CommunicationKind">
            <xs:annotation>
                <xs:documentation>
                    kind of communication
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="MARKETING" />
                <xs:enumeration value="RETENTION" />
                <xs:enumeration value="INFO" />
                <xs:enumeration value="COLLECTION" />
                <xs:enumeration value="TECHNICAL_INFO" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="CampaignCommunicationType">
            <xs:annotation>
                <xs:documentation>
                    type of communication ACTIVE/PASSIVE
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="ACTIVE" />
                <xs:enumeration value="PASSIVE" />
            </xs:restriction>
        </xs:simpleType>
        <xs:simpleType name="ContactIdTO">
            <xs:annotation>
                <xs:documentation>
                    Contact identification in CML. Source system is responsible for contactId uniqueness inside in system.
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string" />
        </xs:simpleType>
        <xs:complexType name="ExternalRelationBaseTO">
            <xs:annotation>
                <xs:documentation>
                    External relation of the contact.
                </xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="entityCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            entityCode - entity code from external system
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="instanceId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            instanceId - unique identifier of the entity from external system
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="ExternalRelationTO">
            <xs:complexContent>
                <xs:extension base="tns:ExternalRelationBaseTO">
                    <xs:sequence>
                        <xs:element name="info" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    info - human readable information
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
        <xs:complexType name="PhoneNumberTO">
            <xs:annotation>
                <xs:documentation>
                    Phone number
                </xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="countryCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            countryCode – international call code, e.g. "+420"
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="nationalNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            nationalNumber – national phone number, e.g. "777111222"
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
        <xs:simpleType name="PhoneTypeTO">
            <xs:annotation>
                <xs:documentation>
                    Type of phone STANDARD - phone identified by phone number UNKNOWN - unknown or withheld phone number
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="STANDARD" />
                <xs:enumeration value="UNKNOWN" />
            </xs:restriction>
        </xs:simpleType>
        <xs:complexType name="PhoneTO">
            <xs:sequence>
                <xs:element name="phoneType" type="tns:PhoneTypeTO" />
                <xs:element name="phoneNumber" type="tns:PhoneNumberTO" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>
        <xs:complexType name="DepartmentQueue">
            <xs:sequence>
                <xs:choice>
                    <xs:element name="businessKey" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                code of departmentQueue - existing passive campaign queue
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="department" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                department for active campaigns
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:schema>
