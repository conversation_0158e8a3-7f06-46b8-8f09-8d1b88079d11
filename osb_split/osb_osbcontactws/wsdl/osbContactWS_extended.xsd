<!-- $Id: e46d303baeb067dd2bb2a1d89ac2d6264dcdf127 $ -->
<xsd:schema targetNamespace="http://osb.abank.cz/contact/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:con="http://osb.abank.cz/contact/" xmlns:concmn="http://osb.airbank.cz/contact/common/dto">
    <xsd:include schemaLocation="osbContactWS.xsd" />
    <xsd:import schemaLocation="../xsd/Common.xsd" namespace="http://osb.airbank.cz/contact/common/dto" />
    <xsd:element name="GetContactDetailExtendedRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="channel" type="con:Channel" minOccurs="0" maxOccurs="1" />
                <xsd:element name="contactId" type="concmn:ContactIdTO" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactDetailExtendedResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactDetail" type="con:ContactDetailExt" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendSMSRequest">
        <xsd:complexType>
            <xsd:choice>
                <xsd:element name="automaticReply" type="con:AutomaticSmsReply" />
                <xsd:element name="notification" type="con:AutomaticNewSms" />
                <xsd:element name="common" type="con:ConceptSms" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendEmailRequest">
        <xsd:complexType>
            <xsd:choice>
                <xsd:element name="automaticReply" type="con:AutomaticReply" />
                <xsd:element name="notification" type="con:AutomaticNewEmail" />
                <xsd:element name="common" type="con:ConceptEmail" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendIBMessageRequest">
        <xsd:complexType>
            <xsd:choice>
                <xsd:element name="automaticReply" type="con:AutomaticIBReply" />
                <xsd:element name="notification" type="con:AutomaticNewIBMessage" />
                <xsd:element name="concept" type="con:ConceptIBContact" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendEmailResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="con:Response" />
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendSMSResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="con:Response" />
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendIBMessageResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="concmn:ContactIdTO" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendContactAlertRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="predecessor" type="con:Predecessor" />
                <xsd:element name="alertContact" type="con:AlertContact" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendContactAlertResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="concmn:ContactIdTO" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterSupplementaryContactRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="direction" type="con:Direction" />
                <xsd:element name="realized" type="xsd:dateTime" />
                <xsd:element name="cuid" type="xsd:string" minOccurs="0" />
                <xsd:element name="contact" type="con:ContactSupple" />
                <xsd:element name="originContext" type="con:OriginContext" minOccurs="0" />
                <xsd:element name="contactExternalRelationList" type="concmn:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterSupplementaryContactResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="concmn:ContactIdTO" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactHistoryExtendedResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactsHistory" type="con:ContactsHistoryExt" />
                <xsd:element maxOccurs="2" minOccurs="1" name="compositeIndicatorList" type="con:CompositeIndicatorList" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    
    <xsd:element name="SendSmsToPrimaryAndHistoricalMobileRequest">
        <xsd:complexType>
            <xsd:choice>
                <xsd:element name="automaticReply" type="con:AutomaticSmsReply" />
                <xsd:element name="notification" type="con:AutomaticNewSms" />
                <xsd:element name="common" type="con:ConceptSms" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendSmsToPrimaryAndHistoricalMobileResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="con:PrimaryAndHistoricalResponse" />
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendEmailToPrimaryAndHistoricalEmailRequest">
        <xsd:complexType>
            <xsd:choice>
                <xsd:element name="automaticReply" type="con:AutomaticReply" />
                <xsd:element name="notification" type="con:AutomaticNewEmail" />
                <xsd:element name="common" type="con:ConceptEmail" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SendEmailToPrimaryAndHistoricalEmailResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="con:PrimaryAndHistoricalResponse" />
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType abstract="true" name="Predecessor">
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0" />
            <xsd:element name="phone" type="concmn:PhoneNumberTO" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="StandardPredecessor">
        <xsd:complexContent>
            <xsd:extension base="con:Predecessor">
                <xsd:sequence>
                    <xsd:element name="contactId" type="xsd:string" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="FailoverPredecessor">
        <xsd:complexContent>
            <xsd:extension base="con:Predecessor">
                <xsd:sequence>
                    <xsd:element name="originContext" type="con:OriginContextExtendedTO" />
                    <xsd:element name="channel" type="con:ChannelDiscriminator" />
                    <xsd:element name="realized" type="xsd:dateTime" />
                    <xsd:element name="operator" type="con:Operator" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="AlertContact">
        <xsd:sequence>
            <xsd:element name="idmessage" type="xsd:string" />
            <xsd:element name="body" type="xsd:string" />
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="Operator">
        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string" />
            <xsd:element name="employeeDepartment" type="xsd:string" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:simpleType name="ChannelDiscriminator">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CALL" />
            <xsd:enumeration value="EMAIL" />
            <xsd:enumeration value="IB" />
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="AutomaticReplyBase">
        <xsd:sequence>
            <xsd:element name="predecessorContactId" type="concmn:ContactIdTO" minOccurs="1" maxOccurs="1" />
            <xsd:element name="classification" type="xsd:string" />
            <xsd:element name="originContext" type="con:OriginContextExtendedTO" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="AutomaticNewBase">
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1" />
            <xsd:element name="originContext">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="context" type="con:OriginContextExtendedTO" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="businessSummaryCauseCode" type="xsd:string" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Business summary cause code</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contactExternalRelationList" type="concmn:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded" />
            <xsd:element name="sender" type="con:SenderTO" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="SenderTO">
        <xsd:annotation>
            <xsd:documentation>
                Sender data
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string" />
            <xsd:element name="employeeDepartment" type="xsd:string" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ConceptTO">
        <xsd:sequence>
            <xsd:element name="conceptContactId" type="concmn:ContactIdTO" minOccurs="1" maxOccurs="1" />
            <xsd:element name="businessSummaryFailed" type="xsd:boolean" minOccurs="1" maxOccurs="1" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ConceptBase">
        <xsd:sequence>
            <xsd:element name="conceptContactId" type="concmn:ContactIdTO" />
            <xsd:element name="businessSummaryFailed" type="xsd:boolean" />
            <xsd:element name="employeeNumber" type="xsd:string" />
            <xsd:element name="employeeDepartment" type="xsd:string" />
            <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1" />
            <xsd:element name="originContext" type="con:OriginContext" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="CommonBase">
        <xsd:sequence>
            <xsd:element name="concept" type="con:ConceptTO" minOccurs="0" maxOccurs="1" />
            <xsd:element name="employeeNumber" type="xsd:string" />
            <xsd:element name="employeeDepartment" type="xsd:string" />
            <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1" />
            <xsd:element name="originContext" type="con:OriginContextExtendedTO" minOccurs="0" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="EmailContactTOExtended">
        <xsd:complexContent>
            <xsd:extension base="con:EmailContactTO">
                <xsd:sequence>
                    <xsd:element name="htmlBody" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="inReplyTo" type="xsd:string" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="AutomaticReply">
        <xsd:complexContent>
            <xsd:extension base="con:AutomaticReplyBase">
                <xsd:sequence>
                    <xsd:element name="email" type="con:EmailContactTOExtended" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="AutomaticNewEmail">
        <xsd:complexContent>
            <xsd:extension base="con:AutomaticNewBase">
                <xsd:sequence>
                    <xsd:element name="email" type="con:EmailContactTOExtended" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ConceptEmail">
        <xsd:complexContent>
            <xsd:extension base="con:CommonBase">
                <xsd:sequence>
                    <xsd:element name="email" type="con:EmailContactTOExtended" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="AutomaticIBReply">
        <xsd:complexContent>
            <xsd:extension base="con:AutomaticReplyBase">
                <xsd:sequence>
                    <xsd:element name="ibContact" type="con:IBContactFromBankTO" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="AutomaticNewIBMessage">
        <xsd:complexContent>
            <xsd:extension base="con:AutomaticNewBase">
                <xsd:sequence>
                    <xsd:element name="ibContact" type="con:IBContactFromBankTO" />
                    <xsd:element name="bodyMB" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="display" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="button" type="con:ibButton" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ConceptIBContact">
        <xsd:complexContent>
            <xsd:extension base="con:ConceptBase">
                <xsd:sequence>
                    <xsd:element name="ibContact" type="con:IBContactFromBankTO" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="AutomaticNewSms">
        <xsd:complexContent>
            <xsd:extension base="con:AutomaticNewBase">
                <xsd:sequence>
                    <xsd:element name="sms" type="con:SmsContactTO" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="AutomaticSmsReply">
        <xsd:complexContent>
            <xsd:extension base="con:AutomaticReplyBase">
                <xsd:sequence>
                    <xsd:element name="sms" type="con:SmsContactTO" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ConceptSms">
        <xsd:complexContent>
            <xsd:extension base="con:CommonBase">
                <xsd:sequence>
                    <xsd:element name="sms" type="con:SmsContactTO" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:simpleType name="resultCodeType">
        <xsd:restriction base="xsd:string">
            <xsd:annotation>
                <xsd:documentation>Values - meaning: 0 - all messages were accepted by Message Server 1 - all messages were rejected (service unavailable) 2 - some of the messages were not accepted because of an error
                </xsd:documentation>
            </xsd:annotation>
            <xsd:enumeration value="0" />
            <xsd:enumeration value="1" />
            <xsd:enumeration value="2" />
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="failedMessageType">
        <xsd:sequence>
            <xsd:element name="messageId" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>Identification of error message</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="errorMessage" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>Human-readable description of the error</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="Response">
        <xsd:sequence>
            <xsd:element name="resultCode" type="con:resultCodeType" minOccurs="1" maxOccurs="1" />
            <xsd:element name="failedMessageList" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>List of messages that were not accepted by message server</xsd:documentation>
                </xsd:annotation>
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="failedMessage" type="con:failedMessageType" minOccurs="1" maxOccurs="unbounded" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="contactId" type="concmn:ContactIdTO" minOccurs="0" maxOccurs="1" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="PrimaryAndHistoricalResponse">
        <xsd:sequence>
            <xsd:element name="resultCode" type="con:resultCodeType" minOccurs="1" maxOccurs="1" />
            <xsd:element name="failedMessageList" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>List of messages that were not accepted by message server</xsd:documentation>
                </xsd:annotation>
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="failedMessage" type="con:failedMessageType" minOccurs="1" maxOccurs="unbounded" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="contactId" type="concmn:ContactIdTO" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ContactSupple">
        <xsd:choice>
            <xsd:element name="emailContact" type="con:BaseEmailContactTO" />
            <xsd:element name="smsContact" type="con:SmsContactTO" />
            <xsd:element name="callContact" type="con:FailoverFromBankCallTO" />
            <xsd:element name="printedMessage" type="con:PrintedContactExt" />
        </xsd:choice>
    </xsd:complexType>
    <xsd:complexType name="FailedSystems">
        <xsd:annotation>
            <xsd:documentation>
                Error systems for IB message
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="system" type="xsd:string" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="PrintedContactExt">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for printed contact subject - subject of the contact body - text (body) of the contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="subject" type="xsd:string" />
            <xsd:element name="body" type="xsd:string" />
            <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ContactItemExt">
        <xsd:annotation>
            <xsd:documentation>
                extented contact data to ContactBaseTO. It's used for getContactHistory output. subject - short text of contact description (e.g. subject for email, ib, etc.) informationClassification - list of all unique codes (from all operators) or contentless information code (like SPAM etc) isRead : contact is or is not read by client. This element is related only to IB channel contacts isAlert : contact is or is not lightbox. This attribute is related only to IB channel contacts hasAttachments : contact has or has not any attachment
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:ContactItemTO">
                <xsd:sequence>
                    <xsd:element name="canDelete" type="xsd:boolean" minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="ContactDetailExt">
        <xsd:annotation>
            <xsd:documentation>
                Contact detail information canDelete : parametr to contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:ContactDetailTO">
                <xsd:sequence>
                    <xsd:element name="canDelete" type="xsd:boolean" minOccurs="0" />
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>
    <xsd:complexType name="CompositeIndicatorList">
        <xsd:sequence>
            <xsd:annotation>
                <xsd:documentation>system - Označení systému</xsd:documentation>
                <xsd:documentation>isFault - Označuje, zda cílový systém odpověděl (false) nebo neodpovedel (true).
                </xsd:documentation>
            </xsd:annotation>
            <xsd:element name="system" type="xsd:string" minOccurs="1" maxOccurs="1" />
            <xsd:element name="isFault" type="xsd:boolean" minOccurs="1" maxOccurs="1" />
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ContactsHistoryExt">
        <xsd:annotation>
            <xsd:documentation>
                Contact history
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="contact" type="con:ContactItemExt" minOccurs="0" maxOccurs="unbounded" />
            <xsd:element name="relation" type="con:ContactRelationTO" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
