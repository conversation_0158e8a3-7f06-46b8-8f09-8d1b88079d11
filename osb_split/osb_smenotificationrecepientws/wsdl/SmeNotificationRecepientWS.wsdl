<wsdl:definitions targetNamespace="http://airbank.cz/osb/NotificationRecepient" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://airbank.cz/osb/NotificationRecepient">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/osb/NotificationRecepient">
            <xs:include schemaLocation="SmeNotificationRecepientWS.xsd" />
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="SmeNotificationRecepientRequest">
        <wsdl:part element="SmeNotificationRecepientRequest" name="SmeNotificationRecepientRequest" />
    </wsdl:message>
    <wsdl:message name="SmeNotificationRecepientResponse">
        <wsdl:part element="SmeNotificationRecepientResponse" name="SmeNotificationRecepientResponse" />
    </wsdl:message>


    <wsdl:portType name="NotificationRecepientPort">
        <wsdl:operation name="SmeNotificationRecepient">
            <wsdl:input message="SmeNotificationRecepientRequest" />
            <wsdl:output message="SmeNotificationRecepientResponse" />
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="NotificationRecepientBinding" type="NotificationRecepientPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="SmeNotificationRecepient">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="NotificationRecepientWS">
        <wsdl:port binding="NotificationRecepientBinding" name="NotificationRecepientPort">
            <soap:address location="http://TO-BE-SPECIFIED/osb/NotificationRecepientWS" />
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
