<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:phi="http://airbank.cz/lap/ws/phishingBlackList" targetNamespace="http://airbank.cz/lap/ws/phishingBlackList">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/lap/ws/phishingBlackList">
                <xsd:include schemaLocation="../xsd/PhishingBlackListWS.xsd" />
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="GetIPsRequest">
            <wsdl:part element="phi:GetIPsRequest" name="GetIPsRequest" />
        </wsdl:message>
        <wsdl:message name="GetIPsResponse">
            <wsdl:part element="phi:GetIPsResponse" name="GetIPsResponse" />
        </wsdl:message>

        <wsdl:message name="GetCountriesRequest">
            <wsdl:part element="phi:GetCountriesRequest" name="GetCountriesRequest" />
        </wsdl:message>
        <wsdl:message name="GetCountriesResponse">
            <wsdl:part element="phi:GetCountriesResponse" name="GetCountriesResponse" />
        </wsdl:message>

        <wsdl:message name="GetReferersRequest">
            <wsdl:part element="phi:GetReferersRequest" name="GetReferersRequest" />
        </wsdl:message>
        <wsdl:message name="GetReferersResponse">
            <wsdl:part element="phi:GetReferersResponse" name="GetReferersResponse" />
        </wsdl:message>

        <wsdl:message name="GetCookiesRequest">
            <wsdl:part element="phi:GetCookiesRequest" name="GetCookiesRequest" />
        </wsdl:message>
        <wsdl:message name="GetCookiesResponse">
            <wsdl:part element="phi:GetCookiesResponse" name="GetCookiesResponse" />
        </wsdl:message>

        <wsdl:message name="GetBrowserLanguagesRequest">
            <wsdl:part element="phi:GetBrowserLanguagesRequest" name="GetBrowserLanguagesRequest" />
        </wsdl:message>
        <wsdl:message name="GetBrowserLanguagesResponse">
            <wsdl:part element="phi:GetBrowserLanguagesResponse" name="GetBrowserLanguagesResponse" />
        </wsdl:message>

        <wsdl:message name="GetAndroidIDsRequest">
            <wsdl:part element="phi:GetAndroidIDsRequest" name="GetAndroidIDsRequest" />
        </wsdl:message>
        <wsdl:message name="GetAndroidIDsResponse">
            <wsdl:part element="phi:GetAndroidIDsResponse" name="GetAndroidIDsResponse" />
        </wsdl:message>

        <wsdl:message name="GetAndroidGSFIDsRequest">
            <wsdl:part element="phi:GetAndroidGSFIDsRequest" name="GetAndroidGSFIDsRequest" />
        </wsdl:message>
        <wsdl:message name="GetAndroidGSFIDsResponse">
            <wsdl:part element="phi:GetAndroidGSFIDsResponse" name="GetAndroidGSFIDsResponse" />
        </wsdl:message>

        <wsdl:message name="GetAndroidHWFingerPrintsRequest">
            <wsdl:part element="phi:GetAndroidHWFingerPrintsRequest" name="GetAndroidHWFingerPrintsRequest" />
        </wsdl:message>
        <wsdl:message name="GetAndroidHWFingerPrintsResponse">
            <wsdl:part element="phi:GetAndroidHWFingerPrintsResponse" name="GetAndroidHWFingerPrintsResponse" />
        </wsdl:message>

        <wsdl:message name="GetiOSidentifierForVendorsRequest">
            <wsdl:part element="phi:GetiOSidentifierForVendorsRequest" name="GetiOSidentifierForVendorsRequest" />
        </wsdl:message>
        <wsdl:message name="GetiOSidentifierForVendorsResponse">
            <wsdl:part element="phi:GetiOSidentifierForVendorsResponse" name="GetiOSidentifierForVendorsResponse" />
        </wsdl:message>

        <wsdl:message name="GetAppUserNameRequest">
            <wsdl:part element="phi:GetAppUserNameRequest" name="GetAppUserNameRequest" />
        </wsdl:message>
        <wsdl:message name="GetAppUserNameResponse">
            <wsdl:part element="phi:GetAppUserNameResponse" name="GetAppUserNameResponse" />
        </wsdl:message>

        <wsdl:message name="GetDeviceUserNameRequest">
            <wsdl:part element="phi:GetDeviceUserNameRequest" name="GetDeviceUserNameRequest" />
        </wsdl:message>
        <wsdl:message name="GetDeviceUserNameResponse">
            <wsdl:part element="phi:GetDeviceUserNameResponse" name="GetDeviceUserNameResponse" />
        </wsdl:message>

        <wsdl:portType name="PhishingBlackListWS">
            <wsdl:operation name="GetIPs">
                <wsdl:input message="phi:GetIPsRequest" />
                <wsdl:output message="phi:GetIPsResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetCountries">
                <wsdl:input message="phi:GetCountriesRequest" />
                <wsdl:output message="phi:GetCountriesResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetReferers">
                <wsdl:input message="phi:GetReferersRequest" />
                <wsdl:output message="phi:GetReferersResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetCookies">
                <wsdl:input message="phi:GetCookiesRequest" />
                <wsdl:output message="phi:GetCookiesResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetBrowserLanguages">
                <wsdl:input message="phi:GetBrowserLanguagesRequest" />
                <wsdl:output message="phi:GetBrowserLanguagesResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetAndroidIDs">
                <wsdl:input message="phi:GetAndroidIDsRequest" />
                <wsdl:output message="phi:GetAndroidIDsResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetAndroidGSFIDs">
                <wsdl:input message="phi:GetAndroidGSFIDsRequest" />
                <wsdl:output message="phi:GetAndroidGSFIDsResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetAndroidHWFingerPrints">
                <wsdl:input message="phi:GetAndroidHWFingerPrintsRequest" />
                <wsdl:output message="phi:GetAndroidHWFingerPrintsResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetiOSidentifierForVendors">
                <wsdl:input message="phi:GetiOSidentifierForVendorsRequest" />
                <wsdl:output message="phi:GetiOSidentifierForVendorsResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetAppUserName">
                <wsdl:input message="phi:GetAppUserNameRequest" />
                <wsdl:output message="phi:GetAppUserNameResponse" />
            </wsdl:operation>
            <wsdl:operation name="GetDeviceUserName">
                <wsdl:input message="phi:GetDeviceUserNameRequest" />
                <wsdl:output message="phi:GetDeviceUserNameResponse" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="PhishingBlackListBinding" type="phi:PhishingBlackListWS">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

            <wsdl:operation name="GetIPs">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetCountries">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetReferers">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetCookies">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetBrowserLanguages">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetAndroidIDs">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetAndroidGSFIDs">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetAndroidHWFingerPrints">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetiOSidentifierForVendors">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetAppUserName">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>

            <wsdl:operation name="GetDeviceUserName">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="PhishingBlackListWS">
            <wsdl:port binding="phi:PhishingBlackListBinding" name="PhishingProcessBinding">
                <soap:address location="/lap/ext/ApprovalProcessWS" />
            </wsdl:port>
        </wsdl:service>
    </wsdl:definitions>
