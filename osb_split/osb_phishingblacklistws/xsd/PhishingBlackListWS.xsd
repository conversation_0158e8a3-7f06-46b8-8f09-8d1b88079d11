<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/lap/ws/phishingBlackList" xmlns="http://airbank.cz/lap/ws/phishingBlackList" jxb:version="2.1" xmlns:data="http://osb.abank.cz/digitization/rest" elementFormDefault="qualified">

        <xsd:element name="GetIPsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů ip:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetIPsResponse">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů ip:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetCountriesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů country:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCountriesResponse">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů country:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetReferersRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů referer:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetReferersResponse">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů referer:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetCookiesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů cookie:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetCookiesResponse">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů cookie:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetBrowserLanguagesRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů getBrowserLanguages:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetBrowserLanguagesResponse">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění do seznamu výstupních elementů getBrowserLanguages:string.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetAndroidIDsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetAndroidIDsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetAndroidGSFIDsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetAndroidGSFIDsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetAndroidHWFingerPrintsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetAndroidHWFingerPrintsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetiOSidentifierForVendorsRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetiOSidentifierForVendorsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetAppUserNameRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetAppUserNameResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="GetDeviceUserNameRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType/>
        </xsd:element>

        <xsd:element name="GetDeviceUserNameResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="data" type="data" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="timeToLive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="timeoutType" type="timeoutType" minOccurs="0" />
                    <xsd:element name="numberOfElements" type="xsd:long" minOccurs="0" />
                    <xsd:element name="creationTime" type="xsd:long" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="elementType" type="elementType" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:simpleType name="timeoutType">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="UNKNOWN" />
                <xsd:enumeration value="FIRST_SEEN" />
                <xsd:enumeration value="LAST_SEEN" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="elementType">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ALN" />
                <xsd:enumeration value="NUM" />
                <xsd:enumeration value="IP" />
                <xsd:enumeration value="PORT" />
                <xsd:enumeration value="ALNIC" />
                <xsd:enumeration value="DATE" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:complexType name="data">
            <xsd:sequence>
                <xsd:element minOccurs="0" name="last_seen" type="xsd:long" />
                <xsd:element minOccurs="0" name="first_seen" type="xsd:long" />
                <xsd:element minOccurs="0" name="source" type="xsd:string" />
                <xsd:element minOccurs="0" name="value" type="xsd:string" />
            </xsd:sequence>
        </xsd:complexType>


    </xsd:schema>
