<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: ade7192e20e24706640d8a747e5424e90d270c64 $ -->
<xs:schema targetNamespace="http://osb.banka.hci/CodeList/Notification/data" elementFormDefault="qualified"
	xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.banka.hci/CodeList/Notification/data">

	<xs:import schemaLocation="CommonTypes.xsd" namespace="http://osb.banka.hci/CommonTypes" />
	<xs:include schemaLocation="CommonCodeList.xsd"/>
	
	<xs:complexType name="CodeListNotification">
		<xs:annotation>
			<xs:documentation>Code list notification data type</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="codeListName" type="xs:string" maxOccurs="1" minOccurs="1">
				<xs:annotation>
					<xs:documentation>Code list name</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="timestamp" type="xs:dateTime" maxOccurs="1" minOccurs="1">
				<xs:annotation>
					<xs:documentation>Time of the code list update</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="item" type="tns:CodeListItem" maxOccurs="unbounded" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Complete code list data. Might be omitted by
						notifier if a code list is
						too large
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

</xs:schema>
