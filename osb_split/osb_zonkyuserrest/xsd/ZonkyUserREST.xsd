<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/ws/ZonkyUser" xmlns="http://airbank.cz/osb/ws/ZonkyUser" jxb:version="2.1" elementFormDefault="qualified" xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" nxsd:version="JSON" nxsd:encoding="UTF-8" nxsd:jsonTopLevelArray="true">

        <xsd:element name="SearchZonkyUsersRequest">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu položek a jejich naplnění podle CUIDu.</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" maxOccurs="unbounded" type="xsd:int" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>


        <xsd:element name="SearchZonkyUsersResponse">
            <xsd:annotation>
                <xsd:documentation>Načtení seznamu z výstupu REST služby a jeho naplnění do výstupních položek SOAP služby</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="Zonky" maxOccurs="unbounded" type="responseRef" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>



        <xsd:complexType name="responseRef">
            <xsd:sequence>
                <xsd:element name="user" type="user" minOccurs="0" maxOccurs="unbounded" />
                <xsd:element name="loanProducts" type="loanProducts" minOccurs="0" maxOccurs="unbounded" />
                <xsd:element name="investorProducts" type="investorProducts" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>



        <xsd:complexType name="user">
            <xsd:sequence>
                <xsd:element name="id" type="xsd:decimal" />
                <xsd:element name="firstName" type="xsd:string" />
                <xsd:element name="lastName" type="xsd:string" />
                <xsd:element name="status" type="UserStatus" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="loanProducts">
            <xsd:sequence>
                <xsd:element name="id" type="xsd:decimal" />
                <xsd:element name="status" type="LoanProductsStatus" />
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="investorProducts">
            <xsd:sequence>
                <xsd:element name="id" type="xsd:decimal" />
                <xsd:element name="type" type="Type" />
                <xsd:element name="status" type="InvestorProductsStatus" />
            </xsd:sequence>
        </xsd:complexType>



        <xsd:simpleType name="UserStatus">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ACTIVE" />
                <xsd:enumeration value="BLOCKED" />
                <xsd:enumeration value="EXPIRED" />
                <xsd:enumeration value="INACTIVE" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="LoanProductsStatus">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ACTIVE" />
                <xsd:enumeration value="APPROVED" />
                <xsd:enumeration value="CANCELED" />
                <xsd:enumeration value="COVERED" />
                <xsd:enumeration value="DECLINED" />
                <xsd:enumeration value="PAID" />
                <xsd:enumeration value="PAID_OFF" />
                <xsd:enumeration value="SCORED" />
                <xsd:enumeration value="SIGNED" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="Type">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="INVESTOR" />
                <xsd:enumeration value="RENTIER" />
            </xsd:restriction>
        </xsd:simpleType>

        <xsd:simpleType name="InvestorProductsStatus">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="APPLICANT" />
                <xsd:enumeration value="APPROVED" />
                <xsd:enumeration value="IN_TERMINATION" />
                <xsd:enumeration value="IN_WITHDRAWAL" />
                <xsd:enumeration value="DECLINED" />
                <xsd:enumeration value="IN_DEMISE" />
            </xsd:restriction>
        </xsd:simpleType>

    </xsd:schema>
