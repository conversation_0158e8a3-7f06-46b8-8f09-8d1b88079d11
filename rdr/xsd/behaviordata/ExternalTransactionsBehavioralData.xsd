<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns="http://airbank.cz/rdr/ws/behaviordata/externaltransaction"
           targetNamespace="http://airbank.cz/rdr/ws/behaviordata/externaltransaction" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:complexType name="ExternalAccount">
        <xs:annotation>
            <xs:documentation>Účet v cizí bance.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>N<PERSON><PERSON>v ú<PERSON>.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="AccountNumber">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="statementDmsId" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor vygenerovaného výpisu z účtu v DMS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="owner" type="AccountOwner" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Vlastníci účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="categorizedTransaction" type="CategorizedExternalTransaction" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kategorizované transakce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="recognisedIncome" type="RecognisedIncome" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozpoznané příjmy.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="behavioralCharacteristic" type="BehavioralCharacteristic" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální charakteristiky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountNumber" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstraktní typ reprezentující číslo účtu.</xs:documentation>
        </xs:annotation>
    </xs:complexType>

    <xs:complexType name="DomesticAccountNumber">
        <xs:annotation>
            <xs:documentation>Tuzemské číslo účtu.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AccountNumber">
                <xs:sequence>
                    <xs:element name="prefix" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Prefix.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="number" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Číslo.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bankCode" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kód banky.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ForeignAccountNumber">
        <xs:annotation>
            <xs:documentation>Mezinárodní číslo účtu.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AccountNumber">
                <xs:sequence>
                    <xs:element name="iban" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>IBAN.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AccountOwner">
        <xs:annotation>
            <xs:documentation>Vlastník účtu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="fullName" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Celé jméno.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BehavioralCharacteristic">
        <xs:annotation>
            <xs:documentation>Behaviorální charakteristika.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="number" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet transakcí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="czkAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka v CZK.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="date" type="xs:dateTime">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum (ke dni, popř. od).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateTo" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum do.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="TransactionsBehavioralCharacteristicCode">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód charakteristiky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Amount">
        <xs:annotation>
            <xs:documentation>Komplexní typ reprezentující částku.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="value" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="currency" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měna.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="TransactionsBehavioralCharacteristicCode">
        <xs:annotation>
            <xs:documentation>Enumerace kódů behaviorální charakteristiky transakcí.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CREDIT_TRANSACTIONS_SUM">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Suma kreditních transakcí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEBIT_TRANSACTIONS_SUM">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Suma debetních transakcí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ATM_WITHDRAWAL_SUM_AND_NUMBER">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Suma a počet výběrů z bankomatu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AVAILABLE_BALANCE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Disponibilní zůstatek na účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MINIMAL_BALANCE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Minimální zůstatek na účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="RecognisedIncome">
        <xs:annotation>
            <xs:documentation>Rozpoznaný příjem.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="czkAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka v CZK.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalCurrencyAmount" type="Amount" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka v původní měně.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číselný kód typu příjmu v CIFu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="RecognisedIncomeStatus">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Stav.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairingFailureReason" type="IncomePairingFailureReason" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Důvod nespárování rozpoznaného příjmu s daty zadanými klientem v žádosti.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairingMethod" type="IncomePairingMethod" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Způsob spárování příjmu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="categorizedTransaction" type="CategorizedExternalTransaction" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kategorizované transakce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountInfo">
        <xs:annotation>
            <xs:documentation>Informace o účtu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ownerName" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název vlastníka.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="AccountNumber">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CategorizedExternalTransaction">
        <xs:annotation>
            <xs:documentation>Kategorizovaná transakce na účtu v cizí bance.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="externalId" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor transakce v cizí bance.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="czkAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka v CZK.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalCurrencyAmount" type="Amount" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka v původní měně.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bookingDate" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas zaúčtování.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="valueDate" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas provedení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="messageToReceiver" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zpráva pro příjemce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="counterparty" type="Counterparty" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Protistrana.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="category" type="ExternalTransactionCategory" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kategorie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Counterparty">
        <xs:annotation>
            <xs:documentation>Protistrana.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="brandName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název vlastníka účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountNumber" type="AccountNumber" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ExternalTransactionCategory">
        <xs:annotation>
            <xs:documentation>Kategorie transakce.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ruleId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor pravidla v LAPu, které vedlo ke ketagorizaci transakce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="ExternalTransactionCategoryCode">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód kategorie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ExternalTransactionCategoryCode">
        <xs:annotation>
            <xs:documentation>Enumerace kódů kategorie transakce.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="GAMBLING">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hazard.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="GAMING">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hry a mikrotransakce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="UNDESIRABLE_COMPANY">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Transakce do malých nebankovek/nežádoucích finančních institucí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DISTRAINT">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Exekuce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SOCIAL_SUBVENTION">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Sociální dávka/příspěvek.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SALARY">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Mzda.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="RecognisedIncomeStatus">
        <xs:annotation>
            <xs:documentation>Enumerace kódů stavu rozpoznání příjmu.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="RECOGNISED">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozpoznáno, ale nepotvrzeno klientem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VERIFIED_BY_SYSTEM">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozpoznaný příjem sprárován vůči informacím zadaných klientem v rámci dat žádosti.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="IncomePairingFailureReason">
        <xs:annotation>
            <xs:documentation>Enumerace důvodů nespárování rozpoznaného příjmu s daty zadnými v žádosti klientem.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="INCOME_TYPE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nespárováno z důvodu neshodujícího se typu příjmu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INCOME_AMOUNT">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nespárováno z důvodu neshodující se částky příjmu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EMPLOYER">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nespárováno z důvodu neshodujících se dat zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACCOUNT_FOREIGN_CURRENCY">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nespárováno z důvodu účtu Rozpoznaného příjmu v cizí měně.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TRANSACTION_FOREIGN_CURRENCY">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nespárováno z důvodu transakce Rozpoznaného příjmu v cizí měně.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TRANSACTION_FOREIGN_ACCOUNT">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nespárováno z důvodu zahraničního účtu transakce Rozpoznaného příjmu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="IncomePairingMethod">
        <xs:annotation>
            <xs:documentation>Enumerace způsobů spárování příjmů.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="INCOME_AMOUNT">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Párování na základě výše příjmu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EMPLOYER_ACCOUNT_NUMBER">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Párování na základě čísla účtu zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EMPLOYER_NAME">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Párování na základě názvu zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EMPLOYER_NAME_PARTS">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Párování na základě části názvu zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="TransactionsBehavioralData">
        <xs:annotation>
            <xs:documentation>Behaviorální data o transakcích.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="extractionDate" type="xs:dateTime">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum vytěžení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="account" type="ExternalAccount" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Účty v cizích bankách.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>