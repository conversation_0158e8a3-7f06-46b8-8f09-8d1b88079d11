<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns:score="http://airbank.cz/rdr/ws/summaryservice/manualdecision"
           xmlns:cd="http://airbank.cz/rdr/ws/consolidationdata"
           xmlns="http://airbank.cz/rdr/ws/approvalprocess"
           targetNamespace="http://airbank.cz/rdr/ws/approvalprocess" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:import namespace="http://airbank.cz/rdr/ws/summaryservice/manualdecision" schemaLocation="../xsd/ManualDecision.xsd"/>
    <xs:import namespace="http://airbank.cz/rdr/ws/consolidationdata" schemaLocation="../xsd/ConsolidationData.xsd"/>
    <xs:include schemaLocation="../xsd/ApplicationData.xsd"/>

    <xs:complexType name="Activity" abstract="true">
        <xs:annotation>
            <xs:documentation>Entita eviduje výkon automatické i manuální aktivity jak v LAP, tak v AMS/AMG.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activityExternalId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Externí id aktivity</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód aktivity</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="executionStarted" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Začátek zpracovávání aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="executionFinished" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ukončení zpracování aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="result" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek Aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="userId" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace uživatele, který vykonal manuální aktivitu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="activityBusinessProcess" type="ActivityBusinessProcess" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Businessové rozdělení aktivit v RDR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="GetActivity">
        <xs:annotation>
            <xs:documentation>Entita eviduje výkon automatické i manuální aktivity jak v LAP, tak v AMS.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Interní id aktivity v RDR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="activityExternalId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Externí id aktivity</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód aktivity</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="executionStarted" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Začátek zpracovávání aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="executionFinished" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ukončení zpracování aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="executionUpdated" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Čas modifikace manuální aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="result" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek Aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="userId" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace uživatele, který vykonal manuální aktivitu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalProcessId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identificace schvalovacího procesu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="adHoc" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Příznak, zda schvalovací proces běžel v režimu AdHoc.
                                * adHoc = true
                                * adHoc = false - Standardní schvalování s dopadem na řízení dalšího flow žádosti v AMS.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="activityBusinessProcess" type="ActivityBusinessProcess" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Businessové rozdělení aktivit v RDR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="ActivitySource" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj ze kterého aktivita byla vytvořena.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ApprovalProcessActivity">
        <xs:complexContent>
            <xs:extension base="Activity">
                <xs:sequence>
                    <xs:element name="adHoc" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Příznak, zda schvalovací proces běžel v režimu AdHoc.
                                        * adHoc = true
                                        * adHoc = false - Standardní schvalování s dopadem na řízení dalšího flow žádosti v AMS.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="decisionOutput" type="DecisionEngineOutput" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Decision output vynesený Blazem (Decision enginem) v aktivitě.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ManualActivity">
        <xs:annotation>
            <xs:documentation>Operace při níž byly vytvořeny dodatečné informace.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Activity">
                <xs:sequence>
                    <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Testový popis podrobně specifikující změnu touto operací. např: "Změna aplikačních dat žádosti". Kapacita 1000
                                        znaků.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="manualDecision" type="score:ManualDecision" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Instrukce pro Blaze od underwritera.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="executionUpdated" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Čas modifikace manuální aktivity.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Person">
        <xs:annotation>
            <xs:documentation>Osoby v obálce. Lze zaslat pouze různá cuids. Pravidlo 1 žadatel pro jednu roli.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace osoby v obálce dle CIF.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="role" type="PersonRole" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Osoba v obálce je vždy nějaké role dle definice výčtového typu PersonRole.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="EnvelopeType">
        <xs:annotation>
            <xs:documentation>Typy obálek z AMS.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CARD"/>
            <xs:enumeration value="SME_CARD"/>
            <xs:enumeration value="SME_CONTRACT"/>
            <xs:enumeration value="SME_CHANGE_ENTITLED_PERSON"/>
            <xs:enumeration value="CONTRACT"/>
            <xs:enumeration value="CURRENT_ACCOUNT"/>
            <xs:enumeration value="SME_CURRENT_ACCOUNT"/>
            <xs:enumeration value="DISPONENT"/>
            <xs:enumeration value="SME_DISPONENT"/>
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="MORTGAGE_REF"/>
            <xs:enumeration value="SAVING_ACCOUNT"/>
            <xs:enumeration value="SME_SAVING_ACCOUNT"/>
            <xs:enumeration value="CHILD_SAVING_ACCOUNT"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="CARD_REPLACEMENT"/>
            <xs:enumeration value="CARD_VIRTUAL"/>
            <xs:enumeration value="PENSION"/>
            <xs:enumeration value="SPLIT_PAYMENT"/>
            <xs:enumeration value="STOCK_ETF"/>
            <xs:enumeration value="TERM_DEPOSIT"/>
            <xs:enumeration value="INVESTMENT_CERTIFICATE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ApplicationType">
        <xs:annotation>
            <xs:documentation>Typy žádostí z AMS.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACCOUNT"/>
            <xs:enumeration value="AFFIDAVIT"/>
            <xs:enumeration value="AUTHORIZED_PERSON"/>
            <xs:enumeration value="CASHLOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="DEBIT_CARD"/>
            <xs:enumeration value="GENERAL_CONTRACT"/>
            <xs:enumeration value="MORTGAGE_REF"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="PENSION"/>
            <xs:enumeration value="SME_CONTRACT" />
            <xs:enumeration value="SME_CONTRACT_PARTICIPANT"/>
            <xs:enumeration value="SME_CHANGE_ENTITLED_PERSON"/>
            <xs:enumeration value="SPLIT_PAYMENT"/>
            <xs:enumeration value="STOCK_ETF"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="CompletionSubjectType">
        <xs:annotation>
            <xs:documentation>Předměty kompletace z OBS.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADD_CHILD_DISP"/>
            <xs:enumeration value="ADD_DISP"/>
            <xs:enumeration value="ANNOU_CHILD_DISP"/>
            <xs:enumeration value="ANNOU_DISP"/>
            <xs:enumeration value="CREATE_BU"/>
            <xs:enumeration value="CREATE_DK"/>
            <xs:enumeration value="CREATE_CHILD_DK_HOLDER"/>
            <xs:enumeration value="CREATE_DK_HOLDER"/>
            <xs:enumeration value="CREATE_HYSA"/>
            <xs:enumeration value="CREATE_LOAN"/>
            <xs:enumeration value="CREATE_OVERDRAFT"/>
            <xs:enumeration value="CREATE_REFINANCING"/>
            <xs:enumeration value="CREATE_VK"/>
            <xs:enumeration value="DECLARATE_CHILD_HOLDER"/>
            <xs:enumeration value="DECLARATE_HOLDER"/>
            <xs:enumeration value="RHY_ACCESSN_TO_DEBT"/>
            <xs:enumeration value="RHY_CODEBT_AMENDMNT"/>
            <xs:enumeration value="RHY_DEBTOR_AMENDMNT"/>
            <xs:enumeration value="RHY_LOAN_AGREEMENT"/>
            <xs:enumeration value="RHY_LOAN_COMMITMENT"/>
            <xs:enumeration value="RHY_VERIFY_CODEBTOR"/>
            <xs:enumeration value="RS"/>
            <xs:enumeration value="PP_INSURANCE_CREATE"/>
            <xs:enumeration value="PP_INSURANCE_CANCEL"/>
            <xs:enumeration value="PPI_CREATION_CODEBTOR"/>
            <xs:enumeration value="CREATE_CHILD_SAVING_ACCOUNT"/>

            <xs:enumeration value="SME_GC"/>
            <xs:enumeration value="SME_CREATE_ENTITLED"/>
            <xs:enumeration value="SME_CHANGE_ENTITLED"/>
            <xs:enumeration value="SME_CREATE_BUFOP"/>
            <xs:enumeration value="SME_CREATE_BUPO"/>
            <xs:enumeration value="SME_CREATE_SUFOP"/>
            <xs:enumeration value="SME_CREATE_SUPO"/>
            <xs:enumeration value="SME_CREATE_DEBIT_CARD"/>

            <xs:enumeration value="CREATE_SPLIT_PAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PersonRole">
        <xs:annotation>
            <xs:documentation>Výčtový typ rolí osoby na žádost z AMS / OBS</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPLICANT"/>
            <xs:enumeration value="CARD_HOLDER"/>
            <xs:enumeration value="DISPONENT_AND_CARD_HOLDER"/>
            <xs:enumeration value="CODEBTOR"/>
            <xs:enumeration value="DISPONENT"/>
            <xs:enumeration value="ENTITLED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ActivityBusinessProcess">
        <xs:annotation>
            <xs:documentation>Businessové rozdělení aktivit v RDR.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPLICATION"/>
            <xs:enumeration value="COMPLETION"/>
            <xs:enumeration value="PRE_DRAWN_CHECK"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ActivitySource">
        <xs:annotation>
            <xs:documentation>Zdroj vytvořené aktivity.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVAL_PROCESS"/>
            <xs:enumeration value="MANUAL"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="CommonRiskData" abstract="true">
        <xs:sequence>
            <xs:element name="personScores" type="PersonScore" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>personalizovaný scoring</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="decisions" type="Decision" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Odpovídá FP LAP: productResults[]
                                K dané žádosti (application) a výsledku (result) daného workflow může existovat více productType. Toto je způsobeno tím, že v
                                případě úvěrových žádostí lze zapsat výsledek pro jiný druh úvěrové žádosti, než byla samotná žádost, která se považuje za tzv.
                                alternativní žádost (=nabídku), která v daný okamžik fyzicky neexistuje.
                                V zásadě by měl být ProductResult jenom vyjádřením se = rozhodnutí k různým žádostem v obálce a neměla by přibývat další úroveň
                                členění dle productType. Proto se alternativa eviduje jako specifický druh žádosti (předmětu schvalování).
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationDecisions" type="ApplicationDecision" minOccurs="0" maxOccurs="2">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application decision - pokud jsou dvě, jedno z nich je alternativa.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="warnings" type="Warning" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Odpovídá FP warnings[] v LAPu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="hardChecks" type="HardCheck" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledky tvrdých kontrol, číselník v BLAZE. Odpovídá FP hardChecksResults[].</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="alerts" type="Alert" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zásobník proměnných Blaze k danému resultu.
                                (Také se používá Warningy z Blaze, ale nejedná se o klasické warningy, jde o to, že Blaze zaznamená v rámci schvalovacího
                                procesu nějaké špatné plnění hodnot vektoru, na což tímto upozorní.)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="blazeStatuses" type="BlazeStatus" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zásobník proměnných Blaze k danému resultu.
                                (Také se používá Warningy z Blaze, ale nejedná se o klasické warningy, jde o to, že Blaze zaznamená v rámci schvalovacího
                                procesu nějaké špatné plnění hodnot vektoru, na což tímto upozorní.)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="result" type="Result" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek schvalování v LAPu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractDecisionOutput">
        <xs:annotation>
            <xs:documentation>Společný předek pro Decision output.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CommonRiskData">
                <xs:sequence>
                    <xs:element name="summaryRiskData" type="SummaryRiskData" minOccurs="0"/>
                    <xs:element name="productRiskData" type="ProductSpecificRiskData" minOccurs="0" maxOccurs="4"/>
                    <xs:element name="riskGrade" type="RiskGradeResults" minOccurs="0"/>
                    <xs:element name="consolidationData" type="cd:ConsolidationData" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Zásobník proměnných Blaze k danému resultu.
                                        (Také se používá Warningy z Blaze, ale nejedná se o klasické warningy, jde o to, že Blaze zaznamená v rámci
                                        schvalovacího
                                        procesu nějaké špatné plnění hodnot vektoru, na což tímto upozorní.)
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="riskInterestRates" type="RiskInterestRate" minOccurs="0" maxOccurs="2"/>
                    <xs:element name="finalInterestRates" type="FinalInterestRate" minOccurs="0" maxOccurs="2"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RiskInterestRate">
        <xs:annotation>
            <xs:documentation>Reprezentuje nápočet riskových sazeb ve scoringu žádosti.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long"/>
            <xs:element name="productType" type="LoanProductType"/>
            <xs:element name="fixedInterestRate" type="xs:boolean" default="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Defaultně bude nastaven na 0, jeho změnu bude řídit Blaze, podle toho zda mu byla doručena manuálně nastavená RIR z OT tabulky do UW scoringu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRatesRanges" type="InterestRatesRange" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InterestRatesRange">
        <xs:annotation>
            <xs:documentation>Představuje jednotlivá pásma riskových sazeb a k nim náležící úrokové sazby.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanAmountFrom" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Spodní hranice pásma, pro kterou je úroková saba nastavena (Můžeme očekávat od Blaze, že bude zohledňovat produktové minimum?).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmountTo" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Horní hranice pásma, pro které bude úroková sazba nastavena. Hordní hranice pásma bude vždy o 1 Kč nižší než spodní hranice následujícího pásma.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskInterestRateStandard" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Standardní úroková sazba.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskInterestRateBonus" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Bonusová úroková saba.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FinalInterestRate">
        <xs:annotation>
            <xs:documentation>Představuje finální sazby pro žádost klienta po aplikované parametrizaci. Finální sazby budou napočteny i při adHoc scoringu, bude mít tedy svou OT reprezentaci. Mohou být napočteny pro žádost a zároveň pro alternativní nabídku, budou-li k dispozici riskové sazby.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long"/>
            <xs:element name="productType" type="LoanProductType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Definuje, pro který produkt byly finální sazby napočtey.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmount" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka, ke které je sazba napočtena.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRateStandard" type="xs:decimal"/>
            <xs:element name="interestRateBonus" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="LoanProductType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CASH_LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="SPLIT_PAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="DecisionEngineOutput">
        <xs:annotation>
            <xs:documentation>Odpovídá FP LAP: results[]
                Záznam o proběhnutém scoringu pro daný výkon workflow.
                Jedná se o provedené scoringy BLAZE.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractDecisionOutput">
                <xs:sequence>
                    <xs:element name="requiredDocuments" type="RequiredDocument" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Ve vektoru ji evidujeme jako FP allowedDistribution.
                                        Kombinace požadovaných primárních dokumentů pro jednotlivé distribuční kanály. S rozlišením na osobu.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="allowedSignChannels" type="AllowedSignChannel" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Ve vektoru evidujeme jako FP allowedChannel.
                                        Způsoby podpisu a povolené kanály doručení smluvní dokumentace vydávané bankou.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requiredManualTasks" type="ManualTask" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Požadované manuální aktivity.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="strategy" type="Strategy" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Odkaz na stregii, nepovinné pro AMS</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scores" type="Score" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="applicationDataStatus" type="ApplicationDataStatus" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Score">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1"/>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="level" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="value" type="xs:decimal" minOccurs="1" maxOccurs="1"/>
            <xs:element name="scorePredictors" type="ScorePredictor" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ScorePredictor">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1"/>
            <xs:element name="scorecardName" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="predictorBin" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="predictorCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="predictorScore" type="xs:decimal" minOccurs="1" maxOccurs="1"/>
            <xs:element name="predictorValue" type="xs:string" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="GetSummary" abstract="true">
        <xs:annotation>
            <xs:documentation>Poslední evidované score.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CommonRiskData">
                <xs:sequence>
                    <xs:element name="lastActivity" type="GetActivity" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Poslední aktivita, která měla za následek změnu score.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="riskInterestRates" type="RiskInterestRate" minOccurs="0" maxOccurs="2"/>
                    <xs:element name="finalInterestRates" type="FinalInterestRate" minOccurs="0" maxOccurs="2"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ApplicationSummary">
        <xs:annotation>
            <xs:documentation>Výsledek schvalování žádostí v LAPu.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="GetSummary">
                <xs:sequence>
                    <xs:element name="manualDecision" type="score:ManualDecision" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Poslední manuálně evidované rozhodnutí operátora.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationData" type="cd:ConsolidationData" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Zásobník proměnných Blaze k danému resultu.
                                        (Také se používá Warningy z Blaze, ale nejedná se o klasické warningy, jde o to, že Blaze zaznamená v rámci
                                        schvalovacího
                                        procesu nějaké špatné plnění hodnot vektoru, na což tímto upozorní.)
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="summaryRiskData" type="SummaryRiskData" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="productRiskData" type="ProductSpecificRiskData" minOccurs="0" maxOccurs="4"/>
                    <xs:element name="riskGrade" type="RiskGradeResults" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CompletionSummary">
        <xs:annotation>
            <xs:documentation>Výsledek schvalování žádostí v LAPu.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="GetSummary">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AdHocSummary">
        <xs:annotation>
            <xs:documentation>Výsledek schvalování žádostí v LAPu.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="GetSummary">
                <xs:sequence>
                    <xs:element name="summaryRiskData" type="SummaryRiskData" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="productRiskData" type="ProductSpecificRiskData" minOccurs="0" maxOccurs="4"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Result">
        <xs:annotation>
            <xs:documentation>Výsledek schvalování v LAPu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Druh scoringu - businessová část schvalovacího procesu, ve které jsou posuzovány logicky související entity a
                                údaje podle BLAZE. Kapacita 32 znaků. FP: result[].resultType
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="nextWfCode" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Informace o dalším požadovaném workflow.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="created" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum vytvoření.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <!-- TODO
                    tohle fyzicky neni Result od Blazu, ale vysledek ApprovalProcesu. Spis mi dava smysl to dat jako atribut na ApprovalProcess
                    zname hodnoty: CLIENT_CANCEL, FAIL_TO_FINISH, FI_CANCEL, REJECT, SUCCESS, TERMINATED_REJECT
             -->
            <xs:element name="result" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek ApprovalProcesu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Důvod výsledku.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="reasonClient" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Důvod výsledku pro klienta.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="comment" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komentář.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskGroup" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Interní atribut BLAZEu určený pro monitoring a zpětnou identifikaci konkrétních případůpři analýzách dopadu úprav BLAZEu.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Strategy">
        <xs:annotation>
            <xs:documentation>Použitá strategie BLAZE</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jméno Scoring strategie</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ strategie (champion/challenger/manual/backup).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace verze strategie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="versionDate" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum nasazení použité verze strategie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ManualTask">
        <xs:annotation>
            <xs:documentation>Požadovaná aktivita k manuálnímu prověření.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CUID osoby v obálce</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeType" type="IncomeType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ příjmu k prověření.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="OperationType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ operace - přidat/odebrat.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Následující manuální aktivita (její identifikační název).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="order" type="xs:int" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pořadí prověřované aktivity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="IncomeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAIN_INCOME">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hlavní příjem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTHER_INCOME">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Vedlejší příjem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Decision">
        <xs:annotation>
            <xs:documentation>Odpovídá FP LAP: productResults[]
                K dané žádosti (application) a výsledku (result) daného workflow může existovat více productType. Toto je způsobeno tím, že v případě úvěrových
                žádostí lze zapsat výsledek pro jiný druh úvěrové žádosti, než byla samotná žádost, která se považuje za tzv. alternativní žádost (=nabídku),
                která v daný okamžik fyzicky neexistuje.
                V zásadě by měl být ProductResult jenom vyjádřením se = rozhodnutí k různým žádostem v obálce a neměla by přibývat další úroveň členění dle
                productType. Proto se alternativa eviduje jako specifický druh žádosti (předmětu schvalování).
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="result" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Původně: productSystemDecision.Výsledek scoringu pro danou žádost.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="comment" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komentář k rozhodnutí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Důvod rozhodnutí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="reasonClient" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Důvod rozhodnutí pro klienta.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalSubject" type="ApprovalSubjectReference" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reference na approval subject</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RequiredDocument">
        <xs:annotation>
            <xs:documentation>Ve vektoru ji evidujeme jako FP allowedDistribution.
                Kombinace požadovaných primárních dokumentů pro jednotlivé distribuční kanály. S rozlišením na osobu.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komu v obálce je vyžadovaný dokument určen.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <!--TODO ciselnik v MDM-->
            <xs:element name="deliveryWay" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Data týkající se předepsaného způsobu (distribuční kanál) doručení dokumentace, data za daný produkt.
                                distribuční kanály, hodnoty z číselníku. číselník bude uložen v IB, číselníkové kódy budou popsány v SD IB (walkin), příklad
                                hodnot distribučních kanálů.
                                - pošta (POST)
                                - IB (IB)
                                - Kurýr (MESSENGER)
                                - pobočka (BRANCH)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <!--TODO ciselnik-->
            <xs:element name="groupType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Skupina do které daný dokument náleží (vychází z definice skupin dokumentů v CIFu) :
                                "PRIMARY" , "SECONDARY", "IDENTIFICATION" ,"FINANCIAL", "ADDRESS", "MOBILE_PHONE", "NODEBT", "REMOTE_IDENTIFICATION",
                                "JOINT_ASSETS", "BUSINESS", "OTHER"
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <!--TODO ciselnik-->
            <xs:element name="documentType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ dokumentu. Číselníkové hodnoty definované a spravované v CIFu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <!--TODO product type???-->
            <xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ produktu - DEPOSIT, LOAN, CONSOLIDATION, MORTGAGE_REF, AFFIDAVIT</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="groupRelation" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Vazba na první (1 - hlavní příjem) nebo druhý (2 - vedlejší příjem) příjem klienta. Vyplněno v případě, že se
                                dokument vztahuje ke konkrétnímu příjmu (např. výplatní páska).
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="requiredCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet požadovaných dokumentů z dané skupiny případně ještě vztažené k prvnímu nebo druhému příjmu klienta.
                                (např. požadavek na doložení 2 dokladů ze skupiny "FINANCIAL" vztažené k 1 příjmu klienta)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet požadovaných dokumentů (např. 3 výplatní pásky)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanBinFrom" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze pro úvěry, doklad je požadován pokud požadovaná částka úvěru překročí hodnotu LoanBinFrom.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="OperationType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze pro úvěry, doklad je požadován pokud požadovaná částka úvěru překročí hodnotu LoanBinFrom.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="HardCheck">
        <xs:annotation>
            <xs:documentation>Výsledky tvrdých kontrol, číselník v BLAZE. Odpovídá FP hardChecksResults[].</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komu v obálce je tvrdá kontrola určena.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="koCode" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód tvrdé kontroly, která skončila negativním výsledkem (způsobila zamítnutí).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="businessProcess" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Businesový proces (žádost, kompletace)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="OperationType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze pro úvěry, doklad je požadován pokud požadovaná částka úvěru překročí hodnotu LoanBinFrom.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalSubject" type="ApprovalSubjectReference" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reference na approval subject</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DecisionLoan">
        <xs:annotation>
            <xs:documentation>Rozšíření rozhodnutí. týkající se úvěrových produktů (CASHLOAN a CONSOLIDATION). Rozhodnutí se může týkat i alternativy a je dáno
                scoringem a
                předmětem schvalování.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Decision">
                <xs:sequence>
                    <xs:element name="consolidationMode" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Režim konsolidace. Jen pro typ předmětu schvalování CONSOLIDATION. Ve vektoru se jedná o FP
                                        productresults[].productScoreResult. Očekávané hodnoty 0+, 0-
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="creditAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše úvěru pro daný scoring a předmět schvalování (žádost nebo alternativa). FP:
                                        outputData.changeProductParameters.creditAmount
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentCount" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Počet splátek pro daný scoring a typ produktu. FP: outputData.changeProductParameters.paymentSum</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRate" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>FP: outputdata.changeProductParameters.yearInterestRate</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateBonus" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>bonusová sazba - FP: outputdata.changeProductParameters.bonusYearInterestRate</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="drawingPostponedTo" type="xs:dateTime" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum na které je odloženo možné čerpání.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="DecisionMortgage">
        <xs:annotation>
            <xs:documentation>
                Třída DecisionMortgage obsahuje rozhodnutí týkající se předmětu schvalování Refinancování hypotéky (=rozhodnutí o hypotéce).
                (ApprovalSubjectType.MORTGAGE_REF). LAP FP: mortgageResults[].*
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Decision">
                <xs:sequence>
                    <xs:element name="maximalApprovedAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>maximální schválená částka hypotéky</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mls" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>mls na žádost o hypotéku</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mlsHousehold" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>mls na žádost o hypotéku pro domácnost.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="riskGrade" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>riskGrade na žádosti o hypotéku</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pairedContract" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Napárovaný kontrakt z registru BRKI.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="AllowedSignChannel">
        <xs:annotation>
            <xs:documentation>Ve vektoru evidujeme jako FP allowedChannel.
                Způsoby podpisu a povolené kanály doručení smluvní dokumentace vydávané bankou.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komu v obálce je povolený kanál doručení určen.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveryWay" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Povolené kanály doručení smlouvy / dodatku. Vrací Blaze.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="signWay" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Povolené způsoby podpisu smlouvy / dodatku. Vrací Blaze.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ produktu - DEPOSIT, LOAN, CONSOLIDATION, MORTGAGE_REF, AFFIDAVIT</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="contractType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ publikovaneho dokumentu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cashLoanAmountTo" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Maximální částka pro povolení kanálu. Pouze pro úvěry. Kanál doručení je povolený, pokud úvěr nepřekročí
                                hodnotu
                                cashLoanAmountTo.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="OperationType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze pro úvěry, doklad je požadován pokud požadovaná částka úvěru překročí hodnotu LoanBinFrom.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Warning">
        <xs:annotation>
            <xs:documentation>Odpovídá FP warnings[] v LAPu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komu v obálce je warning určen.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód warningu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="detail" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Text warningu pro pracovníka BO</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskFlag" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>nově (od R8) bude BLAZE vracet WRN, které ale mají mít spíše jen upozorňovací charakter pro zpracovatele (např.
                                EMPNOINFOW/EXPERTINCOMEW, MANPAIRREASONW), tedy stávající příznak "riskFlag" na WRN.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="businessProcess" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Businesový proces (žádost, kompletace)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="OperationType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze pro úvěry, doklad je požadován pokud požadovaná částka úvěru překročí hodnotu LoanBinFrom.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalSubject" type="ApprovalSubjectReference" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reference na approval subject</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ApprovalSubjectReference">
        <xs:annotation>
            <xs:documentation>Reference na approval subject.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
            </xs:element>
            <xs:element name="discriminator" type="ApprovalSubjectDiscriminator" minOccurs="1" maxOccurs="1">
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApprovalSubjectDiscriminator">
        <xs:annotation>
            <xs:documentation>Diskriminator pro vazbu na schvalovany predmet</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="COMPLETION">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Predmet schvalovani</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="APPLICATION">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>AMS pro žádost</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ENVELOPE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Envelope</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OFFER">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nabídka</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AbstractPersonScore" abstract="true">
        <xs:annotation>
            <xs:documentation>Rozšíření Score pro úvěrové produkty.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="scoreBehavioralData" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Původně scoreEnd2
                                1- pravděpodobnost defaultu (dlouhá)
                                Behav. score. (behaviorální data)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="raroi" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Risk adjusted ROI</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cutOffValue" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota cutOff</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cutOffId" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID CutOff</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanBinAllocation" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Procentuální alokace na každou osobu.
                                FP: personScore[].loanBinAllocation
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiInstallmentsSum" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>suma splátek v registrech, zprávy z úvěrových registrů jsou kombinovány</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiInstallmentsSumAdjusted" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>suma splátek v registrech, zprávy z úvěrových registrů jsou kombinovány</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="minInstallment" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Minimální výše splátky. Bude zřejmě rozpočítáváno na osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace osoby z obálky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PersonScore">
        <xs:annotation>
            <xs:documentation>Rozšíření Score pro úvěrové produkty.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanBinAllocation" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Procentuální alokace na každou osobu.
                                FP: personScore[].loanBinAllocation
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiInstallmentsSum" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>suma splátek v registrech, zprávy z úvěrových registrů jsou kombinovány</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiInstallmentsSumAdjusted" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>suma splátek v registrech, zprávy z úvěrových registrů jsou kombinovány</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="minInstallment" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Minimální výše splátky. Bude zřejmě rozpočítáváno na osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace osoby z obálky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="scoreApplicationData" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Původně scoreEnd1
                                1- pravděpodobnost defaultu (kratka)
                                Apl. score (aplikační data)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskGrade" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>RiskGrade rozlišený dle osoby. Zakazují se NULL value.
                                FP: personScore[].riskGrade
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="income" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Celkový příjem klienta</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <!--Mozna odstranit-->
            <xs:element name="creditExposureSecured" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Score z úvěrových zpráv, které vybral Blaze v rámci posuzování úvěrové žádosti.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditExposureUnsecured" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úvěrová angažovanost na nezajištěných úvěrech v AB</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>

            <xs:element name="usedCbReport" type="UsedCbReport" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Data úvěrové zprávy, kterou vybral Blaze v rámci posuzování úvěrové žádosti</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="mls" type="Mls" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>MLS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="UsedCbReport">
        <xs:annotation>
            <xs:documentation>Risková data ukazující na využitou úvěrovou zprávu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="score" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Score z úvěrových zpráv (scoreBrkiRaw), které vybral Blaze v rámci posuzování úvěrové žádosti. FP:
                                score[].usedCbReport.score
                                Zakazuje se NULL value
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="received" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas přijetí úvěrové zprávy, která byla využita. Zakazuje se NULL value.
                                FP: personcore[].usedCbReport.received
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditBureauRequestId" type="xs:int" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace požadavku na dotaz dle RCM. Povinný údaj. Používá se k propojení s ostatními částmi úvěrové zprávy
                                (příslušné kolekce ve FP sourceData).
                                FP: personScore[].usedCbReport.creditBureauRequestId
                                Nejedná se o identifikátor žádosti. Jedná se o unikátní identifikaci dotazu do úvěrového registru RCM.CreditBureauRequest.id.
                                Zakazuje se NULL value.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractMls" abstract="true">
        <xs:annotation>
            <xs:documentation>MLS.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="productType" type="LoanProductType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CASH_LOAN, CONSOLIDATION</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="evaluationArea" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Oblast dat z níž je MLS vypočten: RISK, REGISTER, RISK_WITH_CL, RISK_WITHOUT_LOANS, HOUSEHOLD (číselník MDM)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota MLS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Mls">
        <xs:annotation>
            <xs:documentation>MLS.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractMls">
                <xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractApplicationDecision" abstract="true">
        <xs:annotation>
            <xs:documentation>Application decision.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="productType" type="ProductType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CASH_LOAN, CONSOLIDATION, OVERDRAFT</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Schválená částka úvěru.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxTerm" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Maximální počet splátek na daný úvěr.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ApplicationDecision">
        <xs:annotation>
            <xs:documentation>Application Decision.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractApplicationDecision">
                <xs:sequence>
                    <xs:element name="applicationId" type="xs:long"/>
                    <xs:element name="offer" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>True, pokud se jedná o nabídku. Určeno na základě approval subjektů v obálce.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="amountFastTrack" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše disponibilního úvěrového rámce, který je možné klientovi nabídnout bez dokládání dodatčených dokladů
                                        klientem.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validToFastTrack" type="xs:dateTime" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum konce platnosti fast tracku LoanBinu</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="SummaryRiskData">
        <xs:sequence>
            <xs:element name="rg4Dsti" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>RG použitý pro výpočet DSTI.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dti" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debt To Income.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dsti" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debt Service to Income.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ProductSpecificRiskData">
        <xs:sequence>
            <xs:element name="creditExposureBeforeRestriction" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úvěrová angažovanost klienta před restrikcemi.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditExposureAfterRestriction" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úvěrová angažovanost klienta po restrikcích.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amountAccordingToMLS" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Schválená částka dle MLS.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amountAccordingToRisk" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Schválená částka dle rizika.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskDataProductType" type="ProductType" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ produktu, pro který je risková informace platná.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="remainingDebts" type="RemainingDebt" minOccurs="0" maxOccurs="3">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zbývající dluh žadatele o úvěr.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RemainingDebt">
        <xs:sequence>
            <xs:element name="remainingDebt" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zbývající dluh žadatele o úvěr.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debtProductType" type="ProductType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ produktu.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BlazeStatus">
        <xs:annotation>
            <xs:documentation>Zásobník proměnných Blaze k danému resultu.
                (Také se používá Warningy z Blaze, ale nejedná se o klasické warningy, jde o to, že Blaze zaznamená v rámci schvalovacího procesu nějaké špatné
                plnění hodnot vektoru, na což tímto upozorní.)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Error or warning code from Blaze.
                                pro WF DOC_EVAL_COMPL - optional, plní BLAZE
                                pro WF NEW_GC, optional, plní BLAZE
                                pro WF NEW_PROD, optional, plní BLAZE
                                pro WF COMPL, optional, plní BLAZE
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Error or warning message from Blaze.
                                pro WF DOC_EVAL_COMPL - optional, plní BLAZE
                                pro WF NEW_GC, optional, plní BLAZE
                                pro WF NEW_PROD, optional, plní BLAZE
                                pro WF COMPL, optional, plní BLAZE
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:simpleType name="OperationType">
        <xs:annotation>
            <xs:documentation>Typ operace. Přidání odebrání warningů, hardchecků, atd.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADD">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Přidat/aktualizovat</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="REMOVE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Odebrat</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Alert">
        <xs:annotation>
            <xs:documentation>Zásobník proměnných Blaze k danému resultu.
                (Také se používá Warningy z Blaze, ale nejedná se o klasické warningy, jde o to, že Blaze zaznamená v rámci schvalovacího procesu nějaké
                špatné plnění hodnot vektoru, na což tímto upozorní.)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Komu v obálce je alert určen.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód alertu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="detail" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Text alertu pro pracovníka BO</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="businessProcess" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Businesový proces (žádost, kompletace)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="OperationType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze pro úvěry, doklad je požadován pokud požadovaná částka úvěru překročí hodnotu LoanBinFrom.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalSubject" type="ApprovalSubjectReference" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reference na approval subject</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ApprovalSubject" abstract="true">
        <xs:annotation>
            <xs:documentation>Předmět schvalování (zobecněná žádost/kompletace)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace předmětu schvalování</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Application">
        <xs:annotation>
            <xs:documentation>Žádost z AMS.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ApprovalSubject">
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Osoba připojená k žádosti.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="type" type="ApplicationType" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ schvalovaného předmětu, dle výčtového typu ApprovalSubjectType.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="LoanApplication">
        <xs:annotation>
            <xs:documentation>Společná entita pro (úvěrové) žádosti typu - CASHLOAN - CONSOLIDATION - MORTGAGE_REF - MORTGAGE.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="loanAmountSimulation" type="xs:decimal" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše uvěru požadovaná klientem</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše úvěru po parametrizaci klientem</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentCount" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Počet splátek po parametrizaci klientem</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše splátky po parametrizaci klientem</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="originLoanApplicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Identifikace původní AMS žádosti, ke které je tato žádost přijatou alternativou.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="splitPaymentIdRealizedTransaction" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>ID platby k rozložení</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="splitPaymentIdBankAccount" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>ID účtu, ze kterého byla realizována platba k rozložení</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CompletionSubject">
        <xs:annotation>
            <xs:documentation>Předmět kompletace jako schvalovaný předmět. Type je dán výčtovám typem CompletionSubjectType</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ApprovalSubject">
                <xs:sequence>
                    <xs:element name="relatedApplicationId" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Identifikace žádosti vzniklé v AMS, ke které se váže předmět kompletace.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="type" type="CompletionSubjectType" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ schvalovaného předmětu, dle výčtového typu CompletionSubjectType.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="EnvelopeSubject">
        <xs:annotation>
            <xs:documentation>Envelope jako předmět schvalování.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ApprovalSubject">
                <xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Offer" abstract="true">
        <xs:annotation>
            <xs:documentation>Nabídka napočítaná Blazem.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ApprovalSubject">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ConsolidationOffer">
        <xs:annotation>
            <xs:documentation>Nabídka napočítaná Blazem.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Offer">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CashLoanOffer">
        <xs:annotation>
            <xs:documentation>Nabídka napočítaná Blazem.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Offer">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="DecisionOutput">
        <xs:annotation>
            <xs:documentation>Pohled na score z boku, pohled na to co v daném okamžiku bylo zapsáno Blazem/operátorem/aktivitou.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractDecisionOutput">
                <xs:sequence>
                    <xs:element name="manualDecision" type="score:ManualDecision" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Poslední manuálně evidované rozhodnutí operátora.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="activity" type="GetActivity" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Aktivita v rámci které vznikl decisionOutput</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ActivityView">
        <xs:annotation>
            <xs:documentation>Decision output pro aktivitu</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="GetActivity">
                <xs:sequence>
                    <xs:element name="decisionOutput" type="ActivityDecisionOutput" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>decision output vynesený aktivitou</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ActivityDecisionOutput">
        <xs:annotation>
            <xs:documentation>Decision output pro aktivitu</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractDecisionOutput">
                <xs:sequence>
                    <xs:element name="manualDecision" type="score:ManualDecision" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>manuálně evidované rozhodnutí operátora.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requiredManualTasks" type="ManualTask" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Požadované manuální aktivity.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <!--TODO temporary - remove after new blaze for RDR integration-->
                    <xs:element name="strategy" type="Strategy" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Odkaz na stregii, nepovinné pro AMS</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ApplicationConsolidationData">
        <xs:annotation>
            <xs:documentation>Application consolidation data pro applicationId nebo envelopeId</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikace obálky.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>ApplicationId - číslo žádosti - identifikační číslo žádosti v AMS.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:element name="consolidationData" type="cd:ConsolidationData" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RiskGrade">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="generalContractId" type="xs:long" minOccurs="0"/>
            <xs:element name="riskGrade" type="xs:string"/>
            <xs:element name="processSource" type="ProcessSource"/>
            <xs:element name="effectiveDate" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="calculationTime" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="exposures" type="ExposuresToRiskGrade" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RiskGradeResults">
        <xs:complexContent>
            <xs:extension base="RiskGrade">
                <xs:sequence>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ExposuresToRiskGrade">
        <xs:sequence>
            <xs:element name="creditExposure" type="xs:decimal"/>
            <xs:element name="productType" type="ProductType"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SourceData">
        <xs:annotation>
            <xs:documentation>Informace o zdrojových údajích v procesu schvalování, které nelze získat jinou cestou, nebo zpětně v čase.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="realizedTransactions" type="RealizedTransactions" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Informace o realizovaných transakcích</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RealizedTransactions">
        <xs:annotation>
            <xs:documentation>Informace o realizovaných transakcích z OBS.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="transactionType" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ finanční transakce</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amountInAccountCurrency" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka v měně účtu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="requiredCurrency" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <xs:documentation>Kód požadované měny</xs:documentation>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="realizationDate" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum realizace transakce = čas, kdy se zaúčtovalo</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>



</xs:schema>
