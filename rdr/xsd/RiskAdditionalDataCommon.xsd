<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://airbank.cz/rdr/ws/riskadditionaldata"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           targetNamespace="http://airbank.cz/rdr/ws/riskadditionaldata" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:complexType name="RiskAdditionalData">
        <xs:annotation>
            <xs:documentation>Třída RiskAdditionalData obsahuje informace zadané underwriterem - Pomocná UW data v obálce.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cooperation" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Součinnost. Textová informace o tom co našel UW v BINFu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Id obálky z AMS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationData" type="ApplicationData" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Aplikační data - Katastr nemovitostí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeSources" type="IncomeSource" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj příjmů.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="obligations" type="Obligation" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Závazky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskData" type="RiskData" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Risková data.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="shadowData" type="ShadowData" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Stínová kopie aplikačních data.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ApplicationData">
        <xs:annotation>
            <xs:documentation>Třída ApplicationData obsahuje informace zadané underwriterem - aplikační data - Katastr nemovitostí.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="correspondenceAddressNote" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Poznámka ke korespondenční adrese - slouží k volnému popisu skutečností zjištěných v katastru nemovitostí (KN).
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="permanentAddressNote" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Poznámka k trvalé adrese - slouží k volnému popisu skutečností zjištěných v katastru nemovitostí (KN).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IncomeSource">
        <xs:annotation>
            <xs:documentation>Třída IncomeSource obsahuje informace zadané underwriterem - zdroj příjmu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="authorizedBusiness" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V ARES/ŽR od. Datum získání opravnění podnikat v ARES/ŽR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeValidated" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Příjem potvrzený. Částka příjmu v Kč, kterou zadal UW.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="vatPayer" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Plátce DPH. Pole vyplněné pouze pro podnikatele. Datum od kdy je subjekt plátcem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="taxOverpayment" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Daňový přeplatek (pro všechny příjmy). Částka v KČ (pouze pro podnikatele).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ příjmu. Bude nabývat hodnot Hlavní / Vedlejší.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isIncomeTaxPaid" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zaplacená daň.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Obligation">
        <xs:annotation>
            <xs:documentation>Třída Obligation obsahuje informace zadané underwriterem - závazky.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="combinationObligation" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kombinace závazků</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="actualBalance" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka k doplacení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka splátky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="mlsAb" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>MLS s AB.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Poznámka ke kombinaci. Popis kombinace závazků, aby ji UW dokázal odlišit od ostatních kombinací.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RiskData">
        <xs:annotation>
            <xs:documentation>Třída RiskData obsahuje informace zadané underwriterem - risková data.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="approvedInstallment" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Schválená splátka. Anuitní splátka spočítaná pro max. term dle dané produktové varianty. Vstup/výstup z produktové
                                kalkulačky. Pouze informační hodnota pro UW a pro historizaci, pro nic jiného se nepoužívá. Požadováno historizovat spolu s
                                daty žádosti.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvedInstallmentUw" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Schválená splátka - UW. Zobrazuje se pouze pro KONS. Splátka napočtená ručně UW mimo systém na základě Výše
                                úvěru-UW. Není vstupem / výstupem prod kalkulačky. Jedná se o částku, která např. zohleňuje upsell pro KONS.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výše úvěru - UW. Zobrazuje se pouze pro KONS. Hodnota zadaná UW, kde si eviduje výši UW včetně upsellu, který je
                                ochoton poskytnou. Není vstup / výstup prod kalkulačky (automatický nápočet), ale pouze ručně na základě vstupu od UW (př.
                                Nabídka, která zohledňuje upsell).
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="decisionUw" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozhodnutí UW. Zadává UW ručně výběrem z možností schváleno, storno, zamítnuto. Slouží pro potřeby propagace
                                rozhodnutí UW ohledně žádosti na snapshot. Následně však musí UW v AMG schválení, zamítnutí, storno provést.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="mls" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>MLS vč. Úvěrů alt HU. Pouze pro konsolidaci.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="mlsAb" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>MLS s AB půjčkou. Zobrazovat pro HU i pro KONS. V R8 sem UW bude vyplňovat hodnotu vypočítanou v Excel kalkulačce.
                                Je požadováno historizovat. Pozn: Stejné požadavky jako v předešlém poli. Vypočtené MLS navíc zohledňuje splátku odpovídající
                                výši úvěru, kterou by chtěl UW poskytnout.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ShadowData">
        <xs:annotation>
            <xs:documentation>Stínová kopie aplikačních dat.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="housingType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Druh bydlení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dependentPersonNum" type="xs:integer" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet vyživovaných osob.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="householdMemberType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ domácnosti.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiUnregisteredInstallments" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Splátky úvěrů nad rámec BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiUnregisteredPrincipal" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jistina nad rámec BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="brkiString" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Řetězec BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="householdIncomeAmount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Celkový čistý měsíční příjem ostatních členů domácnosti.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="medicalTransportationFoodExpenseAmount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měsíční výdaje domácnosti za doktory, léky, cestovné, výživné a jídlo.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="accommodationExpenseAmount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měsíční výdaje domácnosti za bydlení vč. energií (bez hypotéky).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentExpenseAmount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měsíční splátky úvěrů a hypoték žadatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
