<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/rdr/ws/proofRepository"
                  targetNamespace="http://airbank.cz/rdr/ws/proofRepository">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/rdr/ws/proofRepository">
            <xs:include schemaLocation="ProofRepository.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="CreateIdentityProofRequestMessage">
        <wsdl:part element="CreateIdentityProofRequest" name="CreateIdentityProof"/>
    </wsdl:message>
    <wsdl:message name="CreateIdentityProofResponseMessage">
        <wsdl:part element="IdentityProofResponse" name="CreateIdentityProofResponse"/>
    </wsdl:message>

    <wsdl:message name="GetIdentityProofByFilterRequestMessage">
        <wsdl:part element="GetIdentityProofByFilterRequest" name="GetIdentityProofByFilter"/>
    </wsdl:message>
    <wsdl:message name="GetIdentityProofByFilterResponseMessage">
        <wsdl:part element="IdentityProofResponse" name="IdentityProofResponse"/>
    </wsdl:message>

    <wsdl:message name="GetIdentityProofByIdRequestMessage">
        <wsdl:part element="GetIdentityProofByIdRequest" name="GetIdentityProofById"/>
    </wsdl:message>
    <wsdl:message name="GetIdentityProofByIdResponseMessage">
        <wsdl:part element="IdentityProofResponse" name="IdentityProofResponse"/>
    </wsdl:message>

    <wsdl:message name="UpdateIdentityProofRequestMessage">
        <wsdl:part element="UpdateIdentityProofRequest" name="UpdateIdentityProof"/>
    </wsdl:message>
    <wsdl:message name="UpdateIdentityProofResponseMessage">
        <wsdl:part element="UpdateIdentityProofResponse" name="UpdateIdentityProofResponse"/>
    </wsdl:message>

    <wsdl:message name="InvalidateIdentityProofRequestMessage">
        <wsdl:part element="InvalidateIdentityProofRequest" name="InvalidateIdentityProof"/>
    </wsdl:message>
    <wsdl:message name="InvalidateIdentityProofResponseMessage">
        <wsdl:part element="InvalidateProofResponse" name="InvalidateProofRespond"/>
    </wsdl:message>


    <wsdl:message name="CreateIncomeProofRequestMessage">
        <wsdl:part element="CreateIncomeProofRequest" name="CreateIncomeProof"/>
    </wsdl:message>
    <wsdl:message name="CreateIncomeProofResponseMessage">
        <wsdl:part element="IncomeProofResponse" name="CreateIncomeProofResponse"/>
    </wsdl:message>

    <wsdl:message name="GetIncomeProofByFilterRequestMessage">
        <wsdl:part element="GetIncomeProofByFilterRequest" name="GetIncomeProofByFilter"/>
    </wsdl:message>
    <wsdl:message name="GetIncomeProofByFilterResponseMessage">
        <wsdl:part element="IncomeProofResponse" name="IncomeProofResponse"/>
    </wsdl:message>

    <wsdl:message name="GetIncomeProofByIdRequestMessage">
        <wsdl:part element="GetIncomeProofByIdRequest" name="GetIncomeProofById"/>
    </wsdl:message>
    <wsdl:message name="GetIncomeProofByIdResponseMessage">
        <wsdl:part element="IncomeProofResponse" name="IncomeProofResponse"/>
    </wsdl:message>

    <wsdl:message name="InvalidateIncomeProofRequestMessage">
        <wsdl:part element="InvalidateIncomeProofRequest" name="InvalidateIncomeProof"/>
    </wsdl:message>
    <wsdl:message name="InvalidateIncomeProofResponseMessage">
        <wsdl:part element="InvalidateProofResponse" name="InvalidateProofRespond"/>
    </wsdl:message>

    <wsdl:message name="CreateNoDebtProofRequestMessage">
        <wsdl:part element="CreateNoDebtProofRequest" name="CreateNoDebtProof"/>
    </wsdl:message>
    <wsdl:message name="CreateNoDebtProofResponseMessage">
        <wsdl:part element="NoDebtProofResponse" name="CreateNoDebtProofResponse"/>
    </wsdl:message>

    <wsdl:message name="GetNoDebtProofByFilterRequestMessage">
        <wsdl:part element="GetNoDebtProofByFilterRequest" name="GetNoDebtProofByFilter"/>
    </wsdl:message>
    <wsdl:message name="GetNoDebtProofByFilterResponseMessage">
        <wsdl:part element="NoDebtProofResponse" name="NoDebtProofResponse"/>
    </wsdl:message>

    <wsdl:message name="GetNoDebtProofByIdRequestMessage">
        <wsdl:part element="GetNoDebtProofByIdRequest" name="GetNoDebtProofById"/>
    </wsdl:message>
    <wsdl:message name="GetNoDebtProofByIdResponseMessage">
        <wsdl:part element="NoDebtProofResponse" name="NoDebtProofResponse"/>
    </wsdl:message>

    <wsdl:message name="InvalidateNoDebtProofRequestMessage">
        <wsdl:part element="InvalidateNoDebtProofRequest" name="InvalidateNoDebtProof"/>
    </wsdl:message>
    <wsdl:message name="InvalidateNoDebtProofResponseMessage">
        <wsdl:part element="InvalidateProofResponse" name="InvalidateProofRespond"/>
    </wsdl:message>
    <wsdl:message name="CreateContactProofRequest">
        <wsdl:part element="CreateContactProofRequest" name="CreateContactProofRequest"/>
    </wsdl:message>
    <wsdl:message name="CreateContactProofResponse">
        <wsdl:part element="CreateContactProofResponse" name="CreateContactProofResponse"/>
    </wsdl:message>
    <wsdl:message name="GetContactProofByFilterRequest">
        <wsdl:part element="GetContactProofByFilterRequest" name="GetContactProofByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="GetContactProofByFilterResponse">
        <wsdl:part element="GetContactProofByFilterResponse" name="GetContactProofByFilterResponse"/>
    </wsdl:message>

    <wsdl:message name="CreatePersonalDataProofRequest">
        <wsdl:part element="CreatePersonalDataProofRequest" name="CreatePersonalDataProofRequest"/>
    </wsdl:message>
    <wsdl:message name="CreatePersonalDataProofResponse">
        <wsdl:part element="CreatePersonalDataProofResponse" name="CreatePersonalDataProofResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataProofByFilterRequest">
        <wsdl:part element="GetPersonalDataProofByFilterRequest" name="GetPersonalDataProofByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataProofByFilterResponse">
        <wsdl:part element="GetPersonalDataProofByFilterResponse" name="GetPersonalDataProofByFilterResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataProofByIdRequest">
        <wsdl:part element="GetPersonalDataProofByIdRequest" name="GetPersonalDataProofByIdRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataProofByIdResponse">
        <wsdl:part element="GetPersonalDataProofByIdResponse" name="GetPersonalDataProofByIdResponse"/>
    </wsdl:message>
    <wsdl:message name="InvalidatePersonalDataProofRequest">
        <wsdl:part element="InvalidatePersonalDataProofRequest" name="InvalidatePersonalDataProofRequest"/>
    </wsdl:message>
    <wsdl:message name="InvalidatePersonalDataProofResponse">
        <wsdl:part element="InvalidatePersonalDataProofResponse" name="InvalidatePersonalDataProofResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdatePersonalDataProofRequest">
        <wsdl:part element="UpdatePersonalDataProofRequest" name="UpdatePersonalDataProofRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdatePersonalDataProofResponse">
        <wsdl:part element="UpdatePersonalDataProofResponse" name="UpdatePersonalDataProofResponse"/>
    </wsdl:message>

    <wsdl:message name="CreateBusinessDataProofRequest">
        <wsdl:part element="CreateBusinessDataProofRequest" name="CreateBusinessDataProofRequest"/>
    </wsdl:message>
    <wsdl:message name="CreateBusinessDataProofResponse">
        <wsdl:part element="CreateBusinessDataProofResponse" name="CreateBusinessDataProofResponse"/>
    </wsdl:message>
    <wsdl:message name="GetBusinessDataProofByFilterRequest">
        <wsdl:part element="GetBusinessDataProofByFilterRequest" name="GetBusinessDataProofByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="GetBusinessDataProofByFilterResponse">
        <wsdl:part element="GetBusinessDataProofByFilterResponse" name="GetBusinessDataProofByFilterResponse"/>
    </wsdl:message>
    <wsdl:message name="GetBusinessDataProofByIdRequest">
        <wsdl:part element="GetBusinessDataProofByIdRequest" name="GetBusinessDataProofByIdRequest"/>
    </wsdl:message>
    <wsdl:message name="GetBusinessDataProofByIdResponse">
        <wsdl:part element="GetBusinessDataProofByIdResponse" name="GetBusinessDataProofByIdResponse"/>
    </wsdl:message>
    <wsdl:message name="InvalidateBusinessDataProofRequest">
        <wsdl:part element="InvalidateBusinessDataProofRequest" name="InvalidateBusinessDataProofRequest"/>
    </wsdl:message>
    <wsdl:message name="InvalidateBusinessDataProofResponse">
        <wsdl:part element="InvalidateBusinessDataProofResponse" name="InvalidateBusinessDataProofResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBusinessDataProofRequest">
        <wsdl:part element="UpdateBusinessDataProofRequest" name="UpdateBusinessDataProofRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBusinessDataProofResponse">
        <wsdl:part element="UpdateBusinessDataProofResponse" name="UpdateBusinessDataProofResponse"/>
    </wsdl:message>

    <wsdl:portType name="ProofRepository">
        <wsdl:operation name="createIdentityProof">
            <wsdl:documentation>
                Vytvoření identifikačního ověření.
            </wsdl:documentation>
            <wsdl:input message="CreateIdentityProofRequestMessage"/>
            <wsdl:output message="CreateIdentityProofResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="getIdentityProofByFilter">
            <wsdl:documentation>
                Vyhledání identifikačního ověření pomocí filtru.
            </wsdl:documentation>
            <wsdl:input message="GetIdentityProofByFilterRequestMessage"/>
            <wsdl:output message="GetIdentityProofByFilterResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="getIdentityProofById">
            <wsdl:documentation>
                Vyhledání identifikačního ověření pomocí id.
            </wsdl:documentation>
            <wsdl:input message="GetIdentityProofByIdRequestMessage"/>
            <wsdl:output message="GetIdentityProofByIdResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="updateIdentityProof">
            <wsdl:documentation>
                Aktualizace identifikačního ověření.
            </wsdl:documentation>
            <wsdl:input message="UpdateIdentityProofRequestMessage"/>
            <wsdl:output message="UpdateIdentityProofResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="InvalidateIdentityProof">
            <wsdl:documentation>
                Invalidace identifikačního ověření.
            </wsdl:documentation>
            <wsdl:input message="InvalidateIdentityProofRequestMessage"/>
            <wsdl:output message="InvalidateIdentityProofResponseMessage"/>
        </wsdl:operation>

        <wsdl:operation name="createIncomeProof">
            <wsdl:documentation>
                Vytvoření ověření příjmu.
            </wsdl:documentation>
            <wsdl:input message="CreateIncomeProofRequestMessage"/>
            <wsdl:output message="CreateIncomeProofResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="getIncomeProofByFilter">
            <wsdl:documentation>
                Vyhledání ověření příjmu pomocí filtru.
            </wsdl:documentation>
            <wsdl:input message="GetIncomeProofByFilterRequestMessage"/>
            <wsdl:output message="GetIncomeProofByFilterResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="getIncomeProofById">
            <wsdl:documentation>
                Vyhledání ověření příjmu pomocí id.
            </wsdl:documentation>
            <wsdl:input message="GetIncomeProofByIdRequestMessage"/>
            <wsdl:output message="GetIncomeProofByIdResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="InvalidateIncomeProof">
            <wsdl:documentation>
                Invalidace ověření příjmu.
            </wsdl:documentation>
            <wsdl:input message="InvalidateIncomeProofRequestMessage"/>
            <wsdl:output message="InvalidateIncomeProofResponseMessage"/>
        </wsdl:operation>


        <wsdl:operation name="createNoDebtProof">
            <wsdl:documentation>
                Vytvoření identifikačního ověření.
            </wsdl:documentation>
            <wsdl:input message="CreateNoDebtProofRequestMessage"/>
            <wsdl:output message="CreateNoDebtProofResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="getNoDebtProofByFilter">
            <wsdl:documentation>
                Vyhledání identifikačního ověření pomocí filtru.
            </wsdl:documentation>
            <wsdl:input message="GetNoDebtProofByFilterRequestMessage"/>
            <wsdl:output message="GetNoDebtProofByFilterResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="getNoDebtProofById">
            <wsdl:documentation>
                Vyhledání identifikačního ověření pomocí id.
            </wsdl:documentation>
            <wsdl:input message="GetNoDebtProofByIdRequestMessage"/>
            <wsdl:output message="GetNoDebtProofByIdResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="InvalidateNoDebtProof">
            <wsdl:documentation>
                Invalidace identifikačního ověření.
            </wsdl:documentation>
            <wsdl:input message="InvalidateNoDebtProofRequestMessage"/>
            <wsdl:output message="InvalidateNoDebtProofResponseMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CreatePersonalDataProof">
            <wsdl:documentation>
                Založení nového záznamu ověření osobních údajů.
            </wsdl:documentation>
            <wsdl:input message="CreatePersonalDataProofRequest"/>
            <wsdl:output message="CreatePersonalDataProofResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetPersonalDataProofByFilter">
            <wsdl:documentation>
                Poskytování záznamů ověření osobních údajů na základě parametrů filtru.
            </wsdl:documentation>
            <wsdl:input message="GetPersonalDataProofByFilterRequest"/>
            <wsdl:output message="GetPersonalDataProofByFilterResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetPersonalDataProofById">
            <wsdl:documentation>
                Metoda pro získání záznamu ověření osobních údajů podle id(s).
            </wsdl:documentation>
            <wsdl:input message="GetPersonalDataProofByIdRequest"/>
            <wsdl:output message="GetPersonalDataProofByIdResponse"/>
        </wsdl:operation>
        <wsdl:operation name="UpdatePersonalDataProof">
            <wsdl:documentation>
                Metoda pro aktualiyaci záznamů ověření osobních údajů.
            </wsdl:documentation>
            <wsdl:input message="UpdatePersonalDataProofRequest"/>
            <wsdl:output message="UpdatePersonalDataProofResponse"/>
        </wsdl:operation>
        <wsdl:operation name="InvalidatePersonalDataProof">
            <wsdl:documentation>
                Metoda pro zneplatnění záznamů ověření osobních údajů.
            </wsdl:documentation>
            <wsdl:input message="InvalidatePersonalDataProofRequest"/>
            <wsdl:output message="InvalidatePersonalDataProofResponse"/>
        </wsdl:operation>
        <wsdl:operation name="CreateContactProof">
            <wsdl:documentation>
                Založení nového záznamu kontaktních údajů.
            </wsdl:documentation>
            <wsdl:input message="CreateContactProofRequest"/>
            <wsdl:output message="CreateContactProofResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetContactProofByFilter">
            <wsdl:documentation>
                Vyhledání záznamu kontaktních údajů.
            </wsdl:documentation>
            <wsdl:input message="GetContactProofByFilterRequest"/>
            <wsdl:output message="GetContactProofByFilterResponse"/>
        </wsdl:operation>
        <wsdl:operation name="CreateBusinessDataProof">
            <wsdl:documentation>
                Založení nového záznamu ověření údajů o podnikání.
            </wsdl:documentation>
            <wsdl:input message="CreateBusinessDataProofRequest"/>
            <wsdl:output message="CreateBusinessDataProofResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetBusinessDataProofByFilter">
            <wsdl:documentation>
                Poskytování záznamů ověření údajů o podnikání na základě parametrů filtru.
            </wsdl:documentation>
            <wsdl:input message="GetBusinessDataProofByFilterRequest"/>
            <wsdl:output message="GetBusinessDataProofByFilterResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetBusinessDataProofById">
            <wsdl:documentation>
                Metoda pro získání záznamu ověření údajů o podnikání podle id(s).
            </wsdl:documentation>
            <wsdl:input message="GetBusinessDataProofByIdRequest"/>
            <wsdl:output message="GetBusinessDataProofByIdResponse"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateBusinessDataProof">
            <wsdl:documentation>
                Metoda pro aktualizaci záznamů ověření údajů o podnikání.
            </wsdl:documentation>
            <wsdl:input message="UpdateBusinessDataProofRequest"/>
            <wsdl:output message="UpdateBusinessDataProofResponse"/>
        </wsdl:operation>
        <wsdl:operation name="InvalidateBusinessDataProof">
            <wsdl:documentation>
                Metoda pro zneplatnění záznamů ověření údajů o podnikání.
            </wsdl:documentation>
            <wsdl:input message="InvalidateBusinessDataProofRequest"/>
            <wsdl:output message="InvalidateBusinessDataProofResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ProofRepositoryBinding" type="ProofRepository">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="createIdentityProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getIdentityProofByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getIdentityProofById">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="updateIdentityProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InvalidateIdentityProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>


        <wsdl:operation name="createIncomeProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getIncomeProofByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getIncomeProofById">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InvalidateIncomeProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="createNoDebtProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getNoDebtProofByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getNoDebtProofById">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InvalidateNoDebtProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CreateContactProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetContactProofByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CreatePersonalDataProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPersonalDataProofByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPersonalDataProofById">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdatePersonalDataProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InvalidatePersonalDataProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="CreateBusinessDataProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBusinessDataProofByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetBusinessDataProofById">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateBusinessDataProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InvalidateBusinessDataProof">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ProofService">
        <wsdl:documentation>Service providing operations related to Proofs.</wsdl:documentation>
        <wsdl:port name="ProofRepositoryPort" binding="ProofRepositoryBinding">
            <soap:address location="http://airbank.cz/rdr/ws/proof"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
