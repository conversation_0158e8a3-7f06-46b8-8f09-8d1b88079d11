<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns="http://airbank.cz/rdr/ws/cardauthorization"
           targetNamespace="http://airbank.cz/rdr/ws/cardauthorization" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:element name="SaveApprovalProcessDataRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardAuthorizationData" type="CardAuthorizationData"/>
                <xs:element name="scoring" type="Scoring" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SaveApprovalProcessDataResponse">
        <xs:complexType/>
    </xs:element>

    <xs:element name="GetCardAuthorizationAggregatesRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
                <xs:element name="filter" type="Filter" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCardAuthorizationAggregatesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="aggregates" type="CardAuthorizationAggregates" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Filter">
        <xs:sequence>
            <xs:element name="transactionDateFrom" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CardAuthorizationAggregates">
        <xs:sequence>
            <xs:element name="cardAuthorizationData" type="CardAuthorizationData"/>
            <xs:element name="scoring" type="Scoring" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CardAuthorizationData">
        <xs:sequence>
            <xs:element name="idCard" type="xs:long"/>
            <xs:element name="idAccMove" type="xs:long"/>
            <xs:element name="merchantNameAndPlace" type="xs:string" minOccurs="0"/>
            <xs:element name="transactionDate" type="xs:dateTime"/>
            <xs:element name="amountInRequiredCurrency" type="xs:decimal"/>
            <xs:element name="requiredCurrency" type="xs:string"/>
            <xs:element name="requiredCurrencyToCZK" type="xs:decimal" minOccurs="0"/>
            <xs:element name="advice" type="xs:boolean"/>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="transactionType" type="xs:string" minOccurs="0"/>
            <xs:element name="activeCardDevice" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Scoring">
        <xs:sequence>
            <xs:element name="decisions" type="Decision" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="score" type="xs:decimal" minOccurs="0"/>
            <xs:element name="nextActivities" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="scoringFlags" type="ScoringFlag" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Decision">
        <xs:sequence>
            <xs:element name="result" type="Result"/>
            <xs:element name="reason" type="xs:string" minOccurs="0"/>
            <xs:element name="approvalSubject" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="Result">
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVE"/>
            <xs:enumeration value="REJECT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="NextActivity">
        <xs:sequence>
            <xs:element name="code" type="xs:string"/>
            <xs:element name="numericalOrder" type="xs:long"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ScoringFlag">
        <xs:sequence>
            <xs:element name="code" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
