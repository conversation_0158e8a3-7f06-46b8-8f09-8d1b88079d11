<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns:etbd="http://airbank.cz/rdr/ws/behaviordata/externaltransaction"
           xmlns="http://airbank.cz/rdr/ws/behaviordata"
           targetNamespace="http://airbank.cz/rdr/ws/behaviordata" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:import namespace="http://airbank.cz/rdr/ws/behaviordata/externaltransaction"
               schemaLocation="../xsd/behaviordata/ExternalTransactionsBehavioralData.xsd"/>

    <xs:element name="SaveExternalTransactionsBehavioralDataRequest">
        <xs:annotation>
            <xs:documentation>Požadavek pro uložení behaviorálních dat o transakcích na účtech v cizích bankách.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor klienta.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>ID obálky žádosti, v rámci které byly transakce vytěženy.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="accountsStatementId" type="xs:long">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor výpisu z účtu v PAPUCA.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="account" type="etbd:ExternalAccount" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Účty v cizích bankách.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SaveExternalTransactionsBehavioralDataResponse">
        <xs:annotation>
            <xs:documentation>Odpověď na požadavek na uložení behaviorálních dat o transakcích na účtech v cizích bankách.</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

    <xs:element name="FindAndGetLastExternalTransactionsBehavioralDataRequest">
        <xs:annotation>
            <xs:documentation>Požadavek pro načtení posledních behaviorálních dat o transakcích na účtech v cizích bankách.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor obálky.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="FindAndGetLastExternalTransactionsBehavioralDataResponse">
        <xs:annotation>
            <xs:documentation>Odpověď na požadavek na načtení posledních behaviorálních dat o transakcích na účtech v cizích bankách.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="transactionsBehavioralData" type="etbd:TransactionsBehavioralData" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Behaviorální data o transakcích.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="BehaviorDataRequest">
        <xs:annotation>
            <xs:documentation>Request pro získání behaviorálních dat o klientovi. Data napočtena přes DWH.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>CUID - identifikace klienta.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="filter" type="SelectFilter" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Filtrování obsahu a počtu výstupních dat.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="SelectFilterBase">
        <xs:sequence>
            <xs:element name="maxRecords" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet záznamů, které budou vráceny na výstup. Pokud není nastaven, defaultně vrací 1000 entit.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SelectFilter">
        <xs:annotation>
            <xs:documentation>Filtrování obsahu a počtu výstupních dat.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="SelectFilterBase">
                <xs:sequence>
                    <xs:element name="result" type="SelectedData" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jaká konkrétní behaviorální data má výstup obsahovat.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="addNotActiveRecords" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Zahrnout na výstup i neaktivní záznamy.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="SelectedData">
        <xs:annotation>
            <xs:documentation>Jaká konkrétní behaviorální data má výstup obsahovat.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="personBehaviorData" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data vztahující se k osobě.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productBehaviorData" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data vztahující se k produktu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="profitability" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data vztahující se k profitabilitě klienta.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amlRegularUpdatingKyc" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pravidelná aktualizace AML údajů - Poznej svého klienta</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amlDeclaration" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data "Aml prohlášení"</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="BehaviorDataResponse">
        <xs:annotation>
            <xs:documentation>Odpověď obsahující behaviorální data klienta.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="behaviorDataRecord" type="BehaviorDataRecord" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam nalezených záznamů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="behaviorDataRecordCount" type="xs:int" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Počet nalezených záznamů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="BehaviorDataRecord">
        <xs:annotation>
            <xs:documentation>Nalezený záznam.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="status" type="BehaviorDataStatus" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Status záznamu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="insTime" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Čas vložení, kdy byl záznam vytvořen.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifTime" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Čas změny, kdy byl záznam modifikován.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="choice" type="ModelGroup" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jeden druh informací o behaviorálních datech ze skupiny.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dataBatchId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>DWH batchId se kterým byl záznam vytvořen a přenesen do RDR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="BehaviorDataStatus">
        <xs:annotation>
            <xs:documentation>Status záznamu.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Aktivní</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_ACTIVE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Neaktivní</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ModelGroup">
        <xs:annotation>
            <xs:documentation>Jeden druh informací o behaviorálních datech ze skupiny.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="personBehaviorData" type="PersonBehaviorData" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data vztahující se k osobě.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="profitability" type="Profitability" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data vztahující se k profitabilitě klienta.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productBehaviorData" type="ProductBehaviorData" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Behaviorální data vztahující se k produktu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amlRegularUpdatingKyc" type="AmlRegularUpdatingKyc">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pravidelná aktualizace AML údajů - Poznej svého klienta</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amlDeclaration" type="AmlDeclaration">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Aml prohlášení klienta</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="PersonBehaviorData">
        <xs:annotation>
            <xs:documentation>Behaviorální data vztahující se k osobě.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="clientCrmBlFlag" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Information about client blacklisting in CRM database. Information is just flag (TRUE/FALSE) (optional).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="prematurePayoffCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>numbers of all client´s loans, which was paid before last installmentCount day (by extraordinary installmentCount).
                                (optional)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="consolidationCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>number of all client´s consolidation into our Bank. (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanNumCancelations" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>number of all client´s canceled loan applications. (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpd" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximal days past due counted over of all client´s products (with no tolerance) (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpdInternal" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="maxDpdInternalTolerance" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="maxDpdLoan" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="maxDpdTolerance" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximal days past due counted over of all client´s products (with tolerance) (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpdToleranceLoan" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cntAppsLast1yHuCons" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cntExposureIncreaseLast1y" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                počet navýšení úvěrové angažovanosti za poslední rok
                                Pokud klient neměl úvěr, bude null
                                Pokud klient měl úvěr, ale nezvyšoval úvěrovou angažovanosti, bude 0
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cntLoansUtilHuCons" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cntLoansUtilLast1yHuCons" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cntRejectedLast1yHuCons" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sumDistraintMax5Y" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Maximální suma blokací za posledních 5 let</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateDistraintLastTol" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum kdy naposledy byl součet aktivních blokací z důvodu exekuce (DISTRAINT_3P) roven nebo větší než 1000 CZK
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateCreditLast1Dpd" type="xs:date" minOccurs="0" maxOccurs="1"/>
            <xs:element name="dateCreditLast5Dpd" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                datum, kdy naposled měl klient alespoň 5 DPD na nějakém úvěru HÚ/CONS (bez hypoték) za jeho kompletní historii.
                                Pokud klient neměl úvěr, bude null
                                Pokud klient ještě neměl splátku, bude null
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateAppLastRejectHuCons" type="xs:date" minOccurs="0" maxOccurs="1"/>
            <xs:element name="dateLoanUtilFirstHuCons" type="xs:date" minOccurs="0" maxOccurs="1"/>
            <xs:element name="dateLoanUtilLastHuCons" type="xs:date" minOccurs="0" maxOccurs="1"/>
            <xs:element name="flagRiskfulTrans" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="riskBehavioralScore" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionScore" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="dateLastOverdraftSuspension" type="xs:date" minOccurs="0"/>
            <xs:element name="overdraftSuspensionReasons6M" type="xs:string" minOccurs="0"/>
            <xs:element name="minAccountsBalanceLast1M" type="xs:decimal" minOccurs="0"/>
            <xs:element name="riskBehavioralGroup" type="xs:string" minOccurs="0"/>
            <xs:element name="riskBehavioralGroupDaily" type="xs:string" minOccurs="0"/>
            <xs:element name="amtCreditTrnSum" type="xs:decimal" minOccurs="0"/>
            <xs:element name="zonkyBehavioralScore" type="xs:decimal" minOccurs="0"/>
            <xs:element name="zonkyBehavioralDetails" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Profitability">
        <xs:annotation>
            <xs:documentation>Behaviorální data vztahující se k profitabilitě klienta.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="currentProfitability" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>client´s actual profitability in the bank. (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="futureProfitability" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>prediction of client´s future profitability in the bank (based on his behavior). (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="historicalProfitability" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>client´s historical profitability in the bank. (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ProductBehaviorData">
        <xs:annotation>
            <xs:documentation>Behaviorální data vztahující se k produktu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="accountType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CURRENT, LOAN, SAVING</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>account number (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpdInternalTolerance" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximal days past due for client´s account. Counted with internal tolerance (defined by collection) (optional)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpd" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximal days past due for client´s account. Counted with no tolerance (defined by collection) (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpdInternal" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximal days past due for client´s account. Counted with as internal (defined by collection) (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxDpdTolerance" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximal days past due for client´s account. Counted with tolerance (defined by collection) (optional)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AmlRegularUpdatingKyc">
        <xs:annotation>
            <xs:documentation>Pravidelná aktualizace AML údajů - Poznej svého klienta</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="initiated" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zahájení procesu "Pravidlená aktualizace AML údajů" (zahájení lhůty, kdy se měl klient projevit). Po následujících 12
                                měsíců (s přesností na měsíce) nelze založit stejnému klientovi jiný záznam.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="clientFulfilled" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Klient požadovanou činnost od zahájení procesu projevil (můž být i před uplynutím lhůty clientPeriodExpired).
                                NULL = klient požadovanou činnost od zahájení procesu neprojevil.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="clientPeriodExpired" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Proběhnutí lhůty, kdy klient měl nějakou činnost projevit a neprojevil. NULL = lhůta ještě neproběhla nebo proces
                                skončil splněním klienta.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="unsuccessfulTerminated" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>"Poznej svého klienta skončilo neúspěchem". NULL = dosud neukončeno, nebo klient splnil.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="GetFlexibleBehavioralDataRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
                <xs:element name="filter" type="FlexibleBehavioralSelectFilter"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFlexibleBehavioralDataResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="behavioralDataRecords" type="FlexibleBehavioralDataRecord" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="behavioralDataCount" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="FlexibleBehavioralSelectFilter">
        <xs:sequence>
            <xs:element name="riskArea" type="RiskAreaCode"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FlexibleBehavioralDataRecord">
        <xs:sequence>
            <xs:element name="attributeName" type="xs:string"/>
            <xs:element name="riskArea" type="RiskAreaCode"/>
            <xs:element name="attributeStringValue" type="xs:string" minOccurs="0"/>
            <xs:element name="attributeNumberValue" type="xs:decimal" minOccurs="0"/>
            <xs:element name="attributeBooleanValue" type="xs:boolean" minOccurs="0"/>
            <xs:element name="attributeDateValue" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AmlDeclaration">
        <xs:sequence>
            <xs:element name="enforced" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Platné datum vynucení aml prohlášení vypočtené podle chování kleinta</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="RiskAreaCode">
        <xs:annotation>
            <xs:documentation>Riskové oblasti.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="AML_SCORING">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>oblast AML behaviorálních dat</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FRAUD_SCORING">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>oblast ANTIPHISHING behaviorálních dat</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LOAN_SCORING">
            <xs:annotation>
                <xs:appinfo>
                    <jxb:property>
                        <jxb:javadoc>oblast Úvěrových behaviorálních dat</jxb:javadoc>
                    </jxb:property>
                </xs:appinfo>
            </xs:annotation>
        </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
