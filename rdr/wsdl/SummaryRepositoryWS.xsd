<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:rad="http://airbank.cz/rdr/ws/riskadditionaldata"
           xmlns:cd="http://airbank.cz/rdr/ws/consolidationdata"
           xmlns:ap="http://airbank.cz/rdr/ws/approvalprocess"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns="http://airbank.cz/rdr/ws/summaryservice"
           targetNamespace="http://airbank.cz/rdr/ws/summaryservice" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:import namespace="http://airbank.cz/rdr/ws/riskadditionaldata" schemaLocation="../xsd/RiskAdditionalDataCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/rdr/ws/consolidationdata" schemaLocation="../xsd/ConsolidationData.xsd"/>
    <xs:import namespace="http://airbank.cz/rdr/ws/approvalprocess" schemaLocation="../xsd/ApprovalProcessData.xsd"/>

    <xs:element name="SaveManualActivityResultRequest">
        <xs:annotation>
            <xs:documentation>Request pro uložení manuálního score.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelope" type="Envelope" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Obálka z AMS.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="manualActivity" type="ap:ManualActivity" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Manuální aktivita.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="consolidationData" type="cd:ConsolidationData" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Behaviorální data pro schvalovací proces.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="riskAdditionalData" type="rad:RiskAdditionalData" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Dodatečná risková data - využivá se pro sumarizaci v AMG.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="previousActivityId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikátor předchozí aktivity z AactivityBusinessProcessu APPLICATION ze které změny v tomto requestu
                                    vycházejí.
                                </jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SaveManualActivityResultResponse">
        <xs:annotation>
            <xs:documentation>Response o úspěšném uložení manuálního score.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="id" type="xs:long" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Technický identifikátor vytvořeného Score v RDR.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDecisionOutputsRequest">
        <xs:annotation>
            <xs:documentation>Request pro získání uložených elementů Score pro omezující kritérium.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>EnvelopeId - omezující kriterium pro vrácený seznam scoringů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDecisionOutputsResponse">
        <xs:annotation>
            <xs:documentation>Response se získanými elementy Score.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="decisionOutputs" type="ap:GetActivity" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam nalezených scoringů (data o aktivit2).</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDecisionOutputRequest">
        <xs:annotation>
            <xs:documentation>Request pro získání uloženého element Score pro omezující kritérium.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Technický identifkátor požadovaného Score.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDecisionOutputResponse">
        <xs:annotation>
            <xs:documentation>Response se získaným elementem Score.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approvalSubjects" type="ap:ApprovalSubject" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Předmět/y schvalovacího procesu</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="decisionOutput" type="ap:DecisionOutput" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam nalezených scoringů (zjednodušená data).</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetLastSummaryRequest">
        <xs:annotation>
            <xs:documentation>Request pro získání posledního Score pro omezující kritérium.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Omezující kriterium pro posledni scoring.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetLastSummaryResponse">
        <xs:annotation>
            <xs:documentation>Poslední score obsahující Blaze score a zároveň rozhodnutí operátora.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approvalSubjects" type="ap:ApprovalSubject" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Předmět/y schvalovacího procesu</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationSummary" type="ap:ApplicationSummary" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Poslední evidované score pro žádost.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="lastCompletionSummary" type="ap:CompletionSummary" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Poslední evidované score pro kompletaci.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="riskAdditionalData" type="rad:RiskAdditionalData" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Dodatečná risková data - využivá se pro sumarizaci v AMG.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Envelope">
        <xs:annotation>
            <xs:documentation>Obálka z AMS, obsahující nejnutnější minimum dat pro své uložení.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>EnvelopeId ke které se požaduje změna score.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="ap:EnvelopeType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ obálky v AMS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="created" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum vytvoření obálky v AMS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="GetLastAdHocSummaryRequest">
        <xs:annotation>
            <xs:documentation>Request pro získání posledního adHoc Score pro omezující kritérium.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Omezující kriterium pro posledni scoring.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetLastAdHocSummaryResponse">
        <xs:annotation>
            <xs:documentation>Poslední score obsahující Blaze score a zároveň rozhodnutí operátora.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approvalSubjects" type="ap:ApprovalSubject" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Předmět/y schvalovacího procesu</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="lastAdHocSummary" type="ap:AdHocSummary" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Poslední evidované score pro žádost.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SaveClientsRiskGradeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="riskGrade" type="ap:RiskGrade"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SaveClientsRiskGradeResponse">
        <xs:complexType/>
    </xs:element>

    <xs:element name="GetCurrentRiskGradeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentRiskGradeResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="riskGrade" type="ap:RiskGradeResults" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetScoringRiskGradeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Omezující kriterium pro posledni scoring.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetScoringRiskGradeResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="riskGrade" type="ap:RiskGradeResults" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>
