<?xml version="1.0" encoding="UTF-8"?>

<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/rdr/ws/devicepairing"
                  targetNamespace="http://airbank.cz/rdr/ws/devicepairing">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/rdr/ws/devicepairing">
            <xs:include schemaLocation="DevicePairingWS.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="AddApprovalProcessDataRequest">
        <wsdl:part element="AddApprovalProcessDataRequest" name="AddApprovalProcessDataRequest"/>
    </wsdl:message>
    <wsdl:message name="AddApprovalProcessDataResponse">
        <wsdl:part element="AddApprovalProcessDataResponse" name="AddApprovalProcessDataResponse"/>
    </wsdl:message>
    <wsdl:message name="AddApprovalProcessDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="AddApprovalProcessDataFault"/>
    </wsdl:message>

    <wsdl:message name="GetAggregatesDataRequest">
        <wsdl:part element="GetAggregatesDataRequest" name="GetAggregatesDataRequest"/>
    </wsdl:message>
    <wsdl:message name="GetAggregatesDataResponse">
        <wsdl:part element="GetAggregatesDataResponse" name="GetAggregatesDataResponse"/>
    </wsdl:message>
    <wsdl:message name="GetAggregatesDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetAggregatesDataFault"/>
    </wsdl:message>

    <wsdl:portType name="DevicePairing">
        <wsdl:operation name="addApprovalProcessData">
            <wsdl:documentation>Rozhraní webové operace  pro uložení výstupu Device Pairing scoringu</wsdl:documentation>
            <wsdl:input message="AddApprovalProcessDataRequest"/>
            <wsdl:output message="AddApprovalProcessDataResponse"/>
            <wsdl:fault name="AddApprovalProcessDataFault" message="AddApprovalProcessDataFault"/>
        </wsdl:operation>
        <wsdl:operation name="getAggregatesData">
            <wsdl:documentation>Rozhraní webové operace pro získání vybraných agregovaných dat z RDR, obecně za všechny pokusy při párování zařízení</wsdl:documentation>
            <wsdl:input message="GetAggregatesDataRequest"/>
            <wsdl:output message="GetAggregatesDataResponse"/>
            <wsdl:fault name="GetAggregatesDataFault" message="GetAggregatesDataFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="DevicePairingBinding" type="DevicePairing">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="addApprovalProcessData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="AddApprovalProcessDataFault">
                <soap:fault name="AddApprovalProcessDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAggregatesData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetAggregatesDataFault">
                <soap:fault name="GetAggregatesDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="DevicePairingWS">
        <wsdl:port binding="DevicePairingBinding" name="DevicePairingPort">
            <soap:address location="https://airbank.cz/rdr/ws/devicepairing"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>