<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           elementFormDefault="qualified"
           jxb:version="3.0"
           xmlns="http://airbank.cz/rdr/ws/proofRepository"
           targetNamespace="http://airbank.cz/rdr/ws/proofRepository">

    <xs:annotation>
        <xs:documentation>ProofRepository XSD</xs:documentation>
    </xs:annotation>

    <xs:element name="CreateIdentityProofRequest">
        <xs:annotation>
            <xs:documentation>Založit nový záznam ověření identifikačního dokladu.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="IdentityProofTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Konkrétní ověření identifikačního dokladu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="IdentityProofResponse">
        <xs:annotation>
            <xs:documentation>Seznam ověření identifikace.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="IdentityProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Konkrétní ověření identifikačního dokladu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ProofTO" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstraktní předek konkrétního ověření.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Unikátní identifikátor ověření. Vyplňuje RDR v odpovědích.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor klienta (CIF).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V rámci jaké žádosti/obálky byl záznam vytvořen (envelope ID z AMS). Vyplněno, pokud proof vznikne v rámci žádosti.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="issueDate" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum vydání dokladu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isValid" type="xs:boolean" minOccurs="1" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Platnost ověření dokladu. &lt;br/&gt;
                                {@code True} = platné ověření &lt;br/&gt;
                                {@code False} = neplatné ověření
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj ověření. Číselník z MDM. Možné zdroje ověření jsou: Doklad/Manuální verifikace/Pobočka.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V případě potvrzení dokladem určuje typ dokladu. Např. v případě identifikačního dokladu se rozlišuje OP nebo PAS.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="createdBy" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID operátora / systém který proof vytvořil</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="updatedBy" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID operátora / systém který proof modifikoval. Vyplňuje RDR v odpovědích.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="createdDate" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas vytvoření záznamu. Vyplňuje RDR v odpovědích.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="updatedDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas modifikování záznamu. Vyplňuje RDR v odpovědích.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IdentityProofTO">
        <xs:annotation>
            <xs:documentation>Ověření identifikačního dokladu.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofTO">
                <xs:sequence>
                    <xs:element name="number" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Číslo identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Země původu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maritalStatus" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Rodinný stav</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validTo" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Konec platnosti dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="invalidationDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum zneplatnění dokladu na základě údajů z MVČR.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="primaryIdDocumentId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Id primárního identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="primaryIdDocumentType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ primárního identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="primaryIdDocumentNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Číslo primárního identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="secondaryIdDocumentId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Id sekundárního identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="secondaryIdDocumentType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ sekundárního identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="secondaryIdDocumentNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Číslo sekundárního identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountOwnershipDocumentId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Id dokladu prohlášení o účtu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountOwnershipDocumentType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ dokladu prohlášení o účtu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="identificationPayment" type="IdentificationPaymentTO" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Identifikační platba.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="externalIdentityVerification" type="ExternalIdentityVerificationTO" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Informace o poskytnutí identity osoby třetí stranou (např. přes BankID z cizí banky).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <xs:complexType name="IdentificationPaymentTO">
        <xs:annotation>
            <xs:documentation>Identifikační platba.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Id identifikační platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debtorAccountName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název účtu plátce z identifikační platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="messageForReceiver" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zpráva pro příjemnce z identifikační platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="CreateContactProofResponse">
        <xs:annotation>
            <xs:documentation>Seznam ověření kontaktních údajů.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="ContactProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Response pro ověření kontaktních údajů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetContactProofByFilterResponse">
        <xs:annotation>
            <xs:documentation>Konkrétní ověření kontaktu klienta.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="ContactProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Response pro ověření kontaktních údajů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateContactProofRequest">
        <xs:annotation>
            <xs:documentation>Založit nový záznam ověření ident. dokladu.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="ContactProofTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Request pro ověření kontaktních údajů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetContactProofByFilterRequest">
        <xs:annotation>
            <xs:documentation>Založit nový záznam ověření ident. dokladu.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="ContactProofFilterTO">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Filtr pro ověření kontaktních údajů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ContactProofTO">
        <xs:annotation>
            <xs:documentation>Reprezentuje specifické atributy pro ověření kontaktních údajů klienta.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofTO">
                <xs:sequence>
                    <xs:element name="contactType" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ kontaktu (kontatního údaje).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contactValue" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Vlastní kontaktní údaj.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ContactProofFilterTO">
        <xs:annotation>
            <xs:documentation>Parametry filtru pro vyhledání ověření kontaktu klienta..</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofFilterTO">
                <xs:sequence>
                    <xs:element name="contact" type="ContactProofTO">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kontaktní údaj (typ kontaktu + vlastní kontakt).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validFrom" type="xs:dateTime" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Počáteční datum a čas, od kterého se má kontakt hledat.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ExternalIdentityVerificationTO">
        <xs:annotation>
            <xs:documentation>Informace o poskytnutí identity osoby třetí stranou (např. přes BankID z cizí banky).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="verifierCompRegNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČO organizace, která provedla ztotožnění.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="providerBankCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód banky, která poskytla identitu (může ji mít převzatou z jiné organizace).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeeId" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID zaměstnance, který provedl fyzickou identifikaci klienta.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="branchId" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID pobočky, na které byla provedena fyzická identifikace klienta.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ProofUpdateTO" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstraktní předek konkrétního ověření k aktualizaci.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Unikátní identifikátor ověření.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor klienta (CIF).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V rámci jaké žádosti/obálky byl záznam vytvořen (envelope ID z AMS).
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="issueDate" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum vydání dokladu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isValid" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Platnost ověření dokladu. &lt;br/&gt;
                                {@code True} = platné ověření &lt;br/&gt;
                                {@code False} = neplatné ověření
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj ověření. Číselník z MDM. Možné zdroje ověření jsou: Doklad/Manuální verifikace/Pobočka.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V případě potvrzení dokladem určuje typ dokladu. Např. v případě identifikačního dokladu se rozlišuje OP nebo PAS.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IdentityProofUpdateTO">
        <xs:annotation>
            <xs:documentation>Ověření identifikačního dokladu k aktualizaci.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofUpdateTO">
                <xs:sequence>
                    <xs:element name="number" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Číslo identifikačního dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Země původu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maritalStatus" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Rodinný stav</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validTo" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Konec platnosti dokladu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetIdentityProofByFilterRequest">
        <xs:annotation>
            <xs:documentation>Poskytování záznamů ověření identifikace na základě parametrů filtru.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="IdentityProofFilterTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Filter pro vyhledání konkrétních ověření indentifikačních ověření.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ProofFilterTO">
        <xs:annotation>
            <xs:documentation>Parametry filtru pro vyhledání identifikačního dokladu.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="source" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj ověření. Číselník z MDM. Možné zdroje ověření jsou: Doklad/Manuální verifikace/Pobočka.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="onlyValid" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pouze validní záznamy ověření. &lt;br/&gt;
                                {@code True} = vrací pouze validní ověření - defaultní chování &lt;br/&gt;
                                {@code False} = vrací validní i nevalidní ověření
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IdentityProofFilterTO">
        <xs:annotation>
            <xs:documentation>Parametry filtru pro vyhledání ověření identifikačního dokladu.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofFilterTO">
                <xs:sequence>
                    <xs:element name="cuidOrNumber" type="CuidOrNumber">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor klienta (CIF).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>V případě potvrzení dokladem určuje typ dokladu. Např. v případě identifikačního dokladu se rozlišuje OP nebo
                                        PAS.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetIdentityProofByIdRequest">
        <xs:annotation>
            <xs:documentation>Poskytování záznamů ověření identifikace na záklaďě id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="ids" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam id pro vyhledání konkrétních indentifikačních ověření.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateIdentityProofRequest">
        <xs:annotation>
            <xs:documentation>Aktualizace záznamů ověření identifikačních dokumentů podle id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="updatedBy" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>ID operátora / systém který proof modifikoval</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="updatedDate" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Datum a čas modifikování záznamu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="proof" type="IdentityProofUpdateTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Objekt s parametry pro aktualizaci záznamů ověření identifikačních dokumentů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateIdentityProofResponse">
        <xs:annotation>
            <xs:documentation>Prázdná odpověď pro aktualizaci záznamů ověření na základě id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence></xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateIdentityProofRequest">
        <xs:annotation>
            <xs:documentation>Invalidace záznamů ověření identifikačních dokumentů podle id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="params" type="InvalidateProofTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Objekt s parametry pro invalidaci záznamů ověření identifikačních dokumentů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateProofResponse">
        <xs:annotation>
            <xs:documentation>Prázdná odpověď pro invalidaci záznamů ověření na záklaďě id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence></xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateIncomeProofRequest">
        <xs:annotation>
            <xs:documentation>Založit nový záznam ověření identifikace.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="IncomeProofTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Konkrétní ověření příjmu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="IncomeProofResponse">
        <xs:annotation>
            <xs:documentation>Seznam ověření příjmu.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="IncomeProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Konkrétní ověření příjmu s id.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetIncomeProofByFilterRequest">
        <xs:annotation>
            <xs:documentation>Poskytování záznamů ověření identifikace na základě parametrů filtru.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="IncomeProofFilterTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Filter pro vyhledání konkrétních ověření příjmu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="IncomeProofFilterTO">
        <xs:annotation>
            <xs:documentation>Parametry filtru pro vyhledání ověření příjmu.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofFilterTO">
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor klienta (CIF).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kritérium pro vyhledání podle typu příjmu. Hlavní/vedlejší. Subset MDM(DOCUMENT_GROUP_RELATION) - MAIN_INCOME,
                                        OTHER_INCOME
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="createdTo" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kritérium pro vyhledání potvrzení přímů vytvořených před zadaným datem včetně.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="createdFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kritérium pro vyhledání potvrzení příjmů vytvořených po zadaném datu včetně.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetIncomeProofByIdRequest">
        <xs:annotation>
            <xs:documentation>Poskytování záznamů potvrzení příjmu na záklaďě id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="ids" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam id pro vyhledání konkrétních ověření příjmu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateIncomeProofRequest">
        <xs:annotation>
            <xs:documentation>Invalidace záznamů ověření příjmu podle id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="params" type="InvalidateProofTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Objekt s parametry pro invalidaci záznamů ověření příjmu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="IncomeProofTO">
        <xs:annotation>
            <xs:documentation>Ověření příjmů ze zaměstnání, z podnikání a ostatních.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofTO">
                <xs:sequence>
                    <xs:element name="economicalStatus" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Druh pracovního poměru. Číselník EconomicalStatusType. Plněno ze žádosti - Zaměstnanec / OSVČ / Student /.... .
                                        Hodnota je číslo od 0 do 14.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeType" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ příjmu. Hlavní/vedlejší.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="netIncome" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše příjmu. Plněno ze žádosti v případě že je ověřen příjem. Může dojít pouze k ověření zaměstnavatele.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="taxBaseIncome" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Dílčí základ daně.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="last12MAvgNetIncome" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Průměrný čistý měsíční příjem za posledních 12 měsíců.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="startDate" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Zaměstnaný od.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="last1MGamblingAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše částky transakcí kategorizovaných jako hazard za poslední měsíc.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="last3MGamblingAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Výše částky transakcí kategorizovaných jako hazard za poslední 3 měsíce.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastUnemploymentBenefitDate" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum poslední dávky v nezaměstnanosti.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employment" type="EmploymentTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Objekt Employment, který eviduje data navázaná na příjmy ze závislé činnosti.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selfEmployed" type="SelfEmployedTO" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Objekt SelfEmployed, který eviduje data navázaná na příjmy z podnikatelské činnosti.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="EmploymentTO">
        <xs:annotation>
            <xs:documentation>Objekt Employment, který eviduje data navázaná na příjmy ze závislé činnosti.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="employer" type="EconomicalSubjectTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zaměstnavatel.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="verification" type="EmploymentVerificationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Objekt verifikací.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employmentCheck" type="EmploymentCheckTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Objekt kontrol zaměstnání.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SelfEmployedTO">
        <xs:annotation>
            <xs:documentation>Objekt SelfEmployed, který eviduje data navázaná na příjmy z podnikatelské činnosti.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="selfEmployer" type="EconomicalSubjectTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Detail podnikatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="verification" type="SelfEmployedVerificationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek prověrky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SelfEmployedVerificationTO">
        <xs:annotation>
            <xs:documentation>Objekt SelfEmployed, který eviduje data navázaná na příjmy z podnikatelské činnosti.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="employerVerification" type="EmployerVerificationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prověrka pro podnikatele, OSVČ, svobodné podnikání.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EconomicalSubjectTO">
        <xs:annotation>
            <xs:documentation>Ekonomický subjekt.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Země původu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="identificationNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČO zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="taxNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Daňové identifikační číslo (DIČ) - musí být vyplněno IČ nebo DIČ.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EmploymentVerificationTO">
        <xs:annotation>
            <xs:documentation>Prověrka pro zaměstnavatele.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="employerVerification" type="EmployerVerificationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prověrka pro zaměstnavatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="verificationCall" type="VerificationCallTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prověrka pro zaměstnavatele ContactCall.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EmployerVerificationTO">
        <xs:annotation>
            <xs:documentation>Prověrka pro zaměstnavatele, OSVČ, svobodného podnikání.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="registerResult" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Výsledek prověrky zaměstnavatele, OSVČ, svobodného podnikání ověřující existenci subjektu.
                                MDM(VER_ARES_TASK_RESULT) - OK, NOT_FOUND, SUBJECT_DIFFERENCE, ...
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>

        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="VerificationCallTO">
        <xs:annotation>
            <xs:documentation>Evidence prověrky volání zaměstnavateli</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="callResult" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Výsledek verifikace zaměstnavatele.
                                MDM(VER_CONTACT_CALL_TASK_RESULT) - SUCCESSFUL_CALL, VERIFIED_INCOME_DOC, NOT_REACHED, ...
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EmploymentCheckTO">
        <xs:annotation>
            <xs:documentation>Evidence výsledků verifikací zjištěných v rámci prověrky volání zaměstnavateli.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="contractProlonged" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Vyjádření zaměstnavatele o prodloužení pracovní smlouvy na dobu určitou.
                                MDM(VER_CONTRACT_PROLONGED) - YES, NO, NOT_KNOW, ...
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="contractType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pracovní poměr. MDM(VER_CONTRACT_TYPE) - PERMANENT_CONTRACT, FIX_TERM_CONTRACT</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="empFixTermContractValidTo" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Platnost smlouvy na dobu určitou.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employer" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zaměstnavatel. MDM(VER_EMPLOYER) - AGREE, DISAGREE_OTHER, DISAGREE_PRIVATE, ...</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employment" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pracovní vztah. MDM(VER_EMPLOYMENT) - CONTRACT, NOT_WORK, AGREEMENT, OTHER</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpLongSick" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru - klient je dlouhodobě nemocný. &lt;br/&gt;
                                {@code True} = je dlouhodobě nemocný &lt;br/&gt;
                                {@code False} = není dlohodobě nemocný
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpMaternityLeave" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru - mateřská dovolená. &lt;br/&gt;
                                {@code True} = na mateřské dovolené &lt;br/&gt;
                                {@code False} = není na mateřské dovolené
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpNotProvide" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru. &lt;br/&gt;
                                {@code True} = informace neposkytnuta &lt;br/&gt;
                                {@code False} = informace poskytnuta
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpParentalLeave" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru - rodičovská dovolená &lt;br/&gt;
                                {@code True} = rodičovská dovolená &lt;br/&gt;
                                {@code False} = není rodičovská dovolená
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpPayrollExec" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru - srážky ze mzdy, exekuce &lt;br/&gt;
                                {@code True} = klient má srážky ze mzdy nebo exekuci&lt;br/&gt;
                                {@code False} = klient nemá srážky ze mzdy nebo exekuci
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpProbationPeriod" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru - klient je ve zkušení době &lt;br/&gt;
                                {@code True} = klient je ve zkušení době &lt;br/&gt;
                                {@code False} = klient není ve zkušení době
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isEmpTermination" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zjištění bližších informací o klientově pracovním poměru - klient ve výpovědní lhůtě &lt;br/&gt;
                                {@code True} = klient ve výpovědní lhůtě &lt;br/&gt;
                                {@code False} = klient není ve výpovědní lhůtě
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isVerifiedIdentificationNumber" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČO ověřeno?</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentMethod" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Způsob vyplácení mzdy. MDM(VER_PAYMENT_METHOD) - ON_ACCOUNT, IN_CASH</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rizikový případ. MDM(RISK_CASE) = INCOME_DIFFERENCE, INCORRECT_APPLICATION, FAMILY_MEMBER_CERTIFIED, ...</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="verificationLevel" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozsah ověření. MDM(VER_LEVEL) - STANDARD, IN_DEBT, CODE_005, ...</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="CreateNoDebtProofRequest">
        <xs:annotation>
            <xs:documentation>Založit nový záznam ověření bezdlužnosti.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="NoDebtProofTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Konkrétní ověření bezdlužnosti.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="NoDebtProofResponse">
        <xs:annotation>
            <xs:documentation>Seznam ověření bezdlužnosti.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="NoDebtProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Konkrétní ověření bezdlužnosti.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetNoDebtProofByFilterRequest">
        <xs:annotation>
            <xs:documentation>Poskytování záznamů ověření bezdlužnosti na základě parametrů filtru.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="NoDebtProofFilterTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Filter pro vyhledání konkrétních ověření bezdlužnosti.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="NoDebtProofFilterTO">
        <xs:annotation>
            <xs:documentation>Parametry filtru pro vyhledání ověření bezdlužnosti.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofFilterTO">
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor klienta (CIF).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="createdTo" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kritérium pro vyhledání ověření bezdlužnosti vytvořených před zadaným datem včetně.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="createdFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kritérium pro vyhledání ověření bezdlužnosti vytvořených po zadaném datu včetně.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetNoDebtProofByIdRequest">
        <xs:annotation>
            <xs:documentation>Poskytování záznamů potvrzení bezdlužnosti na záklaďě id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="ids" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam id pro vyhledání konkrétních ověření bezdlužnosti.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:complexType name="InvalidatedDocument">
        <xs:annotation>
            <xs:documentation>Komplexní typ obsahující IDs invalidovaných dokumentů a případně i datum invalidace získaný z externího zdroje (MVČR).
            </xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Seznam id pro invalidaci ověření.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidationDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum invalidace občanského průkazu získané z MVČR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="InvalidateNoDebtProofRequest">
        <xs:annotation>
            <xs:documentation>Invalidace záznamů ověření bezdlužnosti podle id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="params" type="InvalidateProofTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Objekt s parametry pro invalidaci záznamů ověření bezdlužnosti.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="InvalidateProofTO">
        <xs:annotation>
            <xs:documentation>Invalidace záznamů ověření podle id.</xs:documentation>
        </xs:annotation>

        <xs:sequence>
            <xs:element name="updatedBy" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID operátora / systém který proof modifikoval</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="updatedDate" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas modifikování záznamu. Vyplňuje RDR v odpovědích.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidatedDocument" type="InvalidatedDocument" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum invalidace občanského průkazu získané z MVČR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="NoDebtProofTO">
        <xs:annotation>
            <xs:documentation>Ověření dokumentu o bezdlužnosti.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="ProofTO">
                <xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="CreatePersonalDataProofRequest">
        <xs:annotation>
            <xs:documentation>
                Request operace pro založení záznamu ověření osobních údajů.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="PersonalDataProofTO" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreatePersonalDataProofResponse">
        <xs:annotation>
            <xs:documentation>
                Response operace pro založení záznamu ověření osobních údajů.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="PersonalDataProofTO" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="PersonalDataProofTO">
        <xs:complexContent>
            <xs:extension base="ProofTO">
                <xs:sequence>
                    <xs:element name="document" type="Document" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Atributy dokladu
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maritalStatus" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Rodinný stav</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="invalidationDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum zneplatnění dokladu na základě údajů z MVČR.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentTypeROB" type="PersonalDocumentType" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ pobytu dle ROB.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="PersonalDataProofUpdateTO">
        <xs:complexContent>
            <xs:extension base="ProofUpdateTO">
                <xs:sequence>
                    <xs:element name="document" type="Document" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Osobní doklad</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maritalStatus" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Rodinný stav</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="invalidationDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum zneplatnění dokladu na základě údajů z MVČR.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentTypeROB" type="PersonalDocumentType" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Typ dokladu dle ROB.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="PersonalDocumentType">
        <xs:annotation>
            <xs:documentation>Číselník definující typ dokladu cizince dle ROB
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ID_CARD">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ID_CARD_NOT_MACHINE_READABLE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nečitelné ID</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PASSPORT">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>PAS</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PASSPORT_NOT_MACHINE_READABLE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nečitelný pas</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="STAY_PERMIT">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Povolení k pobytu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="STAY_PERMIT_BOOKLET">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Povolení k pobytu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="STAY_PERMIT_FORM">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Formulář povolení k pobytu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="STAY_PERMIT_STICKER">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nálepka povolení k pobytu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VISA_STICKER">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Vízum (nálepka)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="number" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Číslo dokladu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="country" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Země původu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="isPrimary" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>
                        Jde o primární doklad?
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="validTo" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Konec platnosti dokladu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="GetPersonalDataProofByFilterRequest">
        <xs:annotation>
            <xs:documentation>
                Request operace na poskytnutí záznamů ověření osobních údajů na základě parametrů filtru.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="PersonalDataProofFilterTO">
                    <xs:annotation>
                        <xs:documentation>
                            Filtr pro vyhledání konkrétních ověření osobních údajů.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetPersonalDataProofByFilterResponse">
        <xs:annotation>
            <xs:documentation>
                Response operace poskytnutí ověření osobních údajů.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="PersonalDataProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Konkrétní ověření osobních údajů
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetPersonalDataProofByIdRequest">
        <xs:annotation>
            <xs:documentation>
                Metoda pro získání záznamu ověření osobních údajů podle id(s).
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="ids" type="xs:long" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetPersonalDataProofByIdResponse">
        <xs:annotation>
            <xs:documentation>
                Response operace na poskytnutí záznamů ověření osobních údajů na základě identifikátorů.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="PersonalDataProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Konkrétní ověření osobních údajů.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePersonalDataProofRequest">
        <xs:annotation>
            <xs:documentation>Aktualizace záznamů ověření osobních údajů podle id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="updatedBy" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>ID operátora / systém který proof modifikoval</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="updatedDate" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Datum a čas modifikování záznamu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="proof" type="PersonalDataProofUpdateTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Objekt s parametry pro aktualizaci záznamů ověření osobních údajů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePersonalDataProofResponse">
        <xs:annotation>
            <xs:documentation>Prázdná odpověď pro aktualizaci záznamů ověření osobních údajů na základě id.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence></xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidatePersonalDataProofRequest">
        <xs:annotation>
            <xs:documentation>
                Request operace na zneplatnění záznamů ověření osobních údajů.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="params" type="InvalidateProofTO">
                    <xs:annotation>
                        <xs:documentation>
                            Parametry zneplatnění ověření osobních údajů.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidatePersonalDataProofResponse">
        <xs:complexType/>
    </xs:element>

    <xs:complexType name="PersonalDataProofFilterTO">
        <xs:complexContent>
            <xs:extension base="ProofFilterTO">
                <xs:sequence>
                    <xs:element name="cuidOrNumber" type="CuidOrNumber">
                        <xs:annotation>
                            <xs:documentation>
                                Ověření vztahující se ke klientovi (CUID) nebo identifikačnímu dokladu (number).
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                V případě potvrzení dokladem určuje typ dokladu. Např. v případě identifikačního dokladu se rozlišuje OP nebo PAS
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="onlyPrimaryDocument" type="xs:boolean" minOccurs="0" default="false">
                        <xs:annotation>
                            <xs:documentation>
                                Pouze ověření primárním identifikačním dokladem. Pokud není parametr použit, vrací se všechna ověření.
                                True = vrací ověření primárními identifikačními doklady
                                False, nenaplněno = vrací všechna ověření - defaultní chování
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CuidOrNumber">
        <xs:choice>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Jednoznačný identifikátor klienta (CIF).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Číslo identifikačního dokladu.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:element name="CreateBusinessDataProofRequest">
        <xs:annotation>
            <xs:documentation>
                Request operace pro založení záznamu ověření údajů o podnikání.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="BusinessDataProofTO" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateBusinessDataProofResponse">
        <xs:annotation>
            <xs:documentation>
                Response operace pro založení záznamu ověření údajů o podnikání.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="BusinessDataProofTO" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="BusinessDataProofTO">
        <xs:complexContent>
            <xs:extension base="ProofTO">
                <xs:sequence>
                    <xs:element name="validTo" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum konce platnosti.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="registrationNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Evidenční číslo.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetBusinessDataProofByFilterRequest">
        <xs:annotation>
            <xs:documentation>
                Request operace na poskytnutí záznamů ověření údajů o podnikání na základě parametrů filtru.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="BusinessDataProofFilterTO">
                    <xs:annotation>
                        <xs:documentation>
                            Filtr pro vyhledání konkrétních ověření údajů o podnikání.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="BusinessDataProofFilterTO">
        <xs:complexContent>
            <xs:extension base="ProofFilterTO">
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>
                                Jednoznačný identifikátor klienta.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                V případě potvrzení dokladem určuje typ dokladu. Např. v případě identifikačního dokladu se rozlišuje OP nebo PAS
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetBusinessDataProofByFilterResponse">
        <xs:annotation>
            <xs:documentation>
                Response operace poskytnutí ověření údajů o podnikání.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="BusinessDataProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Konkrétní ověření údajů o podnikání
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetBusinessDataProofByIdRequest">
        <xs:annotation>
            <xs:documentation>
                Metoda pro získání záznamu ověření údajů o podnikání podle id(s).
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="ids" type="xs:long" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetBusinessDataProofByIdResponse">
        <xs:annotation>
            <xs:documentation>
                Response operace na poskytnutí záznamů ověření údajů o podnikání na základě identifikátorů.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="proofs" type="BusinessDataProofTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Konkrétní ověření údajů o podnikání.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateBusinessDataProofRequest">
        <xs:annotation>
            <xs:documentation>Aktualizace záznamů ověření údajů o podnikání podle id.</xs:documentation>
        </xs:annotation>

        <xs:complexType>
            <xs:sequence>
                <xs:element name="updatedBy" type="xs:string">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>ID operátora / systém který proof modifikoval</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="updatedDate" type="xs:dateTime">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Datum a čas modifikování záznamu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="proof" type="BusinessDataProofUpdateTO" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Objekt s parametry pro aktualizaci záznamů ověření údajů o podnikání.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="BusinessDataProofUpdateTO">
        <xs:complexContent>
            <xs:extension base="ProofUpdateTO">
                <xs:sequence>
                    <xs:element name="validTo" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Datum konce platnosti.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="registrationNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Evidenční číslo.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="UpdateBusinessDataProofResponse">
        <xs:annotation>
            <xs:documentation>Odpověď pro aktualizaci záznamů ověření údajů o podnikání na základě id.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateBusinessDataProofRequest">
        <xs:annotation>
            <xs:documentation>
                Request operace na zneplatnění záznamů ověření údajů o podnikání.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="params" type="InvalidateProofTO">
                    <xs:annotation>
                        <xs:documentation>
                            Parametry zneplatnění ověření údajů o podnikání.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateBusinessDataProofResponse">
        <xs:complexType/>
    </xs:element>

</xs:schema>