<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns="http://airbank.cz/rdr/ws/amlapproval"
           targetNamespace="http://airbank.cz/rdr/ws/amlapproval" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:element name="SavePaymentsApprovalDataRequest">
        <xs:annotation>
            <xs:documentation>Request webové operace pro uložení výstupu scoringu PAYMENT pro AML kontrolu plateb.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="payment" type="Payment" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SavePaymentsApprovalDataResponse">
        <xs:annotation>
            <xs:documentation>Response potvrzující přijetí Requestu webové operace pro uložení výstupu scoringu PAYMENT pro AML kontrolu plateb.</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

    <xs:complexType name="Payment">
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor předepsané transakce k posouzení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="valueEur" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota platby v EUR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="valueDate" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum, ke kterému je hodnota platby vztažena.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="originProcessEvent" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Původní událost platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="originProcessEventId" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Původní identifikace události platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="businessProcessEventRelatedCuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Cuid fyzické osoby související s událostí vzniku.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debitPaymentSide" type="Account">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debetní strana platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditPaymentSide" type="Account">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kreditní strana platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentAssessment" type="PaymentAssessment">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek hodnocení AML rizika platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Account">
        <xs:sequence>
            <xs:element name="amount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka peněz.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="currency" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měna (alpha 3 kód).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Id rámcové smlouvy BcDocument.IDDOCUMENT.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractAmlRiskCategory" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kategorie rizikovosti rámcové smlouvy RISKFUL nebo ACCEPTED.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractOwnerLegalSegment" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Právní segment vlastníka RS - konstanta podle MDM číselníku LegalSegment.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="checkedCuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Sledovaná osoba (vždy FON). Příjemce, nebo odesílatel peněz.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="customerAmlRiskData" type="CustomerAmlRiskData" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>AML rizikové údaje sledované osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="customerAmlRiskFactorCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Faktor zvýšeného rizika. Jednotlivé položky jsou z číselníku RiskFactor.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="clientAmlBehavioralData" type="ClientAmlBehavioralData" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>AML behaviorální údaje sledované osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListMatches" type="BwListMatch" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Údaje o shodách nalezených v BW listech.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CustomerAmlRiskData">
        <xs:sequence>
            <xs:element name="accountsBalanceEurSum" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Souhrnná částka účetních zůstatků na všech účtech sledované osoby (tedy retailové i podnikatelské účty) včetně čerpaného kontokorentu v EUR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum narození sledované osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskCategory" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kategorie rizikovosti osoby podle číselníku AmlRiskCategory.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskCategoryFrom" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum, od kdy daná kategorie rizikovosti platí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ClientAmlBehavioralData">
        <xs:sequence>
            <xs:element name="attributeName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název atributu pro AML behaviorál.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="attributeStringValue" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota datového typu String pro attributeName.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="attributeNumberValue" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota datového typu Long pro attributeName.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="attributeBooleanValue" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota datového typu Boolean pro attributeName.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="attributeDateValue" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota datového typu Date pro attributeName.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BwListMatch">
        <xs:sequence>
            <xs:element name="commonAttribute" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Testovaný atribut platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListKey" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obchodní klíč daného seznamu - konkrétní blacklist, kde došlo ke shodě.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListLimitation" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Omezení pro nějž je nějaký bw list specifikován - položky dány výčtem BwListLimitation.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ blacklistu (diskriminátor) z číselníku BWLTypeCode.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="country" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>alfa2Code země - business klíč blacklistu druhu COUNTRY.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="personName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Celé jméno - business klíč blacklistu druhu PERSON_NAME.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentAssessment">
        <xs:sequence>
            <xs:element name="decision" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek schvalování na jedné ze straně platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="nextActivities" type="NextActivities" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Předepsané navazující aktivity z hodnoceni AML skóringu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="strategyName" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jméno verze skórovací strategie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="strategyVersionDate" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum nasazení použité verze skórovací strategie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="strategyVersion" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace verze skórovací strategie.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debitPaymentSide" type="PaymentResult" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek hodnoceni AML skóringu debetní strany platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditPaymentSide" type="PaymentResult" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek hodnoceni AML skóringu kreditní strany platby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentResult">
        <xs:sequence>
            <xs:element name="customerAmlManualVerification" type="xs:boolean">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>True - požadováno manuální AML prověření osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="assessments" type="Assessment" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnocení AML skóringu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="NextActivities">
        <xs:sequence>
            <xs:element name="code" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Předepsaná navazující aktivita.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Assessment">
        <xs:sequence>
            <xs:element name="amlAlertCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obchodní klíč aml výstrahy (synonymum: aml příznak) na straně příjmu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="limitName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obchodní klíč aml limitu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="limitValue" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota aml limitu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListMatches" type="BwListMatchResult" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek shody s BWListem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BwListMatchResult">
        <xs:sequence>
            <xs:element name="commonAttribute" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Atribut, na němž byl nalezen konflikt.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Diskriminátor druhu bw listu podle výčtu BWL - BWLTypeCode.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="value" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota aml blacklistu ve shodě s conmmonAttributem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bwListKey" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Klíč konkrétního bwlistu podle výčtu BwListCode.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
