<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://airbank.cz/rdr/ws/riskadditionaldata"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           targetNamespace="http://airbank.cz/rdr/ws/riskadditionaldata" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:include schemaLocation="../xsd/RiskAdditionalDataCommon.xsd"/>

    <xs:element name="SaveRiskAdditionalDataRequest">
        <xs:annotation>
            <xs:documentation>Uložení pomocných riskových dat do RDR.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="riskAdditionalData" type="RiskAdditionalData" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Pomocná risková data k uložení do RDR.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SaveRiskAdditionalDataResponse">
        <xs:complexType/>
    </xs:element>

    <xs:element name="GetRiskAdditionalDataResponse">
        <xs:annotation>
            <xs:documentation>odpověď obsahující vyhledaná risková pomocná data, pokud byla nalezena.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="riskAdditionalData" type="RiskAdditionalData" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Vyhledaná risková pomocná data.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetRiskAdditionalDataRequest">
        <xs:annotation>
            <xs:documentation>Získání pomocných riskových dat z RDR.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>EnvelopeId na základě kterého se vyhledají risková pomocná data.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>
