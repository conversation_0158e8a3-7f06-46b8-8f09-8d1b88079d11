<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           xmlns="http://airbank.cz/rdr/ws/salarybankaccount"
           targetNamespace="http://airbank.cz/rdr/ws/salarybankaccount" jxb:version="3.0"
           elementFormDefault="qualified">

    <xs:element name="FindAndGetSalaryBankAccountRequest">
        <xs:annotation>
            <xs:documentation>Request webové operace findAndGetSalaryBankAccount pro načtení mzdového <PERSON>č<PERSON>.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountNumber" type="AccountNumber">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc><PERSON><PERSON><PERSON>.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetSalaryBankAccountsRequest">
        <xs:annotation>
            <xs:documentation>Request webové operace getSalaryBankAccounts pro načtení mzdových účtů.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountNumber" type="AccountNumber">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Číslo účtu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="uniqueOnly" type="xs:boolean">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Pokud true, vrací jen záznamy označené jako unikátní.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="AccountNumber">
        <xs:annotation>
            <xs:documentation>Komplexní typ reprezentující číslo účtu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="prefix" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prefix.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bankCode" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód banky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="FindAndGetSalaryBankAccountResponse">
        <xs:annotation>
            <xs:documentation>Response webové operace pro načtení mzdového účtu.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="account" type="Account" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Účet.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetSalaryBankAccountsResponse">
        <xs:annotation>
            <xs:documentation>Response webové operace pro načtení mzdových účtů.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accounts" type="Account" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Account">
        <xs:annotation>
            <xs:documentation>Komplexní typ reprezentující účet.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="AccountNumber">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prefix.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="owner" type="Owner">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Vlastník.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Owner">
        <xs:annotation>
            <xs:documentation>Komplexní typ reprezentující vlastníka.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="identificationNumber" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČ.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>