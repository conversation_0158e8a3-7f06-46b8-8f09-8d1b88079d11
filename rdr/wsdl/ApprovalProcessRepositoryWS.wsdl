<?xml version="1.0" encoding="UTF-8"?>

<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/rdr/ws/approvalprocess"
                  targetNamespace="http://airbank.cz/rdr/ws/approvalprocess">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/rdr/ws/approvalprocess">
            <xs:include schemaLocation="ApprovalProcessRepositoryWS.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="SaveApprovalProcessRequest">
        <wsdl:part element="SaveApprovalProcessRequest" name="SaveApprovalProcessRequest"/>
    </wsdl:message>
    <wsdl:message name="SaveApprovalProcessResponse">
        <wsdl:part element="SaveApprovalProcessResponse" name="SaveApprovalProcessResponse"/>
    </wsdl:message>

    <wsdl:message name="GetApprovalProcessDataRequest">
        <wsdl:part element="GetApprovalProcessDataRequest" name="GetApprovalProcessDataRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApprovalProcessDataResponse">
        <wsdl:part element="GetApprovalProcessDataResponse" name="GetApprovalProcessDataResponse"/>
    </wsdl:message>

    <wsdl:message name="GetPreviousResultsRequest">
        <wsdl:part element="GetPreviousResultsRequest" name="GetPreviousResultsRequest"/>
    </wsdl:message>

    <wsdl:message name="GetPreviousResultsResponse">
        <wsdl:part element="GetPreviousResultsResponse" name="GetPreviousResultsResponse"/>
    </wsdl:message>

    <wsdl:message name="GetPersonalScoreRequest">
        <wsdl:part element="GetPersonalScoreRequest" name="GetPersonalScoreRequest"/>
    </wsdl:message>

    <wsdl:message name="GetPersonalScoreResponse">
        <wsdl:part element="GetPersonalScoreResponse" name="GetPersonalScoreResponse"/>
    </wsdl:message>

    <wsdl:message name="GetHardChecksRequest">
        <wsdl:part element="GetHardChecksRequest" name="GetHardChecksRequest"/>
    </wsdl:message>

    <wsdl:message name="GetHardChecksResponse">
        <wsdl:part element="GetHardChecksResponse" name="GetHardChecksResponse"/>
    </wsdl:message>

    <wsdl:message name="GetConsolidationDataRequest">
        <wsdl:part element="GetConsolidationDataRequest" name="GetConsolidationDataRequest"/>
    </wsdl:message>
    <wsdl:message name="GetConsolidationDataResponse">
        <wsdl:part element="GetConsolidationDataResponse" name="GetConsolidationDataResponse"/>
    </wsdl:message>

    <wsdl:message name="GetUWRiskGradesRequest">
        <wsdl:part element="GetUWRiskGradesRequest" name="GetUWRiskGradesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetUWRiskGradesResponse">
        <wsdl:part element="GetUWRiskGradesResponse" name="GetUWRiskGradesResponse"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationDataStatusRequest">
        <wsdl:part element="GetApplicationDataStatusRequest" name="GetApplicationDataStatusRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationDataStatusResponse">
        <wsdl:part element="GetApplicationDataStatusResponse" name="GetApplicationDataStatusResponse"/>
    </wsdl:message>

    <wsdl:portType name="ApprovalProcessRepositoryService">
        <wsdl:operation name="SaveApprovalProcess">
            <wsdl:input message="SaveApprovalProcessRequest"/>
            <wsdl:output message="SaveApprovalProcessResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetApprovalProcessData">
            <wsdl:input message="GetApprovalProcessDataRequest"/>
            <wsdl:output message="GetApprovalProcessDataResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetPreviousResults">
            <wsdl:input message="GetPreviousResultsRequest"/>
            <wsdl:output message="GetPreviousResultsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetPersonalScore">
            <wsdl:input message="GetPersonalScoreRequest"/>
            <wsdl:output message="GetPersonalScoreResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetHardChecks">
            <wsdl:input message="GetHardChecksRequest"/>
            <wsdl:output message="GetHardChecksResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetConsolidationData">
            <wsdl:input message="GetConsolidationDataRequest"/>
            <wsdl:output message="GetConsolidationDataResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetUWRiskGrades">
            <wsdl:input message="GetUWRiskGradesRequest"/>
            <wsdl:output message="GetUWRiskGradesResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetApplicationDataStatus">
            <wsdl:input message="GetApplicationDataStatusRequest"/>
            <wsdl:output message="GetApplicationDataStatusResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ApprovalProcessRepositoryBinding" type="ApprovalProcessRepositoryService">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="SaveApprovalProcess">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetApprovalProcessData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPreviousResults">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPersonalScore">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetHardChecks">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetConsolidationData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetUWRiskGrades">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetApplicationDataStatus">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ApprovalProcessRepositoryServicePorts">
        <wsdl:port binding="ApprovalProcessRepositoryBinding" name="ApprovalProcessRepositoryService">
            <soap:address location="https://TODO/ApprovalProcessRepositoryService"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
