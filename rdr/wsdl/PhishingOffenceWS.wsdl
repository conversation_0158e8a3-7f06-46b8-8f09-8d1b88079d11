<?xml version="1.0" encoding="UTF-8"?>

<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/rdr/ws/phishingoffence"
                  targetNamespace="http://airbank.cz/rdr/ws/phishingoffence">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/rdr/ws/phishingoffence">
            <xs:include schemaLocation="PhishingOffenceWS.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="SavePhishingOffenceRequest">
        <wsdl:part element="SavePhishingOffenceRequest" name="SavePhishingOffenceRequest"/>
    </wsdl:message>
    <wsdl:message name="SavePhishingOffenceResponse">
        <wsdl:part element="SavePhishingOffenceResponse" name="SavePhishingOffenceResponse"/>
    </wsdl:message>

    <wsdl:message name="AddPhishingClearanceRequest">
        <wsdl:part element="AddPhishingClearanceRequest" name="AddPhishingClearanceRequest"/>
    </wsdl:message>
    <wsdl:message name="AddPhishingClearanceResponse">
        <wsdl:part element="AddPhishingClearanceResponse" name="AddPhishingClearanceResponse"/>
    </wsdl:message>

    <wsdl:message name="GetPhishingClearancesRequest">
        <wsdl:part element="GetPhishingClearancesRequest" name="GetPhishingClearancesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPhishingClearancesResponse">
        <wsdl:part element="GetPhishingClearancesResponse" name="GetPhishingClearancesResponse"/>
    </wsdl:message>

    <wsdl:message name="AddPhishingThreatTerminationRequest">
        <wsdl:part element="AddPhishingThreatTerminationRequest" name="AddPhishingThreatTerminationRequest"/>
    </wsdl:message>
    <wsdl:message name="AddPhishingThreatTerminationResponse">
        <wsdl:part element="AddPhishingThreatTerminationResponse" name="AddPhishingThreatTerminationResponse"/>
    </wsdl:message>

    <wsdl:message name="GetPhishingThreatTerminationsRequest">
        <wsdl:part element="GetPhishingThreatTerminationsRequest" name="GetPhishingThreatTerminationsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPhishingThreatTerminationsResponse">
        <wsdl:part element="GetPhishingThreatTerminationsResponse" name="GetPhishingThreatTerminationsResponse"/>
    </wsdl:message>

    <wsdl:portType name="PhishingOffence">
        <wsdl:operation name="savePhishingOffence">
            <xs:documentation>Rozhraní webové operace pro uložení důvodu a výsledku reakce na phishingový útok (PhishingOffence)</xs:documentation>
            <wsdl:input message="SavePhishingOffenceRequest"/>
            <wsdl:output message="SavePhishingOffenceResponse"/>
        </wsdl:operation>
        <wsdl:operation name="addPhishingClearance">
            <xs:documentation>Rozhraní webové operace pro zaznamenání očištění od podezření na phishingový útok</xs:documentation>
            <wsdl:input message="AddPhishingClearanceRequest"/>
            <wsdl:output message="AddPhishingClearanceResponse"/>
        </wsdl:operation>
        <wsdl:operation name="getPhishingClearances">
            <xs:documentation>Rozhraní webové operace pro načtení očištění od podezření z phishingového útoku</xs:documentation>
            <wsdl:input message="GetPhishingClearancesRequest"/>
            <wsdl:output message="GetPhishingClearancesResponse"/>
        </wsdl:operation>
        <wsdl:operation name="addPhishingThreatTermination">
            <xs:documentation>Rozhraní webové operace pro zaznamenání odstranění phishingového hrozby</xs:documentation>
            <wsdl:input message="AddPhishingThreatTerminationRequest"/>
            <wsdl:output message="AddPhishingThreatTerminationResponse"/>
        </wsdl:operation>
        <wsdl:operation name="getPhishingThreatTerminations">
            <xs:documentation>Rozhraní webové operace pro načtení detailů odstranění phishingového hrozby pro klienta</xs:documentation>
            <wsdl:input message="GetPhishingThreatTerminationsRequest"/>
            <wsdl:output message="GetPhishingThreatTerminationsResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="PhishingOffenceBinding" type="PhishingOffence">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="savePhishingOffence">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="addPhishingClearance">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getPhishingClearances">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="addPhishingThreatTermination">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getPhishingThreatTerminations">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="PhishingOffenceWS">
        <wsdl:port binding="PhishingOffenceBinding" name="PhishingOffencePort">
            <soap:address location="https://airbank.cz/rdr/ws/phishingOffence"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>