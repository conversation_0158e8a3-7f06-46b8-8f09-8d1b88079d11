<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/rdr/ws/amlapproval"
                  targetNamespace="http://airbank.cz/rdr/ws/amlapproval">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/rdr/ws/amlapproval">
            <xsd:include schemaLocation="AmlApprovalWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="SavePaymentsApprovalDataRequest">
        <wsdl:part element="SavePaymentsApprovalDataRequest" name="SavePaymentsApprovalDataRequest"/>
    </wsdl:message>
    <wsdl:message name="SavePaymentsApprovalDataResponse">
        <wsdl:part element="SavePaymentsApprovalDataResponse" name="SavePaymentsApprovalDataResponse"/>
    </wsdl:message>
    <wsdl:message name="SavePaymentsApprovalDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SavePaymentsApprovalDataFault"/>
    </wsdl:message>

    <wsdl:portType name="AmlApproval">
        <wsdl:operation name="savePaymentsApprovalData">
            <wsdl:input message="SavePaymentsApprovalDataRequest"/>
            <wsdl:output message="SavePaymentsApprovalDataResponse"/>
            <wsdl:fault name="SavePaymentsApprovalDataFault" message="SavePaymentsApprovalDataFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="AmlApprovalBinding" type="AmlApproval">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="savePaymentsApprovalData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SavePaymentsApprovalDataFault">
                <soap:fault name="SavePaymentsApprovalDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="AmlApprovalService">
        <wsdl:port name="AmlApprovalServicePort" binding="AmlApprovalBinding">
            <soap:address location="https://airbank.cz/rdr/ws/amlapproval"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
