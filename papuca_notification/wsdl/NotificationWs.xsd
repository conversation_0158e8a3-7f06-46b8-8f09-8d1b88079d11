<xs:schema targetNamespace="http://airbank.cz/ib/ws/notification/"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:not="http://airbank.cz/ib/ws/notification/"
           xmlns="http://schemas.xmlsoap.org/wsdl/">

    <xs:simpleType name="AuthorizationState">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IN_PROGRESS"/>
            <xs:enumeration value="AUTHORIZED"/>
            <xs:enumeration value="FAILED"/>
            <xs:enumeration value="TIMED_OUT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="AuthorizationFinishedRequest">
        <xs:annotation>
            <xs:documentation>
                Authorization finished notification
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="authId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Identifier of the authorization</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="authState" type="not:AuthorizationState">
                    <xs:annotation>
                        <xs:documentation>State of the authorization</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AuthorizationFinishedResponse">
        <xs:complexType>
            <xs:sequence>
                <!-- no fields -->
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="PairingFinishedRequest">
        <xs:annotation>
            <xs:documentation>
                Message informing IB that pairing for given CUID and bank has finished
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:all>
                <xs:element name="pairingHash" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Identifier of pairing that was finished.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="PairingFinishedResponse">
        <xs:complexType>
            <xs:sequence>
                <!-- no fields -->
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
