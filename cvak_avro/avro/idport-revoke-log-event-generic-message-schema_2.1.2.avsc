{"type": "record", "name": "GenericMessage", "namespace": "cz.monetplus.mep.messaging.api.avro.v2_1_2", "doc": "Generic container for Avro messages", "fields": [{"name": "version", "type": "string"}, {"name": "timestamp", "type": "string"}, {"name": "tenant", "type": ["null", "string"]}, {"name": "componentName", "type": "string"}, {"name": "severity", "type": ["null", "string"]}, {"name": "name", "type": "string"}, {"name": "type", "type": "string"}, {"name": "outcome", "type": "string"}, {"name": "eventId", "type": "string"}, {"name": "parameters", "type": {"type": "map", "values": {"type": "record", "name": "AvroStringParam", "fields": [{"name": "value", "type": "string"}, {"name": "dataType", "type": "string"}]}}}]}