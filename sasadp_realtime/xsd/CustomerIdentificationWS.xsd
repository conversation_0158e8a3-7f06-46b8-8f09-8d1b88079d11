<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
        targetNamespace="http://airbank.cz/sasadprealtime/ws/customeridentification"
        xmlns="http://airbank.cz/sasadprealtime/ws/customeridentification"
        jxb:version="2.1" elementFormDefault="qualified">

    <xsd:element name="GetCustomerHashRequest">
        <xsd:annotation>
            <xsd:documentation>Request a hash for cuid</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCustomerHashResponse">
        <xsd:annotation>
            <xsd:documentation>Response containing a hash for cuid</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuidHash" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:simpleType name="CustomerAction">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ANONYMIZED">
                <xsd:annotation>
                    <xsd:documentation>Klient byl anonymizován</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MERGE">
                <xsd:annotation>
                    <xsd:documentation>Dva zákazníci v CIFu byli sloučení v jednoho zákazníka (využití aliasu?)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NEW">
                <xsd:annotation>
                    <xsd:documentation>V CIFu byl založen nový zákazník</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SPLIT">
                <xsd:annotation>
                    <xsd:documentation>Jeden zákazník v CIFu byl rozdělen na dva různé zákazníky.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UPDATE">
                <xsd:annotation>
                    <xsd:documentation>Zákazník v CIFu byl aktualizován</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>