<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
        targetNamespace="http://airbank.cz/sasadprealtime/ws/campaign"
        xmlns="http://airbank.cz/sasadprealtime/ws/campaign"
        jxb:version="2.1" elementFormDefault="qualified">

    <xsd:element name="ReceiveCommunicationEntityChangesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="communicationEntityChange" type="CommunicationEntityChangeTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ReceiveCommunicationEntityChangesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="notProcessedEntities" type="xsd:string" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="RevokeEntitiesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="communicationEntity" type="CommunicationEntityTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="RevokeEntitiesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="result" type="RevokeResultTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="CommunicationEntityChangeTO">
        <xsd:sequence>
            <xsd:element name="communicationEntityClass" type="xsd:string"/>
            <xsd:element name="communicationEntityId" type="xsd:string"/>
            <xsd:element name="meaning" type="xsd:string"/>
            <xsd:element name="created" type="xsd:dateTime"/>
            <xsd:element name="statusReason" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CommunicationEntityTO">
        <xsd:sequence>
            <xsd:element name="entityType" type="xsd:string"/>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="externalId" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="RevokeResultTO">
        <xsd:sequence>
            <xsd:element name="externalId" type="xsd:string"/>
            <xsd:element name="result" type="xsd:string"/>
            <xsd:element name="detail" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>
    
</xsd:schema>