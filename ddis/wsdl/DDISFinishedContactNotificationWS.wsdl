<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- $Header: https://repository.ddms.local/svn/homecredit/airbank.ddis/trunk/airbank.ddis-ws/src/main/resources/wsdl/DDISFinishedContactNotificationWS.wsdl 180 2015-04-15 07:37:44Z ales.dolecek $ -->
<wsdl:definitions name="DDISFinishedContactNotificationWS"
		targetNamespace="http://dimensiondata.cz/airbank/ddis/ws/finishedcontactnotifications"
		xmlns:tns="http://dimensiondata.cz/airbank/ddis/ws/finishedcontactnotifications"
		xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
		xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
		xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsdl:types>
		<xsd:schema targetNamespace="http://dimensiondata.cz/airbank/ddis/ws/finishedcontactnotifications"
				elementFormDefault="qualified">
			<xsd:include schemaLocation="DDISFinishedContactNotificationWS.xsd"/>
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="finishContactBusinessSummaryRequest">
		<wsdl:part name="parameters" element="tns:finish-contact-business-summary-in" />
	</wsdl:message>
	<wsdl:message name="finishContactBusinessSummaryResponse">
		<wsdl:part name="result" element="tns:finish-contact-business-summary-out" />
	</wsdl:message>
	<wsdl:portType name="DDISFinishedContactNotification">
		<wsdl:operation name="finishContactBusinessSummary">
			<wsdl:input message="tns:finishContactBusinessSummaryRequest" />
			<wsdl:output message="tns:finishContactBusinessSummaryResponse" />
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="DDISFinishedContactNotificationSOAP" type="tns:DDISFinishedContactNotification">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="finishContactBusinessSummary">
			<soap:operation soapAction="http://dimensiondata.cz/airbank/ddis/ws/finishedcontactnotifications/finishContactBusinessSummary" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="DDISFinishedContactNotificationWS">
		<wsdl:port name="DDISFinishedContactNotificationSOAP" binding="tns:DDISFinishedContactNotificationSOAP">
			<soap:address location="http://localhost/ddis/ws/contact/notifications/" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
