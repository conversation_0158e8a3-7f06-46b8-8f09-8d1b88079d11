<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:common="http://airbank.cz/case/common"
           targetNamespace="http://airbank.cz/case/common" elementFormDefault="qualified">

    <xs:simpleType name="Channel">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IB">
                <xs:annotation>
                    <xs:documentation>internet banking</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BRANCH">
                <xs:annotation>
                    <xs:documentation>branch</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ICC">
                <xs:annotation>
                    <xs:documentation>call-center</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OPEN_API">
                <xs:annotation>
                    <xs:documentation>open api channel</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MB">
                <xs:annotation>
                    <xs:documentation>mobile banking</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PMT_GW">
                <xs:annotation>
                    <xs:documentation>payment gateway web</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PMT_GW_APP">
                <xs:annotation>
                    <xs:documentation>payment gateway app</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NIA">
                <xs:annotation>
                    <xs:documentation>NIA operations</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MB_BRANCH">
                <xs:annotation>
                    <xs:documentation>mobile application in branch mode</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VOICEBOT">
                <xs:annotation>
                    <xs:documentation>voice bot (on call auth)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="AuthElementType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SWT">
                <xs:annotation>
                    <xs:documentation>element based on mobile application</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SMS">
                <xs:annotation>
                    <xs:documentation>element based on one time password sent by SMS</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PWD">
                <xs:annotation>
                    <xs:documentation>element based on static password</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CONFIRMATION">
                <xs:annotation>
                    <xs:documentation>element based on confirmation</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VERBAL-APPROVAL">
                <xs:annotation>
                    <xs:documentation>element based on verbal approval, used in ICC channel</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BIRTHDAY">
                <xs:annotation>
                    <xs:documentation>element based on identity birthday knowledge</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INHERENCE">
                <xs:annotation>
                    <xs:documentation>element based on BLAZE decision</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTHER-BANK">
                <xs:annotation>
                    <xs:documentation>for authorization running in other bank (like PISP)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FACE-BIOMETRY">
                <xs:annotation>
                    <xs:documentation>element based on face picture check</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OPER-PWD">
                <xs:annotation>
                    <xs:documentation>element based on face picture check</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TOKEN">
                <xs:annotation>
                    <xs:documentation>token issued for particular session (ongoing call with Aneta assistant)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="HwtSerialNumber">
        <xs:annotation>
            <xs:documentation>unique identification of a hardware token</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{4}[0-9]{8}"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="ErrorType">
        <xs:annotation>
            <xs:documentation>general type of fault message</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="faultCode" type="common:ErrorCode">
                    <xs:annotation>
                        <xs:documentation>mandatory fault code, see ErrorCode enum</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="faultMessage" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>optional error message</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="additionalInfo" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>additional information (use this for debugging/tuning only)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="ErrorCode">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INVALID_CUID">
                <xs:annotation>
                    <xs:documentation>Input argument invalid - initiatorCuid/authorizerCuid</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVALID_ELEMENT">
                <xs:annotation>
                    <xs:documentation>Input argument invalid or not present - authElementId</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVALID_AUTH_ID">
                <xs:annotation>
                    <xs:documentation>Input argument invalid - authId</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVALID_REQUEST_DATA">
                <xs:annotation>
                    <xs:documentation>Input argument invalid - request</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVALID_SWT_ONLINE_SPECIFIC_DATA">
                <xs:annotation>
                    <xs:documentation>Input argument invalid or not present - swtOnlineSpecifics</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ILLEGAL_AUTHORIZATION_STATE">
                <xs:annotation>
                    <xs:documentation>authorization state (AuthnStatus) does not allow to continue with the authorization any more, authorization has already been authorized, rejected or timeouted</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ILLEGAL_ELEMENT">
                <xs:annotation>
                    <xs:documentation>Unable to make attempt with authElement specified</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SMS_CNT_EXCEEDED">
                <xs:annotation>
                    <xs:documentation>unable to send next challenge for the authorization, resend counter exceeded</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NO_AUTHORIZATION_METHOD">
                <xs:annotation>
                    <xs:documentation>No authorization method available for specified parameters</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ELEMENT_NOT_FOUND">
                <xs:annotation>
                    <xs:documentation>Auth element not found</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ELEMENT_NOT_BLOCKED">
                <xs:annotation>
                    <xs:documentation>Auth element not blocked</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BOOKING_NOT_FOUND">
                <xs:annotation>
                    <xs:documentation>Token order (booking) not found</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BRANCH_NOT_FOUND">
                <xs:annotation>
                    <xs:documentation>Branch not found</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INIT_SWT_FAILED">
                <xs:annotation>
                    <xs:documentation>Initialization of SW Token failed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INIT_HWT_FAILED">
                <xs:annotation>
                    <xs:documentation>Initialization of HW Token failed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CLIENT_NOT_FOUND">
                <xs:annotation>
                    <xs:documentation>Client not found</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EXTERNAL_FAULT">
                <xs:annotation>
                    <xs:documentation>Error obtained from external system. Specific error code (if any) available within additionalInfo element.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>

            <!-- NIA faults -->
            <xs:enumeration value="NIA_TIMEOUT">
                <xs:annotation>
                    <xs:documentation>communication with NIA failed/timeouted</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="IDENTIFICATION_FAILED">
                <xs:annotation>
                    <xs:documentation>client's identification in NIA failed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ILLEGAL_CLIENT_STATE">
                <xs:annotation>
                    <xs:documentation>illegal client's state for requested operation</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BIRTHDATE_MISMATCH">
                <xs:annotation>
                    <xs:documentation>client's birthday doesn't match in NIA and NIAIdentificationInputs service</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NO_ELIGIBLE_ID">
                <xs:annotation>
                    <xs:documentation>no ID provided for identification process</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NO_ELIGIBLE_RELATION">
                <xs:annotation>
                    <xs:documentation>NIAIdentificationInputs: eligibility check failure</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CLIENT_IS_FOREIGNER">
                <xs:annotation>
                    <xs:documentation>NIAIdentificationInputs: eligibility check failure</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_IN_PILOT">
                <xs:annotation>
                    <xs:documentation>NIAIdentificationInputs: eligibility check failure</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BELOW_AGE_OF_MAJORITY">
                <xs:annotation>
                    <xs:documentation>NIAIdentificationInputs: eligibility check failure</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_PHYSICALLY_IDENTIFIED">
                <xs:annotation>
                    <xs:documentation>NIAIdentificationInputs: eligibility check failure</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CLIENT_IS_DEAD">
                <xs:annotation>
                    <xs:documentation>NIAIdentificationInputs: eligibility check failure</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NIA_OPTED_OUT">
                <xs:annotation>
                    <xs:documentation>identity tried to authorize in NIA channel but NIA is opted out for this cuid</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NIA_NOT_IDENTIFIED">
                <xs:annotation>
                    <xs:documentation>identity tried to authorize in NIA channel but cuid is not identified in NIA</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
