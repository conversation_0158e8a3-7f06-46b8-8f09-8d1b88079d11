<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://airbank.cz/case/common/securityElementMgmtService"
           xmlns:enum="http://airbank.cz/case/enum/securityElementMgmtService"
           xmlns:common="http://airbank.cz/case/common"
           elementFormDefault="qualified">

    <xs:import schemaLocation="securityElementMgmtServiceEnum.xsd"
               namespace="http://airbank.cz/case/enum/securityElementMgmtService"/>
    <xs:import schemaLocation="serviceCommon.xsd" namespace="http://airbank.cz/case/common"/>

    <xs:complexType name="AuthTokenOrder">
        <xs:annotation>
            <xs:documentation>object that encapsulates an token order</xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="orderId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>unique identification of the token order, generated by Security Element Mgmt
                        Service while order is initialized
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="orderType" type="enum:OrderType">
                <xs:annotation>
                    <xs:documentation>type of the order</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reason" type="xs:string">
                <xs:annotation>
                    <xs:documentation>reason of token order request (value from MDM code list)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="branchID" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        identification of branch that the order belongs to
                        branchId is not used when order is to be delivered to the customer
                        branchId might not be present for reservation (= virtual reservation, not tied-up to a branch)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveryAddress" type="xs:string">
                <xs:annotation>
                    <xs:documentation>client's delivery address of given token, formatted as one single string, just for
                        information
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="orderDate" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>time when the order was initialized</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reservationExpiration" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when the order will expire</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="distributionDate" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time of order distribution to branch or to customer</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="AuthElementInstance">
        <xs:annotation>
            <xs:documentation>object that encapsulates an element</xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="elementType" type="common:AuthElementType">
                <xs:annotation>
                    <xs:documentation>type of the element</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="elementId" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        identifier of the element
                        HWT token uses serial number
                        SWT token uses idInstallation
                        other elements
                        uses internally assigned identifier
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="state" type="enum:AuthElementState">
                <xs:annotation>
                    <xs:documentation>state of the element instance, see AuthElementState enumeration</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="failedAttemptCounter" type="xs:integer">
                <xs:annotation>
                    <xs:documentation>counter of error attempts made by this element</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>identifier of client that owns auth element</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="initialized" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when element was initialized</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paired" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when element was paired to the client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="setPrimary" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when element was set as primary element</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="blocked" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when element was blocked</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastAttempt" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time of last auth attempt of given element instance</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expiration" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when element expired (currently only for HWT)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dismissed" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>time when element was dismissed</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dismissionReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>a reason why element was dismissed</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="FaceBiometryElement">
        <xs:annotation>
            <xs:documentation>object that encapsulates FACE-BIOMETRY element</xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:documentation>identifier of client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="facePictureUuid" type="xs:string">
                <xs:annotation>
                    <xs:documentation>uuid of the face picture</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="verification" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>time when the element has been verified</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="score" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>score result of the verification</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>
</xs:schema>
