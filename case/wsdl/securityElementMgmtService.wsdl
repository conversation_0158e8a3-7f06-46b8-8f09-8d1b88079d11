<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/case/ws/securityElementMgmtService"
                  xmlns:common="http://airbank.cz/case/common"
                  targetNamespace="http://airbank.cz/case/ws/securityElementMgmtService">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/case/ws/securityElementMgmtService">
            <xs:include schemaLocation="../xsd/securityElementMgmtServiceCommon.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="InitHWTokenRequest">
        <wsdl:part element="tns:InitHWTokenRequest" name="InitHWTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="InitHWTokenResponse">
        <wsdl:part element="tns:InitHWTokenResponse" name="InitHWTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="InitHWTokenFault">
        <wsdl:part element="common:ErrorType" name="InitHWTokenFault"/>
    </wsdl:message>

    <wsdl:message name="CanPairHWTokenRequest">
        <wsdl:part element="tns:CanPairHWTokenRequest" name="CanPairHWTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="CanPairHWTokenResponse">
        <wsdl:part element="tns:CanPairHWTokenResponse" name="CanPairHWTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="CanPairHWTokenFault">
        <wsdl:part element="common:ErrorType" name="CanPairHWTokenFault"/>
    </wsdl:message>

    <wsdl:message name="PairHWTokenRequest">
        <wsdl:part element="tns:PairHWTokenRequest" name="PairHWTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="PairHWTokenResponse">
        <wsdl:part element="tns:PairHWTokenResponse" name="PairHWTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="PairHWTokenFault">
        <wsdl:part element="common:ErrorType" name="PairHWTokenFault"/>
    </wsdl:message>

    <wsdl:message name="VerifyHWTokenUsabilityRequest">
        <wsdl:part element="tns:VerifyHWTokenUsabilityRequest" name="VerifyHWTokenUsabilityRequest"/>
    </wsdl:message>
    <wsdl:message name="VerifyHWTokenUsabilityResponse">
        <wsdl:part element="tns:VerifyHWTokenUsabilityResponse" name="VerifyHWTokenUsabilityResponse"/>
    </wsdl:message>
    <wsdl:message name="VerifyHWTokenUsabilityFault">
        <wsdl:part element="common:ErrorType" name="VerifyHWTokenUsabilityFault"/>
    </wsdl:message>

    <wsdl:message name="DiscardTokenRequest">
        <wsdl:part element="tns:DiscardTokenRequest" name="DiscardTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="DiscardTokenResponse">
        <wsdl:part element="tns:DiscardTokenResponse" name="DiscardTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="DiscardTokenFault">
        <wsdl:part element="common:ErrorType" name="DiscardTokenFault"/>
    </wsdl:message>

    <wsdl:message name="OrderHWTokenRequest">
        <wsdl:part element="tns:OrderHWTokenRequest" name="OrderHWTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="OrderHWTokenResponse">
        <wsdl:part element="tns:OrderHWTokenResponse" name="OrderHWTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="OrderHWTokenFault">
        <wsdl:part element="common:ErrorType" name="OrderHWTokenFault"/>
    </wsdl:message>

    <wsdl:message name="CancelHWTokenOrderRequest">
        <wsdl:part element="tns:CancelHWTokenOrderRequest" name="CancelHWTokenOrderRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelHWTokenOrderResponse">
        <wsdl:part element="tns:CancelHWTokenOrderResponse" name="CancelHWTokenOrderResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelHWTokenOrderFault">
        <wsdl:part element="common:ErrorType" name="CancelHWTokenOrderFault"/>
    </wsdl:message>

    <wsdl:message name="InitSWTokenRequest">
        <wsdl:part element="tns:InitSWTokenRequest" name="InitSWTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSWTokenResponse">
        <wsdl:part element="tns:InitSWTokenResponse" name="InitSWTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSWTokenFault">
        <wsdl:part element="common:ErrorType" name="InitSWTokenFault"/>
    </wsdl:message>

    <wsdl:message name="InitSMSRequest">
        <wsdl:part element="tns:InitSMSRequest" name="InitSMSRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSMSResponse">
        <wsdl:part element="tns:InitSMSResponse" name="InitSMSResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSMSFault">
        <wsdl:part element="common:ErrorType" name="InitSMSFault"/>
    </wsdl:message>

    <wsdl:message name="InitSWTokenBioRequest">
        <wsdl:part element="tns:InitSWTokenBioRequest" name="InitSWTokenBioRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSWTokenBioResponse">
        <wsdl:part element="tns:InitSWTokenBioResponse" name="InitSWTokenBioResponse"/>
    </wsdl:message>

    <wsdl:message name="GetElementsByCuidRequest">
        <wsdl:part element="tns:GetElementsByCuidRequest" name="GetElementsByCuidRequest"/>
    </wsdl:message>
    <wsdl:message name="GetElementsByCuidResponse">
        <wsdl:part element="tns:GetElementsByCuidResponse" name="GetElementsByCuidResponse"/>
    </wsdl:message>
    <wsdl:message name="GetElementsByCuidFault">
        <wsdl:part element="common:ErrorType" name="GetElementsByCuidFault"/>
    </wsdl:message>

    <wsdl:message name="GetElementByIdRequest">
        <wsdl:part element="tns:GetElementByIdRequest" name="GetElementByIdRequest"/>
    </wsdl:message>
    <wsdl:message name="GetElementByIdResponse">
        <wsdl:part element="tns:GetElementByIdResponse" name="GetElementByIdResponse"/>
    </wsdl:message>
    <wsdl:message name="GetElementByIdFault">
        <wsdl:part element="common:ErrorType" name="GetElementByIdFault"/>
    </wsdl:message>

    <wsdl:message name="UnblockElementRequest">
        <wsdl:part element="tns:UnblockElementRequest" name="UnblockElementRequest"/>
    </wsdl:message>
    <wsdl:message name="UnblockElementResponse">
        <wsdl:part element="tns:UnblockElementResponse" name="UnblockElementResponse"/>
    </wsdl:message>
    <wsdl:message name="UnblockElementFault">
        <wsdl:part element="common:ErrorType" name="UnblockElementFault"/>
    </wsdl:message>

    <wsdl:message name="BlockElementRequest">
        <wsdl:part element="tns:BlockElementRequest" name="BlockElementRequest"/>
    </wsdl:message>
    <wsdl:message name="BlockElementResponse">
        <wsdl:part element="tns:BlockElementResponse" name="BlockElementResponse"/>
    </wsdl:message>
    <wsdl:message name="BlockElementFault">
        <wsdl:part element="common:ErrorType" name="BlockElementFault"/>
    </wsdl:message>

    <wsdl:message name="GetPrimaryAuthElementRequest">
        <wsdl:part element="tns:GetPrimaryAuthElementRequest" name="GetPrimaryAuthElementRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPrimaryAuthElementResponse">
        <wsdl:part element="tns:GetPrimaryAuthElementResponse" name="GetPrimaryAuthElementResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPrimaryAuthElementFault">
        <wsdl:part element="common:ErrorType" name="GetPrimaryAuthElementFault"/>
    </wsdl:message>

    <wsdl:message name="SetPrimaryAuthElementRequest">
        <wsdl:part element="tns:SetPrimaryAuthElementRequest" name="SetPrimaryAuthElementRequest"/>
    </wsdl:message>
    <wsdl:message name="SetPrimaryAuthElementResponse">
        <wsdl:part element="tns:SetPrimaryAuthElementResponse" name="SetPrimaryAuthElementResponse"/>
    </wsdl:message>
    <wsdl:message name="SetPrimaryAuthElementFault">
        <wsdl:part element="common:ErrorType" name="SetPrimaryAuthElementFault"/>
    </wsdl:message>

    <wsdl:message name="ResetCustomerToInitialAuthMethodListRequest">
        <wsdl:part element="tns:ResetCustomerToInitialAuthMethodListRequest"
                   name="ResetCustomerToInitialAuthMethodListRequest"/>
    </wsdl:message>
    <wsdl:message name="ResetCustomerToInitialAuthMethodListResponse">
        <wsdl:part element="tns:ResetCustomerToInitialAuthMethodListResponse"
                   name="ResetCustomerToInitialAuthMethodListResponse"/>
    </wsdl:message>
    <wsdl:message name="ResetCustomerToInitialAuthMethodListFault">
        <wsdl:part element="common:ErrorType" name="ResetCustomerToInitialAuthMethodListFault"/>
    </wsdl:message>

    <wsdl:message name="UnblockAutoBlockedElementsByChannelRequest">
        <wsdl:part element="tns:UnblockAutoBlockedElementsByChannelRequest"
                   name="UnblockAutoBlockedElementsByChannelRequest"/>
    </wsdl:message>
    <wsdl:message name="UnblockAutoBlockedElementsByChannelResponse">
        <wsdl:part element="tns:UnblockAutoBlockedElementsByChannelResponse"
                   name="UnblockAutoBlockedElementsByChannelResponse"/>
    </wsdl:message>
    <wsdl:message name="UnblockAutoBlockedElementsByChannelFault">
        <wsdl:part element="common:ErrorType" name="UnblockAutoBlockedElementsByChannelFault"/>
    </wsdl:message>

    <wsdl:message name="UnblockAllAutoBlockedElementsRequest">
        <wsdl:part element="tns:UnblockAllAutoBlockedElementsRequest"
                   name="UnblockAllAutoBlockedElementsRequest"/>
    </wsdl:message>
    <wsdl:message name="UnblockAllAutoBlockedElementsResponse">
        <wsdl:part element="tns:UnblockAllAutoBlockedElementsResponse"
                   name="UnblockAllAutoBlockedElementsResponse"/>
    </wsdl:message>
    <wsdl:message name="UnblockAllAutoBlockedElementsFault">
        <wsdl:part element="common:ErrorType" name="UnblockAllAutoBlockedElementsFault"/>
    </wsdl:message>

    <wsdl:message name="AnonymizeClientRequest">
        <wsdl:part element="tns:AnonymizeClientRequest" name="AnonymizeClientRequest"/>
    </wsdl:message>
    <wsdl:message name="AnonymizeClientResponse">
        <wsdl:part element="tns:AnonymizeClientResponse" name="AnonymizeClientResponse"/>
    </wsdl:message>
    <wsdl:message name="AnonymizeClientFault">
        <wsdl:part element="common:ErrorType" name="AnonymizeClientFault"/>
    </wsdl:message>

    <wsdl:message name="ResetCountersRequest">
        <wsdl:part element="tns:ResetCountersRequest" name="ResetCountersRequest"/>
    </wsdl:message>
    <wsdl:message name="ResetCountersResponse">
        <wsdl:part element="tns:ResetCountersResponse" name="ResetCountersResponse"/>
    </wsdl:message>
    <wsdl:message name="ResetCountersFault">
        <wsdl:part element="common:ErrorType" name="ResetCountersFault"/>
    </wsdl:message>

    <wsdl:message name="SetAuthFacePictureRequest">
        <wsdl:part element="tns:SetAuthFacePictureRequest" name="SetAuthFacePictureRequest"/>
    </wsdl:message>
    <wsdl:message name="SetAuthFacePictureResponse">
        <wsdl:part element="tns:SetAuthFacePictureResponse" name="SetAuthFacePictureResponse"/>
    </wsdl:message>
    <wsdl:message name="SetAuthFacePictureFault">
        <wsdl:part element="common:ErrorType" name="SetAuthFacePictureFault"/>
    </wsdl:message>

    <wsdl:message name="SearchSuspiciousFaceBiometryElementsRequest">
        <wsdl:part element="tns:SearchSuspiciousFaceBiometryElementsRequest" name="SearchSuspiciousFaceBiometryElementsRequest"/>
    </wsdl:message>
    <wsdl:message name="SearchSuspiciousFaceBiometryElementsResponse">
        <wsdl:part element="tns:SearchSuspiciousFaceBiometryElementsResponse" name="SearchSuspiciousFaceBiometryElementsResponse"/>
    </wsdl:message>
    <wsdl:message name="SearchSuspiciousFaceBiometryElementsFault">
        <wsdl:part element="common:ErrorType" name="SearchSuspiciousFaceBiometryElementsFault"/>
    </wsdl:message>

    <wsdl:message name="VerifyAuthFacePictureRequest">
        <wsdl:part element="tns:VerifyAuthFacePictureRequest" name="VerifyAuthFacePictureRequest"/>
    </wsdl:message>
    <wsdl:message name="VerifyAuthFacePictureResponse">
        <wsdl:part element="tns:VerifyAuthFacePictureResponse" name="VerifyAuthFacePictureResponse"/>
    </wsdl:message>
    <wsdl:message name="VerifyAuthFacePictureFault">
        <wsdl:part element="common:ErrorType" name="VerifyAuthFacePictureFault"/>
    </wsdl:message>

    <wsdl:message name="SetAuthFacePictureUnverifiableRequest">
        <wsdl:part element="tns:SetAuthFacePictureUnverifiableRequest" name="SetAuthFacePictureUnverifiableRequest"/>
    </wsdl:message>
    <wsdl:message name="SetAuthFacePictureUnverifiableResponse">
        <wsdl:part element="tns:SetAuthFacePictureUnverifiableResponse" name="SetAuthFacePictureUnverifiableResponse"/>
    </wsdl:message>
    <wsdl:message name="SetAuthFacePictureUnverifiableFault">
        <wsdl:part element="common:ErrorType" name="SetAuthFacePictureUnverifiableFault"/>
    </wsdl:message>

    <wsdl:portType name="SecureElementMgmtService">
        <wsdl:operation name="initHWToken">
            <wsdl:documentation>
                DEPRECATED: HWT element is obsolete

                performs HW Token initialization
                HW Tokens will be primarily imported via Token management console
                application (batch processing)
                this operation allows to import one HWT

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / serialNumber : invalid parameter value
                INVALID_REQUEST_DATA / : duplicate element found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:InitHWTokenRequest"/>
            <wsdl:output message="tns:InitHWTokenResponse"/>
            <wsdl:fault message="tns:InitHWTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="canPairHWToken">
            <wsdl:documentation>
                DEPRECATED: HWT element is obsolete

                checks whether HW Token can be paired to the client
                returns true when pairing is possible, false
                when not allowed
                fault when input data is invalid

                Generated faults:
                code / attribute : description
                ELEMENT_NOT_FOUND / serialNumber : HW token not found
                INVALID_REQUEST_DATA / cuid or serialNumber parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:CanPairHWTokenRequest"/>
            <wsdl:output message="tns:CanPairHWTokenResponse"/>
            <wsdl:fault message="tns:CanPairHWTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="pairHWToken">
            <wsdl:documentation>
                DEPRECATED: HWT element is obsolete

                pairs HW Token to the client

                Generated faults:
                code / attribute : description
                ELEMENT_NOT_FOUND / serialNumber : HW token not found
                INVALID_REQUEST_DATA / cuid or serialNumber parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:PairHWTokenRequest"/>
            <wsdl:output message="tns:PairHWTokenResponse"/>
            <wsdl:fault message="tns:PairHWTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyHWTokenUsability">
            <wsdl:documentation>
                DEPRECATED: HWT element is obsolete

                verifies usability of the HW Token

                Generated faults:
                code / attribute : description
                ELEMENT_NOT_FOUND / serialNumber : HW token not found
                INVALID_CUID / cuid : HWT is paired to another or no client
                INVALID_ELEMENT / : invalid element state
				  valid states are: PAIRED, PRIMARY
                  following element states will cause this fault: NEW, ON_BRANCH, UNKNOWN, DISMISSED, BLOCKED_MAN, BLOCKED_AUTO
                INVALID_REQUEST_DATA / : cuid or serialNumber parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:VerifyHWTokenUsabilityRequest"/>
            <wsdl:output message="tns:VerifyHWTokenUsabilityResponse"/>
            <wsdl:fault message="tns:VerifyHWTokenUsabilityFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="discardToken">
            <wsdl:documentation>
                discards token (HWT, SWT)
                returns DiscardTokenResult with values
                - OK
                - OK_ALREADY_DISCARDED
                -
                WARN_PRIMARY
                - WARN_NOT_FOUND

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / elementId or elementType parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:DiscardTokenRequest"/>
            <wsdl:output message="tns:DiscardTokenResponse"/>
            <wsdl:fault message="tns:DiscardTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="orderHWToken">
            <wsdl:documentation>
                DEPRECATED: HWT element is obsolete

                makes order of HW Token - delivery to the client or reservation on (virtual) branch

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid or reason parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:OrderHWTokenRequest"/>
            <wsdl:output message="tns:OrderHWTokenResponse"/>
            <wsdl:fault message="tns:OrderHWTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="cancelHWTokenOrder">
            <wsdl:documentation>
                DEPRECATED: HWT element is obsolete

                discards reservation or delivery request of HW Token

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / orderId : orderId is invalid
                BOOKING_NOT_FOUND / orderId : orderId not found
                OPERATION_FAILED / : the operation failed, e.g. wrong order status
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:CancelHWTokenOrderRequest"/>
            <wsdl:output message="tns:CancelHWTokenOrderResponse"/>
            <wsdl:fault message="tns:CancelHWTokenOrderFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="initSWToken">
            <wsdl:documentation>
                initializes SW Token of the client

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid or idInstallation parameter is invalid
                INIT_SWT_FAILED / initialization of SWT failed
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:InitSWTokenRequest"/>
            <wsdl:output message="tns:InitSWTokenResponse"/>
            <wsdl:fault message="tns:InitSWTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="initSMS">
            <wsdl:documentation>
                initializes SMS encryption for client who uses CAir application

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid or idInstallation parameter is invalid
                INIT_SMS_FAILED / initialization of SMS failed
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:InitSMSRequest"/>
            <wsdl:output message="tns:InitSMSResponse"/>
            <wsdl:fault message="tns:InitSMSFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="initSWTokenBio">
            <wsdl:documentation>
                initializes Biometrics on SW Token of the client

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid or idInstallation parameter is invalid
                INIT_SWT_FAILED / initialization of SWT failed
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:InitSWTokenBioRequest"/>
            <wsdl:output message="tns:InitSWTokenBioResponse"/>
            <wsdl:fault message="tns:InitSWTokenFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="getElementsByCuid">
            <wsdl:documentation>
                gets (all/filtered) auth elements registered to the given client

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / parameter cuid is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:GetElementsByCuidRequest"/>
            <wsdl:output message="tns:GetElementsByCuidResponse"/>
            <wsdl:fault message="tns:GetElementsByCuidFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="getElementById">
            <wsdl:documentation>
                gets element by its identifier (element type and element id)

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / elementId or elementType parameter is invalid
                ELEMENT_NOT_FOUND / auth element not found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:GetElementByIdRequest"/>
            <wsdl:output message="tns:GetElementByIdResponse"/>
            <wsdl:fault message="tns:GetElementByIdFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="unblockElement">
            <wsdl:documentation>
                unblocks blocked auth element
                method unblocks element from either manually or automatically
                blocked state (BLOCKED_MAN, BLOCKED_AUTO)
                element state is set to USABLE/PAIRED/PRIMARY depending on element type and
                current primary element
                basic rules:
                - SWT,HWT: there is no primary element for the client -> unblocked element is set to PRIMARY, otherwise
                it is set to
                PAIRED state
                - SMS: there is no primary element for the client -> unblocked element is set to PRIMARY, otherwise it
                is set to
                USABLE state
                - others: set to USABLE state
                failed attempt counter of this element is reset to zero

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / elementId or elementType parameter is invalid
                ELEMENT_NOT_FOUND / auth element not found
                ELEMENT_NOT_BLOCKED / auth element is not in blocked state (BLOCKED_MAN nor BLOCKED_AUTO)
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:UnblockElementRequest"/>
            <wsdl:output message="tns:UnblockElementResponse"/>
            <wsdl:fault message="tns:UnblockElementFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="blockElement">
            <wsdl:documentation>
                blocks auth element
                method sets element to manually blocked state (BLOCKED_MAN)
                returns
                BlockElementResult with values
                - OK
                - OK_ALREADY_BLOCKED
                - WARN_PRIMARY
                - WARN_NOT_FOUND

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / elementId or elementType parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:BlockElementRequest"/>
            <wsdl:output message="tns:BlockElementResponse"/>
            <wsdl:fault message="tns:BlockElementFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="setPrimaryAuthElement">
            <wsdl:documentation>
                sets auth element as primary for given client

                Generated faults:
                code / attribute : description
                ILLEGAL_ELEMENT / elementId, elementType : wrong element status
                INVALID_ELEMENT / elementId, elementType : wrong element type (the the of element is invalid for being primary)
                INVALID_REQUEST_DATA / : elementId or elementType or cuid parameter is invalid
                INVALID_CUID / cuid : the element is paired to another client
                ELEMENT_NOT_FOUND / : auth element not found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:SetPrimaryAuthElementRequest"/>
            <wsdl:output message="tns:SetPrimaryAuthElementResponse"/>
            <wsdl:fault message="tns:SetPrimaryAuthElementFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="getPrimaryAuthElement">
            <wsdl:documentation>
                gets primary auth element for given client

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid parameter is invalid
                ELEMENT_NOT_FOUND / no primary auth element found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:GetPrimaryAuthElementRequest"/>
            <wsdl:output message="tns:GetPrimaryAuthElementResponse"/>
            <wsdl:fault message="tns:GetPrimaryAuthElementFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="resetCustomerToInitialAuthMethodList">
            <wsdl:documentation>
                resets to default initial auth method list for given customer

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid : parameter is invalid
                CLIENT_NOT_FOUND / cuid: client not found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:ResetCustomerToInitialAuthMethodListRequest"/>
            <wsdl:output message="tns:ResetCustomerToInitialAuthMethodListResponse"/>
            <wsdl:fault message="tns:ResetCustomerToInitialAuthMethodListFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="unblockAutoBlockedElementsByChannel">
            <wsdl:documentation>
                unblocks all auth elements blocked by unsuccessful attempts that are usable in the given channel
                method uses configuration of authorization methods
                all elements that can be used for authorization of any operation
                type in the given channel are unblocked from the BLOCKED_AUTO state
                elements blocked manually (by blockElement - in
                state BLOCKED_MAN) are not unblocked by this method
                failed attempt counter of each unblocked element is reset to zero

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid or channel parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:UnblockAutoBlockedElementsByChannelRequest"/>
            <wsdl:output message="tns:UnblockAutoBlockedElementsByChannelResponse"/>
            <wsdl:fault message="tns:UnblockAutoBlockedElementsByChannelFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="unblockAllAutoBlockedElements">
            <wsdl:documentation>
                unblocks all auth elements blocked by unsuccessful attempts
                method uses configuration of authorization methods
                all elements that can be used for authorization of any operation
                type in the given channel are unblocked from the BLOCKED_AUTO state
                elements blocked manually (by blockElement - in
                state BLOCKED_MAN) are not unblocked by this method
                failed attempt counter of each unblocked element is reset to zero

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / cuid is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:UnblockAllAutoBlockedElementsRequest"/>
            <wsdl:output message="tns:UnblockAllAutoBlockedElementsResponse"/>
            <wsdl:fault message="tns:UnblockAllAutoBlockedElementsFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="anonymizeClient">
            <wsdl:documentation>
                Performs client anonymization, i.e. all sensitive data like client firstname,
                surname or address are replaced by '***' characters.

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / parameter cuid is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:AnonymizeClientRequest"/>
            <wsdl:output message="tns:AnonymizeClientResponse"/>
            <wsdl:fault message="tns:AnonymizeClientFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="resetCounters">
            <wsdl:documentation>
                Performs client anonymization, i.e. all sensitive data like client firstname,
                surname or address are replaced by '***' characters.

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / parameter cuid or elementType is invalid
				CLIENT_NOT_FOUND / cuid: client not found
				ELEMENT_NOT_FOUND / auth element not found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:ResetCountersRequest"/>
            <wsdl:output message="tns:ResetCountersResponse"/>
            <wsdl:fault message="tns:ResetCountersFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="setAuthFacePicture">
            <wsdl:documentation>
                Sets new authentication face picture for the client.

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / parameter cuid is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:SetAuthFacePictureRequest"/>
            <wsdl:output message="tns:SetAuthFacePictureResponse"/>
            <wsdl:fault message="tns:SetAuthFacePictureFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="searchSuspiciousFaceBiometryElements">
            <wsdl:documentation>
                Searches all newest suspicious face biometry elements (face pictures). Pagination available.

                Generated faults:
                code / attribute : description
                INVALID_REQUEST_DATA / parameter is invalid
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:SearchSuspiciousFaceBiometryElementsRequest"/>
            <wsdl:output message="tns:SearchSuspiciousFaceBiometryElementsResponse"/>
            <wsdl:fault message="tns:SearchSuspiciousFaceBiometryElementsFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyAuthFacePicture">
            <wsdl:documentation>
                Sets VERIFIED_OK state on specified FaceBiometry element.

                Generated faults:
                code / attribute : description
                ELEMENT_NOT_FOUND / auth element not found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:VerifyAuthFacePictureRequest"/>
            <wsdl:output message="tns:VerifyAuthFacePictureResponse"/>
            <wsdl:fault message="tns:VerifyAuthFacePictureFault" name="fault"/>
        </wsdl:operation>

        <wsdl:operation name="setAuthFacePictureUnverifiable">
            <wsdl:documentation>
                Sets UNVERIFIABLE state on specified FaceBiometry element.

                Generated faults:
                code / attribute : description
                ELEMENT_NOT_FOUND / auth element not found
                SERVER / : general server error
            </wsdl:documentation>
            <wsdl:input message="tns:SetAuthFacePictureUnverifiableRequest"/>
            <wsdl:output message="tns:SetAuthFacePictureUnverifiableResponse"/>
            <wsdl:fault message="tns:SetAuthFacePictureUnverifiableFault" name="fault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="secureElementMgmtServiceSoap" type="tns:SecureElementMgmtService">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="initHWToken">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="canPairHWToken">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="pairHWToken">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="verifyHWTokenUsability">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="discardToken">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="orderHWToken">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="cancelHWTokenOrder">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="initSWToken">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="initSMS">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="initSWTokenBio">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getElementsByCuid">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getElementById">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="unblockElement">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="blockElement">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="setPrimaryAuthElement">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getPrimaryAuthElement">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="resetCustomerToInitialAuthMethodList">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="unblockAutoBlockedElementsByChannel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="unblockAllAutoBlockedElements">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="anonymizeClient">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
		<wsdl:operation name="resetCounters">
			<soap:operation soapAction=""/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
        <wsdl:operation name="setAuthFacePicture">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="searchSuspiciousFaceBiometryElements">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="verifyAuthFacePicture">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="setAuthFacePictureUnverifiable">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="secureElementMgmtService">
        <wsdl:port binding="tns:secureElementMgmtServiceSoap" name="secureElementMgmtServiceSoap">
            <soap:address location="http://TO-BE-CHANGED/secure-element-mgmt-service/ws"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
