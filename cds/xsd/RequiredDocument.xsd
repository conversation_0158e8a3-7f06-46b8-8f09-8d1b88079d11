<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns="http://airbank.cz/cds/ws/requireddocument"
            xmlns:common="http://airbank.cz/cds/ws/common"
            targetNamespace="http://airbank.cz/cds/ws/requireddocument"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/cds/ws/common" schemaLocation="DocumentCommon.xsd"/>

    <xsd:complexType name="RequiredDocumentGroupHistoryTO">
        <xsd:annotation>
            <xsd:documentation>Group history of required documents, grouped by channel, product type and document group detail if exists</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="common:AuditedEntityTO">
                <xsd:sequence>
                    <xsd:element name="productType" type="common:ProductTypeTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Definition of product type group (DEPOSIT or LOAN)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="loanBinFrom" type="xsd:long" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Loan amount from which is document required.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="groupCount" type="xsd:int" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>count of required documents from document group.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="groupType" type="common:GroupTypeTO">
                        <xsd:annotation>
                            <xsd:documentation>type of document group</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="groupRelation" type="common:GroupRelationTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>information about association to employment (for which income type is document required)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="deliveryWay" type="common:DeliveryWayTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>information about channel, that is allowed as delivery channel for required document.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cuid" type="xsd:long"/>
                    <xsd:element name="documentType" type="common:DocumentTypeWithCountTO" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="RequiredDocumentGroupFilterTO">
        <xsd:annotation>
            <xsd:documentation>Filter for group of required documents</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Primary ID</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="envelopeId" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Envelope ID</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>CUID</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="productType" type="common:ProductTypeTO" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Definition of product type group (DEPOSIT or LOAN)</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="deliveryWay" type="common:DeliveryWayTO" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>information about channel, that is allowed as delivery channel for required document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanAmount" type="xsd:decimal" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Loan amount</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="groupType" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>group type</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="documentType" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>document types</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="optional" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>optional flag in required document group</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BaseRequiredDocumentGroupTO">
        <xsd:annotation>
            <xsd:documentation>Abstract group of required documents with basic attributes</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="common:AuditedEntityTO">
                <xsd:sequence>
                    <xsd:element name="productType" type="common:ProductTypeTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Definition of product type group (DEPOSIT or LOAN)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="loanBinFrom" type="xsd:decimal" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Loan amount from which is document required.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="groupCount" type="xsd:int" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>count of required documents from document group.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="groupType" type="common:GroupTypeTO">
                        <xsd:annotation>
                            <xsd:documentation>type of document group</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="groupRelation" type="common:GroupRelationTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>information about association to employment (for which income type is document required)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="deliveryWay" type="common:DeliveryWayTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>information about channel, that is allowed as delivery channel for required document.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cuid" type="xsd:long"/>
                    <xsd:element name="envelopeId" type="xsd:long" minOccurs="0"/>
                    <xsd:element name="requiredDocumentChange" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="faceMatchCheckStatus" type="FaceMatchCheckStatusTO" minOccurs="0"/>
                    <xsd:element name="faceMatchScore" type="xsd:decimal" minOccurs="0"/>
                    <xsd:element name="optional" type="xsd:boolean"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="FaceMatchCheckStatusTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PENDING"/>
            <xsd:enumeration value="NOT_APPLICABLE"/>
            <xsd:enumeration value="ENQUEUED"/>
            <xsd:enumeration value="DONE"/>
            <xsd:enumeration value="NOT_RECOGNIZED"/>
            <xsd:enumeration value="FAILED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="RequiredDocumentGroupTO">
        <xsd:annotation>
            <xsd:documentation>Abstract group of required documents, grouped by channel a product type</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="BaseRequiredDocumentGroupTO">
                <xsd:sequence>
                    <xsd:element name="requiredDocument" type="RequiredDocumentTO" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:documentation>Required documents.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="RequiredDocumentGroupWithDeliveredDocumentsTO">
        <xsd:annotation>
            <xsd:documentation>Group of required documents, grouped by channel a product type</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="RequiredDocumentGroupTO">
                <xsd:sequence>
                    <xsd:element name="deliveredDocument" type="common:DeliveredDocumentTO" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:documentation>Required documents.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="fulfillmentStatus" type="FulfillmentStatusTO">
                        <xsd:annotation>
                            <xsd:documentation>Fulfillment status</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="RequiredDocumentGroupWithFulfillmentStatusTO">
        <xsd:annotation>
            <xsd:documentation>Group of required documents with fulfillment status</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="RequiredDocumentGroupTO">
                <xsd:sequence>
                    <xsd:element name="fulfillmentStatus" type="FulfillmentStatusTO">
                        <xsd:annotation>
                            <xsd:documentation>Fulfillment status</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="FulfillmentStatusTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="UNDELIVERED"/>
            <xsd:enumeration value="DELIVERED"/>
            <xsd:enumeration value="REJECTED"/>
            <xsd:enumeration value="PROCESSED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="RequiredDocumentTO">
        <xsd:annotation>
            <xsd:documentation>Record for a single required document</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="type">
                <xsd:annotation>
                    <xsd:documentation>Document type</xsd:documentation>
                </xsd:annotation>
                <xsd:simpleType>
                    <xsd:restriction base="xsd:string">
                        <xsd:minLength value="1"/>
                        <xsd:maxLength value="40"/>
                        <xsd:pattern value="[A-Z_]*"/>
                    </xsd:restriction>
                </xsd:simpleType>
            </xsd:element>
            <xsd:element name="count" type="xsd:int" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Document count</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="deliveredDocument" type="common:DeliveredDocumentTO" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Delivered document</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CreateOrUpdateRequiredDocumentGroupTO">
        <xsd:annotation>
            <xsd:documentation>Record for a single document group to create or update</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Required document group identifier. Null value means create, not-null means update.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="productType" type="common:ProductTypeTO"/>
            <xsd:element name="loanBinFrom" type="xsd:decimal" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Loan amount from which is document required.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="deliveryWay" type="common:DeliveryWayTO">
                <xsd:annotation>
                    <xsd:documentation>information about channel, that is allowed as delivery channel for required document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="groupRelation" type="common:GroupRelationTO" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>information about association to employment (for which income type is document required)</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="groupType" type="common:GroupTypeTO">
                <xsd:annotation>
                    <xsd:documentation>type of document group</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="groupCount" type="xsd:int" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Document count in group</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="documentType" type="common:DocumentTypeWithCountTO" maxOccurs="unbounded"/>
            <xsd:element name="optional" type="xsd:boolean"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="RequiredDocumentGroupWithoutRequiredDocumentsTO">
        <xsd:annotation>
            <xsd:documentation>Group of required groups without required documents</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="BaseRequiredDocumentGroupTO"/>
        </xsd:complexContent>
    </xsd:complexType>

</xsd:schema>
