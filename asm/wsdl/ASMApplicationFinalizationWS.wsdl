<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/asm/ws/application/finalization"
                  targetNamespace="http://airbank.cz/asm/ws/application/finalization">
    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/asm/ws/application/finalization">
            <xsd:include schemaLocation="../xsd/ApplicationFinalizationWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignOverInternetRequest">
        <wsdl:part element="InitSignOverInternetRequest" name="InitSignOverInternetRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverInternetResponse">
        <wsdl:part element="InitSignOverInternetResponse" name="InitSignOverInternetResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverInternetFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignOverInternetFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignOverInternetRequest">
        <wsdl:part element="UpdateSignOverInternetRequest" name="UpdateSignOverInternetRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverInternetResponse">
        <wsdl:part element="UpdateSignOverInternetResponse" name="UpdateSignOverInternetResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverInternetFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignOverInternetFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignOverMobileRequest">
        <wsdl:part element="InitSignOverMobileRequest" name="InitSignOverMobileRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverMobileResponse">
        <wsdl:part element="InitSignOverMobileResponse" name="InitSignOverMobileResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverMobileFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignOverMobileFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignOverMobileRequest">
        <wsdl:part element="UpdateSignOverMobileRequest" name="UpdateSignOverMobileRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverMobileResponse">
        <wsdl:part element="UpdateSignOverMobileResponse" name="UpdateSignOverMobileResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverMobileFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignOverMobileFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignAtBranchRequest">
        <wsdl:part element="InitSignAtBranchRequest" name="InitSignAtBranchRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchResponse">
        <wsdl:part element="InitSignAtBranchResponse" name="InitSignAtBranchResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignAtBranchFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignAtBranchRequest">
        <wsdl:part element="UpdateSignAtBranchRequest" name="UpdateSignAtBranchRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchResponse">
        <wsdl:part element="UpdateSignAtBranchResponse" name="UpdateSignAtBranchResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignAtBranchFault"/>
    </wsdl:message>

    <wsdl:message name="InitBlueSignRequest">
        <wsdl:part element="InitBlueSignRequest" name="InitBlueSignRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBlueSignResponse">
        <wsdl:part element="InitBlueSignResponse" name="InitBlueSignResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBlueSignFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBlueSignFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateBlueSignRequest">
        <wsdl:part element="UpdateBlueSignRequest" name="UpdateBlueSignRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBlueSignResponse">
        <wsdl:part element="UpdateBlueSignResponse" name="UpdateBlueSignResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBlueSignFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBlueSignFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignPadRequest">
        <wsdl:part element="InitSignPadRequest" name="InitSignPadRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignPadResponse">
        <wsdl:part element="InitSignPadResponse" name="InitSignPadResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignPadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignPadFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignPadRequest">
        <wsdl:part element="UpdateSignPadRequest" name="UpdateSignPadRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignPadResponse">
        <wsdl:part element="UpdateSignPadResponse" name="UpdateSignPadResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignPadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignPadFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignPadConfirmRequest">
        <wsdl:part element="InitSignPadConfirmRequest" name="InitSignPadConfirmRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignPadConfirmResponse">
        <wsdl:part element="InitSignPadConfirmResponse" name="InitSignPadConfirmResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignPadConfirmFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignPadConfirmFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignPadConfirmRequest">
        <wsdl:part element="UpdateSignPadConfirmRequest" name="UpdateSignPadConfirmRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignPadConfirmResponse">
        <wsdl:part element="UpdateSignPadConfirmResponse" name="UpdateSignPadConfirmResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignPadConfirmFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignPadConfirmFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignAtBranchByPwdRequest">
        <wsdl:part element="InitSignAtBranchByPwdRequest" name="InitSignAtBranchByPwdRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchByPwdResponse">
        <wsdl:part element="InitSignAtBranchByPwdResponse" name="InitSignAtBranchByPwdResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchByPwdFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignAtBranchByPwdFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignAtBranchByPwdRequest">
        <wsdl:part element="UpdateSignAtBranchByPwdRequest" name="UpdateSignAtBranchByPwdRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchByPwdResponse">
        <wsdl:part element="UpdateSignAtBranchByPwdResponse" name="UpdateSignAtBranchByPwdResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchByPwdFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignAtBranchByPwdFault"/>
    </wsdl:message>

    <wsdl:message name="InitSignAtBranchBySmsRequest">
        <wsdl:part element="InitSignAtBranchBySmsRequest" name="InitSignAtBranchBySmsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchBySmsResponse">
        <wsdl:part element="InitSignAtBranchBySmsResponse" name="InitSignAtBranchBySmsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchBySmsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignAtBranchBySmsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignAtBranchBySmsRequest">
        <wsdl:part element="UpdateSignAtBranchBySmsRequest" name="UpdateSignAtBranchBySmsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchBySmsResponse">
        <wsdl:part element="UpdateSignAtBranchBySmsResponse" name="UpdateSignAtBranchBySmsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchBySmsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignAtBranchBySmsFault"/>
    </wsdl:message>

    <wsdl:message name="GetInProgressApplicationsRequest">
        <wsdl:part element="GetInProgressApplicationsRequest" name="GetInProgressApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetInProgressApplicationsResponse">
        <wsdl:part element="GetInProgressApplicationsResponse" name="GetInProgressApplicationsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetInProgressApplicationsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetInProgressApplicationsFault"/>
    </wsdl:message>

    <wsdl:message name="InitDocumentUploadRequest">
        <wsdl:part element="InitDocumentUploadRequest" name="InitDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDocumentUploadResponse">
        <wsdl:part element="InitDocumentUploadResponse" name="InitDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDocumentUploadRequest">
        <wsdl:part element="UpdateDocumentUploadRequest" name="UpdateDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDocumentUploadResponse">
        <wsdl:part element="UpdateDocumentUploadResponse" name="UpdateDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="InitDeclarationRequest">
        <wsdl:part element="InitDeclarationRequest" name="InitDeclarationRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDeclarationResponse">
        <wsdl:part element="InitDeclarationResponse" name="InitDeclarationResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDeclarationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDeclarationFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDeclarationRequest">
        <wsdl:part element="UpdateDeclarationRequest" name="UpdateDeclarationRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeclarationResponse">
        <wsdl:part element="UpdateDeclarationResponse" name="UpdateDeclarationResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeclarationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDeclarationFault"/>
    </wsdl:message>

    <wsdl:message name="InitNoDocumentUploadRequest">
        <wsdl:part element="InitNoDocumentUploadRequest" name="InitNoDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="InitNoDocumentUploadResponse">
        <wsdl:part element="InitNoDocumentUploadResponse" name="InitNoDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="InitNoDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitNoDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateNoDocumentUploadRequest">
        <wsdl:part element="UpdateNoDocumentUploadRequest" name="UpdateNoDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateNoDocumentUploadResponse">
        <wsdl:part element="UpdateNoDocumentUploadResponse" name="UpdateNoDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateNoDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateNoDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="HasRequiredDocumentsRequest">
        <wsdl:part element="HasRequiredDocumentsRequest" name="HasRequiredDocumentsRequest"/>
    </wsdl:message>
    <wsdl:message name="HasRequiredDocumentsResponse">
        <wsdl:part element="HasRequiredDocumentsResponse" name="HasRequiredDocumentsResponse"/>
    </wsdl:message>
    <wsdl:message name="HasRequiredDocumentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="HasRequiredDocumentsFault"/>
    </wsdl:message>

    <wsdl:message name="NewContractActivatedRequest">
        <wsdl:part element="NewContractActivatedRequest" name="NewContractActivatedRequest"/>
    </wsdl:message>
    <wsdl:message name="NewContractActivatedResponse">
        <wsdl:part element="NewContractActivatedResponse" name="NewContractActivatedResponse"/>
    </wsdl:message>
    <wsdl:message name="NewContractActivatedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="NewContractActivatedFault"/>
    </wsdl:message>

    <wsdl:message name="GetCompletionIdForEnvelopeRequest">
        <wsdl:part element="GetCompletionIdForEnvelopeRequest" name="GetCompletionIdForEnvelopeRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCompletionIdForEnvelopeResponse">
        <wsdl:part element="GetCompletionIdForEnvelopeResponse" name="GetCompletionIdForEnvelopeResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCompletionIdForEnvelopeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCompletionIdForEnvelopeFault"/>
    </wsdl:message>

    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="ApplicationFinalization">
        <wsdl:operation name="Start">
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverInternet">
            <wsdl:input message="InitSignOverInternetRequest"/>
            <wsdl:output message="InitSignOverInternetResponse"/>
            <wsdl:fault name="InitSignOverInternetFault" message="InitSignOverInternetFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignOverInternet">
            <wsdl:input message="UpdateSignOverInternetRequest"/>
            <wsdl:output message="UpdateSignOverInternetResponse"/>
            <wsdl:fault name="UpdateSignOverInternetFault" message="UpdateSignOverInternetFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverMobile">
            <wsdl:input message="InitSignOverMobileRequest"/>
            <wsdl:output message="InitSignOverMobileResponse"/>
            <wsdl:fault name="InitSignOverMobileFault" message="InitSignOverMobileFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignOverMobile">
            <wsdl:input message="UpdateSignOverMobileRequest"/>
            <wsdl:output message="UpdateSignOverMobileResponse"/>
            <wsdl:fault name="UpdateSignOverMobileFault" message="UpdateSignOverMobileFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranch">
            <wsdl:input message="InitSignAtBranchRequest"/>
            <wsdl:output message="InitSignAtBranchResponse"/>
            <wsdl:fault name="InitSignAtBranchFault" message="InitSignAtBranchFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranch">
            <wsdl:input message="UpdateSignAtBranchRequest"/>
            <wsdl:output message="UpdateSignAtBranchResponse"/>
            <wsdl:fault name="UpdateSignAtBranchFault" message="UpdateSignAtBranchFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitBlueSign">
            <wsdl:input message="InitBlueSignRequest"/>
            <wsdl:output message="InitBlueSignResponse"/>
            <wsdl:fault name="InitBlueSignFault" message="InitBlueSignFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateBlueSign">
            <wsdl:input message="UpdateBlueSignRequest"/>
            <wsdl:output message="UpdateBlueSignResponse"/>
            <wsdl:fault name="UpdateBlueSignFault" message="UpdateBlueSignFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignPad">
            <wsdl:input message="InitSignPadRequest"/>
            <wsdl:output message="InitSignPadResponse"/>
            <wsdl:fault name="InitSignPadFault" message="InitSignPadFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignPad">
            <wsdl:input message="UpdateSignPadRequest"/>
            <wsdl:output message="UpdateSignPadResponse"/>
            <wsdl:fault name="UpdateSignPadFault" message="UpdateSignPadFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignPadConfirm">
            <wsdl:input message="InitSignPadConfirmRequest"/>
            <wsdl:output message="InitSignPadConfirmResponse"/>
            <wsdl:fault name="InitSignPadConfirmFault" message="InitSignPadConfirmFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignPadConfirm">
            <wsdl:input message="UpdateSignPadConfirmRequest"/>
            <wsdl:output message="UpdateSignPadConfirmResponse"/>
            <wsdl:fault name="UpdateSignPadConfirmFault" message="UpdateSignPadConfirmFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranchByPwd">
            <wsdl:input message="InitSignAtBranchByPwdRequest"/>
            <wsdl:output message="InitSignAtBranchByPwdResponse"/>
            <wsdl:fault name="InitSignAtBranchByPwdFault" message="InitSignAtBranchByPwdFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranchByPwd">
            <wsdl:input message="UpdateSignAtBranchByPwdRequest"/>
            <wsdl:output message="UpdateSignAtBranchByPwdResponse"/>
            <wsdl:fault name="UpdateSignAtBranchByPwdFault" message="UpdateSignAtBranchByPwdFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranchBySms">
            <wsdl:input message="InitSignAtBranchBySmsRequest"/>
            <wsdl:output message="InitSignAtBranchBySmsResponse"/>
            <wsdl:fault name="InitSignAtBranchBySmsFault" message="InitSignAtBranchBySmsFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranchBySms">
            <wsdl:input message="UpdateSignAtBranchBySmsRequest"/>
            <wsdl:output message="UpdateSignAtBranchBySmsResponse"/>
            <wsdl:fault name="UpdateSignAtBranchBySmsFault" message="UpdateSignAtBranchBySmsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetInProgressApplications">
            <wsdl:input message="GetInProgressApplicationsRequest"/>
            <wsdl:output message="GetInProgressApplicationsResponse"/>
            <wsdl:fault name="GetInProgressApplicationsFault" message="GetInProgressApplicationsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDocumentUpload">
            <wsdl:input message="InitDocumentUploadRequest"/>
            <wsdl:output message="InitDocumentUploadResponse"/>
            <wsdl:fault name="InitDocumentUploadFault" message="InitDocumentUploadFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateDocumentUpload">
            <wsdl:input message="UpdateDocumentUploadRequest"/>
            <wsdl:output message="UpdateDocumentUploadResponse"/>
            <wsdl:fault name="UpdateDocumentUploadFault" message="UpdateDocumentUploadFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDeclaration">
            <wsdl:input message="InitDeclarationRequest"/>
            <wsdl:output message="InitDeclarationResponse"/>
            <wsdl:fault name="InitDeclarationFault" message="InitDeclarationFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateDeclaration">
            <wsdl:input message="UpdateDeclarationRequest"/>
            <wsdl:output message="UpdateDeclarationResponse"/>
            <wsdl:fault name="UpdateDeclarationFault" message="UpdateDeclarationFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitNoDocumentUpload">
            <wsdl:input message="InitNoDocumentUploadRequest"/>
            <wsdl:output message="InitNoDocumentUploadResponse"/>
            <wsdl:fault name="InitNoDocumentUploadFault" message="InitNoDocumentUploadFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateNoDocumentUpload">
            <wsdl:input message="UpdateNoDocumentUploadRequest"/>
            <wsdl:output message="UpdateNoDocumentUploadResponse"/>
            <wsdl:fault name="UpdateNoDocumentUploadFault" message="UpdateNoDocumentUploadFault"/>
        </wsdl:operation>

        <wsdl:operation name="HasRequiredDocuments">
            <wsdl:input message="HasRequiredDocumentsRequest"/>
            <wsdl:output message="HasRequiredDocumentsResponse"/>
            <wsdl:fault name="HasRequiredDocumentsFault" message="HasRequiredDocumentsFault"/>
        </wsdl:operation>

        <wsdl:operation name="NewContractActivated">
            <wsdl:documentation>General contract activation message from OBS</wsdl:documentation>
            <wsdl:input message="NewContractActivatedRequest"/>
            <wsdl:output message="NewContractActivatedResponse"/>
            <wsdl:fault name="NewContractActivatedFault" message="NewContractActivatedFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCompletionIdForEnvelope">
            <wsdl:input message="GetCompletionIdForEnvelopeRequest"/>
            <wsdl:output message="GetCompletionIdForEnvelopeResponse"/>
            <wsdl:fault name="GetCompletionIdForEnvelopeFault" message="GetCompletionIdForEnvelopeFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="ApplicationFinalizationBinding" type="ApplicationFinalization">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverInternet">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignOverInternetFault">
                <soap:fault name="InitSignOverInternetFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignOverInternet">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignOverInternetFault">
                <soap:fault name="UpdateSignOverInternetFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverMobile">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignOverMobileFault">
                <soap:fault name="InitSignOverMobileFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignOverMobile">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignOverMobileFault">
                <soap:fault name="UpdateSignOverMobileFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranch">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignAtBranchFault">
                <soap:fault name="InitSignAtBranchFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranch">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignAtBranchFault">
                <soap:fault name="UpdateSignAtBranchFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitBlueSign">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBlueSignFault">
                <soap:fault name="InitBlueSignFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateBlueSign">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBlueSignFault">
                <soap:fault name="UpdateBlueSignFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignPad">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignPadFault">
                <soap:fault name="InitSignPadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignPad">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignPadFault">
                <soap:fault name="UpdateSignPadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignPadConfirm">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignPadConfirmFault">
                <soap:fault name="InitSignPadConfirmFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignPadConfirm">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignPadConfirmFault">
                <soap:fault name="UpdateSignPadConfirmFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranchByPwd">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignAtBranchByPwdFault">
                <soap:fault name="InitSignAtBranchByPwdFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranchByPwd">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignAtBranchByPwdFault">
                <soap:fault name="UpdateSignAtBranchByPwdFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranchBySms">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignAtBranchBySmsFault">
                <soap:fault name="InitSignAtBranchBySmsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranchBySms">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignAtBranchBySmsFault">
                <soap:fault name="UpdateSignAtBranchBySmsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetInProgressApplications">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetInProgressApplicationsFault">
                <soap:fault name="GetInProgressApplicationsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDocumentUpload">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDocumentUploadFault">
                <soap:fault name="InitDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDocumentUpload">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDocumentUploadFault">
                <soap:fault name="UpdateDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDeclaration">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDeclarationFault">
                <soap:fault name="InitDeclarationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDeclaration">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDeclarationFault">
                <soap:fault name="UpdateDeclarationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitNoDocumentUpload">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitNoDocumentUploadFault">
                <soap:fault name="InitNoDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateNoDocumentUpload">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateNoDocumentUploadFault">
                <soap:fault name="UpdateNoDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HasRequiredDocuments">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HasRequiredDocumentsFault">
                <soap:fault name="HasRequiredDocumentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="NewContractActivated">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="NewContractActivatedFault">
                <soap:fault name="NewContractActivatedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCompletionIdForEnvelope">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCompletionIdForEnvelopeFault">
                <soap:fault name="GetCompletionIdForEnvelopeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="ApplicationFinalizationWS">
        <wsdl:port name="ApplicationFinalizationPort" binding="ApplicationFinalizationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/asm/application/finalization"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
