<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns="http://airbank.cz/asm/ws/applicant"
            targetNamespace="http://airbank.cz/asm/ws/applicant"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:include schemaLocation="Applicant.xsd"/>

    <xsd:element name="UpdateCustomerApplicantRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="applicationId" type="xsd:long"/>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="birthNumber" type="xsd:string" minOccurs="0"/>
                <xsd:element name="quintupleIdentity" type="QuintupleIdentityTO" minOccurs="0"/>
                <xsd:element name="honourBefore" type="xsd:string" minOccurs="0"/>
                <xsd:element name="honourAfter" type="xsd:string" minOccurs="0"/>
                <xsd:element name="gender" type="xsd:string" minOccurs="0"/>
                <xsd:element name="citizenship" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="address" type="AddressTO" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="contact" type="ContactTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateCustomerApplicantResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="FindApplicantRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="FindApplicantFilterTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="FindApplicantResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="applicant" type="ApplicantInfoTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="FindApplicantFilterTO">
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long" minOccurs="0"/>
            <xsd:element name="completionId" type="xsd:long" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

</xsd:schema>
