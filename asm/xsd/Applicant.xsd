<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns="http://airbank.cz/asm/ws/applicant"
            targetNamespace="http://airbank.cz/asm/ws/applicant"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:complexType name="QuintupleIdentityTO">
        <xsd:sequence>
            <xsd:element name="firstName" type="xsd:string" minOccurs="0"/>
            <xsd:element name="lastName" type="xsd:string" minOccurs="0"/>
            <xsd:element name="birthDate" type="xsd:date" minOccurs="0"/>
            <xsd:element name="birthPlace" type="BirthPlaceTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BirthPlaceTO">
        <xsd:sequence>
            <xsd:element name="discriminator" type="xsd:string"/>
            <xsd:choice>
                <xsd:sequence>
                    <xsd:element name="townCode" type="xsd:long"/>
                </xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="pragueMunicipalDistrictCode" type="xsd:long"/>
                </xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="country" type="xsd:string"/>
                    <xsd:element name="location" type="xsd:string"/>
                </xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="place" type="xsd:string"/>
                </xsd:sequence>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AddressTO">
        <xsd:sequence>
            <xsd:element name="role" type="xsd:string"/>
            <xsd:element name="country" type="xsd:string" minOccurs="0"/>
            <xsd:element name="town" type="xsd:string" minOccurs="0"/>
            <xsd:element name="street" type="xsd:string" minOccurs="0"/>
            <xsd:element name="houseNum" type="xsd:string" minOccurs="0"/>
            <xsd:element name="zip" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactTO">
        <xsd:sequence>
            <xsd:element name="role" type="xsd:string"/>
            <xsd:element name="callingCode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="contactValue" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ApplicantInfoTO">
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0"/>
            <xsd:element name="birthNumber" type="xsd:string" minOccurs="0"/>
            <xsd:element name="quintupleIdentity" type="QuintupleIdentityTO" minOccurs="0"/>
            <xsd:element name="citizenship" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="address" type="AddressTO" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="identificationDocument" type="IdentificationDocumentTO" minOccurs="0"/>
            <xsd:element name="gcApplicationProcessType" type="ProcessTypeTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IdentificationDocumentTO">
        <xsd:sequence>
            <xsd:element name="documentType" type="xsd:string"/>
            <xsd:element name="documentNumber" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ProcessTypeTO">
        <xsd:annotation>
            <xsd:documentation>Type of application process.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="STANDARD" />
            <xsd:enumeration value="SHORT" />
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
