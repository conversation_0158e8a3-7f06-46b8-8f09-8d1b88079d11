<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns="http://airbank.cz/asm/ws/application/disponent"
            targetNamespace="http://airbank.cz/asm/ws/application/disponent"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xsd:include schemaLocation="DisponentApplication.xsd"/>

    <xsd:element name="StartRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerApplicantCuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>CUID of customer applicant</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="idProfile" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            profile identification (id of relation between subject and general contract in OBS)
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StartResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:StartResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitAffidavitRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitAffidavitResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="consent" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Indicates whether client confirmed the affidavit.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedEligiblePersonCuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Selected eligible person CUID.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="eligiblePerson" type="appCommon:PersonTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of persons eligible to become disponent.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateAffidavitRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="consent" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Indicates whether client confirmed the affidavit.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedEligiblePersonCuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Selected eligible person CUID.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateAffidavitResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitBasicParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitBasicParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="personName" type="appCommon:PersonNameOutputTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Person name data.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="personContact" type="appCommon:PersonContactOutputTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Person contact data.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateBasicParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="personName" type="appCommon:PersonNameInputTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Person name data.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="personContact" type="appCommon:PersonContactInputTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Person contact data.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateBasicParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitPersonalParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitPersonalParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="citizenship" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Citizenship.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentType" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Identification document type.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Identification document number.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentIssuedBy" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Authority which issued the ID document.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentIssueDate" type="xsd:date" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Issue date of the ID document.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="birthNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Birth number.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="gender" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Gender.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="birthDate" type="xsd:date" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Birth date.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="birthPlace" type="appCommon:BirthPlaceTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Birth place.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="permanentAddress" type="appCommon:AddressTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client permanent address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client mailing address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddressSameAsPermanent" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Flag indicating that mailing address is same as permanent address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdatePersonalParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="citizenship" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Citizenship.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentType" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Identification document type.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Identification document number.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentIssuedBy" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Authority which issued the ID document.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentIssueDate" type="xsd:date" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Issue date of the ID document.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="birthNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Birth number.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="gender" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Gender.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="birthDate" type="xsd:date" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Birth date.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="birthPlace" type="appCommon:BirthPlaceTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Birth place.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="permanentAddress" type="appCommon:AddressTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client permanent address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client mailing address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddressSameAsPermanent" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Flag indicating that mailing address is same as permanent address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="permanentAddressConfirmedByClient" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Indicates that client confirmed that address is valid (in case the address or its certain attributes are not found in
                            address registers).
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddressConfirmedByClient" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Indicates that client confirmed that address is valid (in case the address or its certain attributes are not found in
                            address registers).
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdatePersonalParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitRightsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitRightsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="account" type="appCommon:AccountTO" maxOccurs="unbounded" />
                <xsd:element name="accountAccessRight" type="AccountRightTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of disponent rights to the client accounts</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="grantAllRights" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Indicates that client granted all rights to the disponent.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentOrganizerRightGranted" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Indicates that client granted document organizer right to the disponent.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="showDocumentOrganizerRight" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Flag whether to show document organizer right.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateRightsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="accountAccessRight" type="AccountRightTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of disponent rights to the client accounts</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="grantAllRights" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Indicates that client granted all rights to the disponent.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="documentOrganizerRightGranted" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Indicates that client granted document organizer right to the disponent.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateRightsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitLoginRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitLoginResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="username" type="appCommon:UsernameTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client's username</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="usernameReadOnly" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Flag whether username is read only.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="existingPersonWithSecurityElements" type="xsd:boolean"/>
                <xsd:element name="emailAsUsername" type="appCommon:EmailTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client's primary email. When set, it can be used as username.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="hasDuplicateEmail" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>True when client's primary email is marked as duplicate.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="hasDuplicatePhone" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>True when client's primary phone is marked as duplicate.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="sameEmailRequired" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>True when client's primary email is required to be same as theirs email in CIF.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="samePhoneRequired" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>True when client's primary phone is required to be same as theirs email in CIF.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="personContact" type="appCommon:PersonContactOutputTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client contact information.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateLoginRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="username" type="appCommon:UsernameTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client's username</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="personContact" type="appCommon:PersonContactInputTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client contact information.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="emailDuplicityConfirmedByClient" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Indicates that client confirmed that it is OK that given email address is being used by some other bank's
                            client already
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateLoginResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
                <xsd:element name="lopiToken" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
