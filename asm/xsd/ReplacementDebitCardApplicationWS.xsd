<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns="http://airbank.cz/asm/ws/application/replacementdebitcard"
            xmlns:debit="http://airbank.cz/asm/ws/application/debitcard"
            targetNamespace="http://airbank.cz/asm/ws/application/replacementdebitcard"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:annotation>
        <xsd:documentation>
            Common types of application interface
        </xsd:documentation>
    </xsd:annotation>
    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xsd:import namespace="http://airbank.cz/asm/ws/application/debitcard" schemaLocation="DebitCardApplication.xsd"/>

    <xsd:element name="StartRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerApplicantCuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>CUID of customer applicant</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="idProfile" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            profile identification (id of relation between subject and general contract in OBS)
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedCardId" type="xsd:long" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Id of selected card.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StartResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:StartResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitReplacementDebitCardRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitReplacementDebitCardResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="account" type="appCommon:AccountTO" minOccurs="1" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of available accounts</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="card" type="appCommon:CardTO" minOccurs="1" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of debit cards.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedAccount" type="appCommon:AccountTO" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Selected account</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedCard" type="appCommon:CardTO" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Selected card</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateReplacementDebitCardRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="selectedAccountId" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Id of selected account.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedCardId" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Id of selected card.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateReplacementDebitCardResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitReplacementCardParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitReplacementCardParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Default mailing address where to send card.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="cardDesignOption" type="appCommon:CardDesignTO" minOccurs="1" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of possible designs.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="embossedName" type="xsd:string" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Embossed name on debit card.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="legalEntityEmbossedName" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Legal entity embossed name on debit card.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="selectedCardDesignId" type="xsd:long" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Selected card design.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="deliveryAddressSameAsMailing" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Indicates if card delivery is to default or custom address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="customDeliveryAddress" type="appCommon:AddressTO" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Custom delivery address. Used if deliveryAddressSameAsMailing is false.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddressRole" type="debit:MailingAddressRoleTO">
                    <xsd:annotation>
                        <xsd:documentation>Role of mailing address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateReplacementCardParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="selectedCardDesignId" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Selected card design.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="deliveryAddressSameAsMailing" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Indicates if card delivery is to default or custom address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="customDeliveryAddress" type="appCommon:AddressTO" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Custom delivery address. Used if deliveryAddressSameAsMailing is false.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateReplacementCardParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
                <xsd:element name="lopiToken" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>