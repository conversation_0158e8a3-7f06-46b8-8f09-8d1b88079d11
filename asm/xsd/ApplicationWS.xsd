<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns:fs="http://airbank.cz/asm/ws/application/expression"
            xmlns:adc="http://airbank.cz/asm/ws/approvaldata/common"
            xmlns="http://airbank.cz/asm/ws/application"
            targetNamespace="http://airbank.cz/asm/ws/application"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xsd:import namespace="http://airbank.cz/asm/ws/application/expression" schemaLocation="Filter.xsd"/>
    <xsd:import namespace="http://airbank.cz/asm/ws/approvaldata/common" schemaLocation="ApprovalData.xsd"/>

    <xsd:element name="FindApplicationsRequest">
        <xsd:complexType>
            <xsd:annotation>
                <xsd:documentation>
                    Finds any applications according to given filter
                    Notes:
                    FindApplications returns all applications according to given filter.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="filter" type="fs:SelectFilter"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="FindApplicationsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element maxOccurs="unbounded" minOccurs="0" name="application" type="appCommon:Application"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="SetBranchCodeRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Applicant cuid</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="branchCode" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Branch code</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetBranchCodeResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Optional list of affected envelopes</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="FindGCApplicationsForCairActivationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Customer ID.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationStatus" type="adc:ApplicationStatus" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of allowed application statuses.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="FindGCApplicationsForCairActivationResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="application" type="appCommon:ContractApplicationForCairTO"  minOccurs="0" maxOccurs="unbounded" >
                    <xsd:annotation>
                        <xsd:documentation>Contract application data for activation in CAir.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="FindGCApplicationsForCairBySessionUuidRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="applicationFinalizationSessionUuid" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>UUID returned at the beginning of application finalization.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="personalDataSessionUuid" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Personal data session UUID returned after personal data confirmation.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationStatus" type="adc:ApplicationStatus" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>List of allowed application statuses.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="operatorId" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Operator ID.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="FindGCApplicationsForCairBySessionUuidResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="application" type="appCommon:ContractApplicationForCairTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Contract application data for activation in CAir.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="validationResult" type="appCommon:FindGCApplicationsForCairValidationResultTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Validation result.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetApplicationDetailByFilterRequest">
        <xsd:complexType>
            <xsd:annotation>
                <xsd:documentation>
                    Finds any applications according to given filter
                    Notes:
                    FindApplications returns all applications according to given filter.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="filter" type="fs:SelectFilter"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetApplicationDetailByFilterResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="applicationDetail" type="appCommon:ApplicationDetail" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
