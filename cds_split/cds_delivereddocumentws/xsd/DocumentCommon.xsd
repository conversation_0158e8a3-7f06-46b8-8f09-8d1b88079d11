<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns="http://airbank.cz/cds/ws/common"
            targetNamespace="http://airbank.cz/cds/ws/common"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:complexType name="AuditedEntityTO" abstract="true">
        <xsd:annotation>
            <xsd:documentation>
                Parent of objects with common audit data.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>PK in database, can be NULL (new unsaved object)</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="createdTime" type="xsd:dateTime" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Time of the object creation in DB</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="version" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Version of the object in DB</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="modifyUserId" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Cuid of user who modified this record.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="modifyManagerId" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>LDAP employee number of operator who modified this record.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="modifyTime" type="xsd:dateTime" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Time of the record update.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="deleted" type="xsd:boolean">
                <xsd:annotation>
                    <xsd:documentation>Flag indicating whether is the record deleted or not.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ProductTypeTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="LOAN"/>
            <xsd:enumeration value="DEPOSIT"/>
            <xsd:enumeration value="CONSOLIDATION"/>
            <xsd:enumeration value="AFFIDAVIT"/>
            <xsd:enumeration value="CONTRACT_SUPPLEMENT"/>
            <xsd:enumeration value="MORTGAGE"/>
            <xsd:enumeration value="OVERDRAFT"/>
            <xsd:enumeration value="PENSION"/>
            <xsd:enumeration value="INSURANCE"/>
            <xsd:enumeration value="SPLITPAYMENT"/>
            <xsd:enumeration value="INVESTMENT"/>
            <xsd:enumeration value="STOCK_ETF"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="GroupTypeTO">
        <xsd:annotation>
            <xsd:documentation>document group, enumeration.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ADDRESS"/>
            <xsd:enumeration value="BUSINESS"/>
            <xsd:enumeration value="FINANCIAL"/>
            <xsd:enumeration value="IDENTIFICATION"/>
            <xsd:enumeration value="JOINT_ASSETS"/>
            <xsd:enumeration value="MOBILE_PHONE"/>
            <xsd:enumeration value="NODEBT"/>
            <xsd:enumeration value="OTHER"/>
            <xsd:enumeration value="REMOTE_IDENTIFICATION"/>
            <xsd:enumeration value="CO_DEBTORS"/>
            <xsd:enumeration value="MORTGAGE_LOAN"/>
            <xsd:enumeration value="MORTGAGE_LOAN_VALUATION"/>
            <xsd:enumeration value="OBLIGATION"/>
            <xsd:enumeration value="INSURANCE"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="GroupRelationTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PRIMARY"/>
            <xsd:enumeration value="SECONDARY"/>
            <xsd:enumeration value="TERTIARY"/>
            <xsd:enumeration value="MAIN_INCOME"/>
            <xsd:enumeration value="OTHER_INCOME"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="DeliveryWayTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="IB"/>
            <xsd:enumeration value="BRANCH"/>
            <xsd:enumeration value="POST"/>
            <xsd:enumeration value="MESSENGER"/>
            <xsd:enumeration value="ICC"/>
            <xsd:enumeration value="SPB"/>
        </xsd:restriction>
    </xsd:simpleType>
    
    <xsd:complexType name="DocumentTypeWithCountTO">
        <xsd:annotation>
            <xsd:documentation>
                Document type with count of documents
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="documentType" type="xsd:string"/>
            <xsd:element name="documentCount" type="xsd:int"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="DocumentStatusTO">
        <xsd:annotation>
            <xsd:documentation>stavy dokladů a zpracování</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="RECEIVED">
                <xsd:annotation>
                    <xsd:documentation>Přijatý (Neověřený)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VERIFIED_NOK">
                <xsd:annotation>
                    <xsd:documentation>Ověřený NOK : pokud je nějaký problém s přílohou dokladu (nekvalitní scan, chybí zadní strana, atd.), klient dohraje
                        chybějící obrázek
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VERIFIED_OK">
                <xsd:annotation>
                    <xsd:documentation>Zpracovaný (Ověřený OK) : doklad je v pořádku, proběhlo zavstupování a schválení dokladu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="REJECTED">
                <xsd:annotation>
                    <xsd:documentation>Zamítnutý : Doklad byl zamítnutý, konečný stav, klient musí nahrát novou přílohu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CANCELED">
                <xsd:annotation>
                    <xsd:documentation>Stornovaný</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="DocumentStatusOriginTO">
        <xsd:annotation>
            <xsd:documentation>Původ stavu dokladu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="AISP">
                <xsd:annotation>
                    <xsd:documentation>Automatické zpracování na základě dodání dokladu přes AISP</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OCR">
                <xsd:annotation>
                    <xsd:documentation>Automatické zpracování s OCR</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MANUAL">
                <xsd:annotation>
                    <xsd:documentation>Ruční spuštění</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AIS_EOP">
                <xsd:annotation>
                    <xsd:documentation>Agendový Informační Systém Evidence Občanských Průkazů</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="DocumentSourceTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CLIENT"/>
            <xsd:enumeration value="AISP"/>
            <xsd:enumeration value="AB"/>
            <xsd:enumeration value="AIS_EOP">
                <xsd:annotation>
                    <xsd:documentation>Agendový Informační Systém Evidence Občanských Průkazů</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="DocumentMigrationFlagTO">
        <xsd:annotation>
            <xsd:documentation>document flag related to migration of id a validTo</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PROOF">
                <xsd:annotation>
                    <xsd:documentation>data mined from proof</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RISK_ESTIMATE">
                <xsd:annotation>
                    <xsd:documentation>data mined from application and the validTo is computed by risk algorithm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MANUAL_PROCESS">
                <xsd:annotation>
                    <xsd:documentation>data mining unsuccessfull, so the data will be added by the manual process</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MANUAL_PROCESS_CANDIDATE">
                <xsd:annotation>
                    <xsd:documentation>candidate to get data by the manual process</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INACTIVE_CLIENT">
                <xsd:annotation>
                    <xsd:documentation>client has no relation to bank</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ArchiveStatusTO">
        <xsd:annotation>
            <xsd:documentation>stavy dokladů a zpracování</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="IN_REGISTRY">
                <xsd:annotation>
                    <xsd:documentation>Ve spisovne</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXT_REGISTRY">
                <xsd:annotation>
                    <xsd:documentation>V externí spisovně (nepoužívá se)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BORROWED">
                <xsd:annotation>
                    <xsd:documentation>Zapůjčený</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="DocumentAttachmentTO">
        <xsd:sequence>
            <xsd:element name="dmsId" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>id of document in DMS</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="deleted" type="xsd:boolean" default="false">
                <xsd:annotation>
                    <xsd:documentation>Is the document deleted? We want to display also deleted documents. The default value is false.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="anonymized" type="xsd:boolean" default="false">
                <xsd:annotation>
                    <xsd:documentation>Is the document anonymised? The default value is false.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="DeliveryChannelTO">
        <xsd:annotation>
            <xsd:documentation>
                IB - Internetové bankovnictví
                POST - Pošta
                MESSENGER - Kurýr
                BRANCH - Pobočka
                EMAIL Jiný e-mail
                EMAILPR - Primární e-mail Majitele RS
                EMAILDP - Primární e-mail Disponenta
                EMAILCH - Primární e-mail Držitele
                REGISTER - Registry
                SPB - smart phone banking - mobilni aplikace
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="BRANCH"/>
            <xsd:enumeration value="EMAIL"/>
            <xsd:enumeration value="EMAILCH"/>
            <xsd:enumeration value="EMAILDP"/>
            <xsd:enumeration value="EMAILPR"/>
            <xsd:enumeration value="IB"/>
            <xsd:enumeration value="MESSENGER"/>
            <xsd:enumeration value="POST"/>
            <xsd:enumeration value="REGISTER"/>
            <xsd:enumeration value="SPB"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="AbstractDeliveredDocumentTO" abstract="true">
        <xsd:complexContent>
            <xsd:extension base="AuditedEntityTO">
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long">
                        <xsd:annotation>
                            <xsd:documentation>CUID</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="deliveryDate" type="xsd:dateTime" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>when the document was delivered</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="documentType" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>document type in CIF</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="documentName" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>document description</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="issueDate" type="xsd:date" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Date of document issue</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="proofId" type="xsd:long" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Vazba na IDProof v RDR</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="status" type="DocumentStatusTO">
                        <xsd:annotation>
                            <xsd:documentation>status of document</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="statusOrigin" type="DocumentStatusOriginTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>origin of document status</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="archiveStatus" type="ArchiveStatusTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>status of document</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="operatorNote" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>comment by operator - call center / ICC / ECC etc</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="branchOfficerNote" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>comment pobocnika</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="internalNote" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>comment internal</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="deliveryChannel" type="DeliveryChannelTO">
                        <xsd:annotation>
                            <xsd:documentation>delivery channel by which the document was delivered</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="rejectReason" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>document rejection reason in MDM</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cancelReason" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>document cancel reason in MDM</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="barCode" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>search code</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="uploaded" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is uploaded?</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="onlyElectronicVersion" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is only electronic version?</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="isVirtual" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is virtual version of document?</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="notFound" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>not found?</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="discarded" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is discarded?</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="discardedDate" type="xsd:date" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Date of discarding. It is filled when we set discarded to true</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="archiveNumber" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>archive number</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="lentToOperatorId" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>LDAP id of person who was it lent to</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="mergedTo" type="xsd:long" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Merged to document ID</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="lockedFor" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Document is locked for specified operator</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="source" type="DocumentSourceTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>source of document (read-only field, value is set by backend)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="useAsLegalBindingDocument" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>use document as legal binding document</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="DeliveredDocumentTO">
        <xsd:complexContent>
            <xsd:extension base="AbstractDeliveredDocumentTO">
                <xsd:sequence>
                    <xsd:element name="migrationFlag" type="DocumentMigrationFlagTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>migration flag of document</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="attachment" type="DocumentAttachmentTO" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:documentation>document attachments</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="identityInfo" type="IdentityInfoTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Special attributes for identification documents</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="incomeInfo" type="IncomeInfoTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Special attributes for income documents</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="studentConfirmationInfo" type="StudentConfirmationInfoTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Special attributes for student confirmation documents</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="IdentityInfoTO">
        <xsd:complexContent>
            <xsd:extension base="AuditedEntityTO">
                <xsd:sequence>
                    <xsd:element name="documentNumber" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Identity document ID filled by operator</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="validTo" type="xsd:date" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Date of document validity</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="country" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Country of origin (CIF codelist)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="maritalStatus" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Marital status (CIF codelist)</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="code" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Code check / validity check</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="invalidationDate" type="xsd:date" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Date of document invalidation</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="facePictureDmsId" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>DMS ID (uuid) of a face image if any was found on the document.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="issuer" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Issuer name.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="IncomeInfoTO">
        <xsd:complexContent>
            <xsd:extension base="AuditedEntityTO">
                <xsd:sequence>
                    <xsd:element name="inExecution" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is in confiscation</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="inTermination" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is in termination</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="inProbationPeriod" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>is in probation period</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="last12MAvgNetIncome" type="xsd:decimal" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Průměrný čistý měsíční příjem za posledních 12 M.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="last1MGamblingAmount" type="xsd:decimal" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Amount of transactions categorized as gambling for last month</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="last3MGamblingAmount" type="xsd:decimal" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Amount of transactions categorized as gambling for last three months</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="lastUnemploymentBenefitDate" type="xsd:date" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>date of last unemployment benefit</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="incomePairedAutomatically" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>flag whether income was automatically paired to application data</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="StudentConfirmationInfoTO">
        <xsd:complexContent>
            <xsd:extension base="AuditedEntityTO">
                <xsd:sequence>
                    <xsd:element name="ic" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>identification number of university.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="faculty" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>faculty of university.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="grade" type="xsd:int" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>current grade of study.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="isicNumber" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>isic number</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="ValidationResultTO">
        <xsd:annotation>
            <xsd:documentation>Single validation result.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Validation error code (specific codes must be specified for each operation).</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="attributeName" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Attribute name, that caused the validation error</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PagingAndSortingTO">
        <xsd:annotation>
            <xsd:documentation>Object with data for pagination and sorting</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="itemCount" type="xsd:int" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Paging: requested count per page</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="offset" type="xsd:int" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Paging: requested number of record in ordered records</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="maxCount" type="xsd:int" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Paging: max total count of results</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="sortBy" type="SortByTO" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>List of sort by attributes</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="SortByTO">
        <xsd:annotation>
            <xsd:documentation>Sort by string when searching results.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="sortType" type="xsd:string" minOccurs="0"/>
            <xsd:element name="order" type="SortOrderTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="SortOrderTO">
        <xsd:annotation>
            <xsd:documentation>Order of sorting when searching results.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ASC"/>
            <xsd:enumeration value="DESC"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
