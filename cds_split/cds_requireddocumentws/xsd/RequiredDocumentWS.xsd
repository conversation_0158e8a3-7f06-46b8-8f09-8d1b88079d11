<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns="http://airbank.cz/cds/ws/requireddocumentws"
            xmlns:document="http://airbank.cz/cds/ws/requireddocument"
            targetNamespace="http://airbank.cz/cds/ws/requireddocumentws"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import schemaLocation="RequiredDocument.xsd" namespace="http://airbank.cz/cds/ws/requireddocument"/>

    <xsd:element name="GetRequiredDocumentGroupsHistoryRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsHistoryResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requiredDocumentGroup" type="document:RequiredDocumentGroupHistoryTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HasRequiredDocumentGroupsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="document:RequiredDocumentGroupFilterTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HasRequiredDocumentGroupsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="hasRequiredDocumentGroups" type="xsd:boolean"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="document:RequiredDocumentGroupFilterTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requiredDocumentGroup" type="document:RequiredDocumentGroupTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsWithFulfillmentStatusRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="document:RequiredDocumentGroupFilterTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsWithFulfillmentStatusResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requiredDocumentGroup" type="document:RequiredDocumentGroupWithFulfillmentStatusTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsWithDeliveredDocumentsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="document:RequiredDocumentGroupFilterTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetRequiredDocumentGroupsWithDeliveredDocumentsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requiredDocumentGroup" type="document:RequiredDocumentGroupWithDeliveredDocumentsTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="RemoveRequiredDocumentGroupsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="RemoveRequiredDocumentGroupsResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="CreateOrUpdateRequiredDocumentGroupsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long"/>
                <xsd:element name="requiredDocumentGroup" type="document:CreateOrUpdateRequiredDocumentGroupTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="CreateOrUpdateRequiredDocumentGroupsResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
