spring.main.banner-mode=off

spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.show-sql=true
spring.sql.init.mode=always
spring.datasource.hikari.schema=cdm
spring.datasource.hikari.minimumIdle=2

spring.datasource.hikari.data-source-properties.reWriteBatchedInserts=true

agent.keepaliveInterval=300000
agent.monitorPrintInterval=300000

agent.insert.batch.size=10000
agent.insert.list.size=50000

spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=pw

ci360.gatewayHost=extapigwservice-training.ci360.sas.com
ci360.tenantID=
ci360.clientSecret=