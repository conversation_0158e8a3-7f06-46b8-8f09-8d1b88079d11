### Configuration File for ESP Agent
#
# Author: <PERSON>
# Version: 1.2111.1
# last update: 3rd Nov 2021
# 
# Description: This custom CI360 agent can stream events into ESP 
#
# >>> Streaming to ESP <<<
# the ESP source window needs to have a schema with attributes that represent the event attributes from CI360 events
# those attributes need to have exactly the same name like CI360 event attributes
#

# CI360 Settings
ci360.gatewayHost   = extapigwservice-demo.cidemo.sas.com
ci360.tenantID      = 
ci360.clientSecret  = 
ci360.identity      = datahub_id

# ESP Settings
esp.host          = sasserver.demo.sas.com
esp.port          = 3389
esp.urlParameters = ?value=injected&blocksize=1&quiesce=false
esp.Project       = CI360_Event_Stream
esp.Query         = eventstream
esp.Window        = event_source

# Custom Agent settings
agent.keepaliveInterval = 60000
