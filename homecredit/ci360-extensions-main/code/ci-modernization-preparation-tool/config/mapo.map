<?xml version="1.0" encoding="windows-1252"?>
<!-- ############################################################ -->
<!-- 2016-08-26T02:16:38 -->
<!-- SAS XML Libname Engine Map -->
<!-- Generated by XML Mapper, 904300.0.0.20150204190000_v940m3 -->
<!-- ############################################################ -->
<!-- ###  Validation report                                   ### -->
<!-- ############################################################ -->
<!-- Column (campaign_id) in table (NodeRelation) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (node_id) in table (NodeRelation) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (Node) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (LinkNode) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (CommunicationLineItem) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (_nodeId) in table (CommunicationLineItem) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (_nodeName) in table (CommunicationLineItem) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (MultiSelectVar) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (_nodeId) in table (MultiSelectVar) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (_nodeName) in table (MultiSelectVar) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (SelectNodeVar) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (CodeNode) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (ABTestNodeVars) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (LimitNodeVars) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (PrioritizeNodeVars) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (MapNode) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (ExportNodeVars) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- Column (campaign_id) in table (SplitNode) has an XPath outside the scope of the table path. The contents of this column may not correspond to other row values and/or may be missing entirely. -->
<!-- XMLMap validation completed successfully. -->
<!-- ############################################################ -->
<SXLEMAP name="MAPOMAP" version="2.1">

    <NAMESPACES count="0"/>

    <!-- ############################################################ -->
    <TABLE name="Campaign">
        <TABLE-PATH syntax="XPath">/PersistenceDO</TABLE-PATH>

        <COLUMN name="id">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_code">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CampaignDO/properties/_code</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_versionNumber">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_versionNumber</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>4</LENGTH>
        </COLUMN>

        <COLUMN name="_name">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_name</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="flow_id">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_flowType">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_flowType</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_owner">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_owner</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_businessContextId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_businessContextId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_businessContextId1">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CampaignDO/properties/_businessContextId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_creationDate">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_creationDate</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>datetime</DATATYPE>
            <FORMAT width="19">IS8601DT</FORMAT>
            <INFORMAT width="19">IS8601DT</INFORMAT>
        </COLUMN>

        <COLUMN name="_lastModDate">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_lastModDate</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>datetime</DATATYPE>
            <FORMAT width="19">IS8601DT</FORMAT>
            <INFORMAT width="19">IS8601DT</INFORMAT>
        </COLUMN>

        <COLUMN name="_lastRunDate">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_lastRunDate</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>datetime</DATATYPE>
            <FORMAT width="19">IS8601DT</FORMAT>
            <INFORMAT width="19">IS8601DT</INFORMAT>
        </COLUMN>

        <COLUMN name="_parentFolder">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_folderDO/properties/_parentFolder</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>256</LENGTH>
        </COLUMN>

        <COLUMN name="_folder">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/FlowDO/properties/_folderDO/properties/_name</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>100</LENGTH>
        </COLUMN>

        <COLUMN name="_totalCount">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CampaignDO/properties/_totalCount</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="NodeRelation">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_outputNodes/String</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="node_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="output_id">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_outputNodes/String</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="Node">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="node_id">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_label">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_label</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeType">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_nodeType</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="_count">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/NodeDO/properties/_count</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="LinkNode">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeType">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_nodeType</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="_nodeCode">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_nodeCode</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_cellId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_cellId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_cellName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_cellName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_flowId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_flowId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_flowName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_flowName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_flowparentFolder">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_flowFolder/properties/_parentFolder</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>256</LENGTH>
        </COLUMN>

        <COLUMN name="_flowFolder">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LinkNodeDataDO/properties/_flowFolder/properties/_name</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="CommunicationLineItem">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_communicationDefinition/properties/_exportData/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_outputColumnName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_communicationDefinition/properties/_exportData/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_outputColumnName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_name">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_communicationDefinition/properties/_exportData/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_name</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_communicationDefinition/properties/_exportData/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_exportFieldKey/properties/_varRefDO/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CommunicationDO/properties/_communicationDefinition/properties/_exportData/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_exportFieldKey/properties/_varRefDO/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="MultiSelectVar">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MultiSelectNodeDataDO/properties/_SQLExpression/properties/_rootNode/properties/_children/FilterNodeDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MultiSelectNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MultiSelectNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MultiSelectNodeDataDO/properties/_SQLExpression/properties/_rootNode/properties/_children/FilterNodeDO/properties/_subjectId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MultiSelectNodeDataDO/properties/_SQLExpression/properties/_rootNode/properties/_children/FilterNodeDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MultiSelectNodeDataDO/properties/_SQLExpression/properties/_rootNode/properties/_children/FilterNodeDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="SelectNodeVar">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SelectNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SelectNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SelectNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SelectNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SelectNodeDataDO/properties/_varRefDO/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SelectNodeDataDO/properties/_varRefDO/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="CodeNode">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_customNodeInputSubjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_customNodeInputSubjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeType">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_nodeType</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="isCustomNode">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_customNode</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>5</LENGTH>
        </COLUMN>

        <COLUMN name="ProcessNode_type">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_type</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>10</LENGTH>
        </COLUMN>

        <COLUMN name="StoredProcess_name">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_storedProcDetail/properties/_name</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>80</LENGTH>
        </COLUMN>

        <COLUMN name="StoredProcess_folder">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_storedProcDetail/properties/_folder/properties/_name</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>100</LENGTH>
        </COLUMN>

        <COLUMN name="StoredProcess_parent_folder">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_storedProcDetail/properties/_folder/properties/_parentFolder</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>300</LENGTH>
        </COLUMN>

        <COLUMN name="Node_lastRunOn">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_lastRunOn</PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>datetime</DATATYPE>
            <FORMAT width="19">IS8601DT</FORMAT>
            <INFORMAT width="19">IS8601DT</INFORMAT>
        </COLUMN>

        <COLUMN name="_codeText">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/CodeNodeDataDO/properties/_codeText</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>31000</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="ABTestNodeVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/TestSplitNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/TestSplitNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/TestSplitNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/TestSplitNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/TestSplitNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/TestSplitNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="LimitNodeVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LimitNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LimitNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LimitNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LimitNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LimitNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/LimitNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="PrioritizeNodeVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/PrioritizeNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/PrioritizeNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/PrioritizeNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/PrioritizeNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/PrioritizeNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/PrioritizeNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="MapNode">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="MapNodeFilterVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO</TABLE-PATH>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_SQLExpression/properties/_rootNode/properties/_children/FilterNodeDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_SQLExpression/properties/_rootNode/properties/_children/FilterNodeDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="MapNodeSortVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO</TABLE-PATH>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/MapNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="ExportNodeVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/ExportNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/ExportNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/ExportNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_outputColumnName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/ExportNodeDataDO/properties/_exportDefinitions/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_outputColumnName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/ExportNodeDataDO/properties/_exportDefinitions/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_exportFieldKey/properties/_varRefDO/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/ExportNodeDataDO/properties/_exportDefinitions/ExportDO/properties/_exportDefinitionDO/properties/_lineItems/ExportDefinitionLineItemDO/properties/_exportFieldKey/properties/_varRefDO/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="SplitNode">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO</TABLE-PATH>

        <COLUMN name="campaign_id" retain="YES">
            <PATH syntax="XPath">/PersistenceDO/@id</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_nodeName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_nodeName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>64</LENGTH>
        </COLUMN>

        <COLUMN name="_subjectID">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_subjectID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="SplitNodeSplitVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO</TABLE-PATH>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_splitOnNodeDataDO/properties/_varRefDO/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_splitOnNodeDataDO/properties/_varRefDO/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="SplitNodeSortVars">
        <TABLE-PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO</TABLE-PATH>

        <COLUMN name="_nodeId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_nodeId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>16</LENGTH>
        </COLUMN>

        <COLUMN name="_varInfoId">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varInfoId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>128</LENGTH>
        </COLUMN>

        <COLUMN name="_varName">
            <PATH syntax="XPath">/PersistenceDO/properties/_doListToPersist/SplitNodeDataDO/properties/_sortByList/SortByDO/properties/_varRef/properties/_varName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

</SXLEMAP>
