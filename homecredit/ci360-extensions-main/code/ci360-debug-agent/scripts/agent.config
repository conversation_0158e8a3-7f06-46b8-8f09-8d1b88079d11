# Standard agent config - CI360 settings
ci360.gatewayHost=extapigwservice-demo.cidemo.sas.com
ci360.tenantID=
ci360.clientSecret=

# Standard agent config 
agent.keepaliveInterval=300000
agent.runInteractiveConsole=false
agent.monitorOutputInterval=600000
agent.lastEventOutput=last_event_json.txt

# Elastic Search settings
elastic.callElastic=true
elastic.host=
elastic.port=9200
elastic.index=ci360events

# Database settings
db.writeToDb=true
db.url=*****************************************
db.user=
db.password=
db.minIdle=5
db.maxIdle=10
db.maxOpenPreparedStatements=100
db.eventTable=events
