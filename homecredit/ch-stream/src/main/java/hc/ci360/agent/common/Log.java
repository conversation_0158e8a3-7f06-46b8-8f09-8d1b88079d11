package hc.ci360.agent.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.Properties;

public class Log {
    private static final Logger logger = (Logger) LoggerFactory.getLogger(Log.class);
    private Properties properties;

    public Log() throws Exception {
        String configFile = System.getProperty("configFile");

        if (configFile == null)
            throw new Exception("Missing system property 'configFile'");
        File fl= Paths.get(configFile).toFile();

        InputStream is = new FileInputStream(fl);

        this.properties = new Properties();
        properties.load(is);
    }

    public String loadKeyValue(String key) {
        //load and log key value from app.config
        String value = this.properties.getProperty(key);
        logger.info(key + "={}",value);
        return value;
    }

    public String loadKeyValueBase64(String key) {
        //load and log key value from app.config
        String value = Util.base64Decode(this.properties.getProperty(key));
        return value;
    }

    public int loadKeyValueAsInteger(String key) {
        //load and log key value from app.config
        int value = Integer.valueOf(this.properties.getProperty(key));
        logger.info(key + "={}",value);
        return value;
    }
}
