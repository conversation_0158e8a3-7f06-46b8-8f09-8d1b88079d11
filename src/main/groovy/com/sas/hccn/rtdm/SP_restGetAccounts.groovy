package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * GetAccounts
 * @version 22/07/18-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String ID_CUID;
    String Account_Type; // REVOLVING_ACCOUNT
    String Service_Type; // PAYM or CRDPST
    String Account_Status;
    String Service_Status;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix1 = "/cabus-am/rest/openapi/v5/account"
    private final String resourcePrefix2 = "/cabus-am/rest/ap/accounts/"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/am.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetAccounts...");
        log.info("GetAccounts - ID_CUID: $ID_CUID");
        log.info("GetAccounts - Account_Type: $Account_Type")
        log.info("GetAccounts - Service_Type: $Service_Type")
        log.info("GetAccounts - Account_Status: $Account_Status")
        log.info("GetAccounts - Service_Status: $Service_Status")

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("am.username");
        password = config.getProperty("am.password");
        host = config.getProperty("am.host");

        log.info("GetAccounts - host: $host");
        log.info("GetAccounts - resource prefix 1: $resourcePrefix1");
        log.info("GetAccounts - resource prefix 2: $resourcePrefix2");

        if (ID_CUID != null && !ID_CUID.isEmpty()) {

            if (Account_Type == null || Account_Type.isEmpty()) {
                Account_Type = "REVOLVING_ACCOUNT"
                log.info("GetAccounts - Setting 'Account_Type' to default value: 'REVOLVING_ACCOUNT'")
            }
            List<String> serviceTypes;
            if (Service_Type == null || Service_Type.isEmpty()) {
                serviceTypes = Arrays.asList("PAYM", "CRDPST")
                log.info("GetAccounts - Setting 'Service_Type' to default value: List('PAYM', 'CRDPST')")
            } else {
                serviceTypes = Arrays.asList(Service_Type.split("\\s*,\\s*"));
            }
            List<String> accountStatuses;
            if (Account_Status == null || Account_Status.isEmpty()) {
                accountStatuses = new ArrayList<>()
                log.info("GetAccounts - Setting 'Account_Status' to default value: List of all possible values")
            } else {
                accountStatuses = Arrays.asList(Account_Status.split("\\s*,\\s*"));
            }
            List<String> serviceStatuses;
            if (Service_Status == null || Service_Status.isEmpty()) {
                serviceStatuses = new ArrayList<>()
                log.info("GetAccounts - Setting 'Service_Status' to default value: List of all possible values")
            } else {
                serviceStatuses = Arrays.asList(Service_Status.split("\\s*,\\s*"));
            }

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("ID_CUID", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("Account_Number", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Account_Type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Account_Status", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Service_Code", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Service_Type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("Service_Status", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("statusDescription", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            String uri = host + resourcePrefix1
            log.info("GetAccounts - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json", (HttpUtils.ACCEPT): "application/json", (HttpUtils.X_CUID): ID_CUID],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                createAccountNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), httpCallResponse.getErrorMessage())
                return
            }

            log.trace("GetAccounts - httpResponseCode:" + httpResponseCode);
            log.trace("GetAccounts - response:" + responseString);

            AccountByCuidResponse responseObject = MappingUtils.mapToObject(responseString, AccountByCuidResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                createAccountNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), "Failed to map response. Wrong response data format.")
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetAccounts - record size:" + responseObject.data.size());

                if (responseObject.data.size() == 0) {
                    createAccountNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), "No account found")
                    return;
                }

                boolean accountMatches = false; //if some row gets written to result, this is set to true. If not, default record is created.
                for (int j = 0; j < responseObject.data.size(); j++) {

                    Account account = responseObject.data.get(j);
                    log.trace("GetAccounts service - account: " + new ObjectMapper().writeValueAsString(account));

                    if (account.accountType == Account_Type && (accountStatuses.isEmpty() || accountStatuses.contains(account.status))) {
                        log.trace("GetAccounts service - account [$account.number] matching selected criteria. Getting service details");

                        String uri2 = host + resourcePrefix2 + account.number;

                        log.info("GetAccounts service - Endpoint URL: $uri2");
                        HttpCallResponse httpCallResponse2 = HttpUtils.runHttpProcess(
                                evtInfo.getEventName(),
                                evtInfo.getSimulationDate().getTime(),
                                uri2,
                                HttpUtils.getBasicAuthToken(username, password),
                                [(HttpUtils.CONTENT_TYPE): "application/json"],
                                RequestMethod.GET,
                                null
                        );

                        httpResponseCode = httpCallResponse2.getHttpResponseCode();
                        status = httpCallResponse2.getStatus().getStatus();
                        errorMessage = httpCallResponse2.getErrorMessage();
                        String responseString2 = httpCallResponse2.getResponse()

                        if (errorMessage != null && errorMessage.contains("ERR_ACCOUNT_NOT_FOUND")) {
                            log.trace("GetAccounts service - account [$account.number] not found. Creating default result");

                            createServiceNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), account.number, account.accountType, account.status, "Account not found")
                            status = Status.OK.getStatus();
                            continue
                        }
                        if (responseString2 == null) {
                            createServiceNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), account.number, account.accountType, account.status, httpCallResponse2.getErrorMessage())
                            continue
                        }

                        log.trace("GetAccounts service - httpResponseCode:" + httpResponseCode);
                        log.trace("GetAccounts service - response:" + responseString2);

                        AccountDetailResponse accountDetailResponse = MappingUtils.mapToObject(responseString2, AccountDetailResponse.class)
                        if (responseObject == null) {
                            status = Status.ERROR.getStatus();
                            errorMessage = "Failed to map response. Wrong response data format."
                            createServiceNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), account.number, account.accountType, account.status, "Failed to map response. Wrong response data format.")
                            continue
                        }

                        if (httpResponseCode == 200) {
                            log.trace("GetAccounts service - record size:" + accountDetailResponse.contractServices.size());

                            boolean serviceMatches = false;
                            for (int k = 0; k < accountDetailResponse.contractServices.size(); k++) {

                                ContractService contractService = accountDetailResponse.contractServices.get(k)
                                log.trace("GetAccounts service - contractService: " + new ObjectMapper().writeValueAsString(contractService));

                                if (serviceTypes.contains(contractService.serviceType) && (serviceStatuses.isEmpty() || serviceStatuses.contains(contractService.status))) {
                                    log.trace("GetAccounts service - service matching selected criteria. Adding to result");

                                    Row newRow = ClientDetails.rowAdd();
                                    newRow.columnDataSet("ID_CUID", account.id);
                                    newRow.columnDataSet("Account_Number", account.number.toString());
                                    newRow.columnDataSet("Account_Type", account.accountType);
                                    newRow.columnDataSet("Account_Status", account.status);
                                    newRow.columnDataSet("Service_Code", contractService.serviceCode);
                                    newRow.columnDataSet("Service_Type", contractService.serviceType);
                                    newRow.columnDataSet("Service_Status", contractService.status);
                                    newRow.columnDataSet("statusDescription", "");

                                    serviceMatches = true;
                                    accountMatches = true;
                                } else {
                                    log.trace("GetAccounts service - service NOT matching selected criteria.");
                                }
                            }
                            if (!serviceMatches){
                                createServiceNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), account.number, account.accountType, account.status, "No service met the selection criteria")
                                accountMatches = true;
                            }
                        }
                    } else {
                        log.trace("GetAccounts service - account [$account.number] NOT matching selected criteria.");
                    }
                }
                if (!accountMatches) {
                    createAccountNotFoundRow(ClientDetails, Long.parseLong(ID_CUID), "No account met the selection criteria")
                }
            }
        } else {
            log.trace("GetAccounts - No input clients");
        }
    }

    private void createAccountNotFoundRow(RTDMTable ClientDetails, Long id, String statusDescription) {
        Row newRow = ClientDetails.rowAdd();
        newRow.columnDataSet("ID_CUID", id);
        newRow.columnDataSet("Account_Number", "0");
        newRow.columnDataSet("Account_Type", "NOT_EXIST");
        newRow.columnDataSet("Account_Status", "NOT_EXIST");
        newRow.columnDataSet("Service_Code", "NOT_EXIST");
        newRow.columnDataSet("Service_Type", "NOT_EXIST");
        newRow.columnDataSet("Service_Status", "NOT_EXIST");
        newRow.columnDataSet("statusDescription", statusDescription);
    }

    private void createServiceNotFoundRow(RTDMTable ClientDetails, Long id, Long accountNumber, String accountType, String accountStatus, String statusDescription) {
        Row newRow = ClientDetails.rowAdd();
        newRow.columnDataSet("ID_CUID", id);
        newRow.columnDataSet("Account_Number", accountNumber.toString());
        newRow.columnDataSet("Account_Type", accountType);
        newRow.columnDataSet("Account_Status", accountStatus);
        newRow.columnDataSet("Service_Code", "NOT_EXIST");
        newRow.columnDataSet("Service_Type", "NOT_EXIST");
        newRow.columnDataSet("Service_Status", "NOT_EXIST");
        newRow.columnDataSet("statusDescription", statusDescription);
    }
}

class AccountByCuidResponse implements Serializable {
    List<Account> data;
    PagingInfo pagingInfo;
}

class Account implements Serializable {
    Long id;
    String accountOwnerCode;
    Long number;
    String accountType;
    String status;
    String accountCurrency;
    Long billingDay;
    Boolean termination;
    Long billingPeriodClosingDay;
    Boolean blocked;
}

class PagingInfo implements Serializable {
    String prevPage;
    Long itemsPerPage;
}

class AccountDetailResponse implements Serializable {
    Long accountNumber;
    String accountType;
    String accountOwnerCuid;
    List<ContractService> contractServices;
}

class ContractService implements Serializable {
    String serviceCode;
    Long serviceVersion;
    String serviceType;
    String status;
    List<ServiceParameter> serviceParameters;
}

class ServiceParameter implements Serializable {
    String code;
    String value;
}