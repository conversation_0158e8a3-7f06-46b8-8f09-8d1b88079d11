package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * Create offer
 * @version 23/03/23-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String limitId;
    String offerTypeCode;
    String partyId;
    Long maxCreditAmount;
    Long maxMonthlyInst;
    Long minCashPayment;
    Long relativeMinCashPayment;
    String offerValidTo;
    Long pricingCategory;
    String crmPilotCode;
    Long offerIdSas;
    String sourceProcessId;
    String acqChannelCodes; //list
    String productCodes; //list
    String productSetCode;
    String relationId;
    String relationCode;
    String accountNumber;
    Long minMonthlyInst;
    String pricingStrategy;
    Long minCreditAmount;
    Long maxCashPaymentRel;
    Long minTenor;
    String limitCategory;
    String commodityCategoryGroup;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String code; //list

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/offers"

    // Variables from properties
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process CreateOffer...");
        log.info("CreateOffer - limitId: $limitId");
        log.info("CreateOffer - offerTypeCode: $offerTypeCode");
        log.info("CreateOffer - partyId: $partyId");
        log.info("CreateOffer - maxCreditAmount: $maxCreditAmount");
        log.info("CreateOffer - maxMonthlyInst: $maxMonthlyInst");
        log.info("CreateOffer - minCashPayment: $minCashPayment");
        log.info("CreateOffer - relativeMinCashPayment: $relativeMinCashPayment");
        log.info("CreateOffer - offerValidTo: $offerValidTo");
        log.info("CreateOffer - pricingCategory: $pricingCategory");
        log.info("CreateOffer - crmPilotCode: $crmPilotCode");
        log.info("CreateOffer - offerIdSas: $offerIdSas");
        log.info("CreateOffer - sourceProcessId: $sourceProcessId");
        log.info("CreateOffer - acqChannelCodes: $acqChannelCodes");
        log.info("CreateOffer - productCodes: $productCodes");
        log.info("CreateOffer - productSetCode: $productSetCode");
        log.info("CreateOffer - relationId: $relationId");
        log.info("CreateOffer - relationCode: $relationCode");
        log.info("CreateOffer - accountNumber: $accountNumber");
        log.info("CreateOffer - minMonthlyInst: $minMonthlyInst");
        log.info("CreateOffer - pricingStrategy: $pricingStrategy");
        log.info("CreateOffer - minCreditAmount: $minCreditAmount");
        log.info("CreateOffer - maxCashPaymentRel: $maxCashPaymentRel");
        log.info("CreateOffer - minTenor: $minTenor");
        log.info("CreateOffer - limitCategory: $limitCategory");
        log.info("CreateOffer - commodityCategoryGroup: $commodityCategoryGroup");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");

        log.info("CreateOffer - host: $host");
        log.info("CreateOffer - resource prefix: $resourcePrefix");

        // Setting API variables
        String uri = host + resourcePrefix;
        log.info("CreateOffer - Endpoint URL: $uri");

        Request request = new Request()
        request.setLimitId(limitId);
        request.setOfferTypeCode(offerTypeCode);
        request.setPartyId(partyId);
        request.setMaxCreditAmount(maxCreditAmount);
        request.setMaxMonthlyInst(maxMonthlyInst);
        request.setMinCashPayment(minCashPayment);
        request.setRelativeMinCashPayment(relativeMinCashPayment);
        request.setOfferValidTo(offerValidTo);
        request.setPricingCategory(pricingCategory);
        request.setCrmPilotCode(crmPilotCode);
        request.setOfferIdSas(offerIdSas);
        request.setSourceProcessId(sourceProcessId);
        request.setAcqChannelCodes(acqChannelCodes?.split(",")?.toList());
        request.setProductCodes(productCodes?.split(",")?.toList());
        request.setProductSetCode(productSetCode);
        request.setRelationId(relationId);
        request.setRelationCode(relationCode);
        request.setAccountNumber(accountNumber);
        request.setMinMonthlyInst(minMonthlyInst);
        request.setPricingStrategy(pricingStrategy);
        request.setMinCreditAmount(minCreditAmount);
        request.setMaxCashPaymentRel(maxCashPaymentRel);
        request.setMinTenor(minTenor);
        request.setLimitCategory(limitCategory);
        request.setCommodityCategoryGroup(commodityCategoryGroup);

        ObjectMapper mapper = new ObjectMapper();
        String jsonMessage = mapper.writeValueAsString(request)
        byte[] data = jsonMessage.getBytes(StandardCharsets.UTF_8)
        log.info("CreateOffer - Serialized message:" + jsonMessage)

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                null,
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.POST,
                data
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return
        }

        log.trace("CreateOffer - httpResponseCode:" + httpResponseCode);
        log.trace("CreateOffer - response:" + responseString);

        Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return
        }

        if (httpResponseCode == 201) {
            log.trace("CreateOffer - record size:" + responseObject.records.size());

            code = responseObject.records.toListString()
        }
    }
}

class Request implements Serializable {
    String limitId;
    String offerTypeCode;
    String partyId;
    Long maxCreditAmount;
    Long maxMonthlyInst;
    Long minCashPayment;
    Long relativeMinCashPayment;
    String offerValidTo;
    Long pricingCategory;
    String crmPilotCode;
    Long offerIdSas;
    String sourceProcessId;
    List<String> acqChannelCodes;
    List<String> productCodes;
    String productSetCode;
    String relationId;
    String relationCode;
    String accountNumber;
    Long minMonthlyInst;
    String pricingStrategy;
    Long minCreditAmount;
    Long maxCashPaymentRel;
    Long minTenor;
    String limitCategory;
    String commodityCategoryGroup;
}

class Response implements Serializable {
    List<String> records;
}
