package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * GetClientZoneUrlID
 * @version 10/01/23-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String contractNumber;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    String url;
    String id;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/sas/cz-shorten-url/scl/weblink"

    // Variables from properties
    private String host;
    private String username;
    private String password;

    private final String CONFIG_FILE = "/sas/groovy/Connections/api.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetClientZoneUrl...");
        log.info("GetClientZoneUrl - contract number: $contractNumber")

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("api.host");
        username = config.getProperty("api.username")
        password = config.getProperty("api.password")

        log.info("GetClientZoneUrl - host: $host");
        log.info("GetClientZoneUrl - resource prefix: $resourcePrefix");

        // Setting API variables
        Request request = new Request();
        request.setContractNumber(contractNumber);

        ObjectMapper mapper = new ObjectMapper();
        String jsonMessage = mapper.writeValueAsString(request)
        byte[] data = jsonMessage.getBytes(StandardCharsets.UTF_8)

        String uri = host + resourcePrefix;
        log.info("GetClientZoneUrl - Endpoint URL: $uri");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.POST,
                data
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();
        String responseString = httpCallResponse.getResponse()
        if (responseString == null) {
            return
        }

        log.trace("GetClientZoneUrl - httpResponseCode:" + httpResponseCode);
        log.trace("GetClientZoneUrl - response:" + responseString);

        Response responseObject = MappingUtils.mapToObject(responseString, Response.class)
        if (responseObject == null) {
            status = Status.ERROR.getStatus();
            errorMessage = "Failed to map response. Wrong response data format."
            return
        }

        if (httpResponseCode == 200) {
            log.trace("GetClientZoneUrl - record:" + responseObject.id);

            id = responseObject.id
            url = responseObject.url
            status = Status.OK.getStatus();
        }
    }
}

class Request implements Serializable {
    String contractNumber;
}

class Response implements Serializable {
    String id;
    String url;
}
