package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.TokenUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.homecredit.sas.utils.model.TokenResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import javax.sql.DataSource

/**
 * Get Contracts
 * @version 05/11/22-002
 */
class GetDetailForClientList implements Runnable {

    private Map <String, DataSource> mapJDBC = null;
    void setMapJDBC(Map <String, DataSource> input) {
        mapJDBC = input;
    }

    // Input variables
    String id_cuid;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/rest/v12/contracts?customerId=";

    // Variables from properties
    private String username;
    private String password;
    private String host;
    private String authPath; // = "https://sso.vn00c1.vn.infra/auth/realms/hci/protocol/openid-connect/token";

    private final String CONFIG_FILE = "/sas/groovy/Connections/openid.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetContracts...");
        log.info("GetContracts - cuid: $id_cuid");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("openid.username");
        password = config.getProperty("openid.password");
        host = config.getProperty("openid.host");
        authPath = config.getProperty("openid.authpath");

        log.info("GetContracts - host: $host");
        log.info("GetContracts - resource prefix: $resourcePrefix");

        if (id_cuid != null && !id_cuid.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("raw", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("code", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("status", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("type", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("cuid", DataTypes.STRING, Collections.emptyList());

            // Setting API variables
            TokenResponse tokenResponse = TokenUtils.getToken(
                    mapJDBC,
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    username,
                    password,
                    authPath,
                    "MA_TEMP_JDBC",
                    "RTDM_TEMP.SSO_TOKEN"
            )
            if (tokenResponse.getToken() == null) {
                status = tokenResponse.getStatus().getStatus();
                errorMessage = tokenResponse.getErrorMessage();
                return;
            }
            String authToken = tokenResponse.getToken();

            String uri = host + resourcePrefix + id_cuid;
            log.info("GetContracts - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBearerAuthToken(authToken),
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetContracts - httpResponseCode:" + httpResponseCode);
            log.trace("GetContracts - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetContracts - record size:" + responseObject.content.size());

                ObjectMapper mapper = new ObjectMapper()
                for (int j = 0; j < responseObject.content.size(); j++) {

                    String code = responseObject.content.get(j).code;
                    log.trace("GetContracts - code: " + code);

                    Row newRow = ClientDetails.rowAdd();
                    newRow.columnDataSet("raw", "cuid:" + id_cuid + ", response:" + mapper.writeValueAsString(responseObject.content.get(j)));
                    newRow.columnDataSet("code", responseObject.content.get(j).code);
                    newRow.columnDataSet("status", responseObject.content.get(j).status);
                    newRow.columnDataSet("type", responseObject.content.get(j).type);
                    newRow.columnDataSet("cuid", id_cuid);
                }
            }
        } else {
            log.trace("GetContracts - No input clients");
        }
    }
}

class MyResponse implements Serializable {
    List<Records> content;
    Long pageNumber;
    Long pageSize;
    Long totalPages;
    Long totalElements;
}

class Records implements Serializable {
    String code;
    String status;
    String type;
    String paymentMode;
    Customer customer;
    Product product;
    List<Service> services;
    List<Commodity> commodities;
    List<Document> documents;
}

class Customer implements Serializable {
    String id;
}

class Product implements Serializable {
    String code;
    Long version;
}

class Service implements Serializable {
    String id;
}

class Commodity implements Serializable {
    String id;
}

class Document implements Serializable {
    String id;
}