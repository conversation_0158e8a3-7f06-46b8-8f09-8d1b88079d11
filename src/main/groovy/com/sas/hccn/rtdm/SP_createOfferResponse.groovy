package com.sas.hccn.rtdm

import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets

/**
 * Create offer response
 * @version 22/09/07-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String offerId;
    String responseChannelCode;
    String responseText;
    String responseComment;
    String responseTimestamp;
    String userId;
    String sourceIdType;
    String sourceIdValue;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String responseStatus;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/offers/"
    private final String resourceSuffix = "/responses"

    // Event info
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process CreateOfferResponses...");
        log.info("CreateOfferResponses - offerId: $offerId");
        log.info("CreateOfferResponses - responseChannelCode: $responseChannelCode");
        log.info("CreateOfferResponses - responseText: $responseText");
        log.info("CreateOfferResponses - responseComment: $responseComment");
        log.info("CreateOfferResponses - responseTimestamp: $responseTimestamp");
        log.info("CreateOfferResponses - userId: $userId");
        log.info("CreateOfferResponses - sourceIdType: $sourceIdType");
        log.info("CreateOfferResponses - sourceIdValue: $sourceIdValue");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");

        log.info("CreateOfferResponses - host: $host");
        log.info("CreateOfferResponses - resource prefix: $resourcePrefix");

        if (offerId != null && !offerId.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + offerId + resourceSuffix;
            log.info("CreateOfferResponses - Endpoint URL: $uri");

            Request request = new Request()
            request.setAcqChannelCode(responseChannelCode)
            request.setResponseText(responseText)
            request.setResponseComment(responseComment)
            request.setResponseTimestamp(responseTimestamp)
            request.setUserId(userId)
            request.setSourceIdType(sourceIdType)
            request.setSourceIdValue(sourceIdValue)

            ObjectMapper mapper = new ObjectMapper();
            String jsonMessage = mapper.writeValueAsString(request)
            byte[] data = jsonMessage.getBytes(StandardCharsets.UTF_8)
            log.info("CreateOfferResponses - Serialized message:" + jsonMessage)

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.POST,
                    data
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            log.trace("CreateOfferResponses - httpResponseCode:" + httpResponseCode);

            responseStatus = httpResponseCode
        } else {
            log.trace("CreateOfferResponses - No input clients");
        }
    }
}

class Request implements Serializable {
    String acqChannelCode;
    String responseText;
    String responseComment;
    String responseTimestamp;
    String userId;
    String sourceIdType;
    String sourceIdValue;
}
