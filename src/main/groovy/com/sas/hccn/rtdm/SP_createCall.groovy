package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.time.format.DateTimeFormatter

/**
 * Create affinity call record via POST request
 * @version 23/05/30-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    Object campaignEnd;
    String cuid;
    String gender;
    String dateOfBirth;
    Long ilCommunicationId;
    String customerNationalId;
    Object communicationStart;
    Long priority;
    Object communicationEnd;
    Object dateEffective;
    Long dailyFrom;
    String codeCallListType;
    String nameCallList;
    String lastFirstName;
    Object campaignStart;
    String callType;
    Long dailyTill;
    String callSource;

    Object callbackTime;
    String agentId;
    Long resptrackingId;
    String callbackCallList;
    String contactInfo;
    Long switchId;
    Long recordType;

    String key;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/external-system/SAS_RTDM";

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/affinity.properties";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Override
    void run() {

        //start log info
        log.info("Starting process CreateAffinityCall...");
        log.info("CreateAffinityCall - campaignEnd: $campaignEnd");
        log.info("CreateAffinityCall - cuid: $cuid");
        log.info("CreateAffinityCall - gender: $gender");
        log.info("CreateAffinityCall - dateOfBirth: $dateOfBirth");
        log.info("CreateAffinityCall - ilCommunicationId: $ilCommunicationId");
        log.info("CreateAffinityCall - customerNationalId: $customerNationalId");
        log.info("CreateAffinityCall - communicationStart: $communicationStart");
        log.info("CreateAffinityCall - priority: $priority");
        log.info("CreateAffinityCall - communicationEnd: $communicationEnd");
        log.info("CreateAffinityCall - dateEffective: $dateEffective");
        log.info("CreateAffinityCall - dailyFrom: $dailyFrom");
        log.info("CreateAffinityCall - codeCallListType: $codeCallListType");
        log.info("CreateAffinityCall - nameCallList: $nameCallList");
        log.info("CreateAffinityCall - lastFirstName: $lastFirstName");
        log.info("CreateAffinityCall - campaignStart: $campaignStart");
        log.info("CreateAffinityCall - callType: $callType");
        log.info("CreateAffinityCall - dailyTill: $dailyTill");
        log.info("CreateAffinityCall - callSource: $callSource");

        log.info("CreateAffinityCall - callbackTime: $callbackTime");
        log.info("CreateAffinityCall - agentId: $agentId");
        log.info("CreateAffinityCall - resptrackingId: $resptrackingId");
        log.info("CreateAffinityCall - callbackCallList: $callbackCallList");
        log.info("CreateAffinityCall - contactInfo: $contactInfo");
        log.info("CreateAffinityCall - switchId: $switchId");
        log.info("CreateAffinityCall - recordType: $recordType");

        log.info("CreateAffinityCall - key: $key");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("affinity.username");
        password = config.getProperty("affinity.password");
        host = config.getProperty("affinity.host");

        log.info("CreateAffinityCall - host: $host");

        // Setting API variables

        String uri = host + resourcePrefix;
        log.info("CreateAffinityCall - Endpoint URL: $uri");
        Request requestObject = createRequest()
        if (requestObject == null) {
            return
        }
        List requestWrapper = [requestObject]
        String jsonInputString = new ObjectMapper().writeValueAsString(requestWrapper)
        log.info("CreateAffinityCall - Request body: $jsonInputString");
        byte[] postData = jsonInputString.getBytes("UTF-8");

        HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                evtInfo.getEventName(),
                evtInfo.getSimulationDate().getTime(),
                uri,
                HttpUtils.getBasicAuthToken(username, password),
                [(HttpUtils.CONTENT_TYPE): "application/json"],
                RequestMethod.POST,
                postData
        );

        httpResponseCode = httpCallResponse.getHttpResponseCode();
        status = httpCallResponse.getStatus().getStatus();
        errorMessage = httpCallResponse.getErrorMessage();

        log.trace("CreateAffinityCall - httpResponseCode:" + httpResponseCode);

        if (httpResponseCode == 200) {
            status = "OK";
        }
    }

    Request createRequest() {
        Record myRecord = new Record()
        if (callbackTime instanceof GregorianCalendar) {
            myRecord.callbackTime = (Long) (((GregorianCalendar) campaignEnd).getTimeInMillis() / 1000)
        }
        myRecord.agentId = agentId;
        myRecord.resptrackingId = resptrackingId;
        myRecord.callbackCallList = callbackCallList;
        myRecord.contactInfo = contactInfo;
        myRecord.switchId = switchId;
        myRecord.recordType = recordType;

        Data data = new Data();
        data.records = [myRecord];
        if (campaignEnd instanceof GregorianCalendar) {
            data.campaignEnd = formatter.format(((GregorianCalendar) campaignEnd).toZonedDateTime())
        }
        data.cuid = cuid;
        data.gender = gender;
        data.dateOfBirth = dateOfBirth
        data.ilCommunicationId = ilCommunicationId;
        data.customerNationalId = customerNationalId;
        if (communicationStart instanceof GregorianCalendar) {
            data.communicationStart = formatter.format(((GregorianCalendar) communicationStart).toZonedDateTime())
        }
        data.priority = priority;
        if (communicationEnd instanceof GregorianCalendar) {
            data.communicationEnd = formatter.format(((GregorianCalendar) communicationEnd).toZonedDateTime())
        }
        if (dateEffective instanceof GregorianCalendar) {
            data.dateEffective = formatter.format(((GregorianCalendar) dateEffective).toZonedDateTime())
        }
        data.dailyFrom = dailyFrom;
        data.codeCallListType = codeCallListType;
        data.nameCallList = nameCallList;
        data.lastFirstName = lastFirstName;
        if (campaignStart instanceof GregorianCalendar) {
            data.campaignStart = formatter.format(((GregorianCalendar) campaignStart).toZonedDateTime())
        }
        data.callType = callType;
        data.dailyTill = dailyTill;
        data.callSource = callSource;

        Request request = new Request();
        request.key = key;
        request.data = data;

        return request;
    }
}


class Request implements Serializable {
    Data data;
    String key;
}

class Data implements Serializable {
    List<Record> records;

    @JsonProperty("campaign_end")
    String campaignEnd;
    String cuid;
    String gender;
    @JsonProperty("date_of_birth")
    String dateOfBirth;
    @JsonProperty("il_communication_id")
    Long ilCommunicationId;
    @JsonProperty("customer_national_id")
    String customerNationalId;
    @JsonProperty("communication_start")
    String communicationStart;
    Long priority;
    @JsonProperty("communication_end")
    String communicationEnd;
    @JsonProperty("date_effective")
    String dateEffective;
    @JsonProperty("daily_from")
    Long dailyFrom;
    @JsonProperty("code_call_list_type")
    String codeCallListType;
    @JsonProperty("name_call_list")
    String nameCallList;
    @JsonProperty("last_first_name")
    String lastFirstName;
    @JsonProperty("campaign_start")
    String campaignStart;
    @JsonProperty("call_type")
    String callType;
    @JsonProperty("daily_till")
    Long dailyTill;
    @JsonProperty("call_source")
    String callSource;
}

class Record implements Serializable {
    @JsonProperty("callback_time")
    Long callbackTime;
    @JsonProperty("agent_id")
    String agentId;
    @JsonProperty("resptracking_id")
    Long resptrackingId;
    @JsonProperty("callback_call_list")
    String callbackCallList;
    @JsonProperty("contact_info")
    String contactInfo;
    @JsonProperty("switch_id")
    Long switchId;
    @JsonProperty("record_type")
    Long recordType;
}
