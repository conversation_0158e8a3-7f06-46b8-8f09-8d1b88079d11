package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

/**
 * GetAccountDetails
 * @version 22/09/08-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String accountNumber;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    String accountOwnerCode;
    String accountType;
    String accountStatus;
    String accountCurrency;
    String blocked;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/cabus-am/rest/openapi/v5/account/"

    // Variables from properties
    private String username;
    private String password;
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/am.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetAccountDetails...");
        log.info("GetAccountDetails - accountNumber: $accountNumber");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        username = config.getProperty("am.username");
        password = config.getProperty("am.password");
        host = config.getProperty("am.host");

        log.info("GetAccountDetails - host: $host");
        log.info("GetAccountDetails - resource prefix: $resourcePrefix");

        if (accountNumber != null && !accountNumber.isEmpty()) {

            // Setting API variables
            String uri = host + resourcePrefix + accountNumber
            log.info("GetAccountDetails - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    HttpUtils.getBasicAuthToken(username, password),
                    [(HttpUtils.CONTENT_TYPE): "application/json", (HttpUtils.ACCEPT): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetAccountDetails - httpResponseCode:" + httpResponseCode);
            log.trace("GetAccountDetails - response:" + responseString);

            Account responseObject = MappingUtils.mapToObject(responseString, Account.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                accountOwnerCode = responseObject.getAccountOwnerCode()
                accountType = responseObject.getAccountType()
                accountStatus = responseObject.getStatus()
                accountCurrency = responseObject.getAccountCurrency()
                blocked = responseObject.getBlocked().toString()
            }
        } else {
            log.trace("GetAccountDetails - No input clients");
        }
    }
}

class Account implements Serializable {
    Long id;
    String accountOwnerCode;
    Long number;
    String accountType;
    String status;
    String accountCurrency;
    Long billingDay;
    Boolean termination;
    Long billingPeriodClosingDay;
    Boolean blocked;
    // ...
}