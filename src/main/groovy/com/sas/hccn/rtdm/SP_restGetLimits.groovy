package com.sas.hccn.rtdm


import com.homecredit.sas.utils.HttpUtils
import com.homecredit.sas.utils.MappingUtils
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.enumeration.RequestMethod
import com.homecredit.sas.utils.enumeration.Status
import com.homecredit.sas.utils.model.HttpCallResponse
import com.homecredit.sas.utils.model.PropertiesResponse
import com.sas.analytics.ph.common.RTDMTable
import com.sas.analytics.ph.common.RTDMTable.Row
import com.sas.analytics.ph.common.jaxb.DataTypes
import com.sas.rtdm.implementation.engine.EventInfo
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets;

class GetDetailForClientList implements Runnable {

    // Input variables
    String id_cuid;
    String limit_type;
    String limit_subtype;

    // Output variables
    String status = Status.ERROR.getStatus()
    Long httpResponseCode;    // HTTP response code
    String errorMessage;
    RTDMTable ClientDetails;

    // Logger
    private static final Logger log = Logger.getLogger('groovyLog');

    // Event info
    EventInfo evtInfo;

    // Constants
    private final String resourcePrefix = "/ofs/limits/partyId/"
    private final String resourceLimitType = "/limitType/"
    private final String resourceLimitSubtype = "/limitSubType/"

    // Variables from properties
    private String host;

    private final String CONFIG_FILE = "/sas/groovy/Connections/ofs.properties";

    @Override
    void run() {

        //start log info
        log.info("Starting process GetLimits...");
        log.info("GetLimits - cuid: $id_cuid");
        log.info("GetLimits - limit_type: $limit_type");
        log.info("GetLimits - limit_subtype: $limit_subtype");

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        host = config.getProperty("ofs.host");

        log.info("GetLimits - host: $host");
        log.info("GetLimits - resource prefix: $resourcePrefix");
        log.info("GetLimits - resource LimitType: $resourceLimitType");
        log.info("GetLimits - resource LimitSubtype: $resourceLimitSubtype");

        if (id_cuid != null && !id_cuid.isEmpty()) {

            // Create empty table
            ClientDetails = new RTDMTable();
            ClientDetails.columnAdd("partyId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitBulkId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitScoringProcessSource", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("calculationSource", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitValidFrom", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitValidTo", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitStatus", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitScore", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("amtCreditMax", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("amtInstalmentMax", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("amtDownPaymentMin", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("amtDownPaymentMinRel", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("codeRiskGrade", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitAccuracy", DataTypes.INT, Collections.emptyList());
            ClientDetails.columnAdd("limitPilotCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitTypeCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("limitSubTypeCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("acqChannelCodes", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("relationId", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("relationCode", DataTypes.STRING, Collections.emptyList());
            ClientDetails.columnAdd("accountNumber", DataTypes.STRING, Collections.emptyList());


            // Setting API variables
            String uri = host + resourcePrefix + id_cuid + resourceLimitType + limit_type + resourceLimitSubtype + limit_subtype;
            uri = URLEncoder.encode(uri, StandardCharsets.UTF_8.toString())
            log.info("GetLimits - Endpoint URL: $uri");

            HttpCallResponse httpCallResponse = HttpUtils.runHttpProcess(
                    evtInfo.getEventName(),
                    evtInfo.getSimulationDate().getTime(),
                    uri,
                    null,
                    [(HttpUtils.CONTENT_TYPE): "application/json"],
                    RequestMethod.GET,
                    null
            );

            httpResponseCode = httpCallResponse.getHttpResponseCode();
            status = httpCallResponse.getStatus().getStatus();
            errorMessage = httpCallResponse.getErrorMessage();
            String responseString = httpCallResponse.getResponse()
            if (responseString == null) {
                return
            }

            log.trace("GetLimits - httpResponseCode:" + httpResponseCode);
            log.trace("GetLimits - response:" + responseString);

            MyResponse responseObject = MappingUtils.mapToObject(responseString, MyResponse.class)
            if (responseObject == null) {
                status = Status.ERROR.getStatus();
                errorMessage = "Failed to map response. Wrong response data format."
                return
            }

            if (httpResponseCode == 200) {
                log.trace("GetLimits - record size:" + responseObject.records.size());

                for (int j = 0; j < responseObject.records.size(); j++) {

                    String limitId = responseObject.records.get(j).limitId;
                    log.trace("GetLimits - limitId: " + limitId);

                    Row newRow = ClientDetails.rowAdd();
                    newRow.columnDataSet("partyId", responseObject.records.get(j).partyId);
                    newRow.columnDataSet("limitBulkId", responseObject.records.get(j).limitBulkId);
                    newRow.columnDataSet("limitId", responseObject.records.get(j).limitId);
                    newRow.columnDataSet("limitScoringProcessSource", responseObject.records.get(j).limitScoringProcessSource);
                    newRow.columnDataSet("calculationSource", responseObject.records.get(j).calculationSource);
                    newRow.columnDataSet("limitValidFrom", responseObject.records.get(j).limitValidFrom);
                    newRow.columnDataSet("limitValidTo", responseObject.records.get(j).limitValidTo);
                    newRow.columnDataSet("limitStatus", responseObject.records.get(j).limitStatus);
                    newRow.columnDataSet("limitScore", responseObject.records.get(j).limitScore);
                    newRow.columnDataSet("amtCreditMax", responseObject.records.get(j).amtCreditMax);
                    newRow.columnDataSet("amtInstalmentMax", responseObject.records.get(j).amtInstalmentMax);
                    newRow.columnDataSet("amtDownPaymentMin", responseObject.records.get(j).amtDownPaymentMin);
                    newRow.columnDataSet("amtDownPaymentMinRel", responseObject.records.get(j).amtDownPaymentMinRel);
                    newRow.columnDataSet("codeRiskGrade", responseObject.records.get(j).codeRiskGrade);
                    newRow.columnDataSet("limitAccuracy", responseObject.records.get(j).limitAccuracy);
                    newRow.columnDataSet("limitPilotCode", responseObject.records.get(j).limitPilotCode);
                    newRow.columnDataSet("limitTypeCode", responseObject.records.get(j).limitTypeCode);
                    newRow.columnDataSet("limitSubTypeCode", responseObject.records.get(j).limitSubTypeCode);
                    newRow.columnDataSet("acqChannelCodes", responseObject.records.get(j).getAcqChannelCodes());
                    newRow.columnDataSet("relationId", responseObject.records.get(j).relationId);
                    newRow.columnDataSet("relationCode", responseObject.records.get(j).relationCode);
                    newRow.columnDataSet("accountNumber", responseObject.records.get(j).accountNumber);
                }
            }
        } else {
            log.trace("GetLimits - No input clients");
        }
    }
}

class MyResponse implements Serializable {
    List<Records> records;
}

class Records implements Serializable {
    String partyId;
    String limitBulkId;
    String limitId;
    String limitScoringProcessSource;
    String calculationSource;
    String limitValidFrom;
    String limitValidTo;
    String limitStatus;
    String limitScore;
    Long amtCreditMax;
    Long amtInstalmentMax;
    Long amtDownPaymentMin;
    Long amtDownPaymentMinRel;
    String codeRiskGrade;
    Long limitAccuracy;
    String limitPilotCode;
    String limitTypeCode;
    String limitSubTypeCode;
    List<String> acqChannelCodes;
    String relationId;
    String relationCode;
    String accountNumber;

    String getAcqChannelCodes() {
        if (acqChannelCodes == null)
            return null;
        return acqChannelCodes.join(",");
    }
}
