package com.sas.hccn.rtdm

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.rabbitmq.client.*
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.ZoneId

/**
 * @version 24/10/22-001
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String externalId
    String systemCode
    String messageCode
    String logicalApplication
    String cuid
    String reportLevel
    String reportContentType
    String text
    String FCM_MUTABLE_CONTENT
    String FCM_CONTENT_AVAILABLE
    String GMA_TEMPLATE_ID
    String GMA_INAPP
    String GMA_INAPP_TYPE
    String GMA_INAPP_VAR_PAYLOAD
    String priority
    Object expires

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";
    private Properties config = new Properties();
    String priorityBody;
    Long priorityHeader;

    @Override
    void run() {
        //start log info
        log.info("Starting process SendInApp to RabbitMQ...");
        log.info("SendInApp - externalId: $externalId");
        log.info("SendInApp - systemCode: $systemCode");
        log.info("SendInApp - messageCode: $messageCode");
        log.info("SendInApp - logicalApplication: $logicalApplication");
        log.info("SendInApp - cuid: $cuid");
        log.info("SendInApp - reportLevel: $reportLevel");
        log.info("SendInApp - reportContentType: $reportContentType");
        log.info("SendInApp - text: $text");
        log.info("SendInApp - FCM_MUTABLE_CONTENT: $FCM_MUTABLE_CONTENT");
        log.info("SendInApp - FCM_CONTENT_AVAILABLE: $FCM_CONTENT_AVAILABLE");
        log.info("SendInApp - GMA_TEMPLATE_ID: $GMA_TEMPLATE_ID");
        log.info("SendInApp - GMA_INAPP: $GMA_INAPP");
        log.info("SendInApp - priority: $priority");

        try {
            log.info("SendInApp - Loading configuration from path '$CONFIG_FILE'");
            this.config.load(new FileInputStream(CONFIG_FILE));
        } catch (Exception e) {
            log.error("SendInApp - Failed to load configuration: " + e.getMessage())
            status = "ERROR";
            throw e;
        }

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.iap-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");
        String priorityLowNum = config.getProperty ("rabbitmq.priority-low-num");
        String priorityMediumNum = config.getProperty ("rabbitmq.priority-medium-num");
        String priorityHighNum = config.getProperty ("rabbitmq.priority-high-num");
        String priorityLowStr = config.getProperty ("rabbitmq.priority-low-str");
        String priorityMediumStr = config.getProperty ("rabbitmq.priority-medium-str");
        String priorityHighStr = config.getProperty ("rabbitmq.priority-high-str");

        log.info("SendInApp - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange, priorityLowNum = $priorityLowNum, priorityMediumNum = $priorityMediumNum, priorityHighNum = $priorityHighNum, priorityLowStr = $priorityLowStr, priorityMediumStr = $priorityMediumStr, priorityHighStr = $priorityHighStr")

        if (priority != null && !priority.isEmpty()) {
            if (priority.toUpperCase()=="LOW") {
                priorityBody = priorityLowStr;
                priorityHeader = Long.parseLong(priorityLowNum);
            } else if (priority.toUpperCase()=="MEDIUM") {
                priorityBody = priorityMediumStr;
                priorityHeader = Long.parseLong(priorityMediumNum);
            } else if (priority.toUpperCase()=="HIGH") {
                priorityBody = priorityHighStr;
                priorityHeader = Long.parseLong(priorityHighNum);
            } else {
                priorityBody = priorityLowStr;
                priorityHeader = Long.parseLong(priorityLowNum);
            }
        } else {
            priorityBody = priorityLowStr;
            priorityHeader = Long.parseLong(priorityLowNum);
        }

        log.info("SendInApp - priorityHeader: $priorityHeader");
        log.info("SendInApp - priorityBody: $priorityBody");

        Message message = new Message()
        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        message.setLogicalApplication(logicalApplication)
        message.setText(text ?: "")
        message.setCuid(cuid)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        List<Attribute> attributes = new ArrayList<>();
        if (FCM_MUTABLE_CONTENT) attributes.add(new Attribute(type: "FCM_MUTABLE_CONTENT", value: FCM_MUTABLE_CONTENT));
        if (FCM_CONTENT_AVAILABLE) attributes.add(new Attribute(type: "FCM_CONTENT_AVAILABLE", value: FCM_CONTENT_AVAILABLE));
        if (GMA_TEMPLATE_ID) attributes.add(new Attribute(type: "GMA_TEMPLATE_ID", value: GMA_TEMPLATE_ID));
        if (GMA_INAPP) attributes.add(new Attribute(type: "GMA_INAPP", value: GMA_INAPP));
        if (GMA_INAPP_TYPE) attributes.add(new Attribute(type: "GMA_INAPP_TYPE", value: GMA_INAPP_TYPE));
        if (GMA_INAPP_VAR_PAYLOAD) attributes.add(new Attribute(type: "GMA_INAPP_VAR_PAYLOAD", fullValue: GMA_INAPP_VAR_PAYLOAD));
        message.setAttributes(attributes)
        message.setPriority(priorityBody)
        if (expires != null && expires instanceof GregorianCalendar) {
            ZonedDateTime zoned = ((GregorianCalendar) expires)
                    .toZonedDateTime()
                    .withZoneSameInstant(ZoneId.of("+07:00"))
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
            message.setExpires(formatter.format(zoned))
        }

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendInApp - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", priorityHeader);
        headerMap.put("cuid", cuid);
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendInApp - Message sent successfully");
            requestReplyLog.info("SendInApp - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendInApp - Failed to send message: " + e.getMessage())
            errorMessage = "Failed to send message: " + e.getMessage()
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String externalId
    String systemCode
    String messageCode
    String logicalApplication
    String text
    List<Attribute> attributes;
    String cuid
    String reportLevel
    String reportContentType
    String priority
    String expires
}

class Attribute implements Serializable {
    String type;
    String value;
    String fullValue;
}
