package com.sas.hccn.rtdm;

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.homecredit.sas.utils.PropertiesUtils
import com.homecredit.sas.utils.model.PropertiesResponse
import org.apache.log4j.Logger

import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter

/**
 * @version 23/03/15-005
 */
class GetDetailForClientList implements Runnable {

    // Input variables
    String externalId;
    String systemCode;
    String messageCode;
    Object expires;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;
    Boolean isInteractive;

    String CONTRACT_NUM;
    Long CUID;
    Long SAS_SMS_TYPE;
    String SMS_FLASH;

    // Output variables
    String status = "ERROR";            // either "OK" or "ERROR"
    String errorMessage;

    // Internal variables
    private static final Logger log = Logger.getLogger('groovyLog');
    private static final Logger requestReplyLog = Logger.getLogger('groovyRequestReply')

    private final String CONFIG_FILE = "/sas/groovy/Connections/rabbitmq.properties";

    @Override
    void run() {
        //start log info
        log.info("Starting process SendSms to RabbitMQ...");
        log.info("SendSms - externalId: $externalId");
        log.info("SendSms - systemCode: $systemCode");
        log.info("SendSms - messageCode: $messageCode");
        log.info("SendSms - expires: $expires");
        log.info("SendSms - priority: $priority");
        log.info("SendSms - reportLevel: $reportLevel");
        log.info("SendSms - reportContentType: $reportContentType");
        log.info("SendSms - recipient: $recipient");
        log.info("SendSms - text: $text");
        log.info("SendSms - isInteractive: $isInteractive");
        log.info("SendSms - CONTRACT_NUM: $CONTRACT_NUM");
        log.info("SendSms - CUID: $CUID");
        log.info("SendSms - SAS_SMS_TYPE: $SAS_SMS_TYPE");
        log.info("SendSms - SMS_FLASH: $SMS_FLASH");

        List<String> emptyFields = new ArrayList<>()
        if (!externalId) {
            emptyFields.add("externalId")
        }
        if (!systemCode) {
            emptyFields.add("systemCode")
        }
        if (!messageCode) {
            emptyFields.add("messageCode")
        }
        if (!text) {
            emptyFields.add("text")
        }
        if (!recipient) {
            emptyFields.add("recipient")
        }
        if (!emptyFields.isEmpty()) {
            errorMessage = "Following mandatory fields are null or empty: " + String.join(", ", emptyFields)
            return
        }

        PropertiesResponse propertiesResponse = PropertiesUtils.getProperties(CONFIG_FILE)
        if (propertiesResponse.getProperties() == null) {
            status = propertiesResponse.getStatus().getStatus();
            errorMessage = propertiesResponse.getErrorMessage();
            return;
        }
        Properties config = propertiesResponse.getProperties();

        String host = config.getProperty("rabbitmq.host");
        String routingKey = config.getProperty("rabbitmq.sms-routing-key");
        String username = config.getProperty("rabbitmq.username");
        String password = config.getProperty("rabbitmq.password");
        String virtualHost = config.getProperty("rabbitmq.virtual-host");
        String exchange = config.getProperty("rabbitmq.exchange");

        log.info("SendSms - RabbitMQ configuration: host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange")
        Message message = new Message()

        message.setExternalId(externalId)
        message.setSystemCode(systemCode)
        message.setMessageCode(messageCode)
        if (expires instanceof GregorianCalendar) {
            message.setExpires(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(((GregorianCalendar) expires).toZonedDateTime()))
        }
        message.setPriority(priority)
        message.setReportLevel(reportLevel)
        message.setReportContentType(reportContentType)
        message.setRecipient(recipient)
        message.setText(text)
        message.setIsInteractive(isInteractive)
        List<Attribute> attributes = new ArrayList<Attribute>();
        if (CONTRACT_NUM) attributes.add(new Attribute(type: "CONTRACT_NUM", value: CONTRACT_NUM))
        if (CUID) attributes.add(new Attribute(type: "CUID", value: CUID.toString()))
        if (SAS_SMS_TYPE) attributes.add(new Attribute(type: "SAS_SMS_TYPE", value: SAS_SMS_TYPE.toString()))
        if (SMS_FLASH) attributes.add(new Attribute(type: "SMS_FLASH", value: SMS_FLASH))
        message.setAttributes(attributes)

        MessageWrapper messageWrapper = new MessageWrapper()
        messageWrapper.setMessage(Arrays.asList(message))

        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String jsonMessage = mapper.writeValueAsString(messageWrapper)
        log.info("SendSms - Serialized message:" + jsonMessage)

        Map<String, Object> headerMap = new HashMap<String, Object>();
        headerMap.put("SYSTEM_CODE", systemCode);
        headerMap.put("REQUEST_ID", externalId);
        headerMap.put("priority", 0);
        headerMap.put("cuid", CUID.toString());
        headerMap.put("CorrelationID", externalId)
        headerMap.put("Type", "JMSType")
        BasicProperties messageProperties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .headers(headerMap)
                .build();

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        Connection connection;
        try {
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            channel.basicPublish(exchange, routingKey, messageProperties, jsonMessage.getBytes(StandardCharsets.UTF_8));
            log.info("SendSms - Message sent successfully");
            requestReplyLog.info("SendSms - Sent message: $jsonMessage to RabbitMQ (host = $host, routingKey = $routingKey, virtualHost = $virtualHost, exchange = $exchange)")
            status = "OK";
        } catch (Exception e) {
            log.error("SendSms - Failed to send message: " + e.getMessage())
            status = "ERROR";
            throw e;
        } finally {
            if (connection != null) {
                connection.close()
            }
        }
    }
}

class MessageWrapper implements Serializable {
    List<Message> message;
}

class Message implements Serializable {
    String externalId;
    String systemCode;
    String messageCode;
    String expires;
    List<Attribute> attributes;
    String priority;
    String reportLevel;
    String reportContentType;
    String recipient;
    String text;
    Boolean isInteractive;
}

class Attribute implements Serializable {
    String type;
    String value;
}
