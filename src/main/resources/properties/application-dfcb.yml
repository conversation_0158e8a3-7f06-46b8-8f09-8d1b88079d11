logging:
  level:
    ROOT: INFO
    com.glowbyte: TRACE

facebook:
  access-token: EAAPHbZCZATjOEBO7wSZCDLZB4LfZBbjrWtHfw9xWHBEoS1WSQwZAebaQjyRvRchmPVL0dMYdzsMRVajXAgxggYowznW6UZC7ZCErwRzZApCeYmsVtU3dghLBpgoaN6wVFXHpmTrgnNi8kvprSyD66buMOZACC4UJSU1PHTvbxYMMgVZCcRUyOUNfZBksOT6xlsGNvxxM
  ad-account-id: ***************
  appsecret: 727819a2f2aaa558723561d8c04e669c
  proxy:
    host: proxy-server.homecredit.vn
    port: 8080


hc:
  input:
    procedure: MA_TEMP.CORE_PCG.GET_COMMUNICATION_TABLE
    batch-size: 100
    max-iterations: -1
  output.procedure: MA_TEMP.CORE_PCG.FINISH_COMMUNICATION

  ads:
    select-audience-users: SELECT EMAIL, PHONE_NUMBER, SEGMENT_NAME, ACTION FROM {inTable} WHERE BATCH_ID = {batchId}
    update-audience-load: UPDATE INT_COMMUNICATION.FCB_AUDIENCE_LOAD SET JOB_ID = :JOB_ID , JOB_STATUS = :JOB_STATUS, UPDATED_DTTM = SYSTIMESTAMP WHERE DISP_ACTION_ID = :ACTION_ID AND BATCH_ID = :BATCH_ID AND SEGMENT_NAME = :SEGMENT_NAME
    log-procedure: ma_temp.common_pcg.log
    action: D
    area-code: 84
    batch-payload: 100 # This CANT be greater than 100

spring:
  main:
    web-application-type: none

resilience4j.retry:
  instances:
    callFacebook:
      maxAttempts: 3
      waitDuration: 5s
