logging:
  level:
    ROOT: INFO
    com.glowbyte: TRACE

google:
  client-id: 984659830264-jp8git9akc07ucsqabmll9pneqa9b2df.apps.googleusercontent.com
  client-secret: 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
  dev-token: hF8uXi1sB-isLNHq3N4P9A
  refresh-token: 1//04M9dDDwF5HMpCgYIARAAGAQSNwF-L9Ir7_U08F0FptP3NOqxmJ9ph5A40B7fzh7cuZLYqZ79q8dDDd1A7a24FSART1kgzbDhLPg
  login-client-id: 1698640435

hc:
  input:
    procedure: MA_TEMP.CORE_PCG.GET_COMMUNICATION_TABLE
    batch-size: 100
    max-iterations: -1
  output.procedure: MA_TEMP.CORE_PCG.FINISH_COMMUNICATION

  ads:
    select-unfinished-jobs: SELECT JOB_ID FROM INT_COMMUNICATION.GGL_AUDIENCE_LOAD WHERE JOB_STATUS = 'PROCESSING' OR JOB_STATUS = 'PENDING'
    update-unfinished-jobs: UPDATE INT_COMMUNICATION.GGL_AUDIENCE_LOAD SET JOB_STATUS = :JOB_STATUS, UPDATED_DTTM = SYSTIMESTAMP WHERE JOB_ID = :JOB_ID
    area-code: 84

spring:
  main:
    web-application-type: none