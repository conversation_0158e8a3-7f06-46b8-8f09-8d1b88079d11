logging:
  level:
    ROOT: INFO
    com.glowbyte: TRACE

google:
  client-id: 984659830264-jp8git9akc07ucsqabmll9pneqa9b2df.apps.googleusercontent.com
  client-secret: 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
  dev-token: hF8uXi1sB-isLNHq3N4P9A
  refresh-token: *********************************************************************************************************************************************************************************************************************************
  login-client-id: 1698640435

hc:
  input:
    procedure: MA_TEMP.CORE_PCG.GET_COMMUNICATION_TABLE
    batch-size: 100
    max-iterations: -1
  output.procedure: MA_TEMP.CORE_PCG.FINISH_COMMUNICATION

  ads:
    select-audience-users: SELECT EMAIL, PHONE_NUMBER, SEGMENT_NAME, ACTION FROM {inTable} WHERE BATCH_ID = {batchId}
    update-audience-load: UPDATE INT_COMMUNICATION.GGL_AUDIENCE_LOAD SET JOB_ID = :JOB_ID , JOB_STATUS = :JOB_STATUS, UPDATED_DTTM = SYSTIMESTAMP WHERE DISP_ACTION_ID = :ACTION_ID AND BATCH_ID = :BATCH_ID AND SEGMENT_NAME = :SEGMENT_NAME
    log-procedure: ma_temp.common_pcg.log
    action: A
    area-code: 84
    batch-payload: 50000 # Please Note that maximum batch of Google is 100,000 IDENTIFIER, and one user can have 2 identifier. This value cant exceed 50,000

spring:
  main:
    web-application-type: none
