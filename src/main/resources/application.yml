spring:
  kafka:
    bootstrap-servers: cpkafka01-ph00c1.ph.infra:9092,cpkafka02-ph00c1.ph.infra:9092,cpkafka03-ph00c1.ph.infra:9092
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: false
      group-id: kafka-transform
      max-poll-records: 1000
      key-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    properties:
      schema.registry.url: https://schema-registry-ph00c1.ph.infra:8081
      basic.auth.credentials.source: USER_INFO
      basic.auth.user.info: OFS_User:OFS_User
      specific.avro.reader: true
      security:
        protocol: SASL_SSL
      sasl:
        mechanism: SCRAM-SHA-256
        jaas:
          config: org.apache.kafka.common.security.scram.ScramLoginModule required username="SAS_User" password="SAS_User";
kafka:
  lor-sales-quote:
    enabled: false
    input-topic: lor.sales-quote-data.v2
    output-topic: sas.sales-quote-data.v1
  am-event-info:
    enabled: true
    input-topic: am.event-info.v1
    output-topic: sas.transformation.v1