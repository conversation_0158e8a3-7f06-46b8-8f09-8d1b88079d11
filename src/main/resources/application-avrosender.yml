# this comment just for check
logging:
  level:
    ROOT: INFO
    com.glowbyte: INFO
hc:
  avrosender:
    select-string: SELECT, o.* FROM {} o
    header-source: https://sas.vn.infra
    tables:
      - name: ESP_USER.CI_CONTACT_HISTORIES_JOURNAL
        kafka-topic: sas.cdm.contact-history.v2
        header-data-schema: https://schema-registry-vn00c1.vn.infra:8081/schemas/ids/420
        header-type: net.homecredit.tech.reconciliation.v1
        header-time: JOURNAL_DTTM #!!! tady zmenit
        header-partition-key: JOURNAL_ID #!!! tady zmenit
        header-key: key #!!! tady zmenit
        header-subject: KAFKA_TOPIC #!!! tady zmenit
        class-name: net.homecredit.sas.event.cdm.contact_history.v1.ContactHistory
        mapping: "{\"eventName\":\"JOURNAL_NAME:String\",\"eventTimestamp\":\"JOURNAL_DTTM:DateTimeLong\",\"eventId\":\"JOURNAL_ID:Long\",\"cellPackageSk\":\"CELL_PACKAGE_SK:Long\",\"contactDttm\":\"CONTACT_DTTM:DateTimeLong\",\"contactDt\":\"CONTACT_DT:DateTimeLong\",\"contactHistoryStatusCd\":\"CONTACT_HISTORY_STATUS_CD:String\",\"packageHashVal\":\"PACKAGE_HASH_VAL:String\",\"optimizationBackfillFlg\":\"OPTIMIZATION_BACKFILL_FLG:String\",\"externalContactInfoId1\":\"EXTERNAL_CONTACT_INFO_ID1:String\",\"externalContactInfoId2\":\"EXTERNAL_CONTACT_INFO_ID2:String\",\"responseTrackingCd\":\"RESPONSE_TRACKING_CD:String\",\"idCuid\":\"ID_CUID:Long\",\"identifierHashVal\":\"IDENTIFIER_HASH_VAL:String\",\"offerId\":\"OFFER_ID:String\",\"genericResponse\":\"GENERIC_RESPONSE:String\",\"businessGoalFlg\":\"BUSINESS_GOAL_FLG:Long\",\"behavioralGoalFlg\":\"BEHAVIORAL_GOAL_FLG:Long\",\"responseDttm\":\"RESPONSE_DTTM:DateTimeLong\",\"deliveredFlg\":\"DELIVERED_FLG:String\",\"contactedFlg\":\"CONTACTED_FLG:String\",\"deliveredDttm\":\"DELIVERED_DTTM:DateTimeLong\",\"contactedDttm\":\"CONTACTED_DTTM:DateTimeLong\",\"responseNm\":\"RESPONSE_NM:String\",\"chId\":\"CH_ID:Long\",\"deactivationReasonId\":\"DEACTIVATION_REASON_ID:Long\",\"updateDttm\":\"UPDATE_DTTM:DateTimeLong\",\"ilCommunicationId\":\"IL_COMMUNICATION_ID:Long\",\"createdDttm\":\"CREATED_DTTM:DateTimeLong\",\"treatmentSk\":\"TREATMENT_SK:Long\",\"ci360ResponseTrackingCd\":\"CI360_RESPONSE_TRACKING_CD:String\",\"ci360TaskId\":\"CI360_TASK_ID:String\",\"ci360taksIdVersion\":\"CI360_TASK_ID_VERSION:String\",\"ci360CreativeId\":\"CI360_CREATIVE_ID:String\",\"ci360ContactId\":\"CI360_CONTACT_ID:String\",\"ci360IdentityId\":\"CI360_IDENTITY_ID:String\",\"limitId\":\"LIMIT_ID:String\",\"contactHistoryStatusDesc\":\"CONTACT_HISTORY_STATUS_DESC:String\",\"responseText\":\"RESPONSE_TEXT:String\",\"responseTimestamp\":\"RESPONSE_TIMESTAMP:String\",\"crmSegment\":\"CRM_SEGMENT:String\",\"mflcCrm\":\"MFLC_CRM:Long\",\"codeRiskGrade\":\"CODE_RISK_GRADE:String\",\"customInfo1\":\"CUSTOM_INFO1:String\",\"customInfo2\":\"CUSTOM_INFO2:String\",\"customInfo3\":\"CUSTOM_INFO3:String\",\"customInfo4\":\"CUSTOM_INFO4:String\",\"customInfo5\":\"CUSTOM_INFO5:String\"}"
  input:
    procedure: MA_TEMP.CORE_PCG.GET_COMMUNICATION_TABLE
    batch-size: 100
    max-iterations: -1
  output:
    procedure: MA_TEMP.CORE_PCG.FINISH_COMMUNICATION
  task:
    procedure: MA_TEMP.COMMUNICATION_PCG.SEND_TO_NGA
spring:
  main:
    web-application-type: none
  datasource:
    url: **************************************************************
    username: ESP_USER
    password: ESP_USER123
    hikari:
      maximum-pool-size: 1
  kafka:
    producer:
      key-serializer: org.apache.kafka.common.serialization.ByteArraySerializer
      value-serializer: org.apache.kafka.common.serialization.ByteArraySerializer
    bootstrap-servers: cpkafka01-vn00c1.vn.infra:9092,cpkafka02-vn00c1.vn.infra:9092,cpkafka03-vn00c1.vn.infra:9092
    properties:
      security:
        protocol: SASL_SSL
      sasl:
        mechanism: SCRAM-SHA-256
        jaas:
          config: org.apache.kafka.common.security.scram.ScramLoginModule required username="SAS_User" password="SAS_User";

