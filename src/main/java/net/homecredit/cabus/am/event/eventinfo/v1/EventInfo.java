/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package net.homecredit.cabus.am.event.eventinfo.v1;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

/** Event info message */
@org.apache.avro.specific.AvroGenerated
public class EventInfo extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -7380458855569508682L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"EventInfo\",\"namespace\":\"net.homecredit.cabus.am.event.eventinfo.v1\",\"doc\":\"Event info message\",\"fields\":[{\"name\":\"accountStatus\",\"type\":[\"null\",\"string\"],\"doc\":\"Account status\",\"default\":null},{\"name\":\"accountType\",\"type\":[\"null\",\"string\"],\"doc\":\"Account type\",\"default\":null},{\"name\":\"authorization\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Authorization\",\"namespace\":\"net.homecredit.cabus.am.common\",\"doc\":\"Authorization detail\",\"fields\":[{\"name\":\"authorizationDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Authorization date\",\"default\":null},{\"name\":\"authorizationMode\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization mode\",\"default\":null},{\"name\":\"attributes\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Attributes\",\"fields\":[{\"name\":\"entries\",\"type\":{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Entry\",\"fields\":[{\"name\":\"key\",\"type\":\"string\"},{\"name\":\"value\",\"type\":\"string\"}]}}}]}],\"doc\":\"Authorization attributes\",\"default\":null},{\"name\":\"billingAmount\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Money\",\"fields\":[{\"name\":\"value\",\"type\":{\"type\":\"bytes\",\"logicalType\":\"decimal\",\"precision\":19,\"scale\":2}},{\"name\":\"currency\",\"type\":\"string\"}]}],\"doc\":\"Authorization billing amount\",\"default\":null},{\"name\":\"cancelledBillingAmount\",\"type\":[\"null\",\"Money\"],\"doc\":\"Authorization cancelled billing amount\",\"default\":null},{\"name\":\"isClosureAuthorization\",\"type\":[\"null\",\"boolean\"],\"doc\":\"Closure authorization flag\",\"default\":null},{\"name\":\"preliminaryConfirmation\",\"type\":[\"null\",\"boolean\"],\"doc\":\"Preliminary confirmation flag\",\"default\":null},{\"name\":\"accountNumber\",\"type\":[\"null\",\"long\"],\"doc\":\"Authorization account number\",\"default\":null},{\"name\":\"cardUsage\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"CardUsageInfo\",\"doc\":\"Card detail\",\"fields\":[{\"name\":\"MCC\",\"type\":[\"null\",\"string\"],\"doc\":\"MCC\",\"default\":null},{\"name\":\"acquirerID\",\"type\":[\"null\",\"string\"],\"doc\":\"Acquirer ID\",\"default\":null},{\"name\":\"cardAcceptorCity\",\"type\":[\"null\",\"string\"],\"doc\":\"Card acceptor city\",\"default\":null},{\"name\":\"cardAcceptorCountryCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Card acceptor country code\",\"default\":null},{\"name\":\"cardAcceptorId\",\"type\":[\"null\",\"string\"],\"doc\":\"Card acceptor ID\",\"default\":null},{\"name\":\"cardAcceptorName\",\"type\":[\"null\",\"string\"],\"doc\":\"Card acceptor name\",\"default\":null},{\"name\":\"cardAcceptorState\",\"type\":[\"null\",\"string\"],\"doc\":\"Card acceptor state\",\"default\":null},{\"name\":\"cardAcceptorZipCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Card acceptor ZIP code\",\"default\":null},{\"name\":\"locationType\",\"type\":[\"null\",\"string\"],\"doc\":\"Terminal location type\",\"default\":null},{\"name\":\"ownership\",\"type\":[\"null\",\"string\"],\"doc\":\"Terminal ownership\",\"default\":null},{\"name\":\"terminalID\",\"type\":[\"null\",\"string\"],\"doc\":\"Terminal ID\",\"default\":null},{\"name\":\"terminalTokenNumber\",\"type\":[\"null\",\"long\"],\"doc\":\"Terminal token number\",\"default\":null},{\"name\":\"tokenRequestorName\",\"type\":[\"null\",\"string\"],\"doc\":\"Terminal requestor name\",\"default\":null},{\"name\":\"type\",\"type\":[\"null\",\"string\"],\"doc\":\"Terminal type\",\"default\":null}]}],\"doc\":\"Authorization card and terminal detail\",\"default\":null},{\"name\":\"chargeTransactionInPast\",\"type\":\"boolean\",\"doc\":\"Charged in past flag\"},{\"name\":\"conversionRate\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ConversionRate\",\"doc\":\"Authorization conversion rate detail\",\"fields\":[{\"name\":\"value\",\"type\":{\"type\":\"bytes\",\"logicalType\":\"decimal\",\"precision\":20,\"scale\":6}}],\"default\":\"null\"}],\"doc\":\"Authorization conversion rate\",\"default\":null},{\"name\":\"fundsSenderAccount\",\"type\":[\"null\",\"string\"],\"doc\":\"Funds sender account\",\"default\":null},{\"name\":\"fundsSenderAddress\",\"type\":[\"null\",\"string\"],\"doc\":\"Funds sender adress\",\"default\":null},{\"name\":\"fundsSenderCity\",\"type\":[\"null\",\"string\"],\"doc\":\"Funds sender city\",\"default\":null},{\"name\":\"fundsSenderCountryCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Funds sender country code\",\"default\":null},{\"name\":\"fundsSenderName\",\"type\":[\"null\",\"string\"],\"doc\":\"Funds sender name\",\"default\":null},{\"name\":\"pcid\",\"type\":[\"null\",\"long\"],\"doc\":\"Authorization payment card ID\",\"default\":null},{\"name\":\"preferredAllocationType\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization preferred allocation type\",\"default\":null},{\"name\":\"serviceCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization service code\",\"default\":null},{\"name\":\"transactionTypeVariant\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction authorization type variant code\",\"default\":null},{\"name\":\"truncatedPan\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization truncated PAN number\",\"default\":null},{\"name\":\"note\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction authorization note\",\"default\":null},{\"name\":\"originalTransactionDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Original authorization date\",\"default\":null},{\"name\":\"paymentType\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization payment type\",\"default\":null},{\"name\":\"transactionAmount\",\"type\":[\"null\",\"Money\"],\"doc\":\"Authorization transaction amount\",\"default\":null},{\"name\":\"transactionDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Authorization transaction date\",\"default\":null},{\"name\":\"transactionSourceId\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"TransactionSourceId\",\"fields\":[{\"name\":\"sourceSystem\",\"type\":\"string\"},{\"name\":\"sourceTxId\",\"type\":\"string\"}]}],\"doc\":\"Authorization source ID\",\"default\":null},{\"name\":\"type\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization transaction type\",\"default\":null},{\"name\":\"accountCurrency\",\"type\":[\"null\",\"string\"],\"doc\":\"Authorization account currency code\",\"default\":null}]}],\"doc\":\"Authorization overview\",\"default\":null},{\"name\":\"transaction\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"ConfirmedTransaction\",\"namespace\":\"net.homecredit.cabus.am.common\",\"doc\":\"Confirmed transaction detail\",\"fields\":[{\"name\":\"authorizationEventCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction authorization event code\",\"default\":null},{\"name\":\"accountNumber\",\"type\":[\"null\",\"long\"],\"doc\":\"Transaction account number\",\"default\":null},{\"name\":\"attributes\",\"type\":[\"null\",\"Attributes\"],\"doc\":\"Transaction attributes\",\"default\":null},{\"name\":\"cardUsage\",\"type\":[\"null\",\"CardUsageInfo\"],\"doc\":\"Transaction card usage\",\"default\":null},{\"name\":\"chargeTransactionInPast\",\"type\":\"boolean\",\"doc\":\"Transaction charged in past flag\"},{\"name\":\"conversionRate\",\"type\":[\"null\",\"ConversionRate\"],\"doc\":\"Transaction conversion rate\"},{\"name\":\"fundsSenderAccount\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction funds sender account\",\"default\":null},{\"name\":\"fundsSenderAddress\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction funds sender adress\",\"default\":null},{\"name\":\"fundsSenderCity\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction funds sender city\",\"default\":null},{\"name\":\"fundsSenderCountryCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction funds sender country code\",\"default\":null},{\"name\":\"fundsSenderName\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction funds sender name\",\"default\":null},{\"name\":\"pcid\",\"type\":[\"null\",\"long\"],\"doc\":\"Transaction payment card ID\",\"default\":null},{\"name\":\"preferredAllocationType\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction preferred allocation type\",\"default\":null},{\"name\":\"serviceCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction service code\",\"default\":null},{\"name\":\"transactionTypeVariant\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction type variant\",\"default\":null},{\"name\":\"truncatedPan\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction truncated PAN number\",\"default\":null},{\"name\":\"note\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction note\",\"default\":null},{\"name\":\"originalTransactionDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Original transaction date\",\"default\":null},{\"name\":\"paymentType\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction payment type\",\"default\":null},{\"name\":\"transactionAmount\",\"type\":[\"null\",\"Money\"],\"doc\":\"Transaction amount\",\"default\":null},{\"name\":\"transactionDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Transaction date\",\"default\":null},{\"name\":\"transactionSourceId\",\"type\":[\"null\",\"TransactionSourceId\"],\"doc\":\"Transaction source ID\",\"default\":null},{\"name\":\"type\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction type\",\"default\":null},{\"name\"",":\"accountCurrency\",\"type\":[\"null\",\"string\"],\"doc\":\"Transaction currency code\",\"default\":null}]}],\"doc\":\"Transaction detail\",\"default\":null},{\"name\":\"usedWaivers\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"UsedWaiver\",\"namespace\":\"net.homecredit.cabus.am.account.to.common\",\"fields\":[{\"name\":\"tariffItemTypeCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Waived tariff item type code\",\"default\":null},{\"name\":\"waivedAmount\",\"type\":[\"null\",\"net.homecredit.cabus.am.common.Money\"],\"doc\":\"Waived amount\",\"default\":null},{\"name\":\"waiverCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Waiver code\",\"default\":null},{\"name\":\"accountCurrency\",\"type\":[\"null\",\"string\"],\"doc\":\"Waived amount currency\",\"default\":null}]}}],\"doc\":\"Used waivers\",\"default\":null},{\"name\":\"transactions\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"string\"}],\"doc\":\"Transaction list\",\"default\":null},{\"name\":\"accountNumber\",\"type\":[\"null\",\"long\"],\"doc\":\"Account number\",\"default\":null},{\"name\":\"blockingReason\",\"type\":[\"null\",\"string\"],\"doc\":\"Blocking reason\",\"default\":null},{\"name\":\"cancellationReason\",\"type\":[\"null\",\"string\"],\"doc\":\"Cancellation reason\",\"default\":null},{\"name\":\"cancelled\",\"type\":\"boolean\",\"doc\":\"Cancellation flag\"},{\"name\":\"cancelledDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Cancellation date\",\"default\":null},{\"name\":\"eventCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Event code\",\"default\":null},{\"name\":\"eventDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Event date\",\"default\":null},{\"name\":\"eventTimestamp\",\"type\":[\"null\",{\"type\":\"long\",\"logicalType\":\"local-timestamp-millis\"}],\"doc\":\"Event timestamp\",\"default\":null},{\"name\":\"eventTypeEnum\",\"type\":[\"null\",\"string\"],\"doc\":\"Event type\",\"default\":null},{\"name\":\"installmentPlanCode\",\"type\":[\"null\",\"string\"],\"doc\":\"Instalment plan code\",\"default\":null},{\"name\":\"note\",\"type\":[\"null\",\"string\"],\"doc\":\"Event note\",\"default\":null},{\"name\":\"originalEventDate\",\"type\":[\"null\",{\"type\":\"int\",\"logicalType\":\"date\"}],\"doc\":\"Original event date\",\"default\":null},{\"name\":\"pcid\",\"type\":[\"null\",\"long\"],\"doc\":\"Payment card ID\",\"default\":null},{\"name\":\"processingState\",\"type\":[\"null\",\"string\"],\"doc\":\"Event processing date\",\"default\":null},{\"name\":\"transactionSourceId\",\"type\":[\"null\",\"net.homecredit.cabus.am.common.TransactionSourceId\"],\"doc\":\"Transaction source ID\",\"default\":null},{\"name\":\"truncatedPan\",\"type\":[\"null\",\"string\"],\"doc\":\"Truncated PAN number\",\"default\":null},{\"name\":\"accountCurrency\",\"type\":[\"null\",\"string\"],\"doc\":\"Account currency code\",\"default\":null},{\"name\":\"attributes\",\"type\":[\"null\",\"net.homecredit.cabus.am.common.Attributes\"],\"doc\":\"Event attributes\",\"default\":null}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();
  static {
    MODEL$.addLogicalTypeConversion(new org.apache.avro.data.TimeConversions.DateConversion());
    MODEL$.addLogicalTypeConversion(new org.apache.avro.data.TimeConversions.LocalTimestampMillisConversion());
    MODEL$.addLogicalTypeConversion(new org.apache.avro.Conversions.DecimalConversion());
  }

  private static final BinaryMessageEncoder<EventInfo> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<EventInfo> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<EventInfo> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<EventInfo> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<EventInfo> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this EventInfo to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a EventInfo from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a EventInfo instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static EventInfo fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** Account status */
  public java.lang.CharSequence accountStatus;
  /** Account type */
  public java.lang.CharSequence accountType;
  /** Authorization overview */
  public net.homecredit.cabus.am.common.Authorization authorization;
  /** Transaction detail */
  public net.homecredit.cabus.am.common.ConfirmedTransaction transaction;
  /** Used waivers */
  public java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> usedWaivers;
  /** Transaction list */
  public java.util.List<java.lang.CharSequence> transactions;
  /** Account number */
  public java.lang.Long accountNumber;
  /** Blocking reason */
  public java.lang.CharSequence blockingReason;
  /** Cancellation reason */
  public java.lang.CharSequence cancellationReason;
  /** Cancellation flag */
  public boolean cancelled;
  /** Cancellation date */
  public java.time.LocalDate cancelledDate;
  /** Event code */
  public java.lang.CharSequence eventCode;
  /** Event date */
  public java.time.LocalDate eventDate;
  /** Event timestamp */
  public java.time.LocalDateTime eventTimestamp;
  /** Event type */
  public java.lang.CharSequence eventTypeEnum;
  /** Instalment plan code */
  public java.lang.CharSequence installmentPlanCode;
  /** Event note */
  public java.lang.CharSequence note;
  /** Original event date */
  public java.time.LocalDate originalEventDate;
  /** Payment card ID */
  public java.lang.Long pcid;
  /** Event processing date */
  public java.lang.CharSequence processingState;
  /** Transaction source ID */
  public net.homecredit.cabus.am.common.TransactionSourceId transactionSourceId;
  /** Truncated PAN number */
  public java.lang.CharSequence truncatedPan;
  /** Account currency code */
  public java.lang.CharSequence accountCurrency;
  /** Event attributes */
  public net.homecredit.cabus.am.common.Attributes attributes;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public EventInfo() {}

  /**
   * All-args constructor.
   * @param accountStatus Account status
   * @param accountType Account type
   * @param authorization Authorization overview
   * @param transaction Transaction detail
   * @param usedWaivers Used waivers
   * @param transactions Transaction list
   * @param accountNumber Account number
   * @param blockingReason Blocking reason
   * @param cancellationReason Cancellation reason
   * @param cancelled Cancellation flag
   * @param cancelledDate Cancellation date
   * @param eventCode Event code
   * @param eventDate Event date
   * @param eventTimestamp Event timestamp
   * @param eventTypeEnum Event type
   * @param installmentPlanCode Instalment plan code
   * @param note Event note
   * @param originalEventDate Original event date
   * @param pcid Payment card ID
   * @param processingState Event processing date
   * @param transactionSourceId Transaction source ID
   * @param truncatedPan Truncated PAN number
   * @param accountCurrency Account currency code
   * @param attributes Event attributes
   */
  public EventInfo(java.lang.CharSequence accountStatus, java.lang.CharSequence accountType, net.homecredit.cabus.am.common.Authorization authorization, net.homecredit.cabus.am.common.ConfirmedTransaction transaction, java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> usedWaivers, java.util.List<java.lang.CharSequence> transactions, java.lang.Long accountNumber, java.lang.CharSequence blockingReason, java.lang.CharSequence cancellationReason, java.lang.Boolean cancelled, java.time.LocalDate cancelledDate, java.lang.CharSequence eventCode, java.time.LocalDate eventDate, java.time.LocalDateTime eventTimestamp, java.lang.CharSequence eventTypeEnum, java.lang.CharSequence installmentPlanCode, java.lang.CharSequence note, java.time.LocalDate originalEventDate, java.lang.Long pcid, java.lang.CharSequence processingState, net.homecredit.cabus.am.common.TransactionSourceId transactionSourceId, java.lang.CharSequence truncatedPan, java.lang.CharSequence accountCurrency, net.homecredit.cabus.am.common.Attributes attributes) {
    this.accountStatus = accountStatus;
    this.accountType = accountType;
    this.authorization = authorization;
    this.transaction = transaction;
    this.usedWaivers = usedWaivers;
    this.transactions = transactions;
    this.accountNumber = accountNumber;
    this.blockingReason = blockingReason;
    this.cancellationReason = cancellationReason;
    this.cancelled = cancelled;
    this.cancelledDate = cancelledDate;
    this.eventCode = eventCode;
    this.eventDate = eventDate;
    this.eventTimestamp = eventTimestamp;
    this.eventTypeEnum = eventTypeEnum;
    this.installmentPlanCode = installmentPlanCode;
    this.note = note;
    this.originalEventDate = originalEventDate;
    this.pcid = pcid;
    this.processingState = processingState;
    this.transactionSourceId = transactionSourceId;
    this.truncatedPan = truncatedPan;
    this.accountCurrency = accountCurrency;
    this.attributes = attributes;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return accountStatus;
    case 1: return accountType;
    case 2: return authorization;
    case 3: return transaction;
    case 4: return usedWaivers;
    case 5: return transactions;
    case 6: return accountNumber;
    case 7: return blockingReason;
    case 8: return cancellationReason;
    case 9: return cancelled;
    case 10: return cancelledDate;
    case 11: return eventCode;
    case 12: return eventDate;
    case 13: return eventTimestamp;
    case 14: return eventTypeEnum;
    case 15: return installmentPlanCode;
    case 16: return note;
    case 17: return originalEventDate;
    case 18: return pcid;
    case 19: return processingState;
    case 20: return transactionSourceId;
    case 21: return truncatedPan;
    case 22: return accountCurrency;
    case 23: return attributes;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: accountStatus = (java.lang.CharSequence)value$; break;
    case 1: accountType = (java.lang.CharSequence)value$; break;
    case 2: authorization = (net.homecredit.cabus.am.common.Authorization)value$; break;
    case 3: transaction = (net.homecredit.cabus.am.common.ConfirmedTransaction)value$; break;
    case 4: usedWaivers = (java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver>)value$; break;
    case 5: transactions = (java.util.List<java.lang.CharSequence>)value$; break;
    case 6: accountNumber = (java.lang.Long)value$; break;
    case 7: blockingReason = (java.lang.CharSequence)value$; break;
    case 8: cancellationReason = (java.lang.CharSequence)value$; break;
    case 9: cancelled = (java.lang.Boolean)value$; break;
    case 10: cancelledDate = (java.time.LocalDate)value$; break;
    case 11: eventCode = (java.lang.CharSequence)value$; break;
    case 12: eventDate = (java.time.LocalDate)value$; break;
    case 13: eventTimestamp = (java.time.LocalDateTime)value$; break;
    case 14: eventTypeEnum = (java.lang.CharSequence)value$; break;
    case 15: installmentPlanCode = (java.lang.CharSequence)value$; break;
    case 16: note = (java.lang.CharSequence)value$; break;
    case 17: originalEventDate = (java.time.LocalDate)value$; break;
    case 18: pcid = (java.lang.Long)value$; break;
    case 19: processingState = (java.lang.CharSequence)value$; break;
    case 20: transactionSourceId = (net.homecredit.cabus.am.common.TransactionSourceId)value$; break;
    case 21: truncatedPan = (java.lang.CharSequence)value$; break;
    case 22: accountCurrency = (java.lang.CharSequence)value$; break;
    case 23: attributes = (net.homecredit.cabus.am.common.Attributes)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'accountStatus' field.
   * @return Account status
   */
  public java.lang.CharSequence getAccountStatus() {
    return accountStatus;
  }


  /**
   * Sets the value of the 'accountStatus' field.
   * Account status
   * @param value the value to set.
   */
  public void setAccountStatus(java.lang.CharSequence value) {
    this.accountStatus = value;
  }

  /**
   * Gets the value of the 'accountType' field.
   * @return Account type
   */
  public java.lang.CharSequence getAccountType() {
    return accountType;
  }


  /**
   * Sets the value of the 'accountType' field.
   * Account type
   * @param value the value to set.
   */
  public void setAccountType(java.lang.CharSequence value) {
    this.accountType = value;
  }

  /**
   * Gets the value of the 'authorization' field.
   * @return Authorization overview
   */
  public net.homecredit.cabus.am.common.Authorization getAuthorization() {
    return authorization;
  }


  /**
   * Sets the value of the 'authorization' field.
   * Authorization overview
   * @param value the value to set.
   */
  public void setAuthorization(net.homecredit.cabus.am.common.Authorization value) {
    this.authorization = value;
  }

  /**
   * Gets the value of the 'transaction' field.
   * @return Transaction detail
   */
  public net.homecredit.cabus.am.common.ConfirmedTransaction getTransaction() {
    return transaction;
  }


  /**
   * Sets the value of the 'transaction' field.
   * Transaction detail
   * @param value the value to set.
   */
  public void setTransaction(net.homecredit.cabus.am.common.ConfirmedTransaction value) {
    this.transaction = value;
  }

  /**
   * Gets the value of the 'usedWaivers' field.
   * @return Used waivers
   */
  public java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> getUsedWaivers() {
    return usedWaivers;
  }


  /**
   * Sets the value of the 'usedWaivers' field.
   * Used waivers
   * @param value the value to set.
   */
  public void setUsedWaivers(java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> value) {
    this.usedWaivers = value;
  }

  /**
   * Gets the value of the 'transactions' field.
   * @return Transaction list
   */
  public java.util.List<java.lang.CharSequence> getTransactions() {
    return transactions;
  }


  /**
   * Sets the value of the 'transactions' field.
   * Transaction list
   * @param value the value to set.
   */
  public void setTransactions(java.util.List<java.lang.CharSequence> value) {
    this.transactions = value;
  }

  /**
   * Gets the value of the 'accountNumber' field.
   * @return Account number
   */
  public java.lang.Long getAccountNumber() {
    return accountNumber;
  }


  /**
   * Sets the value of the 'accountNumber' field.
   * Account number
   * @param value the value to set.
   */
  public void setAccountNumber(java.lang.Long value) {
    this.accountNumber = value;
  }

  /**
   * Gets the value of the 'blockingReason' field.
   * @return Blocking reason
   */
  public java.lang.CharSequence getBlockingReason() {
    return blockingReason;
  }


  /**
   * Sets the value of the 'blockingReason' field.
   * Blocking reason
   * @param value the value to set.
   */
  public void setBlockingReason(java.lang.CharSequence value) {
    this.blockingReason = value;
  }

  /**
   * Gets the value of the 'cancellationReason' field.
   * @return Cancellation reason
   */
  public java.lang.CharSequence getCancellationReason() {
    return cancellationReason;
  }


  /**
   * Sets the value of the 'cancellationReason' field.
   * Cancellation reason
   * @param value the value to set.
   */
  public void setCancellationReason(java.lang.CharSequence value) {
    this.cancellationReason = value;
  }

  /**
   * Gets the value of the 'cancelled' field.
   * @return Cancellation flag
   */
  public boolean getCancelled() {
    return cancelled;
  }


  /**
   * Sets the value of the 'cancelled' field.
   * Cancellation flag
   * @param value the value to set.
   */
  public void setCancelled(boolean value) {
    this.cancelled = value;
  }

  /**
   * Gets the value of the 'cancelledDate' field.
   * @return Cancellation date
   */
  public java.time.LocalDate getCancelledDate() {
    return cancelledDate;
  }


  /**
   * Sets the value of the 'cancelledDate' field.
   * Cancellation date
   * @param value the value to set.
   */
  public void setCancelledDate(java.time.LocalDate value) {
    this.cancelledDate = value;
  }

  /**
   * Gets the value of the 'eventCode' field.
   * @return Event code
   */
  public java.lang.CharSequence getEventCode() {
    return eventCode;
  }


  /**
   * Sets the value of the 'eventCode' field.
   * Event code
   * @param value the value to set.
   */
  public void setEventCode(java.lang.CharSequence value) {
    this.eventCode = value;
  }

  /**
   * Gets the value of the 'eventDate' field.
   * @return Event date
   */
  public java.time.LocalDate getEventDate() {
    return eventDate;
  }


  /**
   * Sets the value of the 'eventDate' field.
   * Event date
   * @param value the value to set.
   */
  public void setEventDate(java.time.LocalDate value) {
    this.eventDate = value;
  }

  /**
   * Gets the value of the 'eventTimestamp' field.
   * @return Event timestamp
   */
  public java.time.LocalDateTime getEventTimestamp() {
    return eventTimestamp;
  }


  /**
   * Sets the value of the 'eventTimestamp' field.
   * Event timestamp
   * @param value the value to set.
   */
  public void setEventTimestamp(java.time.LocalDateTime value) {
    this.eventTimestamp = value;
  }

  /**
   * Gets the value of the 'eventTypeEnum' field.
   * @return Event type
   */
  public java.lang.CharSequence getEventTypeEnum() {
    return eventTypeEnum;
  }


  /**
   * Sets the value of the 'eventTypeEnum' field.
   * Event type
   * @param value the value to set.
   */
  public void setEventTypeEnum(java.lang.CharSequence value) {
    this.eventTypeEnum = value;
  }

  /**
   * Gets the value of the 'installmentPlanCode' field.
   * @return Instalment plan code
   */
  public java.lang.CharSequence getInstallmentPlanCode() {
    return installmentPlanCode;
  }


  /**
   * Sets the value of the 'installmentPlanCode' field.
   * Instalment plan code
   * @param value the value to set.
   */
  public void setInstallmentPlanCode(java.lang.CharSequence value) {
    this.installmentPlanCode = value;
  }

  /**
   * Gets the value of the 'note' field.
   * @return Event note
   */
  public java.lang.CharSequence getNote() {
    return note;
  }


  /**
   * Sets the value of the 'note' field.
   * Event note
   * @param value the value to set.
   */
  public void setNote(java.lang.CharSequence value) {
    this.note = value;
  }

  /**
   * Gets the value of the 'originalEventDate' field.
   * @return Original event date
   */
  public java.time.LocalDate getOriginalEventDate() {
    return originalEventDate;
  }


  /**
   * Sets the value of the 'originalEventDate' field.
   * Original event date
   * @param value the value to set.
   */
  public void setOriginalEventDate(java.time.LocalDate value) {
    this.originalEventDate = value;
  }

  /**
   * Gets the value of the 'pcid' field.
   * @return Payment card ID
   */
  public java.lang.Long getPcid() {
    return pcid;
  }


  /**
   * Sets the value of the 'pcid' field.
   * Payment card ID
   * @param value the value to set.
   */
  public void setPcid(java.lang.Long value) {
    this.pcid = value;
  }

  /**
   * Gets the value of the 'processingState' field.
   * @return Event processing date
   */
  public java.lang.CharSequence getProcessingState() {
    return processingState;
  }


  /**
   * Sets the value of the 'processingState' field.
   * Event processing date
   * @param value the value to set.
   */
  public void setProcessingState(java.lang.CharSequence value) {
    this.processingState = value;
  }

  /**
   * Gets the value of the 'transactionSourceId' field.
   * @return Transaction source ID
   */
  public net.homecredit.cabus.am.common.TransactionSourceId getTransactionSourceId() {
    return transactionSourceId;
  }


  /**
   * Sets the value of the 'transactionSourceId' field.
   * Transaction source ID
   * @param value the value to set.
   */
  public void setTransactionSourceId(net.homecredit.cabus.am.common.TransactionSourceId value) {
    this.transactionSourceId = value;
  }

  /**
   * Gets the value of the 'truncatedPan' field.
   * @return Truncated PAN number
   */
  public java.lang.CharSequence getTruncatedPan() {
    return truncatedPan;
  }


  /**
   * Sets the value of the 'truncatedPan' field.
   * Truncated PAN number
   * @param value the value to set.
   */
  public void setTruncatedPan(java.lang.CharSequence value) {
    this.truncatedPan = value;
  }

  /**
   * Gets the value of the 'accountCurrency' field.
   * @return Account currency code
   */
  public java.lang.CharSequence getAccountCurrency() {
    return accountCurrency;
  }


  /**
   * Sets the value of the 'accountCurrency' field.
   * Account currency code
   * @param value the value to set.
   */
  public void setAccountCurrency(java.lang.CharSequence value) {
    this.accountCurrency = value;
  }

  /**
   * Gets the value of the 'attributes' field.
   * @return Event attributes
   */
  public net.homecredit.cabus.am.common.Attributes getAttributes() {
    return attributes;
  }


  /**
   * Sets the value of the 'attributes' field.
   * Event attributes
   * @param value the value to set.
   */
  public void setAttributes(net.homecredit.cabus.am.common.Attributes value) {
    this.attributes = value;
  }

  /**
   * Creates a new EventInfo RecordBuilder.
   * @return A new EventInfo RecordBuilder
   */
  public static net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder newBuilder() {
    return new net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder();
  }

  /**
   * Creates a new EventInfo RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new EventInfo RecordBuilder
   */
  public static net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder newBuilder(net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder other) {
    if (other == null) {
      return new net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder();
    } else {
      return new net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder(other);
    }
  }

  /**
   * Creates a new EventInfo RecordBuilder by copying an existing EventInfo instance.
   * @param other The existing instance to copy.
   * @return A new EventInfo RecordBuilder
   */
  public static net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder newBuilder(net.homecredit.cabus.am.event.eventinfo.v1.EventInfo other) {
    if (other == null) {
      return new net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder();
    } else {
      return new net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder(other);
    }
  }

  /**
   * RecordBuilder for EventInfo instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<EventInfo>
    implements org.apache.avro.data.RecordBuilder<EventInfo> {

    /** Account status */
    private java.lang.CharSequence accountStatus;
    /** Account type */
    private java.lang.CharSequence accountType;
    /** Authorization overview */
    private net.homecredit.cabus.am.common.Authorization authorization;
    private net.homecredit.cabus.am.common.Authorization.Builder authorizationBuilder;
    /** Transaction detail */
    private net.homecredit.cabus.am.common.ConfirmedTransaction transaction;
    private net.homecredit.cabus.am.common.ConfirmedTransaction.Builder transactionBuilder;
    /** Used waivers */
    private java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> usedWaivers;
    /** Transaction list */
    private java.util.List<java.lang.CharSequence> transactions;
    /** Account number */
    private java.lang.Long accountNumber;
    /** Blocking reason */
    private java.lang.CharSequence blockingReason;
    /** Cancellation reason */
    private java.lang.CharSequence cancellationReason;
    /** Cancellation flag */
    private boolean cancelled;
    /** Cancellation date */
    private java.time.LocalDate cancelledDate;
    /** Event code */
    private java.lang.CharSequence eventCode;
    /** Event date */
    private java.time.LocalDate eventDate;
    /** Event timestamp */
    private java.time.LocalDateTime eventTimestamp;
    /** Event type */
    private java.lang.CharSequence eventTypeEnum;
    /** Instalment plan code */
    private java.lang.CharSequence installmentPlanCode;
    /** Event note */
    private java.lang.CharSequence note;
    /** Original event date */
    private java.time.LocalDate originalEventDate;
    /** Payment card ID */
    private java.lang.Long pcid;
    /** Event processing date */
    private java.lang.CharSequence processingState;
    /** Transaction source ID */
    private net.homecredit.cabus.am.common.TransactionSourceId transactionSourceId;
    private net.homecredit.cabus.am.common.TransactionSourceId.Builder transactionSourceIdBuilder;
    /** Truncated PAN number */
    private java.lang.CharSequence truncatedPan;
    /** Account currency code */
    private java.lang.CharSequence accountCurrency;
    /** Event attributes */
    private net.homecredit.cabus.am.common.Attributes attributes;
    private net.homecredit.cabus.am.common.Attributes.Builder attributesBuilder;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.accountStatus)) {
        this.accountStatus = data().deepCopy(fields()[0].schema(), other.accountStatus);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.accountType)) {
        this.accountType = data().deepCopy(fields()[1].schema(), other.accountType);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.authorization)) {
        this.authorization = data().deepCopy(fields()[2].schema(), other.authorization);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (other.hasAuthorizationBuilder()) {
        this.authorizationBuilder = net.homecredit.cabus.am.common.Authorization.newBuilder(other.getAuthorizationBuilder());
      }
      if (isValidValue(fields()[3], other.transaction)) {
        this.transaction = data().deepCopy(fields()[3].schema(), other.transaction);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (other.hasTransactionBuilder()) {
        this.transactionBuilder = net.homecredit.cabus.am.common.ConfirmedTransaction.newBuilder(other.getTransactionBuilder());
      }
      if (isValidValue(fields()[4], other.usedWaivers)) {
        this.usedWaivers = data().deepCopy(fields()[4].schema(), other.usedWaivers);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.transactions)) {
        this.transactions = data().deepCopy(fields()[5].schema(), other.transactions);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (isValidValue(fields()[6], other.accountNumber)) {
        this.accountNumber = data().deepCopy(fields()[6].schema(), other.accountNumber);
        fieldSetFlags()[6] = other.fieldSetFlags()[6];
      }
      if (isValidValue(fields()[7], other.blockingReason)) {
        this.blockingReason = data().deepCopy(fields()[7].schema(), other.blockingReason);
        fieldSetFlags()[7] = other.fieldSetFlags()[7];
      }
      if (isValidValue(fields()[8], other.cancellationReason)) {
        this.cancellationReason = data().deepCopy(fields()[8].schema(), other.cancellationReason);
        fieldSetFlags()[8] = other.fieldSetFlags()[8];
      }
      if (isValidValue(fields()[9], other.cancelled)) {
        this.cancelled = data().deepCopy(fields()[9].schema(), other.cancelled);
        fieldSetFlags()[9] = other.fieldSetFlags()[9];
      }
      if (isValidValue(fields()[10], other.cancelledDate)) {
        this.cancelledDate = data().deepCopy(fields()[10].schema(), other.cancelledDate);
        fieldSetFlags()[10] = other.fieldSetFlags()[10];
      }
      if (isValidValue(fields()[11], other.eventCode)) {
        this.eventCode = data().deepCopy(fields()[11].schema(), other.eventCode);
        fieldSetFlags()[11] = other.fieldSetFlags()[11];
      }
      if (isValidValue(fields()[12], other.eventDate)) {
        this.eventDate = data().deepCopy(fields()[12].schema(), other.eventDate);
        fieldSetFlags()[12] = other.fieldSetFlags()[12];
      }
      if (isValidValue(fields()[13], other.eventTimestamp)) {
        this.eventTimestamp = data().deepCopy(fields()[13].schema(), other.eventTimestamp);
        fieldSetFlags()[13] = other.fieldSetFlags()[13];
      }
      if (isValidValue(fields()[14], other.eventTypeEnum)) {
        this.eventTypeEnum = data().deepCopy(fields()[14].schema(), other.eventTypeEnum);
        fieldSetFlags()[14] = other.fieldSetFlags()[14];
      }
      if (isValidValue(fields()[15], other.installmentPlanCode)) {
        this.installmentPlanCode = data().deepCopy(fields()[15].schema(), other.installmentPlanCode);
        fieldSetFlags()[15] = other.fieldSetFlags()[15];
      }
      if (isValidValue(fields()[16], other.note)) {
        this.note = data().deepCopy(fields()[16].schema(), other.note);
        fieldSetFlags()[16] = other.fieldSetFlags()[16];
      }
      if (isValidValue(fields()[17], other.originalEventDate)) {
        this.originalEventDate = data().deepCopy(fields()[17].schema(), other.originalEventDate);
        fieldSetFlags()[17] = other.fieldSetFlags()[17];
      }
      if (isValidValue(fields()[18], other.pcid)) {
        this.pcid = data().deepCopy(fields()[18].schema(), other.pcid);
        fieldSetFlags()[18] = other.fieldSetFlags()[18];
      }
      if (isValidValue(fields()[19], other.processingState)) {
        this.processingState = data().deepCopy(fields()[19].schema(), other.processingState);
        fieldSetFlags()[19] = other.fieldSetFlags()[19];
      }
      if (isValidValue(fields()[20], other.transactionSourceId)) {
        this.transactionSourceId = data().deepCopy(fields()[20].schema(), other.transactionSourceId);
        fieldSetFlags()[20] = other.fieldSetFlags()[20];
      }
      if (other.hasTransactionSourceIdBuilder()) {
        this.transactionSourceIdBuilder = net.homecredit.cabus.am.common.TransactionSourceId.newBuilder(other.getTransactionSourceIdBuilder());
      }
      if (isValidValue(fields()[21], other.truncatedPan)) {
        this.truncatedPan = data().deepCopy(fields()[21].schema(), other.truncatedPan);
        fieldSetFlags()[21] = other.fieldSetFlags()[21];
      }
      if (isValidValue(fields()[22], other.accountCurrency)) {
        this.accountCurrency = data().deepCopy(fields()[22].schema(), other.accountCurrency);
        fieldSetFlags()[22] = other.fieldSetFlags()[22];
      }
      if (isValidValue(fields()[23], other.attributes)) {
        this.attributes = data().deepCopy(fields()[23].schema(), other.attributes);
        fieldSetFlags()[23] = other.fieldSetFlags()[23];
      }
      if (other.hasAttributesBuilder()) {
        this.attributesBuilder = net.homecredit.cabus.am.common.Attributes.newBuilder(other.getAttributesBuilder());
      }
    }

    /**
     * Creates a Builder by copying an existing EventInfo instance
     * @param other The existing instance to copy.
     */
    private Builder(net.homecredit.cabus.am.event.eventinfo.v1.EventInfo other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.accountStatus)) {
        this.accountStatus = data().deepCopy(fields()[0].schema(), other.accountStatus);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.accountType)) {
        this.accountType = data().deepCopy(fields()[1].schema(), other.accountType);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.authorization)) {
        this.authorization = data().deepCopy(fields()[2].schema(), other.authorization);
        fieldSetFlags()[2] = true;
      }
      this.authorizationBuilder = null;
      if (isValidValue(fields()[3], other.transaction)) {
        this.transaction = data().deepCopy(fields()[3].schema(), other.transaction);
        fieldSetFlags()[3] = true;
      }
      this.transactionBuilder = null;
      if (isValidValue(fields()[4], other.usedWaivers)) {
        this.usedWaivers = data().deepCopy(fields()[4].schema(), other.usedWaivers);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.transactions)) {
        this.transactions = data().deepCopy(fields()[5].schema(), other.transactions);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.accountNumber)) {
        this.accountNumber = data().deepCopy(fields()[6].schema(), other.accountNumber);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.blockingReason)) {
        this.blockingReason = data().deepCopy(fields()[7].schema(), other.blockingReason);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.cancellationReason)) {
        this.cancellationReason = data().deepCopy(fields()[8].schema(), other.cancellationReason);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.cancelled)) {
        this.cancelled = data().deepCopy(fields()[9].schema(), other.cancelled);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.cancelledDate)) {
        this.cancelledDate = data().deepCopy(fields()[10].schema(), other.cancelledDate);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.eventCode)) {
        this.eventCode = data().deepCopy(fields()[11].schema(), other.eventCode);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.eventDate)) {
        this.eventDate = data().deepCopy(fields()[12].schema(), other.eventDate);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.eventTimestamp)) {
        this.eventTimestamp = data().deepCopy(fields()[13].schema(), other.eventTimestamp);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.eventTypeEnum)) {
        this.eventTypeEnum = data().deepCopy(fields()[14].schema(), other.eventTypeEnum);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.installmentPlanCode)) {
        this.installmentPlanCode = data().deepCopy(fields()[15].schema(), other.installmentPlanCode);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.note)) {
        this.note = data().deepCopy(fields()[16].schema(), other.note);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.originalEventDate)) {
        this.originalEventDate = data().deepCopy(fields()[17].schema(), other.originalEventDate);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.pcid)) {
        this.pcid = data().deepCopy(fields()[18].schema(), other.pcid);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.processingState)) {
        this.processingState = data().deepCopy(fields()[19].schema(), other.processingState);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.transactionSourceId)) {
        this.transactionSourceId = data().deepCopy(fields()[20].schema(), other.transactionSourceId);
        fieldSetFlags()[20] = true;
      }
      this.transactionSourceIdBuilder = null;
      if (isValidValue(fields()[21], other.truncatedPan)) {
        this.truncatedPan = data().deepCopy(fields()[21].schema(), other.truncatedPan);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.accountCurrency)) {
        this.accountCurrency = data().deepCopy(fields()[22].schema(), other.accountCurrency);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.attributes)) {
        this.attributes = data().deepCopy(fields()[23].schema(), other.attributes);
        fieldSetFlags()[23] = true;
      }
      this.attributesBuilder = null;
    }

    /**
      * Gets the value of the 'accountStatus' field.
      * Account status
      * @return The value.
      */
    public java.lang.CharSequence getAccountStatus() {
      return accountStatus;
    }


    /**
      * Sets the value of the 'accountStatus' field.
      * Account status
      * @param value The value of 'accountStatus'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAccountStatus(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.accountStatus = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'accountStatus' field has been set.
      * Account status
      * @return True if the 'accountStatus' field has been set, false otherwise.
      */
    public boolean hasAccountStatus() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'accountStatus' field.
      * Account status
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearAccountStatus() {
      accountStatus = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'accountType' field.
      * Account type
      * @return The value.
      */
    public java.lang.CharSequence getAccountType() {
      return accountType;
    }


    /**
      * Sets the value of the 'accountType' field.
      * Account type
      * @param value The value of 'accountType'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAccountType(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.accountType = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'accountType' field has been set.
      * Account type
      * @return True if the 'accountType' field has been set, false otherwise.
      */
    public boolean hasAccountType() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'accountType' field.
      * Account type
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearAccountType() {
      accountType = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'authorization' field.
      * Authorization overview
      * @return The value.
      */
    public net.homecredit.cabus.am.common.Authorization getAuthorization() {
      return authorization;
    }


    /**
      * Sets the value of the 'authorization' field.
      * Authorization overview
      * @param value The value of 'authorization'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAuthorization(net.homecredit.cabus.am.common.Authorization value) {
      validate(fields()[2], value);
      this.authorizationBuilder = null;
      this.authorization = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'authorization' field has been set.
      * Authorization overview
      * @return True if the 'authorization' field has been set, false otherwise.
      */
    public boolean hasAuthorization() {
      return fieldSetFlags()[2];
    }

    /**
     * Gets the Builder instance for the 'authorization' field and creates one if it doesn't exist yet.
     * Authorization overview
     * @return This builder.
     */
    public net.homecredit.cabus.am.common.Authorization.Builder getAuthorizationBuilder() {
      if (authorizationBuilder == null) {
        if (hasAuthorization()) {
          setAuthorizationBuilder(net.homecredit.cabus.am.common.Authorization.newBuilder(authorization));
        } else {
          setAuthorizationBuilder(net.homecredit.cabus.am.common.Authorization.newBuilder());
        }
      }
      return authorizationBuilder;
    }

    /**
     * Sets the Builder instance for the 'authorization' field
     * Authorization overview
     * @param value The builder instance that must be set.
     * @return This builder.
     */

    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAuthorizationBuilder(net.homecredit.cabus.am.common.Authorization.Builder value) {
      clearAuthorization();
      authorizationBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'authorization' field has an active Builder instance
     * Authorization overview
     * @return True if the 'authorization' field has an active Builder instance
     */
    public boolean hasAuthorizationBuilder() {
      return authorizationBuilder != null;
    }

    /**
      * Clears the value of the 'authorization' field.
      * Authorization overview
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearAuthorization() {
      authorization = null;
      authorizationBuilder = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'transaction' field.
      * Transaction detail
      * @return The value.
      */
    public net.homecredit.cabus.am.common.ConfirmedTransaction getTransaction() {
      return transaction;
    }


    /**
      * Sets the value of the 'transaction' field.
      * Transaction detail
      * @param value The value of 'transaction'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setTransaction(net.homecredit.cabus.am.common.ConfirmedTransaction value) {
      validate(fields()[3], value);
      this.transactionBuilder = null;
      this.transaction = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'transaction' field has been set.
      * Transaction detail
      * @return True if the 'transaction' field has been set, false otherwise.
      */
    public boolean hasTransaction() {
      return fieldSetFlags()[3];
    }

    /**
     * Gets the Builder instance for the 'transaction' field and creates one if it doesn't exist yet.
     * Transaction detail
     * @return This builder.
     */
    public net.homecredit.cabus.am.common.ConfirmedTransaction.Builder getTransactionBuilder() {
      if (transactionBuilder == null) {
        if (hasTransaction()) {
          setTransactionBuilder(net.homecredit.cabus.am.common.ConfirmedTransaction.newBuilder(transaction));
        } else {
          setTransactionBuilder(net.homecredit.cabus.am.common.ConfirmedTransaction.newBuilder());
        }
      }
      return transactionBuilder;
    }

    /**
     * Sets the Builder instance for the 'transaction' field
     * Transaction detail
     * @param value The builder instance that must be set.
     * @return This builder.
     */

    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setTransactionBuilder(net.homecredit.cabus.am.common.ConfirmedTransaction.Builder value) {
      clearTransaction();
      transactionBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'transaction' field has an active Builder instance
     * Transaction detail
     * @return True if the 'transaction' field has an active Builder instance
     */
    public boolean hasTransactionBuilder() {
      return transactionBuilder != null;
    }

    /**
      * Clears the value of the 'transaction' field.
      * Transaction detail
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearTransaction() {
      transaction = null;
      transactionBuilder = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'usedWaivers' field.
      * Used waivers
      * @return The value.
      */
    public java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> getUsedWaivers() {
      return usedWaivers;
    }


    /**
      * Sets the value of the 'usedWaivers' field.
      * Used waivers
      * @param value The value of 'usedWaivers'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setUsedWaivers(java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver> value) {
      validate(fields()[4], value);
      this.usedWaivers = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'usedWaivers' field has been set.
      * Used waivers
      * @return True if the 'usedWaivers' field has been set, false otherwise.
      */
    public boolean hasUsedWaivers() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'usedWaivers' field.
      * Used waivers
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearUsedWaivers() {
      usedWaivers = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'transactions' field.
      * Transaction list
      * @return The value.
      */
    public java.util.List<java.lang.CharSequence> getTransactions() {
      return transactions;
    }


    /**
      * Sets the value of the 'transactions' field.
      * Transaction list
      * @param value The value of 'transactions'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setTransactions(java.util.List<java.lang.CharSequence> value) {
      validate(fields()[5], value);
      this.transactions = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'transactions' field has been set.
      * Transaction list
      * @return True if the 'transactions' field has been set, false otherwise.
      */
    public boolean hasTransactions() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'transactions' field.
      * Transaction list
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearTransactions() {
      transactions = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'accountNumber' field.
      * Account number
      * @return The value.
      */
    public java.lang.Long getAccountNumber() {
      return accountNumber;
    }


    /**
      * Sets the value of the 'accountNumber' field.
      * Account number
      * @param value The value of 'accountNumber'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAccountNumber(java.lang.Long value) {
      validate(fields()[6], value);
      this.accountNumber = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'accountNumber' field has been set.
      * Account number
      * @return True if the 'accountNumber' field has been set, false otherwise.
      */
    public boolean hasAccountNumber() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'accountNumber' field.
      * Account number
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearAccountNumber() {
      accountNumber = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'blockingReason' field.
      * Blocking reason
      * @return The value.
      */
    public java.lang.CharSequence getBlockingReason() {
      return blockingReason;
    }


    /**
      * Sets the value of the 'blockingReason' field.
      * Blocking reason
      * @param value The value of 'blockingReason'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setBlockingReason(java.lang.CharSequence value) {
      validate(fields()[7], value);
      this.blockingReason = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'blockingReason' field has been set.
      * Blocking reason
      * @return True if the 'blockingReason' field has been set, false otherwise.
      */
    public boolean hasBlockingReason() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'blockingReason' field.
      * Blocking reason
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearBlockingReason() {
      blockingReason = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'cancellationReason' field.
      * Cancellation reason
      * @return The value.
      */
    public java.lang.CharSequence getCancellationReason() {
      return cancellationReason;
    }


    /**
      * Sets the value of the 'cancellationReason' field.
      * Cancellation reason
      * @param value The value of 'cancellationReason'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setCancellationReason(java.lang.CharSequence value) {
      validate(fields()[8], value);
      this.cancellationReason = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'cancellationReason' field has been set.
      * Cancellation reason
      * @return True if the 'cancellationReason' field has been set, false otherwise.
      */
    public boolean hasCancellationReason() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'cancellationReason' field.
      * Cancellation reason
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearCancellationReason() {
      cancellationReason = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'cancelled' field.
      * Cancellation flag
      * @return The value.
      */
    public boolean getCancelled() {
      return cancelled;
    }


    /**
      * Sets the value of the 'cancelled' field.
      * Cancellation flag
      * @param value The value of 'cancelled'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setCancelled(boolean value) {
      validate(fields()[9], value);
      this.cancelled = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'cancelled' field has been set.
      * Cancellation flag
      * @return True if the 'cancelled' field has been set, false otherwise.
      */
    public boolean hasCancelled() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'cancelled' field.
      * Cancellation flag
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearCancelled() {
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'cancelledDate' field.
      * Cancellation date
      * @return The value.
      */
    public java.time.LocalDate getCancelledDate() {
      return cancelledDate;
    }


    /**
      * Sets the value of the 'cancelledDate' field.
      * Cancellation date
      * @param value The value of 'cancelledDate'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setCancelledDate(java.time.LocalDate value) {
      validate(fields()[10], value);
      this.cancelledDate = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'cancelledDate' field has been set.
      * Cancellation date
      * @return True if the 'cancelledDate' field has been set, false otherwise.
      */
    public boolean hasCancelledDate() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'cancelledDate' field.
      * Cancellation date
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearCancelledDate() {
      cancelledDate = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'eventCode' field.
      * Event code
      * @return The value.
      */
    public java.lang.CharSequence getEventCode() {
      return eventCode;
    }


    /**
      * Sets the value of the 'eventCode' field.
      * Event code
      * @param value The value of 'eventCode'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setEventCode(java.lang.CharSequence value) {
      validate(fields()[11], value);
      this.eventCode = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'eventCode' field has been set.
      * Event code
      * @return True if the 'eventCode' field has been set, false otherwise.
      */
    public boolean hasEventCode() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'eventCode' field.
      * Event code
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearEventCode() {
      eventCode = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'eventDate' field.
      * Event date
      * @return The value.
      */
    public java.time.LocalDate getEventDate() {
      return eventDate;
    }


    /**
      * Sets the value of the 'eventDate' field.
      * Event date
      * @param value The value of 'eventDate'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setEventDate(java.time.LocalDate value) {
      validate(fields()[12], value);
      this.eventDate = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'eventDate' field has been set.
      * Event date
      * @return True if the 'eventDate' field has been set, false otherwise.
      */
    public boolean hasEventDate() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'eventDate' field.
      * Event date
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearEventDate() {
      eventDate = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'eventTimestamp' field.
      * Event timestamp
      * @return The value.
      */
    public java.time.LocalDateTime getEventTimestamp() {
      return eventTimestamp;
    }


    /**
      * Sets the value of the 'eventTimestamp' field.
      * Event timestamp
      * @param value The value of 'eventTimestamp'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setEventTimestamp(java.time.LocalDateTime value) {
      validate(fields()[13], value);
      this.eventTimestamp = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'eventTimestamp' field has been set.
      * Event timestamp
      * @return True if the 'eventTimestamp' field has been set, false otherwise.
      */
    public boolean hasEventTimestamp() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'eventTimestamp' field.
      * Event timestamp
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearEventTimestamp() {
      eventTimestamp = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'eventTypeEnum' field.
      * Event type
      * @return The value.
      */
    public java.lang.CharSequence getEventTypeEnum() {
      return eventTypeEnum;
    }


    /**
      * Sets the value of the 'eventTypeEnum' field.
      * Event type
      * @param value The value of 'eventTypeEnum'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setEventTypeEnum(java.lang.CharSequence value) {
      validate(fields()[14], value);
      this.eventTypeEnum = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'eventTypeEnum' field has been set.
      * Event type
      * @return True if the 'eventTypeEnum' field has been set, false otherwise.
      */
    public boolean hasEventTypeEnum() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'eventTypeEnum' field.
      * Event type
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearEventTypeEnum() {
      eventTypeEnum = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'installmentPlanCode' field.
      * Instalment plan code
      * @return The value.
      */
    public java.lang.CharSequence getInstallmentPlanCode() {
      return installmentPlanCode;
    }


    /**
      * Sets the value of the 'installmentPlanCode' field.
      * Instalment plan code
      * @param value The value of 'installmentPlanCode'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setInstallmentPlanCode(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.installmentPlanCode = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'installmentPlanCode' field has been set.
      * Instalment plan code
      * @return True if the 'installmentPlanCode' field has been set, false otherwise.
      */
    public boolean hasInstallmentPlanCode() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'installmentPlanCode' field.
      * Instalment plan code
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearInstallmentPlanCode() {
      installmentPlanCode = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'note' field.
      * Event note
      * @return The value.
      */
    public java.lang.CharSequence getNote() {
      return note;
    }


    /**
      * Sets the value of the 'note' field.
      * Event note
      * @param value The value of 'note'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setNote(java.lang.CharSequence value) {
      validate(fields()[16], value);
      this.note = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'note' field has been set.
      * Event note
      * @return True if the 'note' field has been set, false otherwise.
      */
    public boolean hasNote() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'note' field.
      * Event note
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearNote() {
      note = null;
      fieldSetFlags()[16] = false;
      return this;
    }

    /**
      * Gets the value of the 'originalEventDate' field.
      * Original event date
      * @return The value.
      */
    public java.time.LocalDate getOriginalEventDate() {
      return originalEventDate;
    }


    /**
      * Sets the value of the 'originalEventDate' field.
      * Original event date
      * @param value The value of 'originalEventDate'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setOriginalEventDate(java.time.LocalDate value) {
      validate(fields()[17], value);
      this.originalEventDate = value;
      fieldSetFlags()[17] = true;
      return this;
    }

    /**
      * Checks whether the 'originalEventDate' field has been set.
      * Original event date
      * @return True if the 'originalEventDate' field has been set, false otherwise.
      */
    public boolean hasOriginalEventDate() {
      return fieldSetFlags()[17];
    }


    /**
      * Clears the value of the 'originalEventDate' field.
      * Original event date
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearOriginalEventDate() {
      originalEventDate = null;
      fieldSetFlags()[17] = false;
      return this;
    }

    /**
      * Gets the value of the 'pcid' field.
      * Payment card ID
      * @return The value.
      */
    public java.lang.Long getPcid() {
      return pcid;
    }


    /**
      * Sets the value of the 'pcid' field.
      * Payment card ID
      * @param value The value of 'pcid'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setPcid(java.lang.Long value) {
      validate(fields()[18], value);
      this.pcid = value;
      fieldSetFlags()[18] = true;
      return this;
    }

    /**
      * Checks whether the 'pcid' field has been set.
      * Payment card ID
      * @return True if the 'pcid' field has been set, false otherwise.
      */
    public boolean hasPcid() {
      return fieldSetFlags()[18];
    }


    /**
      * Clears the value of the 'pcid' field.
      * Payment card ID
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearPcid() {
      pcid = null;
      fieldSetFlags()[18] = false;
      return this;
    }

    /**
      * Gets the value of the 'processingState' field.
      * Event processing date
      * @return The value.
      */
    public java.lang.CharSequence getProcessingState() {
      return processingState;
    }


    /**
      * Sets the value of the 'processingState' field.
      * Event processing date
      * @param value The value of 'processingState'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setProcessingState(java.lang.CharSequence value) {
      validate(fields()[19], value);
      this.processingState = value;
      fieldSetFlags()[19] = true;
      return this;
    }

    /**
      * Checks whether the 'processingState' field has been set.
      * Event processing date
      * @return True if the 'processingState' field has been set, false otherwise.
      */
    public boolean hasProcessingState() {
      return fieldSetFlags()[19];
    }


    /**
      * Clears the value of the 'processingState' field.
      * Event processing date
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearProcessingState() {
      processingState = null;
      fieldSetFlags()[19] = false;
      return this;
    }

    /**
      * Gets the value of the 'transactionSourceId' field.
      * Transaction source ID
      * @return The value.
      */
    public net.homecredit.cabus.am.common.TransactionSourceId getTransactionSourceId() {
      return transactionSourceId;
    }


    /**
      * Sets the value of the 'transactionSourceId' field.
      * Transaction source ID
      * @param value The value of 'transactionSourceId'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setTransactionSourceId(net.homecredit.cabus.am.common.TransactionSourceId value) {
      validate(fields()[20], value);
      this.transactionSourceIdBuilder = null;
      this.transactionSourceId = value;
      fieldSetFlags()[20] = true;
      return this;
    }

    /**
      * Checks whether the 'transactionSourceId' field has been set.
      * Transaction source ID
      * @return True if the 'transactionSourceId' field has been set, false otherwise.
      */
    public boolean hasTransactionSourceId() {
      return fieldSetFlags()[20];
    }

    /**
     * Gets the Builder instance for the 'transactionSourceId' field and creates one if it doesn't exist yet.
     * Transaction source ID
     * @return This builder.
     */
    public net.homecredit.cabus.am.common.TransactionSourceId.Builder getTransactionSourceIdBuilder() {
      if (transactionSourceIdBuilder == null) {
        if (hasTransactionSourceId()) {
          setTransactionSourceIdBuilder(net.homecredit.cabus.am.common.TransactionSourceId.newBuilder(transactionSourceId));
        } else {
          setTransactionSourceIdBuilder(net.homecredit.cabus.am.common.TransactionSourceId.newBuilder());
        }
      }
      return transactionSourceIdBuilder;
    }

    /**
     * Sets the Builder instance for the 'transactionSourceId' field
     * Transaction source ID
     * @param value The builder instance that must be set.
     * @return This builder.
     */

    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setTransactionSourceIdBuilder(net.homecredit.cabus.am.common.TransactionSourceId.Builder value) {
      clearTransactionSourceId();
      transactionSourceIdBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'transactionSourceId' field has an active Builder instance
     * Transaction source ID
     * @return True if the 'transactionSourceId' field has an active Builder instance
     */
    public boolean hasTransactionSourceIdBuilder() {
      return transactionSourceIdBuilder != null;
    }

    /**
      * Clears the value of the 'transactionSourceId' field.
      * Transaction source ID
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearTransactionSourceId() {
      transactionSourceId = null;
      transactionSourceIdBuilder = null;
      fieldSetFlags()[20] = false;
      return this;
    }

    /**
      * Gets the value of the 'truncatedPan' field.
      * Truncated PAN number
      * @return The value.
      */
    public java.lang.CharSequence getTruncatedPan() {
      return truncatedPan;
    }


    /**
      * Sets the value of the 'truncatedPan' field.
      * Truncated PAN number
      * @param value The value of 'truncatedPan'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setTruncatedPan(java.lang.CharSequence value) {
      validate(fields()[21], value);
      this.truncatedPan = value;
      fieldSetFlags()[21] = true;
      return this;
    }

    /**
      * Checks whether the 'truncatedPan' field has been set.
      * Truncated PAN number
      * @return True if the 'truncatedPan' field has been set, false otherwise.
      */
    public boolean hasTruncatedPan() {
      return fieldSetFlags()[21];
    }


    /**
      * Clears the value of the 'truncatedPan' field.
      * Truncated PAN number
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearTruncatedPan() {
      truncatedPan = null;
      fieldSetFlags()[21] = false;
      return this;
    }

    /**
      * Gets the value of the 'accountCurrency' field.
      * Account currency code
      * @return The value.
      */
    public java.lang.CharSequence getAccountCurrency() {
      return accountCurrency;
    }


    /**
      * Sets the value of the 'accountCurrency' field.
      * Account currency code
      * @param value The value of 'accountCurrency'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAccountCurrency(java.lang.CharSequence value) {
      validate(fields()[22], value);
      this.accountCurrency = value;
      fieldSetFlags()[22] = true;
      return this;
    }

    /**
      * Checks whether the 'accountCurrency' field has been set.
      * Account currency code
      * @return True if the 'accountCurrency' field has been set, false otherwise.
      */
    public boolean hasAccountCurrency() {
      return fieldSetFlags()[22];
    }


    /**
      * Clears the value of the 'accountCurrency' field.
      * Account currency code
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearAccountCurrency() {
      accountCurrency = null;
      fieldSetFlags()[22] = false;
      return this;
    }

    /**
      * Gets the value of the 'attributes' field.
      * Event attributes
      * @return The value.
      */
    public net.homecredit.cabus.am.common.Attributes getAttributes() {
      return attributes;
    }


    /**
      * Sets the value of the 'attributes' field.
      * Event attributes
      * @param value The value of 'attributes'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAttributes(net.homecredit.cabus.am.common.Attributes value) {
      validate(fields()[23], value);
      this.attributesBuilder = null;
      this.attributes = value;
      fieldSetFlags()[23] = true;
      return this;
    }

    /**
      * Checks whether the 'attributes' field has been set.
      * Event attributes
      * @return True if the 'attributes' field has been set, false otherwise.
      */
    public boolean hasAttributes() {
      return fieldSetFlags()[23];
    }

    /**
     * Gets the Builder instance for the 'attributes' field and creates one if it doesn't exist yet.
     * Event attributes
     * @return This builder.
     */
    public net.homecredit.cabus.am.common.Attributes.Builder getAttributesBuilder() {
      if (attributesBuilder == null) {
        if (hasAttributes()) {
          setAttributesBuilder(net.homecredit.cabus.am.common.Attributes.newBuilder(attributes));
        } else {
          setAttributesBuilder(net.homecredit.cabus.am.common.Attributes.newBuilder());
        }
      }
      return attributesBuilder;
    }

    /**
     * Sets the Builder instance for the 'attributes' field
     * Event attributes
     * @param value The builder instance that must be set.
     * @return This builder.
     */

    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder setAttributesBuilder(net.homecredit.cabus.am.common.Attributes.Builder value) {
      clearAttributes();
      attributesBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'attributes' field has an active Builder instance
     * Event attributes
     * @return True if the 'attributes' field has an active Builder instance
     */
    public boolean hasAttributesBuilder() {
      return attributesBuilder != null;
    }

    /**
      * Clears the value of the 'attributes' field.
      * Event attributes
      * @return This builder.
      */
    public net.homecredit.cabus.am.event.eventinfo.v1.EventInfo.Builder clearAttributes() {
      attributes = null;
      attributesBuilder = null;
      fieldSetFlags()[23] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public EventInfo build() {
      try {
        EventInfo record = new EventInfo();
        record.accountStatus = fieldSetFlags()[0] ? this.accountStatus : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.accountType = fieldSetFlags()[1] ? this.accountType : (java.lang.CharSequence) defaultValue(fields()[1]);
        if (authorizationBuilder != null) {
          try {
            record.authorization = this.authorizationBuilder.build();
          } catch (org.apache.avro.AvroMissingFieldException e) {
            e.addParentField(record.getSchema().getField("authorization"));
            throw e;
          }
        } else {
          record.authorization = fieldSetFlags()[2] ? this.authorization : (net.homecredit.cabus.am.common.Authorization) defaultValue(fields()[2]);
        }
        if (transactionBuilder != null) {
          try {
            record.transaction = this.transactionBuilder.build();
          } catch (org.apache.avro.AvroMissingFieldException e) {
            e.addParentField(record.getSchema().getField("transaction"));
            throw e;
          }
        } else {
          record.transaction = fieldSetFlags()[3] ? this.transaction : (net.homecredit.cabus.am.common.ConfirmedTransaction) defaultValue(fields()[3]);
        }
        record.usedWaivers = fieldSetFlags()[4] ? this.usedWaivers : (java.util.List<net.homecredit.cabus.am.account.to.common.UsedWaiver>) defaultValue(fields()[4]);
        record.transactions = fieldSetFlags()[5] ? this.transactions : (java.util.List<java.lang.CharSequence>) defaultValue(fields()[5]);
        record.accountNumber = fieldSetFlags()[6] ? this.accountNumber : (java.lang.Long) defaultValue(fields()[6]);
        record.blockingReason = fieldSetFlags()[7] ? this.blockingReason : (java.lang.CharSequence) defaultValue(fields()[7]);
        record.cancellationReason = fieldSetFlags()[8] ? this.cancellationReason : (java.lang.CharSequence) defaultValue(fields()[8]);
        record.cancelled = fieldSetFlags()[9] ? this.cancelled : (java.lang.Boolean) defaultValue(fields()[9]);
        record.cancelledDate = fieldSetFlags()[10] ? this.cancelledDate : (java.time.LocalDate) defaultValue(fields()[10]);
        record.eventCode = fieldSetFlags()[11] ? this.eventCode : (java.lang.CharSequence) defaultValue(fields()[11]);
        record.eventDate = fieldSetFlags()[12] ? this.eventDate : (java.time.LocalDate) defaultValue(fields()[12]);
        record.eventTimestamp = fieldSetFlags()[13] ? this.eventTimestamp : (java.time.LocalDateTime) defaultValue(fields()[13]);
        record.eventTypeEnum = fieldSetFlags()[14] ? this.eventTypeEnum : (java.lang.CharSequence) defaultValue(fields()[14]);
        record.installmentPlanCode = fieldSetFlags()[15] ? this.installmentPlanCode : (java.lang.CharSequence) defaultValue(fields()[15]);
        record.note = fieldSetFlags()[16] ? this.note : (java.lang.CharSequence) defaultValue(fields()[16]);
        record.originalEventDate = fieldSetFlags()[17] ? this.originalEventDate : (java.time.LocalDate) defaultValue(fields()[17]);
        record.pcid = fieldSetFlags()[18] ? this.pcid : (java.lang.Long) defaultValue(fields()[18]);
        record.processingState = fieldSetFlags()[19] ? this.processingState : (java.lang.CharSequence) defaultValue(fields()[19]);
        if (transactionSourceIdBuilder != null) {
          try {
            record.transactionSourceId = this.transactionSourceIdBuilder.build();
          } catch (org.apache.avro.AvroMissingFieldException e) {
            e.addParentField(record.getSchema().getField("transactionSourceId"));
            throw e;
          }
        } else {
          record.transactionSourceId = fieldSetFlags()[20] ? this.transactionSourceId : (net.homecredit.cabus.am.common.TransactionSourceId) defaultValue(fields()[20]);
        }
        record.truncatedPan = fieldSetFlags()[21] ? this.truncatedPan : (java.lang.CharSequence) defaultValue(fields()[21]);
        record.accountCurrency = fieldSetFlags()[22] ? this.accountCurrency : (java.lang.CharSequence) defaultValue(fields()[22]);
        if (attributesBuilder != null) {
          try {
            record.attributes = this.attributesBuilder.build();
          } catch (org.apache.avro.AvroMissingFieldException e) {
            e.addParentField(record.getSchema().getField("attributes"));
            throw e;
          }
        } else {
          record.attributes = fieldSetFlags()[23] ? this.attributes : (net.homecredit.cabus.am.common.Attributes) defaultValue(fields()[23]);
        }
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<EventInfo>
    WRITER$ = (org.apache.avro.io.DatumWriter<EventInfo>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<EventInfo>
    READER$ = (org.apache.avro.io.DatumReader<EventInfo>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}










