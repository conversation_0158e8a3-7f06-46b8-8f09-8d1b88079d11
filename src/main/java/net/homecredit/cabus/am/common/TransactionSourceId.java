/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package net.homecredit.cabus.am.common;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@org.apache.avro.specific.AvroGenerated
public class TransactionSourceId extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 5652272448663575903L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"TransactionSourceId\",\"namespace\":\"net.homecredit.cabus.am.common\",\"fields\":[{\"name\":\"sourceSystem\",\"type\":\"string\"},{\"name\":\"sourceTxId\",\"type\":\"string\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<TransactionSourceId> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<TransactionSourceId> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<TransactionSourceId> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<TransactionSourceId> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<TransactionSourceId> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this TransactionSourceId to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a TransactionSourceId from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a TransactionSourceId instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static TransactionSourceId fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  public java.lang.CharSequence sourceSystem;
  public java.lang.CharSequence sourceTxId;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public TransactionSourceId() {}

  /**
   * All-args constructor.
   * @param sourceSystem The new value for sourceSystem
   * @param sourceTxId The new value for sourceTxId
   */
  public TransactionSourceId(java.lang.CharSequence sourceSystem, java.lang.CharSequence sourceTxId) {
    this.sourceSystem = sourceSystem;
    this.sourceTxId = sourceTxId;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return sourceSystem;
    case 1: return sourceTxId;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: sourceSystem = (java.lang.CharSequence)value$; break;
    case 1: sourceTxId = (java.lang.CharSequence)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'sourceSystem' field.
   * @return The value of the 'sourceSystem' field.
   */
  public java.lang.CharSequence getSourceSystem() {
    return sourceSystem;
  }


  /**
   * Sets the value of the 'sourceSystem' field.
   * @param value the value to set.
   */
  public void setSourceSystem(java.lang.CharSequence value) {
    this.sourceSystem = value;
  }

  /**
   * Gets the value of the 'sourceTxId' field.
   * @return The value of the 'sourceTxId' field.
   */
  public java.lang.CharSequence getSourceTxId() {
    return sourceTxId;
  }


  /**
   * Sets the value of the 'sourceTxId' field.
   * @param value the value to set.
   */
  public void setSourceTxId(java.lang.CharSequence value) {
    this.sourceTxId = value;
  }

  /**
   * Creates a new TransactionSourceId RecordBuilder.
   * @return A new TransactionSourceId RecordBuilder
   */
  public static net.homecredit.cabus.am.common.TransactionSourceId.Builder newBuilder() {
    return new net.homecredit.cabus.am.common.TransactionSourceId.Builder();
  }

  /**
   * Creates a new TransactionSourceId RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new TransactionSourceId RecordBuilder
   */
  public static net.homecredit.cabus.am.common.TransactionSourceId.Builder newBuilder(net.homecredit.cabus.am.common.TransactionSourceId.Builder other) {
    if (other == null) {
      return new net.homecredit.cabus.am.common.TransactionSourceId.Builder();
    } else {
      return new net.homecredit.cabus.am.common.TransactionSourceId.Builder(other);
    }
  }

  /**
   * Creates a new TransactionSourceId RecordBuilder by copying an existing TransactionSourceId instance.
   * @param other The existing instance to copy.
   * @return A new TransactionSourceId RecordBuilder
   */
  public static net.homecredit.cabus.am.common.TransactionSourceId.Builder newBuilder(net.homecredit.cabus.am.common.TransactionSourceId other) {
    if (other == null) {
      return new net.homecredit.cabus.am.common.TransactionSourceId.Builder();
    } else {
      return new net.homecredit.cabus.am.common.TransactionSourceId.Builder(other);
    }
  }

  /**
   * RecordBuilder for TransactionSourceId instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<TransactionSourceId>
    implements org.apache.avro.data.RecordBuilder<TransactionSourceId> {

    private java.lang.CharSequence sourceSystem;
    private java.lang.CharSequence sourceTxId;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(net.homecredit.cabus.am.common.TransactionSourceId.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.sourceSystem)) {
        this.sourceSystem = data().deepCopy(fields()[0].schema(), other.sourceSystem);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.sourceTxId)) {
        this.sourceTxId = data().deepCopy(fields()[1].schema(), other.sourceTxId);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
    }

    /**
     * Creates a Builder by copying an existing TransactionSourceId instance
     * @param other The existing instance to copy.
     */
    private Builder(net.homecredit.cabus.am.common.TransactionSourceId other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.sourceSystem)) {
        this.sourceSystem = data().deepCopy(fields()[0].schema(), other.sourceSystem);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.sourceTxId)) {
        this.sourceTxId = data().deepCopy(fields()[1].schema(), other.sourceTxId);
        fieldSetFlags()[1] = true;
      }
    }

    /**
      * Gets the value of the 'sourceSystem' field.
      * @return The value.
      */
    public java.lang.CharSequence getSourceSystem() {
      return sourceSystem;
    }


    /**
      * Sets the value of the 'sourceSystem' field.
      * @param value The value of 'sourceSystem'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.common.TransactionSourceId.Builder setSourceSystem(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.sourceSystem = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'sourceSystem' field has been set.
      * @return True if the 'sourceSystem' field has been set, false otherwise.
      */
    public boolean hasSourceSystem() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'sourceSystem' field.
      * @return This builder.
      */
    public net.homecredit.cabus.am.common.TransactionSourceId.Builder clearSourceSystem() {
      sourceSystem = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'sourceTxId' field.
      * @return The value.
      */
    public java.lang.CharSequence getSourceTxId() {
      return sourceTxId;
    }


    /**
      * Sets the value of the 'sourceTxId' field.
      * @param value The value of 'sourceTxId'.
      * @return This builder.
      */
    public net.homecredit.cabus.am.common.TransactionSourceId.Builder setSourceTxId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.sourceTxId = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'sourceTxId' field has been set.
      * @return True if the 'sourceTxId' field has been set, false otherwise.
      */
    public boolean hasSourceTxId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'sourceTxId' field.
      * @return This builder.
      */
    public net.homecredit.cabus.am.common.TransactionSourceId.Builder clearSourceTxId() {
      sourceTxId = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public TransactionSourceId build() {
      try {
        TransactionSourceId record = new TransactionSourceId();
        record.sourceSystem = fieldSetFlags()[0] ? this.sourceSystem : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.sourceTxId = fieldSetFlags()[1] ? this.sourceTxId : (java.lang.CharSequence) defaultValue(fields()[1]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<TransactionSourceId>
    WRITER$ = (org.apache.avro.io.DatumWriter<TransactionSourceId>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<TransactionSourceId>
    READER$ = (org.apache.avro.io.DatumReader<TransactionSourceId>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.sourceSystem);

    out.writeString(this.sourceTxId);

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.sourceSystem = in.readString(this.sourceSystem instanceof Utf8 ? (Utf8)this.sourceSystem : null);

      this.sourceTxId = in.readString(this.sourceTxId instanceof Utf8 ? (Utf8)this.sourceTxId : null);

    } else {
      for (int i = 0; i < 2; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.sourceSystem = in.readString(this.sourceSystem instanceof Utf8 ? (Utf8)this.sourceSystem : null);
          break;

        case 1:
          this.sourceTxId = in.readString(this.sourceTxId instanceof Utf8 ? (Utf8)this.sourceTxId : null);
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










