//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.glowbyte.hc.integrationlayer.tasks;

import lombok.extern.slf4j.Slf4j;
import net.homecredit.hss.integration.offerstore.ofsdataimport.stubs.CreateOffer;
import net.homecredit.ofs.event.batchoffercreation.v1.BatchOfferCreationRequestCreated;
import net.homecredit.ofs.event.batchofferdeactivation.v1.BatchOfferDeactivationRequestCreated;
import org.apache.avro.specific.SpecificRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * This class implements sending messages messages to the OFS.
 * Messages are written to the kafka queue asynchronously message format - avroschema<br>
 * Based on application argument CHANNEL_CD one of the following methods will be called:
 * <ul>
 *     <li><b>{@link #mapOffer(Map)}</b> - when CHNANNEL_CD is OST, creates task for offer creatin</li>
 *     <li><b>{@link #mapDeactivation(Map)}</b> - when CHNANNEL_CD is DOS, creates task for offer deactivation</li>
 * </ul>
 */
@Slf4j
@Lazy
@Component("batch-create-offer")
public class BatchCreateOfferTask extends BasicTask {
    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    private KafkaTemplate<byte[], SpecificRecord> kafkaTemplate;
    @Value("${hc.batch-create-offer.select.sql}")
    private String selectSql;
    @Value("${hc.batch-create-offer.kafka.topic}")
    private String kafkaTopic;
    @Value("${hc.batch-create-offer.array.separator}")
    private String arraySeparator;
    @Value("${hc.batch-create-offer.header-source}")
    private String headerSource;
    @Value("${hc.batch-create-offer.header-data-schema}")
    private String headerDataSchema;
    @Value("${hc.batch-create-offer.header-type}")
    private String headerType;
    @Value("${hc.batch-create-offer.zone-id}")
    private String zoneId;
    @Value("${hc.batch-create-offer.key}")
    private String key;
    @Value("${hc.batch-create-offer.partition-key}")
    private String partitionKey;
    @Value("${hc.batch-create-offer.header-time}")
    private String headerTime;
    @Value("${hc.batch-create-offer.header-subject}")
    private String headerSubject;
    @Value("${hc.batch-create-offer.header-id}")
    private String headerId;

    public BatchCreateOfferTask() {
    }

    /**
     * This method returns new instance of the following class {@link CreateOffer} <br>
     * Using when CHANNEL_CODE is OST
     * @param row key-value pairs with selected from IN_TABLE
     * @return - OfsDataImport
     */
    private BatchOfferCreationRequestCreated mapOffer(Map<String, Object> row) {
        log.debug("mapping row {}", row);
        BatchOfferCreationRequestCreated offer = new BatchOfferCreationRequestCreated();
        offer.setPartyId(this.convert(row.get("ID_CUID"), String.class));
        if (row.get("LIMIT_ID") != null) {
            offer.setLimitId(this.convert(row.get("LIMIT_ID"), String.class));
        } else {
            offer.setLimitId("null");
        }
        offer.setOfferTypeCode(this.convert(row.get("OFFER_TYPE_CODE"), String.class));
        if(row.get("AMT_CREDIT_MAX") != null) {
            offer.setMaxCreditAmount(this.convert(row.get("AMT_CREDIT_MAX"), Long.class));
        }
        if(row.get("AMT_INSTALMENT_MAX") != null) {
          offer.setMaxMonthlyInst(this.convert(row.get("AMT_INSTALMENT_MAX"), Long.class));
        }
        if(row.get("ACCOUNT_NUMBER") != null) {
            offer.setAccountNumber(this.convert(row.get("ACCOUNT_NUMBER"), String.class));
        }
        if(row.get("AMT_DOWN_PAYMENT_MIN") != null) {
            offer.setMinCashPayment(this.convert(row.get("AMT_DOWN_PAYMENT_MIN"), Long.class));
        }
        offer.setOfferValidTo(this.convert(row.get("OFFER_VALID_TO"), String.class));
        offer.setPricingCategory(this.convert(row.get("PRICING_CATEGORY"), Integer.class));;
        offer.setOfferIdSas(this.convert(row.get("IL_COMMUNICATION_ID"), Integer.class));
        offer.setCrmPilotCode(this.convert(row.get("PROCESS_PILOT"), String.class));
        offer.setSourceProcessId("SASCM");
        offer.setAcqChannelCodes(Arrays.asList(((String)row.get("ACQUISITION_CHANNEL")).split(this.arraySeparator)));
        if(row.get("PRODUCT_CODES") != null){
            offer.setProductCodes(Arrays.asList(((String)row.get("PRODUCT_CODES")).split(this.arraySeparator)));
        } else{
            offer.setProductCodes(Collections.emptyList());
        }
        offer.setProductSetCode(this.convert(row.get("PRODUCT_SET_CODE"), String.class));
        if (row.get("OFFER_SUB_TYPE_CODE") != null) {
            offer.setOfferSubTypeCode(this.convert(row.get("OFFER_SUB_TYPE_CODE"), String.class));
        } else {
            offer.setOfferSubTypeCode("UNUSED");
        }
        offer.setCorrelationId(row.get("IL_COMMUNICATION_ID").toString());
        if (row.get("GUARANTEED_FLAG") != null) {
            String guaranteedFlag = this.convert(row.get("GUARANTEED_FLAG"), String.class);
            if (guaranteedFlag.equals("Y") || guaranteedFlag.equals("GUARANTEED") || guaranteedFlag.equals("true")) {
                offer.setGuaranteedFlag("Y");
            } else {
                offer.setGuaranteedFlag("N");
            }
        }
        return offer;
    }
    /**
     * This method returns new instance of the following class {@link CreateOffer} <br>
     * Using when CHANNEL_CODE is DOS
     * @param row key-value pairs with selected from IN_TABLE
     * @return - OfsDataImport
     */
    private BatchOfferDeactivationRequestCreated mapDeactivation(Map<String, Object> row) {
        log.debug("mapping row {}", row);
        BatchOfferDeactivationRequestCreated offer = new BatchOfferDeactivationRequestCreated();
        offer.setOfferId(this.convert(row.get("OFFER_ID"), String.class));
        offer.setDeactivationReason(this.convert(row.get("DEACTIVATION_REASON_OS"), String.class));
        offer.setCorrelationId(row.get("DEACT_ID").toString());
        return offer;
    }

    protected Map<String, Object> execute(String inTable, String outTable, Map<String, Object> context) {
        List<Map<String, Object>> batch = this.jdbcTemplate.queryForList(this.selectSql.replace("{}", inTable), context);
        List<Map<String, Object>> batchOut = this.jdbcTemplate.queryForList(this.selectSql.replace("{}", outTable), context);

        for (Map<String, Object> row : batch) {
            try {
                final SpecificRecord record;
                log.debug("Java channel is :" + context.get("CHANNEL_CODE").toString());
                if (context.get("CHANNEL_CODE").toString().equalsIgnoreCase("OST")) {
                    record = this.mapOffer(row);
                } else if (context.get("CHANNEL_CODE").toString().equalsIgnoreCase("DOS")) {
                    record = this.mapDeactivation(row);
                } else {
                    log.warn("Invalid CHANNEL_CODE: {}. Cannot map data to kafka", context.get("CHANNEL_CODE"));
                    continue;
                }

                log.debug("sending message {}", record);
                ProducerRecord<byte[], SpecificRecord> producerRecord = new ProducerRecord<>(this.kafkaTopic, toBytes(row.get(key)), record);
                setHeaders(row, producerRecord);
                kafkaTemplate.send(producerRecord);
                row.put("STATUS_NEW", "SUCCESS");
                batchOut.add(row);
            } catch (Exception var9) {
                log.error("can't send offer to kafka", var9);
                row.put("STATUS_NEW", "ERROR");
                batchOut.add(row);
            }
        }

        int count = this.dbService.simpleBatchInsert(outTable, batchOut);
        log.info("Processed batch of size {}, inserted {} rows", batch.size(), count);
        return null;
    }

    private void setHeaders(Map<String, Object> row, ProducerRecord<byte[], SpecificRecord> producerRecord) {
        if (headerDataSchema != null) {
            producerRecord.headers().add("ce_dataschema", toBytes(headerDataSchema));
        }
        producerRecord.headers().add("ce_specversion", toBytes("1.0"));
        if (headerId != null && row.get(headerId) != null) {
            producerRecord.headers().add("ce_id", toBytes(row.get(headerId)));
        }
        if (partitionKey != null && row.get(partitionKey) != null) {
            producerRecord.headers().add("ce_partitionkey", toBytes(row.get(partitionKey)));
        }
        if (headerSource != null) {
            producerRecord.headers().add("ce_source", toBytes(headerSource));
        }
        if (headerSubject != null && row.get(headerSubject) != null) {
            producerRecord.headers().add("ce_subject", toBytes(row.get(headerSubject)));
        }
        if (headerTime != null && row.get(headerTime) != null) {
            producerRecord.headers().add("ce_time", toBytes(((Timestamp) row.get(headerTime)).toInstant()
                    .atZone(ZoneId.of(zoneId))
                    .toOffsetDateTime()));
        }
        producerRecord.headers().add("event_time", toBytes(Instant.now()
                .atZone(ZoneId.of(zoneId))
                .toOffsetDateTime()));
        if (headerType != null) {
            producerRecord.headers().add("ce_type", toBytes(headerType));
        }
        producerRecord.headers().add("content-type", toBytes("application/avro"));
    }


    private byte[] toBytes(Object object) {
        if (object == null) {
            return new byte[]{};
        }
        return object.toString().getBytes(StandardCharsets.UTF_8);
    }
}
