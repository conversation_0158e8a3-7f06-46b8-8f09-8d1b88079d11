//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.glowbyte.hc.integrationlayer.tasks;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.glowbyte.hc.integrationlayer.ICConstants;
import com.glowbyte.hc.integrationlayer.JSONUtils;
import com.glowbyte.hc.integrationlayer.configuration.AvroSenderTableConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.data.TimeConversions;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.io.Encoder;
import org.apache.avro.io.EncoderFactory;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.avro.specific.SpecificRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * This class implements sending messages messages to the OFS. Messages are written to the kafka queue asynchronously message format - avroschema<br>
 */
@Slf4j
@Lazy
@Component("avrosender")
public class AvroSenderTask extends BasicTask {

  @Autowired
  private NamedParameterJdbcTemplate jdbcTemplate;

  @Autowired
  private KafkaTemplate<byte[], SpecificRecord> kafkaTemplate;

  @Autowired
  private AvroSenderTableConfiguration avroSenderTableConfiguration;

  public AvroSenderTask() {
  }

  protected Map<String, Object> execute(String inTable, String outTable, Map<String, Object> context) {
    List<Map<String, Object>> batch = this.jdbcTemplate.queryForList(avroSenderTableConfiguration.getSelectString().replace("{}", inTable), context);
    List<Map<String, Object>> batcOut = this.jdbcTemplate.queryForList(avroSenderTableConfiguration.getSelectString().replace("{}", outTable), context);

    SpecificData.get().addLogicalTypeConversion(new TimeConversions.TimestampConversion());

      // task can scan multiple tables. To add new table, it is necessary to manually specify mapping - see methods below
      for (AvroSenderTableConfiguration.AvroSenderTable table : avroSenderTableConfiguration.getTables()) {
        log.info("Processing table: {}", table.toString());

        log.info("batch size: {}", batch.size());
        for (Map<String, Object> row : batch) {
          try {
            SpecificRecord record = mapGenericClass(row, table);
            log.debug("Serialized record: " + record.toString());
//            byte[] serializedBody = serializeToJson(record);
            ProducerRecord<byte[], SpecificRecord> producerRecord = new ProducerRecord<>(table.getKafkaTopic(), row.get(table.getHeaderKey()).toString().getBytes(StandardCharsets.UTF_8), record);
            producerRecord.headers().add("ce_dataschema", table.getHeaderDataSchema().getBytes(StandardCharsets.UTF_8));
            producerRecord.headers().add("ce_specversion", "2".getBytes(StandardCharsets.UTF_8));
            producerRecord.headers().add("ce_id", row.get("IL_COMMUNICATION_ID").toString().getBytes(StandardCharsets.UTF_8));
            if (table.getHeaderPartitionKey() != null && row.get(table.getHeaderPartitionKey()) != null) {
              producerRecord.headers().add("ce_partitionkey", row.get(table.getHeaderPartitionKey()).toString().getBytes(StandardCharsets.UTF_8));
            }
            producerRecord.headers().add("ce_source", avroSenderTableConfiguration.getHeaderSource().getBytes(StandardCharsets.UTF_8));
            if (table.getHeaderSubject() != null && row.get(table.getHeaderSubject()) != null) {
              producerRecord.headers().add("ce_subject", row.get(table.getHeaderSubject()).toString().getBytes(StandardCharsets.UTF_8));
            }
            if (table.getHeaderTime() != null && row.get(table.getHeaderTime()) != null) {
              producerRecord.headers().add("ce_time", row.get(table.getHeaderTime()).toString().getBytes(StandardCharsets.UTF_8));
            }
            producerRecord.headers().add("ce_event_time", new Timestamp(System.currentTimeMillis()).toString().getBytes(StandardCharsets.UTF_8));
            producerRecord.headers().add("ce_type", table.getHeaderType().getBytes(StandardCharsets.UTF_8));
            producerRecord.headers().add("content_type", "application/avro".getBytes(StandardCharsets.UTF_8));
            kafkaTemplate.send(producerRecord);

            log.info("Processed row, id = {}", this.convert(row.get("ID"), Long.class));
            row.put(ICConstants.STATUS_FIELD, ICConstants.SUCCESS);
            batcOut.add(row);
          } catch (Exception e) {
            log.error("can't send offer to kafka", e);
            row.put(ICConstants.STATUS_FIELD, ICConstants.ERROR);
            batcOut.add(row);
          }
        }

        int count = dbService.simpleBatchInsert(outTable, batcOut);
        log.info("Processed batch of size {}, inserted {} rows", batch.size(), count);
      }
      return null;
  }

  SpecificRecord mapGenericClass(Map<String, Object> row, AvroSenderTableConfiguration.AvroSenderTable table) throws JSONException, ClassNotFoundException, JsonProcessingException {
    JSONObject outObject = new JSONObject();
    JSONObject jsonConfig = new JSONObject(table.getMapping());
    outObject = (JSONObject) JSONUtils.deepObjectGenerate(jsonConfig, outObject, "ROOT", row, zoneId);
//    outObject.put("eventName", table.getName()); // setting eventName manually, will always be mapped from table name
    ObjectMapper mapper = new ObjectMapper();
    mapper.setSerializationInclusion(Include.NON_EMPTY);
    Class<? extends SpecificRecord> clazz = (Class<? extends SpecificRecord>) Class.forName(table.getClassName());
    SpecificRecord record = mapper.readValue(outObject.toString(), clazz);
    return record;
  }

  private static <T extends SpecificRecord> byte[] serializeToJson(T record) {
    DatumWriter<T> writer = new SpecificDatumWriter(record.getSchema());
    ByteArrayOutputStream stream = new ByteArrayOutputStream();
    try {
      Encoder jsonEncoder = EncoderFactory.get().jsonEncoder(record.getSchema(), stream);
      writer.write(record, jsonEncoder);
      jsonEncoder.flush();
    } catch (IOException e) {
      log.error("Serialization error:" + e.getMessage());
    }
    return stream.toByteArray();
  }
}
