package com.glowbyte.hc.integrationlayer.tasks;

import com.glowbyte.hc.integrationlayer.AvroUtils;
import com.glowbyte.hc.integrationlayer.ICConstants;
import com.glowbyte.model.Action.Action;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;


@Slf4j
@Lazy
@Component("rabbit-sender")
public class RabbitSenderTask extends BasicTask {

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    private AmqpTemplate template;

    @Value("${rabbitmq.selectSql}")
    private String selectSql;
    @Value("${rabbitmq.queues:myqueue}")
    private String queues;

    @Override
    protected Map<String, Object> execute(String inTable, String outTable, Map<String, Object> context) {
        List<Map<String, Object>> batch = jdbcTemplate.queryForList(selectSql.replace("{}", inTable), context);
        List<Map<String, Object>> batcOut = jdbcTemplate.queryForList(selectSql.replace("{}", outTable), context);

        Iterator var6 = batch.iterator();

        while (var6.hasNext()) {
            Map row = (Map) var6.next();

            try {
                Action action = new Action();
                log.debug("Java channel is :" + context.get("CHANNEL_CODE").toString());

                action = this.mapAction(row);
                log.debug("sending message {}", action);

                Message message = MessageBuilder.withBody(AvroUtils.serialize(action)).build();
                template.convertAndSend(queues, message);

                row.put(ICConstants.STATUS_FIELD, ICConstants.SUCCESS);
                batcOut.add(row);
            } catch (Exception var9) {
                log.error("can't send table to Amqp", var9);
                row.put(ICConstants.STATUS_FIELD, ICConstants.ERROR);
                batcOut.add(row);
            }
        }


        int count = dbService.simpleBatchInsert(outTable, batcOut);
        log.info("Processed batch of size {}, inserted {} rows", batch.size(), count);
        return null;
    }


    Action mapAction(Map<String, Object> row) {
        log.debug("mapping row {}", row);
        Action action = new Action();
        action.setActionId((Long) this.convert(row.get("ACTION_ID"), Long.class));
        action.setDate((Long) this.convert(row.get("DATE_"), Long.class));
        action.setCardId((Long) this.convert(row.get("CARD_ID"), Long.class));
        action.setPlace((String) this.convert(row.get("PLACE"), String.class));
        action.setNumber((Long) this.convert(row.get("NUMBER_"), Long.class));
        action.setSumma((Long) this.convert(row.get("SUMMA"), Long.class));
        return action;
    }


}
