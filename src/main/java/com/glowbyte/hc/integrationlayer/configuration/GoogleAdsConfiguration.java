package com.glowbyte.hc.integrationlayer.configuration;

import com.google.ads.googleads.lib.GoogleAdsClient;
import com.google.auth.oauth2.UserCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile({"ggl", "dggl", "vggl"})
@ConditionalOnProperty(prefix = "google", name = {"client-id", "client-secret", "dev-token", "refresh-token", "login-client-id"})
@Slf4j
public class GoogleAdsConfiguration {

    @Value("${google.client-id}")
    private String clientID;

    @Value("${google.client-secret}")
    private String clientSecret;

    @Value("${google.dev-token}")
    private String devToken;

    @Value("${google.refresh-token}")
    private String refreshToken;

    @Value("${google.login-client-id}")
    private Long loginClientID;

    @Value("${google.proxy.host:}")
    private String proxyHost;

    @Value("${google.proxy.port:}")
    private Integer proxyPort;

    @Bean
    public GoogleAdsClient googleAdsClient() {
        if (proxyHost != null && proxyPort != null) {
            log.info("Using proxy host: {}, port: {}", proxyHost, proxyPort);
            System.setProperty("https.proxyHost", proxyHost);
            System.setProperty("https.proxyPort", proxyPort.toString());
        }
        return GoogleAdsClient.newBuilder()
                .setDeveloperToken(devToken)
                .setLoginCustomerId(loginClientID)
                .setCredentials(UserCredentials.newBuilder()
                        .setClientId(clientID)
                        .setClientSecret(clientSecret)
                        .setRefreshToken(refreshToken)
                        .build())
                .build();
    }
}
