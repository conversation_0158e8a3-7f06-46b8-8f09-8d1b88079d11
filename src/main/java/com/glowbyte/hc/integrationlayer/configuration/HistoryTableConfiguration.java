package com.glowbyte.hc.integrationlayer.configuration;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "hc.history")
@Configuration
@Data
public class HistoryTableConfiguration {

  private List<HistoryTable> tables;
  private String selectString;
  private String updateSentString;
  private String updateConfirmedString;
  private String selectJournalIdString;
  private String updateJournalIdString;
  private Long exportInterval;
  private String headerSource;
  private String zoneId;

  @Data
  public static class HistoryTable {

    private String name;
    private String journalIdName;
    private Long lastProcessedId = 0L;
    private String kafkaTopic;
    private String headerDataSchema;
    private String headerType;
    private String headerTime;
    private String headerPartitionKey;
    private String headerKey;
    private String headerSubject;
    private String className;
    private String mapping;
  }
}
