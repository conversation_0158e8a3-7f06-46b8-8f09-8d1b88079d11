package com.glowbyte.hc.integrationlayer.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@ConfigurationProperties(prefix = "hc.avrosender")
@Configuration
@Data
public class AvroSenderTableConfiguration {

  private List<AvroSenderTable> tables;
  private String selectString;
  private String selectJournalIdString;
  private String updateJournalIdString;
  private String headerSource;

  @Data
  public static class AvroSenderTable {

    private String name;
    private String journalIdName;
    private Long lastProcessedId = 0L;
    private String kafkaTopic;
    private String headerDataSchema;
    private String headerType;
    private String headerTime;
    private String headerPartitionKey;
    private String headerKey;
    private String headerSubject;
    private String className;
    private String mapping;
  }
}
