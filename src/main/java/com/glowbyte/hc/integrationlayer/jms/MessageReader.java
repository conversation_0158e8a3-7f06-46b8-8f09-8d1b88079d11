package com.glowbyte.hc.integrationlayer.jms;


import lombok.extern.slf4j.Slf4j;

import javax.jms.*;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

/**
 * Class for receiving JMS messages
 */
@Slf4j
public class MessageReader extends BaseMessage {
    private QueueReceiver consumer;

    public MessageReader(ConnectionHolder connectionHolder) throws JMSException {
        super(connectionHolder);
    }

    public String[] readMessages() throws JMSException {
        List<String> messages = new ArrayList<>();
        if (getQueueSession() == null) {
            createSession();
        }
        getQueueConnection().start();
        consumer = getQueueSession().createReceiver(getConnectionHolder().queue);
        System.out.println(getConnectionHolder().queue.getQueueName());
        try {
            while (true) {
                TextMessage textMsg = (TextMessage) consumer.receive(500);
                if (textMsg == null || textMsg.getText().equals("END") ) {
                    System.out.println("There are no messages");
                    break;
                } else {
                    System.out.println(textMsg);
                    System.out.println("Received: " + textMsg.getText());
                    messages.add(textMsg.getText());
                }
            }

            getQueueConnection().stop();
        } catch (JMSException e) {
            e.printStackTrace();
        } finally {
            if (getQueueSession() != null) {
                getQueueSession().close();
            }
            if (getQueueConnection() != null) {
                getQueueConnection().close();
            }
        }
        if (messages.size() ==0){
            return new String[0];
        }
        String[] arr  = new String[messages.size()];
        return  messages.toArray(arr);
    }

    public static MessageReader getMessageReader(String provider, String username,String password,String connectionFactoryName, String queueName) throws NamingException, JMSException{
        Hashtable properties = new Hashtable();
        properties.put(Context.INITIAL_CONTEXT_FACTORY, "weblogic.jndi.WLInitialContextFactory");
        log.info("Provider:"+ provider + " username:" + username);
        properties.put(Context.PROVIDER_URL, provider);
        properties.put(Context.SECURITY_PRINCIPAL, username);
        properties.put(Context.SECURITY_CREDENTIALS, password);
        System.out.println("Connecting to '" + properties.get(Context.PROVIDER_URL) + "'");
        log.trace("Connecting to " + properties.get(Context.PROVIDER_URL) + "'");
        InitialContext initialContext = new InitialContext(properties);
        return new MessageReader(new MessageReader.ConnectionHolder(initialContext, connectionFactoryName, queueName));
    }


}
