package com.glowbyte.hc.integrationlayer;

import org.springframework.boot.convert.ApplicationConversionService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.ConversionService;
import org.springframework.format.datetime.DateFormatter;

/**
 * Technical class for custom Dates converter
 */
@Configuration
public class ICConfiguration {
    public ICConfiguration() {
    }

    @Bean
    public ConversionService conversionService() {
        ConversionService conversionService = ApplicationConversionService.getSharedInstance();
        ((ApplicationConversionService) conversionService).addFormatter(new DateFormatter(ICConstants.ISO8601_TIMESTAMP_FORMAT_DEFINITION));
        return conversionService;
    }
}
