package com.glowbyte.hc.integrationlayer.audience.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.glowbyte.hc.integrationlayer.audience.model.tiktok.ApiResponse;
import com.glowbyte.hc.integrationlayer.audience.model.tiktok.AudienceListResponse;
import com.glowbyte.hc.integrationlayer.audience.model.tiktok.AudienceResponse;
import com.glowbyte.hc.integrationlayer.audience.model.tiktok.UpdateAudienceRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.List;

@Profile({"ttk", "dttk", "vttk"})
@Component
@Slf4j
public class TiktokClient {

    @Value("${tiktok.access-token}")
    private String accessToken;

    @Value("${tiktok.advertiser-id}")
    private String advertiserId;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    OkHttpClient tiktokClient;

    private static final String BASE_URL = "https://business-api.tiktok.com";
    private static final String FILE_UPLOAD = "/open_api/v1.3/dmp/custom_audience/file/upload/";
    private static final String GET_AUDIENCE_DETAILS = "/open_api/v1.3/dmp/custom_audience/get/";
    private static final String UPDATE_AUDIENCE = "/open_api/v1.3/dmp/custom_audience/update/";
    private static final String GET_ALL_AUDIENCES = "/open_api/v1.3/dmp/custom_audience/list/";

    public AudienceListResponse getAllAudiences() throws Exception {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(BASE_URL + GET_ALL_AUDIENCES).newBuilder();

        urlBuilder.addQueryParameter("advertiser_id", advertiserId);
        urlBuilder.addQueryParameter("page_size", "100");
        String url = urlBuilder.build().toString();
        Request httpRequest = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Access-Token", accessToken)
                .build();

        log.info("Obtaining all audiences: {}", url);
        try (Response response = tiktokClient.newCall(httpRequest).execute()) {
            String responseString = response.body().string();
            return objectMapper.readValue(responseString, AudienceListResponse.class);
        } catch (Exception e) {
            log.error("Failed to get all audiences.", e);
            throw e;
        }
    }

    public AudienceResponse getAudience(String audienceId) throws Exception {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(BASE_URL + GET_AUDIENCE_DETAILS).newBuilder();
        urlBuilder.addQueryParameter("advertiser_id", advertiserId);
        urlBuilder.addQueryParameter("custom_audience_ids", audienceId);
        String url = urlBuilder.build().toString();

        Request httpRequest = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Access-Token", accessToken)
                .build();
        try (Response response = tiktokClient.newCall(httpRequest).execute()) {
            String responseString = response.body().string();

            return objectMapper.readValue(responseString, AudienceResponse.class);
        } catch (Exception e) {
            log.error("Failed to get audience by id {}", audienceId, e);
            throw e;
        }
    }

    public ApiResponse updateAudience(String audienceId, List<String> filePaths, String action) throws Exception{
        String url = BASE_URL + UPDATE_AUDIENCE;

        UpdateAudienceRequest request = UpdateAudienceRequest.builder()
                .advertiserId(advertiserId)
                .customAudienceId(audienceId)
                .filePaths(filePaths)
                .action(action)
                .build();

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(request), MediaType.parse("application/json"));
        try {
            return executeHttpRequest(url, body);
        }  catch (Exception e) {
            log.error("Failed to update audience by id {}", audienceId, e);
            throw e;
        }
    }

    public ApiResponse uploadFile(String fileSignature, String calculateType, String fileName, File file) throws Exception {
        String url = BASE_URL + FILE_UPLOAD;
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("advertiser_id", advertiserId)
                .addFormDataPart("file_signature", fileSignature)
                .addFormDataPart("calculate_type", calculateType)
                .addFormDataPart("file", fileName,
                        RequestBody.create(file, MediaType.parse("application/octet-stream")))
                .build();

        try {
            return executeHttpRequest(url, body);
        }  catch (Exception e) {
            log.error("Failed upload file {} with calculate type {}", fileName, calculateType, e);
            throw e;
        }
    }

    private ApiResponse executeHttpRequest(String url, RequestBody body) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Access-Token", accessToken)
                .build();

        String responseString;
        try (Response response = tiktokClient.newCall(request).execute()) {
            responseString = response.body().string();
        }
        return objectMapper.readValue(responseString, ApiResponse.class);
    }
}
