package com.glowbyte.hc.integrationlayer.audience.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AudienceUser {
    String phoneNumber;
    String email;
    String mobileId;
    MobilePlatform mobilePlatform;

    /**
     * Enum to represent mobile platform types for mobile ID targeting
     */
    public enum MobilePlatform {
        ANDROID,  // Google Advertising ID (GAID)
        IOS       // Identifier for Advertisers (IDFA)
    }
}
