package com.glowbyte.hc.integrationlayer.audience.model.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.util.List;

@Builder
public class UpdateAudienceRequest {
    @JsonProperty("advertiser_id")
    private String advertiserId;
    @JsonProperty("custom_audience_id")
    private String customAudienceId;
    @JsonProperty("file_paths")
    private List<String> filePaths;
    @JsonProperty
    private String action;
}
