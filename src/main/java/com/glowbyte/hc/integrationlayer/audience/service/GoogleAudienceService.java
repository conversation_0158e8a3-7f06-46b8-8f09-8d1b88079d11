
package com.glowbyte.hc.integrationlayer.audience.service;

import com.glowbyte.hc.integrationlayer.audience.model.AudienceUser;
import com.glowbyte.hc.integrationlayer.audience.model.JobResult;
import com.google.ads.googleads.lib.GoogleAdsClient;
import com.google.ads.googleads.v19.common.CustomerMatchUserListMetadata;
import com.google.ads.googleads.v19.common.UserData;
import com.google.ads.googleads.v19.common.UserIdentifier;
import com.google.ads.googleads.v19.enums.OfflineUserDataJobStatusEnum;
import com.google.ads.googleads.v19.enums.OfflineUserDataJobTypeEnum;
import com.google.ads.googleads.v19.errors.GoogleAdsException;
import com.google.ads.googleads.v19.resources.OfflineUserDataJob;
import com.google.ads.googleads.v19.resources.UserList;
import com.google.ads.googleads.v19.services.*;
import com.google.ads.googleads.v19.utils.ResourceNames;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * GoogleSenderEngine is an implementation of the {@link AudienceService} for handling user data in Google Ads.
 * <p>
 * This class provides methods for interacting with the Google Ads API, specifically for adding or removing
 * users from customer match lists using the Offline User Data Job API. It handles batching of operations
 * and supports Google Ads API querying to search and update audience lists.
 * </p>
 */
@Profile({"ggl", "dggl", "vggl"})
@Component
@Lazy
@Slf4j
public class GoogleAudienceService extends AudienceService {

    @Autowired
    private GoogleAdsClient googleAdsClient;

    @Value("${hc.ads.batch-payload}")
    protected int batchPayload;

    /**
     * Searches for Google Ads user list IDs based on segment names.
     * <p>
     * This method queries Google Ads for user lists that match the segment names and populates the provided
     * map with the corresponding user list IDs.
     * </p>
     *
     * @param segmentNames A list of segment names.
     */
    public Map<String, Object> getSegmentIds(Set<String> segmentNames) {
        log.info("Obtaining list of custom audiences.");
        String joined = segmentNames.stream()
                .map(segment -> "'" + segment + "'")
                .collect(Collectors.joining(", "));

        // Create the query to search for user lists by segment name
        String query = String.format("SELECT user_list.id, user_list.name " +
                        "FROM user_list " +
                        "WHERE user_list.name IN (%s)",
                joined);
        log.debug("Search user list query: {}", query);

        String proxyHost = System.getProperty("https.proxyHost");
        String proxyPort = System.getProperty("https.proxyPort");
        log.debug("Using proxy host: {}, port: {}", proxyHost, proxyPort);

        // Execute the query and update the segmentHash map with the found user list IDs
        SearchGoogleAdsRequest request = getSearchGoogleAdsRequest(query);
        log.debug(request.toString());
        Map<String, Object> segmentIds = new HashMap<>();
        try (GoogleAdsServiceClient adServiceClient = this.googleAdsClient.getLatestVersion().createGoogleAdsServiceClient()){
            GoogleAdsServiceClient.SearchPagedResponse searchPagedResponse = adServiceClient.search(request);

            // Iterate over the results and update the segmentHash map
            for (GoogleAdsRow row : searchPagedResponse.iterateAll()) {
                UserList userList = row.getUserList();
                log.info("User list found with ID: {} and Name: {}", userList.getId(), userList.getName());
                segmentIds.put(userList.getName(), userList.getId());
            }
        } catch (Exception e) {
            log.error("Failed to search for user list: {}", e.getMessage());
            throw e;
        }
        return segmentIds;
    }

    /**
     * Formats a phone number into E.164 standard.
     * <p>
     * This method takes an input phone number and formats it into the E.164 format, which is the international
     * phone number format. It simply prepends a "+" to the input string, assuming the input is the national number.
     * </p>
     * <p>
     * The E.164 format includes a "+" followed by the country code and the phone number (e.g., +1234567890).
     * </p>
     *
     * @param input The phone number to format, typically without the leading "+".
     * @return The phone number formatted in E.164 format.
     */
    @Override
    public String e164formater(String input) {
        return "+" + input;
    }

    /**
     * Updates a Google Ads user list with the provided segment data.
     * <p>
     * This method processes the segment data and adds or removes users from the Google Ads user list
     * by submitting operations in batches using the {@link OfflineUserDataJobServiceClient}.
     * </p>
     *
     * @param action         Add or delete
     * @param segmentId      The ID of the segment being processed.
     * @param users          User list to be added or removed.
     * @return The ID of the created offline user data job.
     */
    public String updateSegmentGroup(Object segmentId, String action, List<AudienceUser> users){
        log.info("Preparing update job for action {} and segmentId {}", action, segmentId);
        List<UserData> userData = users.stream()
                .map(user -> {
                    UserData.Builder userDataBuilder = UserData.newBuilder();
                    if (user.getEmail() != null) {
                        UserIdentifier hashedEmailIdentifier = UserIdentifier.newBuilder()
                                .setHashedEmail(this.hashEmail(user.getEmail()))
                                .build();
                        userDataBuilder.addUserIdentifiers(hashedEmailIdentifier);
                    }
                    if (user.getPhoneNumber() != null) {
                        UserIdentifier hashedPhoneNumberIdentifier = UserIdentifier.newBuilder()
                                .setHashedPhoneNumber(this.hashPhoneNumber(user.getPhoneNumber()))
                                .build();
                        userDataBuilder.addUserIdentifiers(hashedPhoneNumberIdentifier );
                    }
                    if (user.getMobileId() != null) {
                        UserIdentifier hashedMobileIdIdentifier = UserIdentifier.newBuilder()
                                .setMobileId(user.getMobileId())
                                .build();
                        userDataBuilder.addUserIdentifiers(hashedMobileIdIdentifier );
                    }
                    return userDataBuilder.build();
                }).collect(Collectors.toList());

        // Initialize Offline User Data Job
        log.debug("Initializing OfflineUserDataJob...");
        OfflineUserDataJob offlineUserDataJob = OfflineUserDataJob.newBuilder()
                .setType(OfflineUserDataJobTypeEnum.OfflineUserDataJobType.CUSTOMER_MATCH_USER_LIST)
                .setCustomerMatchUserListMetadata(
                        CustomerMatchUserListMetadata.newBuilder()
                                .setUserList(ResourceNames.userList(googleAdsClient.getLoginCustomerId(), (Long) segmentId))
                                .build()
                )
                .build();

        // Create Offline User Data Job
        log.debug("Creating OfflineUserDataJob...");
        try (OfflineUserDataJobServiceClient client = this.googleAdsClient.getLatestVersion().createOfflineUserDataJobServiceClient()) {
            CreateOfflineUserDataJobResponse createOfflineUserDataJobResponse = client
                    .createOfflineUserDataJob(String.valueOf(googleAdsClient.getLoginCustomerId()), offlineUserDataJob);

            String offlineUserDataJobResourceName = createOfflineUserDataJobResponse.getResourceName();

            List<OfflineUserDataJobOperation> operationsBatch = new ArrayList<>();

            if (action.equals("A")) {
                // Add UserData chunks
                log.debug("Processing Add UserData chunks...");
                for (UserData addUserData : userData) {
                    if (addUserData.getUserIdentifiersCount() > 0) {
                        OfflineUserDataJobOperation operation = OfflineUserDataJobOperation.newBuilder()
                                .setCreate(addUserData)
                                .build();
                        operationsBatch.add(operation);

                        if (operationsBatch.size() == batchPayload) {
                            submitOperationsBatch(offlineUserDataJobResourceName, operationsBatch);
                            operationsBatch.clear(); // Clear the batch after submission
                        }
                    }
                }
            } else if (action.equals("D")) {
                // Remove UserData chunks
                log.debug("Processing Remove UserData chunks...");
                for (UserData removeUserData : userData) {
                    if (removeUserData.getUserIdentifiersCount() > 0) {
                        OfflineUserDataJobOperation operation = OfflineUserDataJobOperation.newBuilder()
                                .setRemove(removeUserData)
                                .build();
                        operationsBatch.add(operation);

                        if (operationsBatch.size() == batchPayload) {
                            submitOperationsBatch(offlineUserDataJobResourceName, operationsBatch);
                            operationsBatch.clear(); // Clear the batch after submission
                        }
                    }
                }
            }

            // Submit any remaining operations in the batch
            if (!operationsBatch.isEmpty()) {
                log.info("Processing remaining operations in batch: {}", operationsBatch);
                submitOperationsBatch(offlineUserDataJobResourceName, operationsBatch);
            }

            log.info("Operations submitted. OfflineUserDataJobResourceName: {}", offlineUserDataJobResourceName);

            // Optional: Uncomment this to run the job after submitting operations
            client.runOfflineUserDataJobAsync(offlineUserDataJobResourceName);

            String jobId = offlineUserDataJobResourceName.substring(offlineUserDataJobResourceName.lastIndexOf('/') + 1);
            log.info("Generated JOB_ID: {}", jobId);
            return jobId;
        } catch (Exception e) {
            log.error("Exception occurred during API request to Google", e);
            return null;
        }
    }

    /**
     * Updates the result of a specific job for a Google Ads offline user data job.
     * <p>
     * This method queries the Google Ads API to check the status of the offline user data job
     * (e.g., "PENDING", "RUNNING", "SUCCESS", "FAILED") and updates the status in the database accordingly.
     * </p>
     *
     * @param jobID The ID of the offline user data job.
     */
    @Override
    public JobResult updateJobResult(String jobID) {
        log.info("Updating job result for JOB_ID: {}", jobID);
        String offlineUserDataJobResourceName = ResourceNames.offlineUserDataJob(googleAdsClient.getLoginCustomerId(), Long.parseLong(jobID));
        try (GoogleAdsServiceClient googleAdsServiceClient = this.googleAdsClient.getLatestVersion().createGoogleAdsServiceClient()) {
            // Create the query to retrieve the job status
            String query = String.format(
                    "SELECT offline_user_data_job.resource_name, offline_user_data_job.id, " +
                            "offline_user_data_job.status FROM offline_user_data_job " +
                            "WHERE offline_user_data_job.resource_name = '%s'", offlineUserDataJobResourceName
            );

            // Create the request
            SearchGoogleAdsRequest request = getSearchGoogleAdsRequest(query);

            // Execute the search request
            for (GoogleAdsRow googleAdsRow : googleAdsServiceClient.search(request).iterateAll()) {
                OfflineUserDataJob offlineUserDataJob = googleAdsRow.getOfflineUserDataJob();
                OfflineUserDataJobStatusEnum.OfflineUserDataJobStatus status = offlineUserDataJob.getStatus();

                switch (status) {
                    case PENDING:
                        return new JobResult(2, "PENDING", null);
                    case RUNNING:
                        return new JobResult(2, "PROCESSING", null);
                    case SUCCESS:
                        return new JobResult(1, "COMPLETED", null);
                    case FAILED:
                        return new JobResult(-1, "FAILED", offlineUserDataJob.getFailureReason().toString());
                    default:
                        return new JobResult(-1, status.toString(), "Job returned unknown status.");
                }
            }
        } catch (GoogleAdsException e) {
            log.error("Google Ads API request failed", e);
        }
        return null;
    }

    /**
     * Helper method to submit a batch of operations for a Google Ads offline user data job.
     *
     * @param jobResourceName The resource name of the offline user data job.
     * @param operationsBatch The batch of operations to submit.
     */
    private void submitOperationsBatch(String jobResourceName, List<OfflineUserDataJobOperation> operationsBatch) {
        log.debug("Submitting {} operations...", operationsBatch.size());
        try (OfflineUserDataJobServiceClient client = this.googleAdsClient.getLatestVersion().createOfflineUserDataJobServiceClient()) {
            client.addOfflineUserDataJobOperations(jobResourceName, operationsBatch);
        } catch (GoogleAdsException e) {
            log.error("Google Ads API request failed: ", e);
        }
    }

    /**
     * Builds a {@link SearchGoogleAdsRequest} for querying the Google Ads API.
     *
     * @param query      The query string to execute.
     * @return The built {@link SearchGoogleAdsRequest}.
     */
    private SearchGoogleAdsRequest getSearchGoogleAdsRequest(String query) {
        return SearchGoogleAdsRequest.newBuilder()
                .setCustomerId(String.valueOf(googleAdsClient.getLoginCustomerId()))
                .setQuery(query)
                .build();
    }
}


