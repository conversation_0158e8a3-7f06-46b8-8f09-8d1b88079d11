package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.model.message.PshMessage;
import com.homecredit.model.messageattribues.PshMessageAttributes;
import com.homecredit.service.ReceiverAgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("psh")
public class PshReceiverAgentService extends ReceiverAgentService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;
    private final ObjectMapper mapper;

    @Override
    @Async
    public void processEvent(String eventMessage) {
        log.info("Received event: {}", eventMessage);
        //configuration message from ci360
        if (eventMessage.startsWith("CFG")) {
            return;
        }

        try {
            PshMessage message = mapper.readValue(eventMessage, PshMessage.class);

            if (message.getAttributes() == null) {
                log.warn("Message has no 'attributes'. Cannot parse message.");
                return;
            }
            if (message.getAttributes().getEventName() == null) {
                log.warn("Message attributes have no 'eventName'. Cannot parse message.");
                return;
            }

            if (message.getAttributes().getEventName().toUpperCase().startsWith(applicationConfig.getCi360().getPrefix())) {
                log.info("Event received, will be processing. eventName: {}", message.getAttributes().getEventName());
                processMessage(message.getAttributes());
            } else {
                log.error("CH_01 - Event name {} is not matching with {}% prefix", message.getAttributes().getEventName(), applicationConfig.getCi360().getPrefix());
            }
        } catch (JsonProcessingException e) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload", e);
        }
    }

    private void processMessage(PshMessageAttributes attributes) {
        log.debug("Creating new record with data {}", attributes);

        Map<String, String> creativeContent = parseCreativeContent(attributes.getCreativeContent());
        if (creativeContentInvalid(creativeContent, attributes.getEventName())) {
            return;
        }
        String leadId = getLeadId(attributes.getDatahubId(), attributes.getResponseTrackingCode(), attributes.getTimestamp());

        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreData())) {
                setTimestampParameter(stmt, 1, getTimeRangeMinDttm(attributes.getTaskpropPshValidityType(), attributes.getTaskPropPshDateValidStart(), attributes.getTaskpropPshTimeValidStart()));
                setTimestampParameter(stmt, 2, getTimeRangeMaxDttm(attributes.getTaskpropPshValidityType(), attributes.getTaskPropPshDateValidEnd(), attributes.getTaskpropPshTimeValidStart(), attributes.getTaskpropPshTimeValidDuration()));
                setStringParameter(stmt, 3, attributes.getDatahubId());
                setStringParameter(stmt, 4, attributes.getSubjectId());
                setStringParameter(stmt, 5, attributes.getCustomerId());
                setStringParameter(stmt, 6, attributes.getVid());
                setStringParameter(stmt, 7, creativeContent.get("text"));
                setStringParameter(stmt, 8, creativeContent.get("title"));
                setStringParameter(stmt, 9, stringToBooleanInt(attributes.getTaskpropPshDoNotStoreInInbox()));
                setStringParameter(stmt, 10, removeExtraQuotes(attributes.getTaskpropPshCategoryId()));
                setStringParameter(stmt, 11, leadId);
                setStringParameter(stmt, 12, attributes.getResponseTrackingCode());
                setStringParameter(stmt, 13, attributes.getTaskId());
                setStringParameter(stmt, 14, attributes.getTaskVersionId());
                setStringParameter(stmt, 15, attributes.getCreativeId());
                stmt.executeUpdate();
            } catch (SQLException e) {
                log.error("CH_03_2 - DB - not possible to write payload to DB", e);
            }
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }
    }

    @Override
    protected Timestamp getTimeRangeMinDttm(String validityType, LocalDate dateValidStart, String timeValidStart) {
        String cleanValidityType = validityType.replaceAll("\"", "");
        if (cleanValidityType.equalsIgnoreCase("Absolute")) {
            if (dateValidStart == null) {
                return null;
            } else {
                return Timestamp.valueOf(dateValidStart.atStartOfDay());
            }
        } else if (cleanValidityType.equalsIgnoreCase("Relative")) {
            if (timeValidStart == null) {
                return Timestamp.valueOf(LocalDateTime.now()); //change from common method (null -> sysdate)
            } else {
                return Timestamp.valueOf(LocalDateTime.now()
                        .plusDays(Long.parseLong(timeValidStart)));
            }
        }
        log.warn("Unknown validity type: {}. Returning null TimeRangeMinDttm", validityType);
        return null;
    }

    @Override
    protected Timestamp getTimeRangeMaxDttm(String validityType, LocalDate dateValidEnd, String timeValidStart, String timeValidDuration) {
        String cleanValidityType = validityType.replaceAll("\"", "");
        if (cleanValidityType.equalsIgnoreCase("Absolute")) {
            if (dateValidEnd == null) {
                return null;
            } else {
                return Timestamp.valueOf(dateValidEnd.atTime(END_OF_DAY));
            }
        } else if (cleanValidityType.equalsIgnoreCase("Relative")) {
            if (timeValidDuration == null) {
                return null;
            } else {
                if (timeValidStart == null) {
                    return Timestamp.valueOf(LocalDate.now().atTime(END_OF_DAY)
                            .plusDays(Long.parseLong(timeValidDuration)));
                } else {
                    return Timestamp.valueOf(LocalDate.now().atTime(END_OF_DAY)
                            .plusDays(Long.parseLong(timeValidDuration))
                            .plusDays(Long.parseLong(timeValidStart)));
                }
            }
        }
        log.warn("Unknown validity type: {}. Returning null TimeRangeMaxDttm", validityType);
        return null;
    }
}
