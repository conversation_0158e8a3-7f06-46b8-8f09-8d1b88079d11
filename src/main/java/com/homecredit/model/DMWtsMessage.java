package com.homecredit.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class DMWtsMessage extends Message {

    private String externalId;
    private String recipient;
    private String systemCode;
    private String messageCode;
    private String cuid;
    private String text;
    private ZonedDateTime expires;
    private List<Attribute> attributes;
    private String priority;
    private String reportLevel;

    @JsonIgnore
    private ZonedDateTime generatedDateTime;
    @JsonIgnore
    private Long id;
    @JsonIgnore
    private String leadId;
    @JsonIgnore
    private String contactId;
}
