package com.homecredit.model.calllisttype;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class CallListTypeCache {

    private CallListTypeCache() {
    }

    private static Map<String, Set<CallListType>> cache = new HashMap<>();

    public static void add(CallListType value) {
        String code = value.getCallListType().toUpperCase();
        cache.computeIfAbsent(code, f -> new HashSet<>()).add(value);
    }

    public static Set<CallListType> get(String code) {
        return cache.get(code.toUpperCase());
    }

    public static void clear() {
        cache = new HashMap<>();
    }
}
