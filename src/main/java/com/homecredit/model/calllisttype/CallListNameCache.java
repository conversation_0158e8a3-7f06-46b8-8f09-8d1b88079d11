package com.homecredit.model.calllisttype;

import java.util.HashMap;
import java.util.Map;

public class CallListNameCache {

    private CallListNameCache() {
    }

    private static Map<Long, CallListName> cache = new HashMap<>();

    public static void add(CallListName value) {
        Long tplId = value.getTplId();
        cache.put(tplId, value);
    }

    public static CallListName get(Long tplId) {
        try {
            return cache.get(tplId);
        } catch (NullPointerException e) {
            return null;
        }
    }

    public static void clear() {
        cache = new HashMap<>();
    }
}
