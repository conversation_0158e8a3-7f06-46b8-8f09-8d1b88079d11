package com.homecredit.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class IdLmaMessage extends Message {

    private String phoneNumber;
    private String workflow;

    @JsonProperty("messageAttribute")
    private String messageAttributeString;
    private String messageId;
    @JsonProperty("additionalData")
    private String additionalDataString;

    @JsonIgnore
    private IdLmaMessageAttribute messageAttribute;
    @JsonIgnore
    private IdLmaAdditionalData additionalData;

    @JsonIgnore
    private ZonedDateTime generatedDateTime;
    @JsonIgnore
    private Long id;
    @JsonIgnore
    private String contactId;
}
