package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

@Data
@ToString(callSuper = true)
public class GenMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("call_source")
    private String callSource;
    @JsonProperty("call_list_type")
    private String callListType;
    @JsonProperty("contact_info")
    private String contactInfo;
    @JsonProperty("contract_code")
    private String contractCode;
    @JsonProperty("contributing_guid_1")
    private String contributingGuid1;
    @JsonProperty("custom_info2")
    private String customInfo2;
    @JsonProperty("custom_info3")
    private String customInfo3;
    @JsonProperty("custom_info4")
    private String customInfo4;
    private String gender;
    private String language1;
    @JsonProperty("last_first_name")
    private String lastFirstName;
    @JsonProperty("SITPhoneNum")
    private String SITPhoneNum;
    private String sentInbox;
    private String sentPush;
    private String sentPushWithPicture;
    private String sentSms;
    private String sentTSO;
    private String sentWhatsapp;
    @JsonProperty("TASKPROP_channel")
    private String taskpropChannel;
    @JsonProperty(value = "TASKPROP_TSO_comm_end_days", required = true)
    private String taskpropTsoCommEndDays;
    @JsonProperty(value = "TASKPROP_TSO_comm_start_days", required = true)
    private String taskpropTsoCommStartDays;
    @JsonProperty("TASKPROP_TSO_callback_req")
    private String taskpropTsoCallbackReq;
    @JsonProperty(value = "TASKPROP_TSO_call_list_type_code", required = true)
    private String taskpropTsoCallListTypeCode;
    @JsonProperty("TASKPROP_TSO_call_list_name")
    private String taskpropTsoCallListName;
    @JsonProperty("TASKPROP_TSO_daily_from_tpl")
    private String taskpropTsoDailyFromTpl;
    @JsonProperty("TASKPROP_TSO_daily_till_tpl")
    private String taskpropTsoDailyTillTpl;
    @JsonProperty("TASKPROP_TSO_priority")
    private String taskpropTsoPriority;
    @JsonProperty("TASKPROP_TSO_template_id")
    private String taskpropTsoTemplateId;
    @JsonProperty("TASKPROP_TSO_template_list")
    private String taskpropTsoTemplateList;
    @JsonProperty("TASKPROP_TSO_template_search_value")
    private String taskpropTsoTemplateSearchValue;
    @JsonProperty("TASKPROP_TSO_template_status")
    private String taskpropTsoTemplateStatus;
    private String text;

    @JsonIgnore
    private Map<String, String> outboundProperties = new HashMap<>();

    @JsonAnySetter
    public void add(String property, String value){
        outboundProperties.put(property, value);
    }
}
