package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class WtsMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty("creative_version_id")
    private String creativeVersionId;
    @JsonProperty("contributing_guid_1")
    private String contributingGuid1;
    @JsonProperty(value = "FEATURE", required = true)
    private String feature;
    @JsonProperty("MESSAGECODE")
    private String messageCode;
    private String recipient;
    private String taskCode;
    @JsonProperty(value = "TASKPROP_WTS_time_valid_duration")
    private String taskpropWtsTimeValidDuration;
}
