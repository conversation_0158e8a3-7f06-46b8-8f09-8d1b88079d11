package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class SmsMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("contributing_guid_1")
    private String contributingGuid1;
    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty("creative_version_id")
    private String creativeVersionId;
    @JsonProperty("TASKPROP_cell_package_list")
    private String taskpropCellPackageList;
    @JsonProperty("TASKPROP_cell_package_val")
    private String taskpropCellPackageVal;
    @JsonProperty(value = "TASKPROP_SMS_time_valid_duration", required = true)
    private String taskpropSmsTimeValidDuration;
    @JsonProperty(value = "TASKPROP_SMS_time_valid_start", required = true)
    private String taskpropSmsTimeValidStart;
    private String xxx;
}
