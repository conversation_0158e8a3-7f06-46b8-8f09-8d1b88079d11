package com.homecredit.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class GenesysMessage extends Message {

    private List<Record> records;

//    @JsonProperty("contact_id")
//    private String contactId;
//
//    @JsonProperty("identity_id")
//    private String identityId;
//
//    private String cuid;
//
//    @JsonProperty("call_type")
//    private Object callType;
//
//    @JsonProperty("communication_start")
//    private String communicationStart;
//
//    @JsonProperty("communication_end")
//    private String communicationEnd;
//
//    @JsonProperty("campaign_start")
//    private String campaignStart;
//
//    @JsonProperty("campaign_end")
//    private String campaignEnd;
//
//    @JsonProperty("il_communication_id")
//    private Long ilCommunicationId;
//
//    @JsonProperty("date_effective")
//    private String dateEffective;
//
//    @JsonProperty("call_source")
//    private String callSource;

    @JsonIgnore
    private Map<String, Object> otherFields;

    @JsonAnyGetter
    public Map<String, Object> getOtherFields() {
        return new HashMap<>(otherFields);
    }

    @Data
    public static class Record {
//        @JsonProperty("resptracking_id")
//        private Long resptrackingId;
//
//        @JsonProperty("record_type")
//        private Integer recordType;
//
//        @JsonProperty("switch_id")
//        private Integer switchId;

        @JsonIgnore
        private Map<String, Object> otherFields;

        @JsonAnyGetter
        public Map<String, Object> getOtherFields() {
            return new HashMap<>(otherFields);
        }
    }
}
