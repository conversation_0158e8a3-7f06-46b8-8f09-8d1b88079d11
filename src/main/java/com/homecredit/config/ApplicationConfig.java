package com.homecredit.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "hci")
@Data
public class ApplicationConfig {
    Query query;
    CI360 ci360;

    @Data
    public static class CI360 {
        String gateway;
        String tenantId;
        String clientSecret;
        String prefix;
        Proxy proxy;
        Integer retries;
        Long restartTimeout;

        @Data
        public static class Proxy {
            String host;
            Integer port;
        }
    }

    @Data
    public static class Query {
        private String storeData;
        private String storeParams;
    }
}
