package com.homecredit;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ConfigurationPropertiesScan(basePackages = "com.homecredit.config")
@EnableScheduling
public class MultiSenderAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(MultiSenderAgentApplication.class, args);
    }
}
