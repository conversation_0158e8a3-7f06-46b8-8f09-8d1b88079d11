package com.homcredit.kafkatransform.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homcredit.kafkatransform.model.SalesQuoteESP;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "kafka.lor-sales-quote.enabled", havingValue = "true")
public class SalesQuoteProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final ObjectMapper mapper;

    @Value("${kafka.lor-sales-quote.output-topic}")
    private String outputTopic;

    public void produce(SalesQuoteESP data) {
        try {
            String json = mapper.writeValueAsString(data);
            log.info("Sending data: {}", json);
            kafkaTemplate.send(outputTopic, data.getCode(), json);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
