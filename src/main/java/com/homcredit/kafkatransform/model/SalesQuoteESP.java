package com.homcredit.kafkatransform.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
public class SalesQuoteESP {

    private String code;
    private BigDecimal cashPayment;
    private BigDecimal financedAmount;
    private BigDecimal netCashPayment;
    private BigDecimal transactionAmount;
    private BigDecimal demandedAmount;
    private BigDecimal rate;
    private String productCode;
    private String tenor;
    private Instant creationDate;
    private Instant updateDate;
}
