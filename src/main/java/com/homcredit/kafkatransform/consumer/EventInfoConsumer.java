package com.homcredit.kafkatransform.consumer;

import com.homcredit.kafkatransform.mapper.EventInfoMapper;
import com.homcredit.kafkatransform.model.EventInfo;
import com.homcredit.kafkatransform.producer.EventInfoProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "kafka.am-event-info.enabled", havingValue = "true")
public class EventInfoConsumer {

    private final EventInfoMapper eventInfoMapper;
    private final EventInfoProducer eventInfoProducer;

    @KafkaListener(topics = "#{'${kafka.am-event-info.input-topic}'}")
    public void consume(ConsumerRecord<byte[], net.homecredit.cabus.am.event.eventinfo.v1.EventInfo> message) {
        net.homecredit.cabus.am.event.eventinfo.v1.EventInfo record = message.value();
        log.info("Received message data: {}", record);
        EventInfo eventInfoESP = eventInfoMapper.map(record);
        eventInfoProducer.produce(eventInfoESP);
    }
}
