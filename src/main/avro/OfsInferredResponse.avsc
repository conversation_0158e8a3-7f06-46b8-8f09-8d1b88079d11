{"namespace": "net.homecredit.hss.integration.offerstore.customerofferresponseeventmessage.stubs", "name": "CustomerOfferResponseEventMessage", "type": "record", "fields": [{"name": "eventType", "type": "string", "doc": "Category of the event (Behavioral, Business)"}, {"name": "cuid", "type": "string", "doc": "Unique identifier of the client - Customer ID"}, {"name": "eventSource", "type": "string", "doc": "Channel description (e. g. GEN, LOR, CLM ...)"}, {"name": "eventDate", "type": "string", "doc": "Date + time of event origination"}, {"name": "eventCode", "type": "string", "doc": "Code of event provided by the channel (e. g. In Call)"}, {"name": "eventDescription", "type": ["null", "string"], "default": null, "doc": "Description of event provided by the channel (e. g. Application approved)"}, {"name": "eventResult", "type": ["null", "string"], "default": null, "doc": "Result/Outcome of the event if available (e. g. request for call back)"}, {"name": "referenceSourceId", "type": ["null", "string"], "default": null, "doc": "Link to source business entity PK (like Application ID, Call Id …)"}, {"name": "offerId", "type": ["null", "string"], "default": null, "doc": "Link to Offer ID where available (e. g. in relation to Application statuses)"}, {"name": "userId", "type": ["null", "string"], "default": null, "doc": "Identification of user having relation to the event"}, {"name": "creationTimestamp", "type": "string", "doc": "Timestamp of record creation on OFS DB"}]}