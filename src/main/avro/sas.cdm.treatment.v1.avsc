{"type": "record", "name": "Treatments", "namespace": "net.homecredit.sas.event.cdm.treatment.v1", "doc": "Schema for SAS Treatments", "fields": [{"name": "eventTimestamp", "type": "long", "doc": "Timestamp of the streaming the event into kafka", "logicalType": "timestamp-micros"}, {"name": "eventName", "type": "string", "doc": "event name"}, {"name": "eventId", "type": "long", "doc": "Internal ID of the event"}, {"name": "treatmentSk", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "TREATMENT_SK is the primary surrogate key for the CI_TREATMENT table. A unique sequential integer value is generated when a new row (treatment) is added."}, {"name": "treatmentCd", "type": ["null", "string"], "doc": "TREATMENT_CD is the business key code for the treatment."}, {"name": "treatmentVersionNo", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "TREATMENT_VERSION_NO is the number that is incremented each time a new version of a treatment is created."}, {"name": "treatmentNm", "type": ["null", "string"], "doc": "TREATMENT_NM is the name of the treatment."}, {"name": "treatmentDesc", "type": ["null", "string"], "doc": "TREATMENT_DESC is the description of the treatment."}, {"name": "treatmentReferenceTxt", "type": ["null", "string"], "doc": "TREATMENT_REFERENCE_TXT is the displayed text of the link to an external file such as an image, a spreadsheet, or a Microsoft Word document. For example, the text might be Ski Resort Logo for Winter Coupon."}, {"name": "treatmentReferenceUrl", "type": ["null", "string"], "doc": "TREATMENT_REFERENCE_URL is the URL of the link to an external file such as an image, spreadsheet, or a Microsoft Word document. For example, the URL might be http://www.ArtDept/winter/SkiLogo5.PNG."}, {"name": "currentVersionFlg", "type": ["null", "string"], "doc": "CURRENT_VERSION_FLG is a flag that indicates whether this record is the current version of the treatment. Valid values are Y and N."}, {"name": "startDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "START_DTTM contains the starting date which a treatment is valid for use in a campaign or presentation to the channel.", "logicalType": "timestamp-micros"}, {"name": "endDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "END_DTTM contains the ending date which a treatment is valid for use in a campaign or presentation to the channel.", "logicalType": "timestamp-micros"}, {"name": "deletedFlg", "type": ["null", "string"], "doc": "DELETED_FLG is a flag that indicates whether the treatment has been deleted. Valid values are Y (the treatment is deleted) and N (the treatment is not deleted)."}, {"name": "processedDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "PROCESSED_DTTM is the last date and time that a record was processed.", "logicalType": "timestamp-micros"}, {"name": "acquisitionChannel", "type": ["null", "string"], "doc": "ACQUISITION_CHANNEL is business defined value - acq.channels for offer creation"}, {"name": "behavioralGoalComm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "BEHAVIORAL_GOAL_COMM - Behavioral goal"}, {"name": "behavioralGoalCommDays", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "BEHAVIORAL_GOAL_COMM_DAYS - Number of days for evaluation of Behavioral goal"}, {"name": "behavioralGoalCommFlag", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "BEHAVIORAL_GOAL_COMM_FLAG - Flag for evaluation of Behavioral goal"}, {"name": "businessGoal", "type": ["null", "string"], "doc": "BUSINESS_GOAL - Business goal"}, {"name": "businessGoalDays", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "BUSINESS_GOAL_DAYS - Number of days for evaluation of Business goal"}, {"name": "businessGoalFlag", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "BUSINESS_GOAL_FLAG - Flag for evaluation of Business goal"}, {"name": "channelNm", "type": ["null", "string"], "doc": "CHANNEL_NM - Treatment channel code"}, {"name": "communicationDaysStart", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "COMMUNICATION_DAYS_START - Number of days till communication start"}, {"name": "communicationDaysEnd", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "COMMUNICATION_DAYS_END - Number of days till communication end"}, {"name": "communicationEnd", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "COMMUNICATION_END - Communication end date", "logicalType": "timestamp-micros"}, {"name": "communicationStart", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "COMMUNICATION_START - Communication start date", "logicalType": "timestamp-micros"}, {"name": "deactivationChannel", "type": ["null", "string"], "doc": "DEACTIVATION_CHANNEL"}, {"name": "deactivationReasonId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "DEACTIVATION_REASON_ID"}, {"name": "evaluationDaysTr", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "EVALUATION_DAYS_TR"}, {"name": "nameCallList", "type": ["null", "string"], "doc": "NAME_CALL_LIST"}, {"name": "offerValidTo", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "OFFER_VALID_TO", "logicalType": "timestamp-micros"}, {"name": "pilotFlg", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "PILOT_FLG - Pilot flag"}, {"name": "pricingCategory", "type": ["null", "string"], "doc": "PRICING_CATEGORY"}, {"name": "pricingPilot", "type": ["null", "string"], "doc": "PRICING_PILOT"}, {"name": "priority", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "PRIORITY"}, {"name": "processPilot", "type": ["null", "string"], "doc": "PROCESS_PILOT"}, {"name": "productCodes", "type": ["null", "string"], "doc": "PRODUCT_CODES"}, {"name": "productType", "type": ["null", "string"], "doc": "PRODUCT_TYPE"}, {"name": "tplId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "TPL_ID"}, {"name": "trackResponseDays", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "TRACK_RESPONSE_DAYS"}, {"name": "treatmentPilotDesc", "type": ["null", "string"], "doc": "TREATMENT_PILOT_DESC - Pilot description"}, {"name": "offerTypeCode", "type": ["null", "string"], "doc": "OFFER_TYPE_CODE"}, {"name": "dateOrNum", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "DATE_OR_NUM - Date or number of days - end"}, {"name": "dateOrNumStart", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "DATE_OR_NUM_START - Date or number of days - start"}, {"name": "productSetCode", "type": ["null", "string"], "doc": "PRODUCT_SET_CODE"}]}