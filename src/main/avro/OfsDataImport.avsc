{"namespace": "net.homecredit.hss.integration.offerstore.ofsdataimport.stubs", "name": "OfsDataImport", "type": "record", "fields": [{"name": "importType", "type": "string", "doc": "type of import"}, {"name": "correlationId", "type": ["null", "string"], "default": null, "doc": "Optional correlation id. If provided, reply could be generated notifying about status of import."}, {"name": "importedData", "type": [{"namespace": "net.homecredit.hss.integration.offerstore.ofsdataimport.stubs", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "record", "fields": [{"name": "relationId", "type": ["null", "string"], "default": null, "doc": "import offer id"}, {"name": "partyId", "type": "string", "doc": "Client unique identifier"}, {"name": "limitId", "type": "string", "doc": "Unique identifier of the limit"}, {"name": "offerTypeCode", "type": "string", "doc": "Type of offer"}, {"name": "offerSubTypeCode", "type": "string", "default": "UNUSED"}, {"name": "maxCreditAmount", "type": ["null", "long"], "default": null, "doc": "Maximal Credit Amount"}, {"name": "maxMonthlyInst", "type": ["null", "long"], "default": null, "doc": "Maximal Monthly Installment Amount"}, {"name": "minCashPayment", "type": ["null", "long"], "default": null, "doc": "Minimal Cash Downpayment"}, {"name": "minCashPaymentRel", "type": ["null", "string"], "default": null, "doc": "Minimal Cash Payment in %"}, {"name": "offerValidTo", "type": "string", "doc": "End date of limit validity in ISO8601 format"}, {"name": "pricingCategory", "type": ["null", "int"], "default": null, "doc": "Pricing category"}, {"name": "offerIdSas", "type": ["null", "int"], "default": null, "doc": "Original Offer ID provided by SAS"}, {"name": "crmPilotCode", "type": ["null", "string"], "default": null, "doc": "The definition of Pilot code"}, {"name": "sourceProcessId", "type": "string", "doc": "The description of source provided data about <PERSON>er"}, {"name": "acqChannelCodes", "type": ["null", {"type": "array", "name": "acqChannelCodeArray", "items": {"type": "string"}}], "default": null, "doc": "Identification of enabled acquisition channel"}, {"name": "productCodes", "type": {"type": "array", "name": "productCodeArray", "items": {"type": "string"}}, "doc": "product codes"}, {"name": "productSetCode", "type": ["null", "string"], "default": null}, {"name": "accountNumber", "type": ["null", "string"], "default": null}, {"name": "minMonthlyInst", "type": ["null", "long"], "default": null}, {"name": "pricingStrategy", "type": ["null", "string"], "default": null}, {"name": "minCreditAmount", "type": ["null", "long"], "default": null, "doc": "Minimum Credit Amount"}, {"name": "maxCashPaymentRel", "type": ["null", "string"], "default": null, "doc": "Maximal Cash Payment in %"}, {"name": "minTenor", "type": ["null", "int"], "default": null, "doc": "Minimum tenor"}, {"name": "maxTenor", "type": ["null", "int"], "default": null, "doc": "Maximal term for given offer type/subtype combination"}, {"name": "limitCategory", "type": ["null", "string"], "default": null, "doc": "Limit category - guaranteed or null"}, {"name": "commodityCategoryGroup", "type": ["null", "string"], "default": null, "doc": "Commodity category group - mobile or non-mobile"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataimport.stubs", "name": "DeactivateOffer", "type": "record", "fields": [{"name": "offerId", "type": "string", "doc": "Offer unique identifier"}, {"name": "deactivationReason", "type": "string", "doc": "Deactivation reason"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataimport.stubs", "name": "CreateLimit", "type": "record", "fields": [{"name": "relationId", "type": ["null", "string"], "default": null, "doc": "import limit id"}, {"name": "partyId", "type": "string", "doc": "Client unique identifier"}, {"name": "limitBulkId", "type": ["null", "string"], "default": null, "doc": "Universally unique identifier of set of limits, calculated for one partyId"}, {"name": "limitScoringProcessSource", "type": "string", "doc": "Source of scoring process"}, {"name": "flagEligibility", "type": "boolean", "doc": "whether the client is eligible"}, {"name": "calculationSource", "type": ["string", "null"], "doc": "Identification of system initiating limit recalculation for client"}, {"name": "limitValidFrom", "type": "string", "doc": "Start date of limit validity in ISO8601 format"}, {"name": "limitValidTo", "type": "string", "doc": "End date of limit validity in ISO8601 format"}, {"name": "limitScore", "type": ["null", "string"], "default": null, "doc": "Risk score result"}, {"name": "amtCreditMax", "type": "long", "doc": "Maximal Credit Amount"}, {"name": "amtInstalmentMax", "type": "long", "doc": "Maximal Monthly Instalment"}, {"name": "amtDownPaymentMin", "type": ["null", "long"], "default": null, "doc": "Minimal Cash Payment"}, {"name": "amtDownPaymentMinRel", "type": ["null", "string"], "default": null, "doc": "Minimal Cash Payment in %"}, {"name": "codeRiskGrade", "type": "string", "doc": "Risk grade"}, {"name": "limitAccuracy", "type": ["null", "int"], "default": null, "doc": "Accuracy of limit calculation"}, {"name": "limitPilotCode", "type": "string", "doc": "Risk pilot identification"}, {"name": "limitTypeCode", "type": "string", "doc": "Type of the limit"}, {"name": "limitSubTypeCode", "type": "string", "doc": "SubType of the limit"}, {"name": "accountNumber", "type": ["null", "string"], "default": null, "doc": "Account number related to this limit (and especially clip/dclip functionality)"}, {"name": "acqChannelCodes", "type": ["null", {"type": "array", "name": "acqChannelCodeArray", "items": {"type": "string"}}], "default": null, "doc": "Identification of enabled acquisition channel"}, {"name": "maxTenor", "type": ["null", "int"], "default": null, "doc": "Maximal term for given limit type/subtype combination"}, {"name": "amtCreditMin", "type": ["null", "long"], "default": null, "doc": "Minimum Credit Amount"}, {"name": "amtDownPaymentMaxRel", "type": ["null", "string"], "default": null, "doc": "Maximal Cash Payment in %"}, {"name": "minTenor", "type": ["null", "int"], "default": null, "doc": "Minimum term for given limit type/subtype combination"}, {"name": "limitCategory", "type": ["null", "string"], "default": null, "doc": "Limit category - guaranteed or null"}, {"name": "commodityCategoryGroup", "type": ["null", "string"], "default": null, "doc": "Commodity category group - mobile or non-mobile"}]}], "doc": "imported data of given type"}]}