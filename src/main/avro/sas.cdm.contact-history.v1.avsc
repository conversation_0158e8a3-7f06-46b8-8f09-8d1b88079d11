{"type": "record", "name": "ContactHistory", "namespace": "net.homecredit.sas.event.cdm.contact_history.v1", "doc": "Schema for All SAS Contact History tables - client,client for offer, limit", "fields": [{"name": "eventTimestamp", "type": "long", "doc": "Timestamp of the streaming the event into kafka", "logicalType": "timestamp-micros"}, {"name": "eventName", "type": "string", "doc": "event name - name of the source table"}, {"name": "eventId", "type": "long", "doc": "Internal ID of the event"}, {"name": "cellPackageSk", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CELL_PACKAGE_SK is the primary reference key of a unique identifier such as Subject ID (for example, Customer, Account, or Household) that associates the CI_CONTACT_HISTORY table with an external table."}, {"name": "contactDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CONTACT_DTTM is the date and time when the subject (such as client, client for offer, limit) is contacted.", "logicalType": "timestamp-micros"}, {"name": "contactDt", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CONTACT_DT is the date (only) when the subject (such as  client, client for offer, limit) is contacted. This column is useful for partitioning and indexing for better performance.", "logicalType": "timestamp-micros"}, {"name": "contactHistoryStatusCd", "type": ["null", "string"], "doc": "CONTACT_HISTORY_STATUS_CD is the reference key code that associates the contact history with a contact history status. See the CI_CONTACT_HISTORY_STATUS table for the supplied values of the status codes."}, {"name": "packageHashVal", "type": ["null", "string"], "doc": "PACKAGE_HASH_VAL is a hash value that is generated to supply a single lookup value for a combination of the dynamic custom details for a package."}, {"name": "optimizationBackfillFlg", "type": ["null", "string"], "doc": "OPTIMIZATION_BACKFILL_FLG is used when SAS Marketing Automation is integrated with SAS Marketing Optimization when there are control groups. SAS Marketing Optimization optimizes and then removes the requested number of people for a control group. Then SAS Marketing Optimization does a second pass and backfills the optimized cells with more customers. The customers added in the second pass have this flag set."}, {"name": "externalContactInfoId1", "type": ["null", "string"], "doc": "EXTERNAL_CONTACT_INFO_ID1 contains channel-specific data that is typically the reference to external channel information such as a transaction number."}, {"name": "externalContactInfoId2", "type": ["null", "string"], "doc": "EXTERNAL_CONTACT_INFO_ID2 contains channel-specific data that is typically the reference to external channel information such as a transaction number."}, {"name": "responseTrackingCd", "type": ["null", "string"], "doc": "RESPONSE_TRACKING_CD or RESPTRACKING_CD is a unique value generated by SAS Marketing Automation that is required to be returned with response data for appropriate attribution and tracking to a particular campaign cell package."}, {"name": "idCuid", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "ID_CUID is client identification id from PIF"}, {"name": "identifierHashVal", "type": ["null", "string"], "doc": "IDENTIFIER_HASH_VAL is a hash value that is generated to supply a single lookup value for the identifiers used in a campaign."}, {"name": "offerId", "type": ["null", "string"], "doc": "OFFER_ID is id of the offer from Offer Store"}, {"name": "genericResponse", "type": ["null", "string"], "doc": "GENERIC_RESPONSE represent is customer was contacted or not, filled based on reponses defined in CDM.DICT_RESP_MAPPING"}, {"name": "businessGoalFlg", "type": ["null", "int"], "doc": "BUSINESS_GOAL_FLG"}, {"name": "behavioralGoalFlg", "type": ["null", "int"], "doc": "BEHAVIORAL_GOAL_FLG"}, {"name": "responseDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "RESPONSE_DTTM is timestamp of response from channel", "logicalType": "timestamp-micros"}, {"name": "deliveredFlg", "type": ["null", "string"], "doc": "DELIVERED_FLG define if message was delivered to customer based on response, filled based on reponses defined in CDM.DICT_RESP_MAPPING"}, {"name": "contactedFlg", "type": ["null", "string"], "doc": "CONTACTED_FLG define if cliend was contacted, filled based on reponses defined in CDM.DICT_RESP_MAPPING"}, {"name": "deliveredDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "DELIVERED_DTTM is date based on response date and delivered_flg", "logicalType": "timestamp-micros"}, {"name": "contactedDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CONTACTED_DTTM is date based on response date and contacted_flg", "logicalType": "timestamp-micros"}, {"name": "responseNm", "type": ["null", "string"], "doc": "RESPONSE_NM is name of the response, filled based on reponses defined in CDM.DICT_RESP_MAPPING"}, {"name": "chId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CH_ID is Contact History ID, surrogate key between CONTACT_HISTORY table and CONTACT_HISTORY_EXT table"}, {"name": "deactivationReasonId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "DEACTIVATION_REASON_ID"}, {"name": "updateDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "UPDATE_DTTM", "logicalType": "timestamp-micros"}, {"name": "ilCommunicationId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "IL_COMMUNICATION_ID is unique key from Integration layer in SAS DB - unique key og the message uniqueness of this key is equal to primary key in CONTACT_HISTORY table"}, {"name": "createdDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CREATED_DTTM", "logicalType": "timestamp-micros"}, {"name": "treatmentSk", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "TREATMENT_SK is the primary surrogate key for the CI_TREATMENT table. A unique sequential integer value is generated when a new row (treatment) is added."}, {"name": "ci360ResponseTrackingCd", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CI360_RESPONSE_TRACKING_CD is reponse tracking code defined by cloud CI360"}, {"name": "ci360TaskId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CI360_TASK_ID is taks id defined by cloud CI360"}, {"name": "ci360taksIdVersion", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CI360_TASK_ID_VERSION is version of the task id from 360"}, {"name": "ci360CreativeId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CI360_CREATIVE_ID is id of the creative for message defined by cloud CI360"}, {"name": "ci360ContactId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CI360_CONTACT_ID is id of the contact defined by cloud CI360"}, {"name": "ci360IdentityId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CI360_IDENTITY_ID is identitiy of the cliend defined by cloud CI360"}, {"name": "limitId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "LIMIT_ID is limit id from Offer Store"}, {"name": "contactHistoryStatusDesc", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CONTACT_HISTORY_STATUS_DESC contains the description of the reference code for a contact history status. Some of the supplied values are Exported, Failed, and Not Ready."}, {"name": "responseText", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "RESPONSE_TEXT"}, {"name": "responseTimestamp", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "RESPONSE_TIMESTAMP"}, {"name": "crmSegment", "type": ["null", "string"], "doc": "CRM_SEGMENT"}, {"name": "mflcCrm", "type": ["null", "string"], "doc": "MFLC_CRM"}, {"name": "codeRiskGrade", "type": ["null", "string"], "doc": "CODE_RISK_GRADE"}, {"name": "customInfo1", "type": ["null", "string"], "doc": "CUSTOM_INFO1 contains values from customization"}, {"name": "customInfo2", "type": ["null", "string"], "doc": "CUSTOM_INFO2 contains values from customization"}, {"name": "customInfo3", "type": ["null", "string"], "doc": "CUSTOM_INFO3 contains values from customization"}, {"name": "customInfo4", "type": ["null", "string"], "doc": "CUSTOM_INFO4 contains values from customization"}, {"name": "customInfo5", "type": ["null", "string"], "doc": "CUSTOM_INFO5 contains values from customization"}]}