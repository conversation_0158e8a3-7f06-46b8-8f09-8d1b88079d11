{"namespace": "net.homecredit.sas.segmenttortdm.v1", "type": "record", "name": "SegmentToRTDM", "doc": "Schema for SAS Messages - segment data from Database to Kafka", "fields": [{"name": "ID", "type": "long", "doc": "Private Key of the Schema Table. Used by RTDM to handle update segment status"}, {"name": "subject_key_1", "type": "string", "doc": "Identifier of subject (e.g. ID_CUID for Client, LIMIT_ID for Limtis). Mandatory"}, {"name": "subject_key_2", "type": ["null", "string"], "default": null, "doc": "Secondary Identifier of few subject. Not Mandatory"}, {"name": "subject_key_3", "type": ["null", "string"], "default": null, "doc": "Secondary Identifier of few subject. Not Mandatory"}, {"name": "subject", "type": "string", "doc": "Subject Name. Used by ESP to determine the body structure of the request."}, {"name": "event_name", "type": "string", "doc": "Event Name. Used by ESP to determine the url structure of the request."}]}