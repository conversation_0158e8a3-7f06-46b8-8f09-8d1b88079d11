{"type": "record", "name": "InputLoad", "namespace": "net.homecredit.sas.event.int_comm.input_load.v1", "doc": "Schema for SAS Responses - integration layer input loads", "fields": [{"name": "eventTimestamp", "type": "long", "doc": "Timestamp of the streaming the event into kafka", "logicalType": "timestamp-micros"}, {"name": "eventName", "type": "string", "doc": "event name"}, {"name": "eventId", "type": "long", "doc": "Internal ID of the event"}, {"name": "ilCommunicationId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "IL_COMMUNICATION_ID"}, {"name": "idCuid", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "ID_CUID"}, {"name": "chId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CH_ID"}, {"name": "responseInputLoadId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "RESPONSE_INPUT_LOAD_ID"}, {"name": "channelCd", "type": ["null", "string"], "doc": "CHANNEL_CD"}, {"name": "responseTrackingCd", "type": ["null", "string"], "doc": "RESPONSE_TRACKING_CD"}, {"name": "createdDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "CREATED_DTTM", "logicalType": "timestamp-micros"}, {"name": "logActionId", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "LOG_ACTION_ID"}, {"name": "updateDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "UPDATE_DTTM", "logicalType": "timestamp-micros"}, {"name": "responseNm", "type": ["null", "string"], "doc": "RESPONSE_NM"}, {"name": "responseDttm", "type": ["null", "string"], "doc": "RESPONSE_DTTM", "logicalType": "timestamp-micros"}, {"name": "recipient", "type": ["null", "string"], "doc": "RECIPIENT"}, {"name": "responseComment", "type": ["null", "string"], "doc": "RESPONSE_COMMENT"}, {"name": "phoneNumber", "type": ["null", "string"], "doc": "PHONE_NUMBER"}, {"name": "callId", "type": ["null", "string"], "doc": "CALL_ID"}, {"name": "userId", "type": ["null", "string"], "doc": "USER_ID"}, {"name": "dateEffective", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "DATE_EFFECTIVE", "logicalType": "timestamp-micros"}, {"name": "processedFlg", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "PROCESSED_FLG"}, {"name": "processedDttm", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "PROCESSED_DTTM", "logicalType": "timestamp-micros"}, {"name": "attempts", "type": ["null", {"type": "long", "java-class": "java.lang.Long"}], "doc": "ATTEMPTS"}, {"name": "responseHistId", "type": ["null", "string"], "doc": "RESPONSE_HIST_ID"}, {"name": "offerId", "type": ["null", "string"], "doc": "OFFER_ID"}, {"name": "responseText", "type": ["null", "string"], "doc": "RESPONSE_TEXT"}]}