{"type": "record", "name": "BatchOfferDeactivationRequestCreated", "namespace": "net.homecredit.ofs.event.batchofferdeactivation.v1", "fields": [{"name": "correlationId", "type": ["null", "string"], "doc": "Optional correlation id. If provided, reply could be generated notifying about status of import.", "default": null}, {"name": "offerId", "type": "string", "doc": "Offer unique identifier"}, {"name": "deactivationReason", "type": "string", "doc": "Deactivation reason"}]}