{"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "OfsDataExport", "type": "record", "fields": [{"name": "exportType", "type": "string", "doc": "type of export"}, {"name": "exportedData", "type": [{"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "LimitExport", "type": "record", "fields": [{"name": "partyId", "type": "string", "doc": "Client unique identifier"}, {"name": "limitBulkId", "type": "string", "doc": "Universally unique identifier of set of limits, calculated for one partyId"}, {"name": "limitId", "type": "string", "doc": "Unique identifier of the limit"}, {"name": "limitScoringProcessSource", "type": "string", "doc": "Source of scoring process"}, {"name": "calculationSource", "type": ["string", "null"], "doc": "Identification of system initiating limit recalculation for client"}, {"name": "limitValidFrom", "type": "string", "doc": "Start date of limit validity in ISO8601 format"}, {"name": "limitValidTo", "type": "string", "doc": "End date of limit validity in ISO8601 format"}, {"name": "limitScore", "type": ["null", "string"], "default": null, "doc": "Risk score result"}, {"name": "limitStatus", "type": {"type": "string"}, "doc": "Status of limit"}, {"name": "amtCreditMax", "type": "long", "doc": "Maximal Credit Amount"}, {"name": "amtInstalmentMax", "type": "long", "doc": "Maximal Monthly Instalment"}, {"name": "amtDownPaymentMin", "type": ["null", "long"], "default": null, "doc": "Minimal Cash Payment"}, {"name": "amtDownPaymentMinRel", "type": ["null", "string"], "default": null, "doc": "Minimal Cash Payment in %"}, {"name": "codeRiskGrade", "type": "string", "doc": "Risk grade"}, {"name": "limitAccuracy", "type": ["null", "int"], "default": null, "doc": "Accuracy of limit calculation"}, {"name": "limitPilotCode", "type": "string", "doc": "Risk pilot identification"}, {"name": "limitTypeCode", "type": "string", "doc": "Type of the limit"}, {"name": "limitSubTypeCode", "type": "string", "doc": "SubType of the limit"}, {"name": "deactivationTimestamp", "type": ["null", "string"], "default": null, "doc": "Timestamp when offer was deactivated"}, {"name": "deactivationReason", "type": ["null", "string"], "default": null, "doc": "Reason why was offer deactivated"}, {"name": "creationTimestamp", "type": "string", "doc": "Timestamp when offer was created"}, {"name": "modificationTimestamp", "type": "string", "doc": "Timestamp when offer was last time modified"}, {"name": "acqChannelCodes", "type": {"type": "array", "name": "acqChannelCodeArray", "items": {"type": "string"}}, "doc": "Identification of enabled acquisition channel"}, {"name": "relationCode", "type": ["null", "string"], "default": null, "doc": "Code of event relationship (fixed value identifying given process)"}, {"name": "relationId", "type": ["null", "string"], "default": null, "doc": "ID of associated event"}, {"name": "accountNumber", "type": ["null", "string"], "default": null, "doc": "Account number related to this limit (and especially clip/dclip functionality)"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "OfferExport", "type": "record", "fields": [{"name": "partyId", "type": "string", "doc": "Client unique identifier"}, {"name": "limitId", "type": "string", "doc": "ID of limit"}, {"name": "offerTypeCode", "type": "string", "doc": "Type of offer"}, {"name": "offerStatus", "type": "string", "doc": "status of offer"}, {"name": "offerId", "type": "string", "doc": "ID of offer"}, {"name": "maxCreditAmount", "type": ["null", "long"], "default": null, "doc": "Maximal Credit Amount"}, {"name": "maxMonthlyInst", "type": ["null", "long"], "default": null, "doc": "Maximal Monthly Instalment"}, {"name": "minCashPayment", "type": ["null", "long"], "default": null, "doc": "Minimal Cash Payment"}, {"name": "minCashPaymentRel", "type": ["null", "string"], "default": null, "doc": "Minimal Cash Payment in %"}, {"name": "offerValidTo", "type": "string", "doc": "End date of offer validity in ISO8601 format"}, {"name": "pricingCategory", "type": ["null", "int"], "default": null, "doc": "Pricing category"}, {"name": "offerIdSas", "type": ["null", "int"], "default": null, "doc": "Original Offer ID provided by SAS"}, {"name": "crmPilotCode", "type": ["null", "string"], "default": null, "doc": "The definition of pilot code"}, {"name": "sourceProcessId", "type": "string", "doc": "The description of source provided data about <PERSON>er"}, {"name": "creationTimestamp", "type": "string", "doc": "Timestamp when offer was created"}, {"name": "modificationTimestamp", "type": "string", "doc": "Timestamp when offer was last time modified"}, {"name": "deactivationTimestamp", "type": ["null", "string"], "default": null, "doc": "Timestamp when offer was deactivated"}, {"name": "deactivationReason", "type": ["null", "string"], "default": null, "doc": "Reason why was offer deactivated"}, {"name": "acqChannelCodes", "type": {"type": "array", "name": "acqChannelCodeArray", "items": {"type": "string"}}, "doc": "Identification of enabled acquisition channel"}, {"name": "productCodes", "type": {"type": "array", "name": "productCodeArray", "items": {"type": "string"}}, "doc": "product codes"}, {"name": "offerTypeName", "type": "string", "doc": "Name of offer type"}, {"name": "productType", "type": ["null", "string"], "default": null, "doc": "type of product"}, {"name": "initTransactionType", "type": ["null", "string"], "default": null, "doc": "type of initial transaction"}, {"name": "priority", "type": ["null", "int"], "default": null, "doc": "offer priority"}, {"name": "entryPoint", "type": ["null", "string"], "default": null, "doc": "entry point"}, {"name": "jointLendingPartnerCode", "type": ["null", "string"], "default": null, "doc": "joint lending partner code"}, {"name": "relationCode", "type": ["null", "string"], "default": null, "doc": "Code of event relationship (fixed value identifying given process)"}, {"name": "relationId", "type": ["null", "string"], "default": null, "doc": "ID of associated event"}, {"name": "productSetCode", "type": ["null", "string"], "default": null}, {"name": "accountNumber", "type": ["null", "string"], "default": null}, {"name": "minMonthlyInst", "type": ["null", "long"], "default": null}, {"name": "pricingStrategy", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "LimitTypeExport", "type": "record", "fields": [{"name": "strategyName", "type": ["null", "string"], "default": null}, {"name": "jointLendingPartnerCode", "type": ["null", "string"], "default": null}, {"name": "limitTypeCode", "type": "string"}, {"name": "limitSubTypeCode", "type": "string"}, {"name": "flagIsScoring", "type": ["null", "boolean"], "default": null}, {"name": "flagIsSasDclip", "type": ["boolean", "null"], "default": false}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "LimitTypeDeletion", "type": "record", "fields": [{"name": "limitTypeCode", "type": "string"}, {"name": "limitSubTypeCode", "type": "string"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "AcquisitionChannelExport", "type": "record", "fields": [{"name": "acqChannelCode", "type": "string"}, {"name": "acqChannelName", "type": "string"}, {"name": "acqChannelGroup", "type": "string"}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "ResponseType", "type": "record", "fields": [{"name": "responseId", "type": "string"}, {"name": "acquisitionChannelCode", "type": "string"}, {"name": "offerTypeCode", "type": ["null", "string"], "default": null, "doc": "Can be null due to schema evolution, when previous version of schema was used to create a message"}, {"name": "responseText", "type": ["null", "string"], "default": null}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "OfferTypeExport", "type": "record", "fields": [{"name": "offerTypeCode", "type": "string"}, {"name": "offerCategory", "type": ["string", "null"], "default": "UNDEFINED", "doc": "Offer category. Enum, currently allowed values: UNDEFINED, CLX"}, {"name": "offerTypeName", "type": "string"}, {"name": "productType", "type": ["null", "string"], "default": null}, {"name": "flagMaxCreditAmount", "type": "string"}, {"name": "flagMaxMonthlyInst", "type": "string"}, {"name": "flagMinCashPayment", "type": "string"}, {"name": "flagMinCashPaymentRel", "type": ["null", "string"], "default": null}, {"name": "flagProductCode", "type": "boolean"}, {"name": "flagPricingCategory", "type": "boolean"}, {"name": "flagCrmPilotCode", "type": "boolean"}, {"name": "flagAcqChannelCode", "type": "boolean", "doc": "DEPRECATED"}, {"name": "flagAcqChannelCodeEnum", "type": ["null", "string"], "default": null, "doc": "Can be null due to schema evolution, when previous version of schema with flagAcqChannelCode was used to create a message"}, {"name": "flagContractCodeRefin", "type": "boolean"}, {"name": "flagProductSet", "type": "boolean"}, {"name": "flagLimitBased", "type": ["boolean", "null"], "default": true}, {"name": "initTransactionType", "type": ["null", "string"], "default": null}, {"name": "priority", "type": ["null", "int"], "default": null}, {"name": "entryPoint", "type": ["null", "string"], "default": null}, {"name": "responseTypes", "type": {"type": "array", "items": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs.ResponseType"}}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "OfferTemplateExport", "type": "record", "fields": [{"name": "offerTemplateDictId", "type": "string"}, {"name": "limitTypeCode", "type": "string"}, {"name": "limitSubTypeCode", "type": "string"}, {"name": "codeRiskGrade", "type": "string"}, {"name": "limitPilotCode", "type": "string"}, {"name": "offerTypeCode", "type": "string"}, {"name": "crmPilotCode", "type": "string"}, {"name": "pricingCategory", "type": ["null", "int"], "default": null}, {"name": "productCodes", "type": {"type": "array", "name": "productCodeArray", "items": {"type": "string"}}, "doc": "product codes"}, {"name": "productSetCode", "type": ["null", "string"], "default": null}, {"name": "acqChannelCodes", "type": {"type": "array", "name": "acqChannelCodeArray", "items": {"type": "string"}}, "doc": "Identification of enabled acquisition channel"}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "ResponseHistoryExport", "type": "record", "fields": [{"name": "responseHistoryId", "type": "string"}, {"name": "offerId", "type": "string"}, {"name": "acqChannelCode", "type": "string"}, {"name": "responseText", "type": ["null", "string"], "default": null}, {"name": "responseComment", "type": ["null", "string"], "default": null}, {"name": "responseTimestamp", "type": "string"}, {"name": "userId", "type": ["null", "string"], "default": null}, {"name": "sourceIdType", "type": ["null", "string"], "default": null}, {"name": "sourceIdValue", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "ProductSetExport", "type": "record", "fields": [{"name": "productSetCode", "type": "string"}, {"name": "productCodes", "type": {"type": "array", "items": {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "ProductCodeExport", "type": "record", "fields": [{"name": "productCode", "type": "string"}, {"name": "creationTimestamp", "type": "string"}]}}, "doc": "product codes"}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "EntryPointExport", "type": "record", "fields": [{"name": "entryPoint", "type": "string"}, {"name": "entryPointDescription", "type": "string"}, {"name": "creationTimestamp", "type": "string"}, {"name": "modificationTimestamp", "type": ["null", "string"], "default": null}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "CustomerExport", "type": "record", "fields": [{"name": "partyId", "type": "string"}, {"name": "maximumInstallment", "type": "string"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "ContractInstalmentExport", "type": "record", "fields": [{"name": "contractCode", "type": "string"}, {"name": "partyId", "type": "string"}, {"name": "instalmentAmount", "type": "string"}, {"name": "requestTimestamp", "type": "string"}, {"name": "sourceSystem", "type": "string"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "IdExport", "type": "record", "fields": [{"name": "id", "type": "string"}]}, {"namespace": "net.homecredit.hss.integration.offerstore.ofsdataexport.stubs", "name": "DeactivatedRecordExport", "type": "record", "fields": [{"name": "deactivationTimestamp", "type": "string", "doc": "Timestamp when records were deactivated"}, {"name": "deactivationReason", "type": "string", "doc": "Reason why was deactivation happened."}, {"name": "id", "type": "string", "doc": "id of deactivated record"}]}], "doc": "exported data of given type"}]}