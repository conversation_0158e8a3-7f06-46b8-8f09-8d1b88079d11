package com.glowbyte.rabbit;

import com.glowbyte.hc.integrationlayer.ILApplication;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.DefaultApplicationArguments;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.util.Arrays;


@ExtendWith(SpringExtension.class)
@ActiveProfiles("sms")
@SpringBootTest(classes = ILApplication.class, args = {"--task=rabbit-sender", "--CHANNEL_CODE=10"})
//rabbit-sender
public class RabbitSenderTests {


    @Autowired
    private ApplicationContext ctx;

    @Autowired
    private RabbitTemplate template;


    // @Autowired
    // private RabbitAdmin admin;

    // @Autowired
    // private RabbitListenerEndpointRegistry registry;

    @Test
    void test() throws IOException {

        ApplicationArguments applicationArguments = new DefaultApplicationArguments((String[]) Arrays.asList("--task=rabbit-sender", "--CHANNEL_CODE=10").toArray());

    }
/*
    @Configuration
    public class Config {

        @Bean(name = "defaultDBPool")
        @Primary
        public DataSource myDataSource(

                @Value("${database.driverClassName}" ) String driverClassName,
                @Value("${database.url}" ) String url,
                @Value("${database.username}" ) String username,
                @Value("${database.pass}" ) String pass
        ) {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setDriverClassName(driverClassName);
            hikariConfig.setJdbcUrl(url);
            hikariConfig.setUsername(username);
            hikariConfig.setPassword(pass);

            return new HikariDataSource(hikariConfig);
        }

        @Bean
        @Primary
        public DbService dbService(@Qualifier("defaultDBPool") DataSource dataSource) {
            return new DbService(dataSource);
        }
    }
*/


}
