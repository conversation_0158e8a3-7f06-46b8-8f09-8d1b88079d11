--drop schema if exists MA_TEMP cascade;
create schema MA_TEMP;


create sequence MA_TEMP.test_table_template_id_seq;

create table MA_TEMP.test_table
(
    row_id     bigint       not null default nextval ('MA_TEMP.test_table_template_id_seq'),
    version    numeric      not null,
    mess       varchar(10)  not null,
    active_flg boolean      not null default true,
    comment    text         not null,
    from_dttm  timestamp(6) not null,
    to_dttm    timestamp(6),

    primary key (row_id)
);


