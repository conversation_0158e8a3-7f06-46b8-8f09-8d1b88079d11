<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Generated by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.9-b130926.1035 svn-revision#5f6196f2b90e9460065a4c2f4e30e065b245e51e. -->
<definitions targetNamespace="http://phone.cti/CTIService" name="CTIService" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:tns="http://phone.cti/CTIService" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata">
  <types>
    <xs:schema version="1.0" targetNamespace="http://phone.cti/CTIService" xmlns:xs="http://www.w3.org/2001/XMLSchema">

      <xs:element name="EndCall" type="tns:EndCall"/>

      <xs:element name="EndCallResponse" type="tns:EndCallResponse"/>

      <xs:element name="StartCall" type="tns:StartCall"/>

      <xs:element name="StartCallResponse" type="tns:StartCallResponse"/>

      <xs:element name="UserSignal" type="tns:UserSignal"/>

      <xs:element name="UserSignalResponse" type="tns:UserSignalResponse"/>

      <xs:element name="callAdd" type="tns:callAdd"/>

      <xs:element name="callAddResponse" type="tns:callAddResponse"/>

      <xs:element name="callListNotification" type="tns:callListNotification"/>

      <xs:element name="callListNotificationResponse" type="tns:callListNotificationResponse"/>

      <xs:element name="callRemove" type="tns:callRemove"/>

      <xs:element name="callRemoveResponse" type="tns:callRemoveResponse"/>

      <xs:element name="updateCallDispositionCode" type="tns:updateCallDispositionCode"/>

      <xs:element name="updateCallDispositionCodeResponse" type="tns:updateCallDispositionCodeResponse"/>
      
      <xs:element name="placeIVRCall">
				<xs:complexType>
					<xs:sequence>
						<xs:element maxOccurs="unbounded" name="calls" type="tns:IVRCall"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
      
			<xs:complexType name="IVRCall">
				<xs:sequence>
					<xs:element name="CUID" type="xs:string"/>
					<xs:element minOccurs="0" name="ContractNr" type="xs:string"/>
					<xs:element minOccurs="0" name="expiration" type="xs:dateTime"/>
					<xs:element minOccurs="0" name="language" nillable="true" type="xs:string"/>
					<xs:element minOccurs="0" name="messageData" nillable="true" type="xs:string"/>
					<xs:element minOccurs="0" name="messageText" nillable="true" type="xs:string"/>
					<xs:element name="messageType" type="xs:string"/>
					<xs:element maxOccurs="unbounded" name="phones" type="tns:Phone"/>
					<xs:element name="taskId" type="xs:string"/>
					<xs:element minOccurs="0" name="callFrom" type="xs:double"/>
					<xs:element minOccurs="0" name="callTo" type="xs:double"/>
					<xs:element minOccurs="0" name="disChannel" type="xs:string"/>
					<xs:element minOccurs="0" name="taskAlias" type="xs:string"/>
				</xs:sequence>
			</xs:complexType>
      
			<xs:complexType name="Phone">
				<xs:sequence>
					<xs:element name="phoneNumber" type="xs:string"/>
					<xs:element minOccurs="0" name="lastRPCReceived" type="xs:dateTime"/>
				</xs:sequence>
			</xs:complexType>
      
			<xs:element name="placeIVRCallResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Status" type="tns:ResultCodes"/>
						<xs:element minOccurs="0" name="StatusText" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
      
			<xs:element name="removeIVRCall">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="UserName" type="xs:string"/>
						<xs:element name="taskId" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
      
			<xs:element name="removeIVRCallResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Status" type="tns:ResultCodes"/>
						<xs:element minOccurs="0" name="StatusText" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
      
      <xs:complexType name="UserSignal">
        <xs:sequence>
          <xs:element name="UserName" type="xs:string" form="qualified"/>
          <xs:element name="Signal" type="tns:UserModes" form="qualified" minOccurs="0"/>
          <xs:element name="CallID" type="xs:string" form="qualified"/>
          <xs:element name="Reason" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="CallGroupName" type="xs:string" form="qualified" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="UserSignalResponse">
        <xs:sequence>
          <xs:element name="Status" type="tns:ResultCodes" form="qualified"/>
          <xs:element name="StatusText" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="callRemove">
        <xs:sequence>
          <xs:element name="UserName" type="xs:string" form="qualified"/>
          <xs:element name="taskId" type="xs:string" form="qualified"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="callRemoveResponse">
        <xs:sequence>
          <xs:element name="Status" type="tns:ResultCodes" form="qualified"/>
          <xs:element name="StatusText" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="callAdd">
        <xs:sequence>
          <xs:element name="UserName" type="xs:string" form="qualified"/>
          <xs:element name="taskId" type="xs:string" form="qualified"/>
          <xs:element name="TaskType" type="xs:string" form="qualified"/>
          <xs:element name="Contract_id" type="xs:string" form="qualified"/>
          <xs:element name="PhoneNumbers" type="tns:PhoneNumber" form="qualified" maxOccurs="unbounded"/>
          <xs:element name="priority" type="xs:double" form="qualified" minOccurs="0"/>
          <xs:element name="CUID" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="language" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="skills" type="xs:string" form="qualified" nillable="true" maxOccurs="unbounded"/>
          <xs:element name="expiration" type="xs:dateTime" form="qualified" minOccurs="0"/>
          <xs:element name="callFrom" type="xs:double" form="qualified" minOccurs="0"/>
          <xs:element name="callTo" type="xs:double" form="qualified" minOccurs="0"/>
          <xs:element name="debtAmount" type="xs:double" form="qualified"/>
          <xs:element name="DPD" type="xs:double" form="qualified"/>
          <xs:element name="lastRPCReceived" type="xs:dateTime" form="qualified" minOccurs="0"/>
          <xs:element name="OutstandingBalance" type="xs:double" form="qualified" minOccurs="0"/>
          <xs:element name="Product" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="SubProduct" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="RiskCategory" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="DaysTonextPenalty" type="xs:double" form="qualified" minOccurs="0"/>
          <xs:element name="PreferredOperator" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="LastCallOperator" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="spin" type="xs:int" form="qualified" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="PhoneNumber">
        <xs:sequence>
          <xs:element name="PhoneNumberId" type="xs:string" form="qualified"/>
          <xs:element name="PhoneNumber" type="xs:string" form="qualified"/>
          <xs:element name="Priority" type="xs:double" form="qualified" minOccurs="0"/>
          <xs:element name="Name" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="phoneNumberType" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="lastRPCReceived" type="xs:dateTime" form="qualified" minOccurs="0"/>
          <xs:element name="lastPTPDate" type="xs:dateTime" form="qualified" minOccurs="0"/>
          <xs:element name="relationship" type="xs:string" form="qualified" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="callAddResponse">
        <xs:sequence>
          <xs:element name="Status" type="tns:ResultCodes" form="qualified"/>
          <xs:element name="StatusText" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="EndCall">
        <xs:sequence>
          <xs:element name="CallID" type="xs:string" form="qualified"/>
          <xs:element name="UserName" type="xs:string" form="qualified"/>
          <xs:element name="CallGroupName" type="xs:string" form="qualified" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="EndCallResponse">
        <xs:sequence>
          <xs:element name="Status" type="tns:ResultCodes" form="qualified"/>
          <xs:element name="StatusText" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="StartCall">
        <xs:sequence>
          <xs:element name="PhoneNumber" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="UserName" type="xs:string" form="qualified" minOccurs="0"/>
          <xs:element name="UserData" type="tns:KeyValuePair" form="qualified" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="CallGroupName" type="xs:string" form="qualified" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="KeyValuePair">
        <xs:sequence>
          <xs:element name="Key" type="xs:string" form="qualified"/>
          <xs:element name="Value" type="xs:string" form="qualified"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="StartCallResponse">
        <xs:sequence>
          <xs:element name="CallID" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
          <xs:element name="Status" type="tns:ResultCodes" form="qualified"/>
          <xs:element name="StatusText" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="updateCallDispositionCode">
        <xs:sequence>
          <xs:element name="UserName" type="xs:string" form="qualified"/>
          <xs:element name="Callid" type="xs:string" form="qualified"/>
          <xs:element name="Code" type="xs:string" form="qualified"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="updateCallDispositionCodeResponse">
        <xs:sequence>
          <xs:element name="Status" type="tns:ResultCodes" form="qualified"/>
          <xs:element name="StatusText" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="callListNotification">
        <xs:sequence>
          <xs:element name="idBatchExt" type="xs:long" form="qualified"/>
          <xs:element name="dataType" type="xs:string" form="qualified" nillable="true" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>

      <xs:complexType name="callListNotificationResponse">
        <xs:sequence/>
      </xs:complexType>

      <xs:simpleType name="UserModes">
        <xs:restriction base="xs:string">
          <xs:enumeration value="READY"/>
          <xs:enumeration value="NOTREADY"/>
        </xs:restriction>
      </xs:simpleType>

      <xs:simpleType name="ResultCodes">
        <xs:restriction base="xs:string">
          <xs:enumeration value="OK"/>
          <xs:enumeration value="FAIL"/>
        </xs:restriction>
      </xs:simpleType>
</xs:schema>
  </types>
  <message name="callRemove">
    <part name="parameters" element="tns:callRemove"/>
  </message>
  <message name="callRemoveResponse">
    <part name="parameters" element="tns:callRemoveResponse"/>
  </message>
  <message name="UserSignal">
    <part name="parameters" element="tns:UserSignal"/>
  </message>
  <message name="UserSignalResponse">
    <part name="parameters" element="tns:UserSignalResponse"/>
  </message>
  <message name="EndCall">
    <part name="parameters" element="tns:EndCall"/>
  </message>
  <message name="EndCallResponse">
    <part name="parameters" element="tns:EndCallResponse"/>
  </message>
  <message name="StartCall">
    <part name="parameters" element="tns:StartCall"/>
  </message>
  <message name="StartCallResponse">
    <part name="parameters" element="tns:StartCallResponse"/>
  </message>
  <message name="callAdd">
    <part name="parameters" element="tns:callAdd"/>
  </message>
  <message name="callAddResponse">
    <part name="parameters" element="tns:callAddResponse"/>
  </message>
  <message name="callListNotification">
    <part name="parameters" element="tns:callListNotification"/>
  </message>
  <message name="callListNotificationResponse">
    <part name="parameters" element="tns:callListNotificationResponse"/>
  </message>
  <message name="updateCallDispositionCode">
    <part name="parameters" element="tns:updateCallDispositionCode"/>
  </message>
  <message name="updateCallDispositionCodeResponse">
    <part name="parameters" element="tns:updateCallDispositionCodeResponse"/>
  </message>
  <message name="placeIVRCall">
		<part name="parameters" element="tns:placeIVRCall"/>
	</message>
	<message name="placeIVRCallResponse">
		<part name="parameters" element="tns:placeIVRCallResponse"/>
	</message>
	<message name="removeIVRCall">
		<part name="parameters" element="tns:removeIVRCall"/>
	</message>
	<message name="removeIVRCallResponse">
		<part name="parameters" element="tns:removeIVRCallResponse"/>
	</message>
  <portType name="CTIService">
    <operation name="callRemove">
      <input wsam:Action="http://phone.cti/CTIService/callRemove" message="tns:callRemove"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/callRemoveResponse" message="tns:callRemoveResponse"/>
    </operation>
    <operation name="UserSignal">
      <input wsam:Action="http://phone.cti/CTIService/UserSignal" message="tns:UserSignal"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/UserSignalResponse" message="tns:UserSignalResponse"/>
    </operation>
    <operation name="EndCall">
      <input wsam:Action="http://phone.cti/CTIService/EndCall" message="tns:EndCall"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/EndCallResponse" message="tns:EndCallResponse"/>
    </operation>
    <operation name="StartCall">
      <input wsam:Action="http://phone.cti/CTIService/StartCall" message="tns:StartCall"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/StartCallResponse" message="tns:StartCallResponse"/>
    </operation>
    <operation name="callAdd">
      <input wsam:Action="http://phone.cti/CTIService/callAdd" message="tns:callAdd"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/callAddResponse" message="tns:callAddResponse"/>
    </operation>
    <operation name="callListNotification">
      <input wsam:Action="http://phone.cti/CTIService/callListNotification" message="tns:callListNotification"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/callListNotificationResponse" message="tns:callListNotificationResponse"/>
    </operation>
    <operation name="updateCallDispositionCode">
      <input wsam:Action="http://phone.cti/CTIService/updateCallDispositionCode" message="tns:updateCallDispositionCode"/>
      <output wsam:Action="http://phone.cti/CTIService/CTIService/updateCallDispositionCodeResponse" message="tns:updateCallDispositionCodeResponse"/>
    </operation>
		<operation name="placeIVRCall">
			<input wsam:Action="http://phone.cti/CTIService/placeIVRCall" message="tns:placeIVRCall"/>
			<output wsam:Action="http://phone.cti/CTIService/placeIVRCallResponse" message="tns:placeIVRCallResponse"/>
		</operation>
		<operation name="removeIVRCall">
			<input wsam:Action="http://phone.cti/CTIService/removeIVRCall" message="tns:removeIVRCall"/>
			<output wsam:Action="http://phone.cti/CTIService/removeIVRCallResponse" message="tns:removeIVRCallResponse"/>
		</operation>
  </portType>
  <binding name="CTIServiceBinding" type="tns:CTIService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
    <operation name="callRemove">
      <soap:operation soapAction="http://phone.cti/CTIService/callRemove"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="UserSignal">
      <soap:operation soapAction="http://phone.cti/CTIService/UserSignal"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="EndCall">
      <soap:operation soapAction="http://phone.cti/CTIService/EndCall"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="StartCall">
      <soap:operation soapAction="http://phone.cti/CTIService/StartCall"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="callAdd">
      <soap:operation soapAction="http://phone.cti/CTIService/callAdd"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="callListNotification">
      <soap:operation soapAction="http://phone.cti/CTIService/callListNotification"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="updateCallDispositionCode">
      <soap:operation soapAction="http://phone.cti/CTIService/updateCallDispositionCode"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="placeIVRCall">
			<soap:operation soapAction="http://phone.cti/CTIService/placeIVRCall"/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
		</operation>
		<operation name="removeIVRCall">
			<soap:operation soapAction="http://phone.cti/CTIService/removeIVRCall"/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
		</operation>
  </binding>
  <service name="CTIService">
    <port name="CTIService" binding="tns:CTIServiceBinding">
      <soap:address location="REPLACE_WITH_ACTUAL_URL"/>
    </port>
  </service>
</definitions>