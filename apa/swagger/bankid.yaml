bankid-v1-processBankIdAuthorizationResult:
  post:
    tags:
      - public-bankid-app-controller
    operationId: processBankIdAuthorizationResult
    description: |
      Is public endpoint without security which is for callback from Onboarding by Bank Id
    requestBody:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ProcessBankIdAuthorizationRequestTO'
      required: true
    deprecated: false

bankid-v1-preLogin:
  post:
    security:
      - bearerAuthOptional: [ ]
    tags:
      - bankid-app-controller
    operationId: bankidPreLogin
    description: |
      Initializes BankId authorization which could be one of two types:
      a) simple authentication from a 3rd party service. some data providing consents may be required to complete the process. client can determine that by
        analysing 'operationType=AUTHENTICATION' property.
      b) authentication with document signing. property 'operationType=DOCUMENT_SIGN'.
      In both cases JWT with AUTHORITY_APA_LOGIN authority will be returned. Some additional info will be stored in JWT as well.
      Then /apa/v1/authentication/init must be called with newly obtained JWT and clients userName or client is able to use QR login too.
    requestBody:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BankidPreLoginRequestTO'
      required: true
    responses:
      "200":
        description: OK
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankidPreLoginResponseTO'
    deprecated: false

bankid-v1-postLogin:
  post:
    security:
      - bearerAuth: [ APA_APP_ACCESS_BANKID ]
    tags:
      - bankid-app-controller
    operationId: bankidPostLogin
    description: |
      This endpoint must be called after succesful authentication in IdPort. The response must be analyzed one of the following ways:
        a) BankidPostLoginResponseTO#redirect#token3 is present in the response, then the process could be considered as fully finised.
        b) BankidRedirectTO#redirect is absent and BankidRedirectTO#consents is present, then the customer must give his consent for personal data usage. All
          of BankidRedirectTO#consents#mandatoryConsents must be given and some of BankidRedirectTO#consents#requiredConsents could be given as well.
    responses:
      "200":
        description: OK
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankidPostLoginResponseTO'
    deprecated: false

bankid-v1-finishAuthorization:
  post:
    security:
      - bearerAuth: [APA_APP_ACCESS_BANKID]
    tags:
      - bankid-app-controller
    operationId: finishAuthorization
    description: finishes bankid authorization. saves customer's consents. returns BankidPostLoginResponseTO.redirect.
    requestBody:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BankidFinishAuthorizationRequestTO'
      required: true
    responses:
      "200":
        description: OK
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankidFinishAuthorizationResponseTO'
    deprecated: false

bankid-v1-rejectAuthorization:
  post:
    security:
      - bearerAuth: [APA_APP_ACCESS_BANKID]
    tags:
      - bankid-app-controller
    operationId: rejectAuthorization
    description: rejects customer's authorization in case of client doesn't want to provide consents or any other reson.
    requestBody:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BankidRejectAuthorizationRequestTO'
      required: true
    deprecated: false


components:
  schemas:
    ProcessBankIdAuthorizationRequestTO:
      title: ProcessBankIdAuthorizationRequestTO
      type: object
      properties:
        state:
          type: string
        code:
          type: string
        error:
          type: string
    BankidPreLoginRequestTO:
      title: BankidPreLoginRequestTO
      type: object
      properties:
        token1:
          type: string
          minLength: 1
        appId:
          type: string
          minLength: 1
        state:
          type: string
          minLength: 1
        tracestate:
          type: string
          minLength: 1
    BankidPreLoginResponseTO:
      title: BankidPreLoginResponseTO
      type: object
      required:
        - operationType
        - apaInitAuthToken
      properties:
        operationType:
          $ref: '#/components/schemas/BankidOperationTypeETO'
        apaInitAuthToken:
          type: string
          minLength: 1
        signRequestId:
          type: string
        appName:
          type: string
        providerName:
          type: string
        envelopeName:
          type: string
        envelopeHash:
          type: string
        redirectUrl:
          type: string
          minLength: 1
        documents:
          type: array
          items:
            $ref: '#/components/schemas/BankidDocumentTO'
        signObject:
          $ref: '#/components/schemas/BankidSignObjectTO'

    BankidPostLoginResponseTO:
      title: BankidPostLoginResponseTO
      type: object
      properties:
        operationType:
          $ref: '#/components/schemas/BankidOperationTypeETO'
        redirect:
          $ref: '#/components/schemas/BankidRedirectTO'
        consents:
          $ref: '#/components/schemas/BankidConsentsNeededTO'

    BankidFinishAuthorizationRequestTO:
      title: BankidFinishAuthorizationRequestTO
      type: object
      required:
        - consents
      properties:
        consents:
          type: array
          items:
            type: string
    BankidFinishAuthorizationResponseTO:
      title: BankidFinishAuthorizationResponseTO
      type: object
      properties:
        redirect:
          $ref: '#/components/schemas/BankidRedirectTO'

    BankidRedirectTO:
      title: BankidRedirectTO
      type: object
      required:
        - token3
        - redirectUrl
      properties:
        token3:
          type: string
          minLength: 1
        state:
          type: string
          minLength: 1
        tracestate:
          type: string
          minLength: 1
        redirectUrl:
          type: string
          minLength: 1
    BankidConsentsNeededTO:
      title: BankidConsentsNeededTO
      properties:
        requiredConsents:
          type: object
          additionalProperties:
            type: object
        mandatoryConsents:
          type: object
          additionalProperties:
            type: object
    BankidConsentTO:
      title: BankidConsentTO
      properties:
        personName:
          $ref: '#/components/schemas/PersonNameTO'
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/AddressTO'
        idcards:
          type: array
          items:
            $ref: '#/components/schemas/IdCardTO'
        maritalStatus:
          type: string
        gender:
          type: string
        birthcountry:
          type: string
        birthdate:
          type: string
          format: date
        age:
          type: integer
          format: int64
#        dateOfDeath:
#          type: string
#          format: date
        birthnumber:
          type: string
        birthplace:
          type: string
#        primaryNationality:
#          type: string
        nationalities:
          type: array
          items:
            type: string
#        ruianReference:
#          type: string
        paymentAccounts:
          type: array
          items:
            type: string
        email:
          type: string
        phoneNumber:
          type: string
        updatedAt:
          type: string
          format: date-time
        majority:
          type: boolean
          default: false
        pep:
          type: boolean
          default: false
        limitedLegalCapacity:
          type: boolean
          default: false
        zoneInfo:
          type: string
        locale:
          type: string
    PersonNameTO:
      title: PersonNameTO
      properties:
        firstName:
          type: string
        lastName:
          type: string
        titlePrefix:
          type: string
        titleSuffix:
          type: string
        allTitles:
          type: string
    AddressTO:
      title: AddressTO
      properties:
        addressType:
          type: string
        street:
          type: string
        buildingapartment:
          type: string
        streetnumber:
          type: string
        city:
          type: string
        zipcode:
          type: string
        country:
          type: string
    IdCardTO:
      title: IdCardTO
      properties:
        idcardType:
          type: string
        description:
          type: string
        country:
          type: string
        number:
          type: string
        validTo:
          type: string
          format: date
        issuer:
          type: string
        issueDate:
          type: string
          format: date-time
    BankidOperationTypeETO:
      type: string
      enum:
        - AUTHENTICATION
        - DOCUMENT_SIGN
    BankidDocumentTO:
      title: BankidDocumentTO
      properties:
        id:
          type: string
        documentHash:
          type: string
        title:
          type: string
        subject:
          type: string
        language:
          type: string
        created:
          type: string
          format: date-time
        author:
          type: string
        size:
          type: integer
          format: int32
        read_by_enduser:
          type: boolean
          default: false
        uri:
          type: string
        page_count:
          type: integer
          format: int32
    BankidSignObjectTO:
      title: BankidSignObjectTO
      properties:
        signObjectEntries:
          type: array
          items:
            $ref: '#/components/schemas/SignObjectEntryTO'
        signObjectHash:
          type: string
        created:
          type: string
          format: date-time
    SignObjectEntryTO:
      title: SignObjectEntryTO
      properties:
        key:
          type: string
        value:
          type: string
    BankidRejectAuthorizationRequestTO:
      title: BankidRejectAuthorizationRequestTO
      required:
        - reason
      properties:
        reason:
          $ref: '#/components/schemas/AuthorizationRejectionReasonETO'
    AuthorizationRejectionReasonETO:
      type: string
      enum:
        - CONSENTS_REJECTED
        - OTHER
