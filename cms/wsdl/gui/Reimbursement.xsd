<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://cms.airbank.cz/ws/card/reimbursement"
           xmlns="http://cms.airbank.cz/ws/card/reimbursement" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jakarta="https://jakarta.ee/xml/ns/jaxb" jakarta:version="3.0" elementFormDefault="qualified">

    <xs:element name="cancelCoverComplaintCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam reklamovaných plateb dan<PERSON> ke stornu pře<PERSON>čtování.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="cancelCoverComplaintCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="refundComplaintCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam reklamovaných plateb dané reklamace k proplacení.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="refundComplaintCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="cancelRefundComplaintCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam reklamovaných plateb dané reklamace ke stornu proplacení.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="cancelRefundComplaintCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="takeBackComplaintCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam reklamovaných plateb dané reklamace k proplacení.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="takeBackComplaintCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="coverComplaintCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam reklamovaných plateb dané reklamace k proplacení.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="taxExpense" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>
                            Přiznak pokrytí nákladů jako daňově
                            true = uznatelných
                            false = neuznatelných
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="coverComplaintCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="coverComplaintCardPaymentsFault">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="code" minOccurs="0" maxOccurs="1" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            COMPLAINT_DOES_NOT_EXISTS - reklamace neexistuje (mohla být jiným operátorem odstraněna) a tento formulář ("Detail reklamace") uzavře
                            CARD_PAYMENTS_UNIDENTIFIED - chybná identifikace platby → tento formulář refreshne
                            COVERING_COMPLAINT_TAX_EXPENSE_INCONSISTENCY - Přeúčtování není možné
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="manualBalanceComplaintAccountRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam reklamovaných plateb dané reklamace k proplacení.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="manualBalanceComplaintAccountResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="pairReturnComplaintCardPaymentRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" >
                    <xs:annotation>
                        <xs:documentation>
                            Reklamovaná platba dané reklamace k proplacení.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="unpairedReturnPresentmentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikátor presentmentu dosud nespárované vratky</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="pairReturnComplaintCardPaymentResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace, kde došlo ke změnám na účtovaném seznamu reklamovaných plateb.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="pairReturnComplaintCardPaymentFault">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="code" minOccurs="0" maxOccurs="1" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            COMPLAINT_DOES_NOT_EXISTS - reklamace neexistuje (mohla být jiným operátorem odstraněna) a tento formulář ("Detail reklamace") uzavře
                            CARD_PAYMENT_UNIDENTIFIED - chybná identifikace platby → tento formulář refreshne
                            CARD_PAYMENT_WITHOUT_PRESENTMENT - vybranou platbu nelze párovat, protože není evidován presentment.
                            CARD_PAYMENT_TAKEN_BACK - vybranou platbu nelze párovat, protože již byla klientovi odúčtována a nelze ji párováním znovu otevírat.
                            CARD_PAYMENT_ALREADY_COVERED - vybranou platbu nelze párovat, protože má přeúčtovány náklady na MAŠ.
                            CARD_PAYMENT_WITH_RETURN_PRESENTMENT - vybraná platba obsahuje spárovanou vratku a nelze ji znovu spárovat → tento formulář refreshne
                            RETURN_PRESENTMENT_ALREADY_PAIRED - vybraná vratka je již spárována (mohl udělat jiný operátor) → tento formulář refreshne
                            RETURN_PRESENTMENT_OLDER_THEN_PAYMENT_PRESENTMENT - vybraná vratka vznikla dříve než platba - párování nelze provést → tento formulář se refreshne.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="unpairReturnComplaintCardPaymentRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identifikator reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pair" type="ReturnToCardPayment" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam párů presentmentů vratek a plateb v reklamaci, které hodlá obsluha odpárovat.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responsibleEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného operátora, který je odpovědný za účtovací operaci</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="unpairReturnComplaintCardPaymentResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace, kde došlo ke změnám na účtovaném seznamu reklamovaných plateb.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="unpairReturnComplaintCardPaymentFault">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="code" minOccurs="0" maxOccurs="1" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            COMPLAINT_DOES_NOT_EXISTS - reklamace neexistuje (mohla být jiným operátorem odstraněna) a tento formulář ("Detail reklamace") uzavře
                            CARD_PAYMENT_UNIDENTIFIED - chybná identifikace plateb v reklamaci → tento formulář refreshne
                            CARD_PAYMENT_REFUND_OUT_OF_SETTLEMENT - proplacení (některých plateb v reklamaci) není kryto z vyrovnávacího účtu → tento formulář refreshne
                            RETURN_PRESENTMENT_OUT_OF_PAIR - vybrané vratky nelze odpárovat, protože (k některým) není evidován spárovaný presentment dané platby v reklamaci → tento formulář refreshne
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ReturnToCardPayment">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Vazba vratky v presentmentu na platbu v reklamaci
                </xs:documentation>
            </xs:annotation>
            <xs:element name="returnPresentmentId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Identifikátor presentmentu vratky.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalAccMoveId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Identifikátor platby kartou v reklamaci.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>