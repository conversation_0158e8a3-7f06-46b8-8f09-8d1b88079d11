<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://cms.airbank.cz/ws/card/complaint"
           xmlns="http://cms.airbank.cz/ws/card/complaint" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jakarta="https://jakarta.ee/xml/ns/jaxb" jakarta:version="3.0" elementFormDefault="qualified">

    <xs:element name="searchComplaintCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintBusinessJiraTicketId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Klíč reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardNumber" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation><PERSON><PERSON><PERSON> karty</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cuid" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ard" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="claimingStatus" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Kód stavu nárokování podle číselníku ClaimingStatus.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="claimStatus" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Kód stavu nároku podle číselníku ClaimStatus.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="presentmentAccMoveId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Identifikátor pohybu presentmentu dané platby odeslaného do OBS.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="chargebackReasonCode" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="memberMessageText" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseDueDateFrom" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseDueDateTo" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="chargebacksDateFrom" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="chargebacksDateTo" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="representmentsDateFrom" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="representmentsDateTo" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="recordsCount" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation>Požadovaný počet plateb - číslo z intervalu 1 - 1000.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchComplaintCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="searchComplaintCardPayment" type="SearchComplaintCardPayment" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam vyhledaných reklamovaných plateb kartou
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getComplaintDetailRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Klíč reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getComplaintDetailResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintDetail" type="ComplaintDetail">
                    <xs:annotation>
                        <xs:documentation>Detail reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getComplaintDetailFault">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="code" minOccurs="0" maxOccurs="1" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            DOES_NOT_EXISTS - neexistuje vstupní compaintId
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchCardPaymentsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>identifikátor karty</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="paymentFrom" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>počáteční datum pro vyhledávání plateb kartou</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="paymentTo" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>koncové datum pro vyhledánívání plateb kartou</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="productIndicator" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Druh místa načtení karty (ATM/POS)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardPaymentDataInputMode" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Režim načtení karty evidovaný na platbě kartou k reklamaci.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="recordsCount" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Není-li zadáno bude vráceno max 1 000 prvních záznamů.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchCardPaymentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardPayment" type="CardPayment"
                            minOccurs="0" maxOccurs="unbounded">
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchComplaintsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintBusinessJiraTicketId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Klíč reklamace</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardNumber" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Číslo karty</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Identifikace klienta</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="createdFrom" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum vzniku reklamace od</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="createdTo" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum vzniku reklamace do</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="recordsCount" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Počet záznamů, povolené hodnoty 1 - 1000.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchComplaintsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaint" type="Complaint" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation></xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="createComplaintRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="ticketId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Klíč reklamace evidovaný v Business Jiře</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam plateb kartou identifikovaných pomocí prvního accMoveId (bylo vráceno službou ComplaintWS.searchCardPayments)
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="creatorEmployeeNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>EmployeeNumber přihlášeného uživatele v CMS .NET clientovi.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="createComplaintResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikace vytvořené reklamace v CMS.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="createComplaintFault">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="code" minOccurs="0" maxOccurs="1" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            COMPLAINT_EXISTS - reklamace s daným klíčem je již evidována a nelze ji založit.
                            COMPLAINT_CARD_PAYMENT_EXISTS - vstupní seznam identifikátorů plateb již obsahuje perzistované platby kartou v reklamaci
                            COMPLAINTS_SAME_CARD_VIOLATION - bylo porušeno pravidlo, že vstupní seznam plateb kartou je od téže karty.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="addCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikace existující reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam identifikátorů plateb pro přidání do existující reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="addCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikace aktualizované reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="addCardPaymentsFault">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="code" minOccurs="0" maxOccurs="1" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            COMPLAINT_DOES_NOT_EXISTS - reklamace neexistuje (mohla být jiným operátorem odstrněna)
                            CARD_PAYMENTS_UNIDENTIFIED - v seznamu plateb jsou chybné identifikace
                            CARD_PAYMENTS_IN_COMPLAINT_ALREADY - vybrané platby obsahují platby již zařazené v reklamacích                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="removeCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam identifikátorů plateb určených k odstranění z dané reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="removeCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:annotation>
                    <xs:documentation>
                        Pokud je reklamace odstraněna, response neobsahuje identifikátor reklamace (byl zrušen).
                    </xs:documentation>
                </xs:annotation>
                <xs:element name="complaintId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor upravené reklamace. Pokud je reklamace odstraněna, není atribut naplněn.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="openClaimingCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam identifikátorů plateb reklamace určených k otevření nárokování z dané reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="openClaimingCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor upravené reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="refuseClaimingCardPaymentsRequestType">
        <xs:complexType>
            <xs:sequence>
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor reklamace
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="originalAccMoveId" type="xs:long" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Seznam identifikátorů plateb reklamace určených k otevření nárokování z dané reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="refuseClaimingCardPaymentsResponseType">
        <xs:complexType>
            <xs:sequence>
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
                <xs:element name="complaintId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>
                            Identifikátor upravené reklamace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="CardPayment">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Platba kartou
                </xs:documentation>
            </xs:annotation>
            <xs:element name="originalAccMoveId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Interní identifikace pohybu na kartovém účtu, který je první spojený s platbou kartou a tím ji také identifikuje.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productIndicator" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Base24 product indicator, e.g: POS/ATM</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardPaymentDataInputMode" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Režim načtení karty - kód z číselníku CardPaymentDataInputMode.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardPaymentHolderAuthenticationMethod" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Typ ověření - kód z číselníku CardPaymentHolderAuthenticationMethod.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="walletCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        V případě platby tokenem kód peněženky, což je název mobilní aplikace druhu peněženka, která token použila.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="walletName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Peněženky nejsou CMS číselník ani MDM číselník, ale standardní tabulka ..., tak proto tahle výjimka.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paid" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>
                        Datum a čas realizace platby kartou - mapován podle stavu (viz. CardPaymentStatus):
                        ACTIVE, CANCELED, RELEASED → Datum a čas platby autorizace našeho času v Air Bank
                        ACCOUNTED, REFUNDED, TAKEN_BACK → Datum a čas obchodníka dle presentmentu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Částka v měně platby (to co klient skutečně na terminále zaplatil)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalCurrency" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Měna platby na terminále.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Částka účtována na účet, ke kterému byla karty vydána v měně účtu.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountCurrency" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Měna účtu, ke kterému byla karta vydána
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merchantName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Evidovaný název obchodníka na platbě kartou k reklamaci
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Stav platby podle číselníku CardPaymentStatus.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="authNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Autorizační kód
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ard" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Acquirer Reference Data - unikatní identifikátor transakce z prvního presentmentu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="complaintBusinessJira" type="ComplaintBusinessJira" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Atributy reklamace pro proklik do BusinessJira.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ComplaintBusinessJira">
        <xs:sequence>
            <xs:element name="ticketId" type="xs:string"/>
            <xs:element name="ticketUrl" type="xs:string"/>
            <xs:element name="reported" minOccurs="0" type="xs:dateTime"/>
            <xs:element name="status" minOccurs="0" type="xs:string"/>
            <xs:element name="commentUrl" minOccurs="0" type="xs:string"/>
            <xs:element name="commentId" minOccurs="0" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ComplaintDetail">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Reklamace a její detailní informace, tedy výčetky, platby, vratky...
                </xs:documentation>
            </xs:annotation>
            <xs:element name="complaint" type="Complaint">
                <xs:annotation>
                    <xs:documentation>
                        Reklamace evidovaná v CMS.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardPayment" type="ComplaintCardPayment"
                        maxOccurs="unbounded">
            </xs:element>
            <xs:element name="cardPaymentStatistic" type="StatusStatistic"
                        maxOccurs="unbounded">
            </xs:element>
            <xs:element name="complaintClaimingStatistic" type="StatusStatistic"
                        maxOccurs="unbounded">
            </xs:element>
            <xs:element name="complaintClaimStatistic" type="StatusStatistic"
                        maxOccurs="unbounded">
            </xs:element>
            <xs:element name="unpairedReturnPresentment" type="ReturnPresentment"
                        minOccurs="0" maxOccurs="unbounded">
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Complaint">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation></xs:documentation>
            </xs:annotation>
            <xs:element name="complaintId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Klíč reklamace</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="complaintBusinessJira" type="ComplaintBusinessJira">
                <xs:annotation>
                    <xs:documentation>
                        Atributy reklamace pro proklik do BusinessJira.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="creation" type="ComplaintCreation">
                <xs:annotation>
                    <xs:documentation>
                        Vznik reklamace v CMS.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentCard" type="ComplaintPaymentCard">
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentsCount" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentsSum" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="claimingTakenEmployee" type="Employee" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="ComplaintCreation">
        <xs:sequence>
            <xs:element name="created" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>
                        Datum a čas vzniku reklamace v CMS.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="employee" type="Employee">
                <xs:annotation>
                    <xs:documentation>
                        Opdovědná osoba zakládající reklamaci
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="SearchComplaintCardPayment">
        <xs:sequence>
            <xs:element name="complaint" type="Complaint">
                <xs:annotation>
                    <xs:documentation>Reklamace platby kartou</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="complaintCardPayment" type="ComplaintCardPayment" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Reklamované platby kartou v dané reklamaci</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ComplaintCardPayment">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation></xs:documentation>
            </xs:annotation>
            <xs:element name="originalAccMoveId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Interní identifikace pohybu na kartovém účtu, který je první spojený s platbou kartou a tím ji také identifikuje.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productIndicator" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Base24 product indicator, e.g: POS/ATM</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardPaymentDataInputMode" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Režim načtení karty - kód z číselníku CardPaymentDataInputMode.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardPaymentHolderAuthenticationMethod" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Typ ověření - kód z číselníku CardPaymentHolderAuthenticationMethod.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="walletCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        V případě platby tokenem kód peněženky, což je název mobilní aplikace druhu peněženka, která token použila.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="walletName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Peněženky nejsou CMS číselník ani MDM číselník, ale standardní tabulka ..., tak proto tahle výjimka.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paid" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>
                        Datum a čas realizace platby kartou - mapován podle stavu (viz. CardPaymentStatus):
                        ACTIVE, CANCELED, RELEASED → Datum a čas platby autorizace našeho času v Air Bank
                        ACCOUNTED, REFUNDED, TAKEN_BACK → Datum a čas obchodníka dle presentmentu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Částka v měně platby (to co klient skutečně na terminále zaplatil)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalCurrency" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Měna platby na terminále.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Částka účtována na účet, ke kterému byla karty vydána v měně účtu.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merchantName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Evidovaný název obchodníka na platbě kartou k reklamaci
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Stav platby podle číselníku CardPaymentStatus.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="authNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Autorizační kód
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ard" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Acquirer Reference Data - unikatní identifikátor transakce z prvního presentmentu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pair" type="PairReturnPresentment" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Spárovaná vratka
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="claimingStatus" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Stav nároku
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="claim" type="Claim" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Nárok
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="refund" type="ComplaintCardPaymentRefund" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Proplacení
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="complaintAccountSum" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        součet částek reklamované platby na účtu COMPLAINT.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="complaintAccountBalancedManually" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>
                        příznak, zápisu manuálního vyrovnání účtu COMPLAINT v OBS.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="Claim">
        <xs:sequence>
            <xs:element name="status" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="messageReasonCode" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="memberMessageText" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="responseDueDate" type="xs:date">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ComplaintCardPaymentRefund">
        <xs:sequence>
            <xs:element name="employeeNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Osoba, která má na odpovědnost rozhodnutí o (případném) proplacení.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeeName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Jméno a příjmení osoby, doplňuje se pro zobrazení.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="decided" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Datum a čas rozhodnutí o (případném) proplacení.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="billingAccount" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Účet k (případnému) proplacení, kód z číselníku ComplaintBillingAccount, fakticky může být jen COMPLAINT, LOSS, LOSST, SETTLEMENT
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ComplaintPaymentCard">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>Platba v reklamaci</xs:documentation>
            </xs:annotation>
            <xs:element name="cardId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Číslo karty</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Číslo karty</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardHolder" type="PaymentCardHolder">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountCurrency" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Měna účtu, ke kterému byla karta vydána
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentCardHolder">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstName" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ComplaintBillingAccountStatistic">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Výčetka (statistika) zúčtovaní podle zúčtovacích účtů.
                    (Nezahrnuje blokace a jejich uvolňování - nejedná se o účetní pohyb.)
                </xs:documentation>
            </xs:annotation>
            <xs:element name="billingAccount" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Stav podle business klíče ComplaintBillingAccount.
                        Pokud není vyplněno, pak počet a suma jsou za všechny účty a to by měla být 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="transactionCount" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Počet transakcí (účetních pohybů) souvisejících s daným účtem a reklamací
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="transactionSum" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Suma transakcí (účetních pohybů) souvisejících s daným účtem a reklamací
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="StatusStatistic">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Výčetka (statistika) plateb v reklamaci podle stavů plateb.
                </xs:documentation>
            </xs:annotation>
            <xs:element name="status" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentsCount" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Počet plateb daného stavu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentsSum" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Suma plateb daného stavu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PairReturnPresentment">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Spárovaná vratka platby v reklamaci
                </xs:documentation>
            </xs:annotation>
            <xs:element name="returnPresentment" type="ReturnPresentment">
                <xs:annotation>
                    <xs:documentation>
                        Presentment vratky.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="method" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Párovací metoda, která spárování vratky provedla - viz. číselník ReturnToPaymentPresentmentPairMethod
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paired" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>
                        Datum a čas provedení párování
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeeNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        V připadě manuálního párování osoba, která je za párování odpovědná
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeeName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Příjmení a jméno operátora, která je za manuální párování odpovědný.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Employee">
        <xs:sequence>
            <xs:element name = "number" type="xs:string" minOccurs="1" >
                <xs:annotation>
                    <xs:documentation>
                        Osobní číslo operátora.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name = "name" type="xs:string" minOccurs="1" >
                <xs:annotation>
                    <xs:documentation>
                        Příjmení a jméno operátora.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ReturnPresentment">
        <xs:sequence>
            <xs:annotation>
                <xs:documentation>
                    Vratka jako presentment.
                </xs:documentation>
            </xs:annotation>
            <xs:element name="presentmentId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Identifikátor presentmentu podle McIncomingClient.id
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accMoveId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        Identifikátor účetního pohybu v CMS (nemusí se nutně jednat vždy o pohyb mezi vyrovnávacím účtem (SETTLEMET) a účtem klienta (CLIENT),
                        ale mohl by zde být i například jen pohyb mezi vyrovnávacím účtem (SETTLEMENT) a vnitřním reklamačním účtem (COMPLAINT)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="returned" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>
                        Lokální datum a čas operace na presentmentu (DATE_TRANSACTION)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Částka vratky
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalCurrency" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Třímístný kód původní měny vratky
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        Částka odpovídající měně účtu spojeného s kartou klienta
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merchantName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Název obchodníka (CARD_ACCEPTOR_NAME)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="authNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Autorizační kód (APPROVAL_CODE)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
