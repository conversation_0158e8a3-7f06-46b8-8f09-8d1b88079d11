<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://cms.airbank.cz/ws/card/cardtoken"
           xmlns="http://cms.airbank.cz/ws/card/cardtoken"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified">

    <xs:element name="getCardTokenDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardTokenId" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getCardTokenDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardToken" type="paymentCardToken" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getCardTokenListRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardNumber" type="xs:string" minOccurs="0"/>
                <xs:element name="accountNumber" type="xs:string" minOccurs="0"/>
                <xs:element name="cuid" type="xs:long" minOccurs="0"/>
                <xs:element name="paymentAppInstanceId" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getCardTokenListResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardToken" type="paymentCardToken" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="deleteTokenRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardTokenId" type="xs:long" minOccurs="0"/>
                <xs:element name="causedBy" type="TokenChangeCausedBy"/>
                <xs:element name="reason" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Free form reason why the Tokens are being deleted.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="reasonCode" type="TokenDeleteReasonCode">
                    <xs:annotation>
                        <xs:documentation>The reason for the action to be deleted.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deleteTokenResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="suspendTokenRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardTokenId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identification number of the Card Token.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="causedBy" type="TokenChangeCausedBy"/>
                <xs:element name="reason" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Free form reason why the Tokens are being suspended.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="reasonCode" type="TokenSuspendReasonCode">
                    <xs:annotation>
                        <xs:documentation>The reason for the action to be suspended.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="suspendTokenResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="unsuspendTokenRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardTokenId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Identification number of the Card Token.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="causedBy" type="TokenChangeCausedBy"/>
                <xs:element name="reason" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Free form reason why the Tokens are being unsuspended.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="reasonCode" type="TokenUnsuspendReasonCode">
                    <xs:annotation>
                        <xs:documentation>The reason for the action to be unsuspended.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="unsuspendTokenResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="activateTokenRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="digitizationId" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="activateTokenResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getDigitizationsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardId" type="xs:long" minOccurs="0"/>
                <xs:element name="states" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="paymentAppInstanceId" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="getDigitizationsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardTokenDigitization" type="cardTokenDigitization" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchTokenRequestorRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="filterTokenRequestor"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="searchTokenRequestorResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="tokenRequestorResultItem" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="updateTokReqCustomNameRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="id" type="xs:long"/>
                <xs:element name="customName" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="updateTokReqCustomNameResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>



    <xs:complexType name="paymentCardToken">
        <xs:sequence>
            <xs:element name="cardTokenId" type="xs:long"/>
            <xs:element name="cardId" type="xs:long"/>
            <xs:element name="tokenUniqueReference" type="xs:string" minOccurs="0"/>
            <xs:element name="state" type="xs:string"/>
            <xs:element name="paymentAppInstanceId" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0"/>
            <xs:element name="validFrom" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="validTo" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="panSuffix" type="xs:int" minOccurs="0"/>
            <xs:element name="tokenSuffix" type="xs:int" minOccurs="0"/>
            <xs:element name="invalidCDCVMCount" type="xs:int" minOccurs="0"/>
            <xs:element name="blockedByCard" type="xs:boolean" minOccurs="0"/>
            <xs:element name="blockedByApp" type="xs:boolean" minOccurs="0"/>
            <xs:element name="blockedByClient" type="xs:boolean" minOccurs="0"/>
            <xs:element name="cardNumber" type="xs:string" minOccurs="0"/>
            <xs:element name="maskedCardNumber" type="xs:string" minOccurs="0"/>
            <xs:element name="accountNumber" type="xs:string"/>
            <xs:element name="cuid" type="xs:long" minOccurs="0"/>
            <xs:element name="walletCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The identifier of the Token wallet.
                        AIRBANK,
                        APPLE_PAY,
                        GOOGLE_PAY
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="walletName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The displayable name of the Token wallet.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deviceName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Device name for non Airbank wallet
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="migrationEnabled" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Flag if the token can be migrated to another card via card replacement or card renewal
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insTime" type="xs:dateTime"/>
            <xs:element name="modifTime" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="cardTokenDigitization">
        <xs:sequence>
            <xs:element name="digitizationId" type="xs:long"/>
            <xs:element name="cardId" type="xs:long"/>
            <xs:element name="insTime" type="xs:dateTime"/>
            <xs:element name="state" type="xs:string"/>
            <xs:element name="tokenUniqueReference" type="xs:string" minOccurs="0"/>
            <xs:element name="paymentAppInstanceId" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorName" type="xs:string" minOccurs="0"/>
            <xs:element name="deviceName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Device name for non Airbank wallet
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deviceScore" type="xs:string" minOccurs="0"/>
            <xs:element name="accountScore" type="xs:string" minOccurs="0"/>
            <xs:element name="wallet" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The identifier of the Token wallet.
                        AIRBANK,
                        APPLE_PAY,
                        GOOGLE_PAY
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bankDecision" type="BankDecision" minOccurs="0"/>
            <xs:element name="activationMethods" type="xs:string" minOccurs="0"/>
            <xs:element name="usedActivationPath" type="xs:string" minOccurs="0"/>
            <xs:element name="modifTime" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="cardNumber" type="xs:string" minOccurs="0"/>
            <xs:element name="maskedCardNumber" type="xs:string" minOccurs="0"/>
            <xs:element name="cardInfoJson" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="filterTokenRequestor">
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0"/>
            <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorName" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorPublicName" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorCustomName" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="tokenRequestorResultItem">
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0"/>
            <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorName" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorPublicName" type="xs:string" minOccurs="0"/>
            <xs:element name="tokenRequestorCustomName" type="xs:string" minOccurs="0"/>
            <xs:element name="insTime" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="modifTime" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="deleteTokenFault" type="xs:string"/>

    <xs:element name="suspendTokenFault" type="xs:string"/>

    <xs:element name="unsuspendTokenFault" type="xs:string"/>

    <xs:element name="activateTokenFault" type="xs:string"/>

    <xs:simpleType name="BankDecision">
        <xs:annotation>
            <xs:documentation>Cesta (zelená/žlutá/oranžová/červená) : pouze pro digitalizace přes Apple Pay, doporučení/scoring od Apple</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVED">
                <xs:annotation>
                    <xs:documentation>zelena</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="REQUIRE_ADDITIONAL_AUTHENTICATION">
                <xs:annotation>
                    <xs:documentation>zluta</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HIGH_RISK">
                <xs:annotation>
                    <xs:documentation>oranzova</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DECLINED">
                <xs:annotation>
                    <xs:documentation>cervena</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TokenChangeCausedBy">
        <xs:annotation>
            <xs:documentation>Who or what caused the Token status to be changed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CARDHOLDER">
                <xs:annotation>
                    <xs:documentation>Operation requested by the Cardholder.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TOKEN_REQUESTOR">
                <xs:annotation>
                    <xs:documentation>Operation requested by the Token Requestor.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ERROR_HANDLER">
                <xs:annotation>
                    <xs:documentation>Operation requested by the Token Requestor.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TokenSuspendReasonCode">
        <xs:annotation>
            <xs:documentation>The reason for the Suspend tokens action.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEVICE_LOST">
                <xs:annotation>
                    <xs:documentation>Token device lost.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEVICE_STOLEN">
                <xs:annotation>
                    <xs:documentation>Token device stolen.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SUSPECTED_FRAUD">
                <xs:annotation>
                    <xs:documentation>Suspected fraudulent token transactions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PUSH_TIMEOUT">
                <xs:annotation>
                    <xs:documentation>Push timeout.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTHER">
                <xs:annotation>
                    <xs:documentation>Other.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TokenUnsuspendReasonCode">
        <xs:annotation>
            <xs:documentation>The reason for the Unsuspend tokens action.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEVICE_FOUND">
                <xs:annotation>
                    <xs:documentation>Token device found or not stolen.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_FRAUD">
                <xs:annotation>
                    <xs:documentation>Confirmed no fraudulent token transactions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTHER">
                <xs:annotation>
                    <xs:documentation>Other.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TokenDeleteReasonCode">
        <xs:annotation>
            <xs:documentation>The reason for the Delete token action.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEVICE_LOST">
                <xs:annotation>
                    <xs:documentation>Token device lost.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEVICE_STOLEN">
                <xs:annotation>
                    <xs:documentation>Token device stolen.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACCOUNT_CLOSED">
                <xs:annotation>
                    <xs:documentation>Account closed.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SUSPECTED_FRAUD">
                <xs:annotation>
                    <xs:documentation>Suspected fraudulent token transactions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PUSH_TIMEOUT">
                <xs:annotation>
                    <xs:documentation>Push tiemout.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTHER">
                <xs:annotation>
                    <xs:documentation>Other.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
