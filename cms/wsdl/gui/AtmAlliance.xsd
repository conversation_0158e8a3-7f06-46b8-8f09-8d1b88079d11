<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://cms.airbank.cz/ws/atm/atmalliance"
           xmlns="http://cms.airbank.cz/ws/atm/atmalliance"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified">

    <xs:complexType name="searchATMAllianceReportFilter">
        <xs:sequence>
            <xs:element name="nameFile" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idTerminal" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="acquirer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionDateFrom" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionDateTo" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idTransaction" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idSettlement" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="lineCount" type="xs:int" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="atmAllianceDailyInterbankReportItem">
        <xs:sequence>
            <xs:element name="nameFile" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idTerminal" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="acquirer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionDateTime" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionAmount" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idTransaction" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idSettlement" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idJira" type="xs:string" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="atmAllianceMonthlyInterbankReportItem">
        <xs:sequence>
            <xs:element name="fileId" type="xs:long" minOccurs="0" maxOccurs="1"/>
            <xs:element name="nameFile" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="acquirer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="fileCreationDateTime" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idSettlement" type="xs:string" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="atmAllianceDailyInterbankReportDetail">
        <xs:sequence>
            <xs:element name="nameFile" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="creationDateTime" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="businessDay" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sequenceNumber" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="duplicity" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="fileVersion" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idTransaction" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionAmount" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionCurrency" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionDateTime" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionType" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="suspiciousCashIn" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="standIn" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idSettlement" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idTerminal" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="acquirer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuerClientInternalReference" type="xs:string" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="atmAllianceMonthlyInterbankReportDetail">
        <xs:sequence>
            <xs:element name="nameFile" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="creationDateTime" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
            <xs:element name="businessDay" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sequenceNumber" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="duplicity" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="fileVersion" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="acquirer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sumAmountOfTransactions" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sumCountOfTransactions" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionCurrency" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionType" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idSettlement" type="xs:string" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="atmAllianceDailyInterbankSummaryReportItem">
        <xs:sequence>
            <xs:element name="nameFile" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="idSettlement" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="issuer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="acquirer" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="transactionCurrency" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sumAmountOfTransactions" type="xs:decimal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sumCountOfTransactions" type="xs:int" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="searchATMAllianceDailyInterbankReportRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="searchATMAllianceReportFilter" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchATMAllianceDailyInterbankReportResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmDailyInterbankReportItem" type="atmAllianceDailyInterbankReportItem" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceDailyInterbankReportDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idTransaction" type="xs:string" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceDailyInterbankReportDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmDailyInterbankReportDetail" type="atmAllianceDailyInterbankReportDetail" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchATMAllianceDailyUnprocessedInterbankReportRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="searchATMAllianceReportFilter" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchATMAllianceDailyUnprocessedInterbankReportResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmDailyUnprocessedInterbankReportItem" type="atmAllianceDailyInterbankReportItem" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceDailyUnprocessedInterbankReportDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idTransaction" type="xs:string" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceDailyUnprocessedInterbankReportDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmDailyUnprocessedInterbankReportDetail" type="atmAllianceDailyInterbankReportDetail" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchATMAllianceMonthlyInterbankReportRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="searchATMAllianceReportFilter" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="searchATMAllianceMonthlyInterbankReportResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmMonthlyInterbankReportItem" type="atmAllianceMonthlyInterbankReportItem" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceMonthlyInterbankReportDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idFile" type="xs:long" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceMonthlyInterbankReportDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmMonthlyInterbankReportDetail" type="atmAllianceMonthlyInterbankReportDetail" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceDailyInterbankSummaryReportRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idTransaction" type="xs:string" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="getATMAllianceDailyInterbankSummaryReportResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="atmAllianceDailyInterbankSummaryReportItem" type="atmAllianceDailyInterbankSummaryReportItem" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>
