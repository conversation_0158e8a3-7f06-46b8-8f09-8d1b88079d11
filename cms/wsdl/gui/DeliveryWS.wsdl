<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://cms.airbank.cz/ws/envelope/delivery" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://cms.airbank.cz/ws/envelope/delivery">

    <xs:annotation>
        <xs:documentation>
            DeliveryWS - Endpoint for operations with Envelope Delivery.
        </xs:documentation>
    </xs:annotation>

    <wsdl:types>
        <xs:schema targetNamespace="http://cms.airbank.cz/ws/envelope/delivery">
            <xs:include schemaLocation="DeliveryWS.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="GetReturnedEnvelopesRequest">
        <wsdl:part element="tns:GetReturnedEnvelopesRequest" name="GetReturnedEnvelopesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetReturnedEnvelopesResponse">
        <wsdl:part element="tns:GetReturnedEnvelopesResponse" name="GetReturnedEnvelopesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetReturnedEnvelopesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetReturnedEnvelopesFault"/>
    </wsdl:message>
    <wsdl:message name="WriteReturnedEnvelopeRequest">
        <wsdl:part element="tns:WriteReturnedEnvelopeRequest" name="WriteReturnedEnvelopeRequest"/>
    </wsdl:message>
    <wsdl:message name="WriteReturnedEnvelopeResponse">
        <wsdl:part element="tns:WriteReturnedEnvelopeResponse" name="WriteReturnedEnvelopeResponse"/>
    </wsdl:message>
    <wsdl:message name="WriteReturnedEnvelopeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="WriteReturnedEnvelopeFault"/>
    </wsdl:message>
    <wsdl:message name="GetRedistributionEnvelopesRequest">
        <wsdl:part element="tns:GetRedistributionEnvelopesRequest" name="GetRedistributionEnvelopesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetRedistributionEnvelopesResponse">
        <wsdl:part element="tns:GetRedistributionEnvelopesResponse" name="GetRedistributionEnvelopesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetRedistributionEnvelopesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetRedistributionEnvelopesFault"/>
    </wsdl:message>
    <wsdl:message name="ConfirmRedistributionRequest">
        <wsdl:part element="tns:ConfirmRedistributionRequest" name="ConfirmRedistributionRequest"/>
    </wsdl:message>
    <wsdl:message name="ConfirmRedistributionResponse">
        <wsdl:part element="tns:ConfirmRedistributionResponse" name="ConfirmRedistributionResponse"/>
    </wsdl:message>
    <wsdl:message name="ConfirmRedistributionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ConfirmRedistributionFault"/>
    </wsdl:message>
    <wsdl:message name="validatePurposeAddressRequest">
        <wsdl:part element="tns:validatePurposeAddressRequest" name="validatePurposeAddressRequest"/>
    </wsdl:message>
    <wsdl:message name="validatePurposeAddressResponse">
        <wsdl:part element="tns:validatePurposeAddressResponse" name="validatePurposeAddressResponse"/>
    </wsdl:message>
    <wsdl:message name="validatePurposeAddressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="validatePurposeAddressFault"/>
    </wsdl:message>

    <wsdl:portType name="DeliveryWS">
        <wsdl:operation name="getReturnedEnvelopes">
            <wsdl:input message="tns:GetReturnedEnvelopesRequest" name="GetReturnedEnvelopesRequest"/>
            <wsdl:output message="tns:GetReturnedEnvelopesResponse" name="GetReturnedEnvelopesResponse"/>
            <wsdl:fault message="tns:GetReturnedEnvelopesFault" name="GetReturnedEnvelopesFault"/>
        </wsdl:operation>
        <wsdl:operation name="writeReturnedEnvelope">
            <wsdl:input message="tns:WriteReturnedEnvelopeRequest" name="WriteReturnedEnvelopeRequest"/>
            <wsdl:output message="tns:WriteReturnedEnvelopeResponse" name="WriteReturnedEnvelopeResponse"/>
            <wsdl:fault message="tns:WriteReturnedEnvelopeFault" name="WriteReturnedEnvelopeFault"/>
        </wsdl:operation>
        <wsdl:operation name="getRedistributionEnvelopes">
            <wsdl:input message="tns:GetRedistributionEnvelopesRequest" name="GetRedistributionEnvelopesRequest"/>
            <wsdl:output message="tns:GetRedistributionEnvelopesResponse" name="GetRedistributionEnvelopesResponse"/>
            <wsdl:fault message="tns:GetRedistributionEnvelopesFault" name="GetRedistributionEnvelopesFault"/>
        </wsdl:operation>
        <wsdl:operation name="confirmRedistribution">
            <wsdl:input message="tns:ConfirmRedistributionRequest" name="ConfirmRedistributionRequest"/>
            <wsdl:output message="tns:ConfirmRedistributionResponse" name="ConfirmRedistributionResponse"/>
            <wsdl:fault message="tns:ConfirmRedistributionFault" name="ConfirmRedistributionFault"/>
        </wsdl:operation>
        <wsdl:operation name="validatePurposeAddress">
            <wsdl:input message="tns:validatePurposeAddressRequest" name="validatePurposeAddressRequest"/>
            <wsdl:output message="tns:validatePurposeAddressResponse" name="validatePurposeAddressResponse"/>
            <wsdl:fault message="tns:validatePurposeAddressFault" name="validatePurposeAddressFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="DeliveryWSSoap11" type="tns:DeliveryWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getReturnedEnvelopes">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetReturnedEnvelopesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetReturnedEnvelopesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetReturnedEnvelopesFault">
                <soap:fault name="GetReturnedEnvelopesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="writeReturnedEnvelope">
            <soap:operation soapAction=""/>
            <wsdl:input name="WriteReturnedEnvelopeRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="WriteReturnedEnvelopeResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="WriteReturnedEnvelopeFault">
                <soap:fault name="WriteReturnedEnvelopeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getRedistributionEnvelopes">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetRedistributionEnvelopesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetRedistributionEnvelopesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetRedistributionEnvelopesFault">
                <soap:fault name="GetRedistributionEnvelopesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="confirmRedistribution">
            <soap:operation soapAction=""/>
            <wsdl:input name="ConfirmRedistributionRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ConfirmRedistributionResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ConfirmRedistributionFault">
                <soap:fault name="ConfirmRedistributionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="validatePurposeAddress">
            <soap:operation soapAction=""/>
            <wsdl:input name="validatePurposeAddressRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="validatePurposeAddressResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="validatePurposeAddressFault">
                <soap:fault name="validatePurposeAddressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="DeliveryWSService">
        <wsdl:port binding="tns:DeliveryWSSoap11" name="DeliveryWSSoap11">
            <soap:address location="https://TO-BE-CHANGED/ws/envelope/DeliveryWS"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>