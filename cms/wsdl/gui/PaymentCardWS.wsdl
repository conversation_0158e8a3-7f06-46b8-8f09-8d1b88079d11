<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/ws/card/paymentcard"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://homecredit.net/ws/card/paymentcard">
    <xs:annotation>
        <xs:documentation>PaymentCardWS - The endpoint for payment card management.
            Error severity: minor, error businessImpact: Inability to manage payment cards.
            Average response time: &lt;10s, Throughput: Average 1 request per second
        </xs:documentation>
    </xs:annotation>

    <wsdl:types>
        <xs:schema targetNamespace="http://homecredit.net/ws/card/paymentcard">
            <xs:include schemaLocation="PaymentCard.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>
    <wsdl:message name="activateCardRequest">
        <wsdl:part element="tns:activateCardRequest" name="activateCardRequest"/>
    </wsdl:message>
    <wsdl:message name="activateCardResponse">
        <wsdl:part element="tns:activateCardResponse" name="activateCardResponse"/>
    </wsdl:message>
    <wsdl:message name="activateCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="activateCardFault"/>
    </wsdl:message>
    <wsdl:message name="addCardDescRequest">
        <wsdl:part element="tns:addCardDescRequest" name="addCardDescRequest"/>
    </wsdl:message>
    <wsdl:message name="addCardDescResponse">
        <wsdl:part element="tns:addCardDescResponse" name="addCardDescResponse"/>
    </wsdl:message>
    <wsdl:message name="addCardDescFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="addCardDescFault"/>
    </wsdl:message>
    <wsdl:message name="changeDesign4CardRequest">
        <wsdl:part element="tns:changeDesign4CardRequest" name="changeDesign4CardRequest"/>
    </wsdl:message>
    <wsdl:message name="changeDesign4CardResponse">
        <wsdl:part element="tns:changeDesign4CardResponse" name="changeDesign4CardResponse"/>
    </wsdl:message>
    <wsdl:message name="changeDesign4CardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="changeDesign4CardFault"/>
    </wsdl:message>
    <wsdl:message name="deleteTokenRequest">
        <wsdl:part element="tns:deleteTokenRequest" name="deleteTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="deleteTokenResponse">
        <wsdl:part element="tns:deleteTokenResponse" name="deleteTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="deleteTokenFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="deleteTokenFault"/>
    </wsdl:message>
    <wsdl:message name="getAccountBalanceRequest">
        <wsdl:part element="tns:getAccountBalanceRequest" name="getAccountBalanceRequest"/>
    </wsdl:message>
    <wsdl:message name="getAccountBalanceResponse">
        <wsdl:part element="tns:getAccountBalanceResponse" name="getAccountBalanceResponse"/>
    </wsdl:message>
    <wsdl:message name="getAccountBalanceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getAccountBalanceFault"/>
    </wsdl:message>
    <wsdl:message name="getAccountOwnerNameRequest">
        <wsdl:part element="tns:getAccountOwnerNameRequest" name="getAccountOwnerNameRequest"/>
    </wsdl:message>
    <wsdl:message name="getAccountOwnerNameResponse">
        <wsdl:part element="tns:getAccountOwnerNameResponse" name="getAccountOwnerNameResponse"/>
    </wsdl:message>
    <wsdl:message name="getAccountOwnerNameFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getAccountOwnerNameFault"/>
    </wsdl:message>
    <wsdl:message name="getActualLimitsRequest">
        <wsdl:part element="tns:getActualLimitsRequest" name="getActualLimitsRequest"/>
    </wsdl:message>
    <wsdl:message name="getActualLimitsResponse">
        <wsdl:part element="tns:getActualLimitsResponse" name="getActualLimitsResponse"/>
    </wsdl:message>
    <wsdl:message name="getActualLimitsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getActualLimitsFault"/>
    </wsdl:message>
    <wsdl:message name="getAvailableValidityRequest">
        <wsdl:part element="tns:getAvailableValidityRequest" name="getAvailableValidityRequest"/>
    </wsdl:message>
    <wsdl:message name="getAvailableValidityResponse">
        <wsdl:part element="tns:getAvailableValidityResponse" name="getAvailableValidityResponse"/>
    </wsdl:message>
    <wsdl:message name="getAvailableValidityFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getAvailableValidityFault"/>
    </wsdl:message>
    <wsdl:message name="getCardsCountRequest">
        <wsdl:part element="tns:getCardsCountRequest" name="getCardsCountRequest"/>
    </wsdl:message>
    <wsdl:message name="getCardsCountResponse">
        <wsdl:part element="tns:getCardsCountResponse" name="getCardsCountResponse"/>
    </wsdl:message>
    <wsdl:message name="getCardsCountFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getCardsCountFault"/>
    </wsdl:message>
    <wsdl:message name="getCardDescRequest">
        <wsdl:part element="tns:getCardDescRequest" name="getCardDescRequest"/>
    </wsdl:message>
    <wsdl:message name="getCardDescResponse">
        <wsdl:part element="tns:getCardDescResponse" name="getCardDescResponse"/>
    </wsdl:message>
    <wsdl:message name="getCardDescFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getCardDescFault"/>
    </wsdl:message>
    <wsdl:message name="getCardDetailRequest">
        <wsdl:part element="tns:getCardDetailRequest" name="getCardDetailRequest"/>
    </wsdl:message>
    <wsdl:message name="getCardDetailResponse">
        <wsdl:part element="tns:getCardDetailResponse" name="getCardDetailResponse"/>
    </wsdl:message>
    <wsdl:message name="getCardDetailFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getCardDetailFault"/>
    </wsdl:message>
    <wsdl:message name="getCardTokenDetailRequest">
        <wsdl:part element="tns:getCardTokenDetailRequest" name="getCardTokenDetailRequest"/>
    </wsdl:message>
    <wsdl:message name="getCardTokenDetailResponse">
        <wsdl:part element="tns:getCardTokenDetailResponse" name="getCardTokenDetailResponse"/>
    </wsdl:message>
    <wsdl:message name="getCardTokenDetailFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getCardTokenDetailFault"/>
    </wsdl:message>
    <wsdl:message name="getCardTokenListRequest">
        <wsdl:part element="tns:getCardTokenListRequest" name="getCardTokenListRequest"/>
    </wsdl:message>
    <wsdl:message name="getCardTokenListResponse">
        <wsdl:part element="tns:getCardTokenListResponse" name="getCardTokenListResponse"/>
    </wsdl:message>
    <wsdl:message name="getCardTokenListFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getCardTokenListFault"/>
    </wsdl:message>
    <wsdl:message name="getCountriesRequest">
        <wsdl:part element="tns:getCountriesRequest" name="getCountriesRequest"/>
    </wsdl:message>
    <wsdl:message name="getCountriesResponse">
        <wsdl:part element="tns:getCountriesResponse" name="getCountriesResponse"/>
    </wsdl:message>
    <wsdl:message name="getCountriesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getCountriesFault"/>
    </wsdl:message>
    <wsdl:message name="getDigitalizationsRequest">
        <wsdl:part element="tns:getDigitalizationsRequest" name="getDigitalizationsRequest"/>
    </wsdl:message>
    <wsdl:message name="getDigitalizationsResponse">
        <wsdl:part element="tns:getDigitalizationsResponse" name="getDigitalizationsResponse"/>
    </wsdl:message>
    <wsdl:message name="getDigitalizationsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getDigitalizationsFault"/>
    </wsdl:message>
    <wsdl:message name="getLastPinByCardNumberRequest">
        <wsdl:part element="tns:getLastPinByCardNumberRequest" name="getLastPinByCardNumberRequest"/>
    </wsdl:message>
    <wsdl:message name="getLastPinByCardNumberResponse">
        <wsdl:part element="tns:getLastPinByCardNumberResponse" name="getLastPinByCardNumberResponse"/>
    </wsdl:message>
    <wsdl:message name="getLastPinByCardNumberFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getLastPinByCardNumberFault"/>
    </wsdl:message>
    <wsdl:message name="getLimitRegisterRequest">
        <wsdl:part element="tns:getLimitRegisterRequest" name="getLimitRegisterRequest"/>
    </wsdl:message>
    <wsdl:message name="getLimitRegisterResponse">
        <wsdl:part element="tns:getLimitRegisterResponse" name="getLimitRegisterResponse"/>
    </wsdl:message>
    <wsdl:message name="getLimitRegisterFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getLimitRegisterFault"/>
    </wsdl:message>
    <wsdl:message name="getLimitsHistory4CardRequest">
        <wsdl:part element="tns:getLimitsHistory4CardRequest" name="getLimitsHistory4CardRequest"/>
    </wsdl:message>
    <wsdl:message name="getLimitsHistory4CardResponse">
        <wsdl:part element="tns:getLimitsHistory4CardResponse" name="getLimitsHistory4CardResponse"/>
    </wsdl:message>
    <wsdl:message name="getLimitsHistory4CardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getLimitsHistory4CardFault"/>
    </wsdl:message>
    <wsdl:message name="isCardBlockedByTypeRequest">
        <wsdl:part element="tns:isCardBlockedByTypeRequest" name="isCardBlockedByTypeRequest"/>
    </wsdl:message>
    <wsdl:message name="isCardBlockedByTypeResponse">
        <wsdl:part element="tns:isCardBlockedByTypeResponse" name="isCardBlockedByTypeResponse"/>
    </wsdl:message>
    <wsdl:message name="isCardBlockedByTypeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="isCardBlockedByTypeFault"/>
    </wsdl:message>
    <wsdl:message name="makeChangeValidityRequest">
        <wsdl:part element="tns:makeChangeValidityRequest" name="makeChangeValidityRequest"/>
    </wsdl:message>
    <wsdl:message name="makeChangeValidityResponse">
        <wsdl:part element="tns:makeChangeValidityResponse" name="makeChangeValidityResponse"/>
    </wsdl:message>
    <wsdl:message name="makeChangeValidityFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="makeChangeValidityFault"/>
    </wsdl:message>
    <wsdl:message name="makeNewPinRequestRequest">
        <wsdl:part element="tns:makeNewPinRequestRequest" name="makeNewPinRequestRequest"/>
    </wsdl:message>
    <wsdl:message name="makeNewPinRequestResponse">
        <wsdl:part element="tns:makeNewPinRequestResponse" name="makeNewPinRequestResponse"/>
    </wsdl:message>
    <wsdl:message name="makeNewPinRequestFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="makeNewPinRequestFault"/>
    </wsdl:message>
    <wsdl:message name="makeSubstitutionCardRequest">
        <wsdl:part element="tns:makeSubstitutionCardRequest" name="makeSubstitutionCardRequest"/>
    </wsdl:message>
    <wsdl:message name="makeSubstitutionCardResponse">
        <wsdl:part element="tns:makeSubstitutionCardResponse" name="makeSubstitutionCardResponse"/>
    </wsdl:message>
    <wsdl:message name="makeSubstitutionCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="makeSubstitutionCardFault"/>
    </wsdl:message>
    <wsdl:message name="saveLimits4CardRequest">
        <wsdl:part element="tns:saveLimits4CardRequest" name="saveLimits4CardRequest"/>
    </wsdl:message>
    <wsdl:message name="saveLimits4CardResponse">
        <wsdl:part element="tns:saveLimits4CardResponse" name="saveLimits4CardResponse"/>
    </wsdl:message>
    <wsdl:message name="saveLimits4CardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="saveLimits4CardFault"/>
    </wsdl:message>
    <wsdl:message name="searchCardValidityRequest">
        <wsdl:part element="tns:searchCardValidityRequest" name="searchCardValidityRequest"/>
    </wsdl:message>
    <wsdl:message name="searchCardValidityResponse">
        <wsdl:part element="tns:searchCardValidityResponse" name="searchCardValidityResponse"/>
    </wsdl:message>
    <wsdl:message name="searchCardValidityFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchCardValidityFault"/>
    </wsdl:message>
    <wsdl:message name="searchCardsRequest">
        <wsdl:part element="tns:searchCardsRequest" name="searchCardsRequest"/>
    </wsdl:message>
    <wsdl:message name="searchCardsResponse">
        <wsdl:part element="tns:searchCardsResponse" name="searchCardsResponse"/>
    </wsdl:message>
    <wsdl:message name="searchCardsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchCardsFault"/>
    </wsdl:message>
    <wsdl:message name="searchHCardValidityRequest">
        <wsdl:part element="tns:searchHCardValidityRequest" name="searchHCardValidityRequest"/>
    </wsdl:message>
    <wsdl:message name="searchHCardValidityResponse">
        <wsdl:part element="tns:searchHCardValidityResponse" name="searchHCardValidityResponse"/>
    </wsdl:message>
    <wsdl:message name="searchHCardValidityFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchHCardValidityFault"/>
    </wsdl:message>
    <wsdl:message name="searchLimits4CardRequest">
        <wsdl:part element="tns:searchLimits4CardRequest" name="searchLimits4CardRequest"/>
    </wsdl:message>
    <wsdl:message name="searchLimits4CardResponse">
        <wsdl:part element="tns:searchLimits4CardResponse" name="searchLimits4CardResponse"/>
    </wsdl:message>
    <wsdl:message name="searchLimits4CardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchLimits4CardFault"/>
    </wsdl:message>
    <wsdl:message name="setCardNameRequest">
        <wsdl:part element="tns:setCardNameRequest" name="setCardNameRequest"/>
    </wsdl:message>
    <wsdl:message name="setCardNameResponse">
        <wsdl:part element="tns:setCardNameResponse" name="setCardNameResponse"/>
    </wsdl:message>
    <wsdl:message name="setCardNameFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="setCardNameFault"/>
    </wsdl:message>
    <wsdl:message name="setRenewalCardRequest">
        <wsdl:part element="tns:setRenewalCardRequest" name="setRenewalCardRequest"/>
    </wsdl:message>
    <wsdl:message name="setRenewalCardResponse">
        <wsdl:part element="tns:setRenewalCardResponse" name="setRenewalCardResponse"/>
    </wsdl:message>
    <wsdl:message name="setRenewalCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="setRenewalCardFault"/>
    </wsdl:message>
    <wsdl:message name="suspendTokenRequest">
        <wsdl:part element="tns:suspendTokenRequest" name="suspendTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="suspendTokenResponse">
        <wsdl:part element="tns:suspendTokenResponse" name="suspendTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="suspendTokenFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="suspendTokenFault"/>
    </wsdl:message>
    <wsdl:message name="tokenActivateRequest">
        <wsdl:part element="tns:tokenActivateRequest" name="tokenActivateRequest"/>
    </wsdl:message>
    <wsdl:message name="tokenActivateResponse">
        <wsdl:part element="tns:tokenActivateResponse" name="tokenActivateResponse"/>
    </wsdl:message>
    <wsdl:message name="tokenActivateFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="tokenActivateFault"/>
    </wsdl:message>
    <wsdl:message name="unsuspendTokenRequest">
        <wsdl:part element="tns:unsuspendTokenRequest" name="unsuspendTokenRequest"/>
    </wsdl:message>
    <wsdl:message name="unsuspendTokenResponse">
        <wsdl:part element="tns:unsuspendTokenResponse" name="unsuspendTokenResponse"/>
    </wsdl:message>
    <wsdl:message name="unsuspendTokenFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="unsuspendTokenFault"/>
    </wsdl:message>
    <wsdl:message name="updateCardRequest">
        <wsdl:part element="tns:updateCardRequest" name="updateCardRequest"/>
    </wsdl:message>
    <wsdl:message name="updateCardResponse">
        <wsdl:part element="tns:updateCardResponse" name="updateCardResponse"/>
    </wsdl:message>
    <wsdl:message name="updateCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="updateCardFault"/>
    </wsdl:message>

    <wsdl:portType name="PaymentCardWS">
        <wsdl:operation name="searchLimits4Card">
            <wsdl:input message="tns:searchLimits4CardRequest" name="searchLimits4CardRequest"/>
            <wsdl:output message="tns:searchLimits4CardResponse" name="searchLimits4CardResponse"/>
            <wsdl:fault message="tns:searchLimits4CardFault" name="searchLimits4CardFault"/>
        </wsdl:operation>
        <wsdl:operation name="getCardsCount">
            <wsdl:input message="tns:getCardsCountRequest" name="getCardsCountRequest"/>
            <wsdl:output message="tns:getCardsCountResponse" name="getCardsCountResponse"/>
            <wsdl:fault message="tns:getCardsCountFault" name="getCardsCountFault"/>
        </wsdl:operation>
        <wsdl:operation name="changeDesign4Card">
            <wsdl:input message="tns:changeDesign4CardRequest" name="changeDesign4CardRequest"/>
            <wsdl:output message="tns:changeDesign4CardResponse" name="changeDesign4CardResponse"/>
            <wsdl:fault message="tns:changeDesign4CardFault" name="changeDesign4CardFault"/>
        </wsdl:operation>
        <wsdl:operation name="getAvailableValidity">
            <wsdl:input message="tns:getAvailableValidityRequest" name="getAvailableValidityRequest"/>
            <wsdl:output message="tns:getAvailableValidityResponse" name="getAvailableValidityResponse"/>
            <wsdl:fault message="tns:getAvailableValidityFault" name="getAvailableValidityFault"/>
        </wsdl:operation>
        <wsdl:operation name="getAccountBalance">
            <wsdl:input message="tns:getAccountBalanceRequest" name="getAccountBalanceRequest"/>
            <wsdl:output message="tns:getAccountBalanceResponse" name="getAccountBalanceResponse"/>
            <wsdl:fault message="tns:getAccountBalanceFault" name="getAccountBalanceFault"/>
        </wsdl:operation>
        <wsdl:operation name="setRenewalCard">
            <wsdl:input message="tns:setRenewalCardRequest" name="setRenewalCardRequest"/>
            <wsdl:output message="tns:setRenewalCardResponse" name="setRenewalCardResponse"/>
            <wsdl:fault message="tns:setRenewalCardFault" name="setRenewalCardFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchHCardValidity">
            <wsdl:input message="tns:searchHCardValidityRequest" name="searchHCardValidityRequest"/>
            <wsdl:output message="tns:searchHCardValidityResponse" name="searchHCardValidityResponse"/>
            <wsdl:fault message="tns:searchHCardValidityFault" name="searchHCardValidityFault"/>
        </wsdl:operation>
        <wsdl:operation name="getCardDetail">
            <wsdl:input message="tns:getCardDetailRequest" name="getCardDetailRequest"/>
            <wsdl:output message="tns:getCardDetailResponse" name="getCardDetailResponse"/>
            <wsdl:fault message="tns:getCardDetailFault" name="getCardDetailFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchCardValidity">
            <wsdl:input message="tns:searchCardValidityRequest" name="searchCardValidityRequest"/>
            <wsdl:output message="tns:searchCardValidityResponse" name="searchCardValidityResponse"/>
            <wsdl:fault message="tns:searchCardValidityFault" name="searchCardValidityFault"/>
        </wsdl:operation>
        <wsdl:operation name="getActualLimits">
            <wsdl:input message="tns:getActualLimitsRequest" name="getActualLimitsRequest"/>
            <wsdl:output message="tns:getActualLimitsResponse" name="getActualLimitsResponse"/>
            <wsdl:fault message="tns:getActualLimitsFault" name="getActualLimitsFault"/>
        </wsdl:operation>
        <wsdl:operation name="makeNewPinRequest">
            <wsdl:input message="tns:makeNewPinRequestRequest" name="makeNewPinRequestRequest"/>
            <wsdl:output message="tns:makeNewPinRequestResponse" name="makeNewPinRequestResponse"/>
            <wsdl:fault message="tns:makeNewPinRequestFault" name="makeNewPinRequestFault"/>
        </wsdl:operation>
        <wsdl:operation name="setCardName">
            <wsdl:input message="tns:setCardNameRequest" name="setCardNameRequest"/>
            <wsdl:output message="tns:setCardNameResponse" name="setCardNameResponse"/>
            <wsdl:fault message="tns:setCardNameFault" name="setCardNameFault"/>
        </wsdl:operation>
        <wsdl:operation name="getAccountOwnerName">
            <wsdl:input message="tns:getAccountOwnerNameRequest" name="getAccountOwnerNameRequest"/>
            <wsdl:output message="tns:getAccountOwnerNameResponse" name="getAccountOwnerNameResponse"/>
            <wsdl:fault message="tns:getAccountOwnerNameFault" name="getAccountOwnerNameFault"/>
        </wsdl:operation>
        <wsdl:operation name="getLastPinByCardNumber">
            <wsdl:input message="tns:getLastPinByCardNumberRequest" name="getLastPinByCardNumberRequest"/>
            <wsdl:output message="tns:getLastPinByCardNumberResponse" name="getLastPinByCardNumberResponse"/>
            <wsdl:fault message="tns:getLastPinByCardNumberFault" name="getLastPinByCardNumberFault"/>
        </wsdl:operation>
        <wsdl:operation name="saveLimits4Card">
            <wsdl:input message="tns:saveLimits4CardRequest" name="saveLimits4CardRequest"/>
            <wsdl:output message="tns:saveLimits4CardResponse" name="saveLimits4CardResponse"/>
            <wsdl:fault message="tns:saveLimits4CardFault" name="saveLimits4CardFault"/>
        </wsdl:operation>
        <wsdl:operation name="getLimitsHistory4Card">
            <wsdl:input message="tns:getLimitsHistory4CardRequest" name="getLimitsHistory4CardRequest"/>
            <wsdl:output message="tns:getLimitsHistory4CardResponse" name="getLimitsHistory4CardResponse"/>
            <wsdl:fault message="tns:getLimitsHistory4CardFault" name="getLimitsHistory4CardFault"/>
        </wsdl:operation>
        <wsdl:operation name="getLimitRegister">
            <wsdl:input message="tns:getLimitRegisterRequest" name="getLimitRegisterRequest"/>
            <wsdl:output message="tns:getLimitRegisterResponse" name="getLimitRegisterResponse"/>
            <wsdl:fault message="tns:getLimitRegisterFault" name="getLimitRegisterFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchCards">
            <wsdl:input message="tns:searchCardsRequest" name="searchCardsRequest"/>
            <wsdl:output message="tns:searchCardsResponse" name="searchCardsResponse"/>
            <wsdl:fault message="tns:searchCardsFault" name="searchCardsFault"/>
        </wsdl:operation>
        <wsdl:operation name="getCardDesc">
            <wsdl:input message="tns:getCardDescRequest" name="getCardDescRequest"/>
            <wsdl:output message="tns:getCardDescResponse" name="getCardDescResponse"/>
            <wsdl:fault message="tns:getCardDescFault" name="getCardDescFault"/>
        </wsdl:operation>
        <wsdl:operation name="makeSubstitutionCard">
            <wsdl:input message="tns:makeSubstitutionCardRequest" name="makeSubstitutionCardRequest"/>
            <wsdl:output message="tns:makeSubstitutionCardResponse" name="makeSubstitutionCardResponse"/>
            <wsdl:fault message="tns:makeSubstitutionCardFault" name="makeSubstitutionCardFault"/>
        </wsdl:operation>
        <wsdl:operation name="activateCard">
            <wsdl:input message="tns:activateCardRequest" name="activateCardRequest"/>
            <wsdl:output message="tns:activateCardResponse" name="activateCardResponse"/>
            <wsdl:fault message="tns:activateCardFault" name="activateCardFault"/>
        </wsdl:operation>
        <wsdl:operation name="makeChangeValidity">
            <wsdl:input message="tns:makeChangeValidityRequest" name="makeChangeValidityRequest"/>
            <wsdl:output message="tns:makeChangeValidityResponse" name="makeChangeValidityResponse"/>
            <wsdl:fault message="tns:makeChangeValidityFault" name="makeChangeValidityFault"/>
        </wsdl:operation>
        <wsdl:operation name="getCountries">
            <wsdl:input message="tns:getCountriesRequest" name="getCountriesRequest"/>
            <wsdl:output message="tns:getCountriesResponse" name="getCountriesResponse"/>
            <wsdl:fault message="tns:getCountriesFault" name="getCountriesFault"/>
        </wsdl:operation>
        <wsdl:operation name="updateCard">
            <wsdl:input message="tns:updateCardRequest" name="updateCardRequest"/>
            <wsdl:output message="tns:updateCardResponse" name="updateCardResponse"/>
            <wsdl:fault message="tns:updateCardFault" name="updateCardFault"/>
        </wsdl:operation>
        <wsdl:operation name="isCardBlockedByType">
            <wsdl:input message="tns:isCardBlockedByTypeRequest" name="isCardBlockedByTypeRequest"/>
            <wsdl:output message="tns:isCardBlockedByTypeResponse" name="isCardBlockedByTypeResponse"/>
            <wsdl:fault message="tns:isCardBlockedByTypeFault" name="isCardBlockedByTypeFault"/>
        </wsdl:operation>
        <wsdl:operation name="addCardDesc">
            <wsdl:input message="tns:addCardDescRequest" name="addCardDescRequest"/>
            <wsdl:output message="tns:addCardDescResponse" name="addCardDescResponse"/>
            <wsdl:fault message="tns:addCardDescFault" name="addCardDescFault"/>
        </wsdl:operation>
        <wsdl:operation name="getCardTokenDetail">
            <wsdl:input message="tns:getCardTokenDetailRequest" name="getCardTokenDetailRequest"/>
            <wsdl:output message="tns:getCardTokenDetailResponse" name="getCardTokenDetailResponse"/>
            <wsdl:fault message="tns:getCardTokenDetailFault" name="getCardTokenDetailFault"/>
        </wsdl:operation>
        <wsdl:operation name="getCardTokenList">
            <wsdl:input message="tns:getCardTokenListRequest" name="getCardTokenListRequest"/>
            <wsdl:output message="tns:getCardTokenListResponse" name="getCardTokenListResponse"/>
            <wsdl:fault message="tns:getCardTokenListFault" name="getCardTokenListFault"/>
        </wsdl:operation>
        <wsdl:operation name="getDigitalizations">
            <wsdl:input message="tns:getDigitalizationsRequest" name="getDigitalizationsRequest"/>
            <wsdl:output message="tns:getDigitalizationsResponse" name="getDigitalizationsResponse"/>
            <wsdl:fault message="tns:getDigitalizationsFault" name="getDigitalizationsFault"/>
        </wsdl:operation>
        <wsdl:operation name="deleteToken">
            <wsdl:input message="tns:deleteTokenRequest" name="deleteTokenRequest"/>
            <wsdl:output message="tns:deleteTokenResponse" name="deleteTokenResponse"/>
            <wsdl:fault message="tns:deleteTokenFault" name="deleteTokenFault"/>
        </wsdl:operation>
        <wsdl:operation name="suspendToken">
            <wsdl:input message="tns:suspendTokenRequest" name="suspendTokenRequest"/>
            <wsdl:output message="tns:suspendTokenResponse" name="suspendTokenResponse"/>
            <wsdl:fault message="tns:suspendTokenFault" name="suspendTokenFault"/>
        </wsdl:operation>
        <wsdl:operation name="unsuspendToken">
            <wsdl:input message="tns:unsuspendTokenRequest" name="unsuspendTokenRequest"/>
            <wsdl:output message="tns:unsuspendTokenResponse" name="unsuspendTokenResponse"/>
            <wsdl:fault message="tns:unsuspendTokenFault" name="unsuspendTokenFault"/>
        </wsdl:operation>
        <wsdl:operation name="tokenActivate">
            <wsdl:input message="tns:tokenActivateRequest" name="tokenActivateRequest"/>
            <wsdl:output message="tns:tokenActivateResponse" name="tokenActivateResponse"/>
            <wsdl:fault message="tns:tokenActivateFault" name="tokenActivateFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="PaymentCardWSSoap11" type="tns:PaymentCardWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="searchLimits4Card">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchLimits4CardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchLimits4CardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchLimits4CardFault">
                <soap:fault name="searchLimits4CardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCardsCount">
            <soap:operation soapAction=""/>
            <wsdl:input name="getCardsCountRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCardsCountResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getCardsCountFault">
                <soap:fault name="getCardsCountFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="changeDesign4Card">
            <soap:operation soapAction=""/>
            <wsdl:input name="changeDesign4CardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="changeDesign4CardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="changeDesign4CardFault">
                <soap:fault name="changeDesign4CardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAvailableValidity">
            <soap:operation soapAction=""/>
            <wsdl:input name="getAvailableValidityRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getAvailableValidityResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getAvailableValidityFault">
                <soap:fault name="getAvailableValidityFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAccountBalance">
            <soap:operation soapAction=""/>
            <wsdl:input name="getAccountBalanceRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getAccountBalanceResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getAccountBalanceFault">
                <soap:fault name="getAccountBalanceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setRenewalCard">
            <soap:operation soapAction=""/>
            <wsdl:input name="setRenewalCardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="setRenewalCardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="setRenewalCardFault">
                <soap:fault name="setRenewalCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchHCardValidity">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchHCardValidityRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchHCardValidityResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchHCardValidityFault">
                <soap:fault name="searchHCardValidityFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCardDetail">
            <soap:operation soapAction=""/>
            <wsdl:input name="getCardDetailRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCardDetailResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getCardDetailFault">
                <soap:fault name="getCardDetailFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchCardValidity">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchCardValidityRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchCardValidityResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchCardValidityFault">
                <soap:fault name="searchCardValidityFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getActualLimits">
            <soap:operation soapAction=""/>
            <wsdl:input name="getActualLimitsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getActualLimitsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getActualLimitsFault">
                <soap:fault name="getActualLimitsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="makeNewPinRequest">
            <soap:operation soapAction=""/>
            <wsdl:input name="makeNewPinRequestRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="makeNewPinRequestResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="makeNewPinRequestFault">
                <soap:fault name="makeNewPinRequestFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="setCardName">
            <soap:operation soapAction=""/>
            <wsdl:input name="setCardNameRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="setCardNameResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="setCardNameFault">
                <soap:fault name="setCardNameFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAccountOwnerName">
            <soap:operation soapAction=""/>
            <wsdl:input name="getAccountOwnerNameRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getAccountOwnerNameResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getAccountOwnerNameFault">
                <soap:fault name="getAccountOwnerNameFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getLastPinByCardNumber">
            <soap:operation soapAction=""/>
            <wsdl:input name="getLastPinByCardNumberRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getLastPinByCardNumberResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getLastPinByCardNumberFault">
                <soap:fault name="getLastPinByCardNumberFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="saveLimits4Card">
            <soap:operation soapAction=""/>
            <wsdl:input name="saveLimits4CardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="saveLimits4CardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="saveLimits4CardFault">
                <soap:fault name="saveLimits4CardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getLimitsHistory4Card">
            <soap:operation soapAction=""/>
            <wsdl:input name="getLimitsHistory4CardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getLimitsHistory4CardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getLimitsHistory4CardFault">
                <soap:fault name="getLimitsHistory4CardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getLimitRegister">
            <soap:operation soapAction=""/>
            <wsdl:input name="getLimitRegisterRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getLimitRegisterResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getLimitRegisterFault">
                <soap:fault name="getLimitRegisterFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchCards">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchCardsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchCardsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchCardsFault">
                <soap:fault name="searchCardsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCardDesc">
            <soap:operation soapAction=""/>
            <wsdl:input name="getCardDescRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCardDescResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getCardDescFault">
                <soap:fault name="getCardDescFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="makeSubstitutionCard">
            <soap:operation soapAction=""/>
            <wsdl:input name="makeSubstitutionCardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="makeSubstitutionCardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="makeSubstitutionCardFault">
                <soap:fault name="makeSubstitutionCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="activateCard">
            <soap:operation soapAction=""/>
            <wsdl:input name="activateCardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="activateCardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="activateCardFault">
                <soap:fault name="activateCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="makeChangeValidity">
            <soap:operation soapAction=""/>
            <wsdl:input name="makeChangeValidityRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="makeChangeValidityResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="makeChangeValidityFault">
                <soap:fault name="makeChangeValidityFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountries">
            <soap:operation soapAction=""/>
            <wsdl:input name="getCountriesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCountriesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getCountriesFault">
                <soap:fault name="getCountriesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="updateCard">
            <soap:operation soapAction=""/>
            <wsdl:input name="updateCardRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="updateCardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="updateCardFault">
                <soap:fault name="updateCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="isCardBlockedByType">
            <soap:operation soapAction=""/>
            <wsdl:input name="isCardBlockedByTypeRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="isCardBlockedByTypeResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="isCardBlockedByTypeFault">
                <soap:fault name="isCardBlockedByTypeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="addCardDesc">
            <soap:operation soapAction=""/>
            <wsdl:input name="addCardDescRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="addCardDescResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="addCardDescFault">
                <soap:fault name="addCardDescFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCardTokenDetail">
            <soap:operation soapAction=""/>
            <wsdl:input name="getCardTokenDetailRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCardTokenDetailResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getCardTokenDetailFault">
                <soap:fault name="getCardTokenDetailFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCardTokenList">
            <soap:operation soapAction=""/>
            <wsdl:input name="getCardTokenListRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCardTokenListResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getCardTokenListFault">
                <soap:fault name="getCardTokenListFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getDigitalizations">
            <soap:operation soapAction=""/>
            <wsdl:input name="getDigitalizationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getDigitalizationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getDigitalizationsFault">
                <soap:fault name="getDigitalizationsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="deleteToken">
            <soap:operation soapAction=""/>
            <wsdl:input name="deleteTokenRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="deleteTokenResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="deleteTokenFault">
                <soap:fault name="deleteTokenFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="suspendToken">
            <soap:operation soapAction=""/>
            <wsdl:input name="suspendTokenRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="suspendTokenResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="suspendTokenFault">
                <soap:fault name="suspendTokenFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="unsuspendToken">
            <soap:operation soapAction=""/>
            <wsdl:input name="unsuspendTokenRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="unsuspendTokenResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="unsuspendTokenFault">
                <soap:fault name="unsuspendTokenFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="tokenActivate">
            <soap:operation soapAction=""/>
            <wsdl:input name="tokenActivateRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="tokenActivateResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="tokenActivateFault">
                <soap:fault name="tokenActivateFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="PaymentCardWSService">
        <wsdl:port binding="tns:PaymentCardWSSoap11" name="PaymentCardWSSoap11">
            <soap:address location="/ws/card/PaymentCardWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
