<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://cms.airbank.cz/ws/card/complaint"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://cms.airbank.cz/ws/card/complaint">

    <xs:annotation>
        <xs:documentation>ComplaintWS - The endpoint for card payment complaint management
        </xs:documentation>
    </xs:annotation>

    <wsdl:types>
        <xs:schema targetNamespace="http://cms.airbank.cz/ws/card/complaint">
            <xs:include schemaLocation="CardPaymentComplaint.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="searchComplaintCardPaymentsRequestType">
        <wsdl:part element="tns:searchComplaintCardPaymentsRequestType" name="searchComplaintCardPaymentsRequestType"/>
    </wsdl:message>

    <wsdl:message name="searchComplaintCardPaymentsResponseType">
        <wsdl:part element="tns:searchComplaintCardPaymentsResponseType" name="searchComplaintCardPaymentsResponseType"/>
    </wsdl:message>

    <wsdl:message name="searchComplaintCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchComplaintCardPaymentsFault"/>
    </wsdl:message>

    <wsdl:message name="getComplaintDetailRequestType">
        <wsdl:part element="tns:getComplaintDetailRequestType" name="getComplaintDetailRequestType"/>
    </wsdl:message>

    <wsdl:message name="getComplaintDetailResponseType">
        <wsdl:part element="tns:getComplaintDetailResponseType" name="getComplaintDetailResponseType"/>
    </wsdl:message>

    <wsdl:message name="getComplaintDetailFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getComplaintDetailFault"/>
    </wsdl:message>

    <wsdl:message name="searchCardPaymentsRequest">
        <wsdl:part element="tns:searchCardPaymentsRequest" name="searchCardPaymentsRequest"/>
    </wsdl:message>

    <wsdl:message name="searchCardPaymentsResponse">
        <wsdl:part element="tns:searchCardPaymentsResponse" name="searchCardPaymentsResponse"/>
    </wsdl:message>

    <wsdl:message name="searchCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchCardPaymentsFault"/>
    </wsdl:message>

    <wsdl:message name="searchComplaintsRequestType">
        <wsdl:part element="tns:searchComplaintsRequestType" name="searchComplaintsRequestType"/>
    </wsdl:message>

    <wsdl:message name="searchComplaintsResponseType">
        <wsdl:part element="tns:searchComplaintsResponseType" name="searchComplaintsResponseType"/>
    </wsdl:message>

    <wsdl:message name="searchComplaintsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchComplaintsFault"/>
    </wsdl:message>

    <wsdl:message name="createComplaintRequestType">
        <wsdl:part element="tns:createComplaintRequestType" name="createComplaintRequestType"/>
    </wsdl:message>

    <wsdl:message name="createComplaintResponseType">
        <wsdl:part element="tns:createComplaintResponseType" name="createComplaintResponseType"/>
    </wsdl:message>

    <wsdl:message name="createComplaintFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="createComplaintFault"/>
    </wsdl:message>

    <wsdl:message name="addCardPaymentsRequestType">
        <wsdl:part element="tns:addCardPaymentsRequestType" name="addCardPaymentsRequestType"/>
    </wsdl:message>

    <wsdl:message name="addCardPaymentsResponseType">
        <wsdl:part element="tns:addCardPaymentsResponseType" name="addCardPaymentsResponseType"/>
    </wsdl:message>

    <wsdl:message name="addCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="addCardPaymentsFault"/>
    </wsdl:message>

    <wsdl:message name="removeCardPaymentsRequestType">
        <wsdl:part element="tns:removeCardPaymentsRequestType" name="removeCardPaymentsRequestType"/>
    </wsdl:message>

    <wsdl:message name="removeCardPaymentsResponseType">
        <wsdl:part element="tns:removeCardPaymentsResponseType" name="removeCardPaymentsResponseType"/>
    </wsdl:message>

    <wsdl:message name="removeCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="removeCardPaymentsFault"/>
    </wsdl:message>

    <wsdl:message name="openClaimingCardPaymentsRequestType">
        <wsdl:part element="tns:openClaimingCardPaymentsRequestType" name="openClaimingCardPaymentsRequestType"/>
    </wsdl:message>

    <wsdl:message name="openClaimingCardPaymentsResponseType">
        <wsdl:part element="tns:openClaimingCardPaymentsResponseType" name="openClaimingCardPaymentsResponseType"/>
    </wsdl:message>

    <wsdl:message name="openClaimingCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="openClaimingCardPaymentsFault"/>
    </wsdl:message>

    <wsdl:message name="refuseClaimingCardPaymentsRequestType">
        <wsdl:part element="tns:refuseClaimingCardPaymentsRequestType" name="refuseClaimingCardPaymentsRequestType"/>
    </wsdl:message>

    <wsdl:message name="refuseClaimingCardPaymentsResponseType">
        <wsdl:part element="tns:refuseClaimingCardPaymentsResponseType" name="refuseClaimingCardPaymentsResponseType"/>
    </wsdl:message>

    <wsdl:message name="refuseClaimingCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="refuseClaimingCardPaymentsFault"/>
    </wsdl:message>

    <wsdl:portType name="ComplaintWS">
        <wsdl:operation name="searchComplaintCardPayments">
            <wsdl:input message="tns:searchComplaintCardPaymentsRequestType" name="searchComplaintCardPaymentsRequestType"/>
            <wsdl:output message="tns:searchComplaintCardPaymentsResponseType" name="searchComplaintCardPaymentsResponseType"/>
            <wsdl:fault message="tns:searchComplaintCardPaymentsFault" name="searchComplaintCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="getComplaintDetail">
            <wsdl:input message="tns:getComplaintDetailRequestType" name="getComplaintDetailRequestType"/>
            <wsdl:output message="tns:getComplaintDetailResponseType" name="getComplaintDetailResponseType"/>
            <wsdl:fault message="tns:getComplaintDetailFault" name="getComplaintDetailFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchCardPayments">
            <wsdl:input message="tns:searchCardPaymentsRequest" name="searchCardPaymentsRequest"/>
            <wsdl:output message="tns:searchCardPaymentsResponse" name="searchCardPaymentsResponse"/>
            <wsdl:fault message="tns:searchCardPaymentsFault" name="searchCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchComplaints">
            <wsdl:input message="tns:searchComplaintsRequestType" name="searchComplaintsRequestType"/>
            <wsdl:output message="tns:searchComplaintsResponseType" name="searchComplaintsResponseType"/>
            <wsdl:fault message="tns:searchComplaintsFault" name="searchComplaintsFault"/>
        </wsdl:operation>
        <wsdl:operation name="createComplaint">
            <wsdl:input message="tns:createComplaintRequestType" name="createComplaintRequestType"/>
            <wsdl:output message="tns:createComplaintResponseType" name="createComplaintResponseType"/>
            <wsdl:fault message="tns:createComplaintFault" name="createComplaintFault"/>
        </wsdl:operation>
        <wsdl:operation name="addCardPayments">
            <wsdl:input message="tns:addCardPaymentsRequestType" name="addCardPaymentsRequestType"/>
            <wsdl:output message="tns:addCardPaymentsResponseType" name="addCardPaymentsResponseType"/>
            <wsdl:fault message="tns:addCardPaymentsFault" name="addCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="removeCardPayments">
            <wsdl:input message="tns:removeCardPaymentsRequestType" name="removeCardPaymentsRequestType"/>
            <wsdl:output message="tns:removeCardPaymentsResponseType" name="removeCardPaymentsResponseType"/>
            <wsdl:fault message="tns:removeCardPaymentsFault" name="removeCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="openClaimingCardPayments">
            <wsdl:input message="tns:openClaimingCardPaymentsRequestType" name="openClaimingCardPaymentsRequestType"/>
            <wsdl:output message="tns:openClaimingCardPaymentsResponseType" name="openClaimingCardPaymentsResponseType"/>
            <wsdl:fault message="tns:openClaimingCardPaymentsFault" name="openClaimingCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="refuseClaimingCardPayments">
            <wsdl:input message="tns:refuseClaimingCardPaymentsRequestType" name="refuseClaimingCardPaymentsRequestType"/>
            <wsdl:output message="tns:refuseClaimingCardPaymentsResponseType" name="refuseClaimingCardPaymentsResponseType"/>
            <wsdl:fault message="tns:refuseClaimingCardPaymentsFault" name="refuseClaimingCardPaymentsFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ComplaintWSSoap11" type="tns:ComplaintWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="searchComplaintCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchComplaintCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchComplaintCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchComplaintCardPaymentsFault">
                <soap:fault name="searchComplaintCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getComplaintDetail">
            <soap:operation soapAction=""/>
            <wsdl:input name="getComplaintDetailRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getComplaintDetailResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getComplaintDetailFault">
                <soap:fault name="getComplaintDetailFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchCardPaymentsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchCardPaymentsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchCardPaymentsFault">
                <soap:fault name="searchCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchComplaints">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchComplaintsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchComplaintsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchComplaintsFault">
                <soap:fault name="searchComplaintsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="createComplaint">
            <soap:operation soapAction=""/>
            <wsdl:input name="createComplaintRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="createComplaintResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="createComplaintFault">
                <soap:fault name="createComplaintFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="addCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="addCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="addCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="addCardPaymentsFault">
                <soap:fault name="addCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="removeCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="removeCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="removeCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="removeCardPaymentsFault">
                <soap:fault name="removeCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="openClaimingCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="openClaimingCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="openClaimingCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="openClaimingCardPaymentsFault">
                <soap:fault name="openClaimingCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="refuseClaimingCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="refuseClaimingCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="refuseClaimingCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="refuseClaimingCardPaymentsFault">
                <soap:fault name="refuseClaimingCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ComplaintWSService">
        <wsdl:port binding="tns:ComplaintWSSoap11" name="ComplaintWSSoap11">
            <soap:address location="/ws/card/ComplaintWS"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>