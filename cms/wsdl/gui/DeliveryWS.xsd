<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://cms.airbank.cz/ws/envelope/delivery"
           xmlns="http://cms.airbank.cz/ws/envelope/delivery"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns:adr="http://homecredit.net/ws/card/address">

    <xs:import namespace="http://homecredit.net/ws/card/address" schemaLocation="../common/AddressDTO.xsd"/>

    <xs:element name="GetReturnedEnvelopesRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="batchDate" type="xs:date" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetReturnedEnvelopesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="returnedEnvelopes" type="ReturnedEnvelope" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ReturnedEnvelope">
        <xs:sequence>
            <xs:element name="eanCode" type="xs:string" />
            <xs:element name="personName" type="xs:string" />
            <xs:element name="deliveryAddress" type="xs:string" />
            <xs:element name="daySequenceNumber" type="xs:int" />
            <xs:element name="scanDate" type="xs:dateTime" />
        </xs:sequence>
    </xs:complexType>

    <xs:element name="WriteReturnedEnvelopeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="batchDate" type="xs:date" />
                <xs:element name="eanCode" type="xs:string" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="WriteReturnedEnvelopeResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="xs:string" />
                <xs:element name="returnedEnvelope" type="ReturnedEnvelope" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetRedistributionEnvelopesRequest">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:element name="GetRedistributionEnvelopesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="redistributionEnvelope" type="RedistributionEnvelope" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="RedistributionEnvelope">
        <xs:sequence>
            <xs:element name="returnedEnvelope" type="ReturnedEnvelope" />
            <xs:element name="redistributionAddress" type="adr:DeliveryAddress" />
        </xs:sequence>
    </xs:complexType>


    <xs:element name="ConfirmRedistributionRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="redistributionAddressId" type="xs:long"  maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:element name="ConfirmRedistributionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="xs:string" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="validatePurposeAddressRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="purposeAddress" type="CmsPurposeAddress" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="validatePurposeAddressResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="validationResult" type="ValidationResult" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ValidationResult">
        <xs:sequence>
            <xs:element name="validationType" type="xs:string"/>
            <xs:element name="attributes" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="resultDetail" type="xs:string" minOccurs="0"/>
            <xs:element name="validationCode" type="xs:string" minOccurs="0"/>
            <xs:element name="values" type="Values" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Values">
        <xs:sequence>
            <xs:element name="key" type="xs:string" />
            <xs:element name="values" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CmsPurposeAddress">
        <xs:sequence>
            <xs:element name="addressLine1" type="xs:string"/>
            <xs:element name="addressLine2" type="xs:string" minOccurs="0"/>
            <xs:element name="town" type="xs:string" />
            <xs:element name="zip" type="xs:string" minOccurs="0"/>
            <xs:element name="countryAlpha2Code" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
