<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
		   attributeFormDefault="unqualified" elementFormDefault="qualified"
		   targetNamespace="http://homecredit.net/ws/card/message" xmlns="http://homecredit.net/ws/card/message">

	<!-- Requests and Responses ************************************************************-->

	<xs:element name="SearchEvents4ServiceRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="filter" type="Events4ServiceFilterDto"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="SearchEvents4ServiceResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="result" type="MessageServiceEventDto" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GetMessageTemplateDetailRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="filter" type="MessageTemplateFilterDto"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GetMessageTemplateDetailResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="result" type="MessageTemplateDetailDto" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="StoreMessageTemplateDetailRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="messageTemplate" type="MessageTemplateDetailDto"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="StoreMessageTemplateDetailResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="messageTemplate" type="MessageTemplateDetailDto"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GetPlaceholdersRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="idServiceEvent" type="xs:long"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GetPlaceholdersResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="result" type="PlaceholderDto" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GetInflectedVerbListRequest">
		<xs:complexType>
			<xs:sequence>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GetInflectedVerbListResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="result" type="InflectedVerbDto" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<!-- Complex types **********************************************************************-->

	<xs:complexType name="Events4ServiceFilterDto">
		<xs:sequence>
			<xs:element name="serviceCode" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="MessageServiceEventDto">
		<xs:sequence>
			<xs:element name="id" type="xs:long"/>
			<xs:element name="code" type="xs:string"/>
			<xs:element name="name" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="MessageTemplateFilterDto">
		<xs:sequence>
			<xs:element name="serviceCode" type="xs:string"/>
			<xs:element name="idServiceEvent" type="xs:long"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="MessageTemplateDetailDto">
		<xs:sequence>
			<xs:element name="id" type="xs:long" nillable="true"/>
			<xs:element name="serviceCode" type="xs:string"/>
			<xs:element name="serviceEventCode" type="xs:string"/>
			<xs:element name="serviceEventName" type="xs:string"/>
			<xs:element name="priority" type="xs:string"/>
			<xs:element name="messageType" type="xs:string"/>
			<xs:element name="text" type="xs:string"/>
			<xs:element name="bodyText" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="PlaceholderDto">
		<xs:sequence>
			<xs:element name="code" type="xs:string"/>
			<xs:element name="description" type="xs:string"/>
			<xs:element name="length" type="xs:int"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="InflectedVerbDto">
		<xs:sequence>
			<xs:element name="inflectCode" type="xs:string"/>
			<xs:element name="inflectValueDescription" type="xs:string"/>
			<xs:element name="inflectLength" type="xs:int"/>
		</xs:sequence>
	</xs:complexType>

	<!-- Simple types **********************************************************************-->
</xs:schema>
