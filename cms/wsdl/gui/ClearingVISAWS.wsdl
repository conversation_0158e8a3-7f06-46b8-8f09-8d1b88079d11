<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/ws/clearing/clearingvisa"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://homecredit.net/ws/clearing/clearingvisa">
    <wsdl:types>
        <xs:schema targetNamespace="http://homecredit.net/ws/clearing/clearingvisa">
            <xs:include schemaLocation="ClearingVISA.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>
    <wsdl:message name="getChargebackVISAResponse">
        <wsdl:part element="tns:getChargebackVISAResponse" name="getChargebackVISAResponse"/>
    </wsdl:message>
    <wsdl:message name="getChargebackVISAFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getChargebackVISAFault"/>
    </wsdl:message>
    <wsdl:message name="getOutgoingRepresentmentVISARequest">
        <wsdl:part element="tns:getOutgoingRepresentmentVISARequest" name="getOutgoingRepresentmentVISARequest"/>
    </wsdl:message>
    <wsdl:message name="getOutgoingRepresentmentVISAResponse">
        <wsdl:part element="tns:getOutgoingRepresentmentVISAResponse" name="getOutgoingRepresentmentVISAResponse"/>
    </wsdl:message>
    <wsdl:message name="getOutgoingRepresentmentVISAFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getOutgoingRepresentmentVISAFault"/>
    </wsdl:message>
    <wsdl:message name="getVisaFeeCollectionRequest">
        <wsdl:part element="tns:getVisaFeeCollectionRequest" name="getVisaFeeCollectionRequest"/>
    </wsdl:message>
    <wsdl:message name="getVisaFeeCollectionResponse">
        <wsdl:part element="tns:getVisaFeeCollectionResponse" name="getVisaFeeCollectionResponse"/>
    </wsdl:message>
    <wsdl:message name="getVisaFeeCollectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getVisaFeeCollectionFault"/>
    </wsdl:message>
    <wsdl:message name="getChargebackVisaCountRequest">
        <wsdl:part element="tns:getChargebackVisaCountRequest" name="getChargebackVisaCountRequest"/>
    </wsdl:message>
    <wsdl:message name="getChargebackVisaCountResponse">
        <wsdl:part element="tns:getChargebackVisaCountResponse" name="getChargebackVisaCountResponse"/>
    </wsdl:message>
    <wsdl:message name="getChargebackVisaCountFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getChargebackVisaCountFault"/>
    </wsdl:message>
    <wsdl:message name="getPresentmentVISAResponse">
        <wsdl:part element="tns:getPresentmentVISAResponse" name="getPresentmentVISAResponse"/>
    </wsdl:message>
    <wsdl:message name="getPresentmentVISAFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getPresentmentVISAFault"/>
    </wsdl:message>
    <wsdl:message name="searchIncomingChargebackVISAResponse">
        <wsdl:part element="tns:searchIncomingChargebackVISAResponse" name="searchIncomingChargebackVISAResponse"/>
    </wsdl:message>
    <wsdl:message name="searchIncomingChargebackVISAFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchIncomingChargebackVISAFault"/>
    </wsdl:message>
    <wsdl:message name="getIncomingClientVisaResponse">
        <wsdl:part element="tns:getIncomingClientVisaResponse" name="getIncomingClientVisaResponse"/>
    </wsdl:message>
    <wsdl:message name="getIncomingClientVisaFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getIncomingClientVisaFault"/>
    </wsdl:message>
    <wsdl:message name="getChargebackVISARequest">
        <wsdl:part element="tns:getChargebackVISARequest" name="getChargebackVISARequest"/>
    </wsdl:message>
    <wsdl:message name="searchIncomingChargebackVISARequest">
        <wsdl:part element="tns:searchIncomingChargebackVISARequest" name="searchIncomingChargebackVISARequest"/>
    </wsdl:message>
    <wsdl:message name="getOutgoingPresentmentVISAResponse">
        <wsdl:part element="tns:getOutgoingPresentmentVISAResponse" name="getOutgoingPresentmentVISAResponse"/>
    </wsdl:message>
    <wsdl:message name="getOutgoingPresentmentVISAFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getOutgoingPresentmentVISAFault"/>
    </wsdl:message>
    <wsdl:message name="getOutgoingPresentmentVISARequest">
        <wsdl:part element="tns:getOutgoingPresentmentVISARequest" name="getOutgoingPresentmentVISARequest"/>
    </wsdl:message>
    <wsdl:message name="getIncomingClientVisaRequest">
        <wsdl:part element="tns:getIncomingClientVisaRequest" name="getIncomingClientVisaRequest"/>
    </wsdl:message>
    <wsdl:message name="getIncomingHoldVisaResponse">
        <wsdl:part element="tns:getIncomingHoldVisaResponse" name="getIncomingHoldVisaResponse"/>
    </wsdl:message>
    <wsdl:message name="getIncomingHoldVisaFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getIncomingHoldVisaFault"/>
    </wsdl:message>
    <wsdl:message name="getIncomingHoldVisaRequest">
        <wsdl:part element="tns:getIncomingHoldVisaRequest" name="getIncomingHoldVisaRequest"/>
    </wsdl:message>
    <wsdl:message name="getPresentmentVISARequest">
        <wsdl:part element="tns:getPresentmentVISARequest" name="getPresentmentVISARequest"/>
    </wsdl:message>
    <wsdl:portType name="ClearingVISAWS">
        <wsdl:operation name="getVisaFeeCollection">
            <wsdl:input message="tns:getVisaFeeCollectionRequest" name="getVisaFeeCollectionRequest"/>
            <wsdl:output message="tns:getVisaFeeCollectionResponse" name="getVisaFeeCollectionResponse"/>
            <wsdl:fault message="tns:getVisaFeeCollectionFault" name="getVisaFeeCollectionFault"/>
        </wsdl:operation>
        <wsdl:operation name="getOutgoingRepresentmentVISA">
            <wsdl:input message="tns:getOutgoingRepresentmentVISARequest" name="getOutgoingRepresentmentVISARequest"/>
            <wsdl:output message="tns:getOutgoingRepresentmentVISAResponse" name="getOutgoingRepresentmentVISAResponse"/>
            <wsdl:fault message="tns:getOutgoingRepresentmentVISAFault" name="getOutgoingRepresentmentVISAFault"/>
        </wsdl:operation>
        <wsdl:operation name="getChargebackVisaCount">
            <wsdl:input message="tns:getChargebackVisaCountRequest" name="getChargebackVisaCountRequest"/>
            <wsdl:output message="tns:getChargebackVisaCountResponse" name="getChargebackVisaCountResponse"/>
            <wsdl:fault message="tns:getChargebackVisaCountFault" name="getChargebackVisaCountFault"/>
        </wsdl:operation>
        <wsdl:operation name="getIncomingHoldVisa">
            <wsdl:input message="tns:getIncomingHoldVisaRequest" name="getIncomingHoldVisaRequest"/>
            <wsdl:output message="tns:getIncomingHoldVisaResponse" name="getIncomingHoldVisaResponse"/>
            <wsdl:fault message="tns:getIncomingHoldVisaFault" name="getIncomingHoldVisaFault"/>
        </wsdl:operation>
        <wsdl:operation name="getIncomingClientVisa">
            <wsdl:input message="tns:getIncomingClientVisaRequest" name="getIncomingClientVisaRequest"/>
            <wsdl:output message="tns:getIncomingClientVisaResponse" name="getIncomingClientVisaResponse"/>
            <wsdl:fault message="tns:getIncomingClientVisaFault" name="getIncomingClientVisaFault"/>
        </wsdl:operation>
        <wsdl:operation name="getChargebackVISA">
            <wsdl:input message="tns:getChargebackVISARequest" name="getChargebackVISARequest"/>
            <wsdl:output message="tns:getChargebackVISAResponse" name="getChargebackVISAResponse"/>
            <wsdl:fault message="tns:getChargebackVISAFault" name="getChargebackVISAFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchIncomingChargebackVISA">
            <wsdl:input message="tns:searchIncomingChargebackVISARequest" name="searchIncomingChargebackVISARequest"/>
            <wsdl:output message="tns:searchIncomingChargebackVISAResponse" name="searchIncomingChargebackVISAResponse"/>
            <wsdl:fault message="tns:searchIncomingChargebackVISAFault" name="searchIncomingChargebackVISAFault"/>
        </wsdl:operation>
        <wsdl:operation name="getOutgoingPresentmentVISA">
            <wsdl:input message="tns:getOutgoingPresentmentVISARequest" name="getOutgoingPresentmentVISARequest"/>
            <wsdl:output message="tns:getOutgoingPresentmentVISAResponse" name="getOutgoingPresentmentVISAResponse"/>
            <wsdl:fault message="tns:getOutgoingPresentmentVISAFault" name="getOutgoingPresentmentVISAFault"/>
        </wsdl:operation>
        <wsdl:operation name="getPresentmentVISA">
            <wsdl:input message="tns:getPresentmentVISARequest" name="getPresentmentVISARequest"/>
            <wsdl:output message="tns:getPresentmentVISAResponse" name="getPresentmentVISAResponse"/>
            <wsdl:fault message="tns:getPresentmentVISAFault" name="getPresentmentVISAFault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="ClearingVISAWSSoap11" type="tns:ClearingVISAWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getVisaFeeCollection">
            <soap:operation soapAction=""/>
            <wsdl:input name="getVisaFeeCollectionRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getVisaFeeCollectionResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getVisaFeeCollectionFault">
                <soap:fault name="getVisaFeeCollectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getOutgoingRepresentmentVISA">
            <soap:operation soapAction=""/>
            <wsdl:input name="getOutgoingRepresentmentVISARequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getOutgoingRepresentmentVISAResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getOutgoingRepresentmentVISAFault">
                <soap:fault name="getOutgoingRepresentmentVISAFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getChargebackVisaCount">
            <soap:operation soapAction=""/>
            <wsdl:input name="getChargebackVisaCountRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getChargebackVisaCountResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getChargebackVisaCountFault">
                <soap:fault name="getChargebackVisaCountFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getIncomingHoldVisa">
            <soap:operation soapAction=""/>
            <wsdl:input name="getIncomingHoldVisaRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getIncomingHoldVisaResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getIncomingHoldVisaFault">
                <soap:fault name="getIncomingHoldVisaFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getIncomingClientVisa">
            <soap:operation soapAction=""/>
            <wsdl:input name="getIncomingClientVisaRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getIncomingClientVisaResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getIncomingClientVisaFault">
                <soap:fault name="getIncomingClientVisaFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getChargebackVISA">
            <soap:operation soapAction=""/>
            <wsdl:input name="getChargebackVISARequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getChargebackVISAResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getChargebackVISAFault">
                <soap:fault name="getChargebackVISAFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchIncomingChargebackVISA">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchIncomingChargebackVISARequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchIncomingChargebackVISAResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchIncomingChargebackVISAFault">
                <soap:fault name="searchIncomingChargebackVISAFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getOutgoingPresentmentVISA">
            <soap:operation soapAction=""/>
            <wsdl:input name="getOutgoingPresentmentVISARequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getOutgoingPresentmentVISAResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getOutgoingPresentmentVISAFault">
                <soap:fault name="getOutgoingPresentmentVISAFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPresentmentVISA">
            <soap:operation soapAction=""/>
            <wsdl:input name="getPresentmentVISARequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getPresentmentVISAResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getPresentmentVISAFault">
                <soap:fault name="getPresentmentVISAFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="ClearingVISAWSService">
        <wsdl:port binding="tns:ClearingVISAWSSoap11" name="ClearingVISAWSSoap11">
            <soap:address location="/ws/clearing/ClearingVISAWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>