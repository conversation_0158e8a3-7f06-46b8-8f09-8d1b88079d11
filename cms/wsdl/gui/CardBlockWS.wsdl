<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/ws/card/cardblock" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://homecredit.net/ws/card/cardblock">
    <xs:annotation>
        <xs:documentation>CardBlockWS - The endpoint for managing card blockages.
            Error severity: minor, error businessImpact: Inability to manage card blockages..
            Average response time: &lt;10s, Throughput: Average 1 request per second
        </xs:documentation>
    </xs:annotation>

    <wsdl:types>
        <xs:schema targetNamespace="http://homecredit.net/ws/card/cardblock">
            <xs:include schemaLocation="CardBlock.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="searchBlockHistoryResponse">
        <wsdl:part element="tns:searchBlockHistoryResponse" name="searchBlockHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="searchBlockHistoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="searchBlockHistoryFault"/>
    </wsdl:message>
    <wsdl:message name="getAvailableBlockReasonResponse">
        <wsdl:part element="tns:getAvailableBlockReasonResponse" name="getAvailableBlockReasonResponse"/>
    </wsdl:message>
    <wsdl:message name="getAvailableBlockReasonFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getAvailableBlockReasonFault"/>
    </wsdl:message>
    <wsdl:message name="finishBlockReasonRequest">
        <wsdl:part element="tns:finishBlockReasonRequest" name="finishBlockReasonRequest"/>
    </wsdl:message>
    <wsdl:message name="finishBlockReasonFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="finishBlockReasonFault"/>
    </wsdl:message>
    <wsdl:message name="getAvailableUnblockReasonResponse">
        <wsdl:part element="tns:getAvailableUnblockReasonResponse" name="getAvailableUnblockReasonResponse"/>
    </wsdl:message>
    <wsdl:message name="getAvailableUnblockReasonFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getAvailableUnblockReasonFault"/>
    </wsdl:message>
    <wsdl:message name="searchBlockHistoryRequest">
        <wsdl:part element="tns:searchBlockHistoryRequest" name="searchBlockHistoryRequest"/>
    </wsdl:message>
    <wsdl:message name="finishBlockReasonResponse">
        <wsdl:part element="tns:finishBlockReasonResponse" name="finishBlockReasonResponse"/>
    </wsdl:message>
    <wsdl:message name="insertBlockReasonResponse">
        <wsdl:part element="tns:insertBlockReasonResponse" name="insertBlockReasonResponse"/>
    </wsdl:message>
    <wsdl:message name="insertBlockReasonFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="insertBlockReasonFault"/>
    </wsdl:message>
    <wsdl:message name="getAvailableBlockReasonRequest">
        <wsdl:part element="tns:getAvailableBlockReasonRequest" name="getAvailableBlockReasonRequest"/>
    </wsdl:message>
    <wsdl:message name="insertBlockReasonRequest">
        <wsdl:part element="tns:insertBlockReasonRequest" name="insertBlockReasonRequest"/>
    </wsdl:message>
    <wsdl:message name="isCardBlockedResponse">
        <wsdl:part element="tns:isCardBlockedResponse" name="isCardBlockedResponse"/>
    </wsdl:message>
    <wsdl:message name="isCardBlockedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="isCardBlockedFault"/>
    </wsdl:message>
    <wsdl:message name="getAvailableUnblockReasonRequest">
        <wsdl:part element="tns:getAvailableUnblockReasonRequest" name="getAvailableUnblockReasonRequest"/>
    </wsdl:message>
    <wsdl:message name="isCardBlockedRequest">
        <wsdl:part element="tns:isCardBlockedRequest" name="isCardBlockedRequest"/>
    </wsdl:message>

    <wsdl:portType name="CardBlockWS">
        <wsdl:operation name="isCardBlocked">
            <wsdl:input message="tns:isCardBlockedRequest" name="isCardBlockedRequest"/>
            <wsdl:output message="tns:isCardBlockedResponse" name="isCardBlockedResponse"/>
            <wsdl:fault message="tns:isCardBlockedFault" name="isCardBlockedFault"/>
        </wsdl:operation>
        <wsdl:operation name="getAvailableUnblockReason">
            <wsdl:input message="tns:getAvailableUnblockReasonRequest" name="getAvailableUnblockReasonRequest"/>
            <wsdl:output message="tns:getAvailableUnblockReasonResponse" name="getAvailableUnblockReasonResponse"/>
            <wsdl:fault message="tns:getAvailableUnblockReasonFault" name="getAvailableUnblockReasonFault"/>
        </wsdl:operation>
        <wsdl:operation name="searchBlockHistory">
            <wsdl:input message="tns:searchBlockHistoryRequest" name="searchBlockHistoryRequest"/>
            <wsdl:output message="tns:searchBlockHistoryResponse" name="searchBlockHistoryResponse"/>
            <wsdl:fault message="tns:searchBlockHistoryFault" name="searchBlockHistoryFault"/>
        </wsdl:operation>
        <wsdl:operation name="insertBlockReason">
            <wsdl:input message="tns:insertBlockReasonRequest" name="insertBlockReasonRequest"/>
            <wsdl:output message="tns:insertBlockReasonResponse" name="insertBlockReasonResponse"/>
            <wsdl:fault message="tns:insertBlockReasonFault" name="insertBlockReasonFault"/>
        </wsdl:operation>
        <wsdl:operation name="finishBlockReason">
            <wsdl:input message="tns:finishBlockReasonRequest" name="finishBlockReasonRequest"/>
            <wsdl:output message="tns:finishBlockReasonResponse" name="finishBlockReasonResponse"/>
            <wsdl:fault message="tns:finishBlockReasonFault" name="finishBlockReasonFault"/>
        </wsdl:operation>
        <wsdl:operation name="getAvailableBlockReason">
            <wsdl:input message="tns:getAvailableBlockReasonRequest" name="getAvailableBlockReasonRequest"/>
            <wsdl:output message="tns:getAvailableBlockReasonResponse" name="getAvailableBlockReasonResponse"/>
            <wsdl:fault message="tns:getAvailableBlockReasonFault" name="getAvailableBlockReasonFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CardBlockWSSoap11" type="tns:CardBlockWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="isCardBlocked">
            <soap:operation soapAction=""/>
            <wsdl:input name="isCardBlockedRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="isCardBlockedResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="isCardBlockedFault">
                <soap:fault name="isCardBlockedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAvailableUnblockReason">
            <soap:operation soapAction=""/>
            <wsdl:input name="getAvailableUnblockReasonRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getAvailableUnblockReasonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getAvailableUnblockReasonFault">
                <soap:fault name="getAvailableUnblockReasonFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="searchBlockHistory">
            <soap:operation soapAction=""/>
            <wsdl:input name="searchBlockHistoryRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="searchBlockHistoryResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="searchBlockHistoryFault">
                <soap:fault name="searchBlockHistoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="insertBlockReason">
            <soap:operation soapAction=""/>
            <wsdl:input name="insertBlockReasonRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="insertBlockReasonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="insertBlockReasonFault">
                <soap:fault name="insertBlockReasonFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="finishBlockReason">
            <soap:operation soapAction=""/>
            <wsdl:input name="finishBlockReasonRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="finishBlockReasonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="finishBlockReasonFault">
                <soap:fault name="finishBlockReasonFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAvailableBlockReason">
            <soap:operation soapAction=""/>
            <wsdl:input name="getAvailableBlockReasonRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getAvailableBlockReasonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getAvailableBlockReasonFault">
                <soap:fault name="getAvailableBlockReasonFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="CardBlockWSService">
        <wsdl:port binding="tns:CardBlockWSSoap11" name="CardBlockWSSoap11">
            <soap:address location="/ws/card/CardBlockWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>