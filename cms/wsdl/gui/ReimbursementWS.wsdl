<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:tns="http://cms.airbank.cz/ws/card/reimbursement"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://cms.airbank.cz/ws/card/reimbursement">
    <xs:annotation>
        <xs:documentation>
            ReimbursementWS - The endpoint for complaint refunding, canceling, covering etc.
        </xs:documentation>
    </xs:annotation>

    <wsdl:types>
        <xs:schema targetNamespace="http://cms.airbank.cz/ws/card/reimbursement">
            <xs:include schemaLocation="Reimbursement.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="cancelCoverComplaintCardPaymentsRequestType">
        <wsdl:part element="tns:cancelCoverComplaintCardPaymentsRequestType" name="cancelCoverComplaintCardPaymentsRequestType"/>
    </wsdl:message>
    <wsdl:message name="cancelCoverComplaintCardPaymentsResponseType">
        <wsdl:part element="tns:cancelCoverComplaintCardPaymentsResponseType" name="cancelCoverComplaintCardPaymentsResponseType"/>
    </wsdl:message>
    <wsdl:message name="cancelCoverComplaintCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="cancelCoverComplaintCardPaymentsFault"/>
    </wsdl:message>
    <wsdl:message name="refundComplaintCardPaymentsRequestType">
        <wsdl:part element="tns:refundComplaintCardPaymentsRequestType" name="refundComplaintCardPaymentsRequestType"/>
    </wsdl:message>
    <wsdl:message name="refundComplaintCardPaymentsResponseType">
        <wsdl:part element="tns:refundComplaintCardPaymentsResponseType" name="refundComplaintCardPaymentsResponseType"/>
    </wsdl:message>
    <wsdl:message name="refundComplaintCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="refundComplaintCardPaymentsFault"/>
    </wsdl:message>
    <wsdl:message name="cancelRefundComplaintCardPaymentsRequestType">
        <wsdl:part element="tns:cancelRefundComplaintCardPaymentsRequestType" name="cancelRefundComplaintCardPaymentsRequestType"/>
    </wsdl:message>
    <wsdl:message name="cancelRefundComplaintCardPaymentsResponseType">
        <wsdl:part element="tns:cancelRefundComplaintCardPaymentsResponseType" name="cancelRefundComplaintCardPaymentsResponseType"/>
    </wsdl:message>
    <wsdl:message name="cancelRefundComplaintCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="cancelRefundComplaintCardPaymentsFault"/>
    </wsdl:message>
    <wsdl:message name="takeBackComplaintCardPaymentsRequestType">
        <wsdl:part element="tns:takeBackComplaintCardPaymentsRequestType" name="takeBackComplaintCardPaymentsRequestType"/>
    </wsdl:message>
    <wsdl:message name="takeBackComplaintCardPaymentsResponseType">
        <wsdl:part element="tns:takeBackComplaintCardPaymentsResponseType" name="takeBackComplaintCardPaymentsResponseType"/>
    </wsdl:message>
    <wsdl:message name="takeBackComplaintCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="takeBackComplaintCardPaymentsFault"/>
    </wsdl:message>
    <wsdl:message name="coverComplaintCardPaymentsRequestType">
        <wsdl:part element="tns:coverComplaintCardPaymentsRequestType" name="coverComplaintCardPaymentsRequestType"/>
    </wsdl:message>
    <wsdl:message name="coverComplaintCardPaymentsResponseType">
        <wsdl:part element="tns:coverComplaintCardPaymentsResponseType" name="coverComplaintCardPaymentsResponseType"/>
    </wsdl:message>
    <wsdl:message name="coverComplaintCardPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="coverComplaintCardPaymentsFault"/>
    </wsdl:message>
    <wsdl:message name="manualBalanceComplaintAccountRequestType">
        <wsdl:part element="tns:manualBalanceComplaintAccountRequestType" name="manualBalanceComplaintAccountRequestType"/>
    </wsdl:message>
    <wsdl:message name="manualBalanceComplaintAccountResponseType">
        <wsdl:part element="tns:manualBalanceComplaintAccountResponseType" name="manualBalanceComplaintAccountResponseType"/>
    </wsdl:message>
    <wsdl:message name="manualBalanceComplaintAccountFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="manualBalanceComplaintAccountFault"/>
    </wsdl:message>
    <wsdl:message name="pairReturnComplaintCardPaymentRequestType">
        <wsdl:part element="tns:pairReturnComplaintCardPaymentRequestType" name="pairReturnComplaintCardPaymentRequestType"/>
    </wsdl:message>
    <wsdl:message name="pairReturnComplaintCardPaymentResponseType">
        <wsdl:part element="tns:pairReturnComplaintCardPaymentResponseType" name="pairReturnComplaintCardPaymentResponseType"/>
    </wsdl:message>
    <wsdl:message name="pairReturnComplaintCardPaymentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="pairReturnComplaintCardPaymentFault"/>
    </wsdl:message>
    <wsdl:message name="unpairReturnComplaintCardPaymentRequestType">
        <wsdl:part element="tns:unpairReturnComplaintCardPaymentRequestType" name="unpairReturnComplaintCardPaymentRequestType"/>
    </wsdl:message>
    <wsdl:message name="unpairReturnComplaintCardPaymentResponseType">
        <wsdl:part element="tns:unpairReturnComplaintCardPaymentResponseType" name="unpairReturnComplaintCardPaymentResponseType"/>
    </wsdl:message>
    <wsdl:message name="unpairReturnComplaintCardPaymentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="unpairReturnComplaintCardPaymentFault"/>
    </wsdl:message>

    <wsdl:portType name="ReimbursementWS">
        <wsdl:operation name="cancelCoverComplaintCardPayments">
            <wsdl:input message="tns:cancelCoverComplaintCardPaymentsRequestType" name="cancelCoverComplaintCardPaymentsRequestType"/>
            <wsdl:output message="tns:cancelCoverComplaintCardPaymentsResponseType" name="cancelCoverComplaintCardPaymentsResponseType"/>
            <wsdl:fault message="tns:cancelCoverComplaintCardPaymentsFault" name="cancelCoverComplaintCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="refundComplaintCardPayments">
            <wsdl:input message="tns:refundComplaintCardPaymentsRequestType" name="refundComplaintCardPaymentsRequestType"/>
            <wsdl:output message="tns:refundComplaintCardPaymentsResponseType" name="refundComplaintCardPaymentsResponseType"/>
            <wsdl:fault message="tns:refundComplaintCardPaymentsFault" name="refundComplaintCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="cancelRefundComplaintCardPayments">
            <wsdl:input message="tns:cancelRefundComplaintCardPaymentsRequestType" name="cancelRefundComplaintCardPaymentsRequestType"/>
            <wsdl:output message="tns:cancelRefundComplaintCardPaymentsResponseType" name="cancelRefundComplaintCardPaymentsResponseType"/>
            <wsdl:fault message="tns:cancelRefundComplaintCardPaymentsFault" name="cancelRefundComplaintCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="takeBackComplaintCardPayments">
            <wsdl:input message="tns:takeBackComplaintCardPaymentsRequestType" name="takeBackComplaintCardPaymentsRequestType"/>
            <wsdl:output message="tns:takeBackComplaintCardPaymentsResponseType" name="takeBackComplaintCardPaymentsResponseType"/>
            <wsdl:fault message="tns:takeBackComplaintCardPaymentsFault" name="takeBackComplaintCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="coverComplaintCardPayments">
            <wsdl:input message="tns:coverComplaintCardPaymentsRequestType" name="coverComplaintCardPaymentsRequestType"/>
            <wsdl:output message="tns:coverComplaintCardPaymentsResponseType" name="coverComplaintCardPaymentsResponseType"/>
            <wsdl:fault message="tns:coverComplaintCardPaymentsFault" name="coverComplaintCardPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="manualBalanceComplaintAccount">
            <wsdl:input message="tns:manualBalanceComplaintAccountRequestType" name="manualBalanceComplaintAccountRequestType"/>
            <wsdl:output message="tns:manualBalanceComplaintAccountResponseType" name="manualBalanceComplaintAccountResponseType"/>
            <wsdl:fault message="tns:manualBalanceComplaintAccountFault" name="manualBalanceComplaintAccountFault"/>
        </wsdl:operation>
        <wsdl:operation name="pairReturnComplaintCardPayment">
            <wsdl:input message="tns:pairReturnComplaintCardPaymentRequestType" name="pairReturnComplaintCardPaymentRequestType"/>
            <wsdl:output message="tns:pairReturnComplaintCardPaymentResponseType" name="pairReturnComplaintCardPaymentResponseType"/>
            <wsdl:fault message="tns:pairReturnComplaintCardPaymentFault" name="pairReturnComplaintCardPaymentFault"/>
        </wsdl:operation>
        <wsdl:operation name="unpairReturnComplaintCardPayment">
            <wsdl:input message="tns:unpairReturnComplaintCardPaymentRequestType" name="unpairReturnComplaintCardPaymentRequestType"/>
            <wsdl:output message="tns:unpairReturnComplaintCardPaymentResponseType" name="unpairReturnComplaintCardPaymentResponseType"/>
            <wsdl:fault message="tns:unpairReturnComplaintCardPaymentFault" name="unpairReturnComplaintCardPaymentFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ReimbursementWSSoap11" type="tns:ReimbursementWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="cancelCoverComplaintCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="cancelCoverComplaintCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="cancelCoverComplaintCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="cancelCoverComplaintCardPaymentsFault">
                <soap:fault name="cancelCoverComplaintCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="refundComplaintCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="refundComplaintCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="refundComplaintCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="refundComplaintCardPaymentsFault">
                <soap:fault name="refundComplaintCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="cancelRefundComplaintCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="cancelRefundComplaintCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="cancelRefundComplaintCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="cancelRefundComplaintCardPaymentsFault">
                <soap:fault name="cancelRefundComplaintCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="takeBackComplaintCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="takeBackComplaintCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="takeBackComplaintCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="takeBackComplaintCardPaymentsFault">
                <soap:fault name="takeBackComplaintCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="coverComplaintCardPayments">
            <soap:operation soapAction=""/>
            <wsdl:input name="coverComplaintCardPaymentsRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="coverComplaintCardPaymentsResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="coverComplaintCardPaymentsFault">
                <soap:fault name="coverComplaintCardPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="manualBalanceComplaintAccount">
            <soap:operation soapAction=""/>
            <wsdl:input name="manualBalanceComplaintAccountRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="manualBalanceComplaintAccountResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="manualBalanceComplaintAccountFault">
                <soap:fault name="manualBalanceComplaintAccountFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="pairReturnComplaintCardPayment">
            <soap:operation soapAction=""/>
            <wsdl:input name="pairReturnComplaintCardPaymentRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="pairReturnComplaintCardPaymentResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="pairReturnComplaintCardPaymentFault">
                <soap:fault name="pairReturnComplaintCardPaymentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="unpairReturnComplaintCardPayment">
            <soap:operation soapAction=""/>
            <wsdl:input name="unpairReturnComplaintCardPaymentRequestType">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="unpairReturnComplaintCardPaymentResponseType">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="unpairReturnComplaintCardPaymentFault">
                <soap:fault name="unpairReturnComplaintCardPaymentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ReimbursementWSService">
        <wsdl:port binding="tns:ReimbursementWSSoap11" name="ReimbursementWSSoap11">
            <soap:address location="/ws/card/ReimbursementWS"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>