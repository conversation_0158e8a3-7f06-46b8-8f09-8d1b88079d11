<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
		   attributeFormDefault="unqualified"
		   elementFormDefault="qualified"
		   targetNamespace="http://homecredit.net/ws/card/general"
		   xmlns="http://homecredit.net/ws/card/general">

	<xs:complexType name="CFlagIvrInDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="idCard" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="idFlagType" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="flagCode" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="validFrom" nillable="true" type="xs:dateTime" />
			<xs:element minOccurs="1" name="cFlagValue" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="idEmp" nillable="true" type="xs:long" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="CFlagInDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="idCard" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="idFlagType" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="flagCode" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="CFlagValue" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="validFrom" nillable="true" type="xs:dateTime" />
		</xs:sequence>
	</xs:complexType>



	<xs:complexType name="IsStatusSeqPermittedDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="oldStatus" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="newStatus" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="regNumber" nillable="true" type="xs:int" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SetCmsSetupDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="bankCode" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="fileAdditionChar" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="ccyShortName" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="ccyCode" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="stateCode" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="region" nillable="true" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<xs:element name="insCFlagIvrRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="paramDto" type="CFlagIvrInDto" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="insCFlagIvrResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="1" name="result" nillable="true" type="xs:long" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="insCFlagRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="paramDto" type="CFlagInDto" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="insCFlagResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="1" name="result" nillable="true" type="xs:long" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>



	<xs:element name="isStatusSeqPermittedRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="paramDto" type="IsStatusSeqPermittedDto" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="isStatusSeqPermittedResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="1" name="result" nillable="true" type="xs:int" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="setCmsSetupRequest">
		<xs:complexType>
			<xs:sequence />
		</xs:complexType>
	</xs:element>

	<xs:element name="setCmsSetupResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="result" type="SetCmsSetupDto" nillable="true" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

</xs:schema>
