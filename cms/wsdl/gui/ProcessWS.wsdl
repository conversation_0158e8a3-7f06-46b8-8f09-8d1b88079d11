<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/ws/process/process"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://homecredit.net/ws/process/process">
    <xs:annotation>
        <xs:documentation>ProcessWS - The endpoint for process management.
            Error severity: minor, error businessImpact: Inability to manage processes.
            Average response time: &lt;10s, Throughput: Average 1 request per second
        </xs:documentation>
    </xs:annotation>
    <wsdl:types>
        <xs:schema targetNamespace="http://homecredit.net/ws/process/process">
            <xs:include schemaLocation="Process.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="stopWatchingRequest">
        <wsdl:part element="tns:stopWatchingRequest" name="stopWatchingRequest"/>
    </wsdl:message>
    <wsdl:message name="stopWatchingResponse">
        <wsdl:part element="tns:stopWatchingResponse" name="stopWatchingResponse"/>
    </wsdl:message>
    <wsdl:message name="stopWatchingFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="stopWatchingFault"/>
    </wsdl:message>
    <wsdl:message name="getWatchedProcessStatusesRequest">
        <wsdl:part element="tns:getWatchedProcessStatusesRequest" name="getWatchedProcessStatusesRequest"/>
    </wsdl:message>
    <wsdl:message name="getWatchedProcessStatusesResponse">
        <wsdl:part element="tns:getWatchedProcessStatusesResponse" name="getWatchedProcessStatusesResponse"/>
    </wsdl:message>
    <wsdl:message name="getWatchedProcessStatusesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getWatchedProcessStatusesFault"/>
    </wsdl:message>
    <wsdl:message name="getActiveProcessStatusesRequest">
        <wsdl:part element="tns:getActiveProcessStatusesRequest" name="getActiveProcessStatusesRequest"/>
    </wsdl:message>
    <wsdl:message name="getActiveProcessStatusesResponse">
        <wsdl:part element="tns:getActiveProcessStatusesResponse" name="getActiveProcessStatusesResponse"/>
    </wsdl:message>
    <wsdl:message name="getActiveProcessStatusesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getActiveProcessStatusesFault"/>
    </wsdl:message>
    <wsdl:message name="getOutputValuesRequest">
        <wsdl:part element="tns:getOutputValuesRequest" name="getOutputValuesRequest"/>
    </wsdl:message>
    <wsdl:message name="getOutputValuesResponse">
        <wsdl:part element="tns:getOutputValuesResponse" name="getOutputValuesResponse"/>
    </wsdl:message>
    <wsdl:message name="getOutputValuesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getOutputValuesFault"/>
    </wsdl:message>
    <wsdl:message name="getProcessStatusRequest">
        <wsdl:part element="tns:getProcessStatusRequest" name="getProcessStatusRequest"/>
    </wsdl:message>
    <wsdl:message name="getProcessStatusResponse">
        <wsdl:part element="tns:getProcessStatusResponse" name="getProcessStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="getProcessStatusFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="getProcessStatusFault"/>
    </wsdl:message>

    <wsdl:portType name="ProcessWS">
        <wsdl:operation name="getWatchedProcessStatuses">
            <wsdl:input message="tns:getWatchedProcessStatusesRequest" name="getWatchedProcessStatusesRequest"/>
            <wsdl:output message="tns:getWatchedProcessStatusesResponse" name="getWatchedProcessStatusesResponse"/>
            <wsdl:fault message="tns:getWatchedProcessStatusesFault" name="getWatchedProcessStatusesFault"/>
        </wsdl:operation>
        <wsdl:operation name="stopWatching">
            <wsdl:input message="tns:stopWatchingRequest" name="stopWatchingRequest"/>
            <wsdl:output message="tns:stopWatchingResponse" name="stopWatchingResponse"/>
            <wsdl:fault message="tns:stopWatchingFault" name="stopWatchingFault"/>
        </wsdl:operation>
        <wsdl:operation name="getActiveProcessStatuses">
            <wsdl:input message="tns:getActiveProcessStatusesRequest" name="getActiveProcessStatusesRequest"/>
            <wsdl:output message="tns:getActiveProcessStatusesResponse" name="getActiveProcessStatusesResponse"/>
            <wsdl:fault message="tns:getActiveProcessStatusesFault" name="getActiveProcessStatusesFault"/>
        </wsdl:operation>
        <wsdl:operation name="getOutputValues">
            <wsdl:input message="tns:getOutputValuesRequest" name="getOutputValuesRequest"/>
            <wsdl:output message="tns:getOutputValuesResponse" name="getOutputValuesResponse"/>
            <wsdl:fault message="tns:getOutputValuesFault" name="getOutputValuesFault"/>
        </wsdl:operation>
        <wsdl:operation name="getProcessStatus">
            <wsdl:input message="tns:getProcessStatusRequest" name="getProcessStatusRequest"/>
            <wsdl:output message="tns:getProcessStatusResponse" name="getProcessStatusResponse"/>
            <wsdl:fault message="tns:getProcessStatusFault" name="getProcessStatusFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ProcessWSSoap11" type="tns:ProcessWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getWatchedProcessStatuses">
            <soap:operation soapAction=""/>
            <wsdl:input name="getWatchedProcessStatusesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getWatchedProcessStatusesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getWatchedProcessStatusesFault">
                <soap:fault name="getWatchedProcessStatusesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="stopWatching">
            <soap:operation soapAction=""/>
            <wsdl:input name="stopWatchingRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="stopWatchingResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="stopWatchingFault">
                <soap:fault name="stopWatchingFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getActiveProcessStatuses">
            <soap:operation soapAction=""/>
            <wsdl:input name="getActiveProcessStatusesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getActiveProcessStatusesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getActiveProcessStatusesFault">
                <soap:fault name="getActiveProcessStatusesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getOutputValues">
            <soap:operation soapAction=""/>
            <wsdl:input name="getOutputValuesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getOutputValuesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getOutputValuesFault">
                <soap:fault name="getOutputValuesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getProcessStatus">
            <soap:operation soapAction=""/>
            <wsdl:input name="getProcessStatusRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getProcessStatusResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="getProcessStatusFault">
                <soap:fault name="getProcessStatusFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ProcessWSService">
        <wsdl:port binding="tns:ProcessWSSoap11" name="ProcessWSSoap11">
            <soap:address location="/ws/process/ProcessWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>