<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://homecredit.net/card/common/resultCode" xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	
	<xs:simpleType name="ResultCode">
        <xs:restriction base="xs:string">
			<xs:enumeration value="OK">
				<xs:annotation>
					<xs:documentation>Success</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ERROR">
				<xs:annotation>
					<xs:documentation>Error</xs:documentation>
				</xs:annotation></xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	
	</xs:schema>