<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
		   attributeFormDefault="unqualified"
		   elementFormDefault="qualified"
		   targetNamespace="http://homecredit.net/ws/card/common"
		   xmlns="http://homecredit.net/ws/card/common">

	<xs:complexType name="CurrencyInfoDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="id" nillable="true" type="xs:long"/>
			<xs:element minOccurs="1" name="decimaldigits" nillable="true" type="xs:long"/>
			<xs:element minOccurs="1" name="idEmpIns" nillable="true" type="xs:long"/>
			<xs:element minOccurs="1" name="idEmpModif" nillable="true" type="xs:long"/>
			<xs:element minOccurs="1" name="insTime" nillable="true" type="xs:dateTime"/>
			<xs:element minOccurs="1" name="modifTime" nillable="true" type="xs:dateTime"/>
			<xs:element minOccurs="1" name="isoalphacode" nillable="true" type="xs:string"/>
			<xs:element minOccurs="1" name="isonumcode" nillable="true" type="xs:string"/>
			<xs:element minOccurs="1" name="name" nillable="true" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="KeyValueDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="key" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="value" nillable="true" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="BlockReasonDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="blockType" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="validFrom" nillable="true" type="xs:dateTime" />
			<xs:element minOccurs="1" name="validTo" nillable="true" type="xs:dateTime" />
			<xs:element minOccurs="1" name="note" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="person" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="idCard" nillable="true" type="xs:long" />
			<xs:element minOccurs="0" name="noValidate" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="LimitValueDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="limitRegisterCode" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="limitType" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="amount" nillable="true" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="NumberUsageLimitsDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="limitAtmNumberUsage" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="limitPosNumberUsage" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="limitMaxAtmNumberUsage" nillable="true" type="xs:long" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ProcessTokenDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="id" nillable="true" type="xs:long" />
			<xs:element minOccurs="1" name="controlled" nillable="false" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ProcessingNoticeDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="attribute" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="description" nillable="true" type="xs:string" />
			<xs:element minOccurs="1" name="severity" nillable="true" type="xs:long" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ResultDto">
		<xs:sequence>
			<xs:element minOccurs="1" name="result" nillable="true" type="xs:long" />
			<xs:element maxOccurs="unbounded" minOccurs="1" name="processingNotices" nillable="true" type="ProcessingNoticeDto" />
		</xs:sequence>
	</xs:complexType>

	<xs:simpleType name="legalSegmentType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CUSTOMER"/>
			<xs:enumeration value="ENTREPRENEUR"/>
			<xs:enumeration value="LEGAL_ENTITY"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="CardBusinessCategory">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TRAVEL">
				<xs:annotation>
					<xs:documentation>Travel card type</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="STANDARD">
				<xs:annotation>
					<xs:documentation>Standard card type</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

</xs:schema>
