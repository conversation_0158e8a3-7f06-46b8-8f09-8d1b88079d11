<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://homecredit.net/ws/card/address"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://homecredit.net/ws/card/address">
    <xs:complexType name="AddressDto">
        <xs:sequence>
            <xs:element name="line1" type="xs:string" minOccurs="0"/>
            <xs:element name="line2" type="xs:string" minOccurs="0"/>
            <xs:element name="town" type="xs:string" minOccurs="0" />
            <xs:element name="zip" type="xs:string" minOccurs="0"/>
            <xs:element name="countryAlpha2Code" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DeliveryAddress">
        <xs:sequence>
            <xs:element name="id" type="xs:long" />
            <xs:element name="addressText" type="xs:string" />
            <xs:element name="line1" type="xs:string" />
            <xs:element name="line2" type="xs:string" minOccurs="0"/>
            <xs:element name="town" type="xs:string" minOccurs="0" />
            <xs:element name="zip" type="xs:string"/>
            <xs:element name="countryAlpha2Code" type="xs:string" minOccurs="0"/>
            <xs:element name="countryEnglishName" type="xs:string" minOccurs="0"/>
            <xs:element name="created" type="xs:dateTime"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
