<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://cms.airbank.cz/ws/envelope/card/delivery"
           xmlns="http://cms.airbank.cz/ws/envelope/card/delivery"
           xmlns:del="http://cms.airbank.cz/ws/envelope/delivery"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified">

    <xs:import namespace="http://homecredit.net/ws/card/address" schemaLocation="../common/AddressDTO.xsd"/>
    <xs:import namespace="http://homecredit.net/cardmanagement" schemaLocation="CardManagement.xsd"/>
    <xs:import namespace="http://cms.airbank.cz/ws/envelope/delivery" schemaLocation="../gui/DeliveryWS.xsd"/>

    <xs:element name="AddCardRedistributionAddressRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cardId" type="xs:long"/>
                <xs:element name="redistributionAddress" type="del:CmsPurposeAddress"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AddCardRedistributionAddressResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            Result code. Values - OK, ALREADY_EXISTS, CARD_SHREDDED
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="validatePurposeAddressRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="purposeAddress" type="del:CmsPurposeAddress" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="validatePurposeAddressResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="validationResult" type="ValidationResult" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ValidationResult">
        <xs:sequence>
            <xs:element name="validationType" type="xs:string"/>
            <xs:element name="attributes" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="resultDetail" type="xs:string" minOccurs="0"/>
            <xs:element name="validationCode" type="xs:string" minOccurs="0"/>
            <xs:element name="values" type="Values" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Values">
        <xs:sequence>
            <xs:element name="key" type="xs:string" />
            <xs:element name="values" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

</xs:schema>
