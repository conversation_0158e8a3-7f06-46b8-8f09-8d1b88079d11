<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:com="http://cms.airbank.cz/ws/card/nfc/common"
           attributeFormDefault="unqualified" elementFormDefault="qualified"
           targetNamespace="http://cms.airbank.cz/ws/card/nfc/issuer" xmlns="http://cms.airbank.cz/ws/card/nfc/issuer">

    <!-- MDES Pre-Digitization API 1.7.1 -->

    <xs:import schemaLocation="CardNfcManagementCommon.xsd" namespace="http://cms.airbank.cz/ws/card/nfc/common"/>

    <!-- types -->
    <!-- simple types -->
    <xs:complexType name="PaymentTypes">
        <xs:annotation>
            <xs:documentation></xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="paymentType" type="PaymentType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="PaymentType">
        <xs:annotation>
            <xs:documentation>Different types of Payments supported for the token.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NFC">
                <xs:annotation>
                    <xs:documentation>The token is NFC capable.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DSRP">
                <xs:annotation>
                    <xs:documentation>The token is DSRP capable.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ECOMMERCE">
                <xs:annotation>
                    <xs:documentation>The token can be used for e-commerce transactions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>


    <xs:simpleType name="TokenStatus">
        <xs:annotation>
            <xs:documentation>The current status of token.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="INACTIVE">
                <xs:annotation>
                    <xs:documentation>Token has not yet been activated.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACTIVE">
                <xs:annotation>
                    <xs:documentation>Token is active and ready to transact.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SUSPENDED">
                <xs:annotation>
                    <xs:documentation>Token is suspended and unable to transact.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEACTIVATED">
                <xs:annotation>
                    <xs:documentation>Token has been permanently deactivated.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="RecommendationReasonCode">
        <xs:restriction base="xs:string">
            <!-- Approved Reason Codes -->
            <xs:enumeration value="LONG_ACCOUNT_TENURE">
                <xs:annotation>
                    <xs:documentation>Account has existed for an extended period of not less than one year. A Payment App Provider may determine a longer
                        account tenure to qualify for this reason.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="GOOD_ACTIVITY_HISTORY">
                <xs:annotation>
                    <xs:documentation>There has been financial activity linked to the account for at least and within a period of not less than six months; no
                        suspicious activity is linked to the account within a period of at least one year.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ADDITIONAL_DEVICE">
                <xs:annotation>
                    <xs:documentation>The digitization is for an additional device for the same Account PAN and consumer account. There must be a currently
                        active (not suspended) Token that was previously digitized and activated on an existing device for the same Account PAN and consumer
                        account.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SOFTWARE_UPDATE">
                <xs:annotation>
                    <xs:documentation>The digitization has been requested due to an authenticated operating system or other software update being installed on
                        the device, causing mobile payment data to be wiped and unable to be restored. This digitization must be for the same
                        paymentAppInstanceId to which a Token was previously digitized and activated for the same Account PAN and consumer account.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <!-- Require Additional Authentication or Declined Reason Codes -->
            <xs:enumeration value="ACCOUNT_TOO_NEW_SINCE_LAUNCH">
                <xs:annotation>
                    <xs:documentation>Account is considered new relative to the Payment App Provider service launch.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACCOUNT_TOO_NEW">
                <xs:annotation>
                    <xs:documentation>Account is considered new relative to provisioning request.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACCOUNT_CARD_TOO_NEW">
                <xs:annotation>
                    <xs:documentation>Account/card is considered new relative to provisioning request.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACCOUNT_RECENTLY_CHANGED">
                <xs:annotation>
                    <xs:documentation>Changes have recently been made to account data.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SUSPICIOUS_ACTIVITY">
                <xs:annotation>
                    <xs:documentation>Suspicious activity has been linked to this account.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INACTIVE_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>Inactive account.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HAS_SUSPENDED_TOKENS">
                <xs:annotation>
                    <xs:documentation>Device contains suspended tokens.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEVICE_RECENTLY_LOST">
                <xs:annotation>
                    <xs:documentation>Device has recently been reported lost.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TOO_MANY_RECENT_ATTEMPTS">
                <xs:annotation>
                    <xs:documentation>Excessive recent tokenization attempts to this device.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TOO_MANY_RECENT_TOKENS">
                <xs:annotation>
                    <xs:documentation>Excessive recent tokenization to this device.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TOO_MANY_DIFFERENT_CARDHOLDERS">
                <xs:annotation>
                    <xs:documentation>Excessive non-matching Cardholder names within the device.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LOW_DEVICE_SCORE">
                <xs:annotation>
                    <xs:documentation>Low device score.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LOW_ACCOUNT_SCORE">
                <xs:annotation>
                    <xs:documentation>Low account score.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OUTSIDE_HOME_TERRITORY">
                <xs:annotation>
                    <xs:documentation>Non-domestic tokenization attempt.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="UNABLE_TO_ASSESS">
                <xs:annotation>
                    <xs:documentation>Unable to provide recommendation due to system issues.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HIGH_RISK">
                <xs:annotation>
                    <xs:documentation>High fraud risk identified. Enhanced verification recommended.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LOW_PHONE_NUMBER_SCORE">
                <xs:annotation>
                    <xs:documentation>Low phone number score.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="Decision">
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVED">
                <xs:annotation>
                    <xs:documentation>Services request was approved.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DECLINED">
                <xs:annotation>
                    <xs:documentation>Services request was declined.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="REQUIRE_ADDITIONAL_AUTHENTICATION">
                <xs:annotation>
                    <xs:documentation>Services request requires additional authentication to be approved.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="Score1to5">
        <xs:annotation>
            <xs:documentation>1 to 5 value score.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[1-5]"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ActivationMethodType">
        <xs:annotation>
            <xs:documentation>Specifies the activation method type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TEXT_TO_CARDHOLDER_NUMBER">
                <xs:annotation>
                    <xs:documentation>Text message to Cardholder's mobile phone number. Value will be the Cardholder's masked mobile phone number.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EMAIL_TO_CARDHOLDER_ADDRESS">
                <xs:annotation>
                    <xs:documentation>Email to Cardholder's email address. Value will be the Cardholder's masked email address.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CARDHOLDER_TO_CALL_AUTOMATED_NUMBER">
                <xs:annotation>
                    <xs:documentation>Cardholder-initiated call to automated call center phone number. Value will be the phone number for the Cardholder to
                        call.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CARDHOLDER_TO_CALL_MANNED_NUMBER">
                <xs:annotation>
                    <xs:documentation>Cardholder-initiated call to manned call center phone number. Value will be the phone number for the Cardholder to call.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CARDHOLDER_TO_VISIT_WEBSITE">
                <xs:annotation>
                    <xs:documentation>Cardholder to visit a website. Value will be the website URL.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CARDHOLDER_TO_USE_MOBILE_APP">
                <xs:annotation>
                    <xs:documentation>Cardholder to use a specific mobile app to activate token. Value will be replaced by a formatted string.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ISSUER_TO_CALL_CARDHOLDER_NUMBER">
                <xs:annotation>
                    <xs:documentation>Issuer-initiated voice call to Cardholder's phone. Value will be the Cardholder's masked voice call phone number.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TokenSuspendedBy">
        <xs:annotation>
            <xs:documentation>Who or what caused the token to be suspended.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ISSUER">
                <xs:annotation>
                    <xs:documentation>Suspended by the Issuer. Payment App Provider unable to unsuspend this Token.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TOKEN_REQUESTOR">
                <xs:annotation>
                    <xs:documentation>Suspended by the Token Requestor.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PAYMENT_APP_PROVIDER">
                <xs:annotation>
                    <xs:documentation>Deprecated- Suspended by the Payment App Provider.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MOBILE_PIN_LOCKED">
                <xs:annotation>
                    <xs:documentation>Suspended due to the Mobile PIN being locked.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CARDHOLDER">
                <xs:annotation>
                    <xs:documentation>Suspended by the Cardholder.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <!-- complex types -->
    <xs:complexType name="Token">
        <xs:all>
            <xs:element name="tokenUniqueReference" type="xs:string"/>
            <xs:element name="status" minOccurs="0" type="xs:string"/>
            <xs:element name="suspendedBy" minOccurs="0" type="xs:string"/>
            <xs:element name="tokenExpiry" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The expiry of the Token PAN, given in MMYY format.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="TokenType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="EMBEDDED_SE"/>
            <xs:enumeration value="CLOUD"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="TokenData">
        <xs:annotation>
            <xs:documentation>The Token information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="token" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The token issued for this service request.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expiryMonth" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The month of the expiration date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expiryYear" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The year of the expiration date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sequenceNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Sequence number of the Token.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="tokensData">
        <xs:sequence>
            <xs:element name="tokens" type="Token" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Contains the Tokens which were updated.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AuthorizeServiceResponseData">
        <xs:sequence>
            <xs:element name="paymentAccountReference" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The payment account reference assigned to the PAN. This should only be returned if Mastercard is not the BIN controller.
                        It will be ignored if Mastercard is the BIN controller for the PAN.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="externalToken" type="TokenData" minOccurs="0"/>
            <xs:element name="alternateAccountIdentifier" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Cardholder-friendly reference to a bank account. Typically used when the cardholder is not aware of their Account PAN.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dataValidUntilTimestamp" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The data/time after which this encrypted payload object is considered invalid. If present, all systems must reject this
                        encrypted object after this time and treat it as invalid data. Must be expressed in ISO 8601 extended format as one of the following:
                        YYYY-MM-DDThh:mm:ss[ .sss ]Z , YYYY-MM-DDThh:mm:ss[ .sss ]±hh:mm, where [ .sss ] is optional and can be 1 to 3 digits. Must be a value
                        no more than 30 days in the future. Mastercard recommends using a value of (Current Time + 30 minutes).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CardholderData">
        <xs:annotation>
            <xs:documentation>Cardholder information used for authorizing the account.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="sourceIp" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The IP of the device initiating the request.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deviceLocation" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Latitude and longitude where the device the consumer is attempting to authorize is located. In the format "(sign)
                        latitude, (sign) longitude" with a precision of 2 decimal places. Latitude is between -90 and 90. Longitude between -180 and 180.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="consumerIdentifier" minOccurs="0" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Consumer Identifier provided by the token requestor. Optionally present in AuthorizeService when provided by the wallet
                        provider.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CardInfoDataObject">
        <xs:annotation>
            <xs:documentation>Account PAN information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="accountNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The Account PAN of the card associated with the service or the token PAN.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expiredMonth" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The month of the expiration date of the card to be digitized. Note that the expiry date may not be in the past. May be
                        omitted if the card does not have an expiry date.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expiredYear" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The year of the expiration date of the card to be digitized. Note that the expiry date may not be in the past. May be
                        omitted if the card does not have an expiry date.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" minOccurs="0" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The source of this card information. Optionally present for Account PAN.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardholderName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The name of the Cardholder in the format LASTNAME/FIRSTNAME or FIRSTNAME LASTNAME. Optionally present for Account PAN.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Services">
        <xs:annotation>
            <xs:documentation>Wrapper for AuthorizationService.services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="service" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>AuthorizationService service</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DeviceInfo">
        <xs:annotation>
            <xs:documentation>Contains information about the target device to be provisioned.</xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="deviceName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The name that the Cardholder has associated to the device with the Payment App Provider.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="serialNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The serial number of the device. May be masked.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="formFactor" minOccurs="0" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The form factor of the device to be provisioned. New values can be added without notice and should be accpeted.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="isoDeviceType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The 2 digit device type provided on the iso messages that the token is being provisioned to. Only present when provided by
                        a Wallet Provider. See Global Communication bulletins for values.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="osName" minOccurs="0" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The name of the device operating system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="osVersion" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The version of the device operating system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="imei" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The IMEI number of the device being provisioned.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="msisdn" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The MSISDN of the device being provisioned.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentTypes" type="PaymentTypes" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Different types of Payments supported for the token.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="storageTechnology" minOccurs="0" type="xs:string">
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="WalletProviderDecisioningInfo">
        <xs:annotation>
            <xs:documentation>Contains information about the decision recommended by the Wallet Provider.</xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="recommendedDecision" type="Decision" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The decision recommended by the Wallet Provider.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="recommendationStandardVersion" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The standards version used by the Wallet Provider to determine the recommended decision.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deviceScore" type="Score1to5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Score given to the device by the Wallet Provider.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountScore" type="Score1to5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Score given to the account by the Wallet Provider.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="phoneNumberScore" type="Score1to5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Score given to the phone number by the Wallet Provider.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="recommendationReasons" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reasons provided to the Wallet Provider on how the recommended decision was reached.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="ActivationMethod">
        <xs:annotation>
            <xs:documentation>The activation method to be used for this digitization.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="ActivationMethodType"/>
            <xs:element name="value" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Specifies the activation method value (meaning varies depending on the activation method type).</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- methods -->
    <xs:element name="authorizeServiceRequest">
        <xs:complexType>
            <xs:all>
                <xs:element name="requestId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="services" type="Services" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Array of services that are being requested for the account.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardInfo" type="com:EncryptedPayload"/>
                <xs:element name="correlationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Value linking pre-digitization messages generated during provisioning.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The party that requested the digitization. Required if tokens are assigned by MDES.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="walletId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Wallet Provider who requested the digitization. Only present when the token is provided to a
                            CardWallet Provider.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="paymentAppInstanceId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Payment App instance within a device that will be provisioned with a token. Only present when
                            supplied by a Wallet Provider.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="accountIdHash" type="xs:base64Binary" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>SHA-256 hash of the Cardholder's account ID with the Payment App Provider. Typically expected to be an email
                            address.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="mobileNumberSuffix" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The last few digits (typically four) of the device's mobile phone number.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceInfo" type="DeviceInfo" minOccurs="0"/>
                <xs:element name="walletProviderDecisioningInfo" type="WalletProviderDecisioningInfo" minOccurs="0"/>
                <xs:element name="activeTokenCount" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The number of active tokens that already exist for the PAN based on the token type. Secure Element and Cloud tokens
                            are counted together. Valid values are 0 to 99. A value of 99 means there are 99 or more active tokens. Tokens that have been
                            deleted from the wallet are excluded from the count.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="tokenType" type="TokenType"/>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="authorizeServiceResponse">
        <xs:complexType>
            <xs:all>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the response.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorCode" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error code for the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorDescription" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error description of the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="services">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="services" maxOccurs="unbounded" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Array of services for the account that the authorization decision applies to. Must be a subset of the
                                        services in
                                        the request object. Services that are not approved for the account will be omitted.
                                    </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="decision" type="Decision">
                    <xs:annotation>
                        <xs:documentation>
                            The authorization decision for the authorization of the requested services.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="activationMethods">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="activationMethods" type="ActivationMethod" minOccurs="0" maxOccurs="unbounded">
                                <xs:annotation>
                                    <xs:documentation>The activation methods to be used for this digitization. Return empty array if no methods are to be
                                        returned.
                                    </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="panSequenceNumber" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The pan sequence number for the card. Acceptable values are in the range 000-099.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="issuerProductConfigId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The unique Issuer identifier assigned to the product configuration in BPMS. It is provided for the Digitization
                            service only.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="encryptedPayload" type="com:EncryptedPayload" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Contains the encrypted AuthroizeServiceResponseData object.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cvcResponse" type="CVCResponse" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The result of the CVC2 validation performed against the value provided by the cardholder.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="avsResponse" type="AVSResponse" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The result of the address validation performed against the values provided by the cardholder.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The party that requested the digitization. Required if tokens are generated by external provider, not present
                            otherwise.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="CVCResponse">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MATCH">
                <xs:annotation>
                    <xs:documentation>Valid CVC2</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVALID">
                <xs:annotation>
                    <xs:documentation>Invalid CVC2</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_PROCESSED">
                <xs:annotation>
                    <xs:documentation>CVC2 was not processed (issuer temporarily unavailable).</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="AVSResponse">
        <xs:restriction base="xs:string">
            <xs:enumeration value="POSTAL_DOES_NOT_MATCH">
                <xs:annotation>
                    <xs:documentation>Address matches, postal code does not.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ADDRESS_AND_POSTAL_DO_NOT_MATCH">
                <xs:annotation>
                    <xs:documentation>Neither address nor postal code matches.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RETRY">
                <xs:annotation>
                    <xs:documentation>Retry, system unable to process.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AVS_NOT_SUPPORTED">
                <xs:annotation>
                    <xs:documentation>AVS currently not supported.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NO_DATA">
                <xs:annotation>
                    <xs:documentation>No data from issuer/Authorization Platform</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ADDRESS_DOES_NOT_MATCH">
                <xs:annotation>
                    <xs:documentation>W = For U.S. addresses, nine-digit postal code matches, address does not; for address outside the U.S., postal code
                        matches, address does not.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ADDRESS_AND_POSTAL_MATCH">
                <xs:annotation>
                    <xs:documentation>X = For U.S. addresses, ninedigit postal code and address matches; for addresses outside the U.S., postal code and address
                        match.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="US5_ADDRESS_AND_POSTAL_MATCH">
                <xs:annotation>
                    <xs:documentation>Y = For U.S. addresses, fivedigit postal code and address matches</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="US5_ ADDRESS_DOES_NOT_MATCH">
                <xs:annotation>
                    <xs:documentation>Z = For U.S. addresses, fivedigit postal code matches, address does not.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="notifyServiceActivatedRequest">
        <xs:complexType>
            <xs:all>
                <xs:element name="requestId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardAndToken" type="com:EncryptedPayload" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Contains the encrypted Card and token object.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deviceInfo" type="DeviceInfo" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Contains the encrypted Card and token object.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="correlationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Value linking pre-digitization messages generated during provisioning.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="tokenRequestorId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The party that requested the digitization.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="walletId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Wallet Provider who requested the digitization. Only present when the token is provided to a
                            CardWallet Provider.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="paymentAppInstanceId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Payment App instance within a device that will be provisioned with a token. Only present when
                            supplied by a Wallet Provider. Note - This may contain the identifier of the Secure Element or a mobile device for some programs.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="accountPanSuffix" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The last few digits (typically four) of the Account PAN being digitized.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="secureElementId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The identifier of the Secure Element to be provisioned with the token. Present only when the token is provisioned to a
                            Secure Element and when provided by the Wallet Provider
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="notifyServiceActivatedResponse">
        <xs:complexType>
            <xs:all>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the response.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorCode" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error code for the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorDescription" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error description of the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="notifyTokenUpdatedRequest">
        <xs:complexType>
            <xs:all>
                <xs:element name="requestId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="tokensData" type="tokensData" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Contains the Tokens which were updated.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="reasonCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The reason code for why the notification is being sent. This applies to all tokens in the Tokens array.
                            Must be one of:
                            Value
                            Meaning
                            STATUS_UPDATE
                            The status of the token has been changed.
                            REDIGITIZATION_COMPLETE
                            The token has been re-digitized to the device.
                            DELETED_FROM_CONSUMER_APP
                            The token has been deleted from the consumer application. The token may still be active.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>

            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="notifyTokenUpdatedResponse">
        <xs:complexType>
            <xs:all>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the response.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorCode" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error code for the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorDescription" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error description of the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="deliverActivationCodeRequest">
        <xs:complexType>
            <xs:all>
                <xs:element name="requestId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="tokenUniqueReference" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            Unique reference to the token to be designated when digitization is complete.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="correlationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>
                            Value linking pre-digitization messages generated during provisioning.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="activationCode" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            The Activation Code to be distributed for the digitization.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="expirationDateTime" type="xs:dateTime" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            The DateTime when the Activation Code is no longer valid.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="activationMethod" type="ActivationMethod">
                    <xs:annotation>
                        <xs:documentation>
                            The activation method selected by the Cardholder.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="deliverActivationCodeResponse">
        <xs:complexType>
            <xs:all>
                <xs:element name="responseHost" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The host that originated the request. Future calls in the same conversation may be routed to this host. Must be
                            provided as: host[:port][/contextRoot] where port and contextRoot are optional. If contextRoot is not provided, the default (per the
                            URL Scheme) is assumed and must be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="responseId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Unique identifier for the response.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorCode" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error code for the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorDescription" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Error description of the reason the operation failed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:all>
        </xs:complexType>
    </xs:element>



</xs:schema>
