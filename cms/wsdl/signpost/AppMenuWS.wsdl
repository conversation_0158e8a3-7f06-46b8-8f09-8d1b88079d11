<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="AppMenuWSDefinitions" targetNamespace="http://homecredit.net/ws-client"
                     xmlns=""
                     xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/"
                     xmlns:WL5G3N1="http://homecredit.net/ws-client"
                     xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>

    <xs:schema elementFormDefault="qualified" targetNamespace="http://homecredit.net/ws-client" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:include schemaLocation="AppMenuWS-ws-client.xsd" />
    </xs:schema>

  </WL5G3N0:types>
  <WL5G3N0:message name="getAppMenuItemsByClone">
    <WL5G3N0:part element="WL5G3N1:getAppMenuItemsByClone" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="getAppMenuItemsByCloneResponse">
    <WL5G3N0:part element="WL5G3N1:getAppMenuItemsByCloneResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="getAppMenuItems">
    <WL5G3N0:part element="WL5G3N1:getAppMenuItems" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="getAppMenuItemsResponse">
    <WL5G3N0:part element="WL5G3N1:getAppMenuItemsResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="AppMenuWS">
    <WL5G3N0:operation name="getAppMenuItemsByClone" parameterOrder="parameters">
      <WL5G3N0:input message="WL5G3N1:getAppMenuItemsByClone"/>
      <WL5G3N0:output message="WL5G3N1:getAppMenuItemsByCloneResponse"/>
    </WL5G3N0:operation>
    <WL5G3N0:operation name="getAppMenuItems" parameterOrder="parameters">
      <WL5G3N0:input message="WL5G3N1:getAppMenuItems"/>
      <WL5G3N0:output message="WL5G3N1:getAppMenuItemsResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="AppMenuWSPort" type="WL5G3N1:AppMenuWS">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="getAppMenuItemsByClone">
      <WL5G3N2:operation style="document"/>
      <WL5G3N0:input>
        <WL5G3N2:body parts="parameters" use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body parts="parameters" use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
    <WL5G3N0:operation name="getAppMenuItems">
      <WL5G3N2:operation style="document"/>
      <WL5G3N0:input>
        <WL5G3N2:body parts="parameters" use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body parts="parameters" use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="AppMenuWS">
    <WL5G3N0:port binding="WL5G3N1:AppMenuWSPort" name="AppMenuWSPort">
      <WL5G3N2:address location="https://app01.de99.np.ab:15106/homer-menu/bl/menu/AppMenuWS"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>