<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:tns="http://homecredit.net/airnet/signpost/ws"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://homecredit.net/airnet/signpost/ws">
  <wsdl:types>
    <xs:schema targetNamespace="http://homecredit.net/airnet/signpost/ws">
      <xs:include schemaLocation="SignpostWS.xsd"/>
    </xs:schema>
    <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
      <xs:include schemaLocation="../common/commonSoapFault.xsd"/>
    </xs:schema>
  </wsdl:types>

  <wsdl:message name="LoginRequest">
    <wsdl:part element="tns:LoginRequest" name="LoginRequest"/>
  </wsdl:message>
  <wsdl:message name="LoginResponse">
    <wsdl:part element="tns:LoginResponse" name="LoginResponse"/>
  </wsdl:message>
  <wsdl:message name="LoginFault">
    <wsdl:part element="commonFault:CoreFaultElement" name="LoginFault"/>
  </wsdl:message>

  <wsdl:portType name="SignpostWS">
    <wsdl:operation name="Login">
      <wsdl:input message="tns:LoginRequest" name="LoginRequest"/>
      <wsdl:output message="tns:LoginResponse" name="LoginResponse"/>
      <wsdl:fault message="tns:LoginFault" name="LoginFault"/>
    </wsdl:operation>
  </wsdl:portType>

  <wsdl:binding name="SignpostWSSoap11" type="tns:SignpostWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="Login">
      <soap:operation soapAction=""/>
      <wsdl:input name="LoginRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="LoginResponse">
        <soap:body use="literal"/>
      </wsdl:output>
      <wsdl:fault name="LoginFault">
        <soap:fault name="LoginFault" use="literal"/>
      </wsdl:fault>
    </wsdl:operation>
  </wsdl:binding>

  <wsdl:service name="SignpostWSService">
    <wsdl:port binding="tns:SignpostWSSoap11" name="SignpostWSSoap11">
      <soap:address location="http://localhost:10188/ws/SignpostWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>