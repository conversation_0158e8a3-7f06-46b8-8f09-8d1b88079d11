<?xml version="1.0" encoding="utf-8" ?>
<xsd:schema elementFormDefault="qualified"
            xmlns="http://homecredit.net/airnet/signpost/ws"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://homecredit.net/airnet/signpost/ws">

    <xsd:element name="LoginRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="environment" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="LoginResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="user" type="User"/>
                <xsd:element name="module" type="Module" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:complexType name="User">
        <xsd:sequence>
            <xsd:element name="username" type="xsd:string"/>
            <xsd:element name="employeeNumber" type="xsd:string"/>
            <xsd:element name="surname" type="xsd:string"/>
            <xsd:element name="givenName" type="xsd:string"/>
            <xsd:element name="commonName" type="xsd:string"/>
            <xsd:element name="role" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="Module">
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string"/>
            <xsd:element name="endpoint" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
