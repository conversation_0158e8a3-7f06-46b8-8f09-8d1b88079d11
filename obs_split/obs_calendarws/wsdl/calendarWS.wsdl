<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions
        xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        xmlns:tns="http://airbank.cz/obs/ws/calendarWS"
        targetNamespace="http://airbank.cz/obs/ws/calendarWS">
        
    <wsdl:types>
        <xsd:schema>
            <xsd:import namespace="http://airbank.cz/obs/ws/calendarWS" schemaLocation="calendarWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </wsdl:types>
    
    <wsdl:message name="addWorkingDaysRequest">
        <wsdl:part name="addWorkingDaysRequest" element="tns:addWorkingDaysRequest" />
    </wsdl:message>
    <wsdl:message name="addWorkingDaysResponse">
        <wsdl:part name="addWorkingDaysResponse" element="tns:addWorkingDaysResponse" />
    </wsdl:message>
    
    <wsdl:message name="AddNextWorkingDayAfterRequest">
        <wsdl:part name="AddNextWorkingDayAfterRequest" element="tns:addNextWorkingDayAfterRequest" />
    </wsdl:message>
    <wsdl:message name="AddNextWorkingDayAfterResponse">
        <wsdl:part name="AddNextWorkingDayAfterResponse" element="tns:addNextWorkingDayAfterResponse" />
    </wsdl:message>    
    
    <wsdl:message name="faultMessage">
        <wsdl:part name="faultMessage" element="com:ErrorsListType" />
    </wsdl:message>
        
    <wsdl:portType name="calendar">
        <wsdl:operation name="addWorkingDays">
            <wsdl:documentation>Returns the 'baseDate' plus 'shift' working days.
When 'shift' is zero and date is not a working date returns NEXT working day.

Assumptions:
logged in user - no

Faults (format - "code / attribute / value : description"):
GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
CLERR_NUMBER_IS_TOO_GREAT / shift / : Shift is greater then 300.
CLERR_NUMBER_IS_TOO_LOW / shift / : Shift is lower then -300.</wsdl:documentation>
          <wsdl:input message="tns:addWorkingDaysRequest" />
            <wsdl:output message="tns:addWorkingDaysResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>
    </wsdl:portType>
    
    <wsdl:binding name="calendarSOAP" type="tns:calendar">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="addWorkingDays">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
        
    <wsdl:service name="calendarWS">
        <wsdl:port binding="tns:calendarSOAP" name="calendarSOAP">
            <soap:address location="/ws/calendarWS" />
        </wsdl:port>
    </wsdl:service>
    
</wsdl:definitions>