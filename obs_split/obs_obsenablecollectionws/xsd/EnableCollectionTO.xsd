<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
	xmlns="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" version="1.0"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/">

	<include schemaLocation="Common.xsd"/>

	<complexType name="EnableCollectionTO">
		<sequence>
			<element name="idEnableCollection" type="long" minOccurs="0">
				<annotation>
					<documentation>primární klíč povolení inkasa</documentation>
				</annotation>
			</element>
			<element name="contraAccountNumber" type="string">
				<annotation>
					<documentation>číslo protiúčtu pro které chci povolit inkaso</documentation>
				</annotation>
			</element>
			<element name="contraAccountNumberPrefix" type="string" minOccurs="0">
				<annotation>
					<documentation>prefix protiúčtu</documentation>
				</annotation>
			</element>
			<element name="contraAccountBank" type="string">
				<annotation>
					<documentation>kód banky</documentation>
				</annotation>
			</element>
			<element name="limit" type="decimal">
				<annotation>
					<documentation>limit inkasa</documentation>
				</annotation>
			</element>
			<element name="name" type="string" minOccurs="0">
				<annotation>
					<documentation>název polovelní inkasa</documentation>
				</annotation>
			</element>
			<element name="validFrom" type="dateTime">
				<annotation>
					<documentation>platnost od</documentation>
				</annotation>
			</element>
			<element name="idCategory" type="long" minOccurs="0"/>
			<element name="enableType" type="tns:EnableType">
				<annotation>
					<documentation>typ schvalování inkasa</documentation>
				</annotation>
			</element>
			<element name="periodicityUnit" type="tns:EnableCollectionPeriodicityUnitType">
				<annotation>
					<documentation>období</documentation>
				</annotation>
			</element>
			<element name="validTo" type="date" minOccurs="0">
				<annotation>
					<documentation>plotnost do</documentation>
				</annotation>
			</element>
			<element name="limitCurrency" type="string">
				<annotation>
					<documentation>měna povolení inkasa</documentation>
				</annotation>
			</element>
			<element name="idBankAccountDebit" type="long">
				<annotation>
					<documentation>id zrojového účtu klineta z kterého se bude inkasovat</documentation>
				</annotation>
			</element>
			<element name="additionalInfo" type="tns:AdditionalInfoTO" minOccurs="0">
				<annotation>
					<documentation>Additional information for mobile application frontend purposes.</documentation>
				</annotation>
			</element>
			<element name="messageToSender" minOccurs="0">
				<annotation>
					<documentation>Zpráva pro odesílatele.</documentation>
				</annotation>
				<simpleType>
					<restriction base="string">
						<minLength value="0"></minLength>
						<maxLength value="140"></maxLength>
					</restriction>
				</simpleType>
			</element>
			<element name="doorDocumentID" type="long" minOccurs="0">
				<annotation>
					<documentation>Identifikátor dokumentu v DOOR</documentation>
				</annotation>
			</element>
			<element name="emailNotif" type="boolean" minOccurs="0">
				<annotation>
					<documentation>má sa pri zrealizovaní inkasa poslat emailova notifikacia?</documentation>
				</annotation>
			</element>
			<element name="mobilityDescription" type="string" minOccurs="0">
				<annotation>
					<documentation>Poznamka pri mobilite k predpise inkasa.</documentation>
				</annotation>
			</element>
			<element name="counterPartyId" type="long" minOccurs="0">
				<annotation>
					<documentation>ID protistrany</documentation>
				</annotation>
			</element>
			<element name="extRequestId" type="string" minOccurs="0">
				<annotation>
					<documentation>Identification of the external request from which the collection originated.</documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>

	<simpleType name="EnableType">
		<annotation>
			<documentation>typ schvalování inkasa</documentation>
		</annotation>
		<restriction base="string">
			<enumeration value="AUTOMATIC"></enumeration>
			<enumeration value="MANUAL"></enumeration>
		</restriction>
	</simpleType>

	<simpleType name="EnableCollectionPeriodicityUnitType">
		<restriction base="string">
			<enumeration value="MONTH"></enumeration>
			<enumeration value="QUARTER"></enumeration>
			<enumeration value="HALF_YEAR"></enumeration>
			<enumeration value="YEAR"></enumeration>
		</restriction>
	</simpleType>

	<simpleType name="NotifChannelType">
		<annotation>
			<documentation>
				kánály pro notifikaci k povolení inkasa
			</documentation>
		</annotation>
		<restriction base="string">
			<enumeration value="IBWO">
				<annotation>
					<documentation>pouze IB a WebOffice</documentation>
				</annotation></enumeration>
			<enumeration value="IBWO_EMAIL">
				<annotation>
					<documentation>IB, WebOffice a email</documentation>
				</annotation></enumeration>
			<enumeration value="IBWO_SMS">
				<annotation>
					<documentation>IB, WebOffice a sms</documentation>
				</annotation></enumeration>
		</restriction>
	</simpleType>

	<complexType name="EnableCollectionInstanceInfoTO">
		<sequence>
			<element name="idEnableCollection" type="long" />
			<element name="firstRealizedTransactionDate" type="date" minOccurs="0">
				<annotation>
					<documentation>
						datum (nejstarší) první realizované transakce pro daný předpis
					</documentation>
				</annotation>
			</element>
			<element name="realizedTransactionCount" type="long">
				<annotation>
					<documentation>
						počet již realizovaných transakcí pro daný předpis
					</documentation>
				</annotation>
			</element>
			<element name="lastRealizedTransactionDate" type="date" minOccurs="0">
				<annotation>
					<documentation>
						datum (nejnovější) poslední realizované transakce pro daný předpis
					</documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>

</schema>
