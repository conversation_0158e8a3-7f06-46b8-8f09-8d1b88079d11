<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://obs.airbank.cz/ws/OBSInsuranceWS"
            xmlns="http://obs.airbank.cz/ws/OBSInsuranceWS"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

    <xsd:complexType name="VinculationRequestItem">
        <xsd:sequence>
            <xsd:element name="contractId" type="xsd:long"/>
            <xsd:element name="riskType" type="RiskType"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="getVinculationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="item" type="VinculationRequestItem" minOccurs="1" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="Vinculation">
        <xsd:sequence>
            <xsd:element name="contractId" type="xsd:long" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="vinculationAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="vinculationDate" type="xsd:date" minOccurs="1" maxOccurs="1"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="getVinculationResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="vinculation" type="Vinculation" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getInsuranceDebtRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contractId" type="xsd:long" minOccurs="1" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getInsuranceDebtResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="totalDebtAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:simpleType name="InsuranceProductCode">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="INS_PP"/>
            <xsd:enumeration value="INS_CP"/>
            <xsd:enumeration value="INS_PV"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:element name="insuranceCreationEventRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contractNumber" type="xsd:string"/>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="insuranceNumber" type="xsd:string"/>
                <xsd:element name="insuranceStart" type="xsd:date"/>
                <xsd:element name="insuranceEnd" type="xsd:date" minOccurs="0"/>
                <xsd:element name="productCode" type="InsuranceProductCode"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="insuranceCreationEventResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="insuranceTerminationEventRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="insuranceNumber" type="xsd:string"/>
                <xsd:element name="productCode" type="InsuranceProductCode"/>
                <xsd:element name="insuranceEnd" type="xsd:date"/>
                <xsd:element name="internalTerminationReason" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            //zruseni pojisteni klientem
                            CLIENT_INSURANCE_TERMINATION,

                            //Zruseni GC klientem
                            CLIENT_GENERAL_CONTRACT_TERMINATION,

                            //Odstoupeni klientem
                            CLIENT_WITHDRAWAL,

                            //Ukonceni BU klientem
                            CLIENT_ACCOUNT_CLOSE,

                            //Ukonceni GC
                            BANK_GENERAL_CONTRACT_TERMINATION,

                            //Smrt
                            BANK_DEATH,

                            //Max vek
                            BANK_MAX_AGE,

                            //dluzne pojistne
                            BANK_UNPAID_PREMIUM,

                            //Ukonceni BU bankou
                            BANK_ACCOUNT_CLOSE,

                            //Odstoupeni
                            CARDIF_WITHDRAWAL,

                            //Odmitnuti plneni
                            CARDIF_REFUSE,

                            //smrt
                            CARDIF_DEATH,

                            //invalidita
                            CARDIF_PERMANENT_INVALIDITY,

                            //Odvolani souhlasu
                            CARDIF_AGREEMENT_CANCELLATION
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="insuranceTerminationEventResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="paymentProtectionCreationEventRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contractNumber" type="xsd:string"/>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="insuranceNumber" type="xsd:string"/>
                <xsd:element name="loanId" type="xsd:long" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="paymentProtectionCreationEventResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="paymentProtectionTerminationEventRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="insuranceNumber" type="xsd:string"/>
                <xsd:element name="insuranceEnd" type="xsd:date"/>
                <xsd:element name="internalTerminationReason" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            //TODO XR-6882
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="paymentProtectionTerminationEventResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="saveMeetingRecordRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="documentPdfUuid" type="xsd:string"/>
                <xsd:element name="documentHtmlUuid" type="xsd:string" minOccurs="0"/>
                <xsd:element name="insuranceApplicationId" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="saveMeetingRecordResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:simpleType name="RiskType">
        <xsd:annotation>
            <xsd:documentation>Insurance risk type.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SICK_LEAVE"/>
            <xsd:enumeration value="JOB_LOSS"/>
            <xsd:enumeration value="DEATH"/>
            <xsd:enumeration value="TOTAL_PERMANENT_DISABILITY"/>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
