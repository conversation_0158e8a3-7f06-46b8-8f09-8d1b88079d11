<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
        xmlns="http://schemas.xmlsoap.org/wsdl/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:tns="http://airbank.cz/obs/ws/rewardWS"
        targetNamespace="http://airbank.cz/obs/ws/rewardWS">

    <types>
        <xsd:schema>
            <xsd:import namespace="http://airbank.cz/obs/ws/rewardWS"
                        schemaLocation="rewardWS.xsd"/>
        </xsd:schema>
    </types>

    <message name="rewardRequest">
        <part name="rewardRequest" element="tns:rewardRequest"/>
    </message>
    <message name="rewardResponse">
        <part name="rewardResponse" element="tns:rewardResponse"/>
    </message>

    <message name="getRewardPayoutStatusRequest">
        <part name="getRewardPayoutStatusRequest" element="tns:getRewardPayoutStatusRequest"/>
    </message>
    <message name="getRewardPayoutStatusResponse">
        <part name="getRewardPayoutStatusResponse" element="tns:getRewardPayoutStatusResponse"/>
    </message>

    <portType name="reward">
        <operation name="reward">
            <input message="tns:rewardRequest"/>
            <output message="tns:rewardResponse"/>
        </operation>
        <operation name="getRewardPayoutStatus">
            <input message="tns:getRewardPayoutStatusRequest"/>
            <output message="tns:getRewardPayoutStatusResponse"/>
        </operation>
    </portType>

    <binding name="rewardSOAP" type="tns:reward">
        <soap:binding style="document"
                      transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="reward">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getRewardPayoutStatus">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>

    <service name="rewardWS">
        <port binding="tns:rewardSOAP" name="rewardSOAP">
            <soap:address location="/ws/rewardWS"/>
        </port>
    </service>
</definitions>
