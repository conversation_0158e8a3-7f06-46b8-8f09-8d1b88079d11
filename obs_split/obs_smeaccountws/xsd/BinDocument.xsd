<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
    elementFormDefault="qualified" version="1.0">


    <include schemaLocation="CardsTO.xsd" />
    <include schemaLocation="ContractTO.xsd" />

    <complexType name="BinDocumentIdentType">
        <sequence>
            <element name="idBinDocument" type="string" />
        </sequence>
    </complexType>

    <complexType name="BinDocument">
        <sequence>
            <element name="ident" type="tns:BinDocumentIdentType" />
            <element name="documentName" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        Popisný název binárního dokumentu. Např. RS by
                        by<PERSON>v<PERSON> smlouva.
                    </documentation>
                </annotation>
            </element>
            <element name="fileName" type="string" />
            <element name="extension" type="string" />
            <element name="type" type="tns:BinDocumentType">
                <annotation>
                    <documentation>typ dokumentu (napr. pdf, dochtml, other (nespecifikovany), txt, ...)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DocumentStatusRequestEntity">
        <sequence>
            <element name="envelopeId" type="long" />
            <element name="cuid" type="long">
                <annotation>
                    <documentation>client id</documentation>
                </annotation>
            </element>
            <element name="channel" type="tns:ChannelCodeType" minOccurs="0">
               <annotation>
                   <documentation>Typ kanalu - IB, BRANCH, ECC, ICC</documentation>
               </annotation>
            </element>
            <element name="createApplicationDate" type="date">
               <annotation>
                   <documentation>date of creating application</documentation>
               </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DocumentStatusEntity">
        <annotation>
            <documentation>
                The data object is used both for request and response data transfer.
                So some fields are mandatory for responce part only and some for request part only
            </documentation>
        </annotation>
        <sequence>
            <element name="group" type="string" minOccurs="0" />
            <element name="groupRelation" type="string" minOccurs="0">
                <annotation>
                    <documentation>vztah se skupinou</documentation>
                </annotation>
            </element>
            <element name="documentType" type="string">
                <annotation>
                    <documentation>typ dokumentu (napr. pdf, dochtml, other (nespecifikovany), txt, ...)</documentation>
                </annotation>
            </element>
            <element name="status" type="tns:BinDocumentStatus">
                <annotation>
                    <documentation>stav dokumentu (napr. overeno, neovereno, zamitnuto ...)</documentation>
                </annotation>
            </element>
            <element name="operatorNote" type="string" minOccurs="0">
                <annotation>
                    <documentation>poznamka operatora - call centra / ICC / ECC etc</documentation>
                </annotation>
            </element>
            <element name="clientNote" type="string" minOccurs="0">
                <annotation>
                    <documentation>poznamka pobocnika</documentation>
                </annotation>
            </element>
            <element name="idPersDocument" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                        Id pers doc (binárního) souboru  - muze byt prazdny - pokud je prazdny v OBS se zalozi novy dokument
                    </documentation>
                </annotation>
            </element>
            <element name="creationDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>creation date of document</documentation>
                </annotation>
            </element>
            <element name="manageDocumentIds" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>pouziva se jen u requestu, nikoli u responsu / IDs managed dokumentu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="OtherDocumentsListEntity">
        <sequence>
            <element name="clientNote" type="string">
                <annotation>
                    <documentation>poznamka pobocnika</documentation>
                </annotation>
            </element>
            <element name="documentIds" type="string" maxOccurs="unbounded">
                <annotation>
                    <documentation>ID dokumentu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="BinDocumentType">
        <annotation>
            <documentation>
                Typ obsahu binárního dokumentu
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PDF" />
            <enumeration value="TXT" />
            <enumeration value="OTHER">
                <annotation>
                    <documentation>Značí jakýkoliv jiný typ binárního dokumentu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CSV" />
            <enumeration value="DOCHTML">
                <annotation>
                    <documentation>html dokument</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="BinDocumentStatus">
        <annotation>
            <documentation>
                Stav binárního dokumentu
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="VERIFIED"/>
            <enumeration value="NOT_VERIFIED"/>
            <enumeration value="VERIFIED_NOK"/>
            <enumeration value="REJECTED"/>
        </restriction>
    </simpleType>

    <complexType name="ReportParameter" abstract="true">
        <sequence>
            <element name="name" type="string">
                <annotation>
                    <documentation>name of parameter</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="StringParameter">
        <complexContent>
            <extension base="tns:ReportParameter">
                <sequence>
                    <element name="value" type="string">
                        <annotation>
                            <documentation>string value of parameter</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LongParameter">
        <complexContent>
            <extension base="tns:ReportParameter">
                <sequence>
                    <element name="value" type="long">
                        <annotation>
                            <documentation>long value of parameter</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DoubleParameter">
        <complexContent>
            <extension base="tns:ReportParameter">
                <sequence>
                    <element name="value" type="double">
                        <annotation>
                            <documentation>double value of parameter</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DateParameter">
        <complexContent>
            <extension base="tns:ReportParameter">
                <sequence>
                    <element name="value" type="date">
                        <annotation>
                            <documentation>date value of parameter</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="RecoverDocumentsResult">
        <annotation>
            <documentation>result of recover documents</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="OK"/>
            <enumeration value="NO_DOCUMENT_FOUND"/>
        </restriction>
    </simpleType>

    <simpleType name="DocumentKindTO">
        <restriction base="string">
            <enumeration value="CONTRACTS"/>
            <enumeration value="DOCUMENTS" />
            <enumeration value="OTHERS"/>
            <enumeration value="ALL"/>
        </restriction>
    </simpleType>

    <complexType name="Courier">
        <sequence>
            <element name="status" minOccurs="0" type="tns:CourierStatusTO">
                <annotation>
                    <documentation>state of the document at courier's</documentation>
                </annotation>
            </element>
            <element name="id" minOccurs="0" type="long" />
            <element name="name" minOccurs="0" type="string" />
            <element name="company" minOccurs="0" type="string" />
        </sequence>
    </complexType>

    <complexType name="Receipt">
        <sequence>
            <element name="channel" minOccurs="0" type="tns:DeliveryWayTO">
                <annotation>
                    <documentation>channel by which the document was delivered</documentation>
                </annotation>
            </element>
            <element name="date" minOccurs="0" type="dateTime">
                <annotation>
                    <documentation>date when the document was received </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PersDocAttachment">
        <sequence>
            <element name="id" type="string" />
            <element name="validFrom" type="dateTime" />
            <element name="validTo" type="dateTime" />
        </sequence>
    </complexType>

    <complexType name="PersonalizedDocument">
        <sequence>
            <element name="id" type="long" />
            <element name="barCode" minOccurs="0" type="string">
                <annotation>
                    <documentation>search code</documentation>
                </annotation>
            </element>
            <element name="type" type="string" />
            <element name="status" type="tns:PersDocStatusType">
                <annotation>
                    <documentation>status of the document</documentation>
                </annotation>
            </element>
            <element name="sendChannel" minOccurs="0" type="tns:DeliveryWayTO">
                <annotation>
                    <documentation>channel by which the document was sent</documentation>
                </annotation>
            </element>
            <element name="receipt" minOccurs="0" type="tns:Receipt" />
            <element name="cancelReason" minOccurs="0" type="tns:CancelReasonTO" />
            <element name="undeliverabilityReason" minOccurs="0" type="tns:UndeliverabilityReasonTO" />
            <element name="courier" minOccurs="0" type="tns:Courier" />
            <element name="completionId" minOccurs="0" type="long" />
            <element name="attachment" type="tns:PersDocAttachment" minOccurs="0" maxOccurs="unbounded" />

            <element name="documentName" type="string" minOccurs="0"/>
            <element name="productNumber" type="string" minOccurs="0"/>
        </sequence>
    </complexType>

    <simpleType name="DeliveryWayTO">
        <restriction base="string">
            <enumeration value="BRANCH" />
            <enumeration value="EMAIL" />
            <enumeration value="EMAILCH" />
            <enumeration value="EMAILDP" />
            <enumeration value="EMAILPR" />
            <enumeration value="IB" />
            <enumeration value="MESSENGER" />
            <enumeration value="POST" />
            <enumeration value="TB" />
            <enumeration value="SPB" />
        </restriction>
    </simpleType>

    <simpleType name="CancelReasonTO">
        <restriction base="string">
            <enumeration value="RSNOINTEREST"/>
            <enumeration value="NOCONTACT"/>
            <enumeration value="RSLOST"/>
            <enumeration value="RSDEVALUED"/>
            <enumeration value="FORMALFAULT"/>
            <enumeration value="RSNOTDELIVERED"/>
            <enumeration value="NOTASSIGNED"/>
            <enumeration value="NOTREADABLE"/>
            <enumeration value="OTHER"/>
        </restriction>
    </simpleType>

    <simpleType name="UndeliverabilityReasonTO">
        <restriction base="string">
            <enumeration value="NONDELIVERYREASON_1">
                <annotation>
                    <documentation>Addressy is not known / Adresát neznámý</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NONDELIVERYREASON_2">
                <annotation>
                    <documentation>Moved without sharing new address / Odstěhoval se bez udání adresy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NONDELIVERYREASON_3">
                <annotation>
                    <documentation>Client died / Klient zemřel</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NONDELIVERYREASON_4">
                <annotation>
                    <documentation>Not picked-up till due date / V úložní době nevyzvednuto</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="CourierStatusTO">
        <restriction base="string">
            <enumeration value="SIGNED"/>
            <enumeration value="DELIVERED"/>
            <enumeration value="REJECTED"/>
            <enumeration value="UNDELIVERED"/>
            <enumeration value="ERROR"/>
        </restriction>
    </simpleType>
</schema>
