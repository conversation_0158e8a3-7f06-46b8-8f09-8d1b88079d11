<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions name="SmeCardholderWS"
             targetNamespace="http://airbank.cz/obs/ws/SmeCardholderWS/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://airbank.cz/obs/ws/SmeCardholderWS/"
             xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/SmeCardholderWS/">
            <xsd:include schemaLocation="SmeCardholderWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="CreateContractDocumentRequest">
        <part name="CreateContractDocumentRequest" element="tns:CreateContractDocumentRequest"/>
    </message>

    <message name="CreateContractDocumentResponse">
        <part name="CreateContractDocumentResponse" element="tns:CreateContractDocumentResponse"/>
    </message>

    <message name="ActivateCardholderRequest">
        <part name="ActivateCardholderRequest" element="tns:ActivateCardholderRequest"/>
    </message>

    <message name="ActivateCardholderResponse">
        <part name="ActivateCardholderResponse" element="tns:ActivateCardholderResponse"/>
    </message>

    <message name="TerminateCardholderRequest">
        <part name="TerminateCardholderRequest" element="tns:TerminateCardholderRequest"/>
    </message>

    <message name="TerminateCardholderResponse">
        <part name="TerminateCardholderResponse" element="tns:TerminateCardholderResponse"/>
    </message>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <portType name="SmeCardholderWS">
        <operation name="CreateContractDocument">
            <documentation>
                Create documents for new holder
            </documentation>
            <input message="tns:CreateContractDocumentRequest"/>
            <output message="tns:CreateContractDocumentResponse"/>
        </operation>
        <operation name="ActivateCardholder">
            <documentation>
                Activate new cardholder
            </documentation>
            <input message="tns:ActivateCardholderRequest"/>
            <output message="tns:ActivateCardholderResponse"/>
        </operation>
        <operation name="TerminateCardholder">
            <documentation>
                Terminate cardholder
            </documentation>
            <input message="tns:TerminateCardholderRequest"/>
            <output message="tns:TerminateCardholderResponse"/>
        </operation>
    </portType>

    <binding name="SmeCardholderWSSOAP" type="tns:SmeCardholderWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="CreateContractDocument">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="ActivateCardholder">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="TerminateCardholder">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>

    <service name="SmeCardholderWS">
        <port binding="tns:SmeCardholderWSSOAP" name="SmeCardholderWSSOAP">
            <soap:address location="/ws/SmeCardholderWS"/>
        </port>
    </service>

</definitions>
