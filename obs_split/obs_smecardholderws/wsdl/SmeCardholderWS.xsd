<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/SmeCardholderWS/"
        xmlns:tns="http://airbank.cz/obs/ws/SmeCardholderWS/"
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:a="http://airbank.cz/obs/ws/SmeApplication"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/">

    <import namespace="http://airbank.cz/obs/ws/SmeApplication" schemaLocation="../xsd/SmeApplication.xsd"/>
    <import schemaLocation="../xsd/ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>

    <element name="CreateContractDocumentRequest">
        <complexType>
            <sequence>
                <element name="cuidOwner" type="long">
                    <annotation>
                        <documentation>Cuid of SME customer</documentation>
                    </annotation>
                </element>
                <element name="cuidCardholder" type="long">
                    <annotation>
                        <documentation>Cuid of new cardholder</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="CMSCardID" type="long">
                    <annotation>
                        <documentation>ID of card in CMS</documentation>
                    </annotation>
                </element>
                <element name="appendixNumber" type="long">
                    <annotation>
                        <documentation>Appendix number</documentation>
                    </annotation>
                </element>
                <element name="envelopeID" type="long">
                    <annotation>
                        <documentation>ID of envelope</documentation>
                    </annotation>
                </element>
                <element name="application" type="a:Application" minOccurs="2" maxOccurs="2">
                    <annotation>
                        <documentation>collection of two applications</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="CreateContractDocumentResponse">
        <complexType>
            <sequence>
                <element name="completion" type="com:CreateContractDocumentCompletionResTO" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Created completions</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="ActivateCardholderRequest">
        <complexType>
            <sequence>
                <element name="cuidCardholder" type="long">
                    <annotation>
                        <documentation>Cuid of new cardholder</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="envelopeID" type="long">
                    <annotation>
                        <documentation>ID of envelope</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="ActivateCardholderResponse">
        <complexType>
            <sequence>
                <element name="activationResult" type="boolean">
                    <annotation>
                        <documentation>true if activation was successful, otherwise false</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="TerminateCardholderRequest">
        <complexType>
            <sequence>
                <element name="cuidCardholder" type="long">
                    <annotation>
                        <documentation>Cuid of cardholder</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="TerminateCardholderResponse">
        <complexType>
            <sequence>
                <element name="terminationResult" type="boolean">
                    <annotation>
                        <documentation>true if termination was successful, otherwise false</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
