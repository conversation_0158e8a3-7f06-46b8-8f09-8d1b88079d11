<?xml version="1.0" encoding="UTF-8"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/contract"
    xmlns:co="http://arbes.com/ib/core/ppf/ws/common/"
    targetNamespace="http://airbank.cz/obs/ws/contract">

    <import schemaLocation="ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />

    <complexType name="Application">
        <sequence>
            <element name="applicationId" type="long" />
            <element name="envelopeId" type="long" />
            <element name="creationDate" type="date" />
        </sequence>
    </complexType>

    <complexType name="ContractOwner">
        <sequence>
            <element name="cuid" type="long" />
            <element name="credentials" type="tns:Credentials" minOccurs="0" />
        </sequence>
    </complexType>

    <complexType name="Credentials">
        <sequence>
            <element name="username" type="string" />
            <element name="generatePassword" type="boolean" />
        </sequence>
    </complexType>

    <complexType name="LegalRequirements">
        <sequence>
            <element name="businessRelationshipPurpose" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>
                        Client to bank relation purposes.
                        Multiple codes from BUSINESS_RELATIONSHIP_PURPOSE
                        MDM code list.
                    </documentation>
                </annotation>
            </element>
            <element name="incomeSources" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>
                        Client future income sources.
                        Multiple codes from SOURCE_OF_INCOME MDM code list.
                    </documentation>
                </annotation>
            </element>
            <element name="savingsSources" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>
                        Client future savings sources.
                        Multiple codes from SOURCE_OF_SAVINGS MDM code list.
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CISPPermission">
        <sequence>
            <element name="id" type="long" />
            <element name="appId" type="string" />
            <element name="granted" type="dateTime" />
            <element name="lastUsed" type="dateTime" />
        </sequence>
    </complexType>

    <simpleType name="Status">
        <restriction base="string">
            <enumeration value="PROPOSAL" />
            <enumeration value="ACTIVE" />
            <enumeration value="ENDED" />
            <enumeration value="CANCELLED" />
        </restriction>
    </simpleType>

    <simpleType name="CustomerRole">
        <restriction base="string">
            <enumeration value="OWNER" />
            <enumeration value="DISPONENT" />
            <enumeration value="CARD_HOLDER" />
            <enumeration value="ENTITLED" />
        </restriction>
    </simpleType>

    <complexType name="CustomerRelation">
        <annotation>
            <documentation>Customer to General Contract relation</documentation>
        </annotation>
        <sequence>
            <element name="role" type="tns:CustomerRole" />
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>date which indicates when relation to general contract appeared</documentation>
                </annotation>
            </element>
            <element name="validTo" minOccurs="0" type="dateTime">
                <annotation>
                    <documentation>date which indicates when relation to general contract finished</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="Signature">
        <sequence>
            <element name="date" type="dateTime" />
            <element name="place" type="string" minOccurs="0" />
        </sequence>
    </complexType>

    <simpleType name="ActivationReason">
        <restriction base="string">
            <enumeration value="PK">
                <annotation>
                    <documentation>Courier sign</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PP">
                <annotation>
                    <documentation>Branch sign</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LP">
                <annotation>
                    <documentation>Branch login</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PI">
                <annotation>
                    <documentation>Logged in identification</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BI">
                <annotation>
                    <documentation>BankID identification</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ECI">
                <annotation>
                    <documentation>Existing contract identification</documentation>
                </annotation>
            </enumeration>
            <enumeration value="O2">
                <annotation>
                    <documentation>O2 identification</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="Activation">
        <sequence>
            <element name="reason" type="tns:ActivationReason" />
            <element name="operator" type="string" minOccurs="0">
                <annotation>
                    <documentation>Employee number</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PaymentDetail">
        <sequence>
            <element name="accountNumber" type="string" />
            <element name="bankCode" type="string" />
        </sequence>
    </complexType>

    <complexType name="GeneralContractEntity">
        <annotation>
            <documentation>The entyty (snapshot) of general contract</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long" />
            <element name="contractNumber" type="string" />
            <element name="completionId" type="long" />
            <element name="ownerName" type="string" />
            <element name="status" type="tns:Status" />
            <element name="autoCompletionAllowed" type="boolean">
                <annotation>
                    <documentation>
                        Pro tuto smlouvu je povelena automaticka kompletace zadosti
                    </documentation>
                </annotation>
            </element>
            <element name="customerRelation" type="tns:CustomerRelation" />
            <element name="signature" type="tns:Signature" minOccurs="0" />
            <element name="activation" type="tns:Activation" minOccurs="0" />
            <element name="firstPayment" type="tns:PaymentDetail" minOccurs="0" />
        </sequence>
    </complexType>


    <complexType name="ContractSignature">
        <sequence>
            <element name="date" type="dateTime" />
            <element name="channel" type="co:SignChannelType" minOccurs="0" />
            <element name="authId" type="string" minOccurs="0" />
        </sequence>
    </complexType>

    <complexType name="SignDetail">
        <sequence>
            <element name="channelSign">
                <annotation>
                    <documentation>Channel of signature</documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="IB" />
                        <enumeration value="SPB" />
                        <enumeration value="TB" />
                        <enumeration value="BRANCH" />
                    </restriction>
                </simpleType>
            </element>
            <element name="signDate" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Date of signature</documentation>
                </annotation>
            </element>
            <element name="authId" type="string" minOccurs="0">
                <annotation>
                    <documentation>External authorization ID</documentation>
                </annotation>
            </element>
            <element name="uuid" type="string" minOccurs="0">
                <annotation>
                    <documentation>UUID of signed binary document</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="BranchDetail">
        <sequence>
            <element name="branchId" type="string" minOccurs="0">
                <annotation>
                    <documentation>Branch ID</documentation>
                </annotation>
            </element>
            <element name="branchAssistantId " type="string">
                <annotation>
                    <documentation>Branch assistant ID / Call center operator ID</documentation>
                </annotation>
            </element>
            <element name="signType">
                <annotation>
                    <documentation>Type of signature</documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="BLUE_SIGN" />
                        <enumeration value="SIGNPAD" />
                        <enumeration value="VOICE_CONFIRMATION" />
                        <enumeration value="PWD" />
                        <enumeration value="OTP" />
                        <enumeration value="SWT" />
                    </restriction>
                </simpleType>
            </element>
        </sequence>
    </complexType>
</schema>