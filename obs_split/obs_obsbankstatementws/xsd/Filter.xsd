<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" xmlns:fs="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" xmlns:com="http://arbes.com/ib/core/ppf/ws/common/">

  <xs:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="Common.xsd"/>

  <xs:annotation>
		<xs:documentation>Definition of types for select data filters.</xs:documentation>
	</xs:annotation>
	<xs:complexType name="SelectFilter">
		<xs:annotation>
			<xs:documentation>SelectFilter defines filter criteria for data selection.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="filterExpression" type="FilterExpression" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Definition of filter criteria based on boolean expression.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="maxRecords" type="xs:int" minOccurs="1" maxOccurs="1" nillable="true">
				<xs:annotation>
					<xs:documentation>&lt;1, 1000&gt;
maximal number of records to be returned.
1000 is reasonable technological limit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="sortedAttribute" type="SortedAttribute" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Definitin of sort criteria for returned data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Between">
		<xs:annotation>
			<xs:documentation>concerete type of typedValueForm is the same as concrete type of typedValueTo</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValueFrom" type="TypedValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="typedValueTo" type="TypedValue" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="DateValue">
		<xs:complexContent>
			<xs:extension base="TypedValue">
				<xs:sequence>
					<xs:element name="value" type="xs:date" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="CompoundFilter" abstract="true">
		<xs:annotation>
			<xs:documentation>Abstract type for all logical operators.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FilterExpression">
				<xs:sequence/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="StringValue">
		<xs:complexContent>
			<xs:extension base="TypedValue">
				<xs:sequence>
					<xs:element name="value" type="xs:string" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="GreaterThan">
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValue" type="TypedValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="AttributeFilter" abstract="true">
		<xs:annotation>
			<xs:documentation>Abstract type for all value filter operators.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FilterExpression">
				<xs:sequence>
					<xs:element name="attributeName" type="xs:string" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>attribute name</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="Like">
		<xs:annotation>
			<xs:documentation>only String valueType supported.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="stringValue" type="StringValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="SmallerEqThan">
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValue" type="TypedValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="SmallerThan">
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValue" type="TypedValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="IntegerValue">
		<xs:complexContent>
			<xs:extension base="TypedValue">
				<xs:sequence>
					<xs:element name="value" type="xs:integer" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	
	<xs:complexType name="SupplierIdValue">
		<xs:complexContent>
			<xs:extension base="TypedValue">
				<xs:sequence>
					<xs:element name="value" type="com:SupplierId" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	
	<xs:complexType name="And">
		<xs:complexContent>
			<xs:extension base="CompoundFilter">
				<xs:sequence>					
					<xs:element name="operand1" type="FilterExpression" minOccurs="1" maxOccurs="1"/>
					<xs:element name="operand2" type="FilterExpression" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="FilterExpression" abstract="true">
		<xs:annotation>
			<xs:documentation>Abstract type for filter expression</xs:documentation>
		</xs:annotation>
		<xs:sequence/>
	</xs:complexType>
	<xs:complexType name="In">
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValue" type="TypedValue" minOccurs="1" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="GreaterEqThan">
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValue" type="TypedValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="SortedAttribute">
		<xs:annotation>
			<xs:documentation>Definition of ordering type for single attribute.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="attributeName" type="xs:string" minOccurs="1" maxOccurs="1"/>
			<xs:element name="orderType" type="OrderType" minOccurs="1" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TypedValue" abstract="true">
		<xs:annotation>
			<xs:documentation>Abstract class for all values of different type.</xs:documentation>
		</xs:annotation>
		<xs:sequence/>
	</xs:complexType>
	<xs:complexType name="Equal">
		<xs:complexContent>
			<xs:extension base="AttributeFilter">
				<xs:sequence>
					<xs:element name="typedValue" type="TypedValue" minOccurs="1" maxOccurs="1">
						<xs:annotation>
							<xs:documentation>value of specific type</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="Not">
		<xs:complexContent>
			<xs:extension base="CompoundFilter">
				<xs:sequence>
					<xs:element name="operand" type="FilterExpression" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="DecimalValue">
		<xs:complexContent>
			<xs:extension base="TypedValue">
				<xs:sequence>
					<xs:element name="value" type="xs:decimal" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="OrderType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="asc"/>
			<xs:enumeration value="desc"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="Or">
		<xs:complexContent>
			<xs:extension base="CompoundFilter">
				<xs:sequence>					
					<xs:element name="operand1" type="FilterExpression" minOccurs="1" maxOccurs="1"/>
					<xs:element name="operand2" type="FilterExpression" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="DateTimeValue">
		<xs:complexContent>
			<xs:extension base="TypedValue">
				<xs:sequence>
					<xs:element name="value" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
</xs:schema>
