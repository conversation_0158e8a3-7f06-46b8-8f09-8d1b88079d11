<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsBankStatementWS/"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:bs="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:fs="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsBankStatementWS/"
>

    <import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" />
    <import schemaLocation="../xsd/BankStatementTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />

    <element name="getBankStatementsRequest">
        <complexType>
            <sequence>
                <element name="idBankStatement" type="long" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <element name="getBankStatementsResponse">
        <complexType>
            <sequence>
                <element name="bankStatement" type="bs:BankStatementTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getBankStatementDataRequest">
        <complexType>
            <sequence>
                <choice>
                    <element name="bankStatementIdentification" type="tns:BankStatementIdentification" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Jednoznacna "business" identifikace vypisu</documentation>
                        </annotation>
                    </element>
                    <element name="idBankStatement" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Interni identifikace výpisu</documentation>
                        </annotation>
                    </element>
                </choice>
            </sequence>
        </complexType>
    </element>

    <element name="getBankStatementDataResponse">
        <complexType>
            <sequence>
                <element name="header" type="bs:StatementHeader" />
                <element name="posting" type="bs:Posting" minOccurs="0" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <complexType name="BankStatementIdentification">
        <annotation>
            <documentation>Jednoznacna business identifikace jednoho vypisu</documentation>
        </annotation>
        <sequence>
            <element name="accountNumber" type="string"/>
            <element name="year" type="int"/>
            <element name="periodicityUnit" type="bs:StatementFrequency" />
            <element name="ordinalNumber" type="int" />
        </sequence>
    </complexType>

    <element name="findBankStatementRequest">
        <annotation>
            <documentation>filtrační podmínky:

                IDBANKACCOUNT - primární klíč účtu k jakému jsou požadované výpisy - povinna podminka, pokud je pouzit securityContext
                ACCOUNTNUMBER - cislo uctu, ke kteremu jsou pozadovane vypisy - povinna podminka, pokud neni pouzit securityContext
                Nemuzou byt zaroven filtry na IDBANKACCOUNT a ACCOUNTNUMBER.

                FREQUENCY - frekvence výpisu viz. simpleType StatementFrequency - nepovinne
                STATEMENTFROM - pocatecni den vypisu - nepovinne - lze filtrovat Equal, SmallerThan, GreaterThan, SmallerEqThan, GreaterEqThan
                STATEMENTTO - koncovy den vypisu - nepovinne - lze filtrovat Equal, SmallerThan, GreaterThan, SmallerEqThan, GreaterEqThan

                vazba mezi těmito atributy pouze AND

                řazení:
                FREQUENCY
                STATEMENTFROM
                STATEMENTTO
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="filter" type="fs:SelectFilter" />
            </sequence>
        </complexType>
    </element>

    <element name="findBankStatementResponse">
        <complexType>
            <sequence>
                <element name="idBankStatement" type="long" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="createBankStatementRequest">
        <complexType>
            <sequence>
                <element name="accountNumber" type="string" />
                <element name="dateFrom" type="date" />
                <element name="dateTo" type="date" />
            </sequence>
        </complexType>
    </element>

    <element name="createBankStatementResponse">
        <complexType>
            <sequence>
                <element name="uuid" type="string" />
            </sequence>
        </complexType>
    </element>

    <complexType name="StatementFor3rdPartyTO">
        <annotation>
            <documentation>
                definice mimořádného vypisu pro třetí stranu
                - itemNo: identifikace požadavku na výpis
                - accountNumber: číslo bankovního účtu
                - dateFrom: počáteční datum intervalu výpisu
                - dateTo: konečné datum intervalu výpisu
            </documentation>
        </annotation>
        <sequence>
            <element name="itemNo" type="string" />
            <element name="accountNumber" type="string" />
            <element name="dateFrom" type="date" />
            <element name="dateTo" type="date" />
        </sequence>
    </complexType>

    <complexType name="StatementIdentTO">
        <annotation>
            <documentation>
                definice mimořádného vypisu pro třetí stranu
                - itemNo: identifikace výpisu z response
                - ident: identifikace (ke generování naplánovaného) výpisu (binarniho dokumentu - binDocumentIdent)
                - result: výsledek žádosti o generování bankovního výpisu
                - OK: žádost založena, v položce "ident" je identifikace výpisu
                - ACCOUNT_NOT_EXIST: požadovaný účet nebyl nalezen
                - INVALID_INTERVAL: chybně zadaný interval (datum od je menší jak datum do, neexistující datum, ...)
                - ACCOUNT_LOCK: S účtem se aktuálně pracuje. Volání je možné opakovat později.
                - GENERAL_ERROR: obecná, nespecifikovaná chyba
            </documentation>
        </annotation>
        <sequence>
            <element name="itemNo" type="string" />
            <element name="ident" type="string" />
            <element name="result" type="string" />
        </sequence>
    </complexType>

    <element name="createBankStatementFor3rdPartyRequest">
        <annotation>
            <documentation>
                Funkce umožní třetí straně zažádat o mimořádný bankovní výpis
                - statement: seznam výpisů k účtům o které je zažádáno
                - callbackSystem: aplikační kód zdrojove aplikace nebo klíčové slovo "ALL" pro všechny podporované aplikace.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="callbackSystem" type="string" />
                <element name="statement" type="tns:StatementFor3rdPartyTO" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <element name="createBankStatementFor3rdPartyResponse">
        <annotation>
            <documentation>
                vrací seznam identifikátorů výpisů připravených ke generování
                - StatementIdent: seznam identifikátorů výpisů (StatementIdentTO.orderNo) napárovaný na identifikaci požadavku z requestu
                (StatementIdentTO.ident)
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="StatementIdent" type="tns:StatementIdentTO" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <element name="getMortgageLoanStatementsRequest">
        <complexType>
            <sequence>
                <element name="idMortgageLoanStatement" type="long" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <element name="getMortgageLoanStatementsResponse">
        <complexType>
            <sequence>
                <element name="statement" type="bs:MortgageLoanStatementTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="findMortgageLoanStatementRequest">
        <annotation>
            <documentation>filter conditions:

                IDMORTGAGELOAN - identification of mortgage loan
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="filter" type="fs:SelectFilter" />
            </sequence>
        </complexType>
    </element>

    <element name="findMortgageLoanStatementResponse">
        <complexType>
            <sequence>
                <element name="idMortgageLoanStatement" type="long" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="createFeeStatementRequest">
        <annotation>
            <documentation>
                Funkce pro založení pouze hlavičky výpisu poplatků nebo založení jak hlavičky výpisů, tak linku na obsah výpisu
                - accountNumber - povinné číslo účtu
                - dateFrom - povinný datum odkdy výpis obsahuje transakční historii
                - dateTo - povinný datum dokdy obsahuje transakční historii
                - type - povinný typ výpisu
                - UUID - nepovinný identifikátor na dokument v kabinetu
                - fileName - nepovinný fyzický název souboru
                - documentName - nepovinný logický název souboru
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="accountNumber" type="long" />
                <element name="dateFrom" type="date" />
                <element name="dateTo" type="date" />
                <element name="type" type="string" />
                <element name="uuid" type="string" minOccurs="0" />
                <element name="fileName" type="string" minOccurs="0" />
                <element name="documentName" type="string" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="createFeeStatementResponse">
        <annotation>
            <documentation>
                Vrací identifikátor vytvořeného výpisu
                - result: výsledek žádosti o generování bankovního výpisu
                - OK: žádost založena, v položce "ident" je identifikace výpisu
                - ACCOUNT_NOT_EXIST: požadovaný účet nebyl nalezen
                - INVALID_INTERVAL: chybně zadaný interval (datum od je menší jak datum do, neexistující datum, ...)
                - INVALID_TYPE: chybně zadaný typ výpisu
                - ACCOUNT_LOCK: S účtem se aktuálně pracuje. Volání je možné opakovat později.
                - GENERAL_ERROR: obecná, nespecifikovaná chyba
                - ordinalNumber - pořadové číslo výpisu
                - idBankAccountStatement - povinný primární klíč výpisu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="string" />
                <element name="idBankAccountStatement" type="long" minOccurs="0" />
                <element name="ordinalNumber" type="long" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="updateFeeStatementRequest">
        <annotation>
            <documentation>
                Funkce pro aktualizaci obsahu výpisu poplatků následovně
                - idBankAccountStatement - povinný primární klíč výpisu
                - UUID - nepovinný identifikátor na dokument v kabinetu
                - fileName - nepovinný fyzický název souboru
                - documentName - nepovinný logický název souboru
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="idBankAccountStatement" type="long" />
                <element name="uuid" type="string" minOccurs="0" />
                <element name="fileName" type="string" minOccurs="0" />
                <element name="documentName" type="string" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="updateFeeStatementResponse">
        <annotation>
            <documentation>
                - result: výsledek aktualizace
                - OK: obsah aktualizován
                - STATEMENT_NOT_EXIST: požadovaný výpis nebyl nalezen
                - GENERAL_ERROR: obecná, nespecifikovaná chyba
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="string" />
            </sequence>
        </complexType>
    </element>
</schema>
