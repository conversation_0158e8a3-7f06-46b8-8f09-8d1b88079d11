<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/SmeDisponentWS/"
        xmlns:tns="http://airbank.cz/obs/ws/SmeDisponentWS/"
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:a="http://airbank.cz/obs/ws/SmeApplication"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/">

    <import namespace="http://airbank.cz/obs/ws/SmeApplication" schemaLocation="../xsd/SmeApplication.xsd"/>
    <import schemaLocation="../xsd/ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>

    <element name="CheckContractEligibilityRequest">
        <complexType>
            <sequence>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="CheckContractEligibilityResponse">
        <complexType>
            <sequence>
                <element name="eligible" type="boolean">
                    <annotation>
                        <documentation>indicates if contract is eligible</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="CreateContractDocumentRequest">
        <complexType>
            <sequence>
                <element name="cuidOwner" type="long">
                    <annotation>
                        <documentation>Cuid of SME customer</documentation>
                    </annotation>
                </element>
                <element name="cuidDisponent" type="long">
                    <annotation>
                        <documentation>Cuid of new disponent</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="appendixNumber" type="long">
                    <annotation>
                        <documentation>Appendix number</documentation>
                    </annotation>
                </element>
                <element name="envelopeID" type="long">
                    <annotation>
                        <documentation>ID of envelope</documentation>
                    </annotation>
                </element>
                <element name="application" type="a:Application" minOccurs="2" maxOccurs="2">
                    <annotation>
                        <documentation>collection of two applications</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="CreateContractDocumentResponse">
        <complexType>
            <sequence>
                <element name="completion" type="com:CreateContractDocumentCompletionResTO" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Created completions</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="ActivateDisponentRequest">
        <complexType>
            <sequence>
                <element name="cuidDisponent" type="long">
                    <annotation>
                        <documentation>Cuid of new disponent</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="envelopeID" type="long">
                    <annotation>
                        <documentation>ID of envelope</documentation>
                    </annotation>
                </element>
                <element name="username" type="string">
                    <annotation>
                        <documentation>Username of new disponent</documentation>
                    </annotation>
                </element>
                <element name="permissionSettings" type="com:PermissionSettingsTO">
                    <annotation>
                        <documentation>Access rights for disponent</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="ActivateDisponentResponse">
        <complexType>
            <sequence>
                <element name="activationResult" type="boolean">
                    <annotation>
                        <documentation>true if activation was successful, otherwise false</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="CreateTerminationContractDocumentRequest">
        <complexType>
            <sequence>
                <element name="cuidOwner" type="long">
                    <annotation>
                        <documentation>Cuid of SME customer</documentation>
                    </annotation>
                </element>
                <element name="cuidDisponent" type="long">
                    <annotation>
                        <documentation>Cuid of new disponent</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="appendixNumber" type="long">
                    <annotation>
                        <documentation>Appendix number</documentation>
                    </annotation>
                </element>
                <element name="envelopeID" type="long">
                    <annotation>
                        <documentation>ID of envelope</documentation>
                    </annotation>
                </element>
                <element name="applicationID" type="long">
                    <annotation>
                        <documentation>ID of application</documentation>
                    </annotation>
                </element>
                <element name="barCode" type="string">
                    <annotation>
                        <documentation>BarCode (search code) included in PDF version of document</documentation>
                    </annotation>
                </element>
                <element name="attachmentID" type="string" maxOccurs="2">
                    <annotation>
                        <documentation>Contract document (UUID)</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="CreateTerminationContractDocumentResponse">
        <complexType>
            <sequence>
                <element name="completion" type="com:CreateContractDocumentCompletionResTO" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Created completions</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="TerminateDisponentRequest">
        <complexType>
            <sequence>
                <element name="cuidDisponent" type="long">
                    <annotation>
                        <documentation>Cuid of disponent</documentation>
                    </annotation>
                </element>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="TerminateDisponentResponse">
        <complexType>
            <sequence>
                <element name="terminationResult" type="boolean">
                    <annotation>
                        <documentation>true if termination was successful, otherwise false</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
