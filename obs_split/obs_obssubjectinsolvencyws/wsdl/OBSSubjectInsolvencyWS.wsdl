<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://obs.airbank.cz/ws/OBSSubjectInsolvencyWS/"
    xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    name="OBSSubjectInsolvencyWS"
	targetNamespace="http://obs.airbank.cz/ws/OBSSubjectInsolvencyWS/">


	<wsdl:types>
		<xsd:schema>
			<xsd:import namespace="http://obs.airbank.cz/ws/OBSSubjectInsolvencyWS/" schemaLocation="OBSSubjectInsolvencyWS.xsd" />
		</xsd:schema>
	</wsdl:types>


	<wsdl:message name="SetSubjectInsolvencyRequest">
		<wsdl:part name="SetSubjectInsolvencyRequest" element="tns:SetSubjectInsolvencyRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="SetSubjectInsolvencyResponse">
		<wsdl:part name="SetSubjectInsolvencyResponse" element="tns:SetSubjectInsolvencyResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:portType name="OBSSubjectInsolvencyWS">
		<wsdl:operation name="SetSubjectInsolvency">
			<wsdl:input message="tns:SetSubjectInsolvencyRequest"></wsdl:input>
			<wsdl:output message="tns:SetSubjectInsolvencyResponse"></wsdl:output>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="OBSSubjectInsolvencyWS" type="tns:OBSSubjectInsolvencyWS">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="SetSubjectInsolvency">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="OBSSubjectInsolvencyWS">
		<wsdl:port binding="tns:OBSSubjectInsolvencyWS" name="OBSSubjectInsolvencyWS">
			<soap:address location="/ws/OBSSubjectInsolvencyWS" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
