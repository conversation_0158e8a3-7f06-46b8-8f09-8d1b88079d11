<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsSpbMaintenanceWS/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="obsSpbMaintenanceWS" targetNamespace="http://arbes.com/ib/core/ppf/ws/obsSpbMaintenanceWS/" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">
	<wsdl:types>
		<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsSpbMaintenanceWS/">
			<xsd:include schemaLocation="OBSSpbMaintenanceWS.xsd" />
			<xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
		</xsd:schema>		
	</wsdl:types>
  <wsdl:message name="isChannelBlockedRequest">
  	<wsdl:part name="parameters" element="tns:isChannelBlockedRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="isChannelBlockedResponse">
  	<wsdl:part name="parameters" element="tns:isChannelBlockedResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="isChannelBlockedFault">
  	<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="obsSpbMaintenanceWS">
    <wsdl:operation name="isChannelBlocked">
    	<wsdl:documentation>metoda vrátí informaci, zda je pro danou osobu kanál SPB zablokován</wsdl:documentation>
    	<wsdl:input message="tns:isChannelBlockedRequest"></wsdl:input>
    	<wsdl:output message="tns:isChannelBlockedResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:isChannelBlockedFault"></wsdl:fault>
        </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="obsSpbMaintenanceWSSOAP"
  	type="tns:obsSpbMaintenanceWS">
  	<soap:binding style="document"
  		transport="http://schemas.xmlsoap.org/soap/http" />
  	<wsdl:operation name="isChannelBlocked">
  		<soap:operation
  			soapAction="" />
  		<wsdl:input>
  			<soap:body use="literal" />
  		</wsdl:input>
  		<wsdl:output>
  			<soap:body use="literal" />
  		</wsdl:output>
  		<wsdl:fault name="fault">
        <soap:fault use="literal" name="fault" />
      </wsdl:fault>
  	</wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="obsSpbMaintenanceWS">
    <wsdl:port binding="tns:obsSpbMaintenanceWSSOAP" name="obsSpbMaintenanceWSSOAP">
      <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsSpbMaintenanceWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
