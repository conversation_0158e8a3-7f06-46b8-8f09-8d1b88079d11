<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:td="http://airbank.cz/obs/ws/termDeposit"
        xmlns:tns="http://airbank.cz/obs/ws/termDepositWS"
        targetNamespace="http://airbank.cz/obs/ws/termDepositWS"
>
    <import namespace="http://airbank.cz/obs/ws/termDeposit" schemaLocation="../xsd/termDeposit.xsd"/>

    <element name="getProductsRequest">
        <complexType>
            <choice>
                <element name="code" type="string">
                    <annotation>
                        <documentation>Kod tranše (produktove varianty)</documentation>
                    </annotation>
                </element>
                <sequence>
                    <element name="onlyActive" type="boolean">
                        <annotation>
                            <documentation>Pouze aktivne nabizene (true) nebo všechny (false).</documentation>
                        </annotation>
                    </element>
                    <element name="type" type="td:ProductType" minOccurs="0">
                        <annotation>
                            <documentation>Typ terminovaneho vkladu</documentation>
                        </annotation>
                    </element>
                </sequence>
            </choice>
        </complexType>
    </element>

    <element name="getProductsResponse">
        <complexType>
            <sequence>
                <element name="product" type="td:Product" minOccurs="0" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <element name="validateProductParametersRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long"/>
                <element name="productCode" type="string">
                    <annotation>
                        <documentation>Identifikator typu terminovaneho vkladu. (getProductsResponse.product.code)</documentation>
                    </annotation>
                </element>
                <element name="principal" type="decimal"/>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Alpha code</documentation>
                    </annotation>
                </element>
                <element name="bankAccountNumber" type="string">
                    <annotation>
                        <documentation>Identifikator uctu, ze ktereho probehne vklad</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <simpleType name="ValidateProductParametersResponseErrorCode">
        <restriction base="string">
            <enumeration value="PRODUCT_NOT_USABLE"/>
            <enumeration value="ACCOUNT_WRONG_OWNER"/>
            <enumeration value="ACCOUNT_WRONG_CURRENCY"/>
            <enumeration value="ACCOUNT_LOW_BALANCE"/>
            <enumeration value="ACCOUNT_BLOCKED"/>
            <enumeration value="PRINCIPAL_TOO_LOW"/>
            <enumeration value="PRINCIPAL_TOO_HIGH"/>
        </restriction>
    </simpleType>

    <element name="validateProductParametersResponse">
        <complexType>
            <choice>
                <element name="maturityDate" type="date"/>
                <element name="errorCode" type="tns:ValidateProductParametersResponseErrorCode"/>
                <element name="errorAttribute" type="string"/>
                <element name="errorValue" type="string"/>
            </choice>
        </complexType>
    </element>

    <element name="createFixedDepositRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long"/>
                <element name="productCode" type="string">
                    <annotation>
                        <documentation>Identifikator typu terminovaneho vkladu. (getProductsResponse.product.code)</documentation>
                    </annotation>
                </element>
                <element name="principal" type="decimal"/>
                <element name="bankAccountNumber" type="string">
                    <annotation>
                        <documentation>Klientsky ucet, ze ktereho bude proveden vklad a na ktery se na konci vkladu vyplati jistina i urok</documentation>
                    </annotation>
                </element>
                <element name="interestRate" type="decimal"/>
                <element name="maturityDate" type="date">
                    <annotation>
                        <documentation>Datum ukonceni vkladu</documentation>
                    </annotation>
                </element>
                <element name="rollover" type="boolean" minOccurs="0" default="false">
                    <annotation>
                        <documentation>
                            true - vklad bude v den otocky obnoven (tj. revolving)
                            false - vklad bude v den otocky ukoncen (tj. jednorazovy vklad)
                            chybejici element je totez jako false
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createFixedDepositResponse">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string">
                    <annotation>
                        <documentation>Unikatni identifikator vkladu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createInvestmentCertificateRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long"/>
                <element name="productCode" type="string">
                    <annotation>
                        <documentation>Identifikator typu terminovaneho vkladu. (getProductsResponse.product.code)</documentation>
                    </annotation>
                </element>
                <element name="principal" type="decimal"/>
                <element name="bankAccountNumber" type="string">
                    <annotation>
                        <documentation>Klientsky ucet, ze ktereho bude proveden vklad a na ktery se budou vyplacet uroky a na konci vkladu se vyplati jistina</documentation>
                    </annotation>
                </element>
                <element name="interestRate" type="decimal"/>
                <element name="maturityDate" type="date">
                    <annotation>
                        <documentation>Datum ukonceni vkladu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createInvestmentCertificateResponse">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string">
                    <annotation>
                        <documentation>Unikatni identifikator vkladu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createInvestmentToAirBankRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long"/>
                <element name="productCode" type="string">
                    <annotation>
                        <documentation>Identifikator typu terminovaneho vkladu. (getProductsResponse.product.code)</documentation>
                    </annotation>
                </element>
                <element name="principal" type="decimal"/>
                <element name="bankAccountNumber" type="string">
                    <annotation>
                        <documentation>Klientsky ucet, ze ktereho bude proveden vklad a na ktery se budou vyplacet uroky a na konci vkladu se vyplati jistina</documentation>
                    </annotation>
                </element>
                <element name="interestRate" type="decimal"/>
                <element name="maturityDate" type="date">
                    <annotation>
                        <documentation>Datum ukonceni vkladu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createInvestmentToAirBankResponse">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string">
                    <annotation>
                        <documentation>Unikatni identifikator vkladu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <simpleType name="ActivateTermDepositResponseErrorCode">
        <restriction base="string">
            <enumeration value="TRANCHE_NOT_USABLE"/>
            <enumeration value="ACCOUNT_NOT_USABLE"/>
            <enumeration value="PRODUCT_NOT_USABLE"/>
        </restriction>
    </simpleType>

    <element name="activateFixedDepositRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string"/>
            </sequence>
        </complexType>
    </element>

    <element name="activateFixedDepositResponse">
        <complexType>
            <sequence>
                <element name="errorCode" type="tns:ActivateTermDepositResponseErrorCode"/>
            </sequence>
        </complexType>
    </element>

    <element name="activateInvestmentCertificateRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string"/>
            </sequence>
        </complexType>
    </element>

    <element name="activateInvestmentCertificateResponse">
        <complexType>
            <sequence>
                <element name="errorCode" type="tns:ActivateTermDepositResponseErrorCode"/>
            </sequence>
        </complexType>
    </element>

    <element name="activateInvestmentToAirBankRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string"/>
            </sequence>
        </complexType>
    </element>

    <element name="activateInvestmentToAirBankResponse">
        <complexType>
            <sequence>
                <element name="errorCode" type="tns:ActivateTermDepositResponseErrorCode"/>
            </sequence>
        </complexType>
    </element>

    <element name="getTermDepositsRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long">
                    <annotation>
                        <documentation>Majitel terminovanych deposit</documentation>
                    </annotation>
                </element>
                <element name="productType" type="td:ProductType" minOccurs="0">
                    <annotation>
                        <documentation>Typ terminovaneho deposita</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getTermDepositsResponse">
        <complexType>
            <sequence>
                <element name="termDeposit" type="td:TermDeposit" minOccurs="0" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <element name="cancelTermDepositRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string"/>
            </sequence>
        </complexType>
    </element>

    <element name="cancelTermDepositResponse">
        <complexType/>
    </element>

    <simpleType name="TerminateTDErrorCode">
        <restriction base="string">
            <enumeration value="TERMDEP_NOT_USABLE"/>
            <enumeration value="ACCOUNT_NOT_USABLE"/>
        </restriction>
    </simpleType>

    <element name="terminateFixedDepositRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string">
                    <annotation>
                        <documentation>Unikatni identifikator vkladu</documentation>
                    </annotation>
                </element>
                <element name="closeReason" type="td:CloseReason">
                    <annotation>
                        <documentation>Duvod ukonceni vkladu</documentation>
                    </annotation>
                </element>
                <element name="destinationAccount" type="td:AccountIdentification"  minOccurs="0">
                    <annotation>
                        <documentation>Ucet pro vyplatu jistiny</documentation>
                    </annotation>
                </element>
             </sequence>
        </complexType>
    </element>

    <element name="terminateFixedDepositResponse">
        <complexType>
            <sequence>
                <element name="errorCode" type="tns:TerminateTDErrorCode" minOccurs="0">
                    <annotation>
                        <documentation>Je-li vse OK => element neni vyplnen</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="setTerminationFixedDepositRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string">
                    <annotation>
                        <documentation>Unikatni identifikator vkladu</documentation>
                    </annotation>
                </element>
                <element name="closeDate" type="date">
                    <annotation>
                        <documentation>Datum předčasného ukončení </documentation>
                    </annotation>
                </element>
                <element name="note" type="string" minOccurs="0" maxOccurs="1">
                    <annotation>
                        <documentation>Poznámka k ukončení.</documentation>
                    </annotation>
                </element>
             </sequence>
        </complexType>
    </element>

    <element name="setTerminationFixedDepositResponse">
        <complexType>
            <sequence>
                <element name="errorCode" type="tns:TerminateTDErrorCode" minOccurs="0">
                    <annotation>
                        <documentation>Je-li vse OK => element neni vyplnen</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <simpleType name="UpdateTDRolloverErrorCode">
        <restriction base="string">
            <enumeration value="TERMDEP_NOT_USABLE"/>
        </restriction>
    </simpleType>

    <element name="updateFixedDepositRolloverRequest">
        <complexType>
            <sequence>
                <element name="depositNumber" type="string">
                    <annotation>
                        <documentation>Unikatni identifikator vkladu</documentation>
                    </annotation>
                </element>
                <element name="rollover" type="boolean">
                    <annotation>
                        <documentation>
                            true - vklad bude v den otocky obnoven
                            false - vklad bude v den otocky ukoncen
                        </documentation>
                    </annotation>
                </element>
             </sequence>
        </complexType>
    </element>

    <element name="updateFixedDepositRolloverResponse">
        <complexType>
            <sequence>
                <element name="errorCode" type="tns:UpdateTDRolloverErrorCode" minOccurs="0">
                    <annotation>
                        <documentation>Je-li vse OK => element neni vyplnen</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>