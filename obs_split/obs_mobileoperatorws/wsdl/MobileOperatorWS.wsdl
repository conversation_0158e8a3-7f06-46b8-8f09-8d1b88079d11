<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions name="MobileOperatorWS"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
             xmlns:tns="http://airbank.cz/obs/ws/MobileOperatorWS/"
             targetNamespace="http://airbank.cz/obs/ws/MobileOperatorWS/">

    <documentation>
        Web Service for mobile operators purposes.
    </documentation>

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/MobileOperatorWS/">
            <xsd:include schemaLocation="MobileOperatorWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </types>

    <message name="fault">
        <part name="fault" element="com:ErrorsListType" />
    </message>

    <message name="sendCompensatoryPaymentForRechargingRequest">
        <part name="parameters" element="tns:sendCompensatoryPaymentForRechargingRequest" />
    </message>

    <message name="sendCompensatoryPaymentForRechargingResponse">
        <part name="parameters" element="tns:sendCompensatoryPaymentForRechargingResponse" />
    </message>

    <message name="getMobileOperatorAccountRequest">
        <part name="parameters" element="tns:getMobileOperatorAccountRequest" />
    </message>

    <message name="getMobileOperatorAccountResponse">
        <part name="parameters" element="tns:getMobileOperatorAccountResponse" />
    </message>

    <portType name="MobileOperatorWS">
        <operation name="sendCompensatoryPaymentForRecharging">
            <documentation>Metoda pro realizaci souhrnné úhrady za službu dobíjení kreditu klientům.
                           Úhrada slouží k finančnímu vyrovnání s protistranou (mobilním operátorem)
                           za určitý časový interval předem definovaný v příslušné smlouvě s protistranou.

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_IS_MANDATORY / operator / : povinná položka
CLERR_IS_MANDATORY / accountingDate / : povinná položka
CLERR_IS_MANDATORY / variableSymbol / : povinná položka
CLERR_IS_MANDATORY / reportName / : povinná položka
CLERR_IS_MANDATORY / amount / : povinná položka
CLERR_INVALID_VALUE / accountingDate / : datum za které posíláme finanční vyrovnání nemůže být v budoucnu
CLERR_INVALID_VALUE / amount / : částka k vyrovnání musí být větší nebo rovna 0
CLERR_NO_DATA_FOUND / operator  / : pro zaslaného operátora nebyla nalezena definice pro finanční vyrovnání
SETTLEMENT_ACCOUNT_FOR_MOBILE_OPERATOR_IS_NOT_ACTIVE / operator / : nalezený technický účet pro zúčtování proti klientovi nebo interní účet pro finální vyrovnání (pokud je vedený v AB) pro daného operátora není aktivní
            </documentation>
            <input message="tns:sendCompensatoryPaymentForRechargingRequest" />
            <output message="tns:sendCompensatoryPaymentForRechargingResponse" />
            <fault name="fault" message="tns:fault" />
        </operation>

        <operation name="getMobileOperatorAccount">
            <documentation>Metoda pro získání detailů o interním technickém účtu který slouží jako
                           průběžka mezi účtem klienta a cílovým účtem mobilního operátora na který
                           ve finále odchází peníze za službu dobíjení kreditu na předplacených SIM kartách.

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_IS_MANDATORY / operator / : povinná položka
CLERR_NO_DATA_FOUND / operator  / : pro zaslaného operátora nebyla nalezena požadovaná data
SETTLEMENT_ACCOUNT_FOR_MOBILE_OPERATOR_IS_NOT_ACTIVE / operator / : nalezený technický účet pro daného operátora není aktivní
            </documentation>
            <input message="tns:getMobileOperatorAccountRequest" />
            <output message="tns:getMobileOperatorAccountResponse" />
            <fault name="fault" message="tns:fault" />
        </operation>
    </portType>

    <binding name="MobileOperatorWSSOAP" type="tns:MobileOperatorWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="sendCompensatoryPaymentForRecharging">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal" />
            </fault>
        </operation>

        <operation name="getMobileOperatorAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal" />
            </fault>
        </operation>
    </binding>

    <service name="MobileOperatorWS">
        <port binding="tns:MobileOperatorWSSOAP" name="MobileOperatorWSSOAP">
            <soap:address location="/ws/MobileOperatorWS" />
        </port>
    </service>

</definitions>
