<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsContractWS/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    name="obsContractWS"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsContractWS/">

    <types>
        <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsContractWS/">
            <xsd:include schemaLocation="OBSContractWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <message name="findContractsRequest">
        <part element="tns:findContractsRequest" name="parameters" />
    </message>

    <message name="findContractsResponse">
        <part element="tns:findContractsResponse" name="parameters" />
    </message>

    <message name="getContractsDetailRequest">
        <part name="parameters" element="tns:getContractsDetailRequest" />
    </message>

    <message name="getContractsDetailResponse">
        <part name="parameters" element="tns:getContractsDetailResponse" />
    </message>

    <message name="getGeneralContractsRequest">
        <part name="parameters" element="tns:getGeneralContractsRequest" />
    </message>

    <message name="getGeneralContractsResponse">
        <part name="parameters" element="tns:getGeneralContractsResponse" />
    </message>

    <message name="signContractRequest">
        <part name="parameters" element="tns:signContractRequest" />
    </message>

    <message name="signContractResponse">
        <part name="parameters" element="tns:signContractResponse" />
    </message>

    <message name="activateAccountsRequest">
        <part name="parameters" element="tns:activateAccountsRequest" />
    </message>

    <message name="activateAccountsResponse">
        <part name="parameters" element="tns:activateAccountsResponse" />
    </message>

    <message name="getPersonsByContractRequest">
        <part name="parameters" element="tns:getPersonsByContractRequest" />
    </message>

    <message name="getPersonsByContractResponse">
        <part name="parameters" element="tns:getPersonsByContractResponse" />
    </message>

    <message name="setLimitsRequest">
        <part name="parameters" element="tns:setLimitsRequest" />
    </message>

    <message name="setLimitsResponse">
        <part name="parameters" element="tns:setLimitsResponse" />
    </message>

    <message name="getLimitsRequest">
        <part name="parameters" element="tns:getLimitsRequest" />
    </message>

    <message name="getLimitsResponse">
        <part name="parameters" element="tns:getLimitsResponse" />
    </message>

    <message name="getUnpersonalizedDocumentsRequest">
        <part name="parameters" element="tns:getUnpersonalizedDocumentsRequest" />
    </message>

    <message name="getUnpersonalizedDocumentsResponse">
        <part name="parameters" element="tns:getUnpersonalizedDocumentsResponse" />
    </message>

    <message name="setBillManagerCommChannelRequest">
        <part name="parameters" element="tns:setBillManagerCommChannelRequest" />
    </message>

    <message name="setBillManagerCommChannelResponse">
        <part name="parameters" element="tns:setBillManagerCommChannelResponse" />
    </message>

    <message name="getBillManagerCommChannelRequest">
        <part name="parameters" element="tns:getBillManagerCommChannelRequest" />
    </message>

    <message name="getBillManagerCommChannelResponse">
        <part name="parameters" element="tns:getBillManagerCommChannelResponse" />
    </message>

    <message name="newApplicationCheckRequest">
        <part name="parameters" element="tns:newApplicationCheckRequest" />
    </message>

    <message name="newApplicationCheckResponse">
        <part name="parameters" element="tns:newApplicationCheckResponse" />
    </message>

    <message name="getDebtsRequest">
        <part name="parameters" element="tns:getDebtsRequest" />
    </message>

    <message name="getDebtsResponse">
        <part name="parameters" element="tns:getDebtsResponse" />
    </message>

    <message name="getExternalProductsDebtsRequest">
        <part name="parameters" element="tns:getExternalProductsDebtsRequest" />
    </message>

    <message name="getExternalProductsDebtsResponse">
        <part name="parameters" element="tns:getExternalProductsDebtsResponse" />
    </message>

    <message name="getAllClientPastDueDebtsRequest">
        <part name="parameters" element="tns:getAllClientPastDueDebtsRequest" />
    </message>

    <message name="getAllClientPastDueDebtsResponse">
        <part name="parameters" element="tns:getAllClientPastDueDebtsResponse" />
    </message>

    <message name="getPermissionSettingsRequest">
        <part name="parameters" element="tns:getPermissionSettingsRequest" />
    </message>

    <message name="getPermissionSettingsResponse">
        <part name="parameters" element="tns:getPermissionSettingsResponse" />
    </message>

    <message name="setPermissionSettingsRequest">
        <part name="parameters" element="tns:setPermissionSettingsRequest" />
    </message>

    <message name="setPermissionSettingsResponse">
        <part name="parameters" element="tns:setPermissionSettingsResponse" />
    </message>

    <message name="getRelationToBankRequest">
        <part name="parameters" element="tns:getRelationToBankRequest" />
    </message>

    <message name="getRelationToBankResponse">
        <part name="parameters" element="tns:getRelationToBankResponse" />
    </message>

    <message name="setRelationToBankRequest">
        <part name="parameters" element="tns:setRelationToBankRequest" />
    </message>

    <message name="setRelationToBankResponse">
        <part name="parameters" element="tns:setRelationToBankResponse" />
    </message>

    <message name="getPermissionsRequest">
        <part name="parameters" element="tns:getPermissionsRequest" />
    </message>

    <message name="getPermissionsResponse">
        <part name="parameters" element="tns:getPermissionsResponse" />
    </message>

    <message name="getPermissionsWithoutLoginRequest">
        <part name="parameters" element="tns:getPermissionsWithoutLoginRequest" />
    </message>

    <message name="getPermissionsWithoutLoginResponse">
        <part name="parameters" element="tns:getPermissionsWithoutLoginResponse" />
    </message>

    <message name="getCardActivationAllowanceRequest">
        <part name="parameters" element="tns:getCardActivationAllowanceRequest" />
    </message>

    <message name="getCardActivationAllowanceResponse">
        <part name="parameters" element="tns:getCardActivationAllowanceResponse" />
    </message>

    <message name="createMortgageContractDocumentRequest">
        <part name="parameters" element="tns:createMortgageContractDocumentRequest" />
    </message>

    <message name="createMortgageContractDocumentResponse">
        <part name="parameters" element="tns:createMortgageContractDocumentResponse" />
    </message>

    <message name="publishContractDocumentationRequest">
        <part name="parameters" element="tns:publishContractDocRequest" />
    </message>

    <message name="publishContractDocumentationResponse">
        <part name="parameters" element="tns:publishContractDocResponse" />
    </message>

    <message name="createContractDocumentCancelInsuranceRequest">
        <part name="parameters" element="tns:createContractDocumentCancelInsuranceRequest" />
    </message>

    <message name="createContractDocumentCancelInsuranceResponse">
        <part name="parameters" element="tns:createContractDocumentCancelInsuranceResponse" />
    </message>

    <message name="createTravelInsuranceCancellationContractDocumentRequest">
        <part name="parameters" element="tns:createTravelInsuranceCancellationContractDocumentRequest" />
    </message>

    <message name="createTravelInsuranceCancellationContractDocumentResponse">
        <part name="parameters" element="tns:createTravelInsuranceCancellationContractDocumentResponse" />
    </message>

    <message name="getAllGeneralContractsRequest">
        <part name="parameters" element="tns:getAllGeneralContractsRequest" />
    </message>

    <message name="getAllGeneralContractsResponse">
        <part name="parameters" element="tns:getAllGeneralContractsResponse" />
    </message>

    <message name="getNoncontractualPersDocumentsRequest">
        <part name="parameters" element="tns:getNoncontractualPersDocumentsRequest" />
    </message>

    <message name="getNoncontractualPersDocumentsResponse">
        <part name="parameters" element="tns:getNoncontractualPersDocumentsResponse" />
    </message>

    <message name="createContractDocumentRequest">
        <part name="parameters" element="tns:createContractDocumentRequest" />
    </message>

    <message name="createContractDocumentResponse">
        <part name="parameters" element="tns:createContractDocumentResponse" />
    </message>

    <message name="createAuthorizedContractDocumentRequest">
        <part name="parameters" element="tns:createAuthorizedContractDocumentRequest" />
    </message>

    <message name="createAuthorizedContractDocumentResponse">
        <part name="parameters" element="tns:createAuthorizedContractDocumentResponse" />
    </message>

    <message name="getCustomerServicesRequest">
        <part name="getCustomerServicesRequest" element="tns:getCustomerServicesRequest" />
    </message>

    <message name="getCustomerServicesResponse">
        <part name="getCustomerServicesResponse" element="tns:getCustomerServicesResponse" />
    </message>

    <message name="documentReplacedRequest">
        <part name="documentReplacedRequest" element="tns:documentReplacedRequest" />
    </message>

    <message name="documentReplacedResponse">
        <part name="documentReplacedResponse" element="tns:documentReplacedResponse" />
    </message>

    <message name="contractStateChangeRequest">
        <part name="ContractStateChangeRequest" element="tns:ContractStateChangeRequest" />
    </message>

    <message name="contractStateChangeResponse">
        <part name="ContractStateChangeResponse" element="tns:ContractStateChangeResponse" />
    </message>

    <message name="setContractNotificationSettingsRequest">
        <part name="parameters" element="tns:setContractNotificationSettingsRequest" />
    </message>

    <message name="setContractNotificationSettingsResponse">
        <part name="parameters" element="tns:setContractNotificationSettingsResponse" />
    </message>

    <message name="getContractNotificationSettingsRequest">
        <part name="parameters" element="tns:getContractNotificationSettingsRequest" />
    </message>

    <message name="getContractNotificationSettingsResponse">
        <part name="parameters" element="tns:getContractNotificationSettingsResponse" />
    </message>

    <message name="createTravelInsuranceContractDocumentRequest">
        <part name="parameters" element="tns:createTravelInsuranceContractDocumentRequest" />
    </message>

    <message name="createTravelInsuranceContractDocumentResponse">
        <part name="parameters" element="tns:createTravelInsuranceContractDocumentResponse" />
    </message>

    <message name="createGeneralContractRequest">
        <part name="parameters" element="tns:createGeneralContractRequest" />
    </message>

    <message name="createGeneralContractResponse">
        <part name="parameters" element="tns:createGeneralContractResponse" />
    </message>

    <message name="grantCISPPermissionRequest">
        <part name="parameters" element="tns:grantCISPPermissionRequest" />
    </message>

    <message name="grantCISPPermissionResponse">
        <part name="parameters" element="tns:grantCISPPermissionResponse" />
    </message>

    <message name="revokeCISPPermissionRequest">
        <part name="parameters" element="tns:revokeCISPPermissionRequest" />
    </message>

    <message name="revokeCISPPermissionResponse">
        <part name="parameters" element="tns:revokeCISPPermissionResponse" />
    </message>

    <message name="getCISPPermissionsRequest">
        <part name="parameters" element="tns:getCISPPermissionsRequest" />
    </message>

    <message name="getCISPPermissionsResponse">
        <part name="parameters" element="tns:getCISPPermissionsResponse" />
    </message>

    <message name="isOwnAccountNotificationEnabledRequest">
        <part name="parameters" element="tns:isOwnAccountNotificationEnabledRequest" />
    </message>

    <message name="isOwnAccountNotificationEnabledResponse">
        <part name="parameters" element="tns:isOwnAccountNotificationEnabledResponse" />
    </message>

    <message name="setOwnAccountNotificationRequest">
        <part name="parameters" element="tns:setOwnAccountNotificationRequest" />
    </message>

    <message name="setOwnAccountNotificationResponse">
        <part name="parameters" element="tns:setOwnAccountNotificationResponse" />
    </message>

    <message name="createDocToInformRequest">
        <part name="parameters" element="tns:createDocToInformRequest" />
    </message>

    <message name="createDocToInformResponse">
        <part name="parameters" element="tns:createDocToInformResponse" />
    </message>

    <message name="createPPInsuranceCreationMortgageContractDocumentsRequest">
        <part name="CreatePPInsuranceCreationMortgageContractDocumentsRequest" element="tns:createPPInsuranceCreationMortgageContractDocumentsRequest" />
    </message>

    <message name="createPPInsuranceCreationMortgageContractDocumentsResponse">
        <part name="CreatePPInsuranceCreationMortgageContractDocumentsResponse" element="tns:createPPInsuranceCreationMortgageContractDocumentsResponse" />
    </message>

    <message name="createPPInsuranceCancellationMortgageContractDocumentsRequest">
        <part name="CreatePPInsuranceCancellationMortgageContractDocumentsRequest" element="tns:createPPInsuranceCancellationMortgageContractDocumentsRequest" />
    </message>

    <message name="createPPInsuranceCancellationMortgageContractDocumentsResponse">
        <part name="CreatePPInsuranceCancellationMortgageContractDocumentsResponse" element="tns:createPPInsuranceCancellationMortgageContractDocumentsResponse" />
    </message>

    <message name="getNextAppendixNumberRequest">
        <part name="parameters" element="tns:getNextAppendixNumberRequest" />
    </message>

    <message name="getNextAppendixNumberResponse">
        <part name="parameters" element="tns:getNextAppendixNumberResponse" />
    </message>

    <message name="registerSignToContractRequest">
        <part name="parameters" element="tns:registerSignToContractRequest" />
    </message>

    <message name="registerSignToContractResponse">
        <part name="parameters" element="tns:registerSignToContractResponse" />
    </message>

    <message name="requestForAuthResetRequest">
        <part name="parameters" element="tns:requestForAuthResetResponse" />
    </message>

    <message name="requestForAuthResetResponse">
        <part name="parameters" element="tns:requestForAuthResetResponse" />
    </message>

    <message name="terminateGeneralContractRequest">
        <part name="parameters" element="tns:terminateGeneralContractRequest" />
    </message>

    <message name="terminateGeneralContractResponse">
        <part name="parameters" element="tns:terminateGeneralContractResponse" />
    </message>

    <portType name="obsContractWS">
        <operation name="findContracts">
            <documentation>
                filtr pro smlouvy.

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:findContractsRequest" />
            <output message="tns:findContractsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getContractsDetail">
            <documentation>
                detail smlouvy (pouze perzonifikované dokumenty, nepoužívá se pro VOB, ceník a pod. !!!)

                požadované podmínky:
                přihlášeného uživatele: ne

                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:getContractsDetailRequest" />
            <output message="tns:getContractsDetailResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getGeneralContracts">
            <documentation>
                informace o rámcových smlouvách

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován technicalContext: ne

                Chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
                CLERR_NO_DATA_FOUND / cuid / : uživatel nenalezen
                CLERR_NO_DATA_FOUND / PERSDOC / :nenalezena rámcová smlouva
            </documentation>
            <input message="tns:getGeneralContractsRequest" />
            <output message="tns:getGeneralContractsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="signContract">
            <documentation>
                podpis dodatku nebo RS

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                Chyby:
                kód / atribut / hodnota
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
                ERROR_BAD_SIGNATURE / / - neplatná autorizace
                CLERR_ENUM_ERROR / authTypeCode / - nesprávná hodnota, není ve výčtu
                CLERR_IS_MANDATORY / authTypeCode / -povinná položka
                CLERR_IS_MANDATORY / signatureDate / - povinná položka
                CLERR_IS_MANDATORY / signedDocumentId / - povinná položka
                CLERR_IS_MANDATORY / actionType / - povinná položka
                CLERR_ENUM_ERROR / actionType / - nesprávná hodnota, není ve výčtu
                CLERR_NO_DATA_FOUND / IDAPPLICATION / - žádost nenalezena
                CLERR_NO_DATA_FOUND / PERSDOC /- smlouva nenalezen
                CLERR_TOO_MANY_ROW / PERSDOC / - nalezeno více smluv
                CLERR_NO_DATA_FOUND / BINDOCOLD / - původní bin dokument nenalezen
                CLERR_NO_DATA_FOUND / BINDOCNEW / - nový bin dokument nenalezen
                CLERR_CHANGE_PRICE / PRICEPROGRAM / - chyba pri zmene cenoveho programu
                CLERR_CANCEL_DK / CANCELCARD / - chyba pri ruseni karty (debetni)
                CLERR_CANCEL_VK / CANCELCARD / chyba pri ruseni karty (virtualni)
                CLERR_VERIFICATION_FAILED / COMPLETIONVERIFY / : chyba pri overovani kompletace
                CLERR_ALLREADY_RECEIVEDPROCESSED / PERSDOC / : text Dokument je již prijatý, proto ho nelze podepsat.
                CLERR_OWNER_SIGN_NEEDED / idContract / : vlastník podepisuje dodatek jako první, držitel nebo disponent
                může následně -
                platí pouze pro podpis prohlášení disponenta/držitele
                CLERR_AFF_TYPE_USER / idContract / : prohlášení v kanálu IB může podepsat pouze D/D je li už klientem,
                jinak tato chyba
                CLERR_GENERAL_CONTRACT_NOT_COMPLETED / idProfile / : neni skompletovana ramcova smlouva
                CLERR_WRONG_CONTRACT_TYPE / CONTRACTTYPE / - v signContractRequest prisel jiny contractType nez je v kompletaci (completionType)
            </documentation>
            <input message="tns:signContractRequest" />
            <output message="tns:signContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="activateAccounts">
            <documentation>
                aktivování pasivních účtů poté co se přihlásili pobočník a klient
                a pobočník ověřil totožnost klienta, údaj potřebný k aktivaci se posílá v kontextu -
                cuid přihlášeného uživatele

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                Chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:activateAccountsRequest" />
            <output message="tns:activateAccountsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getPersonsByContract">
            <documentation>
                get persons by contract

                (SOAPFault):
                code / atribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general system error
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
                CLERR_NO_DATA_FOUND / contractId / : contract with contractId not found
            </documentation>
            <input message="tns:getPersonsByContractRequest" />
            <output message="tns:getPersonsByContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setLimits">
            <documentation>
                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
                CLERR_BRANCH_FORBIDDEN / / : pokial sa uzivatel pokusi menit limit pro pobocku
                CLERR_RAISE_FORBIDDEN / / : pokial sa uzivatel bez prihlaseneho operatora pokusi zvysit limit
                ERR_WRONG_CONTEXT / UPDATE_ERROR / : špatný profil
                ERR_PPF_VALIDFROM / ValidFrom / :neplatné dotum od
                ERR_PPF_CANNOT_BE_NULL / Amount / :atribut je prázdný
                ERR_LIMIT_RANGE_REACHED / Amount / :částka přesáhla povolený limit
            </documentation>
            <input message="tns:setLimitsRequest" />
            <output message="tns:setLimitsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getLimits">
            <documentation>
                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
                CLERR_IS_MANDATORY / idProfile / : povinná položka
                ERR_WRONG_CONTEXT / UPDATE_ERROR / : špatný profil
                ERR_PPF_VALIDFROM / ValidFrom / :neplatné dotum od
                ERR_PPF_CANNOT_BE_NULL / Amount / :atribut je prázdný
                ERR_LIMIT_RANGE_REACHED / Amount / :částka přesáhla povolený limit
            </documentation>
            <input message="tns:getLimitsRequest" />
            <output message="tns:getLimitsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getUnpersonalizedDocuments">
            <documentation>
                metoda pro načtení neperzonifikovaných dokumentů

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:getUnpersonalizedDocumentsRequest" />
            <output message="tns:getUnpersonalizedDocumentsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setBillManagerCommChannel">
            <documentation>
                Metoda nastaví pro danou smlouvu (podle profilu z securityContex) komunikační kanál pro
                Bill Managera.

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:setBillManagerCommChannelRequest" />
            <output message="tns:setBillManagerCommChannelResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getBillManagerCommChannel">
            <documentation>
                Metoda načte pro danou smlouvu (podle profilu z securityContex) komunikační kanál pro
                Bill Managera.

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:getBillManagerCommChannelRequest" />
            <output message="tns:getBillManagerCommChannelResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="newApplicationCheck">
            <documentation>
                metoda zjistí, zda je možné založit další produkt pro daného klienta daného typu

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyb
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.

                logika:
                Pro typ úvěr:
                FS_OBS_20_Splátkový_kalendář
                Kapitola - Ověření možnosti požádat o úvěr
                - zda klient nepřekročil max množství aktivních úvěrů
                - kontrola na zda nepřekročil max pro DPD na úvěrech či depozitech (bežné účty / spořící účty)
                - zda na depositech či úvěrech nepřekročil celkovou dlužnou částku
            </documentation>
            <input message="tns:newApplicationCheckRequest" />
            <output message="tns:newApplicationCheckResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getDebts">
            <documentation>
                vratí pohledávky k úverům, spořícím a běžným účtům pro daný profil nebo pro zvolený
                produkt dle typu nebo konkrétní produkt dle typu a id.

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                logika:
                vrací kolekci dluhů v pořadí BU, SU, HU + dle uživatelského názvu vzestupně
                - zahrnují se pouze aktivní úvěry
                - u BU a SU se vrací debetní disponibilní zůstatek v měně účtu, suma dluhu je přepočten do systémové
                měny (CZK) dle aktuálního platnýho středového kurzu
                - metoda vrací údaje pouze pro BU a SU, jejichž debetní disponibilní zůstatek je výšší, než hodnota
                globálního parametru &quot;DPD tolerance částky - B&quot;.

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:getDebtsRequest" />
            <output message="tns:getDebtsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getExternalProductsDebts">
            <documentation>
                vratí pohledávky k externím produktům (pojištění pravidelných výdajů, pojištění schopnosti splácet).
            </documentation>
            <input message="tns:getExternalProductsDebtsRequest" />
            <output message="tns:getExternalProductsDebtsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getAllClientPastDueDebts">
            <documentation>
                Metoda vrací delikvenci klienta (za CUID) na všech interních i externích produktech klienta evidovaných v AirBank.
            </documentation>
            <input message="tns:getAllClientPastDueDebtsRequest" />
            <output message="tns:getAllClientPastDueDebtsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getPermissionSettings">
            <documentation>
                Return permission settings for user specified by cuid in request body

                required conditions:
                logged user: yes
                required technicalContext: yes

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general error
                CLERR_ACCESS / GENERAL_ERROR / : user is not authorized to perform the operation
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
                CLERR_IS_MANDATORY / idProfile / : mandatory item
                CLERR_NO_DATA_FOUND / cuid / : user with specified cuid not found
            </documentation>
            <input message="tns:getPermissionSettingsRequest" />
            <output message="tns:getPermissionSettingsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setPermissionSettings">
            <documentation>
                Store permission settings for user specified by cuid in request body

                required conditions:
                logged user: yes
                required technicalContext: yes

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general error
                CLERR_ACCESS / GENERAL_ERROR / : user is not authorized to perform the operation
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
                CLERR_IS_MANDATORY / idProfile / : mandatory item
                CLERR_NO_DATA_FOUND / cuid / : user with specified cuid not found
                ERROR_BAD_SIGNATURE / signature / : invalid authorization
            </documentation>
            <input message="tns:setPermissionSettingsRequest" />
            <output message="tns:setPermissionSettingsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getRelationToBank">
            <documentation>
                Get relation to bank

                required conditions:
                logged user: yes
                required technicalContext: yes

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general error
                CLERR_ACCESS / GENERAL_ERROR / : user is not authorized to perform the operation
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
            </documentation>
            <input message="tns:getRelationToBankRequest" />
            <output message="tns:getRelationToBankResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setRelationToBank">
            <documentation>
                Set relation to bank

                required conditions:
                logged user: yes
                required technicalContext: yes

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general error
                CLERR_ACCESS / GENERAL_ERROR / : user is not authorized to perform the operation
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
            </documentation>
            <input message="tns:setRelationToBankRequest" />
            <output message="tns:setRelationToBankResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getPermissions">
            <documentation>
                Get permisions for user from security context
                Return collection of enum AnyPermission:
                ANY_LOAN - for user with permission to all loans who has at least one loan
                ANY_ACCOUNT - for user with permission to all accounts who has at least one account
                - for user with permission to specific accounts
                ANY_SAZKA - for user with permission to sazka withdrawal who has at least one current account

                required conditions:
                logged user: yes
                required technicalContext: yes

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general error
                GENERAL_ERROR_ACCESS / GENERAL_ERROR / : person is not logged
                CLERR_ACCESS / GENERAL_ERROR / : user is not authorized to perform the operation
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
            </documentation>
            <input message="tns:getPermissionsRequest" />
            <output message="tns:getPermissionsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getPermissionsWithoutLogin">
            <documentation>
                Get permisions for user
                Return collection of enum AnyPermission:
                ANY_LOAN - for user with permission to all loans who has at least one loan
                ANY_ACCOUNT - for user with permission to all accounts who has at least one account
                - for user with permission to specific accounts
                ANY_SAZKA - for user with permission to sazka withdrawal who has at least one current account
                DOCUMENT_ORGANIZER - document organizer access rights

                required conditions:
                logged user: no
                required technicalContext: no

                Generated faults (code / attribute / value : description):
                GENERAL_ERROR / GENERAL_ERROR / : general error
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled
                back
                CLERR_NO_DATA_FOUND / cuid / : customer not found
            </documentation>
            <input message="tns:getPermissionsWithoutLoginRequest" />
            <output message="tns:getPermissionsWithoutLoginResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getCardActivationAllowance">
            <documentation>
                Verify the possibility of card activation

                required conditions:
                logged user: no
                required technicalContext: no

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general error
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time
                CARD_NOT_FOUND / idCard / : the card by idCard not found in OBS system
            </documentation>
            <input message="tns:getCardActivationAllowanceRequest" />
            <output message="tns:getCardActivationAllowanceResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createMortgageContractDocument">
            <documentation>
                create mortgage contract document

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createMortgageContractDocumentRequest" />
            <output message="tns:createMortgageContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="publishContractDocumentation">
            <documentation>
                Publish contract documentation

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:publishContractDocumentationRequest" />
            <output message="tns:publishContractDocumentationResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getAllGeneralContracts">
            <documentation>supply all general contracts as per request.</documentation>
            <input message="tns:getAllGeneralContractsRequest" />
            <output message="tns:getAllGeneralContractsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createContractDocumentCancelInsurance">
            <documentation>
                Create document for cancel insurance.
            </documentation>
            <input message="tns:createContractDocumentCancelInsuranceRequest" />
            <output message="tns:createContractDocumentCancelInsuranceResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createTravelInsuranceCancellationContractDocument">
            <documentation>
                Create document for cancel travel insurance.
            </documentation>
            <input message="tns:createTravelInsuranceCancellationContractDocumentRequest" />
            <output message="tns:createTravelInsuranceCancellationContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getNoncontractualPersDocuments">
            <documentation>
                Metoda pro načtení nesmluvních personalizovaných dokumentů

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext: ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly
                odrolovány.
            </documentation>
            <input message="tns:getNoncontractualPersDocumentsRequest" />
            <output message="tns:getNoncontractualPersDocumentsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="contractStateChange">
            <documentation>
                change state of contract

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:contractStateChangeRequest" />
            <output message="tns:contractStateChangeResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createContractDocument">
            <documentation>
                create contract document

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createContractDocumentRequest" />
            <output message="tns:createContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getCustomerServices">
            <documentation>
                getCustomerServices - returns a list of products and services of client

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                INVALID_PARAMS / CUID / : Invalid cuid.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getCustomerServicesRequest" />
            <output message="tns:getCustomerServicesResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="documentReplaced">
            <documentation>
                Document replaced - Cancel previous document and link current and reopen completion when is finished.
                This service cleans inconsistent document between AMS and ELDAP, which is stored at OBS DB.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                TO_MANY_DOCUMENTS / TO_MANY_DOCUMENTS/ : Exists more then the one documentation
            </documentation>
            <input message="tns:documentReplacedRequest" />
            <output message="tns:documentReplacedResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="setContractNotificationSettings">
            <documentation>
                Set contract notification setting.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:setContractNotificationSettingsRequest" />
            <output message="tns:setContractNotificationSettingsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getContractNotificationSettings">
            <documentation>
                Get contract notification setting.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getContractNotificationSettingsRequest" />
            <output message="tns:getContractNotificationSettingsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createTravelInsuranceContractDocument">
            <documentation>
                 Assumptions:
                 logged in user - no

                 Faults (format - "code / attribute / value : description"):
                 GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                 CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createTravelInsuranceContractDocumentRequest" />
            <output message="tns:createTravelInsuranceContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createAuthorizedContractDocument">
            <documentation>
                Metoda bude sloužit k vygenerování dodatku na vědomí.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createAuthorizedContractDocumentRequest" />
            <output message="tns:createAuthorizedContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createGeneralContract">
            <documentation>
                Create a new general contract for client specified by cuid

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createGeneralContractRequest" />
            <output message="tns:createGeneralContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="grantCISPPermission">
            <documentation>

                Assumptions:
                logged in user - no
                businessContex.Customer.contractId is required

                Faults (format - "code / attribute / value : description"):
                CLERR_IS_MANDATORY / businessContex.Customer.contractId / - povinná položka
                CLERR_NO_DATA_FOUND / businessContex.Customer.contractId / - smlouva nenalezena
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:grantCISPPermissionRequest" />
            <output message="tns:grantCISPPermissionResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="revokeCISPPermission">
            <documentation>

                Assumptions:
                logged in user - no
                businessContex.Customer.contractId is required

                Faults (format - "code / attribute / value : description"):
                CLERR_IS_MANDATORY / businessContex.Customer.contractId / - povinná položka
                CLERR_NO_DATA_FOUND / businessContex.Customer.contractId / - smlouva nenalezena
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:revokeCISPPermissionRequest" />
            <output message="tns:revokeCISPPermissionResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getCISPPermissions">
            <documentation>

                Assumptions:
                logged in user - no
                businessContex.Customer.contractId is required

                Faults (format - "code / attribute / value : description"):
                CLERR_IS_MANDATORY / businessContex.Customer.contractId / - povinná položka
                CLERR_NO_DATA_FOUND / businessContex.Customer.contractId / - smlouva nenalezena
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getCISPPermissionsRequest" />
            <output message="tns:getCISPPermissionsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="isOwnAccountNotificationEnabled">
            <documentation>
                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:isOwnAccountNotificationEnabledRequest" />
            <output message="tns:isOwnAccountNotificationEnabledResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setOwnAccountNotification">
            <documentation>
                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:setOwnAccountNotificationRequest" />
            <output message="tns:setOwnAccountNotificationResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createDocToInform">
            <documentation>
                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createDocToInformRequest" />
            <output message="tns:createDocToInformResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createPPInsuranceCreationMortgageContractDocuments">
            <documentation>
                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createPPInsuranceCreationMortgageContractDocumentsRequest" />
            <output message="tns:createPPInsuranceCreationMortgageContractDocumentsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createPPInsuranceCancellationMortgageContractDocuments">
            <documentation>
                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createPPInsuranceCancellationMortgageContractDocumentsRequest" />
            <output message="tns:createPPInsuranceCancellationMortgageContractDocumentsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getNextAppendixNumber">
            <documentation>
                Return next Appendix number.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / generalContractNumber / value : General Contract for mentioned GC number not exist.
            </documentation>
            <input message="tns:getNextAppendixNumberRequest" />
            <output message="tns:getNextAppendixNumberResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="registerSignToContract">
            <documentation>
                Register client's signature to contract.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / CHANNELSIGN / value : Signature channel does not exist.
                CLERR_NO_DATA_FOUND / CUID / value : CUID does not exist.
                CLERR_NO_DATA_FOUND / GCID / value : GCID does not exist.
                CLERR_WRONG_STATUS / GCID / value : GCID has wrong status.
                CLERR_NO_DATA_FOUND / COMPLETIONID / value : Completion ID does not exist.
                CLERR_NO_DATA_FOUND / PERSDOC / value : For required IDcompletion no PersDoc found.
                CLERR_WRONG_STATUS / PERSDOC / value : PersDoc has wrong status.
                CLERR_NO_DATA_FOUND / BINDOCUUID / value : BinDoc with required UUID not found.
                CLERR_NO_DATA_FOUND / BRANCHID / value: Branch with required ID not found.
                CLERR_NO_DATA_FOUND / BRANCHASSISTANTID / value : Branch assistant/operator with required ID not found.
            </documentation>
            <input message="tns:registerSignToContractRequest" />
            <output message="tns:registerSignToContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="requestForAuthReset">
            <documentation>
                Zalození kompletace za ucelem kompletniho resetu bezpecnostnich prvku na pobocce

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / CUSTOMERCUID / value : COSTUMERCUID does not exist.
                CLERR_NO_DATA_FOUND / GENERALCONTRACTNUMBER / value : GENERALCONTRACTNUMBER does not exist.
                CLERR_WRONG_STATUS / GENERALCONTRACTNUMBER / value : GENERALCONTRACTNUMBER has wrong status.
                CLERR_NO_DATA_FOUND / BRANCHID / value: Branch with required ID not found.
                CLERR_NO_DATA_FOUND / BRANCHASSISTANTID / value : Branch assistant/operator with required ID not found.
                CLERR_WRONG_CHNLSIGN / CHNLSIGN / value : CHNLSIGN must be BRANCH.
                CLERR_WRONG_SIGNTP / SIGNTP / value : SIGNTP must be OTP.
                CLERR_NO_DATA_FOUND / PERSDOC / value : Personalized document not found.
            </documentation>
            <input message="tns:requestForAuthResetRequest" />
            <output message="tns:requestForAuthResetResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="terminateGeneralContract">
            <documentation>
                Termination of General contract.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / GCID / value : GCID does not exist.
                CLERR_WRONG_STATUS / GCID / value : GCID has wrong status.
            </documentation>
            <input message="tns:terminateGeneralContractRequest" />
            <output message="tns:terminateGeneralContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
    </portType>

    <binding name="obsContractWSSOAP" type="tns:obsContractWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="findContracts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getContractsDetail">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getGeneralContracts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="signContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="activateAccounts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPersonsByContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setLimits">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getLimits">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getUnpersonalizedDocuments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setBillManagerCommChannel">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getBillManagerCommChannel">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="newApplicationCheck">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getDebts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getExternalProductsDebts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAllClientPastDueDebts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPermissionSettings">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setPermissionSettings">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getRelationToBank">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setRelationToBank">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPermissions">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPermissionsWithoutLogin">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getCardActivationAllowance">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createMortgageContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="publishContractDocumentation">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createContractDocumentCancelInsurance">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createTravelInsuranceCancellationContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAllGeneralContracts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getNoncontractualPersDocuments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="contractStateChange">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getCustomerServices">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="documentReplaced">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setContractNotificationSettings">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getContractNotificationSettings">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createTravelInsuranceContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createAuthorizedContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createGeneralContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="grantCISPPermission">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="revokeCISPPermission">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getCISPPermissions">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="isOwnAccountNotificationEnabled">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setOwnAccountNotification">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createDocToInform">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createPPInsuranceCreationMortgageContractDocuments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createPPInsuranceCancellationMortgageContractDocuments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getNextAppendixNumber">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="registerSignToContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="requestForAuthReset">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="terminateGeneralContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="obsContractWS">
        <port binding="tns:obsContractWSSOAP" name="obsContractWSSOAP">
            <soap:address location="/ws/obsContractWS"/>
        </port>
    </service>

</definitions>
