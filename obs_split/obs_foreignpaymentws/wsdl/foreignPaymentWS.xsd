<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/foreignPaymentWS"
        xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified"
        xmlns:frg="http://airbank.cz/obs/ws/foreignPayment">

    <import namespace="http://airbank.cz/obs/ws/foreignPayment" schemaLocation="../xsd/foreignPayment.xsd" />

    <!-- SWIFT -->
    <!--   getZPSPaymentForExportToSWIFTRequest-->
    <element name="getZPSPaymentForExportToSWIFTRequest">
        <annotation>
            <documentation>
                odchozi platby do SWIFTu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ExportDate" type="dateTime"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>


    <!--   getZPSPaymentForExportToSWIFTResponse -->
    <element name="getZPSPaymentForExportToSWIFTResponse">
        <complexType>
            <sequence>
                <element name="SWIFTExportItems" type="frg:SWIFTExportItem" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>



    <!--   setStatusZPSPaymentRequest-->
    <element name="setStatusZPSPaymentRequest">
        <annotation>
            <documentation>
                kolekce plateb a jejich stavu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ZPSPaymentStatus" type="frg:ZPSPaymentStatus" maxOccurs="unbounded"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>


    <!--   setStatusZPSPaymentResponse -->
    <element name="setStatusZPSPaymentResponse">
        <complexType>
            <sequence>
                <element name="Result" type="frg:OBSResult"/>  <!-- kolekce odchozich ZPS plateb do SWIFTu -->
            </sequence>

        </complexType>
    </element>


    <!--   setZPSPaymentsForImportToOBSRequest-->
    <element name="setZPSPaymentsForImportToOBSRequest">
        <annotation>
            <documentation>
                prichozi platby ze SWIFTu do OBS
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="GrpHdr" type="frg:GrpHdr"/>                                   <!-- hlavicka skupiny ZPS plateb -->
                <element name="SWIFTPayments" type="frg:ZPSPayment" maxOccurs="unbounded"/>  <!-- kolekce prichozi ZPS plateb ze SWIFTu do OBS -->
            </sequence>
        </complexType>
    </element>

    <!--   setZPSPaymentsForImportToOBSResponse -->
    <element name="setZPSPaymentsForImportToOBSResponse">
        <complexType>
            <sequence>
                <element name="ZPSPaymentAccountingInfo" type="frg:ZPSPaymentAccountingInfo" maxOccurs="unbounded"/>  <!-- kolekce vysledku zauctovani ZPS plateb ze SWIFTu -->
            </sequence>
        </complexType>
    </element>



    <!--   setZPSStatementForImportToOBSRequest-->
    <element name="setZPSStatementForImportToOBSRequest">
        <annotation>
            <documentation>
                prichozi vypisy z nostro uctu ze SWIFTu do OBS
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="GrpHdr" type="frg:GrpHdrStmt" />
                <element name="ZPSStatements" type="frg:ZPSStatement" maxOccurs="unbounded"/>  <!-- kolekce ZPS vypisu ze SWIFTu -->
            </sequence>
        </complexType>
    </element>

    <!--   setZPSStatementForImportToOBSResponse -->
    <element name="setZPSStatementForImportToOBSResponse">
        <complexType>
            <sequence>
                <element name="ZPSStatementAccountingInfo" type="frg:ZPSStatementAccountingInfo" maxOccurs="unbounded"/>  <!-- kolekce vysledku zauctovani vypisu ze SWIFTu -->
            </sequence>
        </complexType>
    </element>

    <!--   getPaymentStatusTrackerUpdateRequest -->
    <element name="getPaymentStatusTrackerUpdateRequest">
        <annotation>
            <documentation>
                odchozi potvrzeni o prijeti prichozi Targetove/SWIFTove platby, zpravou trck.001.001.02
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ExportDate" type="dateTime"/>  <!-- aktualni datum -->
                <element name="MaxCount" type="decimal" />    <!-- maximalni pocet zprav -->
            </sequence>
        </complexType>
    </element>

    <!--   getPaymentStatusTrackerUpdateResponse -->
    <element name="getPaymentStatusTrackerUpdateResponse">
        <complexType>
            <sequence>
                <element name="TrackerUpdate" type="frg:SWIFTTrackerUpdate" maxOccurs="unbounded"/>  <!-- kolekce informaci pro sestaveni zpravy trck.001.001.02 -->
            </sequence>
        </complexType>
    </element>


    <!--   getCorrespondentBanksRequest-->
    <element name="getCorrespondentBanksRequest">
        <annotation>
            <documentation>
                seznam korespondecni bank - korespondetu pro ZPS
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ActualDate" type="dateTime"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>


    <!--   getCorrespondentBanksResponse -->
    <element name="getCorrespondentBanksResponse">
        <complexType>
            <sequence>
                <element name="CorrespondentBanks" type="frg:CorrespondentBank" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>





    <!-- SEPA -->
    <!--   setSepaPaymentsForImportToOBSRequest-->
    <element name="setSepaPaymentsForImportToOBSRequest">
        <annotation>
            <documentation>
                prichozi SEPA platby do OBS
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="GrpHdr" type="frg:GrpHdr"/>                                   <!-- hlavicka skupiny sepa zprav -->
                <element name="SEPAPayments" type="frg:SEPAPayment" maxOccurs="unbounded"/>  <!-- kolekce prichozi SEPA plateb do OBS -->
            </sequence>
        </complexType>
    </element>

    <!--   setSepaPaymentsForImportToOBSResponse -->
    <element name="setSepaPaymentsForImportToOBSResponse">
        <complexType>
            <sequence>
                <element name="SepaPaymentAccountingInfo" type="frg:ZPSPaymentAccountingInfo" maxOccurs="unbounded"/>  <!-- kolekce s vysledky zauctovani prichozich zps plateb -->
            </sequence>
        </complexType>
    </element>


    <!--   getSEPAPaymentsForExportToECBRequest-->
    <element name="getSEPAPaymentsForExportToECBRequest">
        <annotation>
            <documentation>
                odchozi SEPA platby do ECB
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ExportDate" type="dateTime"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>


    <!--  getSEPAPaymentsForExportToECBResponse -->
    <element name="getSEPAPaymentsForExportToECBResponse">
        <complexType>
            <sequence>
                <element name="GrpHdr" type="frg:GrpHdr"/>                                   <!-- hlavicka skupiny sepa zprav -->
                <element name="SEPAPayments" type="frg:SEPAPayment" maxOccurs="unbounded"/>  <!-- kolekce odchozich SEPA plateb do ECB -->
            </sequence>
        </complexType>
    </element>


    <!--   getSepaPmtRtrForExportToECBRequest-->
    <element name="getSepaPmtRtrForExportToECBRequest">
        <annotation>
            <documentation>
                odchozi SEPA vratky plateb do ECB
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ExportDate" type="dateTime"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>


    <!--  getSepaPmtRtrForExportToECBResponse -->
    <element name="getSepaPmtRtrForExportToECBResponse">
        <complexType>
            <sequence>
                <element name="GrpHdr" type="frg:PmtRtrGrpHdr"/>                                   <!-- hlavicka skupiny sepa zprav -->
                <element name="SEPAPmtRtrs" type="frg:SEPAPmtRtr" maxOccurs="unbounded"/>           <!-- kolekce odchozich SEPA vratek do ECB -->
            </sequence>
        </complexType>
    </element>


    <!--   setStatusSEPAPaymentRequest-->
    <element name="setStatusSEPAPaymentRequest">
        <annotation>
            <documentation>
                kolekce plateb a jejich stavu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="SEPAPaymentStatus" type="frg:SEPAPaymentStatus" maxOccurs="unbounded"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>


    <!--   setStatusSEPAPaymentResponse -->
    <element name="setStatusSEPAPaymentResponse">
        <complexType>
            <sequence>
                <element name="Result" type="frg:OBSResult"/>  <!-- kolekce odchozich SEPA plateb do ECB-NBS -->
            </sequence>
        </complexType>
    </element>


    <!--   setSEPAConfirmOutPaymentInOBSRequest-->
    <element name="setSEPAConfirmOutPaymentInOBSRequest">
        <annotation>
            <documentation>
                kolekce odchozich SEPA plateb k potvrzeni zpracovani v systemu SIPS
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="SEPAConfirmOutPayment" type="frg:SEPAConfirmOutPayment" maxOccurs="unbounded"/>  <!-- aktualni datum -->
            </sequence>
        </complexType>
    </element>

    <!--   setSEPAConfirmOutPaymentInOBSResponse -->
    <element name="setSEPAConfirmOutPaymentInOBSResponse">
        <complexType>
            <sequence>
                <element name="Result" type="frg:OBSResult"/>  <!-- kolekce odchozich SEPA plateb do ECB-NBS -->
            </sequence>
        </complexType>
    </element>

    <!--   SetSepaPmtRtrForImportToOBSRequest-->
    <element name="setSepaPmtRtrForImportToOBSRequest">
        <annotation>
            <documentation>
                prichozi SEPA vratky do OBS
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="GrpHdr" type="frg:PmtRtrGrpHdr"/>                           <!-- hlavicka pro sepa vratky -->
                <element name="SEPAPmtRtrs" type="frg:SEPAPmtRtr" maxOccurs="unbounded"/>  <!-- kolekce prichozi SEPA vratek do OBS -->
            </sequence>
        </complexType>
    </element>

    <!--   SetSepaPmtRtrForImportToOBSResponse -->
    <element name="setSepaPmtRtrForImportToOBSResponse">
        <complexType>
            <sequence>
                <element name="SepaPmtRtrAccountingInfo" type="frg:ZPSPaymentAccountingInfo" maxOccurs="unbounded"/>  <!-- kolekce s vysledky zauctovani prichozich zps vratek -->
            </sequence>
        </complexType>
    </element>

</schema>
