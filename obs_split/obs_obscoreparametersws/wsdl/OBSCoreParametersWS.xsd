<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCoreParametersWS/">

  <xsd:element name="getGlobalParametersResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="maxFailedUsernamePassword"
          type="xsd:int" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Maximální počet zadání nesprávné
              kombinace login a heslo pro vynucení
              3tího údaje.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="maxFailedUsernameParameter"
          type="xsd:int" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Maximální počet zadání nesprávné
              kombinace login a 3tí údaj pro
              blokaci účtu.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element
          name="maxFailedUsernamePasswordParameter" type="xsd:int"
          minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Maximální počet zadání nesprávné
              kombinace login, 3tí údaj a heslo
              pro blokaci účtu.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="smsMaxSend" type="xsd:int"
          minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Maximální počet pokusů o
              vygenerování sms uživatelem.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="smsValidity" type="xsd:int"
          minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              platnost SMS v sekundách
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="smsMaxAttempt" type="xsd:int"
          minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Počet pokusů k zadání sms kódu na akci.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="smsAttemptInterval"
          type="xsd:int" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Minimální intertval v sekundách mezi
              dvěma požadavky na poslání SMS
              uživatelem.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="billManagerSaltCode"
          type="xsd:string" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Textový řetězec, který se přidává do
              kontrolní hashe při generování URL
              adresy pro potvrzení / zamítnutí
              výzvy Bill Managera.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="minBalChangeNotifCZK"
          type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Minimální částka transakce v CZK pro
              zaslání notifikace. Notifikace je
              zaslána pokud se disponibilní
              zůstatek na účtu změní o min. tuto
              částku.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="minBalChangeNotifUSD"
          type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Minimální částka transakce v USD pro
              zaslání notifikace. Notifikace je
              zaslána pokud se disponibilní
              zůstatek na účtu změní o min. tuto
              částku.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="minBalChangeNotifEUR"
          type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Minimální částka transakce v EUR pro
              zaslání notifikace. Notifikace je
              zaslána pokud se disponibilní
              zůstatek na účtu změní o min. tuto
              částku.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="minBalNotifCZK"
          type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Hodnota minimálního zůstatku, kdy se
              zasílá notifikace pro účty v CZK.
              Notifikace je zaslána, pokud
              disponibilní zůstatek na účtu klesne
              pod tuto hranici.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="minBalNotifUSD"
          type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Hodnota minimálního zůstatku, kdy se
              zasílá notifikace pro účty v USD.
              Notifikace je zaslána, pokud
              disponibilní zůstatek na účtu klesne
              pod tuto hranici.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="minBalNotifEUR"
          type="xsd:decimal" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              Hodnota minimálního zůstatku, kdy se
              zasílá notifikace pro účty v EUR.
              Notifikace je zaslána, pokud
              disponibilní zůstatek na účtu klesne
              pod tuto hranici.
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="maxClientCurrentAccount"
          type="xsd:int" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              maximální počet bežných účtů, které
              klient může vlastnit
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="maxClientSavingAccount"
          type="xsd:int" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              maximální počet spořících účtů,
              které klient může vlastnit
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element
          name="maxClientSavingAccForCurrency" type="xsd:int" maxOccurs="1"
          minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              maximální počet spořících účtů,
              které klient může vlastnit pro danou
              měnu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element
          name="maxAccountNotificationContacts" type="xsd:int" maxOccurs="1"
          minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>maximum number of notification contacts for one bank account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="identsSwiftCode"
          type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>swift kód vlastní banky</xsd:documentation>
                        </xsd:annotation>
        </xsd:element>
        <xsd:element name="identsBaCode"
          type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>kód vlastní banky dle ČNB</xsd:documentation>
                        </xsd:annotation>
        </xsd:element>
      <xsd:element name="maximumCreditExposure" type="xsd:decimal"
                       minOccurs="1" maxOccurs="1">
              <xsd:annotation>
                  <xsd:documentation>
                      Hodnota maximální vyše půjčky
                  </xsd:documentation>
              </xsd:annotation>
          </xsd:element>
          <xsd:element name="maximalAirBankCreditExposure" type="xsd:decimal"
                       minOccurs="1" maxOccurs="1">
              <xsd:annotation>
                  <xsd:documentation>
                      Hodnota maximální vyše půjčky
                  </xsd:documentation>
              </xsd:annotation>
          </xsd:element>
        <xsd:element name="twoPhaseLoginRequired" type="xsd:boolean" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>
              true pokud je bankou vynucen dvoufazovy login
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="maxStmtInterval" type="xsd:decimal">
          <xsd:annotation>
            <xsd:documentation>
              maximalni obdobi pro generovani mimoradneho vypisu
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getGlobalParametersRequest">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
        <xsd:element name="getGPCardResponse">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="maxAccountDispoRecords"
                type="xsd:int" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Maximální počet requestů v jedné kolekci pro získání disponibilního zůstatku. Omezení se používá ve službě obsAccountWS.getAccountDispo.</xsd:documentation>
                        </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxCTLSCardsForPersonPA" type="xsd:int" minOccurs="1" maxOccurs="1"/>
              <xsd:element name="maxCTLSCardsForContractPA" type="xsd:int" minOccurs="1" maxOccurs="1"/>
              <xsd:element name="maxSTICCardsForPersonPA" type="xsd:int" minOccurs="1" maxOccurs="1"/>
              <xsd:element name="maxSTICCardsForContractPA" type="xsd:int" minOccurs="1" maxOccurs="1"/>
              <xsd:element name="maxTransac155Records" type="xsd:int" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                      <xsd:documentation>Maximalni pocet transakci v requestu OBSCardTransactionWS.Transac155</xsd:documentation>
                  </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxTransac155Threads" type="xsd:int" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                      <xsd:documentation>Maximalni pocet vlaken, ve kterych je volana operace OBSCardTransactionWS.Transac155</xsd:documentation>
                  </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxSetEvent189Records" type="xsd:int" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                      <xsd:documentation>Maximalni pocet karet v requestu OBSCardManagementWS.SetEvent189</xsd:documentation>
                  </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPCardRequest">
          <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPCommunicationChannelsResponse">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="maxSMSRecords"
                type="xsd:int">
                        <xsd:annotation>
                          <xsd:documentation>Maximální počet záznamů, které se dá poslat do funkce obsMessageRequestWS.setSMSRequest</xsd:documentation>
                        </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxEmailRecords"
                type="xsd:int">
                        <xsd:annotation>
                          <xsd:documentation>Maximální počet záznamů, které se dá poslat do funkce obsMessageRequestWS.setEmailRequest.</xsd:documentation>
                        </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxCallRecords"
                type="xsd:int">
                        <xsd:annotation>
                          <xsd:documentation>Maximální počet záznamů, které se dá poslat do funkce obsMessageRequestWS.setCallRequest</xsd:documentation>
                        </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPCommunicationChannelsRequest">
          <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPTransactionLimitRequest">
          <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPTransactionLimitResponse">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="maxTransactionLimitIB"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Maximální transakční limit pro
                    internetové bankovnictví
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxTransactionLimitICC"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Maximální transakční limit pro
                    telefonní bankovnictví
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxTransactionLimitBranch"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Maximální transakční limit pro
                    pobočku
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="maxTransactionLimitSPB"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Maximální transakční limit pro
                    mobilní bankovnictví
                  </xsd:documentation>
                        </xsd:annotation>
              </xsd:element>
               <xsd:element name="maxTransactionLimitOPENAPI"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Maximální transakční limit pro
                    Open API
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="minTransactionLimitIB"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Minimální transakční limit pro
                    internetové bankovnictví
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="minTransactionLimitICC"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Minimální transakční limit pro
                    telefonní bankovnictví
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="minTransactionLimitBranch"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Minimální transakční limit pro
                    pobočku
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="minTransactionLimitSPB"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>
                    Minimální transakční limit pro
                    mobilní bankovnictví
                  </xsd:documentation>
                        </xsd:annotation>
              </xsd:element>
              <xsd:element name="minTransactionLimitOPENAPI"
                type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>
                    Minimální transakční limit pro
                    Open API
                  </xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPLoanRequest">
          <xsd:complexType>
            <xsd:sequence>

            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getGPLoanResponse">
          <xsd:complexType>
            <xsd:sequence>

              <xsd:element name="maxDayOfLoanUtilizationIB"
                type="xsd:int" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Lhůta (počet dní), po které může walkin čerpat úvěr pro Způsob podpisu = IB </xsd:documentation></xsd:annotation>
              </xsd:element>
              <xsd:element name="maxDayOfLoanUtilizationPost"
                type="xsd:int" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Lhůta (počet dní), po které může walkin čerpat úvěr pro Způsob podpisu = Pošta </xsd:documentation></xsd:annotation>
              </xsd:element>
              <xsd:element name="minMortgageLoanInstalment" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                   <xsd:documentation>minimal amount of extra instalment on motgage loan</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>

        <xsd:element name="getInstantPaymentParametersRequest">
          <xsd:complexType>
            <xsd:sequence>

            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="getInstantPaymentParametersResponse">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="debitLimit" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                   <xsd:documentation>max. amount of instant payment in CZK</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>

</xsd:schema>
