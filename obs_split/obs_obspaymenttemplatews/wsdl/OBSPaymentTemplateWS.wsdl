<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsPaymentTemplateWS/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="OBSPaymentTemplateWS"
	targetNamespace="http://arbes.com/ib/core/ppf/ws/obsPaymentTemplateWS/" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">
	<wsdl:types>
		<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsPaymentTemplateWS/" xmlns:Q1="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
			xmlns:Q2="http://arbes.com/ib/core/ppf/ws/common/">
			<xsd:include schemaLocation="OBSPaymentTemplateWS.xsd" />
			<xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="findPaymentTemplateRequest1">
		<wsdl:part name="parameters" element="tns:findPaymentTemplateRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="findPaymentTemplateResponse1">
		<wsdl:part name="parameters" element="tns:findPaymentTemplateResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getPaymentTemplateRequest1">
		<wsdl:part name="parameters" element="tns:getPaymentTemplateRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getPaymentTemplateResponse1">
		<wsdl:part name="parameters" element="tns:getPaymentTemplateResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setPaymentTemplateRequest1">
		<wsdl:part name="parameters" element="tns:setPaymentTemplateRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setPaymentTemplateResponse1">
		<wsdl:part name="parameters" element="tns:setPaymentTemplateResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setForeignPaymentTemplateRequest1">
		<wsdl:part name="parameters" element="tns:setForeignPaymentTemplateRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setForeignPaymentTemplateResponse1">
		<wsdl:part name="parameters" element="tns:setForeignPaymentTemplateResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="deletePaymentTemplateRequest1">
		<wsdl:part name="parameters" element="tns:deletePaymentTemplateRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="deletePaymentTemplateResponse1">
		<wsdl:part name="parameters" element="tns:deletePaymentTemplateResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="findPaymentTemplateFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getPaymentTemplateFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setPaymentTemplateFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setForeignPaymentTemplateFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="deletePaymentTemplateFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getPaymentTemplateInstanceInfoRequest">
		<wsdl:part name="parameters" element="tns:getPaymentTemplateInstanceInfoRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getPaymentTemplateInstanceInfoResponse">
		<wsdl:part name="parameters" element="tns:getPaymentTemplateInstanceInfoResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getPaymentTemplateInstanceInfoFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:portType name="obsPaymentTemplateWS">
		<wsdl:operation name="findPaymentTemplate">
			<wsdl:documentation>filtr pro šablon plateb

požadované podmínky:
přihlášeného uživatele: ano
vyžadován businessContext:  ano

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_UNSUPPORTED_FILTER / xx / : tato chyba se týká fitrovacích funkcí - filtr nepodporuje daný filtrovací atribut xx
CLERR_UNSUPPORTED_ORDER / xx / : tato chyba se týká fitrovacích funkcí - filtr nepodporuje daný řadící atribut xx
</wsdl:documentation>
			<wsdl:input message="tns:findPaymentTemplateRequest1"></wsdl:input>
			<wsdl:output message="tns:findPaymentTemplateResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:findPaymentTemplateFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="getPaymentTemplate">
			<wsdl:documentation>metoda pro načtení informací o platební šabloně

požadované podmínky:
přihlášeného uživatele: ano
vyžadován businessContext:  ano

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
</wsdl:documentation>
			<wsdl:input message="tns:getPaymentTemplateRequest1"></wsdl:input>
			<wsdl:output message="tns:getPaymentTemplateResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:getPaymentTemplateFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="setPaymentTemplate">
			<wsdl:documentation>založení / editace šablony platebního příkaz

požadované podmínky:
přihlášeného uživatele: ano
vyžadován businessContext:  ano

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NUMBER_IS_GREATER / amount / maximalni castka : byla prekrocena maximalni castka
CLERR_IS_MANDATORY / idProfile/ : povinná položka
CLERR_IS_MANDATORY / templateName / : povinná položka
CLERR_IS_MANDATORY / contraAccountNumber / : povinná položka
CLERR_CZ_ACCOUNT_NUMBER / contraAccountNumber / : číslo protiúčtu neodpovídá platnému českému formátu
CLERR_IS_MANDATORY / contraBankCode / : povinná položka
CLERR_ENUM_ERROR / contraBankCode / : neplatný kód banky
CLERR_IS_MANDATORY / requiredCurrency / : povinná položka
CLERR_IS_MANDATORY / amountInRequiredCurrency / : povinná položka
ERROR_BAD_SIGNATURE / signature / : neplatná autorizace
PPF_CLERR_DUPLICATE_VALUE / templateName / :duplicitní jméno šablony
CLERR_NO_ACCESS / / : uživatel nemá právo editovat šablonu
</wsdl:documentation>
			<wsdl:input message="tns:setPaymentTemplateRequest1"></wsdl:input>
			<wsdl:output message="tns:setPaymentTemplateResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:setPaymentTemplateFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="setForeignPaymentTemplate">
			<wsdl:documentation>Validate and creates or modifies (if idPaymentTemplate is specified) foreign payment template. Returns ID of the created or modified payment template.

Required conditions:
logged in user: yes
required businessContext: yes

Generated faults:
code / atribute / value : description
GENERAL_ERROR /GENERAL_ERROR / : General system error
CLERR_TIMEOUT / GENERAL_ERROR / : System was not able to process the request in time. Changes were rolled back.
CLERR_NUMBER_IS_GREATER / amount / maximum amount : Exceeding the maximum amount
CLERR_IS_MANDATORY / idProfile/ : Mandatory item
CLERR_IS_MANDATORY / templateName / : Mandatory item
CLERR_NO_ACCESS / / : The user is not allowed to edit the template
PPF_CLERR_DUPLICATE_VALUE / templateName / : Duplicate template name
WWW_INV_BANKCOUNTRY / bankCountry / : Invalid destination country
WWW_INV_CURRENCY / accountCurrencyCredit / : Invalid currency for creating a foreign payment
ERROR_BAD_SIGNATURE / signature / : Invalid authorization
WWW_IBAN_NUMBER / accountNumberCredit / : Invalid IBAN of credit account
WWW_INV_BANKCODE / contraBankCode / : Invalid BIC
CLERR_IBAN_BIC_ERROR / contraBankCode / : Does not match the country of the IBAN and the BIC, if there is no SEPA (assuming complete cancellation of validation in the future)
CLERR_STRING_TOO_LONG / accountFullNameCredit / : Address of reciever is longer than allowed by swift format 3*35x
CLERR_STRING_TOO_LONG / accountNameCredit / : Name of reciever is longer than allowed by swift format 35x
CLERR_STRING_TOO_LONG / accountNumberCredit / : Number of credit account is longer than allowed by swift format 35x
CLERR_STRING_TOO_LONG / bankFullNameCredit / : Address of credit bank is longer than allowed by swift format 3*35x
CLERR_STRING_TOO_LONG / bankNameCredit / : Name of credit bank is longer than allowed by swift format 35x
CLERR_STRING_TOO_LONG / bankCodeCredit / : Code of credit bank is longer than allowed by swift format format 35x
CLERR_STRING_TOO_LONG / messageForBank / : Message for bank is longer than allowed by swift format 6*35x
CLERR_INVALID_CHARACTER / contraBankName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraBankFullName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraAccountName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / contraAccountFullName / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForSender / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForReceiver / : Illegal characters for the swift format
CLERR_INVALID_CHARACTER / messageForBank / : Illegal characters for the swift format
CLERR_NOT_CROSSBORDER / GENERAL_ERROR / : The payment is not foreign payment (payment into a czech bank in czk). Must be entered as a domestic
CLERR_NOT_CROSSBORDER_AB / GENERAL_ERROR / : The payment is not foreign payment (payment into AirBank). Must be entered as a domestic
CLERR_INVALID_CHARACTER / EndToEnd / : Illegal characters for the swift format or illegal specific characters for EndToEnd reference field

</wsdl:documentation>
			<wsdl:input message="tns:setForeignPaymentTemplateRequest1"></wsdl:input>
			<wsdl:output message="tns:setForeignPaymentTemplateResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:setForeignPaymentTemplateFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="deletePaymentTemplate">
			<wsdl:documentation>smazání šablony platebního příkazu

požadované podmínky:
přihlášeného uživatele: ano
vyžadován businessContext:  ano

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NO_DATA_FOUND / PERSDOC / : neplatný idProfile
</wsdl:documentation>
			<wsdl:input message="tns:deletePaymentTemplateRequest1"></wsdl:input>
			<wsdl:output message="tns:deletePaymentTemplateResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:deletePaymentTemplateFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="getPaymentTemplateInstanceInfo">
			<wsdl:documentation>
                Nalezení informací o instancích šablon

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </wsdl:documentation>
			<wsdl:input message="tns:getPaymentTemplateInstanceInfoRequest"></wsdl:input>
			<wsdl:output message="tns:getPaymentTemplateInstanceInfoResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:getPaymentTemplateInstanceInfoFault"></wsdl:fault>
        </wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="obsPaymentTemplateWSSOAP" type="tns:obsPaymentTemplateWS">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="findPaymentTemplate">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getPaymentTemplate">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setPaymentTemplate">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setForeignPaymentTemplate">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deletePaymentTemplate">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getPaymentTemplateInstanceInfo">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="obsPaymentTemplateWS">
		<wsdl:port binding="tns:obsPaymentTemplateWSSOAP" name="obsPaymentTemplateWSSOAP">
			<soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsPaymentTemplateWS" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
