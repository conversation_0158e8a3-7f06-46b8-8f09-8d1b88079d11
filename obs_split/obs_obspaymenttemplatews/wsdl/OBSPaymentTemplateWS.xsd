<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsPaymentTemplateWS/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:Q1="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
	xmlns:Q2="http://arbes.com/ib/core/ppf/ws/common/">
	<xsd:import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"/>
	<xsd:import schemaLocation="../xsd/PaymentTemplateTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
	<xsd:import schemaLocation="../xsd/AutentizationAuthorization.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
	<xsd:element name="findPaymentTemplateRequest">

		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="filter" type="Q1:SelectFilter" maxOccurs="1" minOccurs="1">
					<xsd:annotation>
						<xsd:documentation>filtrační podmínky:

							IDCATEGORY - LONG - id kategorie, nepovinné
							TYPE - nepovinné - výčet dle PaymentTemplateType, operace - eq, in

							Třídění
							CATEGORYID, NAME
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="findPaymentTemplateResponse">

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="idPaymentTemplate" type="xsd:long" maxOccurs="unbounded" minOccurs="0"></xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getPaymentTemplateRequest">

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="idPaymentTemplate" type="xsd:long" maxOccurs="unbounded" minOccurs="1"></xsd:element>
        <xsd:element name="templatePart" type="Q2:TemplatePartEnum" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>required type of data of payment templates, see the list of TemplatePartEnum and documentation of properties in PaymentTemplateTO</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getPaymentTemplateResponse">

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="paymentTemplate" type="Q2:PaymentTemplateTO" maxOccurs="unbounded" minOccurs="0"></xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setPaymentTemplateRequest">

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="paymentTemplate" type="Q2:PaymentTemplateTO" maxOccurs="1" minOccurs="1">
				</xsd:element>
				<xsd:element name="validity" type="xsd:boolean" maxOccurs="1" minOccurs="1">
				</xsd:element>
				<xsd:element name="authorization" type="Q2:AuthType" maxOccurs="1" minOccurs="1"></xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setPaymentTemplateResponse">

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="idPaymentTemplate" type="xsd:long" maxOccurs="1" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>primární klíč nově založeného / editovaného šablony</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="setForeignPaymentTemplateRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="paymentTemplate" type="Q2:BaseForeignPaymentTemplateTO" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>object of type SepaPaymentTemplateTO, PsdPaymentTemplateTO, NonPsdPaymentTemplateTO or ForeignPaymentTemplateTO</xsd:documentation>
          </xsd:annotation>
				</xsd:element>
				<xsd:element name="validity" type="xsd:boolean" maxOccurs="1" minOccurs="1">
				</xsd:element>
				<xsd:element name="authorization" type="Q2:AuthType" maxOccurs="1" minOccurs="1"></xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setForeignPaymentTemplateResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="idPaymentTemplate" type="xsd:long" maxOccurs="1" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>identification number of new or edited payment template</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="deletePaymentTemplateRequest">

		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="idPaymentTemplate" type="xsd:long" maxOccurs="1" minOccurs="1"></xsd:element>
				<xsd:element name="authorization" type="Q2:AuthType" maxOccurs="1" minOccurs="1" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="deletePaymentTemplateResponse">

		<xsd:complexType>
			<xsd:sequence>

			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="getPaymentTemplateInstanceInfoRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="idPaymentTemplate" type="xsd:long" minOccurs="1" maxOccurs="unbounded" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getPaymentTemplateInstanceInfoResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="instanceInfo" type="Q2:PaymentTemplateInstanceInfoTO" minOccurs="0" maxOccurs="unbounded" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>