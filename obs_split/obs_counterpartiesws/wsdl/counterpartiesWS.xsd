<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:c="http://airbank.cz/obs/ws/counterparty"
        targetNamespace="http://airbank.cz/obs/ws/counterpartiesWS">

    <xsd:import namespace="http://airbank.cz/obs/ws/counterparty" schemaLocation="../xsd/counterparty.xsd"/>

    <xsd:element name="changeVisibilityRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            Counterparty id.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="visible" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>
                            Is this counterparty visible for client?
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="changeVisibilityResponse">
        <xsd:annotation>
            <xsd:documentation>The response is empty.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="createCounterpartyRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="counterparty" type="c:AbstractCounterparty">
                    <xsd:annotation>
                        <xsd:documentation>
                            Counterparty to be created.
                            - Attribute id cannot be specified. It is automatically generated.
                            - Attribute contractNumber cannot be specified, it is taken from businessContext.
                              You can create counterparty only for yourself.
                            - Attribute authorId cannot be specified. It is filled in automatically form businessContext
                            - Attribute brand
                              - Only the attribute ID can be filled
                              - Attribute ID must belong to an existing brand
                            - Attribute reuseExisting
                              - not mandatory
                              - default false
                              - if existing counterparty is found, return its iddentification
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="createCounterpartyResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            Identification of created counterparty.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="deleteCounterpartyRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="deleteCounterpartyResponse">
        <xsd:annotation>
            <xsd:documentation>The response is empty.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="findCounterpartiesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="regNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Registration number for counterparties to be returned.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="taxNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Tax identification number (TIN) for counterparties to be returned.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="name" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Name for counterparties to be returned.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="externalId" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            External counterparty (RCM) id for counterparties to be returned.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="bankAccount" type="c:CounterpartyAccount" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Bank account for counterparties to be returned.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="visibleOnly" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            if false, includes invisible cp in result set
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="counterpartyLabel" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Counterparty label used to match counterparty with brand
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="type" type="c:CounterpartyType" minOccurs="0" maxOccurs="4"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="findCounterpartiesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getCounterpartiesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="id" type="xsd:long" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getCounterpartiesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="counterparty" type="c:AbstractCounterparty" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="linkCounterpartyRequest">
        <xsd:annotation>
            <xsd:documentation>Link the counterparty to its identifications (bank accounts, MCC)</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="relations" type="c:CounterpartyRelations">
                    <xsd:annotation>
                        <xsd:documentation>Relations to be created.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="linkCounterpartyResponse">
        <xsd:annotation>
            <xsd:documentation>The response is empty.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="modifyCounterpartiesRequest">
        <xsd:annotation>
            <xsd:documentation>
                - Create new counterparties
                - Update counterparties
                - Link counterparties to its identifications
                - Delete counterparties
                - Rename counterparties
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="createCounterparty" type="c:AbstractCounterparty" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Counterparties to be created.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="updateCounterparty" type="c:AbstractCounterparty" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Counterparties to be updated.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="linkCounterparty" type="c:CounterpartyRelations" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Relations to be created.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="deleteCounterparty" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Counterparty identification (id) to be deleted</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="renameCounterparty" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="id" type="xsd:long"/>
                            <xsd:element name="name" type="xsd:string"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="modifyCounterpartiesResponse">
        <xsd:annotation>
            <xsd:documentation>The response is empty.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="createdCounterpartyId" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Created counterparty id.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="updateCounterpartyRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="counterparty" type="c:AbstractCounterparty">
                    <xsd:annotation>
                        <xsd:documentation>
                            Counterparty to be updated.
                            - Attribute id have to be specified.
                            - It is not possible to change counterparty type.
                            - Attribute contractNumber cannot be specified. It cannot be changed.
                            - Attribute externalId cannot be specified. It cannot be changed.
                            - Attribute authorId cannot be specified.
                            - Attribute brand
                              - Only the attribute ID can be filled.
                              - Attribute ID must belong to an existing brand.
                              - When brand is not used then unlink brand from counterparty.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="updateCounterpartyResponse">
        <xsd:annotation>
            <xsd:documentation>The response is empty.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="listCounterpartiesRequest">
        <xsd:annotation>
            <xsd:documentation>
                Note: Final result set is union of results sets of all filters
                      Counterparty types in all filters have to be disjunctive.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="c:ListCounterpartiesFilter" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="listCounterpartiesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="counterparty" type="c:AbstractCounterparty" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getBrandsRequest">
        <xsd:annotation>
            <xsd:documentation>
                Request is empty (no filter)
                Returns all existing brands.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getBrandsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="brand" type="c:Brand" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="linkBrandsRequest">
        <xsd:annotation>
            <xsd:documentation>
                Link Brands to Counterparties.
                When Brand identifications is empty, then operation cancels the Counterparty's link to the Brand.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="association" type="c:BrandCounterpartyAssociation" minOccurs="0" maxOccurs="unbounded">
                     <xsd:annotation>
                         <xsd:documentation>
                             Brand and counterparty to link
                         </xsd:documentation>
                     </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="linkBrandsResponse">
        <xsd:annotation>
            <xsd:documentation>The response is empty.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>




