<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://airbank.cz/obs/ws/obsBalanceWS/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    name="obsBalanceWS"
    targetNamespace="http://airbank.cz/obs/ws/obsBalanceWS/"
    >

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/obsBalanceWS/">
            <xsd:include schemaLocation="OBSBalanceWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="getTotalDepositBalanceRequest">
        <part name="getTotalDepositBalanceRequest" element="tns:getTotalDepositBalanceRequest"/>
    </message>

    <message name="getTotalDepositBalanceResponse">
        <part name="getTotalDepositBalanceResponse" element="tns:getTotalDepositBalanceResponse"/>
    </message>

    <portType name="obsBalance">
        <operation name="getTotalDepositBalance">
            <input message="tns:getTotalDepositBalanceRequest"/>
            <output message="tns:getTotalDepositBalanceResponse"/>
            <fault name="fault" message="tns:faultMessage"/>
        </operation>
    </portType>

    <binding name="obsBalanceSOAP" type="tns:obsBalance">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="getTotalDepositBalance">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </fault>
        </operation>
    </binding>

    <service name="obsBalanceWS">
        <port binding="tns:obsBalanceSOAP" name="obsBalanceSOAP">
            <soap:address location="/ws/obsBalanceWS"/>
        </port>
    </service>
</definitions>
