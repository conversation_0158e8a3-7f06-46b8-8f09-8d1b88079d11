<?xml version="1.0" encoding="UTF-8"?>
<schema
    targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
    elementFormDefault="qualified" version="1.0"
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:od="http://airbank.cz/obs/ws/overdraft">

    <import schemaLocation="Overdraft.xsd" namespace="http://airbank.cz/obs/ws/overdraft"/>

    <complexType name="BankAccountTO">
        <annotation>
            <documentation>
                Bankovní účet.
                Nemusí být naplněny všechny údaje-viz.AccountPartEnum
                a dokumentace jednotlivých údajů. Není-li uvedena Part,
                jedná se o Part=BASE.
            </documentation>
        </annotation>
        <sequence>
            <element name="idBankAccount" type="long">
                <annotation>
                    <documentation>prim<PERSON><PERSON><PERSON> k<PERSON></documentation>
                </annotation>
            </element>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>číslo účtu</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>iso kód měny</documentation>
                </annotation>
            </element>
            <element name="accountTypeCode" type="tns:BankAccountTypeCode" minOccurs="0">
                <annotation>
                    <documentation>typ účtu, Part=KIND</documentation>
                </annotation>
            </element>
            <element name="fullName" type="string">
                <annotation>
                    <documentation>
                    jméno vlastníka účtu (podle ČNB)
                </documentation>
                </annotation>
            </element>
            <element name="userBankAccountName" type="string" minOccurs="0">
                <annotation>
                    <documentation>uživatelské jméno účtu</documentation>
                </annotation>
            </element>
            <element type="tns:BankAccountStatus" name="status">
                <annotation>
                    <documentation>OBS stav účtu</documentation>
                </annotation>
            </element>
            <element name="defaultAccount" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        hlavní účet klienta, Part=IS_PRIMARY
                    </documentation>
                </annotation>
            </element>
            <element name="owner" type="boolean">
                <annotation>
                    <documentation>je vlastníkem účtu</documentation>
                </annotation>
            </element>
            <element name="balanceAccounting" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        účetní zůstatek v měně účtu, Part=BALANCE
                    </documentation>
                </annotation>
            </element>
            <element name="balanceAvailable" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        disponibilní zůstatek v měně účtu, Part=BALANCE
                    </documentation>
                </annotation>
            </element>
            <element name="blockCardSum" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        suma karetních blokací v měně účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockCardCount" type="int" minOccurs="0">
                <annotation>
                    <documentation>
                        počet karetních blokací na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockBankSum" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        suma bankovních blokací na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockBankCount" type="int" minOccurs="0">
                <annotation>
                    <documentation>
                        počet bankovních blokací na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockSazkaSum" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        suma sazka blokací na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockSazkaCount" type="int" minOccurs="0">
                <annotation>
                    <documentation>
                        počet sazka blokací na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockInstantPaymentSum" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        suma blokací okamžitých plateb na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="blockInstantPaymentCount" type="int" minOccurs="0">
                <annotation>
                    <documentation>
                        počet blokací okamžitých plateb na účtu, Part=BLOCKING
                    </documentation>
                </annotation>
            </element>
            <element name="iban" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        číslo účtu ve formátu IBAN
                    </documentation>
                </annotation>
            </element>
            <element name="posInterestRate" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        úroková míra účtu, Part=POS_INTEREST_RATE
                    </documentation>
                </annotation>
            </element>
            <element name="personRelations" type="tns:PersonRelationsToAccount" minOccurs="0">
                <annotation>
                    <documentation>persons related to account (owner, disponents, card holdes), Part=PERSON_REL</documentation>
                </annotation>
            </element>
            <element name="notificationContacts" type="tns:AccountContacts" minOccurs="0">
                <annotation>
                    <documentation>notification contacts related to account, Part=NOTIF_CONTACT</documentation>
                </annotation>
            </element>
            <element name="loanAccount" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        true - účet slouží pro splácení úvěru (má na sobě
                        navázené nějaké aktivní úvěry), Part=IS_LOAN
                    </documentation>
                </annotation>
            </element>
            <element name="inDebt" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        true - k účtu existuje pohledávka, Part=IS_IN_DEBT
                    </documentation>
                </annotation>
            </element>
            <element name="interestSchemeAddicted" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>true - účet je závislý na úrovni uročení, Part=INTEREST_SEGMENT</documentation>
                </annotation>
            </element>
            <element name="overdraftExists" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>
                        true - k účtu existuje kontokorent
                    </documentation>
                </annotation>
            </element>
            <element name="overdraftDetail" type="od:OverdraftSimpleDetailTO" minOccurs="0">
                <annotation>
                    <documentation>Obsahuje informace o KTK, ktery je svazan s timto uctem. Jestli ucet KTK nema, element neexistuje</documentation>
                </annotation>
            </element>
            <element name="lastCreditActivityAt" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>
                        Poslední kreditní operace na účtu
                    </documentation>
                </annotation>
            </element>
            <element name="dateCreated" type="date">
                <annotation>
                    <documentation>
                        Datum založení účtu
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
    <complexType name="AccountTO">
        <annotation>
            <documentation>
                Bankovní účet part.
            </documentation>
        </annotation>
        <sequence>
            <element name="accountId" type="long">
                <annotation>
                    <documentation>primární klíč</documentation>
                </annotation>
            </element>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>číslo účtu</documentation>
                </annotation>
            </element>
            <element name="accountStatus" type="tns:BankAccountBlockStatus">
                <annotation>
                    <documentation>status účtu</documentation>
                </annotation>
            </element>
            <element name="accountType" type="tns:BankAccountBlockTypeCode">
                <annotation>
                    <documentation>typ účtu</documentation>
                </annotation>
            </element>
            <element name="ownerCuid" type="long">
                <annotation>
                    <documentation>cuid vlastnika uctu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="BankAccountKindCode">
        <annotation>
            <documentation>druhy účtů</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>běžný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAVING_ACCOUNT">
                <annotation>
                    <documentation>spořicí účet</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="AccessChannel">
        <annotation>
            <documentation>
                Přístupový kanál/aplikace
            </documentation>
        </annotation>
            <restriction base="string">
                <enumeration value="IB"/>
                <enumeration value="SPB"/>
                <enumeration value="ICC"/>
                <enumeration value="BRANCH"/>
                <enumeration value="OPENAPI"/>
            </restriction>
    </simpleType>

    <simpleType name="BankAccountTypeCode">
        <annotation>
            <documentation>typy účtů</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="SAVING_ACCOUNT">
                <annotation>
                    <documentation>Spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHILD_SAVING_ACCOUNT">
                <annotation>
                    <documentation>Dětský spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>Běžný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="JPK_ACCOUNT">
                <annotation>
                    <documentation>JPK účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSTALLMENT_ACCOUNT">
                <annotation>
                    <documentation>Splátkový účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PROTECTED_ACCOUNT">
                <annotation>
                    <documentation>Chráněný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTREPRENEUR_CURRENT_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTREPRENEUR_SAVING_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský spořicí účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LEGAL_ENTITY_CURRENT_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský účet PO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LEGAL_ENTITY_SAVING_ACCOUNT">
                <annotation>
                    <documentation>Podnikatelský spořicí účet PO</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="LegalSegment">
        <annotation>
            <documentation>právní segment vlastníka. Konstanty podle MDM číselníku LegalSegment</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CUSTOMER"/>
            <enumeration value="ENTREPRENEUR"/>
            <enumeration value="LEGAL_ENTITY"/>
        </restriction>
    </simpleType>

    <simpleType name="BankAccountBlockTypeCode">
        <annotation>
            <documentation>Typy zablokonanych účtu</documentation>
        </annotation>
        <restriction base="tns:BankAccountTypeCode">
            <enumeration value="SAVING_ACCOUNT">
                <annotation>
                    <documentation>spořící účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>běžný účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PROTECTED_ACCOUNT">
                <annotation>
                    <documentation>chráněný účet</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="BankAccountStatus">
        <annotation>
            <documentation>Core stavy</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="BAS_ACTIVE">
                <annotation>
                    <documentation>Aktivní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_BLOCKED">
                <annotation>
                    <documentation>Blokovaný pro příchozí i odchozí tr.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_READY_TO_ENDED">
                <annotation>
                    <documentation>K uzavření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_BLOCKED_IN">
                <annotation>
                    <documentation>Blokovaný pro příchozí tr.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_BLOCKED_OUT">
                <annotation>
                    <documentation>Blokovaný pro    odchozí tr.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_ARCHIVE">
                <annotation>
                    <documentation>Stornovany</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_CREATED">
                <annotation>
                    <documentation>Demo / V návrhu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_PASIVE">
                <annotation>
                    <documentation>Pasivní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_ENDED">
                <annotation>
                    <documentation>Ukončený</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="BankAccountBlockStatus">
        <annotation>
            <documentation>Blokovany ucet stavy</documentation>
        </annotation>
        <restriction base="tns:BankAccountStatus">
            <enumeration value="BAS_BLOCKED">
                <annotation>
                    <documentation>Blokovaný pro příchozí i odchozí tr.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BAS_BLOCKED_OUT">
                <annotation>
                    <documentation>Blokovaný pro odchozí trans.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="AccountPartEnum">
        <restriction base="string">
            <enumeration value="BASE" />
            <enumeration value="KIND" />
            <enumeration value="IS_PRIMARY" />
            <enumeration value="IS_LOAN" />
            <enumeration value="BLOCKING" />
            <enumeration value="BALANCE" />
            <enumeration value="IS_IN_DEBT" />
            <enumeration value="POS_INTEREST_RATE" />
            <enumeration value="INTEREST_SEGMENT" />
            <enumeration value="PERSON_REL">
                <annotation>
                    <documentation>persons related to account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOTIF_CONTACT">
                <annotation>
                    <documentation>notification contacts related to account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OVERDRAFT">
                <annotation>
                    <documentation>information about overdraft on this account</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="AccountTransactionType">
        <annotation>
            <documentation>
                Výčet typů plateb pro filtr účtů.
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="DOMPAY">
                <annotation>
                    <documentation>Jednorázová tuzemská platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INTLPAY">
                <annotation>
                    <documentation>Jednorázová zahraniční platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MYPAY">
                <annotation>
                    <documentation>platba v rámci svých účtů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STNDPAY">
                <annotation>
                    <documentation>Trvalá platba</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SAVEPAY">
                <annotation>
                    <documentation>Pravidelné spoření</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INKPAY">
                <annotation>
                    <documentation>Povolení k inkasu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SIPOPAY">
                <annotation>
                    <documentation>SIPO</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="BankAccountBlockoutBusinessProcess">
        <annotation>
            <documentation>Business process ktery provedl blokaci uctu</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ANTIPHISHING" />
            <enumeration value="BANK" />
            <enumeration value="EXEC" />
            <enumeration value="INSOLV" />
            <enumeration value="JUDG" />
            <enumeration value="OTHER" />
            <enumeration value="STATE" />
        </restriction>
    </simpleType>

    <complexType name="AccountDispoTO">
        <sequence>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>Číslo účtu.</documentation>
                </annotation>
            </element>
            <element name="dispoAmount" type="decimal">
                <annotation>
                    <documentation>Disponibilní zůstatek.</documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>ISO kód měny zůstatku.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="AccountBalanceTO">
        <sequence>
            <element name="balanceDate" type="date">
                <annotation>
                    <documentation>Datum zůstatku.</documentation>
                </annotation>
            </element>
            <element name="idBankAccount" type="long">
                <annotation>
                    <documentation>ID bankovního účtu.</documentation>
                </annotation>
            </element>
            <element name="accountBalance" type="decimal">
                <annotation>
                    <documentation>Denní zůstatek účtu pro daný datum.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestRateTO">
        <sequence>
            <element name="interest" type="tns:InterestTO" maxOccurs="10">
                <annotation>
                    <documentation>Úroková sazba</documentation>
                </annotation>
            </element>
            <element name="validity" type="date">
                <annotation>
                    <documentation>Platnost</documentation>
                </annotation>
            </element>
            <element name="bankAccountType" type="tns:BankAccountTypeCode">
                <annotation>
                    <documentation>Typ účtu</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>Iso kód měny</documentation>
                </annotation>
            </element>
            <element name="interestingScheme" type="int" minOccurs="0">
                <annotation>
                    <documentation>Úroveň uročení</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestRateComponentTO">
        <annotation>
            <documentation>
                Objekt atributů složky úrokové míry
            </documentation>
        </annotation>
        <sequence>
            <element name="name">
                <annotation>
                    <documentation>
                        název složky úrokové míry (max 20 znaků)
                    </documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <maxLength value="20" />
                    </restriction>
                </simpleType>
            </element>
            <element name="interestRate" type="decimal">
                <annotation>
                    <documentation>výše úrokové míry</documentation>
                </annotation>
            </element>
            <element name="dateMonth" type="date">
                <annotation>
                    <documentation>datum určující měsíc platnosti složky úrokové míry. Z data se tedy použije pouze složka měsíce a roku</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestIncentiveGroupTO">
        <sequence>
            <element name="baseInterest" type="tns:InterestTO" maxOccurs="10">
                <annotation>
                    <documentation>Základní úroková sazba</documentation>
                </annotation>
            </element>
            <element name="validity" type="date">
                <annotation>
                    <documentation>Měsíc platnosti</documentation>
                </annotation>
            </element>
            <element name="individual" type="boolean">
                <annotation>
                    <documentation>Flag pro ručně přiřazenou</documentation>
                </annotation>
            </element>
            <element name="interestIncentives" type="tns:InterestIncentiveTO" maxOccurs="unbounded" minOccurs="0">
                <annotation>
                    <documentation>List jednotlivých pobídek</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestTO">
        <sequence>
            <element name="amountFrom" type="decimal">
                <annotation>
                    <documentation>částka od které úroková sazba platí</documentation>
                </annotation>
            </element>
            <element name="interest" type="decimal">
                <annotation>
                    <documentation>úroková sazba</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestIncentiveTO">
        <sequence>
            <element name="idInterestIncentive" type="long">
                <annotation>
                    <documentation>primární klíč pobídky</documentation>
                </annotation>
            </element>
            <element name="effective" type="boolean">
                <annotation>
                    <documentation>Účinná – (ANO/NE, dle Účinná OD a Účinná DO)</documentation>
                </annotation>
            </element>
            <element name="incentiveTypeCode" type="string">
                <annotation>
                    <documentation>kód typ pobídky </documentation>
                </annotation>
            </element>
            <element name="interest" type="decimal">
                <annotation>
                    <documentation>úrok</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>zkratka</documentation>
                </annotation>
            </element>
            <element name="msgIBEffective" type="string">
                <annotation>
                    <documentation>Marketingová zpráva – účinná </documentation>
                </annotation>
            </element>
            <element name="msgIBAssigned" type="string">
                <annotation>
                    <documentation>Marketingová zpráva  – neúčinná </documentation>
                </annotation>
            </element>
            <element name="validFrom" type="date">
                <annotation>
                    <documentation>Počátek účinnosti nabítky</documentation>
                </annotation>
            </element>
            <element name="effectiveTo" type="date">
                <annotation>
                    <documentation>Do kdy je platná nabítka</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PersonRelationsToAccount">
        <annotation>
            <documentation>persons related to account</documentation>
        </annotation>
        <sequence>
            <element minOccurs="0" maxOccurs="unbounded" name="personRelation" type="tns:PersonRelationToAccount">
                <annotation>
                    <documentation>person related to account</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PersonRelationToAccount">
        <annotation>
            <documentation>person related to account</documentation>
        </annotation>
        <sequence>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>cuid of person related to bank</documentation>
                </annotation>
            </element>
            <element name="relationType" type="tns:RelationToAccountType">
                <annotation>
                    <documentation>type of relation</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="RelationToAccountType">
        <annotation>
            <documentation>type of relation person to account</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CARD_HOLDER">
                <annotation>
                    <documentation>card holder</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISPONENT">
                <annotation>
                    <documentation>disponent</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OWNER">
                <annotation>
                    <documentation>owner</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ENTITLED">
                <annotation>
                    <documentation>entitled (opravnena osoba)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="AccountContacts">
        <annotation>
            <documentation>contacts for account</documentation>
        </annotation>
        <sequence>
            <element name="cuid" type="long" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>cuid of person related to account</documentation>
                </annotation>
            </element>
            <element name="mobilePhone" type="tns:MobilePhoneNumber" minOccurs="0">
                <annotation>
                    <documentation>mobile phone number in 9-digit format</documentation>
                </annotation>
            </element>
            <element name="email" type="string" minOccurs="0">
                <annotation>
                    <documentation>email</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="MobilePhoneNumber">
        <annotation>
            <documentation>mobile phone number in 9-digit format</documentation>
        </annotation>
        <restriction base="string">
            <pattern value="[0-9]*" />
            <length value="9" />
        </restriction>
    </simpleType>

    <complexType name="BankAccountShortTO">
        <annotation>
            <documentation>Short information about bank account.</documentation>
        </annotation>
        <sequence>
            <element name="idBankAccount" type="long">
                <annotation>
                    <documentation>primary key of bank account</documentation>
                </annotation>
            </element>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>bank account number</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>code of bank account currency</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="BankAccountExtTO">
        <annotation>
            <documentation>Short information about bank account.</documentation>
        </annotation>
        <sequence>
            <element name="idBankAccount" type="long">
                <annotation>
                    <documentation>id of bank account</documentation>
                </annotation>
            </element>
            <element name="accountNumber" type="long">
                <annotation>
                    <documentation>number of bank account</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>iso code of currency of bank account</documentation>
                </annotation>
            </element>
            <element name="accountName" type="string">
                <annotation>
                    <documentation>name of bank account</documentation>
                </annotation>
            </element>
            <element name="userAccountName" type="string">
                <annotation>
                    <documentation>user name of bank account</documentation>
                </annotation>
            </element>
            <element name="contractNumber" type="string">
                <annotation>
                    <documentation>number of contract</documentation>
                </annotation>
            </element>
            <element name="accountTypeCode" type="tns:BankAccountTypeCode">
                <annotation>
                    <documentation>type code of bank account</documentation>
                </annotation>
            </element>
            <element name="businessUsage" type="boolean">
                <annotation>
                    <documentation>true - the bank account is for business usage</documentation>
                </annotation>
            </element>
            <element name="activationDate" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>activation date of bank account</documentation>
                </annotation>
            </element>
            <element name="owner">
                <annotation>
                    <documentation>information about bank account owner</documentation>
                </annotation>
                <complexType>
                    <sequence>
                        <element name="cuid" type="long">
                            <annotation>
                                <documentation>cuid of bank account owner</documentation>
                            </annotation>
                        </element>
                        <element name="fullName" type="string">
                            <annotation>
                                <documentation>name of bank account owner</documentation>
                            </annotation>
                        </element>
                        <element name="legalSegment" type="tns:LegalSegment">
                            <annotation>
                                <documentation>právní segment vlastníka.</documentation>
                            </annotation>
                        </element>
                    </sequence>
                </complexType>
            </element>
            <element name="relation" type="tns:RelationToAccountType">
                <annotation>
                    <documentation>relation of person to bank account (OWNER / DISPONENT)</documentation>
                </annotation>
            </element>
            <element name="closeDate" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>termination date of bank account</documentation>
                </annotation>
            </element>
            <element name="status" type="tns:BankAccountStatus">
                <annotation>
                    <documentation>status of bank account</documentation>
                </annotation>
            </element>
            <element name="balanceAccounting" type="decimal">
                <annotation>
                    <documentation>accounting balance of bank account</documentation>
                </annotation>
            </element>
            <element name="balanceAvailable" type="decimal">
                <annotation>
                    <documentation>available balance of bank account</documentation>
                </annotation>
            </element>
            <element name="activatorId" type="string" minOccurs="0">
                <annotation>
                    <documentation>LDAP Id of branch assistant that activated the bank account</documentation>
                </annotation>
            </element>
            <element name="inDebt" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>true - bank account is in overdraft</documentation>
                </annotation>
            </element>
            <element name="blocking" type="tns:BankAccountBlocking" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>information about related blocking</documentation>
                </annotation>
            </element>
            <element name="actualDpdTolerance" type="int" minOccurs="0">
                <annotation>
                    <documentation>DPD - tolerance 100 - code D</documentation>
                </annotation>
            </element>
            <element name="actualDpd" type="int" minOccurs="0">
                <annotation>
                    <documentation>DPD - tolerance 0 - code C</documentation>
                </annotation>
            </element>
            <element name="iban" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        číslo účtu ve formátu IBAN
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="BankAccountBlocking">
        <annotation>
            <documentation>information about blocking</documentation>
        </annotation>
        <sequence>
            <element name="from" type="dateTime">
                <annotation>
                    <documentation>valid from</documentation>
                </annotation>
            </element>
            <element name="to" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>valid to</documentation>
                </annotation>
            </element>
            <element name="amont" type="decimal">
                <annotation>
                    <documentation>amount</documentation>
                </annotation>
            </element>
            <element name="current" type="boolean">
                <annotation>
                    <documentation>currently valid</documentation>
                </annotation>
            </element>
            <element name="historical" type="boolean">
                <annotation>
                    <documentation>historical - last year</documentation>
                </annotation>
            </element>
            <element name="blocking" type="tns:BlockingTypeTO">
                <annotation>
                    <documentation>historical - last year</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="BlockingTypeTO">
        <restriction base="string">
            <enumeration value="3P_ADJUCATION">
                <annotation>
                    <documentation>Soudní rozhodnutí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="3P_DISTRAINT">
                <annotation>
                    <documentation>Exekuce</documentation>
                </annotation>
            </enumeration>
            <enumeration value="3P_INSOLVENCE">
                <annotation>
                    <documentation>Insolvence</documentation>
                </annotation>
            </enumeration>
            <enumeration value="3P_STATE">
                <annotation>
                    <documentation>Státní orgán</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STD_OTHER">
                <annotation>
                    <documentation>Jiné</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="distributionChannel">
        <sequence>
            <element name="channelSaleLead" type="string">
                <annotation>
                    <documentation>Kanal vytvoreni zadosti</documentation>
                </annotation>
            </element>
            <element name="idAgency" type="string" minOccurs="0">
                <annotation>
                    <documentation>ID pobocky sjednani zadosti</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
</schema>
