<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:tns="http://airbank.cz/obs/ws/loan"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        targetNamespace="http://airbank.cz/obs/ws/loan"
        elementFormDefault="qualified">

    <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/ContractTO.xsd" />

    <simpleType name="SegmentType">
        <annotation>
            <documentation>typ segmentu</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="RETAIL" />
        </restriction>
    </simpleType>

    <simpleType name="Subsegment">
        <annotation>
            <documentation>Product's subsegment code</documentation>
        </annotation>
        <restriction base="string">
            <minLength value="1" />
            <maxLength value="50" />
        </restriction>
    </simpleType>

    <simpleType name="ClientType">
        <restriction base="string">
            <enumeration value="W">
                <annotation>
                    <documentation>klient typu walkin - nový klient</documentation>
                </annotation>
            </enumeration>
            <enumeration value="E">
                <annotation>
                    <documentation>existující klient</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ClientSubType">
        <restriction base="string">
            <enumeration value="STD">
                <annotation>
                    <documentation>neúčelový standardtní hotovostní úvěr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REF">
                <annotation>
                    <documentation>refinancování úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REF_R4">
                <annotation>
                    <documentation>Refinancování úvěru z realeasu 4. Pro dokončení starých žádostí.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CON">
                <annotation>
                    <documentation>konsolidace úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AB">
                <annotation>
                    <documentation>zaměstnanecký hotovostní úvěr - AirBank</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PPF">
                <annotation>
                    <documentation>zaměstnanecký hotovostní úvěr - skupina PPF</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ALL">
                <annotation>
                    <documentation>hledani skrz vse typy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPLIT">
                <annotation>
                    <documentation>split payment = rozesplatkovani</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ManagerType">
        <restriction base="string">
            <enumeration value="MNG_COL">
                <annotation>
                    <documentation>Manager vymahani</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTHER">
                <annotation>
                    <documentation>existující klient</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ConsolidationType">
        <annotation>
            <documentation>Typ konsolidace</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="EXTERNAL">
                <annotation>
                    <documentation>externí konsolidace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INTERNAL">
                <annotation>
                    <documentation>interní konsolidace</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="LoanRepaymentType">
        <annotation>
            <documentation>typ varianty splátkového kalendáře, kterou uživatel konfiguruje v IB</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="STANDARD">
                <annotation>
                    <documentation>standartní splátkový kalendář</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BONUS">
                <annotation>
                    <documentation>bonusová splátkový kalendář</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="PaymentScheduleType">
        <annotation>
            <documentation>
                Typ splátkového kalendáře, který se má spočítat
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="STANDARD">
                <annotation>
                    <documentation>vrátí vždy standardní SK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="STANDARD_BONUS">
                <annotation>
                    <documentation>vrátí standardní SK i bonusový SK pokud existuje</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ALL">
                <annotation>
                    <documentation>vrátí všechny dostupné SK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BONUS">
                <annotation>
                    <documentation>vrátí nejlepší bonusový SK. Pokud je na produktové variantě uvedeno, že neexistují bonusové schody, vrátí standardní SK.
                    </documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="PaymentScheduleTO">
        <annotation>
            <documentation>splátkový kalendář</documentation>
        </annotation>
        <sequence>
            <element name="orderNo" type="int">
                <annotation>
                    <documentation>
                        pořadové číslo splátkového kalendáře, 0 -
                        standardní, 1 – nejlepší bonusový SK (dle Prime Rate), 2 – o stupeň horší než 1, atd.
                    </documentation>
                </annotation>
            </element>
            <element name="loanInterest" type="decimal">
                <annotation>
                    <documentation>
                        úroková sazba - - formát jako číslo např. 12.12 se budou vracet jako 12.12%
                    </documentation>
                </annotation>
            </element>
            <element name="totalLoanCost" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        Celkové náklady na úvěr, pokud je zvolen na
                        vstupu typ requiredPaymentSchedule = All u
                        metody getLoanParams, tuto hodnotu OBS dostane
                        zpet v metode getPreContractDocument
                    </documentation>
                </annotation>
            </element>
            <element name="rpsn" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        roční procentní sazba nákladů, pokud je zvolen na
                        vstupu typ requiredPaymentSchedule = All u
                        metody getLoadParams, tuto hodnotu OBS dostane
                        zpet v metode getPreContractDocument
                    </documentation>
                </annotation>
            </element>
            <element name="minTotalLoanCost" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        Minimální celkové náklady na úvěr (úvěr + úroky
                        + poplatky atd.) , pokud je zvolen na vstupu typ
                        requiredPaymentSchedule = Standard /
                        Standard_Bonus u metody getLoanParam.Tuto
                        hodnotu OBS dostane zpet v metode
                        getPreContractDocument v pripade zvoleni
                        requiredPaymentSchedule = Standard
                        /Standard_Bonus v metode getLoanParams.
                    </documentation>
                </annotation>
            </element>
            <element name="maxTotalLoanCost" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        Maximální celkové náklady na úvěr (úvěr + úroky
                        + poplatky atd.) Pokud je zvolen na vstupu typ
                        requiredPaymentSchedule = Standard / Standard
                        _Bonus u metody getLoanParams,Tuto hodnotu OBS
                        dostane zpet v metode getPreContractDocument v
                        pripade zvoleni requiredPaymentSchedule =
                        Standard /Standard_Bonus v metode getLoanParams.
                    </documentation>
                </annotation>
            </element>
            <element name="minRpsn" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        min. roční sazba nákladů, pokud je zvolen na
                        vstupu typ requiredPaymentSchedule = Standard /
                        Standard _Bonus u metody getLoanParams,Tuto
                        hodnotu OBS dostane zpet v metode
                        getPreContractDocument v pripade zvoleni
                        requiredPaymentSchedule = Standard /Standard
                        _Plus v metode getLoanParams.

                    </documentation>
                </annotation>
            </element>
            <element name="maxRpsn" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        max. roční sazba nákladů, pokud je zvolen na
                        vstupu typ requiredPaymentSchedule = Standard /
                        Standard _Bonus u metody getLoanParams. Tuto
                        hodnotu OBS dostane zpet v metode
                        getPreContractDocument v pripade zvoleni
                        requiredPaymentSchedule = Standard /Standard
                        _Bonus v metode getLoanParams.
                    </documentation>
                </annotation>
            </element>
            <element name="repaymentPeriod" type="int">
                <annotation>
                    <documentation>
                        délka splácení (počet jednotek - měsíců)
                    </documentation>
                </annotation>
            </element>
            <element name="correctedRepaymentPeriod" type="boolean">
                <annotation>
                    <documentation>
                        true - délka splacení byla korigována
                    </documentation>
                </annotation>
            </element>
            <element name="bonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        bonus - úspora v korunách mezi standardní a
                        bonusosovu nabídkou splátkového kalendáře. V
                        případě standardního splátkového kalendáře se
                        bonus nevrací. Jinak bude vždy.
                    </documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDate" type="date">
                <annotation>
                    <documentation>datum poslední splátky - může se lišit pro různé splátkové kalendáře</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanPreContractTO">
        <sequence>
            <element name="numberInstalments" type="int">
                <annotation>
                    <documentation>počet splátek</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDate" type="date">
                <annotation>
                    <documentation>
                        termín poslední splátky
                    </documentation>
                </annotation>
            </element>
            <element name="totalAmount" type="decimal">
                <annotation>
                    <documentation>celková částka</documentation>
                </annotation>
            </element>
            <element name="loanInterest" type="float">
                <annotation>
                    <documentation>úroková sazba - formát jako číslo např. 12.12 se budou vracet jako 12.12%</documentation>
                </annotation>
            </element>
            <element name="minRpns" type="float">
                <annotation>
                    <documentation>minimální možné RPNS</documentation>
                </annotation>
            </element>
            <element name="maxRpns" type="float">
                <annotation>
                    <documentation>maximální možné RPNS</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanCharges">
        <sequence>
            <element name="associatedCosts" type="decimal">
                <annotation>
                    <documentation>související náklady</documentation>
                </annotation>
            </element>
            <element name="latePaymentCosts" type="decimal">
                <annotation>
                    <documentation>náklady opožděných plateb</documentation>
                </annotation>
            </element>
            <element name="providingCharges" type="decimal">
                <annotation>
                    <documentation>poplatek za poskytnutí</documentation>
                </annotation>
            </element>
            <element name="administrationCharges" type="decimal">
                <annotation>
                    <documentation>poplatek za správu úvěru</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanInterestRateEvaluationTO">
        <annotation>
            <documentation>Způsob vyhodnocení úrokové sazby úvěru.</documentation>
        </annotation>
        <sequence>
            <element name="inputInterestRateStandard" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>vstupní úroková sazba - standard</documentation>
                </annotation>
            </element>
            <element name="inputInterestRateBonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>vstupní úroková sazba - bonus</documentation>
                </annotation>
            </element>
            <element name="outputInterestRateStandard" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>výstupní úroková sazba - standard</documentation>
                </annotation>
            </element>
            <element name="outputInterestRateBonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>výstupní úroková sazba - bonus</documentation>
                </annotation>
            </element>
            <element name="ruleSet" type="tns:LoanInterestRateRuleSetTO" minOccurs="1" maxOccurs="unbounded">
                <annotation>
                    <documentation>sada pravidel</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanInterestRateRuleSetTO">
        <annotation>
            <documentation>Sada pravidel pro vyhodnocení úrokové sazby.</documentation>
        </annotation>
        <sequence>
            <element name="name" type="string" >
                <annotation>
                    <documentation>název sady pravidel</documentation>
                </annotation>
            </element>
            <element name="code" type="string" >
                <annotation>
                    <documentation>kód sady pravidel</documentation>
                </annotation>
            </element>
            <element name="interestRateDivergenceStandard" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>odchylka úrokové sazby - standard</documentation>
                </annotation>
            </element>
            <element name="interestRateDivergenceBonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>odchylka úrokové sazby - bonus</documentation>
                </annotation>
            </element>
            <element name="interestRateFixStandard" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>fix úrokové sazby - standard</documentation>
                </annotation>
            </element>
            <element name="interestRateFixBonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>fix úrokové sazby - bonus</documentation>
                </annotation>
            </element>
            <element name="usedMinInterestRateStandard" type="boolean">
                <annotation>
                    <documentation>příznak, zda byla použita minimální úroková sazba - standard</documentation>
                </annotation>
            </element>
            <element name="usedMinInterestRateBonus" type="boolean">
                <annotation>
                    <documentation>příznak, zda byla použita minimální úroková sazba - bonus</documentation>
                </annotation>
            </element>
            <element name="ignoredFixRateStandard" type="boolean">
                <annotation>
                    <documentation>příznak, zda bylo ignorováno nastavení fixní úrokové sazby - standard</documentation>
                </annotation>
            </element>
            <element name="ignoredFixRateBonus" type="boolean">
                <annotation>
                    <documentation>příznak, zda bylo ignorováno nastavení fixní úrokové sazby - bonus</documentation>
                </annotation>
            </element>
            <element name="rule" type="tns:LoanInterestRateRuleTO" maxOccurs="unbounded">
                <annotation>
                    <documentation>pravidla</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanInterestRateRuleTO">
        <annotation>
            <documentation>Pravidlo pro vyhodnocení úrokové sazby.</documentation>
        </annotation>
        <sequence>
            <element name="name" type="string" >
                <annotation>
                    <documentation>název pravidla</documentation>
                </annotation>
            </element>
            <element name="property" type="string" >
                <annotation>
                    <documentation>vlastnost pravidla (např. kampaň)</documentation>
                </annotation>
            </element>
            <element name="value" type="string" minOccurs="1" maxOccurs="unbounded">
                <annotation>
                    <documentation>hodnota pravidla</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>


    <simpleType name="LoanNumber">
        <annotation>
            <documentation>Číslo úvěru</documentation>
        </annotation>
        <restriction base="string">
            <minLength value="1" />
            <maxLength value="250" />
        </restriction>
    </simpleType>

    <simpleType name="LoanDetailDisplayType">
        <annotation>
            <documentation>a flag indicating whether to return short or full details</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="SHORT">
                <annotation>
                    <documentation>short detail of loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FULL">
                <annotation>
                    <documentation>full detail of loan</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="BasicLoanTO">
        <annotation>
            <documentation>structure for base information of loan (the same for cash loan and mortgage loan)</documentation>
        </annotation>
        <sequence>
            <element name="idLoan" type="long">
                <annotation>
                    <documentation>identification of loan</documentation>
                </annotation>
            </element>
            <element name="status" type="tns:LoanStatusType">
                <annotation>
                    <documentation>loan status</documentation>
                </annotation>
            </element>
            <element name="clientSubType" type="tns:ClientSubType">
                <annotation>
                    <documentation>client subtype - product type (STD/CON/MORTGAGE)</documentation>
                </annotation>
            </element>
            <element name="loanNumber" type="tns:LoanNumber">
                <annotation>
                    <documentation>number of loan</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>name of loan</documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>currency of loan</documentation>
                </annotation>
            </element>
            <element name="inDept" type="boolean">
                <annotation>
                    <documentation>true - there are debts on loan</documentation>
                </annotation>
            </element>
            <element name="loanAmount" type="decimal">
                <annotation>
                    <documentation>loan amount</documentation>
                </annotation>
            </element>
            <element name="nextInstalmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>amount of next instalment</documentation>
                </annotation>
            </element>
            <element name="nextInstalmentDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>date of next instalment (by standard payment schedule for cash loan)</documentation>
                </annotation>
            </element>
            <element name="paidPrincipal" type="decimal">
                <annotation>
                    <documentation>paid principal</documentation>
                </annotation>
            </element>
            <element name="paidInterest" type="decimal">
                <annotation>
                    <documentation>paid interest</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal">
                <annotation>
                    <documentation>unpaid principal</documentation>
                </annotation>
            </element>
            <element name="idLoanAccount" type="long">
                <annotation>
                    <documentation>id of the OBS client account used for repaying the loan</documentation>
                </annotation>
            </element>
            <element name="clientDisplayableRepaymentAccount" type="boolean">
                <annotation>
                    <documentation>display repayment account to client</documentation>
                </annotation>
            </element>
            <element name="noOfPaidRegInstallments" type="int">
                <annotation>
                    <documentation>Počet plně uhrazených běžných splátek úvěru, pro revolvingové úvěry bude nulový.</documentation>
                </annotation>
            </element>
            <element name="paymentHolidayRequestAllowed" type="boolean">
                <annotation>
                    <documentation>Priznak jestli je povoleno zadat o splatkove prazdniny</documentation>
                </annotation>
            </element>
            <element name="expectedRepaymentDate" type="date">
                <annotation>
                    <documentation>Datum poslední splátky podle aktuálně platného splátkového kalendáře, nikoli jen standardního kalendáře.</documentation>
                </annotation>
            </element>
            <element name="effectiveInterestRate" type="decimal">
                <annotation>
                    <documentation>Effective interest rate</documentation>
                </annotation>
            </element>
            <element name="paymentHolidayActivated" type="boolean">
                <annotation>
                    <documentation>Příznak, zda jsou na úvěru aktuálně zapnuté splátkové prázdniny</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanTO">
        <annotation>
            <documentation>structure for detail of cash loan</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:BasicLoanTO">
                <sequence>
                    <element name="instalmentAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>amount of regular instalment</documentation>
                        </annotation>
                    </element>
                    <element name="rpsn" type="float" minOccurs="0">
                        <annotation>
                            <documentation>RPSN by standard payment schedule</documentation>
                        </annotation>
                    </element>
                    <element name="interestRate" type="decimal">
                        <annotation>
                            <documentation>interest rate (number in percent) - for example 12.12 for interest rate 12.12%</documentation>
                        </annotation>
                    </element>
                    <element name="totalLoanCost" type="decimal">
                        <annotation>
                            <documentation>total cost of loan by standard payment schedule</documentation>
                        </annotation>
                    </element>
                    <element name="lastInstalmentDate" type="date">
                        <annotation>
                            <documentation>date of last instalment by standard payment schedule (date of repayment)</documentation>
                        </annotation>
                    </element>
                    <element name="firstUtilizationDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>date of first utilization</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriod" type="int">
                        <annotation>
                            <documentation>repayment period (number of months) by standard payment schedule</documentation>
                        </annotation>
                    </element>
                    <element name="numberOfFutureInstalments" type="int">
                        <annotation>
                            <documentation>number of unpaid monthly installments by standard payment schedule</documentation>
                        </annotation>
                    </element>
                    <element name="instalmentDay" type="int">
                        <annotation>
                            <documentation>day of the month of instalment</documentation>
                        </annotation>
                    </element>
                    <element name="bonusDetail" type="tns:LoanBonusTO" minOccurs="0">
                        <annotation>
                            <documentation>information about the current payment schedule - if not returned the client have only standard payment schedule.
                                order number of bonus payment schedule: 1 - the best (PrimeRate), 2 - second best, etc
                            </documentation>
                        </annotation>
                    </element>
                    <element name="bonusExisted" type="boolean">
                        <annotation>
                            <documentation>is or was a bonus for loan</documentation>
                        </annotation>
                    </element>
                    <element name="isConsolidable" type="boolean" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="InterestRateTO">
        <simpleContent>
            <extension base="tns:positiveDecimal">
                <attribute name="type" type="tns:rateTypeTO" />
            </extension>
        </simpleContent>
    </complexType>

    <complexType name="ExtendedLoanTO">
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>System identification number</documentation>
                </annotation>
            </element>
            <element name="loanNumber" type="string" />
            <element name="status">
                <annotation>
                    <documentation>Status for RISK department - copy of OverdraftExtendedDetailTO.status</documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="NEW" />
                        <enumeration value="CANCELLED_BY_CLIENT" />
                        <enumeration value="CANCELLED_BY_BANK" />
                        <enumeration value="REJECTED" />
                        <enumeration value="PENDING" />
                        <enumeration value="ACTIVE" />
                        <enumeration value="REPAID_STD" />
                        <enumeration value="REPAID_PREMATURELY" />
                        <enumeration value="REPAID_CONSOLIDATION" />
                        <enumeration value="WITHDRAWN_UNPAID" />
                        <enumeration value="WITHDRAWN_PAID" />
                        <enumeration value="CALLED_DUE_UNPAID" />
                        <enumeration value="CALLED_DUE_PAID" />
                        <enumeration value="WRITE_OFF_UNPAID" />
                        <enumeration value="WRITE_OFF_PAID" />
                        <enumeration value="TO_WRITTEN_OFF_UNPAID" />
                        <enumeration value="TO_WRITTEN_OFF_PAID" />
                    </restriction>
                </simpleType>
            </element>
            <element name="productType">
                <simpleType>
                    <restriction base="string">
                        <enumeration value="STD">
                            <annotation>
                                <documentation>neúčelový standardtní hotovostní úvěr</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="REF">
                            <annotation>
                                <documentation>refinancování úvěru</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="CON">
                            <annotation>
                                <documentation>konsolidace úvěru</documentation>
                            </annotation>
                        </enumeration>
                        <enumeration value="SPLIT">
                            <annotation>
                                <documentation>rozlozeni platby</documentation>
                            </annotation>
                        </enumeration>
                    </restriction>
                </simpleType>
            </element>
            <element name="productVariantCode" type="string" />
            <element name="applicationId" type="long" />
            <element name="approvalDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>It is filled in only for approved loans.</documentation>
                </annotation>
            </element>
            <element name="utilizationDate" type="date" minOccurs="0" />
            <element name="closeDate" type="date" minOccurs="0" />
            <element name="lastInstlChangeDate" type="date" minOccurs="0" />
            <element name="approvedLoanAmount" type="decimal" />
            <element name="approvedInstlAmount" type="decimal" />
            <element name="approvedNoOfInstallments" type="int" />
            <element name="installmentAmount" type="decimal" />
            <element name="amountToRepay" type="decimal" minOccurs="0" >
                <annotation>
                    <documentation>částka k předčasnému splacení</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>unpaid principal</documentation>
                </annotation>
            </element>
            <element name="noOfFutureInstallments" type="int" minOccurs="0" />
            <element name="actualDPD" type="int" minOccurs="0" />
            <element name="actualDPDWithTolerance" type="int" minOccurs="0" />
            <element name="actualInternalDPD" type="int" minOccurs="0" />
            <element name="actualInternalDPDWithTolerance" type="int" minOccurs="0" />
            <element name="totalDebt" type="decimal" minOccurs="0" />
            <element name="principalOverdue" type="decimal" minOccurs="0" />
            <element name="totalAmountRef" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>total amount required to be paid the original obligation</documentation>
                </annotation>
            </element>
            <element name="totalAmountRefApplicant" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>total amount paid by the applicant</documentation>
                </annotation>
            </element>
            <element name="upsell" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>upsell</documentation>
                </annotation>
            </element>
            <element name="paymentScheduleOrderNo" type="string" minOccurs="0">
                <annotation>
                    <documentation>Aktuální bonusová kategorie pro úvěr</documentation>
                </annotation>
            </element>
            <element name="firstInstallmentDate" type="date">
                <annotation>
                    <documentation>Datum první splátky</documentation>
                </annotation>
            </element>
            <element name="repaymentPeriod" type="positiveInteger">
                <annotation>
                    <documentation>Celkový počet splátek</documentation>
                </annotation>
            </element>
            <element name="riskGrade" type="string">
                <annotation>
                    <documentation>RiskGrade s kterým byl úvěr založen</documentation>
                </annotation>
            </element>
            <element name="interestRate" type="tns:InterestRateTO" maxOccurs="2">
                <annotation>
                    <documentation>Výše sazby úvěru</documentation>
                </annotation>
            </element>
            <element name="isConsolidable" type="boolean" />
            <element name="noOfPaidRegInstallments" type="int">
                <annotation>
                    <documentation>Počet plně uhrazených běžných splátek úvěru, pro revolvingové úvěry bude nulový.</documentation>
                </annotation>
            </element>
            <element name="lastPaymentHolidayStart" type="date">
                <annotation>
                    <documentation>Počátek platnosti posledních splátkových prázdnin</documentation>
                </annotation>
            </element>
            <element name="expectedRepaymentDate" type="date">
                <annotation>
                    <documentation>Datum poslední splátky podle aktuálně platného splátkového kalendáře, nikoli jen standardního kalendáře.</documentation>
                </annotation>
            </element>
            <element name="insurance" type="tns:InsuranceTO" minOccurs="0">
                <annotation>
                    <documentation>Pojištění schopnosti splácet úvěr.</documentation>
                </annotation>
            </element>
            <element name="instalmentDay" type="int" minOccurs="0">
                <annotation>
                    <documentation>Den splácení.</documentation>
                </annotation>
            </element>
            <element name="residualAmount" type="decimal" minOccurs="0" >
                <annotation>
                    <documentation>Částka pro splacení ve standardni dobe</documentation>
                </annotation>
            </element>
            <element name="noOfFutureInstallmentsBonus" type="int" minOccurs="0" />
        </sequence>
    </complexType>

    <simpleType name="LoanStatusType">
        <restriction base="string">
            <enumeration value="ACTIVE">
                <annotation>
                    <documentation>aktivní</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENDING_DRAWING">
                <annotation>
                    <documentation>Čeká na načerpání</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WITHDRAWN">
                <annotation>
                    <documentation>Odstoupený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WITHDRAWN_STATUE_LIMITATIONS">
                <annotation>
                    <documentation>Odstoupený v zákonné lhůtě</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CALLING_LOAN_DUE">
                <annotation>
                    <documentation>Zesplatněný</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAID">
                <annotation>
                    <documentation>Splacený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAID_PREMATURELY">
                <annotation>
                    <documentation>Splacený předčasně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PAID_CONSOLIDATION">
                <annotation>
                    <documentation>Splacený konsolidací</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEMO">
                <annotation>
                    <documentation>Demo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCELLED">
                <annotation>
                    <documentation>Stornovaný</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WITHDRAWN_BY_BANK">
                <annotation>
                    <documentation>Odstoupený bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CALLING_LOAN_DUE_PAID">
                <annotation>
                    <documentation>Zesplatněný/Doplacený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WITHDRAWN_PAID">
                <annotation>
                    <documentation>Odstoupený/Doplacený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WITHDRAWN_STATUE_LIMITATIONS_PAID">
                <annotation>
                    <documentation>Odstoupený v zákonné lhůtě/Doplacený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WRITE_OFF_UNPAID">
                <annotation>
                    <documentation>Odepsaný</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TO_WRITTEN_OFF_UNPAID">
                <annotation>
                    <documentation>K odpisu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WRITE_OFF_PAID">
                <annotation>
                    <documentation>Odepsaný/Doplacený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TO_WRITTEN_OFF_PAID">
                <annotation>
                    <documentation>K odpisu/Doplacený</documentation>
                </annotation>
            </enumeration>
            <enumeration value="UNUTILIZED">
                <annotation>
                    <documentation>Nenacerpano (virtualni stav pro filtr)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="InstalmentTypeEvent">
        <annotation>
            <documentation>typ splátky</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="STANDARD">
                <annotation>
                    <documentation>Standardní splátka úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRA">
                <annotation>
                    <documentation>Mimořádná splátka úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="LOSS_BONUS">
                <annotation>
                    <documentation>Ztráta bonusového stupně</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REDEMPTION">
                <annotation>
                    <documentation>Zesplatnění úvěru nebo hypotéky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSURANCE">
                <annotation>
                    <documentation>Pojištění</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPLIT_PAYMENT">
                <annotation>
                    <documentation>Poplatek za sjednání produktu o rozložení platby</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="InsuranceTO">
        <sequence>
            <element name="insuranceNumber" type="string">
                <annotation>
                    <documentation>číslo pojištění</documentation>
                </annotation>
            </element>
            <element name="premium" type="decimal">
                <annotation>
                    <documentation>aktuální výše pojistného dle splátkového kalendáře</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PaymentScheduleEventTO">
        <annotation>
            <documentation>předepsané datum</documentation>
        </annotation>
        <sequence>
            <element name="idPaymentScheduleEvent" type="string" />
            <element name="instalmentType" type="tns:InstalmentTypeEvent" />
            <choice>
                <annotation>
                    <documentation>
                        dle insalment type se vraci instalmentTO(standard, extra) nebo
                        dateOfLossBonus(LOSS_BONUS)
                    </documentation>
                </annotation>
                <element name="dateOfLossBonus" type="date">
                    <annotation>
                        <documentation>datum stráty bonusu - pro typ splátky LOSS_BONUS</documentation>
                    </annotation>
                </element>
                <sequence minOccurs="0">
                    <element name="requiredDate" type="date">
                        <annotation>
                            <documentation>
                                předepsané datum splátky - vyplněno pouze u typu splátky STANDARD
                            </documentation>
                        </annotation>
                    </element>
                    <element name="requiredAmount" type="decimal">
                        <annotation>
                            <documentation>
                                předepsaná výše splátky - vyplněno pouze u typu splátky STANDARD
                            </documentation>
                        </annotation>
                    </element>
                    <element name="realizedDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>
                                datum uhrazení splátky - vyplněno pouze u typu splátky STANDARD, EXTRA

                            </documentation>
                        </annotation>
                    </element>
                    <element name="realizedAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>
                                uhrazená výše splátky - vyplněno pouze u typu splátky STANDARD, EXTRA
                            </documentation>
                        </annotation>
                    </element>
                    <element name="clientRequiredToPay" type="boolean">
                        <annotation>
                            <documentation>
                                klient musi nebo musel splatit dle aktualniho splatkoveho kalendare
                            </documentation>
                        </annotation>
                    </element>
                    <element name="partialRealization" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>
                                určuje, zda splátka obsahuje alespoň jednu částečnou splátku.
                                (false = ještě nebylo nic uhrazeno nebo částka byla uhrazena v jedné splátce,
                                true = splátka obsahuje alespoň jednu částečnou splátku).
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </choice>
        </sequence>
    </complexType>

    <complexType name="PartialRealizationTrnTO">
        <sequence>
            <element name="type" type="tns:PartialRealizationType">
                <annotation>
                    <documentation>typ platby</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>výše částečné splatky</documentation>
                </annotation>
            </element>
            <element name="realizationDate" type="date" />
        </sequence>
    </complexType>

    <simpleType name="PartialRealizationType">
        <restriction base="string">
            <enumeration value="OVERDUE">
                <annotation>
                    <documentation>po splatnosti - i castecna (platba po splatnosti splátky)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PARTIAL">
                <annotation>
                    <documentation>castecna platba pred splatnosti</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="ExtraInstalmentTO">
        <sequence>
            <element name="unpaidPrincipal" type="decimal">
                <annotation>
                    <documentation>nesplacená jistina</documentation>
                </annotation>
            </element>
            <element name="loanAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>výše úvěru</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal"
                     minOccurs="0">
                <annotation>
                    <documentation>vyse splatky</documentation>
                </annotation>
            </element>
            <element name="nextInstalmentDate" type="date"
                     minOccurs="0">
                <annotation>
                    <documentation>datum přísští splátky</documentation>
                </annotation>
            </element>
            <element name="numberOfInstalments" type="int"
                     minOccurs="0">
                <annotation>
                    <documentation>pocet splatek celkove</documentation>
                </annotation>
            </element>
            <element name="numberOfFutureInstalments" type="int"
                     minOccurs="0">
                <annotation>
                    <documentation>
                        pocet budoucich splatek
                    </documentation>
                </annotation>
            </element>
            <element name="loanInterest" type="float" minOccurs="0">
                <annotation>
                    <documentation>
                        úroková sazba - - formát jako číslo např. 12.12
                        se budou vracet jako 12.12%
                    </documentation>
                </annotation>
            </element>
            <element name="rpsn" type="float" minOccurs="0">
                <annotation>
                    <documentation>
                        roční procentní sazba nákladů
                    </documentation>
                </annotation>
            </element>
            <element name="totalLoanCost" type="decimal">
                <annotation>
                    <documentation>
                        Celkové náklady na úvěr
                    </documentation>
                </annotation>
            </element>
            <element name="type" type="tns:ExtraInstalmentTOType">
                <annotation>
                    <documentation>typ záznamu</documentation>
                </annotation>
            </element>
            <element name="orderNo" type="int">
                <annotation>
                    <documentation>
                        0 - standard, 1-8 bonus
                    </documentation>
                </annotation>
            </element>
            <element name="bonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        bonus - úspora v korunách mezi standardním a
                        bonusovým splátkovým kalendářem (pokud se tento
                        zaznam tyka bonusoveho kalendare) (celkové
                        náklady std - bonus).
                    </documentation>
                </annotation>
            </element>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>počátek platnosti záznamu</documentation>
                </annotation>
            </element>
            <element name="validTo" type="dateTime">
                <annotation>
                    <documentation>konec platnosti záznamu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="ExtraInstalmentTOType">
        <restriction base="string">
            <enumeration value="BEFORE_EXTRA_INSTALMENT">
                <annotation>
                    <documentation>stav pred mimoradnou splatkou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AFTER_EXTRA_INSTALMENT">
                <annotation>
                    <documentation>stav po mimoradne splatce</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="IdPaymentScheduleEventType">
        <sequence>
            <element name="idPaymentScheduleEvent" type="string">
                <annotation>
                    <documentation>identifikátor události ve splátkové kalendáři</documentation>
                </annotation>
            </element>
            <element name="overdue" type="boolean">
                <annotation>
                    <documentation>splátka po splatnosti</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanBonusTO">
        <sequence>
            <element name="loanInterestBonus" type="float">
                <annotation>
                    <documentation>
                        úroková sazba - - formát jako číslo např. 12.12 se budou vracet jako 12.12%
                    </documentation>
                </annotation>
            </element>
            <element name="rpsnBonus" type="float">
                <annotation>
                    <documentation>
                        roční procentní sazba nákladů - bonusový SK
                    </documentation>
                </annotation>
            </element>
            <element name="repaymentPeriodBonus" type="int">
                <annotation>
                    <documentation>
                        délka splacení - (počet měsíců) - bonusový SK
                    </documentation>
                </annotation>
            </element>
            <element name="totalLoanCostBonus" type="decimal">
                <annotation>
                    <documentation>
                        celkové náklady - bonusový SK
                    </documentation>
                </annotation>
            </element>
            <element name="numberOfInstalmentBonus" type="int">
                <annotation>
                    <documentation>
                        počet měsíčních nesplacených splátek -
                        bonusového SK
                    </documentation>
                </annotation>
            </element>
            <element name="bonus" type="decimal">
                <annotation>
                    <documentation>
                        rozdíl mezi celkovými naklady na úvěr dle
                        standardního a bonusového splátkového kalendáře
                    </documentation>
                </annotation>
            </element>
            <element name="orderNo" type="int" />
            <element name="validFrom" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>počátek platnosti záznamu</documentation>
                </annotation>
            </element>
            <element name="validTo" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>konec platnosti záznamu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PaymentScheduleInfoTO">
        <sequence>
            <element name="loanInterest" type="float">
                <annotation>
                    <documentation>
                        úroková sazba SK - formát jako číslo např. 12.12 se budou vracet jako 12.12%
                    </documentation>
                </annotation>
            </element>
            <element name="diffLoanInterest" type="float" minOccurs="0">
                <annotation>
                    <documentation>rozdíl v úroková sazbě standardního a daného bonusového SK - formát jako číslo např. 12.12 se budou vracet jako 12.12% - pro
                        standardní SK není vyplněno
                    </documentation>
                </annotation>
            </element>
            <element name="repaymentPeriod" type="int">
                <annotation>
                    <documentation>
                        délka splacení - (počet měsíců) - SK
                    </documentation>
                </annotation>
            </element>
            <element name="diffRepaymentPeriod" type="int" minOccurs="0">
                <annotation>
                    <documentation>rozdíl v délce splacení mezi standardním a daným bonusovým SK - (počet měsíců SK)</documentation>
                </annotation>
            </element>
            <element name="totalLoanCost" type="decimal">
                <annotation>
                    <documentation>
                        celkové náklady daného SK
                    </documentation>
                </annotation>
            </element>
            <element name="bonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>
                        rozdíl mezi celkovými naklady na úvěr dle
                        standardního a bonusového splátkového kalendáře
                        (u standardniho SK není vyplněno)
                    </documentation>
                </annotation>
            </element>
            <element name="orderNo" type="int">
                <annotation>
                    <documentation>
                        pořadové číslo SK (0 - standard, 1 - nejlepší ,
                        atd.)
                    </documentation>
                </annotation>
            </element>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>platnost od SK</documentation>
                </annotation>
            </element>
            <element name="validTo" type="dateTime">
                <annotation>
                    <documentation>platnost do SK</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ProductVariantTO">
        <sequence>
            <element name="idProductVariant" type="long">
                <annotation>
                    <documentation>ID produktové varianty</documentation>
                </annotation>
            </element>
            <element name="code" type="string">
                <annotation>
                    <documentation>Kód produktové varianty</documentation>
                </annotation>
            </element>
            <element name="minRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Minimální délka splácení (počet jednotek - měsíců)</documentation>
                </annotation>
            </element>
            <element name="maxRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Maximální délka splácení (počet jednotek - měsíců)</documentation>
                </annotation>
            </element>
            <element name="minAmount" type="decimal">
                <annotation>
                    <documentation>Minimální výše splátky</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ChangeLimitParamsTO">
        <sequence>
            <element name="minRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Minimální délka splácení (počet jednotek - měsíců)</documentation>
                </annotation>
            </element>
            <element name="maxRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Maximální délka splácení (počet jednotek - měsíců)</documentation>
                </annotation>
            </element>
            <element name="minAmount" type="decimal">
                <annotation>
                    <documentation>Minimální výše splátky</documentation>
                </annotation>
            </element>
            <element name="maxAmount" type="decimal">
                <annotation>
                    <documentation>Maximální výše splátky</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoanLimitParamsCollectionItem">
        <sequence>
            <element name="minCredit" type="decimal">
                <annotation>
                    <documentation>min. výše úvěru</documentation>
                </annotation>
            </element>
            <element name="maxCredit" type="decimal">
                <annotation>
                    <documentation>max. výše. úvěru</documentation>
                </annotation>
            </element>
            <element name="minRepaymentPeriod" type="int">
                <annotation>
                    <documentation>minimální délka splácení</documentation>
                </annotation>
            </element>
            <element name="maxRepaymentPeriod" type="int">
                <annotation>
                    <documentation>max. délka splácení</documentation>
                </annotation>
            </element>
            <element name="minimalInstalment" type="decimal">
                <annotation>
                    <documentation>minimální výše splátky</documentation>
                </annotation>
            </element>
            <element name="clientSubType" type="tns:ClientSubType">
                <annotation>
                    <documentation>podtyp klienta (jemnejsi cleneni dle clientTypu) = podtyp produktové varianty
                        - zatím se vždy vrací STD viz FS
                    </documentation>
                </annotation>
            </element>
            <element name="campaignFound" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>zda byla nalezena kampaň dle kódu na vstupu. Pokud nebyla nelezena dle kodu tak false. Pokud nebyla zadan marketin. kod na
                        vstupu, potom atribut je null.
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="UserPropertiesCommonTO">
        <sequence>
            <element name="segment" type="tns:SegmentType">
                <annotation>
                    <documentation>Segment (RETAIL)</documentation>
                </annotation>
            </element>
            <element name="clientType" type="tns:ClientType">
                <annotation>
                    <documentation>Client type (W/E)</documentation>
                </annotation>
            </element>
            <element name="clientSubType" type="tns:ClientSubType">
                <annotation>
                    <documentation>Client subtype (STD/AB/PPF/REF/CON/ALL)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DebtCommonTO">
        <sequence>
            <element name="ignoreInterestRateParametrization" type="boolean">
                <annotation>
                    <documentation>Flag pro urceni pouziti konfigurace z IRMy</documentation>
                </annotation>
            </element>
            <element name="segment" type="tns:SegmentType">
                <annotation>
                    <documentation>Segment (RETAIL)</documentation>
                </annotation>
            </element>
            <element name="subsegment" type="tns:Subsegment" minOccurs="0">
                <annotation>
                    <documentation>Subsegment code</documentation>
                </annotation>
            </element>
            <element name="clientType" type="tns:ClientType">
                <annotation>
                    <documentation>Client type (W/E)</documentation>
                </annotation>
            </element>
            <element name="clientSubType" type="tns:ClientSubType" minOccurs="0">
                <annotation>
                    <documentation>Client subtype (HYREF/CON)</documentation>
                </annotation>
            </element>
            <element name="customerCareCode" type="string" minOccurs="0">
                <annotation>
                    <documentation>Marketing campaign code</documentation>
                </annotation>
            </element>
            <element name="finalCheck" type="boolean">
                <annotation>
                    <documentation>FALSE : Přidání závazku/ TRUE : finální kontrola po zadání všech závazků</documentation>
                </annotation>
            </element>
            <element name="upsellCons" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Consolidation upsell.</documentation>
                </annotation>
            </element>
            <element name="ppi" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Příznak,zda je/bude sjednáno pojištění schopnosti splácet</documentation>
                </annotation>
            </element>
            <element name="employeeType" type="string" minOccurs="0">
                <annotation>
                    <documentation>typ zaměstnance</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long" minOccurs="0">
                <annotation>
                    <documentation>CIF identifikace klienta</documentation>
                </annotation>
            </element>
            <element name="interestRateStandard" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Standardní úroková sazba požadovaného úvěru pro klienta</documentation>
                </annotation>
            </element>
            <element name="interestRateBonus" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Bonusová úroková sazba požadovaného úvěru pro klienta</documentation>
                </annotation>
            </element>
            <element name="fixedInterestRate" type="boolean">
                <annotation>
                    <documentation>Informace, zda byla sazba změněna underwriterem</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DebtRestrictionTO">
        <sequence>
            <element name="repaymentPeriod" type="int" minOccurs="0">
                <annotation>
                    <documentation>Quantity of instalments set by client at WWW or after the first calculation</documentation>
                </annotation>
            </element>
            <element name="maxInstalment" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Maximální výše splátky pro refinancování/konsolidaci uložená na LoanBinu</documentation>
                </annotation>
            </element>
            <element name="maxAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Maximal loan amount</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Instalment amount</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CashLoanTO">
        <complexContent>
            <extension base="tns:DebtCommonObligationTO">
                <sequence>
                    <element name="totalAmount" type="decimal">
                        <annotation>
                            <documentation>Can request up to [total amount]</documentation>
                        </annotation>
                    </element>
                    <element name="firstUtilizationDate" type="date">
                        <annotation>
                            <documentation>datum čerpání</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriod" type="int">
                        <annotation>
                            <documentation>
                                délka splácení (počet jednotek - měsíců)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="instalmentAmount" type="decimal">
                        <annotation>
                            <documentation>vyse splatky</documentation>
                        </annotation>
                    </element>
                    <element name="instalmentDay" type="int" minOccurs="0">
                        <annotation>
                            <documentation>Den splácení : vyplněno v případě procesu "změna dne splácení"</documentation>
                        </annotation>
                    </element>
                    <element name="currency" type="string" minOccurs="0" />
                    <element name="nrResidualInstalment" type="int" minOccurs="0">
                        <annotation>
                            <documentation>Počet zbývajících splátek</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CreditCardTO">
        <complexContent>
            <extension base="tns:DebtCommonObligationTO">
                <sequence>
                    <element name="totalAmount" type="decimal">
                        <annotation>
                            <documentation>Can request up to [total amount]</documentation>
                        </annotation>
                    </element>
                    <element name="chargeAmount" type="decimal">
                        <annotation>
                            <documentation>Monthly charge, if there is no charge in fact 0 shall be placed</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="InternalCashLoanTO">
        <complexContent>
            <extension base="tns:DebtCommonObligationTO">
                <sequence>
                    <element name="loanNumber" type="string" />
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="OverDraftTO">
        <complexContent>
            <extension base="tns:DebtCommonObligationTO">
                <sequence>
                    <element name="totalAmount" type="decimal">
                        <annotation>
                            <documentation>Can request up to [total amount]</documentation>
                        </annotation>
                    </element>
                    <element name="chargeAmount" type="decimal">
                        <annotation>
                            <documentation>Monthly charge, if there is no charge in fact 0 shall be placed</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DebtCommonObligationTO" abstract="true">
        <sequence>
            <element name="obligationNumber" type="int">
                <annotation>
                    <documentation>unique obligation's identifier</documentation>
                </annotation>
            </element>
            <element name="financialInstitution" type="string">
                <annotation>
                    <documentation>Financial institution's code which is the loan-provided</documentation>
                </annotation>
            </element>

            <element name="residualAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Actual amount to be paid</documentation>
                </annotation>
            </element>
            <element name="addBuffer" type="boolean">
                <annotation>
                    <documentation>Financial institution's code which is the loan-provided</documentation>
                </annotation>
            </element>
            <element name="addBufferDeferredInstalment" type="boolean">
                <annotation>
                    <documentation>Financial institution's code G,loan has some diferred instalments</documentation>
                </annotation>
            </element>

        </sequence>
    </complexType>

    <complexType name="InputValidationErrorTO">
        <sequence>
        <element name="obligationNumber" type="int" minOccurs="0">
                <annotation>
            <documentation>unique obligation's identifier</documentation>
                </annotation>
            </element>
            <element name="rejectReasonCode" type="com:ReasonRejectionType">
                <annotation>
                    <documentation>reject reason - one of the predefined list</documentation>
                </annotation>
            </element>
            <element name="repayAmountSum" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>summ of total obligations amount / shall be displayed in IB if product variant is over-bounded</documentation>
                </annotation>
            </element>
            <element name="repayAmountMaxLimit" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>maximum loan amount according to the product variant / loanRestrictions.maxAmount</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InputValidationErrorsTO">
        <sequence>
            <element name="inputValidationError" type="tns:InputValidationErrorTO" maxOccurs="unbounded" />
        </sequence>
    </complexType>

    <complexType name="ObligationCalculationResultTO">
        <sequence>
        <element name="obligationNumber" type="int">
                <annotation>
            <documentation>unique obligation's identifier</documentation>
                </annotation>
            </element>
            <element name="rpsn" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>roční procentní sazba nákladů - standadrní SK</documentation>
                </annotation>
            </element>
            <element name="repayAmount" type="decimal">
                <annotation>
                    <documentation>zde může být nejen nesplacená jistina, ale i naběhlé úroky (ještě bude vyjasněno businessem).</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Výše měsíční splátky (bez poplatku), povinné pro produkty kreditní karta a kontokorent</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="rateTypeTO">
        <restriction base="string">
            <enumeration value="STDRATE">
                <annotation>
                    <documentation>Standard interest rate</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PRIMERATE">
                <annotation>
                    <documentation>Bonus interest rate</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FIXRATE">
                <annotation>
                    <documentation>Mortgage loan fix intereat rate</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FLOATRATE">
                <annotation>
                    <documentation>Mortgage loan float interest rate</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FLOATRATECAP">
                <annotation>
                    <documentation>Mortgage loan float interest rate cap</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="fixationPeriodTO">
        <annotation>
            <documentation>Fixation period length in years (3 / 5).</documentation>
        </annotation>
        <restriction base="positiveInteger">
            <enumeration value="3" />
            <enumeration value="5" />
        </restriction>
    </simpleType>

    <simpleType name="positiveDecimal">
        <restriction base="decimal">
            <minExclusive value="0" />
        </restriction>
    </simpleType>

    <complexType name="ObligationCalculationResultsTO">
        <sequence>
            <element name="obligationCalculationResult" type="tns:ObligationCalculationResultTO" maxOccurs="unbounded" />
        </sequence>
    </complexType>

    <complexType name="DebtConsolidationOfferTO">
        <sequence>
            <element name="productType" type="tns:ClientSubType">
                <annotation>
                    <documentation>resulting AB product type / consolidation or refinance</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal">
                <annotation>
                    <documentation>vyse splatky</documentation>
                </annotation>
            </element>
            <element name="chargeAmount" type="decimal">
                <annotation>
                    <documentation>Měsíční poplatek</documentation>
                </annotation>
            </element>
            <element name="repaymentPeriod" type="int">
                <annotation>
                    <documentation>délka splácení (počet jednotek - měsíců)</documentation>
                </annotation>
            </element>
            <element name="repaymentPeriodBonus" type="int">
                <annotation>
                    <documentation>délka splácení (počet jednotek - měsíců)</documentation>
                </annotation>
            </element>
            <element name="totalLoanCost" type="decimal">
                <annotation>
                    <documentation>celkové náklady - standardní SK</documentation>
                </annotation>
            </element>
            <element name="totalLoanCostBonus" type="decimal">
                <annotation>
                    <documentation>celkové náklady - bonus</documentation>
                </annotation>
            </element>
            <element name="rpsn" type="decimal">
                <annotation>
                    <documentation>roční procentní sazba nákladů - standadrní SK</documentation>
                </annotation>
            </element>
            <element name="rpsnBonus" type="decimal">
                <annotation>
                    <documentation>roční procentní sazba nákladů - bonusovy SK</documentation>
                </annotation>
            </element>
            <element name="loanAmount" type="decimal">
                <annotation>
                    <documentation>výše úvěru</documentation>
                </annotation>
            </element>
            <element name="loanInterest" type="decimal">
                <annotation>
                    <documentation>úroková sazba - - formát jako číslo např. 12.12 se budou vracet jako 12.12%</documentation>
                </annotation>
            </element>
            <element name="loanInterestBonus" type="decimal">
                <annotation>
                    <documentation>úroková sazba - - formát jako číslo např. 12.12 se budou vracet jako 12.12%</documentation>
                </annotation>
            </element>
            <element name="minRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Minimální počet splátek podle standardního splátkového kalendáře</documentation>
                </annotation>
            </element>
            <element name="idProductVariant" type="int" minOccurs="0">
                <annotation>
                    <documentation>ID produktovej varianty, podľa ktorej bola vytvorená ponuka.</documentation>
                </annotation>
            </element>
            <element name="productVariantCode" type="string" minOccurs="0">
                <annotation>
                    <documentation>Kód produktovej varianty, podľa ktorej bola vytvorená ponuka.</documentation>
                </annotation>
            </element>
            <element name="maxRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Maximální počet splátek podle standardního splátkového kalendáře</documentation>
                </annotation>
            </element>
            <element name="minInstalment" type="decimal">
                <annotation>
                    <documentation>Minimální výše splátky vztažena k maxRepaymentPeriod</documentation>
                </annotation>
            </element>
            <element name="reserveAmountSum" type="decimal">
                <annotation>
                    <documentation>suma částek rezerv pro všechny závazky</documentation>
                </annotation>
            </element>
            <element name="maxInstalment" type="decimal">
                <annotation>
                    <documentation>Maximální výše splátky vztažena k minRepaymentPeriod</documentation>
                </annotation>
            </element>
            <element name="interestRateEvaluation" type="tns:LoanInterestRateEvaluationTO" minOccurs="0">
                <annotation>
                    <documentation>Způsob vyhodnocení úrokové sazby úvěru.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ChangeParamsTO">
        <sequence>
            <element name="instalmentDay" type="int" minOccurs="0">
                <annotation>
                    <documentation>Den splácení : vyplněno v případě procesu "změna dne splácení"</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Výše splátky : vyplněno v případě procesu "změna parametrů půjčky"</documentation>
                </annotation>
            </element>
            <element name="numberInstalments" type="int" minOccurs="0">
                <annotation>
                    <documentation>Počet splátek : vyplněno v případě procesu "změna parametrů půjčky"</documentation>
                </annotation>
            </element>
            <element name="paymentHoliday" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Pokud je true, spusti proces nstaveni splatkovych prazdnin</documentation>
                </annotation>
            </element>
            <element name="managerType" type="tns:ManagerType" minOccurs="0">
                <annotation>
                    <documentation>Priznak managera vymahani (neni povinny, pouziva se jen pri prokliku z WO)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ResultChangeParamsTO">
        <sequence>
            <element name="instalmentDay" type="int">
                <annotation>
                    <documentation>den splátky v měsíci</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal">
                <annotation>
                    <documentation>nesplacená jistina</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal">
                <annotation>
                    <documentation>vyse splatky</documentation>
                </annotation>
            </element>
            <element name="nextInstalment" type="date" minOccurs="0">
                <annotation>
                    <documentation>datum příští splátky - stadardní SK</documentation>
                </annotation>
            </element>
            <element name="numberOfFutureInstalments" type="int">
                <annotation>
                    <documentation>počet měsíčních nesplacených splátek - standardní SK</documentation>
                </annotation>
            </element>
            <element name="loanInterest" type="float">
                <annotation>
                    <documentation>úroková sazba - - formát jako číslo např. 12.12 se budou vracet jako 12.12% -
                        standardní SK
                    </documentation>
                </annotation>
            </element>
            <element name="rpsn" type="float" minOccurs="0">
                <annotation>
                    <documentation>roční procentní sazba nákladů - standadrní SK</documentation>
                </annotation>
            </element>
            <element name="totalLoanCost" type="decimal">
                <annotation>
                    <documentation>celkové náklady - standardní SK</documentation>
                </annotation>
            </element>
            <element name="postponedInstalment" type="date" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>
                        Pro nastaveni splatkovych prazdni obsahuje seznam odlozenych splatek.
                    </documentation>
                </annotation>
            </element>
            <element name="bonusDetail" type="tns:LoanBonusTO" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>informace k aktuálně platnému bonusovému SK - pokud se nevrací, potom žadný neměl
                        nebo o něj přišel
                    </documentation>
                </annotation>
            </element>
            <element name="correctedChangeParams" type="tns:ChangeParamsTO" minOccurs="0">
                <annotation>
                    <documentation>upravene zmenove parametry</documentation>
                </annotation>
            </element>
            <element name="correctedRepaymentPeriod" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>pokud pro požadovaný počet splátek neexistuje výše splátky, OBS sníží počet splátek a vrátí correctedRepaymentPeriod=true
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="AccountNumberPrefixTO">
        <annotation>
            <documentation>Account Number Prefix</documentation>
        </annotation>
        <restriction base="string">
            <pattern value="[0-9]*" />
            <minLength value="1" />
            <maxLength value="6" />
        </restriction>
    </simpleType>

    <simpleType name="AccountNumberTO">
        <annotation>
            <documentation>Account Number</documentation>
        </annotation>
        <restriction base="string">
            <pattern value="[0-9]*" />
            <minLength value="1" />
            <maxLength value="10" />
        </restriction>
    </simpleType>

    <simpleType name="BankCodeTO">
        <annotation>
            <documentation>Numerical Bank Code</documentation>
        </annotation>
        <restriction base="string">
            <pattern value="[0-9]*" />
            <length value="4" />
        </restriction>
    </simpleType>

    <simpleType name="SpecificSymbolTO">
        <annotation>
            <documentation>Specific Symbol - payment's identification</documentation>
        </annotation>
        <restriction base="string">
            <pattern value="[0-9]*" />
            <minLength value="1" />
            <maxLength value="10" />
        </restriction>
    </simpleType>

    <simpleType name="VariableSymbolTO">
        <annotation>
            <documentation>Variable Symbol - payment's identification</documentation>
        </annotation>
        <restriction base="string">
            <pattern value="[0-9]*" />
            <minLength value="1" />
            <maxLength value="10" />
        </restriction>
    </simpleType>

    <complexType name="RepaymentDetailsTO">
        <sequence>
            <element name="accountNumberPrefix" type="tns:AccountNumberPrefixTO" minOccurs="0" />
            <element name="accountNumber" type="tns:AccountNumberTO" />
            <element name="bankCode" type="tns:BankCodeTO" />
            <element name="specificSymbol" type="tns:SpecificSymbolTO" minOccurs="0" />
            <element name="variableSymbol" type="tns:VariableSymbolTO" minOccurs="0" />
        </sequence>
    </complexType>

    <complexType name="ObligationTO">
        <sequence>
            <element name="idObligation" type="long">
                <annotation>
                    <documentation>Obligation identification number</documentation>
                </annotation>
            </element>
            <element name="repaymentDetails" type="tns:RepaymentDetailsTO" minOccurs="0">
                <annotation>
                    <documentation>Obligation repayment details</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ObligationToUpdateTO">
        <sequence>
            <element name="idObligation" type="long">
                <annotation>
                    <documentation>Obligation identification number</documentation>
                </annotation>
            </element>
            <element name="repaymentDetails" type="tns:RepaymentDetailsTO">
                <annotation>
                    <documentation>Obligation repayment details</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ObligationUpdateErrorTO">
        <sequence>
            <element name="idObligation" type="long">
                <annotation>
                    <documentation>Obligation identification number</documentation>
                </annotation>
            </element>
            <element name="rejectReasonCode" type="com:ReasonRejectionType">
                <annotation>
                    <documentation>Obligation update error reason</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DpdHistoryItemTO">
        <sequence>
            <element name="loanNumber" type="tns:LoanNumber" />
            <element name="cuid" type="long">
                <annotation>
                    <documentation>klientské ID z CIFu</documentation>
                </annotation>
            </element>
            <element name="debtRepaymentDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>datum doplacení dluhu (pokud prázdné, produkt je právě dlužný)</documentation>
                </annotation>
            </element>
            <element name="debtAmount" type="decimal">
                <annotation>
                    <documentation>maximální výše dluhu po dobu historické dlužnosti
                        (pokud se jedná o aktuální dlužnost, je naplněna aktuální výše dluhu)
                    </documentation>
                </annotation>
            </element>
            <element name="maxDpd" type="int">
                <annotation>
                    <documentation>maximální výše DPD pro danou dlužnost (pro historickou i aktuální dlužnost)</documentation>
                </annotation>
            </element>
            <element name="isActive" type="boolean">
                <annotation>
                    <documentation>příznak, zdali je úvěr v core-stavu Aktivní</documentation>
                </annotation>
            </element>
            <element name="lastUpdate" type="date">
                <annotation>
                    <documentation>datum poslední aktualizace záznamu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CashloanObligationTO">
        <sequence>
            <element name="accountNumberForRefinancing" type="string" minOccurs="0">
                <annotation>
                    <documentation>Číslo účtu, kód banky, VS, KS a SS. Jednotlivé atributy jsou odděleny kolmítkem. Neposílá se pro interní závazek.
                    </documentation>
                </annotation>
            </element>
            <element name="accountNumberChecked" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Příznak Číslo účtu pro doplacení zkontrolováno. Neposílá se pro interní závazek.</documentation>
                </annotation>
            </element>
            <element name="applicationId" type="long">
                <annotation>
                    <documentation>.</documentation>
                </annotation>
            </element>
            <element name="candidateObligationId" type="long">
                <annotation>
                    <documentation>ID kandidátního závazku definované v AMS.</documentation>
                </annotation>
            </element>
            <element name="financialInstitutionOrigin" type="string" minOccurs="0">
                <annotation>
                    <documentation>Kód finanční instituce od které je původní úvěr.</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>CUID vlastníka úvěru.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
</schema>
