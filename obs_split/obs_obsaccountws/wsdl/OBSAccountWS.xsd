<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:ba="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:fs="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsAccountWS/"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAccountWS/">

    <import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" />
    <import schemaLocation="../xsd/AccountTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />

    <element name="getAccountsRequest">
        <complexType>
            <sequence>
                <element name="idBankAccount" type="long" maxOccurs="unbounded">
                    <annotation>
                        <documentation>id identifikace účtu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountsResponse">
        <complexType>
            <sequence>
                <element name="Account" type="ba:BankAccountTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getAccountsPartialRequest">
        <complexType>
            <sequence>
                <element name="idBankAccount" type="long" maxOccurs="unbounded">
                    <annotation>
                        <documentation>id identifikace účtu</documentation>
                    </annotation>
                </element>
                <element name="accountPart" type="ba:AccountPartEnum" maxOccurs="unbounded" minOccurs="0">
                    <annotation>
                        <documentation>požadované typy údajů bank.účtů, viz. výčet AccountPartEnum a dokumentace vlastností v BankAccountTO</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountsPartialResponse">
        <complexType>
            <sequence>
                <element name="Account" type="ba:BankAccountTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getAccountInfoRequest">
        <complexType>
            <sequence>
                <element name="idBankAccount" type="long" minOccurs="0">
                    <annotation>
                        <documentation>
                            Pokud není uvedeno ID účtu, potom se informace vrací přes profil získaný z SecurityContext.
                            Částky budou vždy v CZK, jinak v měně účtu.
                        </documentation>
                    </annotation>
                </element>
                <element name="validFrom" type="dateTime">
                    <annotation>
                        <documentation>
                            Začátek období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="validTo" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Konec období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="paymentCycle" type="long" minOccurs="0">
                    <annotation>
                        <documentation>
                            Pořadové číslo dne v kalendářním měsíci, kterému se má použít jako konec sledovaného období.
                            Předpoklad je, že si klient bude jako toto datum nastavovat den výplaty.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountInfoResponse">
        <complexType>
            <sequence>
                <element name="futureOrderSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma částky budoucích plateb za požadované období.
                        </documentation>
                    </annotation>
                </element>
                <element name="futureOrderCount" type="int">
                    <annotation>
                        <documentation>
                            Počet budoucích plateb za požadované období.
                        </documentation>
                    </annotation>
                </element>
                <element name="standingOrderSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma částky platných trvalých plateb za požadované období.
                        </documentation>
                    </annotation>
                </element>
                <element name="standingOrderCount" type="int">
                    <annotation>
                        <documentation>
                            Počet platných trvalých plateb za požadované období.
                        </documentation>
                    </annotation>
                </element>
                <element name="notRealizedSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma částky nezrealizovaných plateb. Jejich platnost vypršela a dosud nebyly zrealizované.
                        </documentation>
                    </annotation>
                </element>
                <element name="notRealizedCount" type="int">
                    <annotation>
                        <documentation>
                            Počet nezrealizovaných plateb. Jejich platnost vypršela a dosud nebyly zrealizované.
                        </documentation>
                    </annotation>
                </element>
                <element name="inProcessSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma částky nezrealizovaných platných plateb.
                            Jejich platnost dosud nevypršela a dosud nebyly zrealizované.
                        </documentation>
                    </annotation>
                </element>
                <element name="inProcessCount" type="int">
                    <annotation>
                        <documentation>
                            Počet nezrealizovaných platných plateb.
                            Jejich platnost dosud nevypršela a dosud nebyly zrealizované.
                        </documentation>
                    </annotation>
                </element>
                <element name="collectionCount" type="int">
                    <annotation>
                        <documentation>
                            Počet inkas, které je potřeba povolit.
                        </documentation>
                    </annotation>
                </element>
                <element name="collectionSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma částky inkas, které je potřeba povolit.
                        </documentation>
                    </annotation>
                </element>
                <element name="futureDate" type="dateTime">
                    <annotation>
                        <documentation>
                            Datum konce období (datum včetně časové složky), ke kterému je proveden výpočet.
                            Určuje se na základě vstupních parametrů ValidTo a PaymentCycle.
                        </documentation>
                    </annotation>
                </element>
                <element name="futureBalance" type="decimal">
                    <annotation>
                        <documentation>
                            Předpokládaný (vypočtený) zůstatek na účtu klienta k FutureDate.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="findAccountsRequest">
        <complexType>
            <sequence>
                <element name="filter" type="fs:SelectFilter">
                    <annotation>
                        <documentation>
                            možné filtry:

                            PAYMENTTYPE - viz AccountTransactionType (AccountTO.xsd) - nepovinný -
                            BANKACCOUNTYPE - viz BankAccountTypeCode (AccountTO.xsd) - nepovinný
                            CURRENCY - kód měny - nepovinný
                            ACCOUNTSTATUS - stav účtu - nepovinný, string z BankAccountStatus (AccountTO.xsd) - možné použít operátor IN
                            CURRENCY - měna účtu - string nepovinný - kód měny účtu (např. CZK, USD, EUR, ...) - možný operátor =

                            případná vazba mezi filtračními atributy je
                            AND Vždy vybíráme jen účty z poheldu IB
                            aktivní (tj. dle obs aktivní a jakkoli
                            blokované)

                            Řazení bude vždy běžné účty první, poté
                            spořící. Sekundárně se bude řadit podle
                            uživatelského jména účtu.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="findAccountsResponse">
        <complexType>
            <sequence>
                <element name="idBankAccount" type="long" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="findAndGetAccountsRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long">
                    <annotation>
                        <documentation>cuid osoby ve vztahu k bance</documentation>
                    </annotation>
                </element>
                <element name="profileId" type="long" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Požadované profily osoby</documentation>
                    </annotation>
                </element>
                <element name="accessChannel" type="ba:AccessChannel" />

                <element name="paymentType" type="ba:AccountTransactionType" minOccurs="0"/>
                <element name="bankAccountType" type="ba:BankAccountKindCode" minOccurs="0"/>
                <element name="currency" type="string" minOccurs="0">
                    <annotation>
                        <documentation>měna účtu - ISO kód měny účtu (např. CZK, USD, EUR, ...)</documentation>
                    </annotation>
                </element>
                <element name="accountStatus" type="ba:BankAccountStatus" minOccurs="0" maxOccurs="unbounded"/>
                <element name="accountPart" type="ba:AccountPartEnum" maxOccurs="unbounded" minOccurs="0">
                    <annotation>
                        <documentation>požadované typy údajů bank.účtů, viz. výčet AccountPartEnum a dokumentace vlastností v BankAccountTO</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="findAndGetAccountsResponse">
        <complexType>
            <sequence>
                <element name="Account" type="ba:BankAccountTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getJpkAccountRequest">
        <complexType>
            <sequence>
                <element type="long" name="cuid" />
            </sequence>
        </complexType>
    </element>

    <element name="getJpkAccountResponse">
        <complexType>
            <sequence>
                <element name="Account" type="ba:BankAccountTO" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="blockoutDispositionAccountsRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long">
                    <annotation>
                        <documentation>cuid klienta</documentation>
                    </annotation>
                </element>
                <element name="businessProcess" type="ba:BankAccountBlockoutBusinessProcess">
                    <annotation>
                        <documentation>Triggered by business process</documentation>
                    </annotation>
                </element>
                <element name="businessProcessEvent" type="string" minOccurs="0">
                    <annotation>
                        <documentation>Triggered by business event</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="blockoutDispositionAccountsResponse">
        <complexType>
            <sequence>
                <element name="Account" type="ba:AccountTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getInstallmentAccountRequest">
        <complexType>
            <sequence>
                <element type="long" name="idLoan" />
            </sequence>
        </complexType>
    </element>

    <element name="getInstallmentAccountResponse">
        <complexType>
            <sequence>
                <element name="Account" type="ba:BankAccountTO" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getAccountDispoRequest">
        <complexType>
            <sequence>
                <element name="accountNumber" type="string" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Číslo účtu, pro který se má předat disponibilní zůstatek.</documentation>
                    </annotation>
                </element>
                <element name="useActivity" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>
                            true - revert sign of balance for asset accounts (this is also a default if this element is missing)
                            false - do not revert sign of balance for asset accounts
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountDispoResponse">
        <complexType>
            <sequence>
                <element name="accountDispoTo" type="ba:AccountDispoTO" maxOccurs="unbounded" minOccurs="0">
                    <annotation>
                        <documentation>Disponibilní zůstatek účtu ve měně účtu.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="checkAccountDispoRequest">
        <complexType>
            <sequence>
                <element name="iban" type="string" />
                <element name="amount" type="decimal" />
                <element name="currency" type="string">
                    <annotation>
                        <documentation>Currency code - ISO 4217 alphabetic code</documentation>
                    </annotation>
                </element>
                <element name="merchant" minOccurs="0">
                    <complexType>
                        <sequence>
                            <element name="identification" type="string" />
                            <element name="shortName" type="string" />
                            <element name="countryCode" type="string" minOccurs="0">
                                <annotation>
                                    <documentation>ISO 3166-1 alpha-2 code</documentation>
                                </annotation>
                            </element>
                        </sequence>
                    </complexType>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="checkAccountDispoResponse">
        <complexType>
            <sequence>
                <element name="result">
                    <simpleType>
                        <restriction base="string">
                            <enumeration value="FORBIDDEN">
                                <annotation>
                                    <documentation>You do not have permission to check dispo for requested account</documentation>
                                </annotation>
                            </enumeration>
                            <enumeration value="APPROVED">
                                <annotation>
                                    <documentation>Requested amount is approved</documentation>
                                </annotation>
                            </enumeration>
                            <enumeration value="DECLINED">
                                <annotation>
                                    <documentation>Not enough money present in an account to cover a requested transaction</documentation>
                                </annotation>
                            </enumeration>
                            <enumeration value="INVALID_DEBTOR_ACCOUNT" />
                            <enumeration value="INVALID_CURRENCY" />
                        </restriction>
                    </simpleType>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountStatusRequest">
        <complexType>
            <sequence>
                <element name="accountNumber" type="string" />
            </sequence>
        </complexType>
    </element>

    <element name="getAccountStatusResponse">
        <complexType>
            <sequence>
                <element name="accountStatus" type="ba:BankAccountStatus">
                    <annotation>
                        <documentation>Metoda na zjištění stavu účtu (procesního stavu, ne zůstatku na účtu).</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountBalanceHistoryRequest">
        <complexType>
            <sequence>
                <element name="filter" type="fs:SelectFilter">
                    <annotation>
                        <documentation>
                            filtrace podle

                            Povinná pole
                            dateFrom - date (počátek časového intervalu - povolený operátor je =)
                            dateTo - date (konec časového intervalu povolený operátor je =)
                            idBankAccount - long (ID bankovního účtu)
                            Pozn: rozpětí dateFrom a detaTo může být maximálně rok
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAccountBalanceHistoryResponse">
        <complexType>
            <sequence>
                <element name="accountBalance" type="ba:AccountBalanceTO" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <element name="getInterestRateHistoryRequest">
        <complexType>
            <sequence>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>id účtu</documentation>
                    </annotation>
                </element>
                <element name="dateFrom" type="date" minOccurs="0">
                    <annotation>
                        <documentation>začátek období</documentation>
                    </annotation>
                </element>
                <element name="dateTo" type="date" minOccurs="0">
                    <annotation>
                        <documentation>konec období</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getInterestRateHistoryResponse">
        <complexType>
            <sequence>
                <element name="interests" type="ba:InterestIncentiveGroupTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getExtendAccountDetailRequest">
        <complexType>
            <sequence>
                <element name="idBankAccount" type="long" />
            </sequence>
        </complexType>
    </element>

    <element name="getExtendAccountDetailResponse">
        <complexType>
            <sequence>
                <element name="activeDate" type="date" minOccurs="0">
                    <annotation>
                        <documentation>
                            Datum, kdy účet přešel do stavu aktivní.
                            - pokud se datum nevrací, účet se nikdy do aktivního stavu nepřepl
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getInterestRateRequest">
        <complexType>
            <sequence>
                <element name="bankAccountKind" type="ba:BankAccountKindCode" minOccurs="0" />
                <element name="currencyCode" type="string" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getInterestRateResponse">
        <complexType>
            <sequence>
                <element name="interests" type="ba:InterestRateTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getAccountsWithCardsRequest">
        <complexType>
            <sequence />
        </complexType>
    </element>

    <element name="getAccountsWithCardsResponse">
        <complexType>
            <sequence>
                <element name="account" type="ba:BankAccountShortTO" maxOccurs="unbounded" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

    <element name="getAllAccountsRequest">
        <complexType>
            <choice>
                <sequence>
                    <element name="cuid" type="long" />
                    <element name="actualGCOnly" type="boolean" minOccurs="0" />
                    <element name="includeDispoAccounts" type="boolean" minOccurs="0" />
                    <element name="includeEntitledAccounts" type="boolean" minOccurs="0" />
                </sequence>
                <sequence>
                    <element name="accountNumber" type="string" minOccurs="1" maxOccurs="unbounded" />
                </sequence>
            </choice>
        </complexType>
    </element>

    <element name="getAllAccountsResponse">
        <complexType>
            <sequence>
                <element name="account" type="ba:BankAccountExtTO" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <complexType name="createAccountRequest" abstract="true">
        <sequence>
            <element name="GCID" type="long" />
            <element name="currency" type="string">
                <annotation>
                    <documentation>
                        ISO alphabetic currency code
                    </documentation>
                </annotation>
            </element>
            <element name="distributionChannel" type="ba:distributionChannel">
                <annotation>
                    <documentation>
                        obsahuje kanal vytvoreni zadosti, pokud jde o pobocku, tak obsahuje i ID pobocky
                    </documentation>
                </annotation>
            </element>
            <element name="isMainAccount" type="boolean">
                <annotation>
                    <documentation>Set this account as main for fees</documentation>
                </annotation>
            </element>
            <element name="clientName" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        Account name given by client
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="createAccountResponse" abstract="true">
        <sequence>
            <element name="idAccount" type="long" />
        </sequence>
    </complexType>

    <element name="createCurrentAccountRequest">
        <complexType>
            <complexContent>
                <extension base="tns:createAccountRequest" />
            </complexContent>
        </complexType>
    </element>

    <element name="createCurrentAccountResponse">
        <complexType>
            <complexContent>
                <extension base="tns:createAccountResponse" />
            </complexContent>
        </complexType>
    </element>

    <element name="createSavingAccountRequest">
        <complexType>
            <complexContent>
                <extension base="tns:createAccountRequest" />
            </complexContent>
        </complexType>
    </element>

    <element name="createSavingAccountResponse">
        <complexType>
            <complexContent>
                <extension base="tns:createAccountResponse" />
            </complexContent>
        </complexType>
    </element>

    <element name="createChildSavingAccountRequest">
        <complexType>
            <complexContent>
                <extension base="tns:createAccountRequest">
                  <sequence>
                    <element name="childCuid" type="long">
                      <annotation>
                        <documentation>identifikace (cuid) dětského disponenta</documentation>
                      </annotation>
                    </element>
                  </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="createChildSavingAccountResponse">
        <complexType>
            <complexContent>
                <extension base="tns:createAccountResponse" />
            </complexContent>
        </complexType>
    </element>

    <element name="cancelCurrentAccountRequest">
        <complexType>
            <sequence>
                <element name="idAccount" type="long" />
            </sequence>
        </complexType>
    </element>

    <element name="cancelCurrentAccountResponse">
        <complexType />
    </element>

    <element name="cancelSavingAccountRequest">
        <complexType>
            <sequence>
                <element name="idAccount" type="long" />
            </sequence>
        </complexType>
    </element>

    <element name="cancelSavingAccountResponse">
        <complexType />
    </element>

    <element name="cancelChildSavingAccountRequest">
        <complexType>
            <sequence>
                <element name="idAccount" type="long" />
            </sequence>
        </complexType>
    </element>

    <element name="cancelChildSavingAccountResponse">
        <complexType />
    </element>

    <element name="evaluateAvailableBalanceRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long" />
                <element name="amount" type="decimal" />
            </sequence>
        </complexType>
    </element>

    <element name="evaluateAvailableBalanceResponse">
        <complexType>
            <sequence>
                <element name="balanceAvailable" type="boolean" minOccurs="0" />
            </sequence>
        </complexType>
    </element>

</schema>
