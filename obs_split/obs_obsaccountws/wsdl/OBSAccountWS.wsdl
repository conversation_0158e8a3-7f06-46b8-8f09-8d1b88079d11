<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsAccountWS/"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAccountWS/"
    name="OBSAccountWS">

    <types>
        <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAccountWS/">
            <xsd:include schemaLocation="OBSAccountWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="getAccountsRequest">
        <part name="parameters" element="tns:getAccountsRequest" />
    </message>

    <message name="getAccountsResponse">
        <part name="parameters" element="tns:getAccountsResponse" />
    </message>

    <message name="getAccountsPartialRequest">
        <part name="parameters" element="tns:getAccountsPartialRequest" />
    </message>

    <message name="getAccountsPartialResponse">
        <part name="parameters" element="tns:getAccountsPartialResponse" />
    </message>

    <message name="getAccountInfoRequest">
        <part name="parameters" element="tns:getAccountInfoRequest" />
    </message>

    <message name="getAccountInfoResponse">
        <part name="parameters" element="tns:getAccountInfoResponse" />
    </message>

    <message name="findAccountsRequest">
        <part name="parameters" element="tns:findAccountsRequest" />
    </message>

    <message name="findAccountsResponse">
        <part name="parameters" element="tns:findAccountsResponse" />
    </message>

    <message name="findAndGetAccountsRequest">
        <part name="parameters" element="tns:findAndGetAccountsRequest" />
    </message>

    <message name="findAndGetAccountsResponse">
        <part name="parameters" element="tns:findAndGetAccountsResponse" />
    </message>

    <message name="getJpkAccountRequest">
        <part name="parameters" element="tns:getJpkAccountRequest" />
    </message>

    <message name="getJpkAccountResponse">
        <part name="parameters" element="tns:getJpkAccountResponse" />
    </message>

    <message name="blockoutDispositionAccountsRequest">
        <part name="parameters" element="tns:blockoutDispositionAccountsRequest" />
    </message>

    <message name="blockoutDispositionAccountsResponse">
        <part name="parameters" element="tns:blockoutDispositionAccountsResponse" />
    </message>

    <message name="getInstallmentAccountRequest">
        <part name="parameters" element="tns:getInstallmentAccountRequest" />
    </message>

    <message name="getInstallmentAccountResponse">
        <part name="parameters" element="tns:getInstallmentAccountResponse" />
    </message>

    <message name="getAccountDispoRequest">
        <part name="parameters" element="tns:getAccountDispoRequest" />
    </message>

    <message name="getAccountDispoResponse">
        <part name="parameters" element="tns:getAccountDispoResponse" />
    </message>

    <message name="checkAccountDispoRequest">
        <part name="parameters" element="tns:checkAccountDispoRequest" />
    </message>

    <message name="checkAccountDispoResponse">
        <part name="parameters" element="tns:checkAccountDispoResponse" />
    </message>

    <message name="getAccountStatusRequest">
        <part name="parameters" element="tns:getAccountStatusRequest" />
    </message>

    <message name="getAccountStatusResponse">
        <part name="parameters" element="tns:getAccountStatusResponse" />
    </message>

    <message name="getAccountBalanceHistoryRequest">
        <part name="parameters" element="tns:getAccountBalanceHistoryRequest" />
    </message>

    <message name="getAccountBalanceHistoryResponse">
        <part name="parameters" element="tns:getAccountBalanceHistoryResponse" />
    </message>

    <message name="getInterestRateHistoryRequest">
        <part name="parameters" element="tns:getInterestRateHistoryRequest" />
    </message>

    <message name="getInterestRateHistoryResponse">
        <part name="parameters" element="tns:getInterestRateHistoryResponse" />
    </message>

    <message name="getExtendAccountDetailRequest">
        <part name="parameters" element="tns:getExtendAccountDetailRequest" />
    </message>

    <message name="getExtendAccountDetailResponse">
        <part name="parameters" element="tns:getExtendAccountDetailResponse" />
    </message>

    <message name="getInterestRateRequest">
        <part name="parameters" element="tns:getInterestRateRequest" />
    </message>

    <message name="getInterestRateResponse">
        <part name="parameters" element="tns:getInterestRateResponse" />
    </message>

    <message name="getAccountsWithCardsRequest">
        <part name="parameters" element="tns:getAccountsWithCardsRequest" />
    </message>

    <message name="getAccountsWithCardsResponse">
        <part name="parameters" element="tns:getAccountsWithCardsResponse" />
    </message>

    <message name="getAllAccountsRequest">
        <part name="parameters" element="tns:getAllAccountsRequest" />
    </message>

    <message name="getAllAccountsResponse">
        <part name="parameters" element="tns:getAllAccountsResponse" />
    </message>

    <message name="createCurrentAccountRequest">
        <part name="parameters" element="tns:createCurrentAccountRequest" />
    </message>

    <message name="createCurrentAccountResponse">
        <part name="parameters" element="tns:createCurrentAccountResponse" />
    </message>

    <message name="createSavingAccountRequest">
        <part name="parameters" element="tns:createSavingAccountRequest" />
    </message>

    <message name="createSavingAccountResponse">
        <part name="parameters" element="tns:createSavingAccountResponse" />
    </message>

    <message name="createChildSavingAccountRequest">
        <part name="parameters" element="tns:createChildSavingAccountRequest" />
    </message>

    <message name="createChildSavingAccountResponse">
        <part name="parameters" element="tns:createChildSavingAccountResponse" />
    </message>

    <message name="cancelCurrentAccountRequest">
        <part name="parameters" element="tns:cancelCurrentAccountRequest" />
    </message>

    <message name="cancelCurrentAccountResponse">
        <part name="parameters" element="tns:cancelCurrentAccountResponse" />
    </message>

    <message name="cancelSavingAccountRequest">
        <part name="parameters" element="tns:cancelSavingAccountRequest" />
    </message>

    <message name="cancelSavingAccountResponse">
        <part name="parameters" element="tns:cancelSavingAccountResponse" />
    </message>

    <message name="cancelChildSavingAccountRequest">
        <part name="parameters" element="tns:cancelChildSavingAccountRequest" />
    </message>

    <message name="cancelChildSavingAccountResponse">
        <part name="parameters" element="tns:cancelChildSavingAccountResponse" />
    </message>

    <message name="evaluateAvailableBalanceRequest">
        <part name="parameters" element="tns:evaluateAvailableBalanceRequest" />
    </message>

    <message name="evaluateAvailableBalanceResponse">
        <part name="parameters" element="tns:evaluateAvailableBalanceResponse" />
    </message>

    <portType name="obsAccountWS">
        <operation name="getAccounts">
            <documentation>
                vrátí informace o zadaných účtec

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:getAccountsRequest" />
            <output message="tns:getAccountsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAccountsPartial">
            <documentation>
                vrátí požadované informace o zadaných účtech

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas.
            </documentation>
            <input message="tns:getAccountsPartialRequest" />
            <output message="tns:getAccountsPartialResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAccountInfo">
            <documentation>
                informace o transakcích, trvalých příkazech a inkas k danému účt nebo profilu

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                CLERR_ACCESS / GENERAL_ERROR / :	uživatel nemá právo na účet
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / idBankAccount / : id účtu nenalezeno
            </documentation>
            <input message="tns:getAccountInfoRequest" />
            <output message="tns:getAccountInfoResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="findAccounts">
            <documentation>
                Metoda pro získání primárních klíče účtů zvoleného profil.
                Účty se standartně zafiltrují na idProfilu ze soap obálky.

                požadované podmínky:
                přihlášeného uživatele: ano

                možné chyby:
                kód / atribut / hodnota : popis
                CLERR_ACCESS / GENERAL_ERROR / :	uživatel nemá právo na účet
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_UNSUPPORTED_FILTER / xx / : tato chyba se týká fitrovacích funkcí - filtr nepodporuje daný filtrovací atribut xx
                CLERR_UNSUPPORTED_ORDER / xx / : tato chyba se týká fitrovacích funkcí - filtr nepodporuje daný řadící atribut xx
            </documentation>
            <input message="tns:findAccountsRequest" />
            <output message="tns:findAccountsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="findAndGetAccounts">
            <documentation>
                Metoda pro získání účtů zvoleného profilu.
                Účty se standartně zafiltrují na idProfilu ze soap obálky.

                požadované podmínky:
                přihlášeného uživatele: ano

                možné chyby:
                kód / atribut / hodnota : popis
                CLERR_ACCESS / GENERAL_ERROR / : uživatel nemá právo na účet
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / CUID / : nenalezen uzivatel dle zadaneho CUID
                MISSING_PARAMETER / nazev parametru / : chybi povinny parametr
            </documentation>
            <input message="tns:findAndGetAccountsRequest" />
            <output message="tns:findAndGetAccountsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getJpkAccount">
            <documentation>Metoda pro získání JPK účtu na základě CUID.</documentation>
            <input message="tns:getJpkAccountRequest" />
            <output message="tns:getJpkAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="blockoutDispositionAccounts">
            <documentation>
                Zablokuje účty stavem pro odchozí platby, ke kterým má
                dispoziční právo (cuid). Vrátí seznam zablokovaných účtů na
                základě CUID.
            </documentation>
            <input message="tns:blockoutDispositionAccountsRequest" />
            <output message="tns:blockoutDispositionAccountsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getInstallmentAccount">
            <documentation>Metoda pro získání splatkoveho účtu na základě CUID.</documentation>
            <input message="tns:getInstallmentAccountRequest" />
            <output message="tns:getInstallmentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAccountDispo">
            <documentation>
                Metoda pro získání disponibilního zůstatku pro zadané číslo účtu. Předpokládá se předávání většího množství čísel účtů.
                Maximalni počet čísel účtů, kterou služba přijímá je definován parametrem maxAccountDispoRecords, ze služby obsCoreParametersWS, operace getGPCard.

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován technicalContext:  pouze systém IB

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:getAccountDispoRequest" />
            <output message="tns:getAccountDispoResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="checkAccountDispo">
            <documentation>

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CLERR_IS_MANDATORY / businessContex.Customer.contractId / - povinná položka
                CLERR_NO_DATA_FOUND / businessContex.Customer.contractId / - smlouva nenalezena
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:checkAccountDispoRequest" />
            <output message="tns:checkAccountDispoResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAccountStatus">
            <documentation>
                Zjištění stavu účtu.

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován technicalContext:  pouze systém IB

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                ACCOUNT_NOT_FOUND / accountNumber / Systém nenašel účet, podle zaslaného čísla.
            </documentation>
            <input message="tns:getAccountStatusRequest" />
            <output message="tns:getAccountStatusResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAccountBalanceHistory">
            <documentation>
                Operace vrací denní zůstatky účtu. Setřídění je datum vzestupně

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext:  ano

                Chybové stavy:
                Požadovaný účet nebyl nalezen.
                dateFrom &gt; dateTo - logický chyba
                Rozsah dateFrom a dateTo přesahuje maximální povolený interval (1 kalendářní rok nebo 360 dní)
                Pokud nenalezeny podklady pro zadané období, vrátí se prázdný seznam.

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:getAccountBalanceHistoryRequest" />
            <output message="tns:getAccountBalanceHistoryResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getInterestRateHistory">
            <documentation>
                Aplikační logika:
                - Systém na základě obdrženého ID uctu prohledá databázi pobídek a pokud je k danému účtu připojena nějaká pobídka nebo více pobídek, jsou vráceny parametry dané pobídky (pobídek). Vráceny jsou pobídky, které jsou platné v současnosti nebo v budoucnosti.
                - Pokud je ručně přiřazená úroková sazba potom je nastaven flag individual a list jednotlivých pobídek je prázdný ( celková úroková sazba se nepočítá a je nastavena na pevnou hodnotu totalInterest.
                - Pokud flag individual je false potom úroková sazba je vypočtena jako suma účinných úrokových pobídek (effective - true) a je přičtena základní úroková sazba (vše k datu). To znamená že základní úroková sazba se získá jako totalInterest – suma účinné z listu jednotlivých pobídek.
                - V aktuálním měsici lze ještě získat z listu pobídek neúčinnou (příznak effective je false). Ta se nezapočítává do hodnoty totalInterest
                - V historických měsících nejsou neúčinné pobídky
                - Základní úroková sazba se získá odečtením celkové (totalInterest - suma účinných !!!)
                  Pokud jsou v InterestIncentiveTO pouze neucinné, potom základní úroková sazba je rovna totalInterest

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                CLERR_ACCESS / GENERAL_ERROR / :	uživatel nemá právo na účet
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
            </documentation>
            <input message="tns:getInterestRateHistoryRequest" />
            <output message="tns:getInterestRateHistoryResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getExtendAccountDetail">
            <documentation>
                metoda vrací další atributy k zadanému účtu (tyto atributy jsou potřeba jen v určitých situacích)

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován technicalContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:getExtendAccountDetailRequest" />
            <output message="tns:getExtendAccountDetailResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getInterestRate">
            <input message="tns:getInterestRateRequest" />
            <output message="tns:getInterestRateResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAccountsWithCards">
            <documentation>
                Get accounts with cards for person and profile from security context. Only a basic
                information about these accounts is returned because client may not have permissions to these accounts.

                (SOAPFault):
                code / atribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general system error
                GENERAL_ERROR_ACCESS / GENERAL_ERROR / / :	person is not logged
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled back
            </documentation>
            <input message="tns:getAccountsWithCardsRequest" />
            <output message="tns:getAccountsWithCardsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getAllAccounts">
            <documentation>
                Get all accounts for person. This operation check operator level.

                (SOAPFault):
                code / atribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / : general system error
                CLERR_ACCESS / GENERAL_ERROR / : operator has not right for current person
                CLERR_TIMEOUT / GENERAL_ERROR / : system cannot handle the request in time, all the changes were rolled back
            </documentation>
            <input message="tns:getAllAccountsRequest" />
            <output message="tns:getAllAccountsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="createCurrentAccount">
            <documentation>
                zalozi bezny ucet na smluve ve stavu aktivni nebo v narhu

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / GCID / : smlouva nenalezena
                CLERR_WRONG_STATUS / GCID / : smlouve je v nepodporovanom stavu
                CLERR_NO_DATA_FOUND / currency / : nepodporovana měna
            </documentation>
            <input message="tns:createCurrentAccountRequest" />
            <output message="tns:createCurrentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="createSavingAccount">
            <documentation>
                zalozi sporici ucet na smluve ve stavu aktivni nebo v narhu

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / GCID / : smlouva nenalezena
                CLERR_WRONG_STATUS / GCID / : smlouve je v nepodporovanom stavu
                CLERR_NO_DATA_FOUND / currency / : nepodporovana měna
            </documentation>
            <input message="tns:createSavingAccountRequest" />
            <output message="tns:createSavingAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="createChildSavingAccount">
            <documentation>
                Založí dětský spořicí účet.

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly odrolovány
                CLERR_NO_DATA_FOUND / GCID / : smlouva nebyla nalezena
                CLERR_WRONG_STATUS / GCID / : smlouva je v nepodporovaném stavu
                CLERR_NO_DATA_FOUND / currency / : nepodporovaná měna
                CLERR_NO_DATA_FOUND / childCuid / : disponent nebyl nalezen
            </documentation>
            <input message="tns:createChildSavingAccountRequest" />
            <output message="tns:createChildSavingAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="cancelCurrentAccount">
            <documentation>
                Zruseni neaktivovaneho (v stavu DEMO) bezneho uctu

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / idAccount / : účet nenalezen
            </documentation>
            <input message="tns:cancelCurrentAccountRequest" />
            <output message="tns:cancelCurrentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="cancelSavingAccount">
            <documentation>
                Zruseni neaktivovaneho (v stavu DEMO) sporiciho uctu

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / idAccount / : účet nenalezen
            </documentation>
            <input message="tns:cancelSavingAccountRequest" />
            <output message="tns:cancelSavingAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="cancelChildSavingAccount">
            <documentation>
                Zruší neaktivní (stav DEMO) dětský spořicí účet.

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek včas, případné změny byly odrolovány
                CLERR_NO_DATA_FOUND / idAccount / : spořicí účet pro děti nebyl nalezen nebo je v neočekávaném stavu
            </documentation>
            <input message="tns:cancelChildSavingAccountRequest" />
            <output message="tns:cancelChildSavingAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="evaluateAvailableBalance">
            <input message="tns:evaluateAvailableBalanceRequest" />
            <output message="tns:evaluateAvailableBalanceResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

    </portType>

    <binding name="obsAccountWSSOAP" type="tns:obsAccountWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="getAccounts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAccountsPartial">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAccountInfo">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="findAccounts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="findAndGetAccounts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getJpkAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="blockoutDispositionAccounts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getInstallmentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAccountDispo">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="checkAccountDispo">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAccountStatus">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAccountBalanceHistory">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getInterestRateHistory">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getExtendAccountDetail">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getInterestRate">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAccountsWithCards">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getAllAccounts">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createCurrentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createSavingAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createChildSavingAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="cancelCurrentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="cancelSavingAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="cancelChildSavingAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="evaluateAvailableBalance">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="obsAccountWS">
        <port binding="tns:obsAccountWSSOAP" name="obsAccountWSSOAP">
            <soap:address location="/ws/obsAccountWS" />
        </port>
    </service>
</definitions>
