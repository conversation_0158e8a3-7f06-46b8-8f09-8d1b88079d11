<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://airbank.cz/obs/ws/investmentWS"
    targetNamespace="http://airbank.cz/obs/ws/investmentWS">

    <types>
        <xsd:schema>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
            <xsd:import namespace="http://airbank.cz/obs/ws/investmentWS" schemaLocation="investmentWS.xsd" />
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="getInvestmentAccountRequest">
        <part name="getInvestmentAccountRequest" element="tns:getInvestmentAccountRequest" />
    </message>

    <message name="getInvestmentAccountResponse">
        <part name="getInvestmentAccountResponse" element="tns:getInvestmentAccountResponse" />
    </message>

    <message name="activateInvestmentAccountRequest">
        <part name="activateInvestmentAccountRequest" element="tns:activateInvestmentAccountRequest" />
    </message>

    <message name="activateInvestmentAccountResponse">
        <part name="activateInvestmentAccountResponse" element="tns:activateInvestmentAccountResponse" />
    </message>

    <message name="cancelInvestmentAccountRequest">
        <part name="cancelInvestmentAccountRequest" element="tns:cancelInvestmentAccountRequest" />
    </message>

    <message name="cancelInvestmentAccountResponse">
        <part name="cancelInvestmentAccountResponse" element="tns:cancelInvestmentAccountResponse" />
    </message>

    <message name="createContractRequest">
        <part name="createContractRequest" element="tns:createContractRequest" />
    </message>

    <message name="createContractResponse">
        <part name="createContractResponse" element="tns:createContractResponse" />
    </message>

    <message name="getContractRequest">
        <part name="getContractRequest" element="tns:getContractRequest" />
    </message>

    <message name="getContractResponse">
        <part name="getContractResponse" element="tns:getContractResponse" />
    </message>

    <message name="hasContractRequest">
        <part name="hasContractRequest" element="tns:hasContractRequest" />
    </message>

    <message name="hasContractResponse">
        <part name="hasContractResponse" element="tns:hasContractResponse" />
    </message>

    <message name="cancelContractRequest">
        <part name="cancelContractRequest" element="tns:cancelContractRequest" />
    </message>

    <message name="cancelContractResponse">
        <part name="cancelContractResponse" element="tns:cancelContractResponse" />
    </message>

    <message name="activateContractRequest">
        <part name="activateContractRequest" element="tns:activateContractRequest" />
    </message>

    <message name="activateContractResponse">
        <part name="activateContractResponse" element="tns:activateContractResponse" />
    </message>

    <message name="setContractTerminationRequest">
        <part name="setContractTerminationRequest" element="tns:setContractTerminationRequest" />
    </message>

    <message name="setContractTerminationResponse">
        <part name="setContractTerminationResponse" element="tns:setContractTerminationResponse" />
    </message>

    <message name="terminateContractRequest">
        <part name="terminateContractRequest" element="tns:terminateContractRequest" />
    </message>

    <message name="terminateContractResponse">
        <part name="terminateContractResponse" element="tns:terminateContractResponse" />
    </message>

    <message name="canTerminateInvestmentAccountRequest">
        <part name="canTerminateInvestmentAccountRequest" element="tns:canTerminateInvestmentAccountRequest" />
    </message>

    <message name="canTerminateInvestmentAccountResponse">
        <part name="canTerminateInvestmentAccountResponse" element="tns:canTerminateInvestmentAccountResponse" />
    </message>

    <message name="terminateInvestmentAccountRequest">
        <part name="terminateInvestmentAccountRequest" element="tns:terminateInvestmentAccountRequest" />
    </message>

    <message name="terminateInvestmentAccountResponse">
        <part name="terminateInvestmentAccountResponse" element="tns:terminateInvestmentAccountResponse" />
    </message>

    <message name="createMeetingRecordRequest">
        <part name="createMeetingRecordRequest" element="tns:createMeetingRecordRequest" />
    </message>

    <message name="createMeetingRecordResponse">
        <part name="createMeetingRecordResponse" element="tns:createMeetingRecordResponse" />
    </message>

    <portType name="investment">
        <operation name="getInvestmentAccount">
            <documentation>
                Get client bank account for investments

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                PERSON_NOT_FOUND / cuid / : Person specified by it's cuid doesn't exist
            </documentation>
            <input message="tns:getInvestmentAccountRequest" />
            <output message="tns:getInvestmentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="activateInvestmentAccount">
            <documentation>
                Activates client's bank account for investments.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER / accountNumber / : The account is required and was not entered.
                ACCOUNT_NOT_FOUND / accountNumber / value: The account was not found.
                INVALID_ACCOUNT_STATUS / GENERAL_ERROR / : The account status is not DEMO.
                INVALID_ACCOUNT_TYPE / GENERAL_ERROR / : The account type is not INVESTMENT.
            </documentation>
            <input message="tns:activateInvestmentAccountRequest" />
            <output message="tns:activateInvestmentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="cancelInvestmentAccount">
            <documentation>
                Cancels client's bank account for investments.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER / accountNumber / : The account is required and was not entered.
                ACCOUNT_NOT_FOUND / accountNumber / value: The account was not found.
                INVALID_ACCOUNT_STATUS / GENERAL_ERROR / : The account status is not DEMO.
                INVALID_ACCOUNT_TYPE / GENERAL_ERROR / : The account type is not INVESTMENT.
            </documentation>
            <input message="tns:cancelInvestmentAccountRequest" />
            <output message="tns:cancelInvestmentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="createContract">
            <documentation>
                Creates a new investment agreement.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : Some of the input parameters were not specified or the corresponding entity was not found or the parameter has an invalid value.
                ALREADY_ACTIVE : The client already has an active investment contract.
                DIFFERENT_PARAMETERS : The client has an investment contract in a proposal with different parameters.
            </documentation>
            <input message="tns:createContractRequest" />
            <output message="tns:createContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="getContract">
            <documentation>
                Returns the investment contract details.

                Assumptions:
                logged in user - no
                An active investment contract is required.

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : No search criteria specified or the person, investment account or asset account was not found.
                CONTRACT_NOT_FOUND : The Investment contract was not found or is not effective.
            </documentation>
            <input message="tns:getContractRequest" />
            <output message="tns:getContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="hasContract">
            <documentation>
                Finds out whether customer (cuid) has a investment contract

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : Person was not found using search criteria from the request.
            </documentation>
            <input message="tns:hasContractRequest" />
            <output message="tns:hasContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="cancelContract">
            <documentation>
                Cancels an inactive investment contract.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : The Investment contract number must be entered.
                CONTRACT_NOT_FOUND : The Investment contract was not found or is not in appropriate state.
            </documentation>
            <input message="tns:cancelContractRequest" />
            <output message="tns:cancelContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="activateContract">
            <documentation>
                Activates an investment contract.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : The Investment contract number must be entered.
                CONTRACT_NOT_FOUND : The Investment contract was not found or is not in appropriate state.
            </documentation>
            <input message="tns:activateContractRequest" />
            <output message="tns:activateContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="setContractTermination">
            <documentation>
                It sets the investment contract to indicate that it is to be terminated after the settlement of all securities..

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : Some input parameter was not entered or has invalid value.
                CONTRACT_NOT_FOUND : The Investment contract was not found or is not in appropriate state.
            </documentation>
            <input message="tns:setContractTerminationRequest" />
            <output message="tns:setContractTerminationResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="terminateContract">
            <documentation>
                Terminates an active investment contract.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER : All input parameters must be entered.
                CONTRACT_NOT_FOUND : The Investment contract was not found or is not in appropriate state.
            </documentation>
            <input message="tns:terminateContractRequest" />
            <output message="tns:terminateContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="canTerminateInvestmentAccount">
            <documentation>
                It is possible to terminate an investment account?

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                ACCOUNT_NOT_FOUND / accountNumber / : The account was not found.
            </documentation>
            <input message="tns:canTerminateInvestmentAccountRequest" />
            <output message="tns:canTerminateInvestmentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="terminateInvestmentAccount">
            <documentation>
                Terminate an investment account

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                ACCOUNT_NOT_FOUND / accountNumber / : The account was not found.
                NOT_TERMINABLE / accountNumber / : The account can't be terminated.
            </documentation>
            <input message="tns:terminateInvestmentAccountRequest" />
            <output message="tns:terminateInvestmentAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="createMeetingRecord">
            <documentation>
                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createMeetingRecordRequest" />
            <output message="tns:createMeetingRecordResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
    </portType>

    <binding name="investmentSOAP" type="tns:investment">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="getInvestmentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="activateInvestmentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="cancelInvestmentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="createContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="getContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="hasContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="cancelContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="activateContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="setContractTermination">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="terminateContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="canTerminateInvestmentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="terminateInvestmentAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="createMeetingRecord">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="investmentWS">
        <port binding="tns:investmentSOAP" name="investmentSOAP">
            <soap:address location="/ws/investmentWS" />
        </port>
    </service>
</definitions>
