<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsInsuranceFeeWS/"
            xmlns="http://arbes.com/ib/core/ppf/ws/obsInsuranceFeeWS/"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
    <xsd:simpleType name="PaymentType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="P"/>
            <xsd:enumeration value="R"/>
            <xsd:enumeration value="M"/>
            <xsd:enumeration value="C"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="TravelInsPaymentType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="P">
                <xsd:annotation>
                    <xsd:documentation>
                        P (payment) - úhrada pojištění z běžného účtu klienta v rámci uzavření pojistné smlouvy
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="E">
                <xsd:annotation>
                    <xsd:documentation>
                        E - (elongation) - prodloužení již probíhajícího pojištění - zvýšení částky pojištění - dodatek, nové č.poj. Sml, nové poj
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="R">
                <xsd:annotation>
                    <xsd:documentation>
                        R - (refund) vratka pojištění - zrušení cesty, odstoupení od pojištění klientem
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S">
                <xsd:annotation>
                    <xsd:documentation>
                        S - (shortening) zkrácení již probíhajícího pojištění - snížení částky pojištění
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C">
                <xsd:annotation>
                    <xsd:documentation>
                        C - (cancel) - storno - vratka původně uzavřeného pojištění v budoucnu (zrušení původní pojistné smlouvy)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A">
                <xsd:annotation>
                    <xsd:documentation>
                        A - (adjustment) - úhrada pojištění z BU klienta v rámci uzavření nové (korigované) pojistné smlouvy
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="InsurancePPIRowItem">
        <xsd:sequence>
            <xsd:element name="InsuranceContractId" type="xsd:long" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        Externi ID kontraktu pojisteni v systemu INS
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="InsurancePeriod" type="xsd:gYearMonth" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        obdobi pro pojistne ve formatu RRRR-MM
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="CurrencyCode" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        kod meny - pokud neprijde, uctuje se v CZK
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AmountBrutto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        castka pojistneho brutto, ktere se bude
                        uctovat klientovi
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AmountNetto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        castka pojistneho netto, ktera nalezi
                        Cardiffu
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="TransID" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        identifikator transakce pojistneho, zatim se
                        ma skladat z: CisloRS a periody RRRRMM (bez mezer)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ClientText" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Text transakce pojisteni
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="PaymentType" type="PaymentType">
                <xsd:annotation>
                    <xsd:documentation>
                        Typ transakce pojisteni, P-Payment, R-Refund, M-Mandatory, C-Close
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="InsuranceNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        Cislo pojisteni
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="InsuranceRowItem">
        <xsd:sequence>
            <xsd:element name="InsuranceContractId" type="xsd:long" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        Externi ID kontraktu pojisteni v systemu INS
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ContractNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        cislo ramcove smlouvy
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="InsurancePeriod" type="xsd:gYearMonth" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        obdobi pro pojistne ve formatu RRRR-MM
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="CurrencyCode" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        kod meny - pokud neprijde, uctuje se v CZK
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AmountBrutto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        castka pojistneho brutto, ktere se bude
                        uctovat klientovi
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AmountNetto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        castka pojistneho netto, ktera nalezi
                        Cardiffu
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="TransID" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        identifikator transakce pojistneho, zatim se
                        ma skladat z: CisloRS a periody RRRRMM (bez mezer)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ClientText" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Text transakce pojisteni
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="PaymentType" type="PaymentType">
                <xsd:annotation>
                    <xsd:documentation>
                        Typ transakce pojisteni, P-Payment, R-Refund, M-Mandatory, C-Close
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="InsuranceNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        Cislo pojisteni
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>



    <xsd:element name="SetInsuranceFeesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="InsuranceRowItem" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="ContractNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        cislo ramcove smlouvy
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="InsurancePeriod" type="xsd:gYearMonth" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        obdobi pro pojistne ve formatu RRRR-MM
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="CurrencyCode" type="xsd:string" maxOccurs="1" minOccurs="0">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        kod meny - pokud neprijde, uctuje se v CZK
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="AmountBrutto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        castka pojistneho brutto, ktere se bude
                                        uctovat klientovi
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="AmountNetto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        castka pojistneho netto, ktera nalezi
                                        Cardiffu
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="TransID" type="xsd:string" minOccurs="1" maxOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        identifikator transakce pojistneho, zatim se
                                        ma skladat z: CisloRS a periody RRRRMM (bez mezer)
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="ClientText" type="xsd:string">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Text transakce pojisteni
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="PaymentType" type="PaymentType">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Typ transakce pojisteni, P-Payment, R-Refund, M-Mandatory, C-Close
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="InsuranceContractId" type="xsd:long" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Externi ID kontraktu pojisteni v systemu INS
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="InsuranceNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Cislo pojisteni
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetTravelInsuranceFeesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="TravelInsuranceFee" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="ContractNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        cislo ramcove smlouvy
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="idAccount" type="xsd:long" minOccurs="0" maxOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Cislo uctu ze ktereho maji byt penize zuctovany, pokud bude prazdne,
                                        pouzije se primarni ucet klienta
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="CurrencyCode" type="xsd:string" maxOccurs="1" minOccurs="0">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        kod meny - pokud neprijde, uctuje se v CZK
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="AmountBrutto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        castka pojistneho brutto, ktere se bude
                                        uctovat klientovi
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="AmountNetto" type="xsd:decimal" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        castka pojistneho netto, ktera nalezi Maxime
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="TravelStartDate" type="xsd:date" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Datum platnosti pojisteni od (zacatek cesty) vcetne
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="TravelEndDate" type="xsd:date" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Datum platnosti pojisteni do (konec cesty cesty) vcetne
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>

                            <xsd:element name="ClientText" type="xsd:string">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Text transakce pojisteni
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="PaymentType" type="TravelInsPaymentType">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        P - (payment), E - (elongation),R - (refund), S - (shortening),
                                        C - (cancel), A - (adjustment)
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="InsuranceContractId" type="xsd:long" maxOccurs="1" minOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Externi ID kontraktu pojisteni v systemu INS
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                            <xsd:element name="InsuranceNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                                <xsd:annotation>
                                    <xsd:documentation>
                                        Cislo pojisteni ve formatu CP10000000...
                                    </xsd:documentation>
                                </xsd:annotation>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetTravelInsuranceFeesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="TravelInsuranceFee" maxOccurs="unbounded" type="TravelInsuranceFeeResponse"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetInsuranceFeesResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="CancelInsuranceFeesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="CancelDate" type="xsd:date" maxOccurs="1" minOccurs="1"></xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="CancelInsuranceFeesResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="TravelInsuranceFeeResponse">
        <xsd:sequence>
            <xsd:element name="InsuranceContractId" type="xsd:long" maxOccurs="1" minOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        ID cestovniho pojisteni
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="transID" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        Cislo vytvorene platby - v pripade uspesneho vytvoreni
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="errorCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        Chybovy kod pri vytvoreni platby
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:element name="SetPaymentProtectionInsuranceFeesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="InsurancePPIRowItems" type="InsurancePPIRowItem" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="SetPaymentProtectionInsuranceFeesResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="SetPersonalItemProtectionFeesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="InsurancePIPRowItems" type="InsuranceRowItem" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="SetPersonalItemProtectionFeesResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
