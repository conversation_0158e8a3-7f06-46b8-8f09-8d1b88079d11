<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://airbank.cz/obs/ws/debtNotificationWS/">

    <xsd:element name="invokeDebtNotificationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contractNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="invokeDebtNotificationResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>

