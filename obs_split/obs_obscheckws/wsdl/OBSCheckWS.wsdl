<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" 
xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsCheckWS/" 
xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" 
xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
name="obsCheckWS"
targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCheckWS/"
xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">
	
	<wsdl:types>
		<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCheckWS/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
			<xsd:include schemaLocation="OBSCheckWS.xsd"></xsd:include>
			<xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
		</xsd:schema>
	</wsdl:types>
	
  <wsdl:message name="checkOBSStatusRequest">
    <wsdl:part element="tns:checkOBSStatusRequest"
    	name="parameters" />
  </wsdl:message>
  <wsdl:message name="checkOBSStatusResponse">
    <wsdl:part element="tns:checkOBSStatusResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="checkOBSStatusFault">
  	<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="obsCheckStatusWS">
    <wsdl:operation name="checkOBSStatus">
      <wsdl:documentation>Metoda zjistujici, zda je OBS v bezicim stavu

      </wsdl:documentation>
      <wsdl:input message="tns:checkOBSStatusRequest"/>
      <wsdl:output message="tns:checkOBSStatusResponse"/>
            <wsdl:fault name="fault" message="tns:checkOBSStatusFault"></wsdl:fault>
        </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="OBSCheckStatusWSSOAP" type="tns:obsCheckStatusWS">
  	<soap:binding style="document"
  		transport="http://schemas.xmlsoap.org/soap/http" />
  	<wsdl:operation name="checkOBSStatus">
  		<soap:operation soapAction="" />
  		<wsdl:input>
  			<soap:body use="literal" />
  		</wsdl:input>
  		<wsdl:output>
  			<soap:body use="literal" />
  		</wsdl:output>
  		<wsdl:fault name="fault">
        <soap:fault use="literal" name="fault" />
      </wsdl:fault>
  	</wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="OBSCheckStatusWS">
    <wsdl:port binding="tns:OBSCheckStatusWSSOAP" name="obsCheckStatusWSSOAP">
      <soap:address location="http://airbank.cz/ib/core/ppf/ws/obsCheckWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
