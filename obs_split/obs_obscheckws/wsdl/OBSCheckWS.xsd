<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCheckWS/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
		
	<xsd:element name="checkOBSStatusRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>Tato metoda vyžaduje CUID pro kontrolu, zda uživatel
							nebyl v minulosti klientem.
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="URL" type="xsd:string" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>
							URL
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="VS" type="xsd:string" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>
							VS
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="ss" type="xsd:string" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>
							SS
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="checkOBSStatusResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="retVal" type="xsd:string">
					<xsd:annotation>
						<xsd:documentation>
							returned request attributes
						</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
