<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsLoxonWebOfficeWS/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/" name="obsLoxonWebOfficeWS" targetNamespace="http://arbes.com/ib/core/ppf/ws/obsLoxonWebOfficeWS/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsLoxonWebOfficeWS/">
      <xsd:import schemaLocation="../xsd/Common.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
      <xsd:element name="getTokenForDisplayPerson">
        <xsd:complexType>
          <xsd:sequence>
          	<xsd:element name="idOperator" type="xsd:string"
          		minOccurs="1" maxOccurs="1">
          		<xsd:annotation>
          			<xsd:documentation>
          				operator identifier
          			</xsd:documentation>
          		</xsd:annotation>
          	</xsd:element>
          	<xsd:element name="cuid" type="xsd:long" minOccurs="1"
          		maxOccurs="1">
          		<xsd:annotation>
          			<xsd:documentation>
          				identification of customer
          			</xsd:documentation>
          		</xsd:annotation>
          	</xsd:element>
            <xsd:element name="callId" type="xsd:string" minOccurs="0" maxOccurs="1" >
              <xsd:annotation>
                <xsd:documentation>identification of phone call</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="getTokenForDisplayPersonResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="token" type="xsd:string" minOccurs="1" maxOccurs="1">
            	<xsd:annotation>
            		<xsd:documentation>token for displaying current person in WebOffice</xsd:documentation>
            	</xsd:annotation></xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:schema>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
     	<xsd:import
     		namespace="http://arbes.com/ib/core/ppf/ws/common/"
     		schemaLocation="../xsd/Common.xsd">
     </xsd:import></xsd:schema>
  </wsdl:types>
  <wsdl:message name="getTokenForDisplayPersonRequest">
    <wsdl:part element="tns:getTokenForDisplayPerson" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="getTokenForDisplayPersonResponse">
    <wsdl:part element="tns:getTokenForDisplayPersonResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="getTokenForDisplayPersonFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="obsLoxonWebOfficeWS">
    <wsdl:operation name="getTokenForDisplayPerson">
      <wsdl:documentation>get token for displaying specific person in WebOffice

(SOAPFault): 
code / atribute / value : description
CLERR_ACCESS / GENERAL_ERROR / : operator does not exist or is not logged
CLERR_NO_DATA_FOUND / cuid / : person with specific cuid not found
GENERAL_ERROR /GENERAL_ERROR / : general system error
CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time</wsdl:documentation>
      <wsdl:input message="tns:getTokenForDisplayPersonRequest"/>
      <wsdl:output message="tns:getTokenForDisplayPersonResponse"/>
            <wsdl:fault name="fault" message="tns:getTokenForDisplayPersonFault"></wsdl:fault>
        </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="obsLoxonWebOfficeSOAP" type="tns:obsLoxonWebOfficeWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getTokenForDisplayPerson">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="obsLoxonWebOfficeWS">
    <wsdl:port binding="tns:obsLoxonWebOfficeSOAP" name="obsLoxonWebOfficeWSSOAP">
      <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsLoxonWebOfficeWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
