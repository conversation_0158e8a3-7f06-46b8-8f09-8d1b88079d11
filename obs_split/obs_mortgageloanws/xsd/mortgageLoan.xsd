<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:lo="http://airbank.cz/obs/ws/loan"
        xmlns:tns="http://airbank.cz/obs/ws/mortgageLoan"
        targetNamespace="http://airbank.cz/obs/ws/mortgageLoan">

    <import namespace="http://airbank.cz/obs/ws/loan" schemaLocation="LoanTO.xsd"/>

    <simpleType name="dayInMonthTO">
        <restriction base="integer">
            <minInclusive value="1"/>
            <maxInclusive value="31"/>
        </restriction>
    </simpleType>

    <complexType name="basicParams">
        <sequence>
            <element name="minMortgageAmount" type="decimal"/>
            <element name="maxMortgageAmount" type="decimal">
                <annotation>
                    <documentation>Maximalni vyse hypoteky o kterou si muze klient zazadat</documentation>
                </annotation>
            </element>
            <element name="maxMortgageAmountOperator" type="decimal">
                <annotation>
                    <documentation>Maximalni vyse hypoteky kterou lze poskytnout</documentation>
                </annotation>
            </element>
            <element name="minCollateralAmount" type="decimal"/>
            <element name="maxCollateralAmount" type="decimal"/>
            <element name="minRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Minimalni delka splaceni v letech</documentation>
                </annotation>
            </element>
            <element name="maxRepaymentPeriod" type="int">
                <annotation>
                    <documentation>Maximalni delka splaceni v letech</documentation>
                </annotation>
            </element>
        </sequence>
        <attribute name="mortgageType" type="tns:MortgageType" use="required"/>
    </complexType>

    <complexType name="scoringTO">
        <sequence>
            <element name="riskGrade" type="string" minOccurs="0">
                <annotation>
                    <documentation>Client risk grade from scoring process.</documentation>
                </annotation>
            </element>
            <element name="scoringDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Referenční datum, ke kterému se vybírá úroková sazba, marže nebo cap-přirážka a v některých případech i PRIBOR sazba (pro
                        výpočet capInterestRate).
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="clientTO">
        <sequence>
            <element name="segment" type="lo:SegmentType"/>
            <element name="subsegment" type="lo:Subsegment" minOccurs="0"/>
            <element name="clientType" type="lo:ClientType"/>
            <element name="campaignCode" type="string" minOccurs="0"/>
            <element name="cuid" type="long" minOccurs="0">
                <annotation>
                    <documentation>Client cuid - for birth date</documentation>
                </annotation>
            </element>
            <element name="scoring" type="tns:scoringTO"/>
        </sequence>
    </complexType>

    <simpleType name="interestRateTypeTO">
        <annotation>
            <documentation>Interest rate type (FIX / FLOAT)</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="FIX">
                <annotation>
                    <documentation>Fix interest rate.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="FLOAT">
                <annotation>
                    <documentation>Float interest rate.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="requestedMortgageLoanParamsTO">
        <sequence>
            <element name="mortgageType" type="tns:MortgageType">
                <annotation>
                    <documentation>Requested mortgage loan type.
                        HYREF or HYNEW
                    </documentation>
                </annotation>
            </element>
            <element name="fixationPeriodLength" type="lo:fixationPeriodTO">
                <annotation>
                    <documentation>Requested mortagage loan fixation period length.</documentation>
                </annotation>
            </element>
            <element name="mortgageLoanAmount" type="decimal">
                <annotation>
                    <documentation>Requested mortgage loan amount.</documentation>
                </annotation>
            </element>
            <element name="repaymentPeriod" type="decimal">
                <annotation>
                    <documentation>Requested mortgage loan repayment period length.
                        Calculate offers for all possible repayment period lengths
                    </documentation>
                </annotation>
            </element>
            <element name="instalmentDay" type="tns:dayInMonthTO" minOccurs="0">
                <annotation>
                    <documentation>Day in month for regular instalment.</documentation>
                </annotation>
            </element>
            <element name="utilizationDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Requested mortgage loan utilization date.</documentation>
                </annotation>
            </element>
            <element name="paymentProtectionInsurance" type="boolean">
                <annotation>
                    <documentation>Mortgage is insured</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="offeredCollateralTO">
        <sequence>
            <element name="value" type="decimal">
                <annotation>
                    <documentation>Value of Collateral</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="refMortgageLoanTO">
        <sequence>
            <element name="refixationDate" type="date">
                <annotation>
                    <documentation>Mortgage loan refixation date.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="productVariantTO">
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>Product variant identification.</documentation>
                </annotation>
            </element>
            <element name="code" type="string">
                <annotation>
                    <documentation>Product variant code.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="loanScheduleOffer">
        <sequence>
            <element name="interestRate" type="decimal">
                <annotation>
                    <documentation>Offered interest rate.</documentation>
                </annotation>
            </element>
            <choice>
                <element name="APR" type="decimal">
                    <annotation>
                        <documentation>Annual percentage rate of charge</documentation>
                    </annotation>
                </element>
                <element name="marketingInterestRate" type="decimal">
                    <annotation>
                        <documentation>Marketing interest rate for payment schedule with OTB included.</documentation>
                    </annotation>
                </element>
            </choice>
            <element name="numberOfInstalments" type="int">
                <annotation>
                    <documentation>Number of instalments.</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDate" type="date">
                <annotation>
                    <documentation>Last instalment date.</documentation>
                </annotation>
            </element>
            <element name="totalAmount" type="decimal">
                <annotation>
                    <documentation>Total amount repaid for this payment schedule.</documentation>
                </annotation>
            </element>
            <element name="mortgageCashbackValue" type="decimal">
                <annotation>
                    <documentation>Cashback amount for the first instalment of HYPO.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="mortgageLoanOfferTO">
        <sequence>
            <element name="productVariant" type="tns:productVariantTO">
                <annotation>
                    <documentation>Offered mortgage loan product variant.</documentation>
                </annotation>
            </element>
            <element name="utilizationDate" type="dateTime">
                <annotation>
                    <documentation>Offered mortgage loan utilization date.</documentation>
                </annotation>
            </element>
            <element name="firstInstalmentDate" type="date">
                <annotation>
                    <documentation>Offered mortgage loan first instalment date.</documentation>
                </annotation>
            </element>
            <element name="minRepaymentPeriod" type="integer">
                <annotation>
                    <documentation>Minimum repayment period in years.</documentation>
                </annotation>
            </element>
            <element name="maxRepaymentPeriod" type="integer">
                <annotation>
                    <documentation>Maximum repayment period in years.</documentation>
                </annotation>
            </element>
            <element name="minCollateralParams" type="tns:offeredCollateralTO">
                <annotation>
                    <documentation>Minimum collateral parameters required for offered mortgage loan.</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal">
                <annotation>
                    <documentation>Instalment amount for this payment schedule.</documentation>
                </annotation>
            </element>
            <element name="standardSchedule" type="tns:loanScheduleOffer">
                <annotation>
                    <documentation>Standard schedule offer without extra payment to OTB.</documentation>
                </annotation>
            </element>
            <element name="scheduleWithOTB" type="tns:loanScheduleOffer" minOccurs="0">
                <annotation>
                    <documentation>Schedule offer with extra payment to OTB.</documentation>
                </annotation>
            </element>
            <element name="insuranceInterestRateDivergence" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Divergence of interest rate for insurance.</documentation>
                </annotation>
            </element>
            <element name="otbUsageInterestRateDivergence" type="decimal">
                <annotation>
                    <documentation>Divergence of interest rate for non-zero balance in OTB.</documentation>
                </annotation>
            </element>
            <element name="paymentProtectionPremium" type="decimal">
                <annotation>
                    <documentation>PPI fee amount.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="globalParametersTO">
        <sequence>
            <element name="maxLtv" type="decimal">
                <annotation>
                    <documentation>Parametr LTV, který nesmí být překročen pro poměr Výše hypotéky / Hodnota nemovitosti.</documentation>
                </annotation>
            </element>
            <element name="maxLtvWithoutPenalty" type="decimal">
                <annotation>
                    <documentation>Maximalni hodnota LTV pro kterou jeste neni penalizace urokove sazby.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="getMortgageLoanParamsErrorTO">
        <restriction base="string">
            <enumeration value="CAMPAIGN_CODE_NOT_FOUND">
                <annotation>
                    <documentation>
                        campaignCode not found. There is no product variant with
                        specified campaign code.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="AMOUNT_MIN">
                <annotation>
                    <documentation>
                        The mortgage loan amount is too low. There is no product
                        variant for the requested mortage loan amount.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="AMOUNT_MAX">
                <annotation>
                    <documentation>
                        The mortgage loan amount is too high. There is no product
                        variant for the requested mortage loan amount.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="LTV">
                <annotation>
                    <documentation>
                        The mortgage loan amount is too high. The collateral
                        value is too low for the requested mortage loan amount.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="REFIX_DATE_PAST">
                <annotation>
                    <documentation>
                        Refixation date is in the past.
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERM_OUT_OF_RANGE"/>
        </restriction>
    </simpleType>

    <complexType name="ExtraTransferTO">
        <sequence>
            <element name="interestRate" type="decimal"/>
            <element name="mortgageLoanAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>mortgage loan amount</documentation>
                </annotation>
            </element>
            <element name="amountToRepay" type="decimal">
                <annotation>
                    <documentation>amount to repay in current time</documentation>
                </annotation>
            </element>
            <element name="openToBuyAmount" type="decimal">
                <annotation>
                    <documentation>balance of open to buy account</documentation>
                </annotation>
            </element>
            <element name="nextInstalmentDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>date of next instalment</documentation>
                </annotation>
            </element>
            <element name="nextInstalmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>amount of next regular instalment</documentation>
                </annotation>
            </element>
            <element name="savingAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>amount of deposits to reserve</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>date of last instalment when regular instalment</documentation>
                </annotation>
            </element>
            <element name="nextMinInstalmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>next minimal instalment</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDateMin" type="date" minOccurs="0">
                <annotation>
                    <documentation>date of last instalment when minimal instalment</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>unpaid principal</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MrtgChangeParamsTO">
        <sequence>
            <element name="instalmentDay" type="tns:dayInMonthTO" minOccurs="0">
                <annotation>
                    <documentation>instalment day: it is filled in when the process "change of instalment day"</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>instalment amount: it is filled in when the process "change of instalment amount"</documentation>
                </annotation>
            </element>
            <element name="savingAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>amount of deposits to reserve: it is filled in when the process "change of amount of deposits to reserve""
                    </documentation>
                </annotation>
            </element>
            <element name="managerType" type="lo:ManagerType" minOccurs="0">
                <annotation>
                    <documentation>a flag indicating whether the change is processed by manager Collections</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ResultMrtgChangeParamsTO">
        <sequence>
            <element name="instalmentDay" type="tns:dayInMonthTO">
                <annotation>
                    <documentation>day in month for regular instalment</documentation>
                </annotation>
            </element>
            <element name="instalmentAmount" type="decimal">
                <annotation>
                    <documentation>amount of next instalment</documentation>
                </annotation>
            </element>
            <element name="savingAmount" type="decimal">
                <annotation>
                    <documentation>amount of deposits to reserve</documentation>
                </annotation>
            </element>
            <element name="numberOfFutureInstalments" type="int">
                <annotation>
                    <documentation>number of unpaid monthly installments</documentation>
                </annotation>
            </element>
            <element name="nextInstalmentDate" type="date">
                <annotation>
                    <documentation>date of next instalment</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDate" type="date">
                <annotation>
                    <documentation>date of last instalment</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="ExtraTransferType">
        <restriction base="string">
            <enumeration value="EXTRA_INSTALMENT">
                <annotation>
                    <documentation>extra instalment - transfer from client account into open to buy account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ADDITIONAL_DISBURSEMENT">
                <annotation>
                    <documentation>additional disbursement - transfer from open to buy account into client account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTB_TO_PRINCIPAL">
                <annotation>
                    <documentation>transfer from open to buy account into unpaid principal</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="PaymentScheduleEventType">
        <restriction base="string">
            <enumeration value="UTILIZATION">
                <annotation>
                    <documentation>Utilization</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REG_INSTALMENT">
                <annotation>
                    <documentation>Regular instalment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REG_PREPAY">
                <annotation>
                    <documentation>Regular payment to OTB account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EXTRA_PREPAY">
                <annotation>
                    <documentation>Extraordinary payment to OTB account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PREPAY_TO_PRC">
                <annotation>
                    <documentation>Payment from OTB account to loan principal</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ADDITIONAL_DISBURSEMENT">
                <annotation>
                    <documentation>Additional disbursement from OTB account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NEXT_INSTALMENT">
                <annotation>
                    <documentation>Next regular instalment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NEXT_PREPAY">
                <annotation>
                    <documentation>Next regular payment to OTB account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENALTY_RECALC">
                <annotation>
                    <documentation>Penalty interest recalculation</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENALTY_RETURN">
                <annotation>
                    <documentation>Return of penalty interest</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENINT_RECALC">
                <annotation>
                    <documentation>Pending loan interest recalculation</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENINT_RETURN">
                <annotation>
                    <documentation>Return of previously paid pending interest</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PENINT_PAY">
                <annotation>
                    <documentation>Pending interest - full repayment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BUSINT_PAY">
                <annotation>
                    <documentation>Business interest - full repayment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REDEMPTION">
                <annotation>
                    <documentation>Redemption</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REG_INSURANCE">
                <annotation>
                    <documentation>Regular insurance premium</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NEXT_INSURANCE">
                <annotation>
                    <documentation>Next regular insurance premium</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="MrtgPaySchedEventTO">
        <sequence>
            <element name="idPaymentScheduleEvent" type="string">
                <annotation>
                    <documentation>id of payment schedule event</documentation>
                </annotation>
            </element>
            <element name="type" type="tns:PaymentScheduleEventType">
                <annotation>
                    <documentation>type of event</documentation>
                </annotation>
            </element>
            <element name="requiredDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>required date</documentation>
                </annotation>
            </element>
            <element name="realizedDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>realized date</documentation>
                </annotation>
            </element>
            <element name="requiredAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>required amount</documentation>
                </annotation>
            </element>
            <element name="realizedAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>realized amount</documentation>
                </annotation>
            </element>
            <element name="paidPrincipal" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>paid principal</documentation>
                </annotation>
            </element>
            <element name="paidInterest" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>paid principal</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>unpaid principal</documentation>
                </annotation>
            </element>
            <element name="amountToRepay" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>amount to repay</documentation>
                </annotation>
            </element>
            <element name="overdue" type="boolean">
                <annotation>
                    <documentation>true when instalment is overdue</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="PaymentScheduleAccType">
        <restriction base="string">
            <enumeration value="CURRENT_ACCOUNT">
                <annotation>
                    <documentation>current account of client</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTB_ACCOUNT">
                <annotation>
                    <documentation>OTB account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INNER_ACCOUNT">
                <annotation>
                    <documentation>inner bank account</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INSTALLMENT_ACCOUNT">
                <annotation>
                    <documentation>installment account</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="MrtgPaySchedTrnTO">
        <sequence>
            <element name="accountType" type="tns:PaymentScheduleAccType" minOccurs="0">
                <annotation>
                    <documentation>type of transaction</documentation>
                </annotation>
            </element>
            <element name="realizedDate" type="date">
                <annotation>
                    <documentation>realized date</documentation>
                </annotation>
            </element>
            <element name="realizedAmount" type="decimal">
                <annotation>
                    <documentation>realized amount</documentation>
                </annotation>
            </element>
            <element name="paidPrincipal" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>paid principal</documentation>
                </annotation>
            </element>
            <element name="paidInterest" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>paid principal</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>unpaid principal</documentation>
                </annotation>
            </element>
            <element name="amountToRepay" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>amount to repay</documentation>
                </annotation>
            </element>
            <element name="partialPayment" type="boolean">
                <annotation>
                    <documentation>true when transaction is partial payment of instalment</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MrtgInterestRateHistTO">
        <sequence>
            <element name="interestRate" type="decimal">
                <annotation>
                    <documentation>Úroková sazba</documentation>
                </annotation>
            </element>
            <element name="interestRateCap" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Maximální úroková sazba</documentation>
                </annotation>
            </element>
            <element name="validity" type="date">
                <annotation>
                    <documentation>Datum počátku platnosti sazby</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="MrtgObligationType">
        <restriction base="string">
            <enumeration value="CONSTRUCTION_SAVINGS_LOAN">
                <annotation>
                    <documentation>construction savings loan</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MORTGAGE_LOAN">
                <annotation>
                    <documentation>mortgage loan</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="MrtgObligationTO">
        <sequence>
            <element name="idObligation" type="long">
                <annotation>
                    <documentation>obligation id from application</documentation>
                </annotation>
            </element>
            <element name="idObligationBrki" type="string" minOccurs="0">
                <annotation>
                    <documentation>loan id in BRKI</documentation>
                </annotation>
            </element>
            <element name="financialInstitution" type="string">
                <annotation>
                    <documentation>code of financial institution that provide loan</documentation>
                </annotation>
            </element>
            <element name="obligationType" type="tns:MrtgObligationType">
                <annotation>
                    <documentation>type of obligation (CONSTRUCTION_SAVINGS_LOAN / MORTGAGE_LOAN)</documentation>
                </annotation>
            </element>
            <element name="originalDocumentNumber" type="string" minOccurs="0">
                <annotation>
                    <documentation>Číslo původní úvěrové smlouvy</documentation>
                </annotation>
            </element>
            <element name="originalDocumentDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Datum uzavření původní úvěrové smlouvy</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="CancelApplicationType">
        <restriction base="string">
            <enumeration value="CLIENT_CANCEL"/>
            <enumeration value="FI_CANCEL"/>
            <enumeration value="REJECT"/>
        </restriction>
    </simpleType>

    <simpleType name="CancelApplicationFinalStatus">
        <restriction base="string">
            <enumeration value="REJECTED"/>
            <enumeration value="CANCELLED"/>
        </restriction>
    </simpleType>

    <complexType name="CancelApplicationResult">
        <sequence>
            <element name="contractId" type="long">
                <annotation>
                    <documentation>identifier of contract</documentation>
                </annotation>
            </element>
            <element name="finalStatus" type="tns:CancelApplicationFinalStatus">
                <annotation>
                    <documentation>final status</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MortgageApplication">
        <annotation>
            <documentation>The mortgage application.</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>The application identification.</documentation>
                </annotation>
            </element>
            <element name="approvalDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>The mortgage approval date.</documentation>
                </annotation>
            </element>
            <element name="approvedInstalmentAmount" type="decimal">
                <annotation>
                    <documentation>The approved amount of the regular instalment.</documentation>
                </annotation>
            </element>
            <element name="approvedAmount" type="decimal">
                <annotation>
                    <documentation>The approved mortgage amount.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="GeneralContractStatus">
        <annotation>
            <documentation>General contract states.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ACTIVE">
                <annotation>
                    <documentation>General contracts active or in proposal.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="INACTIVE">
                <annotation>
                    <documentation>Inactive general contracts.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ALL">
                <annotation>
                    <documentation>The status of the general contracts does not matter.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="MortgageType">
        <annotation>
            <documentation>Types of mortgages.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="HYNEW">
                <annotation>
                    <documentation>Standard mortgage.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYREF">
                <annotation>
                    <documentation>Refinanced mortgage.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="PersonRelationType">
        <annotation>
            <documentation>Types of person's relation to the mortgage.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="OWNER">
                <annotation>
                    <documentation>The person is the owner of the mortgage.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CODEBTOR">
                <annotation>
                    <documentation>The person is a co-debtor on the mortgage.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISPONENT">
                <annotation>
                    <documentation>The person can see this mortgage as a disponent.</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="MortgageStatus">
        <annotation>
            <documentation>Statuses of mortgages.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="NEW"/>
            <enumeration value="CANCELLED_BY_CLIENT"/>
            <enumeration value="CANCELLED_BY_BANK"/>
            <enumeration value="REJECTED"/>
            <enumeration value="PENDING"/>
            <enumeration value="DRAWING"/>
            <enumeration value="ACTIVE"/>
            <enumeration value="REPAID_STD"/>
            <enumeration value="REPAID_PREMATURELY"/>
            <enumeration value="REPAID_CONSOLIDATION"/>
            <enumeration value="WITHDRAWN_UNPAID"/>
            <enumeration value="WITHDRAWN_PAID"/>
            <enumeration value="CALLED_DUE_UNPAID"/>
            <enumeration value="CALLED_DUE_PAID"/>
            <enumeration value="WRITE_OFF_UNPAID"/>
            <enumeration value="WRITE_OFF_PAID"/>
            <enumeration value="TO_WRITTEN_OFF_UNPAID"/>
            <enumeration value="TO_WRITTEN_OFF_PAID"/>
        </restriction>
    </simpleType>

    <complexType name="CoDebtor">
        <annotation>
            <documentation>A mortgage co-debtor.</documentation>
        </annotation>
        <sequence>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>The co-debtor's identification.</documentation>
                </annotation>
            </element>
            <element name="firstName" type="string">
                <annotation>
                    <documentation>The co-debtor's first name.</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string">
                <annotation>
                    <documentation>The co-debtor's last name.</documentation>
                </annotation>
            </element>
            <element name="birthDate" type="date">
                <annotation>
                    <documentation>The co-debtor's birth date.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="RepaymentAccount">
        <annotation>
            <documentation>The account from which the mortgage is repaid.</documentation>
        </annotation>
        <sequence>
            <element name="idAccount" type="long">
                <annotation>
                    <documentation>Identification of the repayment account in the OBS.</documentation>
                </annotation>
            </element>
            <element name="displayAccount" type="boolean">
                <annotation>
                    <documentation>Flag whether to show the account to the client.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestFixation">
        <annotation>
            <documentation>The fixation of the mortgage interest rate.</documentation>
        </annotation>
        <sequence>
            <element name="periodLength" type="int">
                <annotation>
                    <documentation>The length of the fixation period in years.</documentation>
                </annotation>
            </element>
            <element name="refixationDate" type="date">
                <annotation>
                    <documentation>The interest rate fixation end date.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="InterestRate">
        <annotation>
            <documentation>The interest rate of the mortgage.</documentation>
        </annotation>
        <sequence>
            <element name="type" type="tns:interestRateTypeTO">
                <annotation>
                    <documentation>The interest rate type.</documentation>
                </annotation>
            </element>
            <element name="rate" type="decimal">
                <annotation>
                    <documentation>The current interest rate in percent.</documentation>
                </annotation>
            </element>
            <element name="cap" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>The guaranteed maximum interest rate.</documentation>
                </annotation>
            </element>
            <element name="pribor" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>The PRIBOR for mortgages with FLOAT interest rate type.</documentation>
                </annotation>
            </element>
            <element name="effectiveRate" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>The effective interest rate (APR) in percent.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MortgageDrawdown">
        <annotation>
            <documentation>The drawdown of the mortgage.</documentation>
        </annotation>
        <sequence>
            <element name="firstDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>The date of the first drawdown.</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>The amount drawn.</documentation>
                </annotation>
            </element>
            <element name="maxDate" type="date">
                <annotation>
                    <documentation>The date until which the mortgage can be taken out.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="Instalment">
        <annotation>
            <documentation>The mortgage instalment.</documentation>
        </annotation>
        <sequence>
            <element name="amount" type="decimal">
                <annotation>
                    <documentation>The amount of the instalment.</documentation>
                </annotation>
            </element>
            <element name="date" type="date">
                <annotation>
                    <documentation>The date of the instalment</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PaymentSchedule">
        <annotation>
            <documentation>The mortgage payment schedule.</documentation>
        </annotation>
        <sequence>
            <element name="regularInstalmentAmount" type="decimal">
                <annotation>
                    <documentation>The regular instalment amount.</documentation>
                </annotation>
            </element>
            <element name="regularInstalmentDay" type="int">
                <annotation>
                    <documentation>Day of the month for regular instalments.</documentation>
                </annotation>
            </element>
            <element name="nextInstalment" type="tns:Instalment" minOccurs="0">
                <annotation>
                    <documentation>The next instalment according to the standard repayment schedule.</documentation>
                </annotation>
            </element>
            <element name="firstInstalmentDate" type="date">
                <annotation>
                    <documentation>The date of the first instalment.</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentDate" type="date">
                <annotation>
                    <documentation>The date of the last instalment. Including the regular instalment to the reserve.</documentation>
                </annotation>
            </element>
            <element name="lastInstalmentChangeDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>The date of last instalment change.</documentation>
                </annotation>
            </element>
            <element name="numberOfFutureInstalments" type="int">
                <annotation>
                    <documentation>The number of remaining instalments.</documentation>
                </annotation>
            </element>
            <element name="totalNumberOfInstalments" type="int">
                <annotation>
                    <documentation>The total number of instalments.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PaymentData">
        <annotation>
            <documentation>Details of the financial status of the mortgage.</documentation>
        </annotation>
        <sequence>
            <element name="totalAmount" type="decimal">
                <annotation>
                    <documentation>The total mortgage amount.</documentation>
                </annotation>
            </element>
            <element name="paidPrincipal" type="decimal">
                <annotation>
                    <documentation>The paid principal.</documentation>
                </annotation>
            </element>
            <element name="paidInterest" type="decimal">
                <annotation>
                    <documentation>The paid interest.</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal">
                <annotation>
                    <documentation>The unpaid principal.</documentation>
                </annotation>
            </element>
            <element name="numberOfPaidRegularInstalments" type="int">
                <annotation>
                    <documentation>The number of fully paid regular instalments.</documentation>
                </annotation>
            </element>
            <element name="minAnnuityAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>The minimum instalment amount.</documentation>
                </annotation>
            </element>
            <element name="minAnnuityCount" type="int" minOccurs="0">
                <annotation>
                    <documentation>The number of instalments at the minimum instalment.</documentation>
                </annotation>
            </element>
            <element name="openToBuyAmount" type="decimal">
                <annotation>
                    <documentation>The paid into the reserve.</documentation>
                </annotation>
            </element>
            <element name="regularPrepay" type="decimal">
                <annotation>
                    <documentation>The regular prepay from savings.</documentation>
                </annotation>
            </element>
            <element name="inDebt" type="boolean">
                <annotation>
                    <documentation>If true, there are debts on the loan.</documentation>
                </annotation>
            </element>
            <element name="amountToRepay" type="decimal">
                <annotation>
                    <documentation>The amount needed to pay off the mortgage.</documentation>
                </annotation>
            </element>
            <element name="principalOverdue" type="decimal">
                <annotation>
                    <documentation>The principal overdue.</documentation>
                </annotation>
            </element>
            <element name="totalDebt" type="decimal">
                <annotation>
                    <documentation>The total debt.</documentation>
                </annotation>
            </element>
            <element name="upsell" type="decimal">
                <annotation>
                    <documentation>The amount of up-sell.</documentation>
                </annotation>
            </element>
            <element name="currentDPD" type="int" minOccurs="0">
                <annotation>
                    <documentation>The current DPD with tolerance of the amount F.</documentation>
                </annotation>
            </element>
            <element name="currentDPDWithTolerance" type="int" minOccurs="0">
                <annotation>
                    <documentation>The current DPD with tolerance of the amount H.</documentation>
                </annotation>
            </element>
            <element name="currentInternalDPD" type="int" minOccurs="0">
                <annotation>
                    <documentation>The current DPD with tolerance of the amount K. It uses working days.</documentation>
                </annotation>
            </element>
            <element name="currentInternalDPDWithTolerance" type="int" minOccurs="0">
                <annotation>
                    <documentation>The current DPD with tolerance of the amount J. It uses working days.</documentation>
                </annotation>
            </element>
            <element name="originalCommitmentAmount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>The amount needed to repay the original commitment.</documentation>
                </annotation>
            </element>
            <element name="amountOfClientsFunds" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>The amount of the client's own funds used to repay the original commitment.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MortgageOverview">
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>The mortgage internal identification.</documentation>
                </annotation>
            </element>
            <element name="mortgageNumber" type="string">
                <annotation>
                    <documentation>The mortgage number.</documentation>
                </annotation>
            </element>
            <element name="type" type="tns:MortgageType">
                <annotation>
                    <documentation>The mortgage type.</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>The mortgage name.</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>The currency of the mortgage. The ISO alpha-3 code.</documentation>
                </annotation>
            </element>
            <element name="status" type="tns:MortgageStatus">
                <annotation>
                    <documentation>The mortgage status for RISK department.</documentation>
                </annotation>
            </element>
            <element name="unpaidPrincipal" type="decimal">
                <annotation>
                    <documentation>The unpaid principal.</documentation>
                </annotation>
            </element>
            <element name="openToBuyAmount" type="decimal">
                <annotation>
                    <documentation>The amount paid into the reserve.</documentation>
                </annotation>
            </element>
            <element name="inDebt" type="boolean">
                <annotation>
                    <documentation>TRUE - there is nonzero past due debt on the mrtg, FALSE otherwise</documentation>
                </annotation>
            </element>
            <element name="hasActivePaymentProtectionInsurance" type="boolean">
                <annotation>
                    <documentation>TRUE when there is a active payment protection insurance linked to the mortgage, FALSE otherwise</documentation>
                </annotation>
            </element>
            <element name="repaymentStartDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>The day the mortgage started the repayment phase of its lifecycle (the drawdown phase was finished)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MortgageDetail">
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>The mortgage OBS identification.</documentation>
                </annotation>
            </element>
            <element name="mortgageNumber" type="string">
                <annotation>
                    <documentation>The mortgage number.</documentation>
                </annotation>
            </element>
            <element name="type" type="tns:MortgageType">
                <annotation>
                    <documentation>The mortgage type.</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>The mortgage name.</documentation>
                </annotation>
            </element>
            <element name="status" type="tns:MortgageStatus">
                <annotation>
                    <documentation>The mortgage status for RISK department.</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>The currency of the mortgage. The ISO alpha-3 code.</documentation>
                </annotation>
            </element>
            <element name="personRelationType" type="tns:PersonRelationType">
                <annotation>
                    <documentation>Type of person's relation to the mortgage.</documentation>
                </annotation>
            </element>
            <element name="productVariant" type="string">
                <annotation>
                    <documentation>The product variant code.</documentation>
                </annotation>
            </element>
            <element name="closeDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Mortgage close date.</documentation>
                </annotation>
            </element>
            <element name="application" type="tns:MortgageApplication"/>
            <element name="coDebtor" type="tns:CoDebtor" minOccurs="0" maxOccurs="unbounded"/>
            <element name="repaymentAccount" type="tns:RepaymentAccount"/>
            <element name="interestFixation" type="tns:InterestFixation" minOccurs="0"/>
            <element name="interestRate" type="tns:InterestRate"/>
            <element name="drawdown" type="tns:MortgageDrawdown"/>
            <element name="paymentSchedule" type="tns:PaymentSchedule"/>
            <element name="paymentData" type="tns:PaymentData"/>
            <element name="paymentProtectionInsurance" type="lo:InsuranceTO" minOccurs="0"/>
        </sequence>
    </complexType>

</schema>
