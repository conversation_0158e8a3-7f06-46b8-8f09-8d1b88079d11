<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
  xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/" xmlns="http://www.w3.org/2001/XMLSchema"
  elementFormDefault="qualified" version="1.0">

  <complexType name="GeneralCustomer">
    <sequence>
      <element name="cuid" type="long">
        <annotation>
          <documentation>subject identification</documentation>
        </annotation>
      </element>
      <element name="legalSegment" type="string">
        <annotation>
          <documentation>
            Customer segment e.g. ENTREPRENEUR, CUSTOMER, ...  (MDM:LEGAL_SEGMENT)
          </documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>

</schema>