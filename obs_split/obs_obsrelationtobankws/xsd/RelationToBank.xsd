<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
  xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/" xmlns="http://www.w3.org/2001/XMLSchema"
  elementFormDefault="qualified" version="1.0"
  xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">

  <include schemaLocation="ContractTO.xsd"></include>
  <include schemaLocation="GeneralCustomer.xsd"></include>

  <complexType name="PersonType">
    <sequence>
      <element name="cuid" type="long"/>
    </sequence>
  </complexType>

  <complexType name="RelationToBank">
    <sequence>
      <element name="relationType" type="Q1:RelationType">
        <annotation>
          <documentation>type of relation to bank</documentation>
        </annotation>
      </element>
      <element name="contractId" type="long">
        <annotation>
          <documentation>identifier of contract</documentation>
        </annotation>
      </element>
      <element name="activationDate" type="date" minOccurs="0">
        <annotation>
          <documentation>datum založení rámcové smlouvy</documentation>
        </annotation>
      </element>
      <element name="ownerGeneralCustomer" type="Q1:GeneralCustomer">
        <annotation>
          <documentation>contract owner</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>

  <complexType name="PersonRelationType">
    <sequence>
      <element name="cuid" type="long">
        <annotation>
          <documentation>user identification</documentation>
        </annotation>
      </element>
      <element name="relation" type="Q1:RelationToBank" minOccurs="0" maxOccurs="unbounded">
        <annotation>
          <documentation>relation to bank</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>

</schema>
