<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/OBSGeneralContractRelationWS/"
        xmlns:tns="http://airbank.cz/obs/ws/OBSGeneralContractRelationWS/"
        xmlns="http://www.w3.org/2001/XMLSchema">

    <complexType name="EntitledPersonType">
        <sequence>
            <element name="smeCuid" type="long">
                <annotation>
                    <documentation>CUID of sme customer</documentation>
                </annotation>
            </element>
            <element name="generalContractNumber" type="string">
                <annotation>
                    <documentation>general contract number</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>list of CUIDs of entitled persons</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>


    <element name="GetEntitledPersonsRequest">
        <complexType>
            <sequence>
                <element name="smeCuid" type="long" maxOccurs="5000">
                    <annotation>
                        <documentation>CUID of SME customer</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="GetEntitledPersonsResponse">
        <complexType>
            <sequence>
                <element name="entitledPerson" type="tns:EntitledPersonType" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>list of CUIDs of entitled persons</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>

