<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions name="AccountReservationWS"
             targetNamespace="http://airbank.cz/obs/ws/AccountReservationWS"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://airbank.cz/obs/ws/AccountReservationWS"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
>

    <documentation>
        Web Service for bank accounts reservations.
    </documentation>

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/AccountReservationWS" elementFormDefault="qualified">
            <xsd:include schemaLocation="AccountReservationWS.xsd" />
        </xsd:schema>
    </types>

    <message name="CreateAccountReservationRequest">
        <part name="CreateAccountReservationRequest" element="tns:CreateAccountReservationRequest"/>
    </message>
    <message name="CreateAccountReservationResponse">
        <part name="CreateAccountReservationResponse" element="tns:CreateAccountReservationResponse"/>
    </message>
    <message name="GetAccountReservationRequest">
        <part name="GetAccountReservationRequest" element="tns:GetAccountReservationRequest"/>
    </message>
    <message name="GetAccountReservationResponse">
        <part name="GetAccountReservationResponse" element="tns:GetAccountReservationResponse"/>
    </message>

    <portType name="AccountReservationWS">
        <operation name="CreateAccountReservation">
            <documentation>
                Create new account reservation
            </documentation>
            <input message="tns:CreateAccountReservationRequest"/>
            <output message="tns:CreateAccountReservationResponse"/>
        </operation>
        <operation name="GetAccountReservation">
            <documentation>
                Get reserved bankaccount
            </documentation>
            <input message="tns:GetAccountReservationRequest"/>
            <output message="tns:GetAccountReservationResponse"/>
        </operation>
    </portType>

    <binding name="AccountReservationSOAP" type="tns:AccountReservationWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="CreateAccountReservation">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="GetAccountReservation">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>

    <service name="AccountReservationWS">
        <port binding="tns:AccountReservationSOAP" name="AccountReservationSOAP">
            <soap:address location="https://localhost:7002/"/>
        </port>
    </service>
</definitions>
