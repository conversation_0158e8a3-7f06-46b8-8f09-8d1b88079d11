<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:sc="http://airbank.cz/obs/ws/obsScoringVerification"
           xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/obs/ws/obsScoringVerificationWS"
           targetNamespace="http://airbank.cz/obs/ws/obsScoringVerificationWS" jxb:version="3.0">

    <xs:annotation>
        <xs:documentation>Types for scoring verification</xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/obs/ws/obsScoringVerification" schemaLocation="../xsd/ScoringVerification.xsd"/>

    <xs:element name="processScoringVerificationRequest">
        <xs:annotation>
            <xs:documentation>Scoring data collected by asynchronous LAP process. Contains scoring results based on LAP Vector specification.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="sc:ScoringVerificationRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="processScoringVerificationResponse">
        <xs:annotation>
            <xs:documentation>Empty confirmation message. Should not throw any exception otherwise is something wrong. (Will be spec.)</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

</xs:schema>
