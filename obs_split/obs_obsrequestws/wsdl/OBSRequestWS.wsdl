<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- $Id: bd26655858fec9aa1bab150e3c4eb3f1cbe6622c $ -->
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:com="http://arbes.com/ib/core/ppf/ws/common/" xmlns:tns="http://airbank.cz/obs/ws/OBSRequestWS/" targetNamespace="http://airbank.cz/obs/ws/OBSRequestWS/">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/OBSRequestWS/" elementFormDefault="qualified">
            <xsd:include schemaLocation="OBSRequestWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="createRequestRequest">
        <wsdl:part element="tns:createRequestRequest" name="createRequestRequest" />
    </wsdl:message>
    <wsdl:message name="createRequestResponse">
        <wsdl:part element="tns:createRequestResponse" name="createRequestResponse" />
    </wsdl:message>
    <wsdl:message name="createRequestFault">
        <wsdl:part name="createRequestFault" element="com:ErrorsListType"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="getRequestsRequest">
        <wsdl:part element="tns:getRequestsRequest" name="getRequestsRequest" />
    </wsdl:message>
    <wsdl:message name="getRequestsResponse">
        <wsdl:part element="tns:getRequestsResponse" name="getRequestsResponse" />
    </wsdl:message>
    <wsdl:message name="getRequestsFault">
        <wsdl:part name="getRequestsFault" element="com:ErrorsListType"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="finishRequestRequest">
        <wsdl:part element="tns:finishRequestRequest" name="finishRequestRequest" />
    </wsdl:message>
    <wsdl:message name="finishRequestResponse">
        <wsdl:part element="tns:finishRequestResponse" name="finishRequestResponse" />
    </wsdl:message>
    <wsdl:message name="finishRequestFault">
        <wsdl:part name="finishRequestFault" element="com:ErrorsListType"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="addResultDescriptionRequest">
        <wsdl:part element="tns:addResultDescriptionRequest" name="addResultDescriptionRequest" />
    </wsdl:message>
    <wsdl:message name="addResultDescriptionResponse">
        <wsdl:part element="tns:addResultDescriptionResponse" name="addResultDescriptionResponse" />
    </wsdl:message>
    <wsdl:message name="addResultDescriptionFault">
        <wsdl:part name="addResultDescriptionFault" element="com:ErrorsListType"></wsdl:part>
    </wsdl:message>

    <wsdl:portType name="OBSRequestWS">
        <wsdl:operation name="createRequest">
            <wsdl:documentation>Vytvoří Požadavek v systému OBS a vrátí jeho identifikátor.

požadované podmínky:
přihlášeného uživatele: ne

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NO_DATA_FOUND / cuid / : subjekt s uvedeným cuid nebyl nalezen
CLERR_NO_DATA_FOUND / type / : typ požadavku nebyl nalezen
CLERR_NO_DATA_FOUND / queue / : fronta požadavků nebyla nalezena
CLERR_NO_DATA_FOUND / solver / : operátor s uvedeným číslem zaměstnance nebyl nalezen
CLERR_NO_DATA_FOUND / accountNumber / : účet nebyl nalezen
CLERR_NO_DATA_FOUND / loanNumber / : úvěr nebyl nalezen
CLERR_MANDATORY_PARAMETER / accountNumber / : účet je pro tento typ požadavku povinný</wsdl:documentation>
            <wsdl:input message="tns:createRequestRequest" />
            <wsdl:output message="tns:createRequestResponse" />
            <wsdl:fault name="fault" message="tns:createRequestFault" />
        </wsdl:operation>

        <wsdl:operation name="getRequests">
            <wsdl:documentation>Vrátí seznam požadavků vedených na zákazníka (dle CUID)

požadované podmínky:
přihlášeného uživatele: ne

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NO_DATA_FOUND / cuid / : subjekt s uvedeným cuid nebyl nalezen</wsdl:documentation>
            <wsdl:input message="tns:getRequestsRequest" />
            <wsdl:output message="tns:getRequestsResponse" />
            <wsdl:fault name="fault" message="tns:getRequestsFault" />
        </wsdl:operation>

        <wsdl:operation name="finishRequest">
            <wsdl:documentation>Dokončí požadavek se zápisem textového popisu výsledku.

                požadované podmínky: Požadavek musí být ve stavu V řešení nebo Rozpracovaný
                přihlášeného uživatele: ne

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / requestId / : požadavek s daným ID nebyl nalezen
                CLERR_STATUS_REQUEST / status / : Výchozí stav požadavku není V řešení nebo Rozpracovaný
                CLERR_NULL_DESCR / resultDescription / : Zadaný popis řešení je řetězec nulové délky
                CLERR_SET_RCALLBACK / makeCallBack / : požadavek se zpětným voláním nelze automaticky dokončit
                CLERR_NOEXIST_SOLVER / solver / : Zadaný operátor (employeeNumber) není evidován v bc_operator</wsdl:documentation>
            <wsdl:input message="tns:finishRequestRequest" />
            <wsdl:output message="tns:finishRequestResponse" />
            <wsdl:fault name="fault" message="tns:finishRequestFault" />
        </wsdl:operation>

        <wsdl:operation name="addResultDescription">
            <wsdl:documentation>Aditivně připojí textový popis k výsledku v požadavku. Stav požadavku ponechá jak je.

                požadované podmínky: Požadavek musí být ve stavu V řešení nebo Rozpracovaný
                přihlášeného uživatele: ne

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_NO_DATA_FOUND / requestId / : požadavek s daným ID nebyl nalezen
                CLERR_STATUS_REQUEST / status / : Výchozí stav požadavku není V řešení nebo Rozpracovaný
                CLERR_NULL_DESCR / resultDescription / : Zadaný popis řešení je řetězec nulové délky
                CLERR_NOEXIST_WRITER / writer / : Zadaný operátor (employeeNumber) není evidován v bc_operator</wsdl:documentation>
            <wsdl:input message="tns:addResultDescriptionRequest" />
            <wsdl:output message="tns:addResultDescriptionResponse" />
            <wsdl:fault name="fault" message="tns:addResultDescriptionFault" />
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="OBSRequestWSSOAP" type="tns:OBSRequestWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="createRequest">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="getRequests">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="finishRequest">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="addResultDescription">
            <soap:operation style="document" soapAction=""/>
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="OBSRequestWS">
        <wsdl:port binding="tns:OBSRequestWSSOAP" name="OBSRequestWSSOAP">
            <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/OBSRequestWS" />
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
