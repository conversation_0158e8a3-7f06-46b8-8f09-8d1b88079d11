<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:fp="http://airbank.cz/obs/ws/feePackage"
    targetNamespace="http://airbank.cz/obs/ws/feePackageWS">

    <import namespace="http://airbank.cz/obs/ws/feePackage" schemaLocation="../xsd/feePackage.xsd" />

    <element name="getFeePackageParamsRequest">
        <complexType>
            <sequence />
        </complexType>
    </element>

    <element name="getFeePackageParamsResponse">
        <complexType>
            <sequence>
                <element name="feePackageParams" type="fp:feePackageParamsTO" minOccurs="0" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <element name="getFeePackageRequest">
        <complexType>
            <sequence>
                <element name="idGeneralContract" type="long">
                    <annotation>
                        <documentation>OBS identification of the client's general contract.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getFeePackageResponse">
        <complexType>
            <sequence>
                <element name="feePackage" type="fp:feePackageTO" minOccurs="0" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <element name="setFeePackageRequest">
        <complexType>
            <sequence>
                <element name="feePackage" type="fp:setFeePackageTO" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <element name="setFeePackageResponse">
        <complexType>
            <sequence />
        </complexType>
    </element>

    <element name="addProductImpactToFeePackageRequest">
        <complexType>
            <sequence>
                <element name="idGeneralContract" type="long">
                    <annotation>
                        <documentation>OBS identification of the client's general contract.</documentation>
                    </annotation>
                </element>
                <element name="productType" type="string">
                    <annotation>
                        <documentation>A product type.</documentation>
                    </annotation>
                </element>
                <element name="currency" type="string" minOccurs="0">
                    <annotation>
                        <documentation>Three-letter ISO 4217 product currency code.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="addProductImpactToFeePackageResponse">
        <complexType>
            <sequence>
                <element name="feePackageActivates" type="boolean">
                    <annotation>
                        <documentation>The flag that adding the product activates the fee package.</documentation>
                    </annotation>
                </element>
                <element name="type" type="string">
                    <annotation>
                        <documentation>A fee package type.</documentation>
                    </annotation>
                </element>
                <element name="name" type="string">
                    <annotation>
                        <documentation>A fee package name.</documentation>
                    </annotation>
                </element>
                <element name="feeAmount" type="decimal">
                    <annotation>
                        <documentation>The amount of the package fee.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="recalculateFeePackageRequest">
        <complexType>
            <sequence>
                <element name="recalculateFeePackage" type="fp:recalculateFeePackageTO" maxOccurs="unbounded"/>
            </sequence>
        </complexType>
    </element>

    <element name="recalculateFeePackageResponse">
        <complexType>
            <sequence />
        </complexType>
    </element>

</schema>