<?xml version="1.0" encoding="UTF-8"?>
<schema
    targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/"
    xmlns="http://www.w3.org/2001/XMLSchema"
    elementFormDefault="qualified" version="1.0"
    xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">

    <include schemaLocation="CountryTO.xsd"></include>

    <complexType name="ExchangeRateTO">
        <sequence>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>kód měny</documentation>
                </annotation>
            </element>
            <element name="lot" type="long">
                <annotation>
                    <documentation>jednotka měny</documentation>
                </annotation>
            </element>
            <element name="nonCashBuy" type="decimal">
                <annotation>
                    <documentation>deviza nákup</documentation>
                </annotation>
            </element>
            <element name="nonCashSell" type="decimal">
                <annotation>
                    <documentation>deviza prodej</documentation>
                </annotation>
            </element>
            <element name="nonCashMid" type="decimal">
                <annotation>
                    <documentation>deviza střed</documentation>
                </annotation>
            </element>
            <element name="exchangeRateDeviation" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>odchylka ECB</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CurrencyTypeTO">
        <sequence>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>code of currency</documentation>
                </annotation>
            </element>
            <element name="name" type="string">
                <annotation>
                    <documentation>name of currency</documentation>
                </annotation>
            </element>
            <element name="country" type="Q1:CountryTypeTO" minOccurs="0">
                <annotation>
                    <documentation>country</documentation>
                </annotation>
            </element>
            <element name="ehp" type="boolean">
                <annotation>
                    <documentation>european economic area</documentation>
                </annotation>
            </element>
            <element name="zps" type="boolean">
                <annotation>
                    <documentation>currency is allowed for foreign payment order</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="StockExchangeRateTO">
        <sequence>
            <element name="sourceCurrencyCode" type="string">
                <annotation>
                    <documentation>kód zdrojové měny</documentation>
                </annotation>
            </element>
            <element name="targetCurrencyCode" type="string">
                <annotation>
                    <documentation>kód cílové měny</documentation>
                </annotation>
            </element>
            <element name="nonCashBuy" type="decimal">
                <annotation>
                    <documentation>deviza nákup</documentation>
                </annotation>
            </element>
            <element name="nonCashMid" type="decimal">
                <annotation>
                    <documentation>deviza střed</documentation>
                </annotation>
            </element>
            <element name="nonCashSell" type="decimal">
                <annotation>
                    <documentation>deviza prodej</documentation>
                </annotation>
            </element>
            <element name="exchangeRateDeviation" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>odchylka ECB</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

</schema>
