<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/cardTransaction"
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:tns="http://airbank.cz/obs/ws/cardTransaction">

    <simpleType name="ComplaintBillingAccount">
        <restriction base="string">
            <enumeration value="COMPLAINT">
                <annotation>
                    <documentation>Reklamační účet</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLIENT">
                <annotation>
                    <documentation>Klientský účet</documentation>
                </annotation>
            </enumeration>

            <enumeration value="SETTLEMENT">
                <annotation>
                    <documentation>Vyrovnávací účet</documentation>
                </annotation>
            </enumeration>

            <enumeration value="LOSS">
                <annotation>
                    <documentation>Účet Manka a škody <PERSON></documentation>
                </annotation>
            </enumeration>

            <enumeration value="LOSST">
                <annotation>
                    <documentation>Účet Manka a škody daňový</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="Transaction" abstract="true">
        <sequence>
            <element name="accMoveId" type="long">
                <annotation>
                    <documentation>Externí identifikátor. V tomto případě identifikátor daného pohybu v CMS.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CommonTransaction">
        <complexContent>
            <extension base="tns:Transaction">
                <sequence>
                    <element name="debitTransactionSide" type="tns:TransactionSide">
                        <annotation>
                            <documentation>debetní strana běžného pohybu</documentation>
                        </annotation>
                    </element>
                    <element name="creditTransactionSide" type="tns:TransactionSide">
                        <annotation>
                            <documentation>kreditní strana běžného pohybu</documentation>
                        </annotation>
                    </element>
                    <element name="valueDate" type="dateTime">
                        <annotation>
                            <documentation>Valuta = "hodnota je účtována ke dni", zapisuje se i čas.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="originalAmount" type="decimal">
                        <annotation>
                            <documentation>Původní (originální) nebo taky transakční částka, která přišla z clearingu vztahující se k platbě kartou v reklamaci (CMS ji zná vždy).
                            </documentation>
                        </annotation>
                    </element>
                    <element name="originalCurrency" type="string">
                        <annotation>
                            <documentation>měna transakce Měna původní částky platby v reklamaci (ISO Alpha3 Code).
                            </documentation>
                        </annotation>
                    </element>
                    <element name="settlementAmount" type="decimal">
                        <annotation>
                            <documentation>Náhradní částka pro určení hodnoty transakce, pokud originalCurrency není v kurzovém lísktu AB.
                                Částka v měně rekonciliace pro hodnotu transakce, kterou říká a dopočítává asociace (Mastercard) DE005.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="settlementCurrency" type="string">
                        <annotation>
                            <documentation>Náhradní měna pro určení hodnoty transakce,
                                pokud pokud originalCurrency není v kurzovém lísktu AB k účtování na příslušné straně transakce. Může se jednat pouze o měnu rekonciliace.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="originalPlace" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Místo původu pro účetní operaci. V případě účtování
                                COMPLAINT - CLIENT je zapisováno místo platby / výběru kartou v reklamaci. Na účtu klienta se transformuje do položky ochodní místo / bankomat.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>V případě účtování COMPLAINT - CLIENT se transformuje na účtu klienta do
                                konkrétního běžného účtu názvu pohybu složeného z názvu držitele, čísla karty v anonymizované podobě a emboss_name (doplňuje OBS)
                                IB pak zobrazuje např: Jana Nováková (517766******1234)
                                MA zobrazuje: Jana Nováková / 517766******1234 / JANA NOVÁKOVÁ
                            </documentation>
                        </annotation>
                    </element>
                    <element name="originalAccMoveId" type="long">
                        <annotation>
                            <documentation>Externí identifikátor původní transakce spojené s platbou. Identifikátor daného pohybu v CMS.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CancelationTransaction">
        <complexContent>
            <extension base="tns:Transaction">
                <sequence>
                    <element name="toCancelAccMoveId" type="long">
                        <annotation>
                            <documentation>Odkaz na externí identifikátor accMoveId, které má OBS stornovat.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="TransactionSide">
        <annotation>
            <documentation>Strana účetního pohybu</documentation>
        </annotation>
        <sequence>
            <element name="account" type="tns:ComplaintBillingAccount">
            </element>
            <element name="amount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Částka nařízená ke zúčtování. Jen ve výjimečných případech a jen když se jedná o účet CLIENT může být nevyplněna a OBS ji dopočte z hodnoty pohybu.
                        účet CLIENT, částka je doplňována vždy pokud presentment prošel přes volání obsCardTransactionWS.transac155, která vrací částku zúčtovanou na účtu klienta
                        Nemá dosud žádnou výjimku. I v případě, že presentment vratky je přímo účtován SETLLEMENT → COMPLAINT (EUR nebo CZK podle SETTLEMENT účtu) a tedy CMS neeviduje ekvivalent účtu klienta, nemůže být tato vratka účtovaná jako COMPLAINT → CLIENT, ale musí dojít nejdříve ke stornu SETLLEMENT → COMPLAINT a teprve následně se provolá obsCardTransactionWS.transac155 a pro presentment vratky je získána částka účtovaná na účet klienta.
                        účet SETTLEMENT, COMPLAINT, LOSS, LOSST částka je vždy určena presentmentem podle McIncomingClient.amountReconciliation
                    </documentation>
                </annotation>
            </element>
            <element name="currency" type="string" minOccurs="0">
                <annotation>
                    <documentation>Měna účtu ke zúčtování. Jen ve výjimeřných případech a jen když se jedná o účet CLIENT může být nevyplněna a OBS ji dopočte z attributu cardId.
                        CLIENT - vždy se doplňuje a nemá dosud žádnou výjimku, může nabývat hodnot CZK, EUR, USD
                        SETTLEMENT, COMPLAINT, LOSS, LOSST - vždy se doplňuje a nemá žádnou výjimku, může nabývat hodnot CZK, EUR a je určen podle presentmentu McIncomingClient.currencyReconciliation
                    </documentation>
                </annotation>
            </element>
            <element name="note" type="string" minOccurs="0">
                <annotation>
                    <documentation>Poznámka. V případě účtu CLIENT se jedná o poznámku na účetní položce (transakci na běžném účtu), kteoru si klient může editovat.
                        CMS sem zapisuje text např: "Odůčtování proplacení reklamované platby", nebo "Proplacení reklamovaného výběru v hotovosti"...
                        COMPLAINT, LOSS, LOSST - text daný zřetězením businessJiraTicketId + tečka + mezera + text daný MDM číselníkem BusinessProcessEvent dané události viz. atribut businessProcessEvent
                    </documentation>
                </annotation>
            </element>

            <element name="matchingSymbol" type="string" minOccurs="0">
                <annotation>
                    <documentation>Požadovaný párovací symbol strany účetního pohybu k vyrovnání s jiným účetním pohybem do 0. Používám se pro účty
                        SETTLEMENT - vždy se vyplňuje McIncomingClient.incommingFileId
                        COMPLAINT - vždy se vyplňuje jako zřetězení ticketId v Business Jira a identifikátoru platby originalAccMoveId (odděleno pomlčkou) - CMS naplňuje: Complaint.ticketId + "-" + ComplaintCardPayment.originalAccMoveId
                        LOSS, LOSST, CLIENT - nic </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ComplaintRefund">
        <sequence>
            <element name="matchingSymbol" type="string">
                <annotation>
                    <documentation>Matching symbol for a newly opened item on the COMPLAINT side.</documentation>
                </annotation>
            </element>
            <element name="accMoveId" type="long">
                <annotation>
                    <documentation>Identification of the movement of payment of the complaint in the CMS.</documentation>
                </annotation>
            </element>
            <element name="note" type="string" minOccurs="0">
                <annotation>
                    <documentation>Note to the payment of the complaint.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="TransacAdm161">
        <sequence>
            <element name="idRecord" type="long"/>
            <element name="transactionType" type="string"/>
            <element name="transactionDate" type="date"/>
            <element name="amountTrans" type="decimal"/>
            <element name="currencyTrans" type="string"/>
            <element name="cardAcceptorTerminalID" type="string"/>
        </sequence>
    </complexType>

</schema>
