<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/investment"
    targetNamespace="http://airbank.cz/obs/ws/investment">

    <simpleType name="terminationWayTO">
        <annotation>
            <documentation>The way of termination of the investment contract / Způsob ukončení investiční smlouvy</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="WITHDRAWAL_BY_BANK">
                <annotation>
                    <documentation>Withdrawal by the bank / Odstoupení ze strany banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="WITHDRAWAL_BY_CLIENT">
                <annotation>
                    <documentation>Withdrawal by the client / Odstoupení ze strany klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERMINATION_BY_BANK">
                <annotation>
                    <documentation>Termination by the bank / Výpověď ze strany banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TERMINATION_BY_CLIENT">
                <annotation>
                    <documentation>Termination by the client / Výpověď ze strany klienta</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="terminationReasonTO">
        <annotation>
            <documentation>The reason for termination of the investment contract / Důvod ukončení investiční smlouvy</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="DEATH">
                <annotation>
                    <documentation>Death / Úmrtí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTHER_VIOLATION_BY_CLIENT">
                <annotation>
                    <documentation>Other breach by the client / Jiné porušení (ze strany klienta)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AML">
                <annotation>
                    <documentation>Breach of AML / Porušení AML</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CLIENT_REQUEST">
                <annotation>
                    <documentation>Client request / Na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="IBKR_REJECT">
                <annotation>
                    <documentation>Client rejected in IBKR / Klient zamítnut v IBKR (IBKR = Interactive Brokers)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="IBKR_NOT_ACTIVE">
                <annotation>
                    <documentation>Account not activated in IBKR / Účet nebyl aktivován v IBKR (IBKR = Interactive Brokers)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="IBKR_VIOLATION_BY_CLIENT">
                <annotation>
                    <documentation>Violation of an investment service by client / Zneužití investiční služby klientem (IBKR = Interactive Brokers)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="contractTerminationTO">
        <sequence>
            <element name="way" type="tns:terminationWayTO">
                <annotation>
                    <documentation>The way of termination of the investment contract.</documentation>
                </annotation>
            </element>
            <element name="reason" type="tns:terminationReasonTO">
                <annotation>
                    <documentation>The reason for termination of the investment contract.</documentation>
                </annotation>
            </element>
            <element name="requestedDate" type="date">
                <annotation>
                    <documentation>The requested termination date of the investment contract.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="investmentAccountTO">
        <sequence>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>Investment account number.</documentation>
                </annotation>
            </element>
            <element name="bankCode" type="string">
                <annotation>
                    <documentation>Bank code of the investment account.</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>Currency code of the investment account.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

</schema>