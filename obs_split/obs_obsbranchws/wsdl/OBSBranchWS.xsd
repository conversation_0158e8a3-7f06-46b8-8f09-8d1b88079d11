<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsBranchWS/" xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/"
            xmlns="http://www.w3.org/2001/XMLSchema">
    <import schemaLocation="../xsd/AddressTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
    <import schemaLocation="../xsd/BranchTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
    <element name="getAddressByCodeRequest">
        <complexType>
            <sequence>
                <element name="branchCode" type="string">
                    <annotation>
                        <documentation>Kód pobo<PERSON>ky</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="getAddressByCodeResponse">
        <complexType>
            <sequence>
                <element name="address" type="Q1:ObsAddressTypeTO" minOccurs="0">
                    <annotation>
                        <documentation>
                            Address of branch
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="Branch"/>
    <element name="getBranchesResponse">
        <complexType>
            <sequence>
                <element name="branches" type="Q1:BranchTO" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>
                            Returned branches
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="branches"/>
    <element name="authenticateBranchRequest">
        <complexType>
            <sequence>
                <element name="ipAddress" type="string"/>
                <element name="certificateId" type="string">
                    <annotation>
                        <documentation>id certifikátu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="authenticateBranchResponse">
        <complexType>
            <sequence>
                <element name="branch" type="Q1:BranchTO" minOccurs="0"/>
                <element name="authResult" type="Q1:BranchAuthentizationResult"/>
            </sequence>
        </complexType>
    </element>
    <element name="getBranchesRequest">
        <complexType>
            <sequence/>
        </complexType>
    </element>

    <element name="getBranchDetailRequest">
        <complexType>
            <choice>
                <element name="branchCode" type="string">
                    <annotation>
                        <documentation>kód pobočky</documentation>
                    </annotation>
                </element>
                <element name="branchDepartmentDn" type="string">
                    <annotation>
                        <documentation>LDAP distinguished name (DN) pobočky</documentation>
                    </annotation>
                </element>
            </choice>
        </complexType>
    </element>
    <element name="getBranchDetailResponse">
        <complexType>
            <sequence>
                <element name="branchDetail" type="Q1:BranchDetailTO">
                    <annotation>
                        <documentation>detail pobočky</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getBranchCertificatesRequest">
      <complexType>
        <sequence>
          <element name="branchCommonName" type="string">
            <annotation>
              <documentation>Pojmenování pobočky na certifikátu. Slouží pro účely vyhledání z CAir.</documentation>
            </annotation>
          </element>
        </sequence>
      </complexType>
    </element>

    <element name="getBranchCertificatesResponse">
      <complexType>
        <sequence>
          <element name="branchCertificate" type="Q1:BranchCertificateTO" minOccurs="0" maxOccurs="2">
            <annotation>
              <documentation>pobockovy certifikat</documentation>
            </annotation>
          </element>
        </sequence>
      </complexType>
    </element>
    <element name="getBranchConfigurationDataRequest">
        <complexType>
            <sequence>
                <element name="branchCommonName" type="string">
                    <annotation>
                        <documentation>Pojmenování pobočky na certifikátu. Slouží pro účely vyhledání z CAir.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
    <element name="getBranchConfigurationDataResponse">
        <complexType>
            <sequence>
                <element name="plannedCallDepartmentQueueCode" type="string">
                    <annotation>
                        <documentation>Kód oddělení pro plánované hovory</documentation>
                    </annotation>
                </element>
                <element name="branchPrinterDnsName" type="string">
                    <annotation>
                        <documentation>DNS název tiskárny na pobočce</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
</schema>