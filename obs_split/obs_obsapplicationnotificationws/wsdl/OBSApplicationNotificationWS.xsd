<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsApplicationNotificationWS/" xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:Q2="http://airbank.cz/ams/ws/application/shared">

	<xsd:import schemaLocation="AMSApplication.xsd" namespace="http://airbank.cz/ams/ws/application/shared" />
	<xsd:import schemaLocation="../xsd/ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />	

	<xsd:element name="applicationNotificationRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="applicationDetail" type="Q2:ApplicationDetail" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>detail žádosti z IB</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="applicationNotificationResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="contractIdent" type="Q1:ContractIdent" maxOccurs="unbounded" minOccurs="0" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	
  <xsd:element name="setPasswordRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>user identification by CIF id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="username" type="xsd:string" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>client's username</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="password" type="xsd:base64Binary" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>password from application - see wiki https://wiki.apedie.abank.cz/pages/viewpage.action?pageId=*********</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setPasswordResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
