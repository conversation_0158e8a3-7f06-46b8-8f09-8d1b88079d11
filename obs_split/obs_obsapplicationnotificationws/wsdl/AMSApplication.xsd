<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:cl="http://airbank.cz/ams/ws/application/codelists"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/shared"
           targetNamespace="http://airbank.cz/ams/ws/application/shared">

    <xs:import namespace="http://airbank.cz/ams/ws/application/codelists" schemaLocation="AMSCodeLists.xsd"/>

    <xs:complexType name="ApplicationId">
        <xs:sequence>
            <xs:element name="id" type="xs:long"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Address">
        <xs:annotation>
            <xs:documentation>adresa</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>id adresy</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="country" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>2letter country code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="town" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>CZ: Obec (město).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="streetOrLocality" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>CZ: Ulice nebo část obce.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="house" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>CZ: Číslo domu.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="zip" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>zip code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="right" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>přesto , že není úplně správně klient potvrzuje jeho správnost</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PurposeAddress">
        <xs:annotation>
            <xs:documentation>účelová adresa</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="addressLine1" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Address line 1</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="addressLine2" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Address line 2</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="town" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Town name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="zip" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Zip code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="country" type="xs:string">
                <xs:annotation>
                    <xs:documentation>2letter country code</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Customer">
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="salutation" type="xs:string" minOccurs="0"/>
                    <xs:element name="honourBefore" type="xs:string" minOccurs="0"/>
                    <xs:element name="name1" type="xs:string" minOccurs="0"/>
                    <xs:element name="name2" type="xs:string" minOccurs="0"/>
                    <xs:element name="honourAfter" type="xs:string" minOccurs="0"/>
                    <xs:element name="vocative" type="xs:string" minOccurs="0"/>
                    <xs:element name="gender" type="xs:string" minOccurs="0"/>
                    <xs:element name="citizenships" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="birth" type="xs:date" minOccurs="0"/>
                    <xs:element name="birthPlace" type="BirthPlace" minOccurs="0"/>
                    <xs:element name="relation" type="xs:int" minOccurs="0"/>
                    <xs:element name="relatedParty" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="politicallyExposed" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="anonymizationStatus" type="xs:string" minOccurs="0"/>
                    <xs:element name="anonymizationDate" type="xs:date" minOccurs="0"/>
                    <xs:element name="embossed" type="CustomerEmbossed" minOccurs="0"/>
                    <xs:element name="birthnum" type="CustomerBirthnum" minOccurs="0"/>
                    <xs:element name="personalData" type="CustomerPersonalData" minOccurs="0"/>
                    <xs:element name="financialData" type="CustomerFinData" minOccurs="0"/>
                    <xs:element name="householdIncomeExpenseData" type="HouseholdIncomeExpenseDataTO"/>
                    <xs:element name="amlStatus" type="xs:string" minOccurs="0"/>
                    <xs:element name="manualAmlCheck" type="CustomerManualAmlCheck" minOccurs="0"/>
                    <xs:element name="addresses" type="CustomerAddress" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="contacts" type="CustomerContact" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="employments" type="CustomerEmployment" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="documents" type="CustomerDocument" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="consents" type="CustomerConsent" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="feePackages" type="FeePackage" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="FeePackage">
        <xs:annotation>
            <xs:documentation>Fee package data for the applicant.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Type of the fee package</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CustomerFeature">
        <xs:annotation>
            <xs:documentation>customer feature</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>id featury</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifPerson" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>person/system name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="trustLevel" type="xs:int">
                <xs:annotation>
                    <xs:documentation>data trust level</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="HouseholdIncomeExpenseDataTO">
        <xs:annotation>
            <xs:documentation>Customer incomes and expenses.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="otherHouseholdMembersIncome" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="medicalFoodTransportationExpense" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="accommodationExpense" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="otherHouseholdMembersInstallmentsExpense" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="dynamicExpense1" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="dynamicExpense2" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="dynamicExpense3" type="IncomeExpenseTO" minOccurs="0"/>
            <xs:element name="dynamicExpense4" type="IncomeExpenseTO" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IncomeExpenseTO">
        <xs:annotation>
            <xs:documentation>Expense or Income data holder with label and amount.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="amount" type="xs:decimal"/>
            <xs:element name="label" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CustomerPersonalData">
        <xs:annotation>
            <xs:documentation>customer personal data feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="maritalStatus" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>status of marital</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="married" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>is married</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="residenceType" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>residence type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="residenceTo" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>residence to</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="education" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>education</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="housingType" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>housing type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="dependentPersons" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>num of dependent persons</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="secondIdentDocument" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>druhý typ identifikačního dokladu</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerFinData">
        <xs:annotation>
            <xs:documentation>customer fin data feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="cashLoanPaymentsSum" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>sum of cash loan payments</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="creditLimitsSum" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>sum of credit card and KTK payments</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerManualAmlCheck">
        <xs:annotation>
            <xs:documentation>customer aml check feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="manualCheck" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>aml check result</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerConsent">
        <xs:annotation>
            <xs:documentation>customer consent feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="type" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>consent type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validFrom" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>valid from</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validTo" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>valid to</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>reason of reject</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerEmbossed">
        <xs:annotation>
            <xs:documentation>customer embossed name feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="name" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>embossed name</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerBirthnum">
        <xs:annotation>
            <xs:documentation>customer birth num feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="num" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>birth num</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerDocAttr">
        <xs:annotation>
            <xs:documentation>document add data</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="attrName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>document add attribute name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="attrValue" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>document add attribute value</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CustomerDocument">
        <xs:annotation>
            <xs:documentation>customer document feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="group" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc group</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="type" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="country" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc country</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="number" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc num</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="issuer" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc issuer</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validFrom" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc valid from</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validTo" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc valid to</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="attestedCopy" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>attested copy</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isFake" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc is fake</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="deliveryChannel" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc delivery channel</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isPrimary" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>doc is primary</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="attributes" type="CustomerDocAttr" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>add doc data</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerContact">
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="value" type="xs:string" minOccurs="0"/>
                    <xs:element name="callingCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="role" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerEmployment">
        <xs:annotation>
            <xs:documentation>customer employment feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="role" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                employment role
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="netIncome" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                net income
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="economicalStatus" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                economical status
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employmentType" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeTimeType" type="IncomeTimeTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Type of income time.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employedFrom" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                employed from
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="agreementUntil" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                agrement until
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="industry" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                industry
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="profession" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                profession
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="addresses" type="CustomerAddress" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                emp adress
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contacts" type="CustomerContact" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                emp contacts
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name" type="xs:string" minOccurs="0"/>
                    <xs:element name="idNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="taxNumber" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CustomerAddress">
        <xs:annotation>
            <xs:documentation>customer adress feature</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="CustomerFeature">
                <xs:sequence>
                    <xs:element name="address" type="Address">
                        <xs:annotation>
                            <xs:documentation>addressa</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="role" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>role adresy</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="residenceFrom" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>resident od</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="BirthPlace">
        <xs:annotation>
            <xs:documentation>Customer's birth place.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="discriminator" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Birth place discriminator from MDM codelist BIRTH_PLACE_DISCRIMINATOR (https://wiki.airbank.cz/x/uYVZDw).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:choice minOccurs="0">
                <xs:element name="ruianBirthPlace" type="RuianBirthPlace"/>
                <xs:element name="countryBirthPlace" type="CountryBirthPlace"/>
                <xs:element name="outsideCountryBirthPlace" type="OutsideCountryBirthPlace"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RuianBirthPlace">
        <xs:sequence>
            <xs:choice>
                <xs:element name="townCode" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Code of town from RUIAN.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pragueMunicipalDistrictCode" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Code of Prague municipal district from RUIAN.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CountryBirthPlace">
        <xs:sequence>
            <xs:element name="countryAlpha2Code" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Two letter code of country.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="location" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Text representation of exact location for given country.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OutsideCountryBirthPlace">
        <xs:sequence>
            <xs:element name="place" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Text representation of vague location (for example "On the sea").</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Sellerplace">
        <xs:sequence>
            <xs:element name="code" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Sellerplace code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Sellerplace name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="salesChannel" type="SalesChannel" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Sales channel to which this sellerplace belongs</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SalesChannel">
        <xs:sequence>
            <xs:element name="code" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Sales channel code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Sales channel description</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Sales channel name</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ApplicationStatusHistoryRecord">
        <xs:annotation>
            <xs:documentation>history record</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="from" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>from</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="cl:ApplicationStatus">
                <xs:annotation>
                    <xs:documentation>Status of the application</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="to" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>to</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Channel">
        <xs:annotation>
            <xs:documentation>data time stamp during application flow</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>IB, ECC, ICC, BRANCH</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="time" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>time stamp</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idAgency" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>branch or ecc identifier in obs</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idOperator" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>operator identifier</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LAPResult">
        <xs:annotation>
            <xs:documentation>lap result data</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of product request rejection (codelist value)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of product request rejection for communication with client (codelist value)</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Application">
        <xs:annotation>
            <xs:documentation>application</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="ApplicationId">
                <xs:annotation>
                    <xs:documentation>id application</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>envelope ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="subApplicationId" type="ApplicationId" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Only in the case that this record is returned on the sameness in this subApplication</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="birth" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Applicant birth date (not exists only in LEAD)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Applicant cuid (not exists only in LEAD)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ident" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Applicant birth number (rodné číslo) (not exists only in LEAD)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name1" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Applicant first name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name2" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Applicant surname</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ContractApplication">
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="status" type="cl:ApplicationStatus">
                        <xs:annotation>
                            <xs:documentation>Status of the application</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelLead" type="Channel" minOccurs="0"/>
                    <xs:element name="channelSale" type="Channel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>channel snapshost when application was created</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelFinish" type="Channel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>channel snapshost when application finished</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionChannel" type="cl:DistributionalChannel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Distributional channel which user chose for contract delivery</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionBranchId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of branch that client chose for contract delivery</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="email" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>applicant's email</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobile" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>applicant's primary mobile</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mgm" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>member get member</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ProductApplication">
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="status" type="cl:ApplicationStatus">
                        <xs:annotation>
                            <xs:documentation>Status of the application</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelSale" type="Channel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>channel snapshost when application was created</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelApprove" type="Channel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>channel snapshost when application approved</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionChannel" type="cl:DistributionalChannel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Distributional channel which user chose for contract delivery</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionBranchId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of branch that client chose for contract delivery</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="completionId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>identifier of contract</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="type" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>identifier of contract</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="LOAN"/>
                                <xs:enumeration value="LOAN_REFINANCE"/>
                                <xs:enumeration value="LOAN_CONSOLIDATION"/>
                                <xs:enumeration value="LOAN_REFINANCE_R4"/>
                                <xs:enumeration value="ACCOUNT"/>
                                <xs:enumeration value="CARD"/>
                                <xs:enumeration value="CARD_HOLDER"/>
                                <xs:enumeration value="DISPONENT"/>
                                <xs:enumeration value="DISPONENT_AUTH"/>
                                <xs:enumeration value="MOBILITY"/>
                                <xs:enumeration value="TERM_DISPONENT"/>
                                <xs:enumeration value="TARIFF"/>
                                <xs:enumeration value="TERM_ACCOUNT"/>
                                <xs:enumeration value="TERM_CARD"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForProduct" abstract="true">
        <xs:sequence>
            <xs:element name="completionId" type="xs:long" minOccurs="0"/>
            <xs:element name="approveStatus" type="cl:ApplicationApprovalStatus" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Status of product request approval</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationStatus" type="cl:ApplicationStatus" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Status of product request</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="parentApplicationId" type="ApplicationId" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Id of parent application</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productRequestId" type="ApplicationId">
                <xs:annotation>
                    <xs:documentation>Id of product request</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="previousApplicationId" type="ApplicationId" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>If this is an alternative application (cash loan alternative to consolidation or vice versa), this contains ID of the
                        original application
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="replacedByApplicationId" type="ApplicationId" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>If there is an alternative application for this application, this attribute contains ID of the alternative
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejected" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>flag indicating if request for product was rejected</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of product request rejection (codelist value)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of product request rejection for communication with client (codelist value)</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="MortgageStageType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPLICATION"/>
            <xs:enumeration value="OFFER_ACCEPTANCE"/>
            <xs:enumeration value="DOCUMENT_DELIVERY"/>
            <xs:enumeration value="CONTRACT_SIGNOFF"/>
            <xs:enumeration value="QUANTIFICATION_DELIVERY"/>
            <xs:enumeration value="MORTGAGE_DRAW"/>
            <xs:enumeration value="COMPLETED"/>
            <xs:enumeration value="CANCELLED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DocumentType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="GENERAL_CONTRACT"/>
            <xs:enumeration value="CONTRACT_SUPPLEMENT"/>
            <xs:enumeration value="AFFIDAVIT"/>
            <xs:enumeration value="MORTGAGE_CONTRACT"/>
            <xs:enumeration value="MORTGAGE_APPLICATION"/>
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DocumentRole">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRIMARY">
                <xs:annotation>
                    <xs:documentation>primary document class</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SECONDARY">
                <xs:annotation>
                    <xs:documentation>secondary document class</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTHER">
                <xs:annotation>
                    <xs:documentation>other document class</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="RequestForGC">
        <xs:complexContent>
            <xs:extension base="ApplicationDetail">
                <xs:sequence>
                    <xs:element name="countEnterIntoRequest" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>
                                Number of entering into unfinished
                                application
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionAddress" type="Address" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Address for contract delivery if client
                                chose distributional channel "Post"
                                ("Pošta")
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionBranchId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Id of branch that client chose for
                                contract delivery
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="distributionChannel" type="cl:DistributionalChannel" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Distributional channel which user chose
                                for contract delivery
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="finishedBy" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Number of seller that finished the
                                application in the system
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="modifTime" type="xs:dateTime">
                        <xs:annotation>
                            <xs:documentation>
                                Date and time of last application
                                modification
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="paperLead" type="PaperLead" minOccurs="0"/>
                    <xs:element name="pricelist" type="cl:Tariff" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Chosen fee pricelist
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="productRequests" type="RequestForProduct" maxOccurs="unbounded"/>
                    <xs:element name="channelSaleLead" type="xs:string"/>
                    <xs:element name="recommandatoryCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="URL" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                URL address, through which the applicant
                                came into lead pages
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="username" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                Username that client chose
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="passwordEntered" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>
                                true if password was entered, otherwise false
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelLead" type="Channel" minOccurs="0"/>
                    <xs:element name="relationToBankPurposes">
                        <xs:annotation>
                            <xs:documentation>AML data - business relationship purpose, source of future income and savings</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="businessRelationshipPurpose" type="AmlCodeList">
                                    <xs:annotation>
                                        <xs:documentation>Client relation to bank purposes</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="sourcesOfIncome" type="AmlCodeList">
                                    <xs:annotation>
                                        <xs:documentation>Sources of future client income</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="sourcesOfSavings" type="AmlCodeList">
                                    <xs:annotation>
                                        <xs:documentation>Sources of future client savings</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ApplicationStatusHistory">
        <xs:annotation>
            <xs:documentation>history of app status</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="statusHistoryRecord" type="ApplicationStatusHistoryRecord" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>records</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestForCard">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="accountNumber" type="xs:integer" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of account to which this card belongs without prefix</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountRequestId" type="ApplicationId" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of application for account to which the card belongs</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="addressCard" type="CmsPurposeAddressTO">
                        <xs:annotation>
                            <xs:documentation>Address for card delivery</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="addressPin" type="CmsPurposeAddressTO">
                        <xs:annotation>
                            <xs:documentation>Address for PIN delivery</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cardType" type="xs:string"/>
                    <xs:element name="embossedName" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Embossed name generated by CIF</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="embossedNameByUser" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Embossed name proposed by user</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="embossedNameChangedByUser" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating if user changed embossed name</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isVirtual" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether virtual or plastic card is requested</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="replacedCardNumber" type="xs:integer" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>If this is a request for replacement card the field contains replaced card number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedDesing" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Selected design of card</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="sendPIN" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating if user wants to send PIN by mail</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="showPIN" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating if user wants to show PIN in IB (default is true)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="transferPIN" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating if user wants to change PIN in IB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requiredCardDevice" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>indicates, if we create physical card through GPE</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cardName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>card name chosen by the client</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CmsPurposeAddressTO">
        <xs:annotation>
            <xs:documentation>Address for CMS purposes e.g. card delivery address</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="addressLine1" type="xs:string">
                <xs:annotation>
                    <xs:documentation>The first line of address (usually contains street and number, name of box etc.)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="addressLine2" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Complementing address information</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="town" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Address town</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="zipCode" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Address zip code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="countryAlpha2Code" type="CountryAlpha2CodeTO">
                <xs:annotation>
                    <xs:documentation>2-letter country code</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryAlpha2CodeTO">
        <xs:annotation>
            <xs:documentation>
                2-letter country code
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:length value="2" />
            <xs:pattern value="[a-zA-Z]*" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ApplicationDetail" abstract="true">
        <xs:sequence>
            <xs:element name="applicant" type="Customer" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Applicant data</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Identification of application</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Identification of application envelope (in AMS)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractCompletionId" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Completion ID of general contract which the application belongs to</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractId" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>ID of general contract which the application belongs to</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="cl:ApplicationStatus">
                <xs:annotation>
                    <xs:documentation>Status of the application</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelSale" type="Channel" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>channel snapshost when application was created</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelApprove" type="Channel" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>channel snapshost when application was send to LAP</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelFinish" type="Channel" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>channel snapshost when application finished</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of product request rejection (codelist value)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of product request rejection for communication with client (codelist value)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statusHistory" type="ApplicationStatusHistory" minOccurs="0"/>
            <xs:element name="completionId" type="xs:long" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestForAccount">
        <xs:annotation>
            <xs:documentation>Type of account</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="accountName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Name of account
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountType" type="xs:string"/>
                    <xs:element name="currency" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                Currency of account
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isMainAccount" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Flag indicating whether client wants the
                                account as main
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanRequired" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>account for loan service</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="forced" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>forced account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForProducts">
        <xs:complexContent>
            <xs:extension base="ApplicationDetail">
                <xs:sequence>
                    <xs:element name="productRequests" type="RequestForProduct" maxOccurs="unbounded"/>
                    <xs:element name="channelSaleLead" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForCardHolder">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="cardApplicationId" type="ApplicationId">
                        <xs:annotation>
                            <xs:documentation>Id of associated application for card product</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cardHolder" type="Customer"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForDisponent">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="disponent" type="Customer"/>
                    <xs:element name="username" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Username requested for disponent</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="permissionSettings" type="PermissionSettingsTO">
                        <xs:annotation>
                            <xs:documentation>access rights for disponent</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForMobility">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="coupledAccountId" type="xs:integer" minOccurs="0"/>
                    <xs:element name="coupledAccountRequestId" type="ApplicationId" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Coupled request for account for which mobility is requested</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobilityAccountBankCode" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Bank code of mobility account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobilityAccountCurrency" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Currency of mobility account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobilityAccountNumber" type="xs:integer">
                        <xs:annotation>
                            <xs:documentation>Mobility account number</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobilityAccountPrefix" type="xs:int" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Mobility account prefix</xs:documentation>
                    </xs:annotation>
                    </xs:element>
                    <xs:element name="revocationDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of revocation TP/inkas (orig. Bank)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="establishmentDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of establishment TP/inkas (AB)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="standingOrderMobilityType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>preference of client which standing order to move either ALL or SELECTED only - shall be listed offline at branch</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="ALL"/>
                                <xs:enumeration value="SELECTED"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:element name="stopPreviousBankAccountTransfersRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>stop performing transfers to previous bank account in the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="stopPreviousBankAccountStandingOrdersRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>stop sending standing order transfers to previous bank account in the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cooperationRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request to provide some cooperation in orders transfer from other bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cooperationRequestType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>definition of how shall we cooperate</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="PASSIVE">
                                    <xs:annotation>
                                        <xs:documentation>default selection - only provide some examples how to</xs:documentation>
                                    </xs:annotation>
                                </xs:enumeration>
                                <xs:enumeration value="ACTIVE">
                                    <xs:annotation>
                                        <xs:documentation>active cooperation is required - we directly notify recipients/senders on account change.
                                            Not preferred by us. Warning shall be thrown
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:enumeration>
                            </xs:restriction>
                        </xs:simpleType>

                    </xs:element>
                    <xs:element name="provideIncomingTransfersListRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>provide list of incoming transfers and bills from previous account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="provideOutgoingTransfersListRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>provide list of outgoing transfers and bills from previous account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="balanceTransferRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request to transfer final positive balance from the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="overviewDeliveryRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>send final list of standing orders and payment bills (inkaso)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="notificationDeliveryType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>type of delivery - online or offline</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="EMAIL"/>
                                <xs:enumeration value="POST"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:element name="notifyOriginalBankAccountCancelRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>we shall deliver cancellation notification to the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="establishBankOperationsRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>we shall begin transfers, orders and bills for the newly established account to the establishedDate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>identification document type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>identification document number</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobilityPackage" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Selected mobility package.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForTariffChange">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="tariff" type="cl:Tariff"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="PaperLead">
        <xs:complexContent>
            <xs:extension base="ApplicationDetail">
                <xs:sequence>
                    <xs:element name="email" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Client's email</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mobilePhone" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Mobile phone number in 9-digit format</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name1" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Client's first name</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name2" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Client's surname</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pickerName1" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Picker's first name</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pickerName2" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Picker's surname</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pickTime" type="xs:dateTime" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date and time of lead picking</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="sellerplace" type="Sellerplace" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Sellerplace where the lead was picked</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForAccountCancel">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="canceledAccountId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Identification of bank account to cancel</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cancelationDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>Cancelation date</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="transferAccountBankCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Bank code of transfer account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="transferAccountNumber" type="xs:integer" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Transfer account number</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="transferAccountPrefix" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Transfer account prefix</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForCardCancel">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="coupledAccountId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Identification of bank account to which the card belongs</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cardId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Identification of card to cancel</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cancelationReason" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Reason of cancelation as inputted by user</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForDisponentCancel">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Identification of disponent to cancel</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForDisponentDeclaration">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="disponentRequestId" type="ApplicationId">
                        <xs:annotation>
                            <xs:documentation>
                                Request for disponent based on which
                                this declaration was created
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="disponent" type="Customer">
                        <xs:annotation>
                            <xs:documentation>
                                Personal data of new disponent
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="username" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Username requested for disponent</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ApplicationDecision">
        <xs:annotation>
            <xs:documentation>
                výsledek rozhodnutí ke konkrétní žádosti
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="amount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>
                        schválná částka
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="amountFastTrack" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        schválná částka pro proces fast track
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxTerm" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        maximální doba splácení
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LoanCharges">
        <xs:annotation>
            <xs:documentation>loan charges</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="associatedCosts" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>související náklady</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="latePaymentCosts" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>náklady opožděných plateb</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="providingCharges" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>poplatek za poskytnutí</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="administrationCharges" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>poplatek za správu úvěru</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestForLoanChange">
        <xs:annotation>
            <xs:documentation>request for loan</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="idApplication" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                application id
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idLoan" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                loan id
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="paymentSchedule" type="PaymentSchedule" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                varianty splácení od nejhorší k nejlepší
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idLimitProductVariant" type="xs:long" minOccurs="0"/>
                    <xs:element name="modificationType" type="cl:LoanModificationType"/>
                    <xs:element name="newInstalmentDay" type="xs:int" minOccurs="0"/>
                    <xs:element name="newInstalmentPeriod" type="xs:int" minOccurs="0"/>
                    <xs:element name="newInstalment" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentInstalmentDay" type="xs:int" minOccurs="0"/>
                    <xs:element name="currentInstalment" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="unpaidPrincipal" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentSchedule" type="PaymentSchedule" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="AbstractRequestForMortgage">
        <xs:annotation>
            <xs:documentation>abstract request for mortgage loan/refinance</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="payDay" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Instalment day</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fixationPeriod" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Fixation period</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstInstalmentDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>First instalment date</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="repaymentDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of mortgage repayment</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Mortgage name</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="internalCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Internal client evaluation code created by branch officer</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>výše úvěru</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalment" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Monthly instalment amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="capInstalment" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximum monthly instalment amount for selected term and cap interest rate (only valid and not null for variable
                                rate type)
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minInstalment" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimum monthly instalment (when using the longest possible repayment period)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minCapInstalment" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimum monthly instalment with cap interest rate (when using the longest possible repayment period)
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRate" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Interest rate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="capInterestRate" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Cap interest rate (only valid and not null for variable rate type)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="otbUsageInterestRateDivergence" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Interest rate divergence in case with OTB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Type of interest rate - fix, float</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="FIX"/>
                                <xs:enumeration value="FLOAT"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:element name="term" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Repayment period (in years)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="realEstatePriceEstimate" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Estimated real estate price</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="realEstatePriceOngoing" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Ongoing real estate price</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="walkin" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Application is a part of general contract application</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idProductVariant" type="xs:long" minOccurs="0"/>
                    <xs:element name="productVariantCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>kód produktové varianty</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Description of the application written by employee at the branch</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request type (currently there are only 2 possible values - comment, specific case)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idWorkFlow" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>scoring workflow id (maximal workflow ID with status = VERIFICATION)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scoreExpire" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Platnost scoringu do</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellReasonCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Code of the reason to request higher amount than the original amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="downsellPayment" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Způsob doplacení downsellu</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idAccountApplication" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Account application id</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coupledAccountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Account id</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="sameApplicationData" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>priznak editace aplikacnich dat klientem, false - pokud byla aplikacni data editovana</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestMargin" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Marže pro variabilní úrokovou sazbu</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pribor" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Aktuální pribor (Fixing úrokových sazeb na mezibankovním trhu depozit)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mortgageNum" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Číslo hypotéky</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="riskGrade" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Risk grade</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="globalLimitHyref" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximální výše uvěrového rámce pro hypotéku</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountSimulation" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Loan amount required in time before scoring</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="uwVerifier" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Vlastník obchodního případu</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="stage" type="MortgageStageType">
                        <xs:annotation>
                            <xs:documentation>Aktuální stav (stage) žádosti</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="stageHistory" type="MortgageStageHistory" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Historie stavů (stage) žádosti</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmount" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Vyse upsellu</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="downsellAmount" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Castka kterou klient doplaci ze sveho : downsell</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="ltv" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Current LTV</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxLtv" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximal LTV</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="ltvOngoing" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Current LTV ongoing</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxLtvOngoing" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximal LTV ongoing</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="feeLtv" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>LTV fee was genereated</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="purpose1" type="MortgagePurpose" minOccurs="0"/>
                    <xs:element name="purpose2" type="MortgagePurpose" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForMortgageRefinance">
        <xs:annotation>
            <xs:documentation>request for mortgage refinance</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractRequestForMortgage">
                <xs:sequence>
                    <xs:element name="processLater" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating, that the application cannot be processed right now, because the refixation date is too far in
                                future, and the customer wants us to call him later
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="commitment" type="MortgageCommitment" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Refinancované závazky</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForMortgage">
        <xs:annotation>
            <xs:documentation>request for mortgage</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractRequestForMortgage">
                <xs:sequence>
                    <xs:element name="purchasePrice" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Kupni cena nemovitosti</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForCoDebtor">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="coDebtor" type="Customer" minOccurs="0"/>
                    <xs:element name="relationToMainApplicant" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Vztah k hlavnímu žadateli</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForMortgageChange">
        <xs:annotation>
            <xs:documentation>request for mortgage loan change</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="idApplication" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>application id</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idMortgage" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>mortgage id</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="modificationType" type="cl:MortgageLoanModificationType"/>
                    <xs:element name="newInstalmentDay" type="xs:int" minOccurs="0"/>
                    <xs:element name="newInstalmentAmount" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="newOtbInstalmentAmount" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="newNextInstalmentDate" type="xs:date" minOccurs="0"/>
                    <xs:element name="newLastInstalmentDate" type="xs:date" minOccurs="0"/>
                    <xs:element name="newUnpaidPrincipal" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="newOtbAmount" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="newAmountToRepay" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="newInterestRate" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentInstalmentDay" type="xs:int" minOccurs="0"/>
                    <xs:element name="currentInstalmentAmount" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentOtbInstalmentAmount" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentNextInstalmentDate" type="xs:date" minOccurs="0"/>
                    <xs:element name="currentLastInstalmentDate" type="xs:date" minOccurs="0"/>
                    <xs:element name="currentUnpaidPrincipal" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentOtbAmount" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentAmountToRepay" type="xs:decimal" minOccurs="0"/>
                    <xs:element name="currentInterestRate" type="xs:decimal" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ConsolidationOffer">
        <xs:annotation>
            <xs:documentation>refinance extension (related to loan)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="productScoreResult" type="xs:string" minOccurs="0"/>
            <xs:element name="loanAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="instalment" type="xs:decimal" minOccurs="0"/>
            <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="idProductVariant" type="xs:long" minOccurs="0"/>
            <xs:element name="productVariant" type="xs:string" minOccurs="0"/>
            <xs:element name="evalMaxRepaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="evalMinRepaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="usedUpsell" type="xs:decimal" minOccurs="0"/>
            <xs:element name="refinanceApproveStatus" type="cl:ApplicationApprovalStatus" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Status of product request approval</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0"/>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0"/>
            <xs:element name="consolidationOfferCalculation" type="ConsolidationOfferCalculation" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="commitment" type="LoanCommitment" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ConsolidationOfferCalculation">
        <xs:sequence>

            <!-- offer based on estimated values -->
            <xs:element name="offerInstalmentAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerChargeAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerRepaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="offerRepaymentPeriodBonus" type="xs:int" minOccurs="0"/>
            <xs:element name="offerTotalAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerTotalAmountBonus" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerRpsn" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerRpsnBonus" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerUnpaidPrincipal" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerRiskGrade" type="xs:string" minOccurs="0"/>
            <xs:element name="offerLoanInterest" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerLoanInterestBonus" type="xs:decimal" minOccurs="0"/>
            <xs:element name="role" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        type of refinance calculation - prescoring, postscoring
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="PRESCORE"/>
                        <xs:enumeration value="POSTSCORE"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="offerUpsell" type="xs:decimal" minOccurs="0"/>
            <xs:element name="offerReserveAmount" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LoanCommitment">
        <xs:sequence>
            <xs:element name="orderNumberId" type="xs:long" minOccurs="0"/>
            <xs:element name="origInstalmentAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="origTotalAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="origLoanDate" type="xs:date" minOccurs="0"/>
            <xs:element name="origNumberOfInstalments" type="xs:int" minOccurs="0"/>
            <xs:element name="origMaxCredit" type="xs:decimal" minOccurs="0"/>
            <xs:element name="internal" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Is internal or external commitment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="origLoanNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Internal loan number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="finInstCode" type="xs:string" minOccurs="0"/>
            <xs:element name="finInstBankCode" type="xs:string" minOccurs="0"/>
            <xs:element name="finInstChargeAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="finInstBufferAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="finInstBufferUsed" type="xs:boolean" minOccurs="0"/>
            <xs:element name="commitmentType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        type of commitment / credit card, cashloan, overdraft
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="CREDIT_CARD"/>
                        <xs:enumeration value="CASHLOAN"/>
                        <xs:enumeration value="OVERDRAFT"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="role" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        type of debt consolidation calculation - prescoring, postscoring
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="PRESCORE"/>
                        <xs:enumeration value="POSTSCORE"/>
                        <xs:enumeration value="BRKI"/>
                        <xs:enumeration value="EXPERT_INCOME"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="manualCheck" type="xs:boolean" minOccurs="0"/>
            <xs:element name="rejected" type="xs:boolean" minOccurs="0"/>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0"/>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0"/>
            <xs:element name="commitmentEstimate" type="CommitmentEstimate" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="pairedContracts" type="xs:string" minOccurs="0"/>
            <xs:element name="instalmentDay" type="xs:int" minOccurs="0"/>
            <xs:element name="creditBureau" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageTranche">
        <xs:sequence>
            <xs:element name="order" type="xs:int"/>
            <xs:element name="type">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="FIRST"/>
                        <xs:enumeration value="SPECIFIC"/>
                        <xs:enumeration value="REPLICATING"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="status">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="DEMO"/>
                        <xs:enumeration value="WAITING"/>
                        <xs:enumeration value="ACTIVE"/>
                        <xs:enumeration value="DRAWN"/>
                        <xs:enumeration value="DELETED"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="utilizationDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>date of utilization</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestedUtilizationDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>requested date of utilization by client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="preDrawdownConditions" type="DrawdownCondition" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgagePurpose">
        <xs:sequence>
            <xs:element name="tranches" type="MortgageTranche" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="postDrawdownConditions" type="DrawdownCondition" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="type">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="PURCHASE"/>
                        <xs:enumeration value="REFINANCING"/>
                        <xs:enumeration value="CONSTRUCTION"/>
                        <xs:enumeration value="RECONSTRUCTION"/>
                        <xs:enumeration value="UNKNOWN"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="label">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="PURPOSE1"/>
                        <xs:enumeration value="PURPOSE2"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DrawdownCondition">
        <xs:sequence>
            <xs:element name="type">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="FIRST"/>
                        <xs:enumeration value="FINAL"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="state">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="PASSED"/>
                        <xs:enumeration value="FAILED"/>
                        <xs:enumeration value="UNKNOWN"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="order" type="xs:int"/>
            <xs:element name="name" type="xs:string" minOccurs="0"/>
            <xs:element name="code" type="xs:string" minOccurs="0"/>
            <xs:element name="note" type="xs:string" minOccurs="0"/>
            <xs:element name="contractText" type="xs:string" minOccurs="0"/>
            <xs:element name="conditionDays" type="xs:int" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageCommitment">
        <xs:sequence>
            <xs:element name="orderNumberId" type="xs:long" minOccurs="0"/>
            <xs:element name="quantification" type="xs:decimal" minOccurs="0"/>
            <xs:element name="quantificationDate" type="xs:date" minOccurs="0"/>
            <xs:element name="finInstCode" type="xs:string" minOccurs="0"/>
            <xs:element name="commitmentType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        type of commitment / mortgage loan, construction savings loan
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="MORTGAGE_LOAN"/>
                        <xs:enumeration value="CONSTRUCTION_SAVINGS_LOAN"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="pairedContracts" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageStageHistory">
        <xs:sequence>
            <xs:element name="stage" type="MortgageStageType">
                <xs:annotation>
                    <xs:documentation>Stav žádosti</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="date" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>Datum přechodu</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CommitmentEstimate">
        <xs:sequence>
            <!-- Estimated values based on BRKI or client data -->
            <xs:element name="estimatedTotalAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="estimatedInstAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="estimatedRpsn" type="xs:decimal" minOccurs="0"/>
            <xs:element name="estimatedUnpaidPrincipal" type="xs:decimal" minOccurs="0"/>
            <xs:element name="estimatedRemainingInstallments" type="xs:int" minOccurs="0"/>
            <xs:element name="estimatedReserveAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="role" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        type of debt consolidation calculation - prescoring, postscoring
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="PRESCORE"/>
                        <xs:enumeration value="POSTSCORE"/>
                        <xs:enumeration value="BRKI"/>
                        <xs:enumeration value="EXPERT_INCOME"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LoanData">
        <xs:sequence>
            <xs:element name="loanAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="loanAmountSimulation" type="xs:decimal" minOccurs="0"/>
            <xs:element name="instalment" type="xs:decimal" minOccurs="0"/>
            <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="idProductVariant" type="xs:long" minOccurs="0"/>
            <xs:element name="productVariant" type="xs:string" minOccurs="0"/>
            <xs:element name="evalMaxRepaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="evalMinRepaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="loanApproveStatus" type="cl:ApplicationApprovalStatus" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Status of product request approval</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0"/>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestForLoan">
        <xs:annotation>
            <xs:documentation>request for loan</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="idAccountApplication" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                account application id
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coupledAccountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                account id
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                výše úvěru
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountSim" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                původní výše úvěru
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalment" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>splátka</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="repaymentPeriod" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                délka splácení
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="payDay" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                den splácení
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstInstalmentDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                datum první splátky
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="paymentSchedule" type="PaymentSchedule" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                varianty splácení od nejhorší k nejlepší
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationDecision" type="ApplicationDecision">
                        <xs:annotation>
                            <xs:documentation>
                                výsledek rozhodnutí ke konkrétní žádosti
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                pojmenování úvěru
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="walkin" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                je součástí o GC
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="charges" type="LoanCharges" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                poplatky
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="business" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                úvěr pro firemní účely
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="productVariantCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                kód produktové varianty
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idTargetAccount" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                identifikátor účtu v OBS pro načerpání
                                úvěru
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idTargetEnvelope" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                identifikátor obálky v OBS pro načerpání
                                úvěru
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idPrecontractDocumentPdf" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                identifikátor předsmluvního formuláře
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idPrecontractDocumentHtml" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                identifikátor předsmluvního formuláře
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idTargetTransaction" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                id transakce v systemu
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idTargetPlan" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                id finančního planu v systemu
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusLevel" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                počet variant
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="internalCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="idProductVariant" type="xs:long" minOccurs="0"/>
                    <xs:element name="dateOfUtilization" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                date of utilization
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanApplicationType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                type of loan - normal, refinance, consolidation
                            </xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:enumeration value="NORMAL"/>
                                <xs:enumeration value="REFINANCE"/>
                                <xs:enumeration value="CONSOLIDATION"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:element name="brkiCalled" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                priznak, jestli byl poslan dotaz do BRKI
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idWorkFlow" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                scoring workflow id (maximal workflow ID with status = VERIFICATION )
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationOffer" type="ConsolidationOffer" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="loanData" type="LoanData" minOccurs="0"/>
                    <xs:element name="sameApplicationData" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                priznak editace aplikacnich dat klientem, false - pokud byla aplikacni data editovana
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="originalApplicationId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>
                                This attribute is used to pair alternative applications, that should be reported as a single application to BRKI (and this is
                                the BRKI identifier)
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request type (currently there are only 2 possible values: INHERITANCE_REF and MANUAL_CONSOLIDATION)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request description.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="deceasedCuid" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Cuid of deceased customer in case of INHERITANCE_REF requestType</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="RequestForAuthReset">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="signatureType" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>BLUE_SIGN or SIGNPAD. The method that client used to sign the application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="newUsername" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>New username that client defined.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="newPhone" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>New phone number that client defined.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idSignedDocument" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>OBS ID of the PDF document that was signed by client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="PaymentSchedule">
        <xs:annotation>
            <xs:documentation>payment schedule</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="orderNo" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        OBS product order number
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanInterest" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>urok</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rpsn" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="minRpsn" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxRpsn" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalAmount" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>kolik zaplatí</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastInstalmentDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        posledni splatka
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfInstalments" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>pocet splatek</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="minTotalAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="maxTotalAmount" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PermissionSettingsTO">
        <xs:annotation>
            <xs:documentation>current access rights for RS</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="generalPermission">
                <xs:annotation>
                    <xs:documentation>general access - access to all entities of specific type</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="loansAllowed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>view on all loans (regardless of the accessibility of payment account)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountsAllowed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>access to all accounts (also future)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documentOrganizerAllowed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>access to document organizer</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="accountPermission">
                <xs:annotation>
                    <xs:documentation>access to the accounts; is filled even if access is granted to all accounts</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="idBankAccount" type="xs:long"
                                    minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>primary key of bank account</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmlCodeList">
        <xs:annotation>
            <xs:documentation>list of codes from MDM code list</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="code" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>code from MDM code list</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RequiredDocument">
        <xs:annotation>
            <xs:documentation>Allowed distribution for applications set.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="productType" type="ProductType">
                <xs:annotation>
                    <xs:documentation>Definition of product type group (DEPOSIT or LOAN)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanBinFrom" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan amount from which is document required.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentCount" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>count of required documents of current document type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroupCount" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>count of required documents from document group.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroupDetail" type="DocumentGroupDetail" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>information about association to employment (for which income type is document required)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentDeliveryWay" type="DeliveryWay">
                <xs:annotation>
                    <xs:documentation>information about channel, that is allowed as delivery channel for required document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentType" type="xs:string"/>
            <xs:element name="documentGroup" type="DocumentGroup"/>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="modifOperator" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AllowedChannel">
        <xs:sequence>
            <xs:element name="deliveryWay" type="DeliveryWay">
                <xs:annotation>
                    <xs:documentation>allowed channel for documentation delivery to Air/Bank</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="signatureWay" type="SignatureWay">
                <xs:annotation>
                    <xs:documentation>allowed channel for signing contract/supplement.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmountTo" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>permission for channel, only if cash loan amount is below this value. Valid only for loan applications.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productType" type="ProductType">
                <xs:annotation>
                    <xs:documentation>information about product type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="documentType" type="DocumentType" minOccurs="0"/>
            <xs:element name="modifOperator" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="DocumentGroup">
        <xs:annotation>
            <xs:documentation>document group, enumeration.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDRESS"/>
            <xs:enumeration value="BUSINESS"/>
            <xs:enumeration value="FINANCIAL"/>
            <xs:enumeration value="IDENTIFICATION"/>
            <xs:enumeration value="JOINT_ASSETS"/>
            <xs:enumeration value="MOBILE_PHONE"/>
            <xs:enumeration value="NODEBT"/>
            <xs:enumeration value="OTHER"/>
            <xs:enumeration value="REMOTE_IDENTIFICATION"/>
            <xs:enumeration value="CO_DEBTORS"/>
            <xs:enumeration value="MORTGAGE_LOAN"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="DocumentGroupDetail">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRIMARY"/>
            <xs:enumeration value="SECONDARY"/>
            <xs:enumeration value="MAIN_INCOME"/>
            <xs:enumeration value="OTHER_INCOME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ProductType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="DEPOSIT"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="AFFIDAVIT"/>
            <xs:enumeration value="CONTRACT_SUPPLEMENT"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="INSURANCE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="DeliveryWay">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IB"/>
            <xs:enumeration value="BRANCH"/>
            <xs:enumeration value="POST"/>
            <xs:enumeration value="MESSENGER"/>
            <xs:enumeration value="ICC"/>
            <xs:enumeration value="SPB"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SignatureWay">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BLUE_SIGN"/>
            <xs:enumeration value="CALL_ID"/>
            <xs:enumeration value="PWD_OTP"/>
            <xs:enumeration value="SIGNPAD"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Filter">
        <xs:sequence>
            <xs:element name="productTypes" type="ProductType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Same value is used for filtering allow channels and required document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentTypes" type="DocumentType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>For filtering allow channels. For example only for GENERAL_CONTRACT.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveryWays" type="DeliveryWay" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>For filtering allow channels. For example only for IB.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Same value is used for filtering allow channels and required document. Value can be for main debtor or co-debtor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanBinRestriction" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>For allow channels it consider loanAmountTo to filter and for required document it consider loanbinFrom for filtering
                        documents.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="onlyLiveApplication" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Active application which is not deleted, canceled, rejected or its approve status is not REJECTED, FI_CANCEL and
                        CLIENT_CANCEL.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RequestForInsurance">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="insurance" type="InsuranceTO" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Insurance.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForTravelInsurance">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="firstName" type="xs:string" minOccurs="0"/>
                    <xs:element name="lastName" type="xs:string" minOccurs="0"/>
                    <xs:element name="birthDate" type="xs:date" minOccurs="0"/>
                    <xs:element name="insuranceNumber" type="xs:string"/>
                    <xs:element name="startDate" type="xs:date"/>
                    <xs:element name="endDate" type="xs:date"/>
                    <xs:element name="coverageLevel" type="xs:string"/>
                    <xs:element name="region" type="xs:string"/>
                    <xs:element name="supplement" type="TravelInsuranceSupplementTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Travel insurance supplements. If any.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuredPerson" type="TravelInsuranceInsuredPersonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Persons travelling with the client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForPersonalDataChange">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForInvestmentContract">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="investmentAccount" type="xs:string" minOccurs="0"/>
                    <xs:element name="assetAccount" type="xs:long" minOccurs="0"/>
                    <xs:element name="investmentContractNumber" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForPension">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="pensionContractNumber" type="xs:string" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForPensionBeneficiary">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="share" type="xs:decimal" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="TravelInsuranceSupplementTO">
        <xs:annotation>
            <xs:documentation>Travel insurance supplment - lawyer, luggage, ...</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="code" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Travel Insurance supplement type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="priceForClient" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Price for the client</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="TravelInsuranceInsuredPersonTO">
        <xs:annotation>
            <xs:documentation>Person travelling with the client.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="firstName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Travel Insurance supplement type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Traveler's last name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthDate" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Person (traveler) birth date.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="RequestForInsuranceChange">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="currentInsurances" type="InsuranceTO" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Current insurances.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="newInsurances" type="InsuranceTO" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>New insurances.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InsuranceTO">
        <xs:annotation>
            <xs:documentation>Insurance.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="InsuranceTypeTO">
                <xs:annotation>
                    <xs:documentation>Insurance type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="variant" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Insurance variant - code from INS pricelist.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="InsuranceTypeTO">
        <xs:annotation>
            <xs:documentation>Insurance type enum.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="SICK_LEAVE_AND_JOB_LOSS"/>
            <xs:enumeration value="SICK_LEAVE"/>
            <xs:enumeration value="DEATH_AND_TOTAL_PERMANENT_DISABILITY"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ApplicationDataRequiredTypeTO">
        <xs:annotation>
            <xs:documentation>Type of application data type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FULL"/>
            <xs:enumeration value="LIMITED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="RequestForInsuranceCancel">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="canceledInsuranceId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Identification of insurance to cancel</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cancelationDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>Cancelation date</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForOverdraft">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="idAccount" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>account id</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="overdraftAmount" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>výše KTK</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestedOverdraftAmount" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>pozadovana vyse KTK</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationDecision" type="ApplicationDecision">
                        <xs:annotation>
                            <xs:documentation>
                                výsledek rozhodnutí ke konkrétní žádosti
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idPrecontractDocumentPdf" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>identifikátor předsmluvního formuláře</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idPrecontractDocumentHtml" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>identifikátor předsmluvního formuláře</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="internalCode" type="xs:string" minOccurs="0"/>
                    <xs:element name="brkiCalled" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                priznak, jestli byl poslan dotaz do BRKI
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idWorkFlow" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                scoring workflow id (maximal workflow ID with status = VERIFICATION )
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="sameApplicationData" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                priznak editace aplikacnich dat klientem, false - pokud byla aplikacni data editovana
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request type (currently there are only 2 possible values: INHERITANCE_REF and MANUAL_CONSOLIDATION)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request description.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="annualPercentageRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Annual percentage rate (RPSN)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Interest rate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationDataRequiredType" type="ApplicationDataRequiredTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Type of application data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForTerminateOverdraft">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <xs:element name="terminateReason" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>reason of termination</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="idOverdraft" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>ID of terminated overdraft</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="IncomeTimeTypeTO">
        <xs:annotation><xs:documentation>Type of income time</xs:documentation></xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="SHORT">
                <xs:annotation><xs:documentation>Time of income is short (less that X years)</xs:documentation></xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LONG">
                <xs:annotation><xs:documentation>Time of income is long (more that X years)</xs:documentation></xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="RequestForPaymentProtectionInsurance">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
                <xs:sequence>
                    <!-- TODO XR-6882 - amount, coverage... -->
                    <xs:element name="test" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForTerminatePaymentProtectionInsurance">
        <xs:complexContent>
            <xs:extension base="RequestForProduct">
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
</xs:schema>
