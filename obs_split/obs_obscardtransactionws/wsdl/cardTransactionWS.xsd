<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/cardTransactionWS"
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:ct="http://airbank.cz/obs/ws/cardTransaction">

    <import namespace="http://airbank.cz/obs/ws/cardTransaction" schemaLocation="../xsd/cardTransaction.xsd" />

    <element name="bookComplaintRequest">
        <complexType>
            <sequence>
                <element name="transaction" type="ct:Transaction">
                    <annotation>
                        <documentation>Podvojný pohyb na účtu (transakce).</documentation>
                    </annotation>
                </element>

                <element name="businessProcessEvent" type="string">
                        <annotation>
                            <documentation>Omezený seznam položek číselníku BusinessProcessEvent pro proces COMPLAINT.</documentation>
                        </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="bookComplaintResponse">
        <complexType>
            <sequence>
                <element name="transactionId" type="long">
                        <annotation>
                            <documentation>Referenční číslo transakce zobrazované na výpisech, v IB nebo MA. Jednoznačně identifikuje transakci.
                                           Pozn. Pokud v případě storna nedojde ke stornování realizované transakce, ale k ukončení ještě nerealizované požadované transakce, systém vrátí id ukončené požadované transakce.</documentation>
                        </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="cashInSettlementRequest">
        <complexType>
            <sequence>
                <element name="idRecord" type="long">
                    <annotation>
                        <documentation>
                            Unique identifier of this data set.
                            Used to silently ignore duplicity requests (idempotence).
                        </documentation>
                    </annotation>
                </element>
                <element name="settlementId" type="string">
                    <annotation>
                        <documentation>
                            Settlement payment ID for payment of the liability by the ATM owner (acquirer) to the card issuer (issuer),
                            in the form "YYMMDDnnn0" where "YYMMDD" is the Business Day, "nnn" - Sequence day number, "0" - RFU.
                            Acquirer shall use the "variable symbol" in the payment.
                        </documentation>
                    </annotation>
                </element>
                <element name="issuer" type="string">
                    <annotation>
                        <documentation>
                            CNB bank code of issuer.
                        </documentation>
                    </annotation>
                </element>
                <element name="acquirer" type="string">
                    <annotation>
                        <documentation>
                            CNB bank code of acquirer.
                        </documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>
                            The currency code of the settled transactions. Currently only CZK.
                        </documentation>
                    </annotation>
                </element>
                <element name="totalAmount" type="decimal">
                    <annotation>
                        <documentation>
                            The total amount of the ATM owner's (acquirer) liability to the card issuer (issuer).
                            The sum of the amounts of all "CI" transactions for a given combination of acquirer and issuer.
                        </documentation>
                    </annotation>
                </element>
                <element name="totalCount" type="long">
                    <annotation>
                        <documentation>
                            Total number of transactions in the ATM owner's (acquirer) commitment to the card issuer.
                            The number of CI transactions for the given combination of acquirer and issuer.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="cashInSettlementResponse">
        <complexType>
            <sequence/>
        </complexType>
    </element>

</schema>
