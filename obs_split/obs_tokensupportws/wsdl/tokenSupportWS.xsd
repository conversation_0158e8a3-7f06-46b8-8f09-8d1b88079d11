<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://airbank.cz/obs/ws/tokenSupportWS">

  <xsd:element name="chargeFeeRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="chargeType" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Typ účtovaného poplatku</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="HWTVK">
                <xsd:annotation>
                  <xsd:documentation>poplatek za vydání podpisové karty</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="HWTVNK">
                <xsd:annotation>
                  <xsd:documentation>poplatek za vydání náhradní podpisové karty</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="HWTVOK">
                <xsd:annotation>
                  <xsd:documentation>poplatek za vydání obnovené podpisové karty</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="chargeDate" type="xsd:date" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Datum zaúčtování poplatku. Musí být => sysdate.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="serialNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Sériové číslo HW tokenu</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>identifier of client</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="chargeFeeResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" minOccurs="1" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Úspěšné zpracování (SUCCESS) nebo kód chyby</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="SUCCESS">
                <xsd:annotation>
                  <xsd:documentation>Požadavek úspěšně zpracován</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="GENERAL_UNSPECIFIED_ERROR">
                <xsd:annotation>
                  <xsd:documentation>Ostatní blíže nespecifikované</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="ELEMENT_NOT_FOUND">
                <xsd:annotation>
                  <xsd:documentation>Problém s neexistencí záznamu v OBS odpovídajícího vstupním datům (nebyla nalezena požadovaná entita).</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="INVALID_PARAMETER">
                <xsd:annotation>
                  <xsd:documentation>Chybná nebo neočekávaná hodnota vstupního parametru (chybný nebo chybně zpracovaný datový typ, nebo hodnota nebo kód mimo očekávaný rozsah či seznam).</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="COULD_NOT_CREATE_TRN">
                <xsd:annotation>
                  <xsd:documentation>Nelze založit požadovanou transakci.</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
              <xsd:enumeration value="INVALID_IDPERSON">
                <xsd:annotation>
                  <xsd:documentation>Neexistující nebo chybné ID osoby.</xsd:documentation>
                </xsd:annotation>
              </xsd:enumeration>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
