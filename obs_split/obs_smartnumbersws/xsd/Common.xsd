<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: 1b97d8306f4f1f6a1774b29c839b9cc3707fa12b $ -->
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/" elementFormDefault="qualified">

	<complexType name="ErrorItemType">
		<sequence>
			<element name="errorCode" type="string" minOccurs="1" maxOccurs="1">
				<annotation>
					<documentation>Kód chyby</documentation>
				</annotation>
			</element>

			<element name="errorAttr" type="string" minOccurs="0" maxOccurs="1">
				<annotation>
					<documentation>
						Ke kterému atributu se chyba váže.
					</documentation>
				</annotation>
			</element>
			<element name="errorValue" type="string" maxOccurs="unbounded"
				minOccurs="0"></element>
			<element name="clearable" type="boolean" minOccurs="0" maxOccurs="1">
				<annotation>
					<documentation>Checks that raise this error can be skipped.</documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>

	<complexType name="ErrorsListType">
		<annotation>
			<documentation>Seznam chyb</documentation>
		</annotation>
		<sequence>
			<element name="errorItem" type="tns:ErrorItemType" minOccurs="0"
				maxOccurs="unbounded"></element>
		</sequence>
	</complexType>

  <element name="ErrorsListType" type="tns:ErrorsListType" />


	<complexType name="CommonResponse" abstract="true">
		<annotation>
			<documentation>
				Předek pro výstupní zprávy, které obsahují pole pro vrácení
				chyb.
			</documentation>
		</annotation>
		<sequence>
			<element name="error" type="tns:ErrorsListType" maxOccurs="unbounded"
				minOccurs="1">
				<annotation>
					<documentation>
						Seznam chyb
          </documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>

	<simpleType name="ActionType">
		<annotation>
			<documentation>Výčet typů akcí</documentation>
		</annotation>
		<restriction base="string">
			<enumeration value="TRNSFR">
				<annotation>
					<documentation>
						Platební příkazy (mimo platebního příkazu v
						rámci svých vlastních účtů)
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="LOGIN">
				<annotation>
					<documentation>Přihlášení zákazníka</documentation>
				</annotation>
			</enumeration>
			<enumeration value="TRNSFR_OWN">
				<annotation>
					<documentation>
						Nový jednorázový platební příkaz - v rámci svých
						vlastních účtů
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="OPERLOGIN">
				<annotation>
					<documentation>
						Přihlášení zaměstnance
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SIGN_GC">
				<annotation>
					<documentation>
						Podpis rámcové smlouvy
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CHANGEPASSWD">
				<annotation>
					<documentation>
						Změna hesla pro přihlášení
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="RESETPASSWORD">
				<annotation>
					<documentation>Reset hesla</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CHANGEPRIMPHONE">
				<annotation>
					<documentation>
						Změna mobilního telefonu
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SIGN_SUP">
				<annotation>
					<documentation>
						Podepsání dodatku k rámcové smlouvě
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SIGN_AFF">
				<annotation>
					<documentation>
						Podpis prohlášení disponenta/držitele
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SIGN_MOB">
				<annotation>
					<documentation>Podpis mobility</documentation>
				</annotation>
			</enumeration>
			<enumeration value="PERSDATACHANGE">
				<annotation>
					<documentation>Změna osobních údajů</documentation>
				</annotation>
			</enumeration>
			<enumeration value="DISPLAYPIN">
				<annotation>
					<documentation>Zobrazení PIN</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SENDCARD">
				<annotation>
					<documentation>
						Zaslání náhradní karty
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CHANGECARDLIM">
				<annotation>
					<documentation>Změna limitů na DK</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CHANGEACCLIM">
				<annotation>
					<documentation>
						Změna transakčních limitů na BÚ/HYSA
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SIGN_SUP_DD">
				<annotation>
					<documentation>
						Podpis dodatku disponenta / držitele
					</documentation>
				</annotation>
			</enumeration>
			<enumeration value="EXTRALOANINSTALMENT">
				<annotation>
					<documentation>podpis mimořádné splátky, mimořádného splacení</documentation>
				</annotation></enumeration>
			<enumeration value="PREMATURELOANINST">
				<annotation>
					<documentation>podpis předčasného úplného splacení úvěru</documentation>
				</annotation></enumeration>
      <enumeration value="SIGN_REG">
        <annotation>
          <documentation>souhlas s úvěrovými registry</documentation>
        </annotation></enumeration>
      <enumeration value="DEVICEREGISTER">
        <annotation>
          <documentation>registrace mobilního zařízení</documentation>
        </annotation></enumeration>
      <enumeration value="DEVICECHANGE">
        <annotation>
          <documentation>změna nastavení mobilního zařízení</documentation>
        </annotation></enumeration>
      <enumeration value="SIGN_SAZKA">
        <annotation>
          <documentation>podpis pro vygenerování sazka kódu</documentation>
        </annotation></enumeration>
      <enumeration value="DISPLAY_SAZKA">
        <annotation>
          <documentation>zobrazení sazka kódu</documentation>
        </annotation>
      </enumeration>
      <enumeration value="CARD_NFC_DIGITIZE">
        <annotation>
          <documentation>digitalizace karty</documentation>
        </annotation>
      </enumeration>
      <enumeration value="MOBILE_AUTH_PIN_SET">
        <annotation>
          <documentation>změna podpisového PINu</documentation>
        </annotation>
      </enumeration>
      <enumeration value="MOBILE_AUTH_PIN_VER">
        <annotation>
          <documentation>ověření podpisového PINu</documentation>
        </annotation>
      </enumeration>
      <enumeration value="CHANGELOANDATE">
          <annotation>
              <documentation>změna data splátky</documentation>
          </annotation>
      </enumeration>
			<enumeration value="CHANGEPERMISSION">
				<annotation>
					<documentation>změna rozsahu dispozičních práv</documentation>
				</annotation>
			</enumeration>
			<enumeration value="RESETSECELEMENT">
				<annotation>
					<documentation>reset bezpečnostních obrázů/otázek</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CONFIRMPASSWORD">
				<annotation>
					<documentation>confirmation password on leadpages</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CHANGEINSURANCE">
				<annotation>
					<documentation>A change made on insurance (except cancellation)</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CREATECLAIM">
				<annotation>
					<documentation>Create claim for insurance indemnity</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SET_SEC_ELEMENT_1">
				<annotation>
					<documentation>Změna autorizační metody - krok 1</documentation>
				</annotation>
			</enumeration>
			<enumeration value="SET_SEC_ELEMENT_2">
				<annotation>
					<documentation>Změna autorizační metody - krok 2</documentation>
				</annotation>
			</enumeration>
			<enumeration value="PERSONAL_DATA_CHANGE">
                <annotation>
                    <documentation>změna osobních údajů</documentation>
                </annotation>
			</enumeration>
			<enumeration value="TERMINATE_OVERDRAFT">
				<annotation>
					<documentation>Zruseni kontokorentu</documentation>
				</annotation>
			</enumeration>
			<enumeration value="CONFIRMPHONENUMBER">
				<annotation>
					<documentation>Ověření telefonního čísla v leadpages</documentation>
				</annotation>
			</enumeration>
		</restriction>
	</simpleType>

	<element name="actionType" type="tns:ActionType" />

	<complexType name="SecurityContext">
		<annotation>
			<documentation>Obsah předávaný v hlavičce každé volané WS metody.
				Určuje kontext pro OBS.</documentation>
		</annotation>
		<sequence>
			<element type="long" name="cuid" minOccurs="0"
				maxOccurs="1">
				<annotation>
					<documentation>id klienta</documentation>
				</annotation>
			</element>
			<element type="string" name="trackingID" minOccurs="0" maxOccurs="1" />
			<element type="string" name="operatorCUID" minOccurs="0" maxOccurs="1">
				<annotation>
					<documentation>id operátora</documentation>
				</annotation>
			</element>
			<element type="string" name="languageIsoCode" maxOccurs="1"
				minOccurs="0">
				<annotation>
					<documentation>
						ISO kód jazyka. Např. cs pro češtinu.
					</documentation>
				</annotation>
			</element>
			<element name="channelCode" minOccurs="0" maxOccurs="1">
				<annotation>
					<documentation>
						Kód kanálu.

						IB - veřejný normální IB BRANCH - tento kód se
						použije pouze v případě, že klient je na pobočce
						a je zároveň přihlášen pobočník !!! Pokud klient
						pracuje na pobočce a není přihlášen s
						pobočníkem, potom se používá kód IB !!! ECC -
						externí call centrum
						SPB - smart phone banking
					</documentation>
				</annotation>
				<simpleType>
					<restriction base="string">
						<enumeration value="IB">
							<annotation>
								<documentation>IB - veřejný normální IB</documentation></annotation></enumeration>
						<enumeration value="BRANCH">
							<annotation>
								<documentation>tento kód se
						použije pouze v případě, že klient je na pobočce
						a je zároveň přihlášen pobočník !!! Pokud klient
						pracuje na pobočce a není přihlášen s
						pobočníkem, potom se používá kód IB !!!</documentation></annotation></enumeration>
						<enumeration value="ECC">
							<annotation>
								<documentation>externí call centrum</documentation></annotation></enumeration>
						<enumeration value="SPB">
							<annotation>
								<documentation>smart phone banking</documentation></annotation></enumeration>
					</restriction>
				</simpleType>
			</element>
			<element type="long" name="idProfile" minOccurs="0"
				maxOccurs="1">
				<annotation>
					<documentation>
						id profilu - id vazby mezi osobou a rámcovou
						službou - získá se z objektu
						GeneralContractTO.idProfile
					</documentation>
				</annotation>
			</element>
			<element name="certificateId" type="string" minOccurs="0"
				maxOccurs="1">
				<annotation>
					<documentation>
						ID certifikátu pobočky
					</documentation>
				</annotation>
			</element>
			<element name="branchCode" type="string" maxOccurs="1"
				minOccurs="0">
				<annotation>
					<documentation>Kód pobočky</documentation>
				</annotation>
			</element>
			<element name="authType" type="string" maxOccurs="1"
				minOccurs="0">
				<annotation>
					<documentation>
						Kód autorizačního typu - slouží pouze pro
						potřeby logování na OSB - OBS ho ignoruje
					</documentation>
				</annotation>
			</element>
			<element name="UUID" type="string" maxOccurs="1"
				minOccurs="0">
				<annotation>
					<documentation>
						jednoznační identifikátor - pro potřeby logování
						na OSB - OBS ignoruje
					</documentation>
				</annotation>
			</element>
			<element name="getAuthTypeTime" type="dateTime"
				maxOccurs="1" minOccurs="0">
				<annotation>
					<documentation>
						čas volání metody getAuthType - pro potřeby
						logování na OSB - OBS ignoruje
					</documentation>
				</annotation>
			</element>
			<element name="installationID" type="string" maxOccurs="1" minOccurs="0">
				<annotation>
					<documentation>jednoznačná identifikace mobilního zařízení - posílá se vždy pro kanál SPB</documentation>
				</annotation>
			</element>
			<element name="system" type="string" maxOccurs="1" minOccurs="0">
				<annotation>
					<documentation>Označení systému, který volá tuto službu</documentation>
				</annotation>
			</element>
		</sequence>
	</complexType>

  <element name="SecurityContext" type="tns:SecurityContext" />

  <complexType name="AdditionalInfoTO">
    <annotation>
      <documentation>Additional information for mobile application frontend purposes.</documentation>
    </annotation>
    <all>
      <element name="sourceBankAccountNumber" type="string" minOccurs="0" maxOccurs="1">
        <annotation>
          <documentation>Source bank account number.</documentation>
        </annotation>
      </element>
      <element name="sourceBankAccountName" type="string" minOccurs="0" maxOccurs="1">
        <annotation>
          <documentation>Source bank account name.</documentation>
        </annotation>
      </element>
    </all>
  </complexType>

  <complexType name="SupplierId">
    <annotation>
      <documentation>identification of supplier</documentation>
    </annotation>
    <sequence>
      <element name="type">
        <simpleType>
          <restriction base="string">
            <enumeration value="CLIENT" />
            <enumeration value="BANKING" />
          </restriction>
        </simpleType>
      </element>
      <element name="identifier" type="string">
      </element>
      <element name="identifierType">
        <simpleType>
          <restriction base="string">
            <enumeration value="IDENTIFICATION_NUMBER" />
            <enumeration value="TAX_IDENTIFICATION_NUMBER" />
          </restriction>
        </simpleType>
      </element>
      <element name="country">
        <simpleType>
          <restriction base="string">
            <enumeration value="CZ" />
            <enumeration value="SK" />
          </restriction>
        </simpleType>
      </element>
    </sequence>
  </complexType>
</schema>
