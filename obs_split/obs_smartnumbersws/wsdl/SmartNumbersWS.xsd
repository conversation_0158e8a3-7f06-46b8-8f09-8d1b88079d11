<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/SmartNumbersWS/"
    xmlns:sn="http://airbank.cz/obs/ws/SmartNumbers"
    targetNamespace="http://airbank.cz/obs/ws/SmartNumbersWS/">

    <import namespace="http://airbank.cz/obs/ws/SmartNumbers" schemaLocation="../xsd/SmartNumbers.xsd"/>

    <complexType name="LoanInfo">
        <sequence>
            <element name="idLoan" type="long">
                <annotation>
                    <documentation>ID půjčky</documentation>
                </annotation>
            </element>
            <element name="loanNumber" type="string">
                <annotation>
                    <documentation><PERSON><PERSON><PERSON></documentation>
                </annotation>
            </element>
            <element name="loanName" type="string">
                <annotation>
                    <documentation>N<PERSON><PERSON>v p<PERSON></documentation>
                </annotation>
            </element>
            <element name="remainingInstallmentCount" type="long">
                <annotation>
                    <documentation>Počet zbývajících splátek</documentation>
                </annotation>
            </element>
            <element name="remainingPayment" type="decimal">
                <annotation>
                    <documentation>Zbývající částka k doplacení půjčky</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>Kód měny</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MortgageInfo">
        <sequence>
            <element name="idMortgage" type="long">
                <annotation>
                    <documentation>ID hypotéky</documentation>
                </annotation>
            </element>
            <element name="MortgageNumber" type="string">
                <annotation>
                    <documentation>Číslo hypotéky</documentation>
                </annotation>
            </element>
            <element name="MortgageName" type="string">
                <annotation>
                    <documentation>Název hypotéky</documentation>
                </annotation>
            </element>
            <element name="remainingInstallmentCount" type="long">
                <annotation>
                    <documentation>Počet zbývajících splátek</documentation>
                </annotation>
            </element>
            <element name="remainingPayment" type="decimal">
                <annotation>
                    <documentation>Zbývající částka k doplacení hypotéky</documentation>
                </annotation>
            </element>
            <element name="currencyCode" type="string">
                <annotation>
                    <documentation>Kód měny</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <element name="getMainAccountDispoRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>ID bankovního účtu</documentation>
                    </annotation>
                </element>
                <element name="processDateTime" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Datum a čas, ke kterému se má vrátit zůstatek na účtu.
                            Pokud není vyplněno, bere se aktuální čas.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getMainAccountDispoResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="accountBalance" type="decimal">
                    <annotation>
                        <documentation>Dostupný zůstatek na účtu bez kontokorentu (KTK) k danému datumu a času</documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getMainAccountSmartDispoRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>ID bankovního účtu</documentation>
                    </annotation>
                </element>
                <element name="intervalFrom" type="dateTime">
                    <annotation>
                        <documentation>
                            Začátek období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="intervalTo" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Konec období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="paymentCycle" type="long" minOccurs="0">
                    <annotation>
                        <documentation>
                            Pořadové číslo dne v kalendářním měsíci, které se má použít jako konec sledovaného období.
                            Předpoklad je, že si klient bude jako toto datum nastavovat den výplaty.
                        </documentation>
                    </annotation>
                </element>
                <element name="includingOwnAccountTransfer" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda se mají započítávat i převody mezi vlastními účty.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getMainAccountSmartDispoResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="futureDateTime" type="dateTime">
                    <annotation>
                        <documentation>
                            Datum konce období (datum včetně časové složky), ke kterému je proveden výpočet.
                            Určuje se na základě vstupních parametrů intervalTo a paymentCycle.
                        </documentation>
                    </annotation>
                </element>
                <element name="futurePaymentCount" type="long">
                    <annotation>
                        <documentation>
                            Počet všech plánovaných plateb k futureDateTime
                        </documentation>
                    </annotation>
                </element>
                <element name="futureBalance" type="decimal">
                    <annotation>
                        <documentation>
                            Předpokládaný (vypočtený) zůstatek na účtu klienta k futureDateTime
                        </documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getMainAccountFuturePaymentsRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>ID bankovního účtu</documentation>
                    </annotation>
                </element>
                <element name="intervalFrom" type="dateTime">
                    <annotation>
                        <documentation>
                            Začátek období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="intervalTo" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Konec období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="paymentCycle" type="long" minOccurs="0">
                    <annotation>
                        <documentation>
                            Pořadové číslo dne v kalendářním měsíci, které se má použít jako konec sledovaného období.
                            Předpoklad je, že si klient bude jako toto datum nastavovat den výplaty.
                        </documentation>
                    </annotation>
                </element>
                <element name="includingOwnAccountTransfer" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda se mají započítávat i převody mezi vlastními účty.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getMainAccountFuturePaymentsResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="futureDateTime" type="dateTime">
                    <annotation>
                        <documentation>
                            Datum konce období (datum včetně časové složky), ke kterému je proveden výpočet.
                            Určuje se na základě vstupních parametrů intervalTo a paymentCycle.
                        </documentation>
                    </annotation>
                </element>
                <element name="futurePaymentCount" type="long">
                    <annotation>
                        <documentation>
                            Počet všech plánovaných plateb k futureDateTime
                        </documentation>
                    </annotation>
                </element>
                <element name="futurePaymentSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma všech plánovaných plateb k futureDateTime
                        </documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getMainAccountCardTransactionsRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>ID bankovního účtu</documentation>
                    </annotation>
                </element>
                <element name="idCard" type="long" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Seznam ID karet</documentation>
                    </annotation>
                </element>
                <element name="intervalFrom" type="dateTime">
                    <annotation>
                        <documentation>
                            Začátek období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="intervalTo" type="dateTime">
                    <annotation>
                        <documentation>
                            Konec období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getMainAccountCardTransactionsResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="activeCardCount" type="long">
                    <annotation>
                        <documentation>
                            Počet aktivních platebních karet
                        </documentation>
                    </annotation>
                </element>
                <element name="cardPaymentCount" type="long">
                    <annotation>
                        <documentation>
                            Počet plateb kartami za dané období
                        </documentation>
                    </annotation>
                </element>
                <element name="cardPaymentSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma všech plateb kartami za dané období
                        </documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getMainAccountOutgoingTransactionsRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>ID bankovního účtu</documentation>
                    </annotation>
                </element>
                <element name="intervalFrom" type="dateTime">
                    <annotation>
                        <documentation>
                            Začátek období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="intervalTo" type="dateTime">
                    <annotation>
                        <documentation>
                            Konec období (datum včetně časové složky), za které se provádí výpočet.
                        </documentation>
                    </annotation>
                </element>
                <element name="includingOwnAccountTransfer" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda se mají započítávat i převody mezi vlastními účty.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getMainAccountOutgoingTransactionsResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="outgoingPaymentCount" type="long">
                    <annotation>
                        <documentation>
                            Počet všech odchozích plateb za dané období
                        </documentation>
                    </annotation>
                </element>
                <element name="outgoingPaymentSum" type="decimal">
                    <annotation>
                        <documentation>
                            Suma všech odchozích plateb za dané období
                        </documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="evaluateLastMonthExpensesRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="idBankAccount" type="long">
                    <annotation>
                        <documentation>ID bankovního účtu</documentation>
                    </annotation>
                </element>
                <element name="processDateTime" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Datum a čas, ke kterému se má provést výpočet útraty.
                            Pokud není vyplněno, bere se aktuální čas.
                            Ukázky algoritmu výpočtu:
                            1) Datum není konec měsíce - např. 20.04.:
                               Porovnáme vždy shodné období, tedy 01.04-20.04. oproti 01.03.-20.03.
                            2) Datum je konec měsíce, předchozí měsíc má delší období než aktuální - např. 30.4. (duben 30 dnů vs březen 31 dnů):
                               Porovnáme vždy shodné období, tedy 01.04.-30.04. oproti 01.03.-30.03.
                            3) Datum je konec měsíce, předchozí měsíc má kratší období než aktuální - např. 31.05. (květen 31 dnů vs duben 30 dnů):
                               Porovnáme vždy celé kalendářní měsíce, tedy 01.05.-31.05. oproti 01.04.-30.04.
                        </documentation>
                    </annotation>
                </element>
                <element name="includingOwnAccountTransfer" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda se mají započítávat i převody mezi vlastními účty.
                        </documentation>
                    </annotation>
                </element>
                <element name="onlyCardTransactions" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda se mají započítávat pouze karetní transakce.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="evaluateLastMonthExpensesResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="previousMonthExpensesAmount" type="decimal">
                    <annotation>
                        <documentation>Útrata za předchozí období</documentation>
                    </annotation>
                </element>
                <element name="lastMonthExpensesAmount" type="decimal">
                    <annotation>
                        <documentation>Útrata za poslední období</documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getAllAccountsDispoRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="generalContractId" type="long">
                    <annotation>
                        <documentation>ID rámcové smlouvy</documentation>
                    </annotation>
                </element>
                <element name="processDateTime" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Datum a čas, ke kterému se má vrátit kumulativní dostupný zůstatek všech účtů (BÚ a SÚ) pod rámcovou smlouvou (RS).
                            Pokud není vyplněno, bere se aktuální čas.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getAllAccountsDispoResponse">
        <complexType>
            <sequence>
                <element name="available" type="boolean">
                    <annotation>
                        <documentation>
                            Příznak, zda je služba platná/přístupná pro klienta.
                        </documentation>
                    </annotation>
                </element>
                <element name="accountCount" type="long">
                    <annotation>
                        <documentation>
                            Počet účtů (BÚ a SÚ) pod rámcovou smlouvou (RS)
                        </documentation>
                    </annotation>
                </element>
                <element name="cumulativeBalance" type="decimal">
                    <annotation>
                        <documentation>
                            Kumulativní dostupný zůstatek všech účtů (BÚ a SÚ) pod rámcovou smlouvou (RS) k danému datumu a času v měně hlavního účtu
                        </documentation>
                    </annotation>
                </element>
                <element name="currencyCode" type="string">
                    <annotation>
                        <documentation>Kód měny</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getLoansInfoRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="generalContractId" type="long">
                    <annotation>
                        <documentation>ID rámcové smlouvy</documentation>
                    </annotation>
                </element>
                <element name="processDateTime" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Datum a čas, ke kterému se má vrátit zůstatek a počet zbývajících splátek půjčky.
                            Pokud není vyplněno, bere se aktuální čas.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getLoansInfoResponse">
        <complexType>
            <sequence>
                <element name="loanInfo" type="tns:LoanInfo" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Informace o půjčce</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>


    <element name="getMortgagesInfoRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long">
                    <annotation>
                        <documentation>ID profilu</documentation>
                    </annotation>
                </element>
                <element name="generalContractId" type="long">
                    <annotation>
                        <documentation>ID rámcové smlouvy</documentation>
                    </annotation>
                </element>
                <element name="processDateTime" type="dateTime" minOccurs="0">
                    <annotation>
                        <documentation>
                            Datum a čas, ke kterému se má vrátit zůstatek a počet zbývajících splátek hypotéky.
                            Pokud není vyplněno, bere se aktuální čas.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getMortgagesInfoResponse">
        <complexType>
            <sequence>
                <element name="mortgageInfo" type="tns:MortgageInfo" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Informace o hypotéce</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getContractParametersOverviewRequest">
        <complexType>
            <sequence>
                <element name="profileId" type="long"/>
            </sequence>
        </complexType>
    </element>

    <element name="getContractParametersOverviewResponse">
        <complexType>
            <sequence>
                <element name="account" type="sn:Account" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Seznam vsech klientskych bezaku a sporaku</documentation>
                    </annotation>
                </element>
                <element name="activationDate" type="dateTime" minOccurs="0"/>
                <element name="overdueDebt" type="boolean">
                    <annotation>
                        <documentation>Příznak, zda je na smlouvě evidován dluh po splatnosti</documentation>
                    </annotation>
                </element>
                <element name="cashLoan" type="sn:Loan" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Seznam vsech aktivnich hotovostnich pujcek</documentation>
                    </annotation>
                </element>
                <element name="mortgage" type="sn:Loan" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Seznam vsech aktivnich hypotek</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
