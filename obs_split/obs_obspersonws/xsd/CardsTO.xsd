<?xml version="1.0" encoding="UTF-8"?>
<schema elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
           xmlns="http://www.w3.org/2001/XMLSchema"
           xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/"
           xmlns:Q2="http://arbes.com/ib/core/ppf/ws/AccountSettingsTO"
           xmlns:ct="http://airbank.cz/obs/ws/cardTransaction">

    <import schemaLocation="cardTransaction.xsd" namespace="http://airbank.cz/obs/ws/cardTransaction"></import>

    <complexType name="Authorization153">
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="idCard" type="long"/>
                    <element name="idAccount" type="long"/>
                    <element name="transactionType" type="string">
                        <annotation>
                            <documentation>Typ transakce: 1 - normal
                                                             2 - reversal
                                                             3 - advise normal
                                                             4 - manual cancellation
                                                             5 - automatic cancellation
                                                             6 - advise reversal
                                                             8 - release due to complaint
                                                             9 - revoke release due to complaint
                            </documentation>
                        </annotation>
                    </element>
                    <element name="transactionCode" type="string">
                        <annotation>
                            <documentation>ISO code of processing</documentation>
                        </annotation>
                    </element>
                    <element name="amountAccount" type="decimal">
                        <annotation>
                            <documentation>Částka v měně účtu karty</documentation>
                        </annotation>
                    </element>
                    <element name="currencyAccount" type="string">
                        <annotation>
                            <documentation>Currency of card account which is joined to debit card.</documentation>
                        </annotation>
                    </element>
                    <element name="transactionClass" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Transaciton Type class = 'RTL', 'ATM', ...</documentation>
                        </annotation>
                    </element>
                    <element name="country" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Země obchodníka</documentation>
                        </annotation>
                    </element>
                    <element name="termOwner" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Hodnota atributu vyplněná v atributu TERM_OWNER-FIID položky P-60 autorizační zprávy. Tento atribut je používán k
                                rozpoznání, zda-li se jedná o transakci uskutečněné na cizím nebo na ATM banky.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="terminalCountry" type="string" minOccurs="0">
                        <annotation>
                            <documentation>The attribute is filled in by TERMINAL_COUNTRY from item P-43 (ISO2 alpha code)</documentation>
                        </annotation>
                    </element>
                    <element name="productIndicator" minOccurs="0">
                        <annotation>
                            <documentation>
                                Product indicator definuje typ
                                autorizační zprávy (00 – network
                                management messages, 01 – Base24 ATM, 02
                                – Base24 POS). Na OBS budou předávány
                                pouze 01 a 02 typy zpráv.
                            </documentation>
                        </annotation>
                        <simpleType>
                            <restriction base="string">
                                <length value="2"/>
                                <enumeration value="01"/>
                                <enumeration value="02"/>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="checkBalance" type="int" minOccurs="0">
                        <annotation>
                            <documentation>Hodnota je podstatná pro typ autorizace &amp;quot;normal autorizace (kód 1)
                                1 - pro autorizaci se ověřuje balance
                                0 - pro autorizaci se neověřuje balance
                            </documentation>
                        </annotation>
                    </element>
                    <element name="mcc" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Merchant Category Code</documentation>
                        </annotation>
                    </element>
                    <element name="captureDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Capture date (P17)</documentation>
                        </annotation>
                    </element>
                    <element name="idInstallation" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Device installation ID</documentation>
                        </annotation>
                    </element>
                    <element name="tokenUniqueReference" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Token unique reference</documentation>
                        </annotation>
                    </element>
                    <element name="tokenWallet" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Token wallet code - AIRBANK, APPLE_PAY, ...</documentation>
                        </annotation>
                    </element>
                    <element name="tokenDeviceName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Token device name for non AIRBANK token</documentation>
                        </annotation>
                    </element>
                    <element name="idAccMoveToReplace" type="long" minOccurs="0">
                        <annotation>
                            <documentation>Identification of the previous card hold to be replaced by this adjustment/completion.</documentation>
                        </annotation>
                    </element>
                    <element name="complaintTicketId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Identification of the complaint. Business Jira ticket id.</documentation>
                        </annotation>
                    </element>
                    <element name="cardAcceptorIDCode" type="string" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="Transact155">
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="idCard" type="long"/>
                    <element name="processingCode" type="string"/>
                    <element name="amountAccount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Nepoužívat, bude až pro kreditní karty.</documentation>
                        </annotation>
                    </element>
                    <element name="currencyAccount"
                                type="string">
                        <annotation>
                            <documentation>Nepoužívat, bude až pro kreditní karty.</documentation>
                        </annotation>
                    </element>
                    <element name="amountSett" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>
                                Částka v zúčtovací měně (settlement amount)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="currencySett" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Zúčtovací měna (settlement currency).
                            </documentation>
                        </annotation>
                    </element>
                    <element name="transactType" type="string">
                        <annotation>
                            <documentation>
                                Transaction type • C – electronic
                                transaction (Cash Advance) • P –
                                electronic transaction (POS) • A –
                                electronic transaction (ATM)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="incomingFileID"
                                type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                ID of incoming file (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="regionGroup" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Group of transaction (clearing) There
                                could be these values here: • ONUS - own
                                ATM • ENES, INET - foreign ATM
                            </documentation>
                        </annotation>
                    </element>
                    <element name="mcc" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Merchant category gcode (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="approvalCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Approval code (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardAcceptorTerminalID"
                                type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Acceptor of the cards (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardAcceptorIDCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Acceptor of the cards (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="country" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Country of acceptor of the cards (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="street" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Street of acceptor of the cards (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="postalCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Postal code of acceptor of the cards (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="ATMOwnerCategory" type="Q1:ATMOwnerCategoryEnum">
                      <annotation>
                          <documentation>Kategorie vlastníků bankomatů (ATM owner category)
                              AB - ATM Air Bank
                              alliance - other
                          </documentation>
                      </annotation>
                    </element>
                    <element name="complaintRefund" type="ct:ComplaintRefund" minOccurs="0">
                        <annotation>
                            <documentation>Instruction for immediate payment of the presentment specified in the card payment complaint.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="TransactCommon">
        <annotation>
            <documentation>ID transaction canceled</documentation>
        </annotation>
        <sequence>
            <element name="idAccMove" type="long" minOccurs="0">
                <annotation>
                    <documentation>Transaction ID, Pro Presentment159 je prázdné.</documentation>
                </annotation>
            </element>
            <element name="idAccMoveCanceled" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID transaction canceled, Pro Presentment159 je prázdné.</documentation>
                </annotation>
            </element>
            <element name="transactionDate" type="dateTime">
                <annotation>
                    <documentation>Transaction date and time.</documentation>
                </annotation>
            </element>
            <element name="amountTrans" type="decimal">
                <annotation>
                    <documentation>
                        Amount draw in currency of drawing.
                    </documentation>
                </annotation>
            </element>
            <element name="currencyTrans" type="string">
                <annotation>
                    <documentation>
                        Currency of drawing
                    </documentation>
                </annotation>
            </element>
            <element name="merchantName" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        Name of acceptor of the cards (clearing)
                    </documentation>
                </annotation>
            </element>
            <element name="city" type="string" minOccurs="0">
                <annotation>
                    <documentation>City of acceptor of the cards (clearing)</documentation>
                </annotation>
            </element>
            <element name="state" type="string" minOccurs="0">
                <annotation>
                    <documentation>State of acceptor of the cards (clearing)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="Event189">
        <sequence>
            <element name="idAccount" type="long">
                <annotation>
                    <documentation>ID credit</documentation>
                </annotation>
            </element>
            <element name="eventType" type="string">
                <annotation>
                    <documentation>Typ události:
                        • BLOCK_CARD - blocking of the card
                        • UNBLOCK_CARD - unblocking of the card
                        • RENEWAL_CARD - in case of extedning of expiration date of card
                        • automatic_renewal_new_card - automatic new card renew
                        • automatic_renewal_original_card - automatic original card number renewal
                        • REPLACEMENT_CARD – in case of issuing replacement card
                        • CHANGE_STATUS - změna stav
                        • UPDATE_CARD - aktualizace objektu karty (vstup je eventValue = PaymentCard
                        • CARD_ISSUED - first plastic of this card was successfully handed over to production
                    </documentation>
                </annotation>
            </element>
            <element name="eventDate" type="dateTime"
                       >
                <annotation>
                    <documentation>Datum události</documentation>
                </annotation>
            </element>
            <element name="accountNumber" type="string">
                <annotation>
                    <documentation>Číslo účtu</documentation>
                </annotation>
            </element>
            <element name="idCard" type="long"/>
            <element name="eventValue" type="Q1:EventValue">
                <annotation>
                    <documentation>Hodnota závislá na typu zprávy.</documentation>
                </annotation>
            </element>
            <element name="reason" type="string" minOccurs="0">
                <annotation>
                    <documentation>určuje důvod pro volání daného eventu určeno BlockInfo.reason</documentation>
                </annotation>
            </element>
            <element name="seqNumber" type="long">
                <annotation>
                    <documentation>Sekvencni cislo udalosti pro danou kartu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
    <complexType name="LimitsPaymentCard">
        <sequence>
            <element name="limitRegisters" type="Q1:LimitRegister" maxOccurs="unbounded">
                <annotation>
                    <documentation>Limity pro kartu</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
    <complexType name="LimitRegister">
        <sequence>
            <element name="limitRegisterCode"  type="string">
                <annotation>
                    <documentation>Limity pro kartu.</documentation>
                </annotation>

            </element>
            <element name="limits" type="Q1:limit" maxOccurs="2"/>
        </sequence>
    </complexType>
    <complexType name="limit">
        <sequence>
            <element name="limitTypeCode" type="string"/>
            <element name="amount" type="decimal" minOccurs="0">
                <annotation>
                    <documentation>Výše limitu. Povoluje se NULL value.

                        Význam NULL value jako vstupní hodnoty pro změnu limitu.
                        Zrušení dočasného limitu (chce se, aby platil jen transakční) lze provést uvedením příslušného dočasného
                        limitu s explicitně uvedenou NULL hodnotou. Pokud se nechce limit měnit, vůbec se v
                        requestu neposílá.
                    </documentation>
                </annotation>
            </element>
            <element name="actualAmount" type="decimal">
                <annotation>
                    <documentation>Suma prostředků k čerpání (zbytek z limitu)</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="CashIn153">
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="idCard" type="long"></element>
                    <element name="transactionType" type="string"/>
                    <element name="prefixAccountNumber" type="int" minOccurs="0"/>
                    <element name="accountNumber" type="long"/>
                    <element name="variableSymbol" type="long" minOccurs="0"/>
                    <element name="constSymbol" type="int" minOccurs="0"/>
                    <element name="specifySymbol" type="long" minOccurs="0"/>
                    <element name="note1" type="string" minOccurs="0"/>
                    <element name="note2" type="string" minOccurs="0"/>
                    <element name="note3" type="string" minOccurs="0"/>
                    <element name="note4" type="string" minOccurs="0"/>
                    <element name="cardAcceptorTerminalID">
                        <simpleType>
                            <restriction base="string">
                                <minLength value="1"/>
                                <maxLength value="8"/>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="acquirer" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                ČNB kód banky, na jejímž ATM vklad proběhl.
                                Pokud element chybí, jde o ONUS vklad.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CashIn155">
        <annotation>
            <documentation>ID příjemce karet</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="idCard" type="long"/>
                    <element name="prefixAccountNumber" type="int" minOccurs="0"/>
                    <element name="accountNumber" type="long"/>
                    <element name="variableSymbol" type="long" minOccurs="0"/>
                    <element name="constSymbol" type="int" minOccurs="0"/>
                    <element name="specifySymbol" type="long" minOccurs="0"/>
                    <element name="approvalCode" type="string" minOccurs="0"/>
                    <element name="cardAcceptorTerminalID" type="string" minOccurs="0"/>
                    <element name="country" type="string" minOccurs="0"/>
                    <element name="street" type="string" minOccurs="0"/>
                    <element name="postalCode" type="string" minOccurs="0"/>
                    <element name="CardAcceptorIDCode" type="string" minOccurs="0"/>
                    <element name="retrievalReferenceNumber">
                        <annotation>
                            <documentation>. Identifikátor transakce přidělený terminálem pro jednoznačné dohledání transakčních dokladů nebo jejich kopií,
                                maximální délka 12 znaků dle manuálů karetních asociací. Reversal message musí mít shodnou hodnotu jako normal message.
                            </documentation>
                        </annotation>
                        <simpleType>
                            <restriction base="string">
                                <maxLength value="12"></maxLength>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="acquirer" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Kód banky, na jejímž ATM vklad proběhl.
                                Pokud element chybí, jde o ONUS vklad.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="settlementId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Identifikátor platby, kterou vyrovná acquirerská banka svůj závazek issuerské bance.
                                Pokud element chybí, jde o ONUS vklad.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="Presentment159">
        <annotation>
            <documentation>
                Účetní položka presentmentu karty (naší i cizí) na našem ATM.
            </documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="idCard" type="long" minOccurs="0"/>
                    <element name="idRecord" type="long"></element>
                    <element name="idRecordCanceled" type="long"/>
                    <element name="transactionType" type="string"/>
                    <element name="cardAcceptorTerminalID" type="string"/>
                    <element name="country" type="string"/>
                    <element name="street" type="string"/>
                    <element name="postalCode" type="string"/>
                    <element name="direction" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                N – normal transaction, R – reversal
                                transaction
                            </documentation>
                        </annotation>
                    </element>
                    <element name="informationStage" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                L – log, C – clearing, pro vlastní
                                klienty pouze L
                            </documentation>
                        </annotation>
                    </element>
                    <element name="retrievalReferenceNumber">
                        <annotation>
                            <documentation>
                                . Identifikátor transakce přidělený
                                terminálem pro jednoznačné dohledání
                                transakčních dokladů nebo jejich kopií,
                                maximální délka 12 znaků dle manuálů
                                karetních asociací. Reversal message
                                musí mít shodnou hodnotu jako normal
                                message.
                            </documentation>
                        </annotation>
                        <simpleType>
                            <restriction base="string">
                                <maxLength value="12"></maxLength>
                            </restriction>
                        </simpleType>
                    </element>
                    <element name="outgoingFileID" type="string" minOccurs="0"/>
                    <element name="association" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                hodnoty
                                MC - asociace MasterCard, VISA - asociace Visa
                            </documentation>
                        </annotation>
                    </element>
                    <element name="clearingDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>datum odeslání do clearingu</documentation>
                        </annotation>
                    </element>
                    <element name="group" type="string" minOccurs="0">
                        <annotation>
                            <documentation>skupina z GPE (ONUS, CZEK, VISA, ...)</documentation>
                        </annotation>
                    </element>
                    <element name="reconciliationAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Částka v měně rekonciliace</documentation>
                        </annotation>
                    </element>
                    <element name="reconciliationCurrency" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Měna rekonciliace</documentation>
                        </annotation>
                    </element>
                    <element name="dccAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>
                                Částka v měně karty cizozemce. (Částka, která v autorizaci putuje do banky vydavatele cizozemce.)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="dccCurrency" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Měna karty cizozemce. (Měna, která v autorizaci putuje do banky vydavatele cizozemce.)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="surcharge" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Příplatek účtovaný k výběru cizí kartou pokud není použito DCC. Pro starší verzi ATLF není naplňován. Měna je dána TransactCommon.currencyTrans. Normal operace má kladné znaménko, reversal záporné.</documentation>
                        </annotation>
                    </element>
                    <element name="issuer" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Kód banky, jejíž kartou vklad proběhl.
                                Pokud element chybí, nejedná se o vklad.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="settlementId" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Identifikátor platby, kterou vyrovná acquirerská banka svůj závazek issuerské bance.
                                Pokud element chybí, nejedná se o vklad.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="RenewalCard">
        <complexContent>
            <extension base="Q1:EventValue">
                <sequence>
                    <element name="cardValidity" type="Q1:CardValidity" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CardValidity">
        <sequence>

            <element name="validityFrom" type="dateTime"/>
            <element name="validityTo" type="dateTime"/>
            <element name="takeCard" type="dateTime" minOccurs="0"/>
            <element name="designId" type="long" minOccurs="0"/>
            <element name="embossingName" type="string" minOccurs="0"/>
            <element name="distributionDate" type="date"  minOccurs="0">
                <annotation>
                    <documentation>datum odeslání karty klientovi</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>


    <complexType name="EventValue"/>

    <complexType name="BlockCard">
        <annotation>
            <documentation>eventType = BLOCK_CARD</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:EventValue">
                <sequence>
                    <element name="blockId" type="long">
                        <annotation>
                            <documentation>ID blokace</documentation>
                        </annotation>
                    </element>
                    <element name="type" type="int">
                        <annotation>
                            <documentation>0 - klientem
                                1 - bankou
                                2 - vymáhání (v první fázi nepoužito)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="note" type="string" minOccurs="0"/>
                    <element name="personName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Jméno a příjmení 3. osoby</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="UnblockCard">
        <annotation>
            <documentation>eventType = UNBLOCK_CARD</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:EventValue">
                <sequence>
                    <element name="blockId" type="long">
                        <annotation>
                            <documentation>ID blokace, která byla odblokována</documentation>
                        </annotation>
                    </element>
                    <element name="note" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Poznámka k odblokaci</documentation>
                        </annotation>
                    </element>
                    <element name="personName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Jméno a příjmení 3. osoby, která požadovala odblokaci</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ChangeStatus">
        <annotation>
            <documentation>eventType = CHANGE_STATUS</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:EventValue">
                <sequence>
                    <element name="blockId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>ID změny stavu</documentation>
                        </annotation>
                    </element>
                    <element name="status" type="string">
                        <annotation>
                            <documentation>Nový stav karty</documentation>
                        </annotation>
                    </element>
                    <element name="note" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Poznámka ke změně stavu karty</documentation>
                        </annotation>
                    </element>
                    <element name="personName" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Jméno a příjmení 3. osoby, která požadovla změnu stavu karty</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="PaymentCard">
        <annotation>
            <documentation>eventType = REPLACEMENT_CARD eventType = UPDATE_CARD</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:EventValue">
                <sequence>
                    <element name="id" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                ID karty - Jednoznačný identifikátor
                                karty v Homeru.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idPredecessor" type="long"  minOccurs="0">
                        <annotation>
                            <documentation>Číslo karty, kterou tato karta nahradila

                                Developer note:
                                pozor - v CMS kara ví, jakou nahrazuje a ne kterou nahradila idSubst
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idReplacement" type="long"  minOccurs="0">
                        <annotation>
                            <documentation>Číslo karty, která tuto kartu nahrazuje

                                Developer note:
                                pozor - v CMS kara ví, jakou nahrazuje a ne kterou nahradila idSubst
                            </documentation>
                        </annotation>
                    </element>
                    <element name="numberCard" type="string"  minOccurs="0">
                        <annotation>
                            <documentation>cislo karty.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardType" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Typ karty
                            </documentation>
                        </annotation>
                    </element>
                    <element name="status" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Stav karty
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardValidity" type="Q1:CardValidity" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>
                                Aktuální plast karty. Pokud není uveden,
                                karta není vyrobena.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="limitsPaymentCard" type="Q1:LimitsPaymentCard" minOccurs="0">
                        <annotation>
                            <documentation>
                                Limity na kartě. NULL hodnoty jsou povoleny.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="blockInfos" type="Q1:BlockInfo" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>
                                Informace o aktuálních blokacích.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cardValidityInSystem" type="int" minOccurs="0">
                        <annotation>
                            <documentation>
                                V případě obyčejné karty jde o
                                monthValidity z typu karty V případě VK
                                jde o dayValidityOfVC z typu karty
                            </documentation>
                        </annotation>
                    </element>
                    <element name="technology" type="string">
                        <annotation>
                            <documentation>
                                mag - magnetická
                                chip - čipová
                                ctls - bezkontaktní
                                virt - virtuální
                                NULL hodnoty jsou povoleny.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="maxLimitAtmUsage" type="int" minOccurs="0">
                        <annotation>
                            <documentation>
                                Maximální počet použití VK za dobu její
                                životnosti.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="limitAtmNumberUsage" type="int" minOccurs="0">
                        <annotation>
                            <documentation>
                                Počet použití karty v ATM
                            </documentation>
                        </annotation>
                    </element>
                    <element name="limitPosNumberUsage" type="int" minOccurs="0">
                        <annotation>
                            <documentation>
                                Počet použití karty v POS
                            </documentation>
                        </annotation>
                    </element>
                    <element name="personType" minOccurs="0" type="int">
                        <annotation>
                            <documentation>
                                Typ osoby držící kartu vzhledem k účtu,
                                ke kterému karta patří. 1 - majitel 2 -
                                disponent 3 - držitel
                            </documentation>
                        </annotation>

                    </element>
                    <element name="renewal" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>
                                Automatická obnova je/není aktivní
                            </documentation>
                        </annotation>

                    </element>
                    <element name="replacementAllowed" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>
                                Informace o tom, zdali k dané kartě je možno vydat kartu náhradní
                                - true (k dané kartě je možno vydat kartu náhradní)/
                                false (u dané karty není možné vydat náhradní kartu)
                                NULL hodnoty jsou povoleny.
                                Poznámka:
                                Náhradní kartu lze vydat, pokud platí všechny tři níže uvedené podmínky
                                - karta není blokovaná
                                - karta není zrušená před více dny než hodnota parametru DaysCanceled2Replace
                                - ke kartě ještě nebyla vydána náhradní karta
                            </documentation>
                        </annotation>
                    </element>
                    <element name="name" minOccurs="0" type="string">
                        <annotation>
                            <documentation>
                                Název karty Max. 25 znaků
                            </documentation>
                        </annotation>

                    </element>
                    <element name="cuid" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                ID držitele karty
                            </documentation>
                        </annotation>
                    </element>
                    <element name="accountNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Číslo účtu, ke kterému byla karta vydána
                            </documentation>
                        </annotation>
                    </element>
                    <element name="remainderLimitATMUsage" type="int" minOccurs="0">
                        <annotation>
                            <documentation>
                                Zůstatek z počtu použití VK (zůstatek z limitu paymentCard.max_limit_atm_usage).
                                Limit ponižuje každá úspěšně autorizovaná transakce na bankomatu.
                                NULL hodnoty jsou povoleny.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="dateOfConfirmation"
                                type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>Datum potvrzení výroby karty (v Homeru datum zařazení požadavku na výrobu karty do fronty k její personalizaci pro
                                odeslání nejbližším EVPK souborem).
                            </documentation>
                        </annotation>
                    </element>
                    <element name="dateOfCancellation" type="dateTime" minOccurs="0">
                        <annotation>
                            <documentation>Datum storna karty.</documentation>
                        </annotation>
                    </element>
                    <element name="reason" nillable="true" type="string">
                        <annotation>
                            <documentation>Jaky si duvod</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="BlockInfo">
        <sequence>
            <element name="blockId" type="long">
                <annotation>
                    <documentation>ID blokace</documentation>
                </annotation>
            </element>
            <element name="blockType" type="string">
                <annotation>
                    <documentation>Důvod (typ) blokace.
                        Kódy obecného číselníku 699
                    </documentation>
                </annotation>
            </element>
            <element name="validFrom" type="dateTime">
                <annotation>
                    <documentation>Blokace od.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="Event189result">
        <sequence>
            <element name="idCardHomer" type="long">
                <annotation>
                    <documentation>ID karty v Homeru</documentation>
                </annotation>
            </element>
            <element name="result" type="string">
                <annotation>
                    <documentation>
                        Kód (atribut result)/ Popis / Číslo

                        Úspěšné zpracování:
                        SUCCESS / Požadavek úspěšně zpracován / 1

                        Warning:
                        IGNORED / Operaci nijak nezpracovavame / 5
                        ALREADY_OK / Operace jiz byla provedena / 10

                        Chyby:
                        GENERAL_UNSPECIFIED_ERROR / Ostatní blíže nespecifikované. / -1
                        ELEMENT_NOT_FOUND / Problém s neexistencí záznamu v OBS odpovídajícího vstupním datům (nebyla nalezena požadovaná entita). / -2
                        INVALID_PARAMETER / Chybná nebo neočekávaná hodnota vstupního parametru (chybný nebo chybně zpracovaný datový typ, nebo hodnota nebo kód
                        mimo očekávaný rozsah či
                        seznam).
                        / -4
                        INVALID_HOMERCARD / Neexistuje ID karty v OBS - jedná se o nekorektně založenou kartu, která neprošla kompletací. / -12
                        INVALID_EVENT_TYPE / Zadaný EventType neodpovídá výčtu hodnot / -47
                        BLOCKAGE_ALLREADY_EXIST / Blokace karty se zadanym 'Blocknumber' jiz existuje. / -45
                        BLOCKAGE_CANNOT_DEBLOCK / Blokaci karty se zadanym 'Blocknumber' bud neexistuje nebo jiz byla deblokovana. / -46
                        COULD_NOT_CREATE_TRN / Nelze založit požadovanou transakci. / -43
                        INVALID_CARD_TYPE / Neexistující nebo chybný typ karty. / -10
                        INVALID_CARD_STATUS / Neexistující nebo chybný stav karty. / -9
                        INVALID_HOMERACCOUNT / Funkce nevrací korektní ID účtu z Homera. / -11
                        INVALID_IDACCOUNT / Neexistující nebo chybné ID bankovního účtu. / -8
                        INVALID_IDPERSON / Neexistující nebo chybné ID osoby. / -7
                        NO_SUCH_PAYMENTCARD / Nenalezena platební karta. / -48
                        REQUEST_OUT_OF_SEQUENCE / Sequence number in request out of sequence for given card / -13
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="Reconciliation157">
        <sequence>
            <element name="idRecord" type="long">
                <annotation>
                    <documentation>
                        ID záznamu – jedná se o jednoznačný
                        identifikátor záznamu ze souboru IISS v Homérovi
                    </documentation>
                </annotation>
            </element>
            <element name="incomingFileID" type="string">
                <annotation>
                    <documentation>
                        Rozlišení, zda posíláme pohyby na nostro účtu za
                        příchozí nebo odchozí soubory. Pro zúčtování
                        ostatním bankám: • 6861 – Acknowledgement
                        reconciliation message Pokud GCM přijme od
                        ostatních členů pro Brusson zúčtovací soubory: •
                        6862 – Notification reconciliation message
                    </documentation>
                </annotation>
            </element>
            <element name="messageReasonCode" type="string">
                <annotation>
                    <documentation>
                        Rozlišení, zda posíláme pohyby na nostro účtu za
                        příchozí nebo odchozí soubory. Pro zúčtování
                        ostatním bankám: • 6861 – Acknowledgement
                        reconciliation message Pokud GCM přijme od
                        ostatních členů pro Brusson zúčtovací soubory: •
                        6862 – Notification reconciliation message
                    </documentation>
                </annotation>
            </element>
            <element name="messageTypeIdentifier" type="string">
                <annotation>
                    <documentation>
                        Message Type Identifier – definuje za jaký typ
                        zpráv je posílán pohyb na nostro účtu 1240 –
                        Presentment + InterchangeFee 1740 – Fee
                        collection 1644 – Retrieval request (nefinanční
                        transakce, není posílána v rámci
                        Reconciliation157) 1442 - Chargeback Umístění
                        hodnoty atributu v tabulce
                        card.mc_financial_position_detail.reconciled_mti
                    </documentation>
                </annotation>
            </element>
            <element name="processingCode" type="string">
                <annotation>
                    <documentation>Procesing code (clearing):
                        00 - Purchase
                        01 - ATM
                        09 - Purchase with cash back
                        12 - Cash Disbursement
                        17 - Convenience Check
                        18 - Unique
                        19 - Fee Collection (Credit to originator)
                        20 - Credit (Purchase Return)
                        28 - Payment Transaction
                        29 - Fee Collection (Debit to originator)
                        50 - Payment/Balance service (Collection only)
                    </documentation>
                </annotation>
            </element>
            <element name="functionCode" type="string">
                <annotation>
                    <documentation>
                        Funkční kód zprávy, tři číslice Funkční kódy pro
                        Presentment 200 – První presentment 205 – Druhý
                        presentment (úplný) 282 – Druhý presentment
                        (částečný) Funkční kódy pro Fee Collection 700 –
                        Fee collection (generovaný členy) 780 – Fee
                        collection navrácení 781 – Fee collection
                        znovuodeslání 783 – Fee collection generovaný
                        GCMS Funkční kódy pro Chargeback 450 – First
                        chargeback (Full) 453 – First chargeback
                        (Partial) 451 – Arbitrážní chargeback (Full) 454
                        – Arbitrážní chargeback (Partial)
                    </documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>
                        Měna vyrovnání 203 – Česká koruna 978 - Euro
                    </documentation>
                </annotation>
            </element>
            <element name="amountDebit" type="decimal">
                <annotation>
                    <documentation>
                        Suma debetních uskutečněných transakcí
                    </documentation>
                </annotation>
            </element>
            <element name="amountCredit" type="decimal">
                <annotation>
                    <documentation>
                        Suma kreditních uskutečněných transakcí (vždy se
                        zápornou hodnotou)
                    </documentation>
                </annotation>
            </element>
            <element name="amountNet" type="decimal">
                <annotation>
                    <documentation>
                        Celková částka vyrovnání (bez interchange fee)
                        reconciliation_net = reconciliation_amount_debit
                        + reconciliation_amount_credit
                    </documentation>
                </annotation>
            </element>
            <element name="feeDebit" type="decimal">
                <annotation>
                    <documentation>
                        Interchange fee, které byly naúčtovány Brussonu
                    </documentation>
                </annotation>
            </element>
            <element name="feeCredit" type="decimal">
                <annotation>
                    <documentation>
                        Interchange fee, které obdrží jako odměnu
                        Brusson
                    </documentation>
                </annotation>
            </element>
            <element name="feeNet" type="decimal">
                <annotation>
                    <documentation>
                        Celková částka k vyrovnání za interchange fee
                    </documentation>
                </annotation>
            </element>
            <element name="businessDate" type="date">
                <annotation>
                    <documentation>
                        Business date vzájemného vyrovnání
                    </documentation>
                </annotation>
            </element>
            <element name="amountTransactionDebit" type="decimal">
                <annotation>
                    <documentation>
                        Umístění hodnoty atributu v tabulce
                        card.mc_financial_position_detail.transaction_amount_debit
                    </documentation>
                </annotation>
            </element>
            <element name="amountTransactionCredit" type="decimal">
                <annotation>
                    <documentation>
                        Umístění hodnoty atributu v tabulce
                        card.mc_financial_position_detail.transaction_amount_credit
                    </documentation>
                </annotation>
            </element>
            <element name="amountTransactionNet" type="decimal">
                <annotation>
                    <documentation>
                        Celková částka za transakce.
                        AmountTransactionNet = AmountTransactionDebit +
                        AmountTransactionCredit Umístění hodnoty
                        atributu v tabulce
                        card.mc_financial_position_detail.transaction_amount_net
                    </documentation>
                </annotation>
            </element>
            <element name="transactionCurrency" type="string">
                <annotation>
                    <documentation>
                        Měna transakcí (v případě zúčtování cizích
                        klientů na našich terminálech je nutné znát
                        korespondující částku v dané měně). ISO číselný
                        kód.
                    </documentation>
                </annotation>
            </element>
            <element name="reconciledFileID" type="string"  minOccurs="0"/>
            <element name="creditTransactionsCount" type="long">
                <annotation>
                    <documentation>počet kreditních transakcí</documentation>
                </annotation>
            </element>
            <element name="debitTransactionsCount" type="long">
                <annotation>
                    <documentation>počet debetních transakcí</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="Reconciliation157VISA">
        <sequence>
            <element name="idRecord" type="long">
                <annotation>
                    <documentation>
                        ID záznamu – jedná se o jednoznačný identifikátor
                        záznamu ze souboru IISS v Homérovi
                    </documentation>
                </annotation>
            </element>
            <element name="currency" type="string">
                <annotation>
                    <documentation>
                        Měna vyrovnání 203 – Česká koruna 978 - Euro
                    </documentation>
                </annotation>
            </element>
            <element name="amountDebit" type="decimal">
                <annotation>
                    <documentation>
                        Suma debetních uskutečněných transakcí
                    </documentation>
                </annotation>
            </element>
            <element name="amountCredit" type="decimal">
                <annotation>
                    <documentation>
                        Suma kreditních uskutečněných transakcí (vždy se zápornou hodnotou)
                    </documentation>
                </annotation>
            </element>
            <element name="amountNet" type="decimal"
                       >
                <annotation>
                    <documentation>
                        Celková částka vyrovnání (bez interchange fee)
                        reconciliation_net = reconciliation_amount_debit +
                        reconciliation_amount_credit
                    </documentation>
                </annotation>
            </element>
            <element name="businessDate" type="date">
                <annotation>
                    <documentation>
                        Business date vzájemného vyrovnání
                    </documentation>
                </annotation>
            </element>
            <element name="amountTransactionDebit" type="decimal">
                <annotation>
                    <documentation>
                        Umístění hodnoty atributu v tabulce
                        card.mc_financial_position_detail.transaction_amount_debit
                    </documentation>
                </annotation>
            </element>
            <element name="amountTransactionCredit" type="decimal">
                <annotation>
                    <documentation>
                        Umístění hodnoty atributu v tabulce
                        card.mc_financial_position_detail.transaction_amount_credit
                    </documentation>
                </annotation>
            </element>
            <element name="amountTransactionNet" type="decimal">
                <annotation>
                    <documentation>
                        Celková částka za transakce. AmountTransactionNet
                        = AmountTransactionDebit + AmountTransactionCredit
                        Umístění hodnoty atributu v tabulce
                        card.mc_financial_position_detail.transaction_amount_net
                    </documentation>
                </annotation>
            </element>
            <element name="transactionCurrency" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        Měna transakcí (v případě zúčtování cizích klientů
                        na našich terminálech je nutné znát korespondující
                        částku v dané měně). ISO číselný kód.
                    </documentation>
                </annotation>
            </element>

            <element name="reportType" type="string">
                <annotation>
                    <documentation>120 - Interchange Value Report
                        130 - Reimbursement Fees Report
                        140 - Visa Charges Report
                    </documentation>
                </annotation>
            </element>
            <element name="businessMode" type="string">
                <annotation>
                    <documentation>Hodnoty atributu v reconciliation 157
                        1 - Acquirer
                        2 - Issuer
                        3 – Other
                        (pozn. v reportech VISA existuje hodnota 9, nicméně to jsou součtové hodnoty, které do OBS nebudou posílány)
                    </documentation>
                </annotation>
            </element>
            <element name="businessTransactionType" type="string">
                <annotation>
                    <documentation>V případě ATM acquirungu, transakcí bude chodit
                        310 - ATM Cash
                        V případě Reimbursement fee to budou kódy
                        850 - ATM Authorization
                        852 - ATM Balance Inquiry
                        853 - ATM Authorization Reversal
                        856 - ATM Decline
                        857 - ATM Transfer
                        V případě fee collection (VISA charges)
                        5xx – Fee collection
                        Např. 504 =&gt; reason code 0150 =&gt; Recovered Card Handling Fees/Rewards
                    </documentation>
                </annotation>
            </element>
            <element name="businessTransactionCycle" type="string">
                <annotation>
                    <documentation>1 Originals
                        2 Chargebacks
                        3 Representments
                        4 Second Chargebacks
                        5 Debit Adjustments
                        6 Credit Adjustments
                    </documentation>
                </annotation>
            </element>
            <element name="clearingDate" type="date">
                <annotation>
                    <documentation>datum odeslání do clearingu</documentation>
                </annotation>
            </element>
            <element name="creditTransactionsCount" type="long">
                <annotation>
                    <documentation>počet kreditních transakcí</documentation>
                </annotation>
            </element>
            <element name="debitTransactionsCount" type="long">
                <annotation>
                    <documentation>počet debetních transakcí</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>


    <complexType name="Transac155WC">
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="accountNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                vyplněno (trn z klientského účtu),
                                nevyplněno (trn z účtu reklamací)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cuid" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                cuid subjektu, který si vygeneroval SK
                            </documentation>
                        </annotation>
                    </element>
                    <element name="sazkaCode" type="string">
                        <annotation>
                            <documentation>
                                použité číslo SK
                            </documentation>
                        </annotation>
                    </element>
                    <element name="incomingFileID" type="string">
                        <annotation>
                            <documentation>
                                ID příchozího souboru (clearing)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="approvalCode" type="string">
                        <annotation>
                            <documentation>
                                autorizační kód
                            </documentation>
                        </annotation>
                    </element>
                    <element name="acceptorTerminalID" type="string">
                        <annotation>
                            <documentation>
                                ID SAZKA terminálu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="street" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                ulice provozovny umístění terminálu kde se výběr uskutečnil
                            </documentation>
                        </annotation>
                    </element>
                    <element name="postalCode" type="string"  minOccurs="0">
                        <annotation>
                            <documentation>PSČ provozovny umístění terminálu kde se výběr uskutečnil</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="Authorization153WC">
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="accountNumber" type="string">
                        <annotation>
                            <documentation>číslu účtu</documentation>
                        </annotation>
                    </element>
                    <element name="generalContractNumber" type="string">
                        <annotation>
                            <documentation>číslo RS</documentation>
                        </annotation>
                    </element>
                    <element name="transactionType" type="string">
                        <annotation>
                            <documentation>
                                Typ transakce (1 - normal, 4-manual
                                cancellation, 5-automatic cancellation)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="amountAccount" type="decimal">
                        <annotation>
                            <documentation>
                                částka transakce v měně účtu. V případě, že je
                                CurrencyTrans shodná s currencyAccount, pak do
                                AmountAccount je vložena částka z atributu
                                AmountTrans. Přepočet do měny účtu je proveden
                                pomocí kurzu deviza nákup
                            </documentation>
                        </annotation>
                    </element>
                    <element name="currencyAccount" type="string">
                        <annotation>
                            <documentation>
                                - měna účtu, ISO třímístný číselný kód měny
                            </documentation>
                        </annotation>
                    </element>
                    <element name="cuid" type="long">
                        <annotation>
                            <documentation>
                                cuid subjektu, který si vygeneroval Sazka kód
                            </documentation>
                        </annotation>
                    </element>

                    <element name="codeID" type="string">
                        <annotation>
                            <documentation>identifikátor sazka kódu z externího systému</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="ATMOwnerCategoryEnum">
        <restriction base="string">
            <enumeration value="AB">
                <annotation>
                    <documentation>bankomat Air Bank</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ALLIANCE">
                <annotation>
                    <documentation>bankomat alliance s AB bez poplatku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="OTHER">
                <annotation>
                    <documentation>bankomat jine banky</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="ChannelCodeType">
        <restriction base="string">
            <enumeration value="IB">
                <annotation>
                    <documentation>IB - veřejný normální IB</documentation>
                </annotation>
            </enumeration>
            <enumeration value="BRANCH">
                <annotation>
                    <documentation>tento kód se
                        použije pouze v případě, že klient je na pobočce
                        a je zároveň přihlášen pobočník !!! Pokud klient
                        pracuje na pobočce a není přihlášen s
                        pobočníkem, potom se používá kód IB !!!
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="ECC">
                <annotation>
                    <documentation>externí call centrum</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SPB">
                <annotation>
                    <documentation>smart phone banking</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>


    <simpleType name="TransactionTypeEnum">
        <restriction base="string">
            <enumeration value="STANDARD">
                <annotation>
                    <documentation>normal</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MANUAL">
                <annotation>
                    <documentation>ruční zrušení VK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AUTOMATIC">
                <annotation>
                    <documentation>automatické zrušení VK</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>
    <simpleType name="reasonNewCard">
        <restriction base="string">
            <enumeration value="NCOBS">
                <annotation>
                    <documentation>Vydání nové karty iniciované z OBS</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>
    <simpleType name="reasonReplacementCard">
        <restriction base="string">
            <enumeration value="RCOBS">
                <annotation>
                    <documentation>Vydání náhradní karty iniciované z OBS</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RC4AR">
                <annotation>
                    <documentation>Vydání náhradní karty iniciované procesem AO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RC4F">
                <annotation>
                    <documentation>Potvrzení zneužití karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RCTL">
                <annotation>
                    <documentation>Po odcizení/ztrátě karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RC4S">
                <annotation>
                    <documentation>Použití karty na bankomatu se skimovacím zařízením</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RC4H">
                <annotation>
                    <documentation>Zadržení karty v ATM</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RCOF">
                <annotation>
                    <documentation>Jiné zneužití a zrušení karty vyžadují chargeback pravidla MC</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RCOR">
                <annotation>
                    <documentation>Jiný důvod vydání náhradní karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RCBM">
                <annotation>
                    <documentation>Hromadné vydání náhradní karty</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>
    <simpleType name="reasonChangeCardStatus">
        <restriction base="string">
            <enumeration value="CCCR4CCA">
                <annotation>
                    <documentation>Zrušení karty na žádost klienta (iniciované žádostí o zrušení karty/účtu)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4WACB">
                <annotation>
                    <documentation>Zrušení karty z důvodů odstoupení od účtu klientem/bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4CHH">
                <annotation>
                    <documentation>Zrušení karty z důvodu zrušení držitele držitelem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4CC">
                <annotation>
                    <documentation>Zrušení karty z důvodu stornování kompletace DK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4RC">
                <annotation>
                    <documentation>Zrušení karty z důvodu zamítnutí kompletace</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4F">
                <annotation>
                    <documentation>Potvrzení zneužití karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCTL">
                <annotation>
                    <documentation>Po odcizení/ztrátě karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4SIFU">
                <annotation>
                    <documentation>Podezření OKAC na záměrné podvodné používání karty klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4S">
                <annotation>
                    <documentation>Použití karty na bankomatu se skimovacím zařízením</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4H">
                <annotation>
                    <documentation>Zadržení karty v ATM</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCOF">
                <annotation>
                    <documentation>Jiné zneužití a zrušení karty vyžadují chargeback pravidla MC</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCOR">
                <annotation>
                    <documentation>Jiný důvod zrušení karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4WCB">
                <annotation>
                    <documentation>Zrušení karty při odstoupení od smlouvy ze strany banky</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4DC">
                <annotation>
                    <documentation>Úmrtí klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCCE">
                <annotation>
                    <documentation>Exspirace karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARCAR">
                <annotation>
                    <documentation>Zrušení karty z důvodu aktivace náhradní karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCARC">
                <annotation>
                    <documentation>Zrušení původní karty z důvodu aktivace náhradní karty mimo AO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4CRC">
                <annotation>
                    <documentation>Zrušení karty z důvodů vytvoření náhradní karty (stávající karta ve stavech n,r,u,d) mimo AO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CC4CRCAR">
                <annotation>
                    <documentation>Zrušení původni karty založením náhradní karty v procesu AO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CCNAC">
                <annotation>
                    <documentation>Zrušení karty z důvodu neaktivování nově vydané karty</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="AccountingType">
        <restriction base="string">
            <enumeration value="TEMP_CREDIT">
                <annotation>
                    <documentation>Chargeback amount will be temporarily paid to the client</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREDIT_CLIENT">
                <annotation>
                    <documentation>Accepted chargeback that was not reimbursed yet will be paid to the client</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DEBIT_CLIENT">
                <annotation>
                    <documentation>Temporarily paid but rejected chargeback will be withholded from the client</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_DEBIT_CLIENT">
                <annotation>
                    <documentation>Temporarily paid but rejected chargeback will be left to the client</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREDIT_CLIENT_LOST_CHB">
                <annotation>
                    <documentation>Disburse from bank expenses</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHARGEBACK_NORMAL">
                <annotation>
                    <documentation>Chargeback was sent in the outgoing file</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHARGEBACK_REVERSAL">
                <annotation>
                    <documentation>Chargeback reversal was sent in the outgoing file</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REPRESENTMENT_NORMAL">
                <annotation>
                    <documentation>Representment was received in the incoming file</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REPRESENTMENT_REVERSAL">
                <annotation>
                    <documentation>Representment reversal was received in the incoming file</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DISPUTE">
                <annotation>
                    <documentation>Payment for dispute has been received from MC</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHB">
                <annotation>
                    <documentation>Obsolete, use CHARGEBACK_NORMAL instead</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHB_CANCELED">
                <annotation>
                    <documentation>Obsolete, use CHARGEBACK_REVERSAL instead</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHB_REJECTED">
                <annotation>
                    <documentation>Obsolete, use REPRESENTMENT_NORMAL instead</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHB_REJECTEDCANCELED">
                <annotation>
                    <documentation>Obsolete, use REPRESENTMENT_REVERSAL instead</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="TransactionTypeCHBK">
        <restriction base="string">
            <enumeration value="1CHB">
                <annotation>
                    <documentation>First chargeback</documentation>
                </annotation>
            </enumeration>
            <enumeration value="1CHB_P">
                <annotation>
                    <documentation>First chargeback (partial)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="2CHB">
                <annotation>
                    <documentation>Second chargeback</documentation>
                </annotation>
            </enumeration>
            <enumeration value="2CHB_P">
                <annotation>
                    <documentation>Second chargeback (partial)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="2PR">
                <annotation>
                    <documentation>Second presentment</documentation>
                </annotation>
            </enumeration>
            <enumeration value="2PR_P">
                <annotation>
                    <documentation>Second presentment (partial)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PA">
                <annotation>
                    <documentation>Pre-arbitration (full)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PA_P">
                <annotation>
                    <documentation>Pre-arbitration (partial)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="A">
                <annotation>
                    <documentation>Arbitration (full)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="A_P">
                <annotation>
                    <documentation>Arbitration (partial)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="Chargeback158">
        <complexContent>
            <extension base="Q1:TransactCommon">
                <sequence>
                    <element name="idAccount" type="long" minOccurs="0">
                        <annotation>
                            <documentation>Account ID in CMS</documentation>
                        </annotation>
                    </element>
                    <element name="idCard" type="long" minOccurs="0">
                        <annotation>
                            <documentation>Card ID in CMS</documentation>
                        </annotation>
                    </element>
                    <element name="idAccMoveOriginal" type="long" minOccurs="0">
                        <annotation>
                            <documentation>ID of original (1.presentment) transaction</documentation>
                        </annotation>
                    </element>
                    <element name="accounting" type="Q1:AccountingType">
                        <annotation>
                            <documentation>Accounting type</documentation>
                        </annotation>
                    </element>
                    <element name="amountSett" type="decimal">
                        <annotation>
                            <documentation>Amount in settlement currency</documentation>
                        </annotation>
                    </element>
                    <element name="currencySett" type="string">
                        <annotation>
                            <documentation>Settlement currency ISO code</documentation>
                        </annotation>
                    </element>
                    <element name="transactionType" type="Q1:TransactionTypeCHBK">
                        <annotation>
                            <documentation>Transaction type</documentation>
                        </annotation>
                    </element>
                    <element name="incomingFileID" type="string" minOccurs="0">
                        <annotation>
                            <documentation>ID of incoming clearing file</documentation>
                        </annotation>
                    </element>
                    <element name="outgoingFileID" type="string" minOccurs="0">
                        <annotation>
                            <documentation>ID of outgoing clearing file</documentation>
                        </annotation>
                    </element>
                    <element name="association" type="string">
                        <annotation>
                            <documentation>Card association (MC, VISA)</documentation>
                        </annotation>
                    </element>
                    <element name="clearingDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Transaction was processed in clearing center at this date</documentation>
                        </annotation>
                    </element>
                    <element name="regionGroup" type="string">
                        <annotation>
                            <documentation>ONUS, CZEK, ENES, INET, VISA</documentation>
                        </annotation>
                    </element>
                    <element name="complaintMatchingSymbol" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Matching symbol for COMPLAINT account</documentation>
                        </annotation>
                    </element>
                    <element name="settlementMatchingSymbol" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Matching symbol for SETTLEMENT account</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="EnrichedTransaction">
        <sequence>
            <element name="accMoveId" type="long">
                <annotation>
                    <documentation>ID of acc move in CMS.</documentation>
                </annotation>
            </element>

            <element name="cashbackAmount" type="decimal">
                <annotation>
                    <documentation>How much the client saved in redemption, in CZK.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

</schema>
