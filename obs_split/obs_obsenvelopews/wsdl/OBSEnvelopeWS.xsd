<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsEnvelopeWS/"
    xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:Q2="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"/>
    <xsd:import schemaLocation="../xsd/EnvelopeTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
    <xsd:element name="getEnvelopeRequest">
        <xsd:complexType>
            <xsd:choice>

                <xsd:element name="filter" type="Q2:SelectFilter" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Filtrace podle:
                            Nepovinná pole:
                            idBankAccount - long - id bankovního účtu, pro který se vrátí kolekce obálek , možno použít operátory =, in
                            idEnvelope - long - id obálky, služba vrátí konkrétní obálku

                            řazení vždy primárně dle jména účtu, sekundárně dle jména obálky

                            Všechny atributy filtru mají mezi sebou vazbu AND
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
         </xsd:complexType>
    </xsd:element>
    <xsd:element name="getEnvelopeResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="account" type="Q1:AccountEnvelopeTO" maxOccurs="unbounded" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="setEnvelopeRequest">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="envelope" type="Q1:EnvelopeTo"/>

            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="setEnvelopeResponse">

        <xsd:complexType>
            <xsd:sequence>
                 <xsd:element name="idEnvelope" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>ID vytvořené obálky.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="deleteEnvelopeRequest">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="envelopeID" type="xsd:long"/>
                <xsd:element name="targetEnvelopeID" type="xsd:long" minOccurs="0"/>

            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="deleteEnvelopeResponse">
        <xsd:complexType>
            <xsd:sequence>

            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getEnvelopeBalanceHistoryRequest">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="filter" type="Q2:SelectFilter">
                    <xsd:annotation>
                        <xsd:documentation>Filtrování
                            Povinná pole:
                            dateFrom - date (možné operátory: =) - Počáteční datum intervalu, za který jsou požadovány zůstatky.
                            dateTo - date (možné operátory: =) - Konečné datum intervalu, za který jsou požadovány zůstatky.
                            idEnvelope - long (možné operátory: =) - id obálky.

                            Všechny atributy filtru mají mezi sebou vazbu AND
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>

            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getEnvelopeBalanceHistoryResponse">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="balance" type="Q1:EnvelopeBalanceTo" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="transferBetweenEnvelopesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="idEnvelopeFrom" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            ID obálky, ze které se přesouvají peníze. Pokud není vyplněno, jedná se o převod z Volných prostředků.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="idEnvelopeTo" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            ID obálky, do které se přesouvají peníze. Pokud není vyplněno, jedná se o převod do Volných prostředků.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="amount" type="xsd:decimal" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Částka, která se přesouvá mezi obálkami. Pokud není vyplněno, jedná se o převod všech pěnez ze zdrojové obálky do cílové obálky.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="transferBetweenEnvelopesResponse">
        <xsd:complexType />
    </xsd:element>

</xsd:schema>
