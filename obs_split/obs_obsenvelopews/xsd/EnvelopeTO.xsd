<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/" xmlns="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" version="1.0"
	xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">

    <complexType name="EnvelopeTo">
      <sequence>
        <element name="idBankAccount" type="long">
          <annotation>
            <documentation>ID účtu, na který je obálka navázána.</documentation>
          </annotation>
        </element>
        <element name="envelopeID" type="long" minOccurs="0">
          <annotation>
            <documentation>
              ID obálky. Pokud není vyplněno, tak ARBES OBS
              obálku založí. Pokud je vyplněno, tak jsou
              hodnoty aktualizovány.
            </documentation>
          </annotation>
        </element>
        <element name="envelopeName" type="string">
          <annotation>
            <documentation>Název obálky.</documentation>
          </annotation>
        </element>
        <element name="targetAmount" type="decimal">
          <annotation>
            <documentation>Cílová částka.</documentation>
          </annotation>
        </element>
        <element name="idIcon" type="long" minOccurs="0">
          <annotation>
            <documentation>ID ikony obálky</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>


    <complexType name="EnvelopeDetailTO">
    	<sequence>
    		<element name="envelopeID" type="long">
    			<annotation>
    				<documentation>
    					ID obálky. Pokud není vyplněno, tak ARBES OBS
    					obálku založí. Pokud je vyplněno, tak jsou
    					hodnoty aktualizovány.
    				</documentation>
    			</annotation>
    		</element>
    		<element name="envelopeName" type="string">
    			<annotation>
    				<documentation>Název obálky.</documentation>
    			</annotation>
    		</element>
    		<element name="targetAmount" type="decimal">
    			<annotation>
    				<documentation>Cílová částka.</documentation>
    			</annotation>
    		</element>
    		<element name="envelopeBalance" type="decimal">
    			<annotation>
    				<documentation>zůstatek na obálce</documentation>
    			</annotation>
    		</element>
        <element name="idIcon" type="long" minOccurs="0">
          <annotation>
            <documentation>ID ikony obálky</documentation>
          </annotation>
        </element>
    	</sequence>
    </complexType>

    <complexType name="EnvelopeBalanceTo">
    	<sequence>
    		<element name="envelopeID" type="long" minOccurs="0" maxOccurs="1">
    			<annotation>
    				<documentation>ID obálky, za kterou jsou vráceny zůstatky. </documentation>
    			</annotation></element>
            <element name="balanceDate" type="date" minOccurs="1" maxOccurs="1">
            	<annotation>
            		<documentation>Datum zůstatku.</documentation>
            	</annotation></element>
            <element name="balance" type="decimal" minOccurs="1" maxOccurs="1">
            	<annotation>
            		<documentation>Částka zůstatku ve měně účtu.</documentation>
            	</annotation></element>
    	</sequence>
    </complexType>

    <complexType name="AccountEnvelopeTO">
    	<sequence>
    	 <element name="idBankAccount" type="long" minOccurs="1"
          maxOccurs="1">
          <annotation>
            <documentation>
              ID účtu, na který je obálka navázána.
            </documentation>
          </annotation>
        </element>
        <element name="accountName" type="string" maxOccurs="1" minOccurs="1">
          <annotation>
            <documentation>jméno účtu. Naplní OBS při načítaní obálky.</documentation>
        </annotation></element>
    		<element name="envelope"
                    type="Q1:EnvelopeDetailTO" minOccurs="0"
                    maxOccurs="unbounded">
        </element>
        <element name="unassignedBalance"
          type="decimal" minOccurs="1" maxOccurs="1">
          <annotation>
            <documentation>
              Suma disponibililních prostředků,
              které nejsou přiřazeny na žádnou
              obálku.
            </documentation>
          </annotation>
        </element>
    	</sequence>
    </complexType>
</schema>
