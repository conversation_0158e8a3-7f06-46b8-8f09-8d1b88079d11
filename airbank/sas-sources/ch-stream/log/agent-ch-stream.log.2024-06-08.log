2024-06-08 19:32:54,640  main        INFO   c.a.c.a.Agent    
2024-06-08 19:32:54,641  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-08 19:32:54,645  main        INFO   c.a.c.a.Agent    App: /C:/Git/sas/cz/ab/agent-ch-stream2/target/agent-ch-stream.jar
2024-06-08 19:32:54,646  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-08 19:32:54,646  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-08 19:32:54,648  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-08 19:32:54,649  main        INFO   c.a.c.c.Config    ci360.jwt=aGVzbG8
2024-06-08 19:32:54,649  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-08 19:32:54,649  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-08 19:32:54,649  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-08 19:32:54,649  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail topic
2024-06-08 19:32:54,649  main        INFO   c.a.c.a.Agent    test mode = true
2024-06-08 19:32:54,650  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-08 19:32:54,650  main        INFO   c.a.c.c.Config    proxy.server=
2024-06-08 19:32:54,650  main        INFO   c.a.c.c.Config    proxy.port=0
2024-06-08 19:32:54,651  main        INFO   c.a.c.c.Config    db.user=TODO
2024-06-08 19:32:54,651  main        INFO   c.a.c.c.Config    db.database=TODO
2024-06-08 19:32:54,651  main        INFO   c.a.c.c.Config    db.server=TODO
2024-06-08 19:32:54,651  main        INFO   c.a.c.c.Config    db.service_name=TODO
2024-06-08 19:32:54,651  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-08 19:32:54,651  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-08 19:32:54,656  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-08 19:32:54,656  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-08 19:32:54,656  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-08 19:32:54,656  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-08 19:32:54,668  Thread-1    INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-08 19:32:54,669  Thread-1    DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"169445","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","SITPhoneNum":"1324567890","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"72034a08-9abf-3488-99ec-7b7383d44494","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"a89963c0-e340-4b3f-8936-635a2666ff19","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"15:57","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"Koh35eJYisi2C3.VT5auuYg_L4z_IvGk","primaryVisualization":"BUTTON","sentSms":"N","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"72034a08-9abf-3488-99ec-7b7383d44494","guid":"98a32fef-2d80-40b4-9c1f-e94306da1f61","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"169445","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"169445","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"169445","timestamp":"*************","sentInbox":"Y","internal_tenant_id":"********","sentPushWithPicture":"N","sentPush":"N","message_id":"0","sessionId":"169445","event_category":"unifiedAndEngage","customer_id":"58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"98a32fef-2d80-40b4-9c1f-e94306da1f61"}
2024-06-08 19:32:54,677  Thread-1    DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 72034a08-9abf-3488-99ec-7b7383d44494, UPDATED_DTTM = 2024-06-08 19:32:54.674, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 72034a08-9abf-3488-99ec-7b7383d44494, SPOT_ID = , LEAD_ID = 72034a08-9abf-3488-99ec-7b7383d44494_a89963c0-e340-4b3f-8936-635a2666ff19_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = XNA, CONTACT_POLICY_PRODUCT_CODE = XNA, SUBJECT_ID = 169445, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = Koh35eJYisi2C3.VT5auuYg_L4z_IvGk, CAMP_SUBTYPE_CODE = XNA, CONTACT_POLICY_TYPE_CODE = XNA, COMM_TYPE_CODE = XNA, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-05-20 10:19:37.741, CONTACT_DT = 2024-05-20 00:00:00.0, RTC_ID = a89963c0-e340-4b3f-8936-635a2666ff19, CAMPAIGN_MESSAGE_CD = -1, AUD_OCCURENCE_ID = , CONTACT_ID = 98a32fef-2d80-40b4-9c1f-e94306da1f61, EMAIL_IMPRINT_URL = , SESSION_ID = 169445, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = XNA, INSERTED_DTTM = 2024-06-08 19:32:54.674, 
2024-06-08 19:32:54,678  Thread-1    DEBUG  c.a.c.a.Event    Validation: OK
