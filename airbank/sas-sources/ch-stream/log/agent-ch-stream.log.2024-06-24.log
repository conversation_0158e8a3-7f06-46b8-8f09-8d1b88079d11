2024-06-24 00:00:53,960  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:01:53,966  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:02:53,971  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:03:53,976  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:04:53,981  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:05:53,986  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:06:53,991  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:07:53,998  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:08:54,004  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:09:54,009  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:10:54,015  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:11:54,021  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:12:54,027  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:13:54,032  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:14:54,038  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:15:54,043  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:16:54,049  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:17:54,055  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:18:54,061  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:19:54,067  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:20:54,072  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:21:54,078  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:22:54,083  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:23:54,089  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:24:54,095  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:25:54,100  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:26:54,106  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:27:54,112  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:28:54,117  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:29:54,123  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:30:54,128  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:31:54,133  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:32:54,139  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:33:54,144  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:34:54,150  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:35:54,155  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:36:54,160  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:37:54,167  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:38:54,173  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:39:54,179  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:40:54,185  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:41:54,191  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:42:54,197  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:43:54,203  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:44:54,208  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:45:54,214  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:46:54,221  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:47:54,227  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:48:54,233  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:49:54,238  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:50:54,244  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:51:54,251  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:52:54,257  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:53:54,263  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:54:54,269  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:55:54,275  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:56:54,282  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:57:54,288  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:58:54,294  main        INFO   c.a.c.a.Agent    running
2024-06-24 00:59:54,300  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:00:54,306  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:01:54,312  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:02:54,319  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:03:54,325  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:04:54,331  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:05:54,337  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:06:54,343  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:07:54,349  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:08:54,355  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:09:54,362  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:10:54,368  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:11:54,374  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:12:54,381  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:13:54,387  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:14:54,393  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:15:54,399  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:16:54,406  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:17:54,412  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:18:54,418  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:19:54,424  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:20:54,431  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:21:54,437  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:22:54,442  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:23:54,448  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:24:54,453  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:25:54,459  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:26:54,465  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:27:54,471  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:28:54,477  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:29:54,482  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:30:54,488  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:31:54,494  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:32:54,500  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:33:54,507  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:34:54,513  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:35:54,519  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:36:54,525  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:37:54,531  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:38:54,537  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:39:54,543  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:40:54,550  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:41:54,556  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:42:54,562  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:43:54,568  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:44:54,573  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:45:54,579  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:46:54,585  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:47:54,591  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:48:54,598  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:49:54,604  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:50:54,610  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:51:54,617  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:52:54,623  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:53:54,630  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:54:54,635  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:55:54,641  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:56:54,647  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:57:54,653  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:58:54,659  main        INFO   c.a.c.a.Agent    running
2024-06-24 01:59:54,665  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:00:54,671  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:01:54,677  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:02:54,683  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:03:54,689  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:04:54,695  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:05:54,700  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:06:54,705  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:07:54,710  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:08:54,716  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:09:54,722  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:10:54,728  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:11:54,733  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:12:54,738  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:13:54,744  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:14:54,749  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:15:54,756  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:16:54,765  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:17:54,772  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:18:54,778  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:19:54,785  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:20:54,791  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:21:54,798  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:22:54,805  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:23:54,812  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:24:54,818  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:25:54,825  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:26:54,832  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:27:54,838  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:28:54,844  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:29:54,850  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:30:54,856  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:31:54,861  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:32:54,867  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:33:54,873  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:34:54,879  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:35:54,885  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:36:54,891  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:37:54,897  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:38:54,903  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:39:54,908  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:40:54,914  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:41:54,921  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:42:54,926  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:43:54,932  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:44:54,938  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:45:54,944  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:46:54,950  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:47:54,957  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:48:54,963  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:49:54,968  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:50:54,974  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:51:54,980  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:52:54,986  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:53:54,991  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:54:54,997  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:55:55,003  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:56:55,009  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:57:55,015  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:58:55,021  main        INFO   c.a.c.a.Agent    running
2024-06-24 02:59:55,027  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:00:55,033  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:01:55,038  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:02:55,044  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:03:55,050  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:04:55,057  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:05:55,063  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:06:55,070  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:07:55,076  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:08:55,082  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:09:55,088  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:10:55,095  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:11:55,101  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:12:55,107  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:13:55,113  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:14:55,119  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:15:55,125  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:16:55,133  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:17:55,139  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:18:55,146  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:19:55,152  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:20:55,158  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:21:55,165  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:22:55,171  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:23:55,178  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:24:55,185  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:25:55,191  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:26:55,198  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:27:55,204  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:28:55,210  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:29:55,216  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:30:55,222  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:31:55,229  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:32:55,235  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:33:55,242  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:34:55,248  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:35:55,254  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:36:55,261  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:37:55,267  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:38:55,273  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:39:55,279  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:40:55,287  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:41:55,293  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:42:55,299  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:43:55,306  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:44:55,313  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:45:55,320  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:46:55,326  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:47:55,333  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:48:55,338  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:49:55,345  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:50:55,351  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:51:55,358  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:52:55,364  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:53:55,371  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:54:55,377  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:55:55,383  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:56:55,389  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:57:55,396  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:58:55,403  main        INFO   c.a.c.a.Agent    running
2024-06-24 03:59:55,410  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:00:55,416  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:01:55,422  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:02:55,428  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:03:55,434  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:04:55,440  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:05:55,445  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:06:55,451  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:07:55,457  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:08:55,464  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:09:55,469  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:10:55,475  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:11:55,481  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:12:55,486  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:13:55,492  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:14:55,498  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:15:55,504  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:16:55,510  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:17:55,516  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:18:55,522  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:19:55,528  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:20:55,535  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:21:55,542  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:22:55,548  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:23:55,554  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:24:55,561  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:25:55,567  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:26:55,574  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:27:55,580  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:28:55,586  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:29:55,592  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:30:55,599  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:31:55,606  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:32:55,612  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:33:55,618  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:34:55,624  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:35:55,631  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:36:55,637  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:37:55,643  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:38:55,649  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:39:55,655  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:40:55,661  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:41:55,667  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:42:55,674  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:43:55,680  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:44:55,686  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:45:55,692  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:46:55,699  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:47:55,705  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:48:55,710  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:49:55,717  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:50:55,723  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:51:55,730  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:52:55,735  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:53:55,742  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:54:55,748  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:55:55,755  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:56:55,760  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:57:55,766  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:58:55,773  main        INFO   c.a.c.a.Agent    running
2024-06-24 04:59:55,779  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:00:55,785  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:01:55,792  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:02:55,798  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:03:55,805  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:04:55,812  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:05:55,818  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:06:55,824  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:07:55,831  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:08:55,837  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:09:55,843  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:10:55,850  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:11:55,857  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:12:55,863  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:13:55,870  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:14:55,877  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:15:55,884  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:16:55,891  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:17:55,898  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:18:55,904  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:19:55,909  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:20:55,915  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:21:55,920  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:22:55,926  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:23:55,932  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:24:55,938  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:25:55,944  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:26:55,950  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:27:55,958  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:28:55,965  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:29:55,971  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:30:55,977  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:31:55,982  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:32:55,989  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:33:55,996  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:34:56,002  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:35:56,008  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:36:56,015  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:37:56,022  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:38:56,028  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:39:56,034  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:40:56,041  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:41:56,047  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:42:56,053  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:43:56,060  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:44:56,067  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:45:56,073  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:46:56,080  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:47:56,086  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:48:56,092  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:49:56,098  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:50:56,105  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:51:56,111  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:52:56,118  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:53:56,124  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:54:56,131  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:55:56,137  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:56:56,143  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:57:56,148  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:58:56,155  main        INFO   c.a.c.a.Agent    running
2024-06-24 05:59:56,161  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:00:56,167  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:01:56,173  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:02:56,178  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:03:56,184  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:04:56,190  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:05:56,195  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:06:56,201  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:07:56,206  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:08:56,211  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:09:56,217  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:10:56,223  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:11:56,229  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:12:56,235  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:13:56,241  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:14:56,247  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:15:56,253  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:16:56,259  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:17:56,266  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:18:56,272  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:19:56,278  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:20:56,284  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:21:56,291  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:22:56,299  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:23:56,306  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:24:56,313  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:25:56,321  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:26:56,328  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:27:56,333  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:28:56,339  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:29:56,345  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:30:56,351  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:31:56,357  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:32:56,363  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:33:56,370  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:34:56,375  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:35:56,381  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:36:56,387  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:37:56,394  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:38:56,400  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:39:56,407  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:40:56,414  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:41:56,420  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:42:56,426  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:43:56,432  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:44:56,438  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:45:56,444  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:46:56,450  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:47:56,455  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:48:56,461  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:49:56,467  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:50:56,473  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:51:56,479  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:52:56,485  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:53:56,491  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:54:56,497  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:55:56,503  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:56:56,510  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:57:56,516  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:58:56,521  main        INFO   c.a.c.a.Agent    running
2024-06-24 06:59:56,527  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:00:56,533  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:01:56,539  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:02:56,545  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:03:56,551  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:04:56,558  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:05:56,564  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:06:56,571  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:07:56,577  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:08:56,583  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:09:56,590  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:10:56,596  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:11:56,603  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:12:56,609  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:13:56,615  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:14:56,621  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:15:56,627  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:16:56,634  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:17:56,640  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:18:56,645  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:19:56,652  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:20:56,658  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:21:56,665  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:22:56,672  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:23:56,678  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:24:56,685  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:25:56,691  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:26:56,697  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:27:56,703  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:28:56,710  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:29:56,716  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:30:56,722  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:31:56,728  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:32:56,734  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:33:56,741  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:34:56,747  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:35:56,753  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:36:56,758  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:37:56,764  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:38:56,769  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:39:56,775  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:40:56,781  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:41:56,786  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:42:56,791  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:43:56,797  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:44:56,803  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:45:56,809  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:46:56,815  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:47:56,821  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:48:56,827  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:49:56,833  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:50:56,839  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:51:56,845  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:52:56,852  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:53:56,857  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:54:56,863  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:55:56,869  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:56:56,875  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:57:56,881  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:58:56,887  main        INFO   c.a.c.a.Agent    running
2024-06-24 07:59:56,893  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:00:56,899  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:01:56,906  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:02:56,911  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:03:56,917  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:04:56,922  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:05:56,928  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:06:56,933  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:07:56,939  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:08:56,944  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:09:56,949  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:10:56,955  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:11:56,961  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:12:56,967  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:13:56,973  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:14:56,979  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:15:56,984  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:16:56,991  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:17:56,997  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:18:57,003  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:19:57,009  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:20:57,015  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:21:57,021  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:22:57,027  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:23:57,033  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:24:57,039  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:25:57,046  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:26:57,053  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:27:57,060  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:28:57,066  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:29:57,073  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:30:57,079  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:31:57,085  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:32:57,091  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:33:57,097  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:34:57,103  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:35:57,109  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:36:57,115  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:37:57,122  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:38:57,128  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:39:57,134  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:40:57,140  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:41:57,146  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:42:57,152  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:43:57,159  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:44:57,166  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:45:57,173  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:46:57,180  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:47:57,187  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:48:57,195  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:49:57,201  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:50:57,208  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:51:57,214  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:52:57,221  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:53:57,227  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:54:57,233  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:55:57,239  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:56:57,246  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:57:57,253  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:58:57,259  main        INFO   c.a.c.a.Agent    running
2024-06-24 08:59:57,266  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:00:57,272  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:01:57,279  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:02:57,285  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:03:57,292  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:04:57,298  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:05:57,306  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:06:57,313  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:07:57,319  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:08:57,326  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:09:57,332  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:10:57,339  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:11:57,345  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:12:57,351  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:13:57,358  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:14:57,364  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:15:57,371  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:16:57,378  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:17:57,384  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:18:57,390  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:19:57,397  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:20:57,403  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:21:57,410  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:22:57,417  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:23:57,424  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:24:57,430  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:25:57,436  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:26:57,442  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:27:57,448  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:28:57,455  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:29:57,461  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:30:57,468  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:31:57,474  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:32:57,480  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:33:57,487  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:34:57,494  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:35:57,501  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:36:57,507  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:37:57,513  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:38:57,519  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:39:57,526  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:40:57,532  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:41:57,537  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:42:57,543  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:43:57,550  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:44:57,556  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:45:57,563  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:46:57,570  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:47:57,577  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:48:57,584  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:49:57,591  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:50:57,598  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:51:57,605  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:52:57,612  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:53:57,618  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:54:57,624  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:55:57,630  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:56:57,636  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:57:57,643  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:58:57,649  main        INFO   c.a.c.a.Agent    running
2024-06-24 09:59:57,656  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:00:57,662  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:01:57,668  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:02:57,674  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:03:57,680  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:04:57,686  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:05:57,692  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:06:57,697  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:07:57,703  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:08:57,708  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:09:57,714  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:10:57,720  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:11:57,727  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:12:57,733  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:13:57,738  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:14:57,743  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:15:57,749  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:16:57,754  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:17:57,760  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:18:57,766  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:19:57,773  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:20:57,779  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:21:57,785  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:22:57,791  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:23:57,797  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:24:57,803  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:25:57,810  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:26:57,816  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:27:57,823  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:28:57,830  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:29:57,837  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:30:57,843  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:31:57,849  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:32:57,855  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:33:57,862  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:34:57,868  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:35:57,875  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:36:57,882  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:37:57,888  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:38:57,895  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:39:57,902  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:40:57,908  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:41:57,914  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:42:57,920  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:43:57,927  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:44:57,933  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:45:57,940  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:46:57,946  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:47:57,952  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:48:57,958  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:49:57,965  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:50:57,970  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:51:57,977  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:52:57,983  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:53:57,989  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:54:57,995  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:55:58,001  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:56:58,008  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:57:58,015  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:58:58,021  main        INFO   c.a.c.a.Agent    running
2024-06-24 10:59:58,027  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:00:58,033  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:01:58,039  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:02:58,045  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:03:58,051  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:04:58,057  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:05:58,064  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:06:58,071  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:07:58,079  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:08:58,085  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:09:58,092  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:10:58,098  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:11:58,104  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:12:13,107  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-24 11:12:13,116  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-24 11:12:13,118  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@32c58588[coreClient=WebSocketCoreClient@61ce23ac{STARTED},openSessions.size=1]
2024-06-24 11:12:13,123  4155890-20  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-24 11:12:13,124  4155890-20  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-24 11:12:13,128  4155890-20  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-24 11:12:13,130  4155890-20  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-24 11:12:14,131  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-24 11:12:33,012  main        INFO   c.a.c.a.Agent    
2024-06-24 11:12:33,013  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-24 11:12:33,018  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-24 11:12:33,021  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-24 11:12:33,021  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-24 11:12:33,030  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-24 11:12:33,031  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-24 11:12:33,032  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-24 11:12:33,033  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-24 11:12:33,034  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-24 11:12:33,034  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-24 11:12:33,035  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-24 11:12:33,035  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-24 11:12:33,035  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-24 11:12:33,035  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-24 11:12:33,035  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-24 11:12:33,035  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-24 11:12:33,721  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-24 11:12:33,722  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-24 11:12:33,722  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-24 11:12:33,722  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-24 11:12:33,727  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-24 11:12:33,727  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 11:12:33,780  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-24 11:12:34,473  4818087-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-24 11:12:34,874  4818087-23  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-24 11:12:34,889  4818087-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-24 11:12:34,890  4818087-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-24 11:12:34,890  4818087-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-24 11:13:33,928  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:14:33,939  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:15:33,950  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:15:37,268  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 11:15:37,269  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"064f1cb8-a2f3-4bd5-aa5b-c4825f90c451","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"064f1cb8-a2f3-4bd5-aa5b-c4825f90c451"}
2024-06-24 11:15:37,441  2-thread-2  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 11:15:37,443  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"d78f0c62-b67f-4ce8-9bc0-8932c37b41c5","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"d78f0c62-b67f-4ce8-9bc0-8932c37b41c5"}
2024-06-24 11:15:40,085  2-thread-2  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"2b772d1a-68ba-4444-889d-2800218ec724","name":"PSH draft","lastModifiedTimeStamp":"Mon May 27 09:12:33 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon May 27 09:12:32 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_97","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_19"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["Onboarding"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGC"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_1"]},{"propertyName":"tsk_cp_product","propertyValue":["VYD"]}],"triggerCriteria":null}
2024-06-24 11:15:40,085  2-thread-2  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"2b772d1a-68ba-4444-889d-2800218ec724","name":"PSH draft","lastModifiedTimeStamp":"Mon May 27 09:12:33 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon May 27 09:12:32 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_97","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_19"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["Onboarding"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGC"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_1"]},{"propertyName":"tsk_cp_product","propertyValue":["VYD"]}],"triggerCriteria":null}
2024-06-24 11:15:40,090  2-thread-2  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from CI360 API, and saved to TaskCache table, duration[ms]: 2644
2024-06-24 11:15:40,103  2-thread-2  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:40,160  2-thread-1  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"********-4dff-43c7-92a6-5a74243beb01","name":"Cmp Brief Test","lastModifiedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_95","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_14"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["X-Sell"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","SAVINGS_INVESTMENTS","INSURANCE","OTHER"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["K/VK/01"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["PU_KONS"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[]}}
2024-06-24 11:15:40,160  2-thread-1  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"********-4dff-43c7-92a6-5a74243beb01","name":"Cmp Brief Test","lastModifiedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_95","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_14"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["X-Sell"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","SAVINGS_INVESTMENTS","INSURANCE","OTHER"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["K/VK/01"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["PU_KONS"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[]}}
2024-06-24 11:15:40,161  2-thread-1  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from CI360 API, and saved to TaskCache table, duration[ms]: 2891
2024-06-24 11:15:40,163  2-thread-1  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:40,303  2-thread-1  ERROR  c.a.c.c.Http    Response, Code: 404 , Message: 
2024-06-24 11:15:40,304  2-thread-1  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-24 11:15:40,304  2-thread-1  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:40,305  2-thread-1  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from CI360 API, and saved to CreativeCache table, duration[ms]: 140
2024-06-24 11:15:40,308  2-thread-1  ERROR  c.a.c.c.CreativeCache    Exception()
java.sql.BatchUpdateException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM_CRT_CP_CACHE"."CREATIVE_VERSION_ID")

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:167)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:40,313  2-thread-1  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:15:40,317  2-thread-1  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 11:15:40.316, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 11:15:37.018, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 064f1cb8-a2f3-4bd5-aa5b-c4825f90c451, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 11:15:40.316, 
2024-06-24 11:15:40,317  2-thread-1  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:15:40,329  2-thread-1  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:15:40,340  2-thread-2  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","name":"zrdz","status":"active","versions":[{"id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","validFrom":"2024-05-20 08:16:16.000","latest":true}],"type":"text","createdByUser":"<EMAIL>","publishedByUser":"<EMAIL>@cixniceu","category":"cre_communication_brief","externalCode":"CRT_56","attributes":[{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"}]}
2024-06-24 11:15:40,340  2-thread-2  DEBUG  c.a.c.a.CI360Api    Creative API json: {"id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","name":"zrdz","status":"active","versions":[{"id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","validFrom":"2024-05-20 08:16:16.000","latest":true}],"type":"text","createdByUser":"<EMAIL>","publishedByUser":"<EMAIL>@cixniceu","category":"cre_communication_brief","externalCode":"CRT_56","attributes":[{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"}]}
2024-06-24 11:15:40,340  2-thread-2  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from CI360 API, and saved to CreativeCache table, duration[ms]: 237
2024-06-24 11:15:40,347  2-thread-2  ERROR  c.a.c.c.CreativeCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559863) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:167)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:40,349  2-thread-2  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:15:40,350  2-thread-2  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 11:15:40.349, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 11:15:37.121, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = d78f0c62-b67f-4ce8-9bc0-8932c37b41c5, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 11:15:40.349, 
2024-06-24 11:15:40,350  2-thread-2  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:15:40,355  2-thread-2  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:15:42,731  2-thread-3  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 11:15:42,732  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1719220542500","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719220542500","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"b4adfa45-3751-4b28-bb97-a724bb0a3786","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/b/cixniceu/b4adfa45-3751-4b28-bb97-a724bb0a3786.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"399d680e-ce27-4f63-8748-bf9cbbe752f7","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"399d680e-ce27-4f63-8748-bf9cbbe752f7"}
2024-06-24 11:15:43,074  2-thread-3  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"eadf9947-5e69-4e7a-9228-3326a922bdcb","name":"SIT EMAIL CHStream","lastModifiedTimeStamp":"Fri Jun 21 14:34:09 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Fri Jun 21 14:31:59 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_106","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","INSURANCE"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentEmail","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH"],"attributeOperator":"EQ"}]}}
2024-06-24 11:15:43,074  2-thread-3  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"eadf9947-5e69-4e7a-9228-3326a922bdcb","name":"SIT EMAIL CHStream","lastModifiedTimeStamp":"Fri Jun 21 14:34:09 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Fri Jun 21 14:31:59 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_106","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","INSURANCE"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentEmail","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH"],"attributeOperator":"EQ"}]}}
2024-06-24 11:15:43,075  2-thread-3  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from CI360 API, and saved to TaskCache table, duration[ms]: 342
2024-06-24 11:15:43,077  2-thread-3  ERROR  c.a.c.c.TaskCache    Exception()
java.sql.BatchUpdateException: ORA-00001: unique constraint (APP_CAMPAIGN_CDM2.SYS_C009559860) violated

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.TaskCache.saveTaskCpToCache(TaskCache.java:210)
	at cz.ab.ci360.cache.TaskCache.getTask(TaskCache.java:72)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:99)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:43,077  2-thread-3  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 11:15:43,079  2-thread-3  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:15:43,080  2-thread-3  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 11:15:43.079, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_af3610c7-ead4-4b06-8e88-d8985c9df954_1719220542500, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = , IMPRINT_ID = b4adfa45-3751-4b28-bb97-a724bb0a3786, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 11:15:42.5, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 399d680e-ce27-4f63-8748-bf9cbbe752f7, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/b/cixniceu/b4adfa45-3751-4b28-bb97-a724bb0a3786.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 11:15:43.079, 
2024-06-24 11:15:43,080  2-thread-3  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:15:43,086  2-thread-3  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:15:43,087  2-thread-3  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NumberFormatException: For input string: "b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.base/java.lang.Long.parseLong(Long.java:692)
	at java.base/java.lang.Long.parseLong(Long.java:817)
	at cz.ab.ci360.agent_ch_stream.Event.processEmail(Event.java:133)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:119)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:15:43,087  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1719220542500","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719220542500","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"b4adfa45-3751-4b28-bb97-a724bb0a3786","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/b/cixniceu/b4adfa45-3751-4b28-bb97-a724bb0a3786.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"399d680e-ce27-4f63-8748-bf9cbbe752f7","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"399d680e-ce27-4f63-8748-bf9cbbe752f7"}
2024-06-24 11:15:45,500  2-thread-4  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 11:15:45,500  2-thread-4  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"669fdea7-6cd0-42d9-bdfb-ad470f0e0330","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"669fdea7-6cd0-42d9-bdfb-ad470f0e0330"}
2024-06-24 11:15:45,500  2-thread-4  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 11:15:45,500  2-thread-4  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 11:15:45,502  2-thread-4  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:15:45,503  2-thread-4  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-24 11:15:45.502, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-24 11:15:43.653, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 669fdea7-6cd0-42d9-bdfb-ad470f0e0330, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 11:15:45.502, 
2024-06-24 11:15:45,503  2-thread-4  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:15:45,508  2-thread-4  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:16:33,960  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:17:33,970  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:18:33,979  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:19:33,988  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:20:33,996  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:21:34,004  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:22:34,012  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:23:34,020  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:24:34,030  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:25:34,038  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:26:34,047  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:27:34,055  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:28:34,064  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:29:34,073  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:30:34,081  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:31:34,090  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:32:34,099  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:33:34,107  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:34:34,115  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:35:34,123  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:36:34,131  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:37:34,139  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:38:34,147  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:39:34,155  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:40:34,163  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:41:34,170  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:42:34,178  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:43:34,187  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:44:34,195  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:45:34,204  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:46:34,213  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:46:52,215  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-24 11:46:52,225  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-24 11:46:52,226  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@4b6d374e[coreClient=WebSocketCoreClient@2cc3ad05{STARTED},openSessions.size=1]
2024-06-24 11:46:52,229  4818087-23  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-24 11:46:52,230  4818087-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-24 11:46:52,233  4818087-23  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-24 11:46:52,235  4818087-23  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-24 11:46:53,236  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-24 11:52:16,019  main        INFO   c.a.c.a.Agent    
2024-06-24 11:52:16,021  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-24 11:52:16,026  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-24 11:52:16,029  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-24 11:52:16,029  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-24 11:52:16,033  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-24 11:52:16,035  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-24 11:52:16,036  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-24 11:52:16,036  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-24 11:52:16,036  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-24 11:52:16,036  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-24 11:52:16,037  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-24 11:52:16,037  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-24 11:52:16,037  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-24 11:52:16,037  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-24 11:52:16,038  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-24 11:52:16,038  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-24 11:52:16,039  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-24 11:52:16,039  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-24 11:52:16,039  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-24 11:52:16,039  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-24 11:52:16,039  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-24 11:52:16,039  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-24 11:52:16,786  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-24 11:52:16,787  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-24 11:52:16,787  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-24 11:52:16,787  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-24 11:52:16,790  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-24 11:52:16,790  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 11:52:16,839  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-24 11:52:17,483  5623216-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-24 11:52:17,539  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-24 11:52:17,553  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-24 11:52:17,554  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-24 11:52:17,554  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-24 11:52:20,568  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 11:52:20,568  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"dfda6035-ec6f-47d4-8b14-1ebb9807f7c9","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"dfda6035-ec6f-47d4-8b14-1ebb9807f7c9"}
2024-06-24 11:52:20,568  2-thread-3  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 11:52:20,569  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"b993116c-05ed-4d94-b773-44a9a38134bd","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/b/cixniceu/b993116c-05ed-4d94-b773-44a9a38134bd.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"6df6222a-c7c3-423e-9daa-53e012f26443","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"6df6222a-c7c3-423e-9daa-53e012f26443"}
2024-06-24 11:52:20,570  2-thread-2  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 11:52:20,571  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"2c4e6c74-ecc6-4f7d-aaae-1cbf30d2d7c6","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"2c4e6c74-ecc6-4f7d-aaae-1cbf30d2d7c6"}
2024-06-24 11:52:20,605  2-thread-4  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 11:52:20,605  2-thread-4  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"2e215d5f-2e2b-43ad-985b-8eb22e0d3e4a","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"2e215d5f-2e2b-43ad-985b-8eb22e0d3e4a"}
2024-06-24 11:52:21,014  2-thread-2  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"2b772d1a-68ba-4444-889d-2800218ec724","name":"PSH draft","lastModifiedTimeStamp":"Mon May 27 09:12:33 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon May 27 09:12:32 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_97","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_19"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["Onboarding"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGC"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_1"]},{"propertyName":"tsk_cp_product","propertyValue":["VYD"]}],"triggerCriteria":null}
2024-06-24 11:52:21,015  2-thread-2  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"2b772d1a-68ba-4444-889d-2800218ec724","name":"PSH draft","lastModifiedTimeStamp":"Mon May 27 09:12:33 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon May 27 09:12:32 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_97","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_19"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["Onboarding"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGC"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_1"]},{"propertyName":"tsk_cp_product","propertyValue":["VYD"]}],"triggerCriteria":null}
2024-06-24 11:52:21,019  2-thread-2  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from CI360 API, and saved to TaskCache table, duration[ms]: 445
2024-06-24 11:52:21,274  2-thread-2  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","name":"zrdz","status":"active","versions":[{"id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","validFrom":"2024-05-20 08:16:16.000","latest":true}],"type":"text","createdByUser":"<EMAIL>","publishedByUser":"<EMAIL>@cixniceu","category":"cre_communication_brief","externalCode":"CRT_56","attributes":[{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"}]}
2024-06-24 11:52:21,274  2-thread-2  DEBUG  c.a.c.a.CI360Api    Creative API json: {"id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","name":"zrdz","status":"active","versions":[{"id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","validFrom":"2024-05-20 08:16:16.000","latest":true}],"type":"text","createdByUser":"<EMAIL>","publishedByUser":"<EMAIL>@cixniceu","category":"cre_communication_brief","externalCode":"CRT_56","attributes":[{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"PK"}]}
2024-06-24 11:52:21,275  2-thread-2  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from CI360 API, and saved to CreativeCache table, duration[ms]: 237
2024-06-24 11:52:21,289  2-thread-2  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:52:21,294  2-thread-2  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 11:52:21.292, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 11:51:19.11, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = 2c4e6c74-ecc6-4f7d-aaae-1cbf30d2d7c6, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 11:52:21.292, 
2024-06-24 11:52:21,294  2-thread-2  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:52:21,302  2-thread-2  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:52:23,196  2-thread-3  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"eadf9947-5e69-4e7a-9228-3326a922bdcb","name":"SIT EMAIL CHStream","lastModifiedTimeStamp":"Fri Jun 21 14:34:09 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Fri Jun 21 14:31:59 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_106","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","INSURANCE"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentEmail","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH"],"attributeOperator":"EQ"}]}}
2024-06-24 11:52:23,196  2-thread-3  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"eadf9947-5e69-4e7a-9228-3326a922bdcb","name":"SIT EMAIL CHStream","lastModifiedTimeStamp":"Fri Jun 21 14:34:09 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Fri Jun 21 14:31:59 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_106","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","INSURANCE"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentEmail","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH"],"attributeOperator":"EQ"}]}}
2024-06-24 11:52:23,196  2-thread-3  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from CI360 API, and saved to TaskCache table, duration[ms]: 2627
2024-06-24 11:52:23,204  2-thread-1  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"********-4dff-43c7-92a6-5a74243beb01","name":"Cmp Brief Test","lastModifiedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_95","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_14"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["X-Sell"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","SAVINGS_INVESTMENTS","INSURANCE","OTHER"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["K/VK/01"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["PU_KONS"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[]}}
2024-06-24 11:52:23,204  2-thread-1  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"********-4dff-43c7-92a6-5a74243beb01","name":"Cmp Brief Test","lastModifiedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Thu Jun 20 11:31:29 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_95","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_14"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["X-Sell"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","SAVINGS_INVESTMENTS","INSURANCE","OTHER"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["K/VK/01"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["PU_KONS"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[]}}
2024-06-24 11:52:23,205  2-thread-1  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from CI360 API, and saved to TaskCache table, duration[ms]: 2636
2024-06-24 11:52:23,210  2-thread-4  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 2604
2024-06-24 11:52:23,332  2-thread-3  ERROR  c.a.c.c.Http    Response, Code: 404 , Message: 
2024-06-24 11:52:23,332  2-thread-3  DEBUG  c.a.c.a.CI360Api    Creative API json: 
2024-06-24 11:52:23,332  2-thread-3  ERROR  c.a.c.a.CI360Api    Exception()
org.json.JSONException: A JSONObject text must begin with '{' at 0 [character 1 line 1]
	at org.json.JSONTokener.syntaxError(JSONTokener.java:503)
	at org.json.JSONObject.<init>(JSONObject.java:213)
	at org.json.JSONObject.<init>(JSONObject.java:430)
	at cz.ab.ci360.agent_ch_stream.CI360Api.getCreativeAttributes(CI360Api.java:84)
	at cz.ab.ci360.cache.CreativeCache.getCI360ApiCreativeCp(CreativeCache.java:91)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:67)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:52:23,333  2-thread-3  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from CI360 API, and saved to CreativeCache table, duration[ms]: 133
2024-06-24 11:52:23,335  2-thread-3  ERROR  c.a.c.c.CreativeCache    Exception()
java.sql.BatchUpdateException: ORA-01400: cannot insert NULL into ("APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM_CRT_CP_CACHE"."CREATIVE_VERSION_ID")

	at oracle.jdbc.driver.OraclePreparedStatement.executeLargeBatch(OraclePreparedStatement.java:9711)
	at oracle.jdbc.driver.T4CPreparedStatement.executeLargeBatch(T4CPreparedStatement.java:1447)
	at oracle.jdbc.driver.OraclePreparedStatement.executeBatch(OraclePreparedStatement.java:9487)
	at oracle.jdbc.driver.OracleStatementWrapper.executeBatch(OracleStatementWrapper.java:237)
	at cz.ab.ci360.cache.CreativeCache.saveCreativeCpToCache(CreativeCache.java:167)
	at cz.ab.ci360.cache.CreativeCache.getCreative(CreativeCache.java:71)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:100)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:52:23,336  2-thread-4  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 125
2024-06-24 11:52:23,336  2-thread-1  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 126
2024-06-24 11:52:23,338  2-thread-3  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:52:23,339  2-thread-3  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 11:52:23.339, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_af3610c7-ead4-4b06-8e88-d8985c9df954_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = , IMPRINT_ID = b993116c-05ed-4d94-b773-44a9a38134bd, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 11:51:23.635, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 6df6222a-c7c3-423e-9daa-53e012f26443, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/b/cixniceu/b993116c-05ed-4d94-b773-44a9a38134bd.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 11:52:23.339, 
2024-06-24 11:52:23,339  2-thread-3  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:52:23,339  2-thread-4  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:52:23,340  2-thread-4  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-24 11:52:23.34, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-24 11:48:43.053, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 2e215d5f-2e2b-43ad-985b-8eb22e0d3e4a, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 11:52:23.34, 
2024-06-24 11:52:23,341  2-thread-4  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:52:23,341  2-thread-1  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 11:52:23,342  2-thread-1  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 11:52:23.342, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 11:51:19.084, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = dfda6035-ec6f-47d4-8b14-1ebb9807f7c9, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 11:52:23.342, 
2024-06-24 11:52:23,342  2-thread-1  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 11:52:23,346  2-thread-4  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:52:23,346  2-thread-3  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:52:23,346  2-thread-1  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 11:52:23,346  2-thread-3  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NumberFormatException: For input string: "b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.base/java.lang.Long.parseLong(Long.java:692)
	at java.base/java.lang.Long.parseLong(Long.java:817)
	at cz.ab.ci360.agent_ch_stream.Event.processEmail(Event.java:133)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:119)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 11:52:23,346  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"b993116c-05ed-4d94-b773-44a9a38134bd","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/b/cixniceu/b993116c-05ed-4d94-b773-44a9a38134bd.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"6df6222a-c7c3-423e-9daa-53e012f26443","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"6df6222a-c7c3-423e-9daa-53e012f26443"}
2024-06-24 11:53:17,009  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:54:17,021  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:55:17,033  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:56:17,044  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:57:17,055  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:58:17,065  main        INFO   c.a.c.a.Agent    running
2024-06-24 11:59:17,075  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:00:17,083  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:01:17,091  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:02:17,099  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:03:17,106  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:04:17,114  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:05:17,121  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:06:17,130  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:07:17,139  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:08:17,148  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:09:17,157  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:10:17,165  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:11:17,173  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:12:17,181  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:13:17,188  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:14:17,196  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:15:17,203  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:16:05,574  2-thread-5  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 12:16:05,574  2-thread-5  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"742bff9f-984f-414d-8fc5-22709415a39c","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"742bff9f-984f-414d-8fc5-22709415a39c"}
2024-06-24 12:16:05,574  2-thread-5  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 12:16:05,574  2-thread-5  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 12:16:05,575  2-thread-6  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 12:16:05,575  2-thread-6  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"7741b775-1e8c-48c0-95b8-dd52a512d510","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"7741b775-1e8c-48c0-95b8-dd52a512d510"}
2024-06-24 12:16:05,575  2-thread-6  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 12:16:05,575  2-thread-6  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 12:16:05,581  2-thread-5  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 12:16:05,582  2-thread-6  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 12:16:05,582  2-thread-5  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 12:16:05.581, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 12:16:05.359, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 742bff9f-984f-414d-8fc5-22709415a39c, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 12:16:05.581, 
2024-06-24 12:16:05,582  2-thread-5  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 12:16:05,583  2-thread-6  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 12:16:05.582, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 12:16:05.427, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = 7741b775-1e8c-48c0-95b8-dd52a512d510, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 12:16:05.582, 
2024-06-24 12:16:05,585  2-thread-6  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 12:16:05,586  2-thread-5  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 12:16:05,587  2-thread-6  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 12:16:17,211  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:16:32,314  2-thread-7  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 12:16:32,314  2-thread-7  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1719224191887","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719224191887","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"72d36c6a-fdcf-4bcb-b446-3c214c9adb1e","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/7/cixniceu/72d36c6a-fdcf-4bcb-b446-3c214c9adb1e.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"a52249d6-5275-45b3-a9e1-2fad0f90def7","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"a52249d6-5275-45b3-a9e1-2fad0f90def7"}
2024-06-24 12:16:32,314  2-thread-7  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from application cache, duration[ms]: 0
2024-06-24 12:16:32,314  2-thread-7  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 12:16:32,317  2-thread-7  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 12:16:32,318  2-thread-7  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 12:16:32.317, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_af3610c7-ead4-4b06-8e88-d8985c9df954_1719224191887, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = , IMPRINT_ID = 72d36c6a-fdcf-4bcb-b446-3c214c9adb1e, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 12:16:31.887, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = a52249d6-5275-45b3-a9e1-2fad0f90def7, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/7/cixniceu/72d36c6a-fdcf-4bcb-b446-3c214c9adb1e.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 12:16:32.317, 
2024-06-24 12:16:32,318  2-thread-7  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 12:16:32,322  2-thread-7  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 12:16:32,322  2-thread-7  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NumberFormatException: For input string: "b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.base/java.lang.Long.parseLong(Long.java:692)
	at java.base/java.lang.Long.parseLong(Long.java:817)
	at cz.ab.ci360.agent_ch_stream.Event.processEmail(Event.java:133)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:119)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 12:16:32,322  2-thread-7  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1719224191887","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719224191887","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"72d36c6a-fdcf-4bcb-b446-3c214c9adb1e","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/7/cixniceu/72d36c6a-fdcf-4bcb-b446-3c214c9adb1e.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"a52249d6-5275-45b3-a9e1-2fad0f90def7","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"a52249d6-5275-45b3-a9e1-2fad0f90def7"}
2024-06-24 12:17:17,218  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:18:17,227  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:19:17,235  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:20:17,244  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:21:17,252  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:22:17,261  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:23:17,270  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:24:17,280  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:25:17,289  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:26:17,299  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:27:17,308  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:28:17,317  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:29:17,326  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:30:17,334  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:31:17,344  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:32:17,352  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:33:17,361  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:34:17,370  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:35:17,378  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:36:17,386  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:37:17,395  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:38:17,406  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:39:17,415  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:40:17,424  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:41:17,433  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:42:17,441  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:43:17,450  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:44:17,459  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:45:17,469  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:46:17,477  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:47:17,485  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:48:17,494  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:49:17,504  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:50:17,513  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:51:17,522  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:52:17,530  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:53:17,538  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:54:17,546  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:55:17,555  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:56:17,563  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:57:17,572  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:58:17,580  main        INFO   c.a.c.a.Agent    running
2024-06-24 12:59:17,588  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:00:17,597  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:01:17,606  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:02:17,615  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:03:17,624  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:04:17,633  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:05:17,642  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:06:17,650  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:07:17,659  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:08:17,671  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:09:17,679  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:10:17,687  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:11:17,695  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:12:17,703  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:13:17,711  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:14:17,720  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:15:17,727  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:16:17,735  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:17:17,742  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:18:17,750  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:19:17,758  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:20:17,766  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:21:17,775  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:22:17,781  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:23:17,788  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:24:17,796  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:25:17,803  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:25:39,847  2-thread-8  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 13:25:39,848  2-thread-8  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"44c2b353-f16c-4f1b-b7ca-5d5710154a30","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"44c2b353-f16c-4f1b-b7ca-5d5710154a30"}
2024-06-24 13:25:39,848  2-thread-8  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 13:25:39,848  2-thread-8  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 13:25:39,854  2-thread-8  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 13:25:39,857  2-thread-8  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 13:25:39.854, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 13:25:39.622, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 44c2b353-f16c-4f1b-b7ca-5d5710154a30, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 13:25:39.854, 
2024-06-24 13:25:39,857  2-thread-8  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 13:25:39,865  2-thread-8  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 13:25:40,111  2-thread-9  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 13:25:40,111  2-thread-9  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"5092beb6-3796-347c-bc98-9688b1b30792","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"ddd96168-a2cb-471b-b963-64ba7a4103c0","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"ddd96168-a2cb-471b-b963-64ba7a4103c0"}
2024-06-24 13:25:40,111  2-thread-9  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 13:25:40,111  2-thread-9  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 13:25:40,113  2-thread-9  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 13:25:40,114  2-thread-9  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 13:25:40.114, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 13:25:39.71, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = ddd96168-a2cb-471b-b963-64ba7a4103c0, EMAIL_IMPRINT_URL = , SESSION_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 13:25:40.114, 
2024-06-24 13:25:40,114  2-thread-9  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 13:25:40,122  2-thread-9  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 13:26:03,404  -thread-10  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 13:26:03,404  -thread-10  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"dd8a42dd-8c0f-49da-b9d1-b5f63795ae27","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"dd8a42dd-8c0f-49da-b9d1-b5f63795ae27"}
2024-06-24 13:26:03,404  -thread-10  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 13:26:03,404  -thread-10  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 13:26:03,410  -thread-10  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 13:26:03,411  -thread-10  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-24 13:26:03.411, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-24 13:26:03.219, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = dd8a42dd-8c0f-49da-b9d1-b5f63795ae27, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 13:26:03.411, 
2024-06-24 13:26:03,411  -thread-10  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 13:26:03,418  -thread-10  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 13:26:07,922  -thread-11  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 13:26:07,922  -thread-11  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1719228367659","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719228367659","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"9858fcff-241c-45cc-a3c9-8a21f2c77543","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/9/cixniceu/9858fcff-241c-45cc-a3c9-8a21f2c77543.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"5b162171-0334-49f5-acc0-15a893f60c84","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"5b162171-0334-49f5-acc0-15a893f60c84"}
2024-06-24 13:26:07,922  -thread-11  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from application cache, duration[ms]: 0
2024-06-24 13:26:07,922  -thread-11  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 13:26:07,925  -thread-11  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 13:26:07,926  -thread-11  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 5092beb6-3796-347c-bc98-9688b1b30792, UPDATED_DTTM = 2024-06-24 13:26:07.926, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 5092beb6-3796-347c-bc98-9688b1b30792, SPOT_ID = , LEAD_ID = 5092beb6-3796-347c-bc98-9688b1b30792_af3610c7-ead4-4b06-8e88-d8985c9df954_1719228367659, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c, MESSAGE_ID = , IMPRINT_ID = 9858fcff-241c-45cc-a3c9-8a21f2c77543, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 13:26:07.659, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 5b162171-0334-49f5-acc0-15a893f60c84, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/9/cixniceu/9858fcff-241c-45cc-a3c9-8a21f2c77543.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 13:26:07.926, 
2024-06-24 13:26:07,926  -thread-11  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 13:26:07,934  -thread-11  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 13:26:07,934  -thread-11  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NumberFormatException: For input string: "b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.base/java.lang.Long.parseLong(Long.java:692)
	at java.base/java.lang.Long.parseLong(Long.java:817)
	at cz.ab.ci360.agent_ch_stream.Event.processEmail(Event.java:133)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:119)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 13:26:07,934  -thread-11  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"b3036bbd-c6d6-309e-bb1a-c0dab40b3d6c","testFlag":"0","generatedTimestamp":"1719228367659","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"5092beb6-3796-347c-bc98-9688b1b30792","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719228367659","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"9858fcff-241c-45cc-a3c9-8a21f2c77543","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/9/cixniceu/9858fcff-241c-45cc-a3c9-8a21f2c77543.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"5092beb6-3796-347c-bc98-9688b1b30792","guid":"5b162171-0334-49f5-acc0-15a893f60c84","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"5b162171-0334-49f5-acc0-15a893f60c84"}
2024-06-24 13:26:17,810  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:27:17,817  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:28:17,824  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:29:17,831  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:30:17,837  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:31:17,844  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:32:17,851  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:33:17,858  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:34:17,865  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:35:17,872  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:36:17,879  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:37:17,886  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:38:17,893  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:39:17,900  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:40:17,906  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:41:17,912  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:42:17,919  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:43:17,926  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:44:17,932  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:45:17,939  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:46:17,946  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:47:17,952  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:48:17,960  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:49:17,967  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:50:17,974  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:51:17,981  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:52:17,987  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:53:17,994  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:54:18,001  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:55:18,008  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:56:18,016  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:57:18,025  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:58:18,031  main        INFO   c.a.c.a.Agent    running
2024-06-24 13:59:18,037  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:00:18,044  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:01:18,050  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:02:18,056  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:03:18,063  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:04:18,069  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:05:18,075  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:06:18,081  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:07:18,089  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:08:18,095  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:09:18,101  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:10:18,107  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:11:18,113  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:12:18,118  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:13:18,124  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:14:18,131  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:15:18,137  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:16:18,143  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:17:18,149  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:18:18,154  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:19:18,161  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:20:18,167  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:21:18,174  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:22:18,180  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:23:18,188  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:24:18,195  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:25:18,202  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:26:18,209  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:27:18,216  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:28:18,223  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:29:18,230  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:30:18,236  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:31:18,244  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:32:18,249  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:33:18,255  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:34:18,262  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:35:18,268  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:36:18,275  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:37:18,282  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:38:18,288  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:39:18,295  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:40:18,302  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:41:18,309  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:42:18,316  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:43:18,323  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:44:18,330  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:45:18,336  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:46:18,342  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:47:18,349  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:48:18,355  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:49:18,361  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:50:18,368  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:51:18,375  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:52:18,382  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:53:18,389  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:54:18,396  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:55:18,402  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:56:18,409  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:57:18,416  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:58:18,423  main        INFO   c.a.c.a.Agent    running
2024-06-24 14:59:18,430  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:00:18,436  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:01:18,443  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:02:18,449  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:03:18,456  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:04:18,463  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:05:18,469  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:06:18,475  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:07:18,481  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:08:18,488  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:09:18,494  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:10:18,501  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:11:18,508  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:12:18,515  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:13:18,521  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:14:18,528  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:15:18,535  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:16:18,542  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:17:18,548  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:18:18,555  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:19:18,562  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:20:18,571  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:21:18,578  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:22:18,585  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:23:18,592  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:24:18,599  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:25:18,606  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:26:18,613  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:27:18,621  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:28:18,628  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:29:18,634  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:30:18,640  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:31:18,647  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:32:18,654  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:33:18,660  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:34:18,668  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:35:18,674  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:36:18,681  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:37:18,687  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:38:18,692  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:39:18,698  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:40:18,704  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:41:18,711  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:42:18,717  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:43:18,725  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:44:18,732  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:45:18,739  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:46:18,745  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:47:18,752  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:48:18,759  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:49:18,766  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:50:18,772  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:51:18,780  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:52:18,786  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:53:18,793  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:54:18,799  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:55:18,805  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:56:18,812  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:57:18,818  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:58:18,826  main        INFO   c.a.c.a.Agent    running
2024-06-24 15:59:18,832  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:00:18,838  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:01:18,844  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:02:18,851  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:03:18,857  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:04:18,862  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:05:18,868  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:06:18,875  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:07:18,881  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:08:18,887  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:09:18,892  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:10:18,898  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:11:18,904  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:12:18,910  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:13:18,916  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:14:18,923  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:15:18,929  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:16:18,935  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:17:18,942  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:18:18,948  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:19:18,956  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:20:18,962  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:21:18,969  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:22:18,976  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:23:18,983  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:24:18,990  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:25:18,998  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:26:19,005  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:27:19,012  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:28:19,019  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:29:19,026  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:30:19,033  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:31:19,040  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:32:19,047  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:33:19,054  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:34:19,062  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:35:19,070  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:36:19,077  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:37:19,084  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:38:19,091  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:39:19,099  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:40:19,106  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:41:19,112  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:42:19,119  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:43:19,126  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:44:19,133  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:45:19,140  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:46:19,146  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:47:19,153  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:48:19,160  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:49:19,167  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:50:19,174  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:51:19,181  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:52:19,188  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:53:19,194  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:54:19,201  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:55:19,207  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:56:19,214  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:57:19,220  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:58:19,227  main        INFO   c.a.c.a.Agent    running
2024-06-24 16:59:19,233  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:00:19,240  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:01:19,246  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:02:19,253  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:02:47,336  -thread-12  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 17:02:47,336  -thread-12  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"169445","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"72034a08-9abf-3488-99ec-7b7383d44494","session":"72034a08-9abf-3488-99ec-7b7383d44494","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"datahub_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"72034a08-9abf-3488-99ec-7b7383d44494","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"72034a08-9abf-3488-99ec-7b7383d44494","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"72034a08-9abf-3488-99ec-7b7383d44494","event_category":"unifiedAndEngage","imprint_id":"0","datahub_id":"72034a08-9abf-3488-99ec-7b7383d44494","event_datetime_utc":"*************","guid":"1eb0412b-39ea-4866-8af5-a16e1c71270f","applicationId":"SAS360Proxy","customer_id":"58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"1eb0412b-39ea-4866-8af5-a16e1c71270f"}
2024-06-24 17:02:47,336  -thread-12  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 17:02:47,336  -thread-12  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 17:02:47,336  -thread-13  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 17:02:47,336  -thread-13  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"72034a08-9abf-3488-99ec-7b7383d44494","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"72034a08-9abf-3488-99ec-7b7383d44494","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","datahub_id":"72034a08-9abf-3488-99ec-7b7383d44494","event_datetime_utc":"*************","guid":"fa5fa97c-52e7-4b3e-abff-bab6328007a2","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"169445","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"72034a08-9abf-3488-99ec-7b7383d44494","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"datahub_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"72034a08-9abf-3488-99ec-7b7383d44494","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"72034a08-9abf-3488-99ec-7b7383d44494","event_category":"unifiedAndEngage","customer_id":"58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"fa5fa97c-52e7-4b3e-abff-bab6328007a2"}
2024-06-24 17:02:47,336  -thread-13  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 17:02:47,336  -thread-13  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 17:02:47,341  -thread-12  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 17:02:47,341  -thread-12  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 72034a08-9abf-3488-99ec-7b7383d44494, UPDATED_DTTM = 2024-06-24 17:02:47.341, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 72034a08-9abf-3488-99ec-7b7383d44494, SPOT_ID = , LEAD_ID = 72034a08-9abf-3488-99ec-7b7383d44494_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 169445, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-06-24 17:02:46.976, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 1eb0412b-39ea-4866-8af5-a16e1c71270f, EMAIL_IMPRINT_URL = , SESSION_ID = 72034a08-9abf-3488-99ec-7b7383d44494, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 17:02:47.341, 
2024-06-24 17:02:47,341  -thread-12  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:02:47,342  -thread-13  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 17:02:47,343  -thread-13  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 72034a08-9abf-3488-99ec-7b7383d44494, UPDATED_DTTM = 2024-06-24 17:02:47.342, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = 72034a08-9abf-3488-99ec-7b7383d44494, SPOT_ID = , LEAD_ID = 72034a08-9abf-3488-99ec-7b7383d44494_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = 169445, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-06-24 17:02:47.038, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = fa5fa97c-52e7-4b3e-abff-bab6328007a2, EMAIL_IMPRINT_URL = , SESSION_ID = 72034a08-9abf-3488-99ec-7b7383d44494, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 17:02:47.342, 
2024-06-24 17:02:47,343  -thread-13  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:02:47,351  -thread-13  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:02:47,352  -thread-12  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:02:51,021  -thread-14  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 17:02:51,021  -thread-14  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"ed84b2f0-9ad8-4d5b-b1aa-a68eedc43063","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"ed84b2f0-9ad8-4d5b-b1aa-a68eedc43063"}
2024-06-24 17:02:51,021  -thread-14  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 17:02:51,021  -thread-14  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 17:02:51,023  -thread-14  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 17:02:51,024  -thread-14  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-24 17:02:51.023, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-24 17:02:50.729, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = ed84b2f0-9ad8-4d5b-b1aa-a68eedc43063, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 17:02:51.023, 
2024-06-24 17:02:51,024  -thread-14  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:02:51,030  -thread-14  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:03:01,476  -thread-15  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 17:03:01,476  -thread-15  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"169445","testFlag":"0","generatedTimestamp":"1719241381245","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"72034a08-9abf-3488-99ec-7b7383d44494","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719241381245","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"4a8b7ddb-6aa9-4e1b-a39b-771e6fd59c54","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/4/cixniceu/4a8b7ddb-6aa9-4e1b-a39b-771e6fd59c54.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"72034a08-9abf-3488-99ec-7b7383d44494","guid":"8a542e3a-d53d-4f3b-9c0e-739b2760bd3f","customer_id":"58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"8a542e3a-d53d-4f3b-9c0e-739b2760bd3f"}
2024-06-24 17:03:01,476  -thread-15  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from application cache, duration[ms]: 0
2024-06-24 17:03:01,476  -thread-15  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId= loaded from application cache, duration[ms]: 0
2024-06-24 17:03:01,482  -thread-15  ERROR  c.a.c.c.CampaignCache    Exception()
java.sql.SQLSyntaxErrorException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:807)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:983)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1168)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3713)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1167)
	at cz.ab.ci360.cache.CampaignCache.loadDatabaseCampaignCp(CampaignCache.java:85)
	at cz.ab.ci360.cache.CampaignCache.getCamapign(CampaignCache.java:53)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:101)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: "CAMPAING_MESSAGE_CD": invalid identifier

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 23 common frames omitted
2024-06-24 17:03:01,482  -thread-15  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 72034a08-9abf-3488-99ec-7b7383d44494, UPDATED_DTTM = 2024-06-24 17:03:01.482, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 72034a08-9abf-3488-99ec-7b7383d44494, SPOT_ID = , LEAD_ID = 72034a08-9abf-3488-99ec-7b7383d44494_af3610c7-ead4-4b06-8e88-d8985c9df954_1719241381245, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = 169445, MESSAGE_ID = , IMPRINT_ID = 4a8b7ddb-6aa9-4e1b-a39b-771e6fd59c54, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-06-24 17:03:01.245, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 8a542e3a-d53d-4f3b-9c0e-739b2760bd3f, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/4/cixniceu/4a8b7ddb-6aa9-4e1b-a39b-771e6fd59c54.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 17:03:01.482, 
2024-06-24 17:03:01,482  -thread-15  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:03:01,489  -thread-15  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:03:01,491  -thread-15  ERROR  c.a.c.a.Event    Error when processing event.
java.lang.NullPointerException: null
	at cz.ab.ci360.agent_ch_stream.Event.processEmail(Event.java:153)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:119)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 17:03:01,491  -thread-15  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"169445","testFlag":"0","generatedTimestamp":"1719241381245","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"72034a08-9abf-3488-99ec-7b7383d44494","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719241381245","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"4a8b7ddb-6aa9-4e1b-a39b-771e6fd59c54","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/4/cixniceu/4a8b7ddb-6aa9-4e1b-a39b-771e6fd59c54.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"72034a08-9abf-3488-99ec-7b7383d44494","guid":"8a542e3a-d53d-4f3b-9c0e-739b2760bd3f","customer_id":"58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"8a542e3a-d53d-4f3b-9c0e-739b2760bd3f"}
2024-06-24 17:03:19,259  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:04:19,267  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:05:19,274  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:06:19,280  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:07:19,287  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:08:19,294  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:09:19,300  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:10:19,306  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:11:19,312  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:12:19,319  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:13:19,325  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:14:19,332  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:15:19,338  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:16:19,344  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:17:19,351  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:18:19,357  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:19:19,364  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:20:19,371  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:21:19,378  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:22:19,385  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:23:19,392  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:24:19,399  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:25:19,407  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:26:19,414  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:27:19,421  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:28:19,427  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:29:19,434  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:30:19,441  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:31:19,447  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:32:19,453  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:33:19,460  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:34:19,467  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:35:19,474  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:36:19,480  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:37:19,486  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:38:19,493  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:39:19,500  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:40:19,507  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:41:19,514  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:42:19,522  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:43:19,528  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:44:19,534  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:45:19,543  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:46:19,550  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:47:19,557  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:48:19,564  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:49:19,570  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:50:19,577  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:51:19,584  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:52:19,591  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:52:59,596  main        INFO   c.a.c.a.Agent    Stop reason: 'signal'
2024-06-24 17:52:59,606  main        INFO   c.s.m.a.s.CI360Agent    Stopping event stream connection now
2024-06-24 17:52:59,607  main        INFO   o.e.j.w.c.WebSocketClient    Shutdown WebSocketClient@d214e748[coreClient=WebSocketCoreClient@3cc41abc{STARTED},openSessions.size=1]
2024-06-24 17:52:59,610  5623216-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-24 17:52:59,610  5623216-24  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-24 17:52:59,615  5623216-24  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-24 17:52:59,616  5623216-24  DEBUG  c.a.c.a.Agent    Stream closed
2024-06-24 17:53:00,617  main        INFO   c.a.c.a.Agent    Exit code: 0
2024-06-24 17:53:23,466  main        INFO   c.a.c.a.Agent    
2024-06-24 17:53:23,468  main        INFO   c.a.c.a.Agent    ----- starting -------------------------------------------------------
2024-06-24 17:53:23,472  main        INFO   c.a.c.a.Agent    App: /opt/agents/ch-stream/agent-ch-stream.jar
2024-06-24 17:53:23,475  main        INFO   c.a.c.a.Agent    Version: 1.0.0
2024-06-24 17:53:23,475  main        INFO   c.a.c.a.Agent    ----- configuration --------------------------------------------------
2024-06-24 17:53:23,478  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-24 17:53:23,479  main        INFO   c.a.c.c.Config    ci360.tenantID=f0cb22506600016b1805ee8b
2024-06-24 17:53:23,480  main        INFO   c.a.c.c.Config    agent.max_threads=5000
2024-06-24 17:53:23,480  main        INFO   c.a.c.c.Config    kafka.topic=sentCampaingEmail
2024-06-24 17:53:23,480  main        INFO   c.a.c.c.Config    agent.kafka=false
2024-06-24 17:53:23,481  main        INFO   c.a.c.c.Config    proxy.server=proxy.np.ab
2024-06-24 17:53:23,481  main        INFO   c.a.c.c.Config    proxy.port=3128
2024-06-24 17:53:23,481  main        INFO   c.a.c.c.Config    proxy.realm=proxy
2024-06-24 17:53:23,481  main        INFO   c.a.c.c.Config    proxy.user=sascloud_user
2024-06-24 17:53:23,481  main        INFO   c.a.c.c.Config    proxy.non_proxy_hosts=app-sas-test.np.ab
2024-06-24 17:53:23,481  main        INFO   c.a.c.a.Agent    test mode = false
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    db.user=APP_CAMPAIGN_CDM2
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    db.database=APP_CAMPAIGN_CDM2
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    db.server=DBS01.DE11.NP.AB:1621
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    db.service_name=MNDE11DW.NP.AB
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    db.init_connection_pool_size=10
2024-06-24 17:53:23,482  main        INFO   c.a.c.c.Config    db.max_connection_pool_size=200
2024-06-24 17:53:24,260  main        INFO   c.a.c.c.Config    event.process_event_with_prefix=c_
2024-06-24 17:53:24,261  main        INFO   c.a.c.c.Config    event.direct_event=c_direct
2024-06-24 17:53:24,261  main        INFO   c.a.c.c.Config    event.ignore_events=c_activitystart,c_abtestpathassignment
2024-06-24 17:53:24,261  main        INFO   c.a.c.a.Agent    ----- eof configuration --------------------------------------------------
2024-06-24 17:53:24,264  main        INFO   c.s.m.a.s.CI360Agent    Maximum upstream queue size: 1000
2024-06-24 17:53:24,265  main        INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 17:53:24,323  main        INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-24 17:53:24,971  5623216-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-24 17:53:25,032  5623216-24  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-24 17:53:25,047  5623216-24  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-24 17:53:25,048  5623216-24  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-24 17:53:25,048  5623216-24  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-24 17:54:24,477  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:55:03,474  2-thread-1  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 17:55:03,474  2-thread-1  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"e5ed1264-7f59-483b-9159-2f47189bf7fb","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"e5ed1264-7f59-483b-9159-2f47189bf7fb"}
2024-06-24 17:55:03,580  2-thread-1  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from TaskCache table cie_contact_history_stream_task_cp_cache, duration[ms]: 103
2024-06-24 17:55:03,580  2-thread-1  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 17:55:03,587  2-thread-1  DEBUG  c.a.c.c.CampaignCache    CamapignVersionId=MSG_14 loaded from table APP_CAMPAIGN_CDM2.CIE_CAMPAIGN_MESSAGE, duration[ms]: 5
2024-06-24 17:55:03,590  2-thread-1  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-24 17:55:03.589, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-24 17:55:03.286, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = e5ed1264-7f59-483b-9159-2f47189bf7fb, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 17:55:03.589, 
2024-06-24 17:55:03,590  2-thread-1  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:55:03,599  2-thread-1  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:55:24,488  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:56:05,992  2-thread-2  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 17:56:05,992  2-thread-2  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"*********","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"*********","session":"*********","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"*********","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"*********","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"b03752ce-09d0-4525-a448-97fb2ba298ca","applicationId":"SAS360Proxy","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"b03752ce-09d0-4525-a448-97fb2ba298ca"}
2024-06-24 17:56:05,994  2-thread-2  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 17:56:05,994  2-thread-2  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 17:56:05,995  2-thread-2  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 17:56:05,995  2-thread-2  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 17:56:05.995, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = *********, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 17:56:05.719, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = b03752ce-09d0-4525-a448-97fb2ba298ca, EMAIL_IMPRINT_URL = , SESSION_ID = *********, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 17:56:05.995, 
2024-06-24 17:56:05,996  2-thread-2  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:56:05,999  2-thread-3  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 17:56:05,999  2-thread-3  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"*********","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"223c076a-af14-49aa-98b1-8ddb89308084","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"*********","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"*********","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"*********","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"*********","event_category":"unifiedAndEngage","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"223c076a-af14-49aa-98b1-8ddb89308084"}
2024-06-24 17:56:06,002  2-thread-3  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from TaskCache table cie_contact_history_stream_task_cp_cache, duration[ms]: 2
2024-06-24 17:56:06,003  2-thread-2  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:56:06,022  2-thread-3  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from CreativeCache table cie_contact_history_stream_CRT_cp_cache, duration[ms]: 19
2024-06-24 17:56:06,027  2-thread-3  DEBUG  c.a.c.c.CampaignCache    CamapignVersionId=MSG_19 loaded from table APP_CAMPAIGN_CDM2.CIE_CAMPAIGN_MESSAGE, duration[ms]: 5
2024-06-24 17:56:06,028  2-thread-3  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 17:56:06.028, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = *********, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 17:56:05.756, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = 223c076a-af14-49aa-98b1-8ddb89308084, EMAIL_IMPRINT_URL = , SESSION_ID = *********, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 17:56:06.028, 
2024-06-24 17:56:06,028  2-thread-3  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:56:06,037  2-thread-3  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:56:19,574  2-thread-4  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 17:56:19,574  2-thread-4  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"*********","testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"66c84cac-7b00-41a7-a7c8-74fd9693d965","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"a6251cc1-344f-4e23-88d9-6fba8b401d9b","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P","recipientDomain":"airbank.cz","goal_guid":"942e528c-9698-48f1-ac21-d51853f6ea71","event_category":"unifiedAndEngage","imprint_id":"ce834756-94ef-4cb4-9ac5-b6950c689017","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/c/cixniceu/ce834756-94ef-4cb4-9ac5-b6950c689017.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"3ea8c442-4346-4a8c-aa84-bb31fd66dd6b","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"3ea8c442-4346-4a8c-aa84-bb31fd66dd6b"}
2024-06-24 17:56:22,291  2-thread-4  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"66c84cac-7b00-41a7-a7c8-74fd9693d965","name":"SIT EMAIL","lastModifiedTimeStamp":"Mon May 27 09:12:17 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon May 27 09:12:16 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":1,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_85","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_14"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["X-Sell"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":null}
2024-06-24 17:56:22,292  2-thread-4  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"66c84cac-7b00-41a7-a7c8-74fd9693d965","name":"SIT EMAIL","lastModifiedTimeStamp":"Mon May 27 09:12:17 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon May 27 09:12:16 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":1,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_85","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_14"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["X-Sell"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Cash Loan"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":null}
2024-06-24 17:56:22,294  2-thread-4  DEBUG  c.a.c.c.TaskCache    TaskVersionId=XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P loaded from CI360 API, and saved to TaskCache table, duration[ms]: 2719
2024-06-24 17:56:22,303  2-thread-4  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 17:56:22,304  2-thread-4  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 17:56:22,304  2-thread-4  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 17:56:22.304, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_a6251cc1-344f-4e23-88d9-6fba8b401d9b_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = *********, MESSAGE_ID = , IMPRINT_ID = ce834756-94ef-4cb4-9ac5-b6950c689017, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 17:56:19.204, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = a6251cc1-344f-4e23-88d9-6fba8b401d9b, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 3ea8c442-4346-4a8c-aa84-bb31fd66dd6b, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/c/cixniceu/ce834756-94ef-4cb4-9ac5-b6950c689017.html, SESSION_ID = , TASK_ID = 66c84cac-7b00-41a7-a7c8-74fd9693d965, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 17:56:22.304, 
2024-06-24 17:56:22,304  2-thread-4  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:56:22,311  2-thread-4  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:56:22,312  2-thread-4  DEBUG  c.a.c.a.Event    message to kafka: {"creator":"SAS360","Products":["LOANS"],"cuid":*********,"personFirtsName":null,"businessSummaryCauseCode":"CH/05/SEGD","created":"2024-06-24T17:56:22.311476+02:00","timeSent":"2024-06-24T17:56:22.311476+02:00","externalId":"de352c11-06a4-310d-ba7b-71107b6d63a1_a6251cc1-344f-4e23-88d9-6fba8b401d9b_*************","emailSubject":"TEST SIT EMAIL","emailURL":"https://d3on7v574i947w.cloudfront.net/c/cixniceu/ce834756-94ef-4cb4-9ac5-b6950c689017.html","communicationCode":"XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P","communicationName":"TODO","relatedCuid":null,"personEmail":null,"personLastName":null,"communicationKind":"INFO","campaignName":"Campaign Brief test","campaignCode":"MSG_14"}
2024-06-24 17:56:24,001  2-thread-5  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 17:56:24,001  2-thread-5  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"*********","testFlag":"0","generatedTimestamp":"1719244583805","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719244583805","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"99db5ba9-54d2-4178-a073-7445187b015c","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/9/cixniceu/99db5ba9-54d2-4178-a073-7445187b015c.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"26a3d2e7-96ea-4e70-8c01-9eec197ba368","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"26a3d2e7-96ea-4e70-8c01-9eec197ba368"}
2024-06-24 17:56:24,003  2-thread-5  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from TaskCache table cie_contact_history_stream_task_cp_cache, duration[ms]: 1
2024-06-24 17:56:24,003  2-thread-5  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 17:56:24,005  2-thread-5  DEBUG  c.a.c.c.CampaignCache    CamapignVersionId=MSG_22 loaded from table APP_CAMPAIGN_CDM2.CIE_CAMPAIGN_MESSAGE, duration[ms]: 1
2024-06-24 17:56:24,006  2-thread-5  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 17:56:24.005, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_af3610c7-ead4-4b06-8e88-d8985c9df954_1719244583805, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = *********, MESSAGE_ID = , IMPRINT_ID = 99db5ba9-54d2-4178-a073-7445187b015c, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 17:56:23.805, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 26a3d2e7-96ea-4e70-8c01-9eec197ba368, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/9/cixniceu/99db5ba9-54d2-4178-a073-7445187b015c.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 17:56:24.005, 
2024-06-24 17:56:24,006  2-thread-5  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 17:56:24,010  2-thread-5  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 17:56:24,010  2-thread-5  DEBUG  c.a.c.a.Event    message to kafka: {"creator":"SAS360","Products":["ACCOUNTS_PAYMENTS"],"cuid":*********,"personFirtsName":null,"businessSummaryCauseCode":"CH/05/SEGD","created":"2024-06-24T17:56:24.010219+02:00","timeSent":"2024-06-24T17:56:24.010219+02:00","externalId":"de352c11-06a4-310d-ba7b-71107b6d63a1_af3610c7-ead4-4b06-8e88-d8985c9df954_1719244583805","emailSubject":"TEST SIT EMAIL","emailURL":"https://d3on7v574i947w.cloudfront.net/9/cixniceu/99db5ba9-54d2-4178-a073-7445187b015c.html","communicationCode":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","communicationName":"TODO","relatedCuid":null,"personEmail":null,"personLastName":null,"communicationKind":"INFO","campaignName":"CampBrief_CHStreamTEst","campaignCode":"MSG_22"}
2024-06-24 17:56:24,499  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:57:24,510  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:58:24,521  main        INFO   c.a.c.a.Agent    running
2024-06-24 17:59:24,532  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:00:24,541  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:01:24,549  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:02:24,558  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:03:24,566  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:04:11,510  2-thread-6  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 18:04:11,510  2-thread-6  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","session":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"datahub_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH2","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_category":"unifiedAndEngage","imprint_id":"0","datahub_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_datetime_utc":"*************","guid":"270cb5b5-cdf4-490f-b726-1366ca1f5e5b","applicationId":"SAS360Proxy","customer_id":"customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"270cb5b5-cdf4-490f-b726-1366ca1f5e5b"}
2024-06-24 18:04:11,510  2-thread-6  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 18:04:11,510  2-thread-6  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:04:11,510  2-thread-6  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:04:11,511  2-thread-6  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, UPDATED_DTTM = 2024-06-24 18:04:11.51, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, SPOT_ID = , LEAD_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = , MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-06-24 18:04:11.274, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 270cb5b5-cdf4-490f-b726-1366ca1f5e5b, EMAIL_IMPRINT_URL = , SESSION_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:04:11.51, 
2024-06-24 18:04:11,511  2-thread-6  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:04:11,519  2-thread-6  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-24 18:04:11,519  2-thread-6  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-24 18:04:11,520  2-thread-6  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","session":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"datahub_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH2","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_category":"unifiedAndEngage","imprint_id":"0","datahub_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_datetime_utc":"*************","guid":"270cb5b5-cdf4-490f-b726-1366ca1f5e5b","applicationId":"SAS360Proxy","customer_id":"customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"270cb5b5-cdf4-490f-b726-1366ca1f5e5b"}
2024-06-24 18:04:11,657  2-thread-7  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 18:04:11,657  2-thread-7  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH2","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","datahub_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_datetime_utc":"*************","guid":"085e3570-5658-48bd-83b0-540e2f5814fa","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"datahub_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_category":"unifiedAndEngage","customer_id":"customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"085e3570-5658-48bd-83b0-540e2f5814fa"}
2024-06-24 18:04:11,658  2-thread-7  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 18:04:11,658  2-thread-7  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 18:04:11,658  2-thread-7  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_19 loaded from application cache, duration[ms]: 0
2024-06-24 18:04:11,659  2-thread-7  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, UPDATED_DTTM = 2024-06-24 18:04:11.658, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, SPOT_ID = , LEAD_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = , MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-06-24 18:04:11.374, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = 085e3570-5658-48bd-83b0-540e2f5814fa, EMAIL_IMPRINT_URL = , SESSION_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:04:11.658, 
2024-06-24 18:04:11,659  2-thread-7  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:04:11,664  2-thread-7  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-24 18:04:11,665  2-thread-7  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-24 18:04:11,666  2-thread-7  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH2","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","datahub_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_datetime_utc":"*************","guid":"085e3570-5658-48bd-83b0-540e2f5814fa","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"datahub_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","event_category":"unifiedAndEngage","customer_id":"customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"085e3570-5658-48bd-83b0-540e2f5814fa"}
2024-06-24 18:04:24,574  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:04:37,913  2-thread-8  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 18:04:37,914  2-thread-8  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"b3062d5f-81c2-4d13-a177-75c0fee9f491","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"6c31fbda-ff8c-4ff7-8973-61d521993c1a","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7","recipientDomain":"sas.com","goal_guid":"3fb9ea52-4e6d-48c3-af06-cb5e6c3f239a","event_category":"unifiedAndEngage","imprint_id":"3077d130-0c7f-497f-a96c-b7c5aab16b97","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/3/cixniceu/3077d130-0c7f-497f-a96c-b7c5aab16b97.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","guid":"92bc8d81-09b7-470a-ac95-4c1cab2e754d","customer_id":"customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"92bc8d81-09b7-470a-ac95-4c1cab2e754d"}
2024-06-24 18:04:38,309  2-thread-8  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"b3062d5f-81c2-4d13-a177-75c0fee9f491","name":"SIT EMAIL CHStream 2","lastModifiedTimeStamp":"Mon Jun 24 16:03:08 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon Jun 24 16:03:07 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":1,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_108","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","INSURANCE"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentEmail","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH2"],"attributeOperator":"EQ"}]}}
2024-06-24 18:04:38,309  2-thread-8  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"b3062d5f-81c2-4d13-a177-75c0fee9f491","name":"SIT EMAIL CHStream 2","lastModifiedTimeStamp":"Mon Jun 24 16:03:08 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon Jun 24 16:03:07 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"email","taskType":"single","numSpots":1,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_108","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["EML"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["ACCOUNTS_PAYMENTS","LOANS","INSURANCE"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["INFO"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["CH/05/SEGD"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_4"]},{"propertyName":"tsk_cp_product","propertyValue":["MOBILITA"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentEmail","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH2"],"attributeOperator":"EQ"}]}}
2024-06-24 18:04:38,310  2-thread-8  DEBUG  c.a.c.c.TaskCache    TaskVersionId=TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7 loaded from CI360 API, and saved to TaskCache table, duration[ms]: 396
2024-06-24 18:04:38,322  2-thread-8  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:04:38,323  2-thread-8  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_22 loaded from application cache, duration[ms]: 0
2024-06-24 18:04:38,324  2-thread-8  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, UPDATED_DTTM = 2024-06-24 18:04:38.323, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb, SPOT_ID = , LEAD_ID = c2b2038b-0bd8-3569-a45f-a6671ab73ddb_6c31fbda-ff8c-4ff7-8973-61d521993c1a_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = , MESSAGE_ID = , IMPRINT_ID = 3077d130-0c7f-497f-a96c-b7c5aab16b97, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442, CONTACT_DTTM = 2024-06-24 18:04:37.661, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 6c31fbda-ff8c-4ff7-8973-61d521993c1a, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 92bc8d81-09b7-470a-ac95-4c1cab2e754d, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/3/cixniceu/3077d130-0c7f-497f-a96c-b7c5aab16b97.html, SESSION_ID = , TASK_ID = b3062d5f-81c2-4d13-a177-75c0fee9f491, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 18:04:38.323, 
2024-06-24 18:04:38,324  2-thread-8  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:04:38,331  2-thread-8  ERROR  c.a.c.a.Event    ErrorCode:CH_02 - DB - not possible to write payload to DB
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-24 18:04:38,332  2-thread-8  ERROR  c.a.c.a.Event    Error when processing event.
java.sql.SQLException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:509)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:461)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1104)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:550)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:268)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:655)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:270)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:91)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForRows(T4CPreparedStatement.java:970)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1205)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3666)
	at oracle.jdbc.driver.T4CPreparedStatement.executeInternal(T4CPreparedStatement.java:1426)
	at oracle.jdbc.driver.OraclePreparedStatement.execute(OraclePreparedStatement.java:3778)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.execute(OraclePreparedStatementWrapper.java:1081)
	at cz.ab.ci360.agent_ch_stream.Event.saveToDb(Event.java:397)
	at cz.ab.ci360.agent_ch_stream.Event.processMessage(Event.java:116)
	at cz.ab.ci360.agent_ch_stream.Event.run(Event.java:69)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-12899: value too large for column "APP_CAMPAIGN_CDM2"."CIE_CONTACT_HISTORY_STREAM"."CUSTOMER_ID" (actual: 105, maximum: 100)

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:513)
	... 21 common frames omitted
2024-06-24 18:04:38,332  2-thread-8  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"b3062d5f-81c2-4d13-a177-75c0fee9f491","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"6c31fbda-ff8c-4ff7-8973-61d521993c1a","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7","recipientDomain":"sas.com","goal_guid":"3fb9ea52-4e6d-48c3-af06-cb5e6c3f239a","event_category":"unifiedAndEngage","imprint_id":"3077d130-0c7f-497f-a96c-b7c5aab16b97","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/3/cixniceu/3077d130-0c7f-497f-a96c-b7c5aab16b97.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"c2b2038b-0bd8-3569-a45f-a6671ab73ddb","guid":"92bc8d81-09b7-470a-ac95-4c1cab2e754d","customer_id":"customer=58b434459073189d675f5746143e0c0eac4485643f271fa6b221282df9fb559ca96d075c527d278063839a2b8f054442","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"92bc8d81-09b7-470a-ac95-4c1cab2e754d"}
2024-06-24 18:05:24,583  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:06:24,591  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:07:23,107  2-thread-9  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 18:07:23,108  2-thread-9  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"286577","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"286577","session":"286577","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH2","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"286577","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"286577","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","guid":"25e2b0f1-b7d0-463e-a7f5-70f9fa3c2343","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"25e2b0f1-b7d0-463e-a7f5-70f9fa3c2343"}
2024-06-24 18:07:23,108  2-thread-9  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 18:07:23,108  2-thread-9  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:07:23,108  2-thread-9  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:07:23,108  2-thread-9  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, UPDATED_DTTM = 2024-06-24 18:07:23.108, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, SPOT_ID = , LEAD_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 286577, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 18:07:22.951, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 25e2b0f1-b7d0-463e-a7f5-70f9fa3c2343, EMAIL_IMPRINT_URL = , SESSION_ID = 286577, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:07:23.108, 
2024-06-24 18:07:23,108  2-thread-9  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:07:23,117  2-thread-9  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:07:23,169  -thread-10  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 18:07:23,169  -thread-10  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"286577","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH2","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","guid":"c20ea177-571d-41e0-a655-a8728cd106ce","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"286577","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"286577","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"286577","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"286577","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"c20ea177-571d-41e0-a655-a8728cd106ce"}
2024-06-24 18:07:23,170  -thread-10  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 18:07:23,170  -thread-10  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 18:07:23,170  -thread-10  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_19 loaded from application cache, duration[ms]: 0
2024-06-24 18:07:23,170  -thread-10  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, UPDATED_DTTM = 2024-06-24 18:07:23.17, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, SPOT_ID = , LEAD_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = 286577, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 18:07:22.963, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = c20ea177-571d-41e0-a655-a8728cd106ce, EMAIL_IMPRINT_URL = , SESSION_ID = 286577, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:07:23.17, 
2024-06-24 18:07:23,170  -thread-10  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:07:23,184  -thread-10  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:07:24,599  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:07:33,896  -thread-11  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 18:07:33,897  -thread-11  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"286577","testFlag":"0","generatedTimestamp":"1719245253678","screen_info":"x@","task_id":"b3062d5f-81c2-4d13-a177-75c0fee9f491","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"6c31fbda-ff8c-4ff7-8973-61d521993c1a","variant":"0","eventName":"c_send","event":"c_send","timestamp":"1719245253678","event_channel":"email","internal_tenant_id":"********","task_version_id":"TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7","recipientDomain":"sas.com","goal_guid":"3fb9ea52-4e6d-48c3-af06-cb5e6c3f239a","event_category":"unifiedAndEngage","imprint_id":"566e71d8-b33e-4f18-ba01-89cf09b3dd11","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/5/cixniceu/566e71d8-b33e-4f18-ba01-89cf09b3dd11.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","guid":"10b58270-79da-44aa-af1c-32780be7817a","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"10b58270-79da-44aa-af1c-32780be7817a"}
2024-06-24 18:07:33,897  -thread-11  DEBUG  c.a.c.c.TaskCache    TaskVersionId=TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7 loaded from application cache, duration[ms]: 0
2024-06-24 18:07:33,897  -thread-11  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:07:33,897  -thread-11  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_22 loaded from application cache, duration[ms]: 0
2024-06-24 18:07:33,897  -thread-11  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, UPDATED_DTTM = 2024-06-24 18:07:33.897, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, SPOT_ID = , LEAD_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964_6c31fbda-ff8c-4ff7-8973-61d521993c1a_1719245253678, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = 286577, MESSAGE_ID = , IMPRINT_ID = 566e71d8-b33e-4f18-ba01-89cf09b3dd11, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 18:07:33.678, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 6c31fbda-ff8c-4ff7-8973-61d521993c1a, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 10b58270-79da-44aa-af1c-32780be7817a, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/5/cixniceu/566e71d8-b33e-4f18-ba01-89cf09b3dd11.html, SESSION_ID = , TASK_ID = b3062d5f-81c2-4d13-a177-75c0fee9f491, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 18:07:33.897, 
2024-06-24 18:07:33,897  -thread-11  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:07:33,905  -thread-11  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:07:33,905  -thread-11  DEBUG  c.a.c.a.Event    message to kafka: {"creator":"SAS360","Products":["ACCOUNTS_PAYMENTS"],"cuid":286577,"personFirtsName":null,"businessSummaryCauseCode":"CH/05/SEGD","created":"2024-06-24T18:07:33.905674+02:00","timeSent":"2024-06-24T18:07:33.905674+02:00","externalId":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964_6c31fbda-ff8c-4ff7-8973-61d521993c1a_1719245253678","emailSubject":"TEST SIT EMAIL","emailURL":"https://d3on7v574i947w.cloudfront.net/5/cixniceu/566e71d8-b33e-4f18-ba01-89cf09b3dd11.html","communicationCode":"TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7","communicationName":"TODO","relatedCuid":null,"personEmail":null,"personLastName":null,"communicationKind":"INFO","campaignName":"CampBrief_CHStreamTEst","campaignCode":"MSG_22"}
2024-06-24 18:08:24,607  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:09:24,615  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:10:24,623  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:11:24,631  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:12:24,638  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:13:24,646  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:14:24,653  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:14:32,445  -thread-13  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 18:14:32,445  -thread-13  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"345729","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"345729","session":"345729","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"e807ede5-b392-3d89-9827-c364c7a6f1e8","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"345729","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","sentPush":"Y","message_id":"0","sessionId":"345729","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"e807ede5-b392-3d89-9827-c364c7a6f1e8","guid":"********-b081-420d-b377-ef75bc760280","applicationId":"SAS360Proxy","customer_id":"c8843c8b54d4428ccdc773447a9965549bd5a29efb5717d338f2146caad153b10d90f5b39ad45c5e54889579c87e51f6","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"********-b081-420d-b377-ef75bc760280"}
2024-06-24 18:14:32,445  -thread-13  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 18:14:32,445  -thread-13  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:14:32,445  -thread-13  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:14:32,445  -thread-12  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 18:14:32,445  -thread-13  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = e807ede5-b392-3d89-9827-c364c7a6f1e8, UPDATED_DTTM = 2024-06-24 18:14:32.445, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = e807ede5-b392-3d89-9827-c364c7a6f1e8, SPOT_ID = , LEAD_ID = e807ede5-b392-3d89-9827-c364c7a6f1e8_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 345729, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = c8843c8b54d4428ccdc773447a9965549bd5a29efb5717d338f2146caad153b10d90f5b39ad45c5e54889579c87e51f6, CONTACT_DTTM = 2024-06-24 18:14:32.253, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = ********-b081-420d-b377-ef75bc760280, EMAIL_IMPRINT_URL = , SESSION_ID = 345729, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:14:32.445, 
2024-06-24 18:14:32,446  -thread-13  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:14:32,445  -thread-12  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_109","channel_user_id":"345729","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"14d280a0-278c-40c8-bf6a-4eca81841b10","vid":"e807ede5-b392-3d89-9827-c364c7a6f1e8","internalTenantId":"********","response_tracking_code":"055c33d3-df56-4136-9afb-5e345c732de2","primaryClickMeaning":"DISMISS_CLICK","creative_version_id":".ppUsZUjStnFmyVKHFRKei5jVZc1gjhA","eventName":"c_X_phsjkjjd","text":"Test CH","event_channel":"external","task_version_id":"mpsltS4QjVRH_LKCyyLTns6mfV_M5u2.","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"e807ede5-b392-3d89-9827-c364c7a6f1e8","guid":"6968e755-674c-4b93-b841-46f7d482227d","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"345729","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"345729","screen_info":"x@","task_id":"4d1ed051-8e59-42e0-b8da-0e8195a13402","outboundProperties.Image":"fsgfsdg","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"<html style=\"--vh: 9.19px;\">\n  <head><meta content=\"text/html;charset=UTF-8\" http-equiv=\"Content-Type\" />\n \n<style>\n@media (min-width: 1102px)\n.css-a787mt {\n    display: block;\n}\n@media (min-width: 768px)\n.css-a787mt {\n    display: block;\n}\n@media screen and (min-width: 69rem)\n.css-a787mt {\n    padding-bottom: 48px;\n}\n@media screen and (min-width: 48rem)\n.css-a787mt {\n    padding-bottom: 48px;\n}\n@media screen and (min-width: 69rem)\n.css-a787mt {\n    padding-top: 48px;\n}\n@media screen and (min-width: 48rem)\n.css-a787mt {\n    padding-top: 48px;\n}\n.css-a787mt {\n    padding-top: 32px;\n    padding-bottom: 32px;\n    background-color: #41454b;\n    display: block;\n    background-size: cover;\n    width: 100%;\n}\n@media screen and (min-width: 69rem)\n.css-hh9ztf {\n    padding-right: 32px;\n}\n@media screen and (min-width: 48rem)\n.css-hh9ztf {\n    padding-right: 24px;\n}\n@media screen and (min-width: 69rem)\n.css-hh9ztf {\n    padding-left: 32px;\n}\n@media screen and (min-width: 48rem)\n.css-hh9ztf {\n    padding-left: 24px;\n}\n.css-hh9ztf {\n    margin-left: auto;\n    margin-right: auto;\n    padding-left: 16px;\n    padding-right: 16px;\n    max-width: 1232px;\n    width: 100%;\n    max-width: 1232;\n}\n\n@media screen and (min-width: 48rem)\n.css-1v2yjxn {\n    padding-right: 56px;\n}\n@media screen and (min-width: 48rem)\n.css-1v2yjxn {\n    padding-left: 56px;\n}\n.css-1v2yjxn {\n    margin-left: auto;\n    margin-right: auto;\n    padding-top: 40px;\n    padding-bottom: 40px;\n    padding-left: 40px;\n    padding-right: 40px;\n    width: 100%;\n    background-color: #f2f2f2;\n    max-width: unset;\n    height: 100%;\n    border-radius: 16px;\n    border-radius: 16px;\n    background-size: cover;\n    display: block;\n    width: 100%;\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    position: relative;\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n    -webkit-align-items: center;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n}\n\n\n.css-1v2yjxn > div:first-of-type {\n    width: 100%;\n}\n.css-7nter6 {\n    margin-right: auto;\n    margin-left: auto;\n}\n.css-1rnoae3 {\n    margin-top: -12px;\n    margin-bottom: -12px;\n    margin-left: -4px;\n    margin-right: -4px;\n    -webkit-align-items: center;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    -webkit-flex-direction: row;\n    -ms-flex-direction: row;\n    flex-direction: row;\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-flex: 0 1 auto;\n    -ms-flex: 0 1 auto;\n    flex: 0 1 auto;\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n    box-sizing: border-box;\n}\n\n@media screen and (min-width: 69rem)\n.css-soz0ez {\n    width: 66.66666666666666%;\n}\n@media screen and (min-width: 48rem)\n.css-soz0ez {\n    width: 50%;\n}\n.css-soz0ez {\n    padding-top: 12px;\n    padding-bottom: 12px;\n    padding-left: 4px;\n    padding-right: 4px;\n    width: 100%;\n    display: block;\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    outline: none;\n    position: relative;\n    outline-offset: -5px;\n}\n\n\n.css-pfatbp {\n    text-align: left;\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    height: 100%;\n    -webkit-align-items: flex-start;\n    -webkit-box-align: flex-start;\n    -ms-flex-align: flex-start;\n    align-items: flex-start;\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n}\n\n.css-l5197u {\n    color: #41454b;\n    font-family: Roboto, sans-serif;\n    line-height: 1.63;\n    font-weight: 300;\n    -webkit-letter-spacing: 0.3px;\n    -moz-letter-spacing: 0.3px;\n    -ms-letter-spacing: 0.3px;\n    letter-spacing: 0.3px;\n    width: 100%;\n    color: #41454b;\n    font-family: Roboto, sans-serif;\n    line-height: 1.63;\n    font-weight: 300;\n    -webkit-letter-spacing: 0.3px;\n    -moz-letter-spacing: 0.3px;\n    -ms-letter-spacing: 0.3px;\n    letter-spacing: 0.3px;\n}\n\n.css-1rhplxf {\n    color: #41454b;\n    font-family: Roboto, sans-serif;\n    line-height: 1.63;\n    font-weight: 300;\n    -webkit-letter-spacing: 0.3px;\n    -moz-letter-spacing: 0.3px;\n    -ms-letter-spacing: 0.3px;\n    letter-spacing: 0.3px;\n}\n\n\n@media screen and (min-width: 69rem)\n.css-1avjulb {\n    width: 33.33333333333333%;\n}\n@media screen and (min-width: 48rem)\n.css-1avjulb {\n    width: 50%;\n}\n.css-1avjulb {\n    padding-top: 12px;\n    padding-bottom: 12px;\n    padding-left: 4px;\n    padding-right: 4px;\n    width: 100%;\n    display: block;\n    -webkit-flex-direction: column;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    outline: none;\n    position: relative;\n    outline-offset: -5px;\n}\n\n@media screen and (min-width: 48rem)\n.css-1o3h4fz {\n    -webkit-box-pack: end;\n    -webkit-justify-content: flex-end;\n    -ms-flex-pack: end;\n    justify-content: flex-end;\n}\n.css-1o3h4fz {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: center;\n    -webkit-justify-content: center;\n    -ms-flex-pack: center;\n    justify-content: center;\n}\n\n.css-12e9ekn {\n    margin: -8px;\n    width: auto;\n    -webkit-align-items: center;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    -webkit-box-pack: start;\n    -webkit-justify-content: flex-start;\n    -ms-flex-pack: start;\n    justify-content: flex-start;\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-flex-flow: row wrap;\n    -ms-flex-flow: row wrap;\n    flex-flow: row wrap;\n    width: auto;\n}\n.css-e6y7lz {\n    padding: 8px;\n    display: -webkit-inline-box;\n    display: -webkit-inline-flex;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n    -webkit-align-items: center;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n}\n\n\n.css-1o8uzcm {\n    padding-left: 24px;\n    padding-right: 24px;\n    font-size: 14px;\n    color: #0a100d;\n    background-color: transparent;\n    font-family: Roboto, sans-serif;\n    font-weight: 500;\n    -webkit-letter-spacing: 0.5px;\n    -moz-letter-spacing: 0.5px;\n    -ms-letter-spacing: 0.5px;\n    letter-spacing: 0.5px;\n    border-radius: 8px;\n    border-color: rgba(122, 127, 130, 0.5);\n    border-color: rgba(122, 127, 130, 0.5);\n    border-radius: 8px;\n    box-shadow: inset 1px 1px 0 0 rgba(255,255,255,0.25);\n    text-transform: uppercase;\n    -webkit-text-decoration: none;\n    text-decoration: none;\n    min-width: 12rem;\n    height: 50px;\n    display: -webkit-inline-box;\n    display: -webkit-inline-flex;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n    -webkit-align-items: center;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    -webkit-box-pack: justify;\n    -webkit-justify-content: space-between;\n    -ms-flex-pack: justify;\n    justify-content: space-between;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    text-shadow: 0 1px 1px rgba(255,255,255,0.5);\n    border-style: solid;\n    border-width: 1px;\n    -webkit-transition: all 190ms ease;\n    transition: all 190ms ease;\n    cursor: pointer;\n    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.05));\n}\n\n\n.css-1o8uzcm span, .css-1o8uzcm svg {\n    pointer-events: none;\n}\n.css-1o8uzcm .icon-wrapper {\n    -webkit-transform: translateX(0px);\n    -ms-transform: translateX(0px);\n    transform: translateX(0px);\n    -webkit-transition: all 0.75s ease;\n    transition: all 0.75s ease;\n}\n.css-1o8uzcm span, .css-1o8uzcm svg {\n    pointer-events: none;\n}\n.css-1f9j9tb {\n    margin-left: 16px;\n}\n\n.css-1o8uzcm span, .css-1o8uzcm svg {\n    pointer-events: none;\n}\n.css-1o8uzcm svg {\n    text-shadow: 1px 1px 0 0 rgba(255, 255, 255, 0.50);\n}\nsvg:not(:root) {\n    overflow: hidden;\n}\n.css-1prq09 {\n    width: 24px;\n    fill: #0a100d;\n    height: 24px;\n}\n<\/style>\n  <\/head>\n  <body>\n<div><div id=\"JumbotronWithSticker-_ibzq10dn6\" class=\"css-a787mt e1ig6ntm0\"><section class=\"css-hh9ztf e1ig6ntm0\"><article class=\"css-1v2yjxn e1ig6ntm0\"><div class=\"css-7nter6 e1ig6ntm0\"><div class=\"css-1rnoae3 e1ig6ntm0\"><div class=\"css-soz0ez e1ig6ntm0\"><article class=\"css-pfatbp e1ig6ntm0\"><div data-list-style-type=\"check\" class=\"e1ig6ntm0 css-l5197u e1ig6ntm0\"><p class=\"css-1rhplxf e1ig6ntm0\">Máme pro vás nejlepší podnikatelský účet.<\/p><\/div><\/article><\/div><div class=\"css-1avjulb e1ig6ntm0\"><div class=\"css-1o3h4fz e1ig6ntm0\"><div class=\"css-12e9ekn e1ig6ntm0\"><div class=\"css-e6y7lz e1ig6ntm0\"><a rel=\"noopener noreferrer\" href=\"https://www.airbank.cz/produkty/podnikatelsky-ucet/\" class=\"css-1o8uzcm e1ig6ntm0\"><span class=\"css-fkt26h e1ig6ntm0\">chci zjistit více<\/span><span class=\"icon-wrapper css-1f9j9tb e1ig6ntm0\"><svg viewBox=\"0 0 25 25\" fill=\"heading\" class=\"css-1prq09 e1ig6ntm0\"><path d=\"m19.16 12-4.24-4.24a.7.7 0 1 0-1 1l3.05 3.04H6.33a.7.7 0 0 0 0 1.4h10.64l-3.04 3.05a.7.7 0 0 0 0 .99.7.7 0 0 0 .99 0l4.24-4.25a.7.7 0 0 0 0-.99\" fill-rule=\"evenodd\"><\/path><\/svg><\/span><\/a><\/div><\/div><\/div><\/div><\/div><\/div><div class=\"css-1c1853y css-1m6agl6 e1ig6ntm0\">TIP<\/div><\/article><\/section><\/div><\/div>\n  <\/body>\n<\/html>","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"345729","timestamp":"*************","internal_tenant_id":"********","sentPush":"Y","message_id":"0","sessionId":"345729","event_category":"unifiedAndEngage","customer_id":"c8843c8b54d4428ccdc773447a9965549bd5a29efb5717d338f2146caad153b10d90f5b39ad45c5e54889579c87e51f6","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"6968e755-674c-4b93-b841-46f7d482227d"}
2024-06-24 18:14:32,455  -thread-13  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:14:32,834  -thread-12  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"version":1,"taskId":"4d1ed051-8e59-42e0-b8da-0e8195a13402","name":"SIT PSH CH Stream","lastModifiedTimeStamp":"Mon Jun 24 16:13:30 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon Jun 24 16:13:30 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_109","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS","SAVINGS_INVESTMENTS","INSURANCE","OTHER"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["K/VK/01"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_1"]},{"propertyName":"tsk_cp_product","propertyValue":["UNITY"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentPush","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH"],"attributeOperator":"EQ"}]}}
2024-06-24 18:14:32,834  -thread-12  DEBUG  c.a.c.a.CI360Api    Task API json: {"version":1,"taskId":"4d1ed051-8e59-42e0-b8da-0e8195a13402","name":"SIT PSH CH Stream","lastModifiedTimeStamp":"Mon Jun 24 16:13:30 UTC 2024","lastModifiedBy":"<EMAIL>","lastPublishedTimeStamp":"Mon Jun 24 16:13:30 UTC 2024","lastPublishedBy":"<EMAIL>","state":"active","channel":"external","taskType":"custom","numSpots":0,"deliveryType":"triggered","priority":3,"businessContextName":null,"taskCode":"TSK_109","customProperties":[{"propertyName":"tsk_load_camp_brief","propertyValue":[null]},{"propertyName":"tsk_comm_camp_name","propertyValue":["MSG_22"]},{"propertyName":"tsk_load_camp_brief_details","propertyValue":[null]},{"propertyName":"tsk_load_camp_brief_status","propertyValue":["Úspěšně načteno"]},{"propertyName":"tsk_comm_chan_code","propertyValue":["PSH"]},{"propertyName":"tsk_camp_type","propertyValue":["Information"]},{"propertyName":"tsk_camp_subtype","propertyValue":["Technical"]},{"propertyName":"tsk_camp_product","propertyValue":["LOANS","SAVINGS_INVESTMENTS","INSURANCE","OTHER"]},{"propertyName":"tsk_camp_comm_type","propertyValue":["MARKETING"]},{"propertyName":"tsk_camp_buss_cause_cd","propertyValue":["K/VK/01"]},{"propertyName":"tsk_cp_type","propertyValue":["cp_1"]},{"propertyName":"tsk_cp_product","propertyValue":["UNITY"]}],"triggerCriteria":{"eventId":"25ce22c4-e395-4bc1-84f8-0c5d6454145c","eventName":"Ext_Sit_Event","eventType":"external","eventSubType":null,"eventPossibleAttributes":[{"attributeName":"sentPush","attributeValues":["Y"],"attributeOperator":"EQ"},{"attributeName":"text","attributeValues":["Test CH"],"attributeOperator":"EQ"}]}}
2024-06-24 18:14:32,834  -thread-12  DEBUG  c.a.c.c.TaskCache    TaskVersionId=mpsltS4QjVRH_LKCyyLTns6mfV_M5u2. loaded from CI360 API, and saved to TaskCache table, duration[ms]: 388
2024-06-24 18:14:33,077  -thread-12  DEBUG  c.a.c.c.Http    Response, Code: 200 , Message: {"id":"14d280a0-278c-40c8-bf6a-4eca81841b10","name":"POC: Ukazka Podnikatel CSS","status":"active","versions":[{"id":".ppUsZUjStnFmyVKHFRKei5jVZc1gjhA","validFrom":"2024-06-24 16:11:44.000","latest":true},{"id":"GMOVWkGnRO0.f.iXEQXLkJ9iWfnrOrUV","validFrom":"2024-05-07 05:56:47.000","validTo":"2024-06-24 16:11:44.000","latest":false},{"id":"GHMFIl_iM4rpHlELzZScnEMpNtdBiaq3","validFrom":"2024-04-19 13:39:03.000","validTo":"2024-05-07 05:56:47.000","latest":false},{"id":"HrkkMRGC9mB_JF.P1nI7nVxFRXDKxkLK","validFrom":"2024-04-17 18:28:20.000","validTo":"2024-04-19 13:39:03.000","latest":false}],"type":"html","createdByUser":"<EMAIL>","publishedByUser":"<EMAIL>@cixniceu","category":"cre_communication_brief","externalCode":"CRT_24","attributes":[{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"IB"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"IB"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"IB"}]}
2024-06-24 18:14:33,077  -thread-12  DEBUG  c.a.c.a.CI360Api    Creative API json: {"id":"14d280a0-278c-40c8-bf6a-4eca81841b10","name":"POC: Ukazka Podnikatel CSS","status":"active","versions":[{"id":".ppUsZUjStnFmyVKHFRKei5jVZc1gjhA","validFrom":"2024-06-24 16:11:44.000","latest":true},{"id":"GMOVWkGnRO0.f.iXEQXLkJ9iWfnrOrUV","validFrom":"2024-05-07 05:56:47.000","validTo":"2024-06-24 16:11:44.000","latest":false},{"id":"GHMFIl_iM4rpHlELzZScnEMpNtdBiaq3","validFrom":"2024-04-19 13:39:03.000","validTo":"2024-05-07 05:56:47.000","latest":false},{"id":"HrkkMRGC9mB_JF.P1nI7nVxFRXDKxkLK","validFrom":"2024-04-17 18:28:20.000","validTo":"2024-04-19 13:39:03.000","latest":false}],"type":"html","createdByUser":"<EMAIL>","publishedByUser":"<EMAIL>@cixniceu","category":"cre_communication_brief","externalCode":"CRT_24","attributes":[{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"IB"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"IB"},{"identityCode":"cre_camp_cp_product","dataType":"TEXT","value":"IB"}]}
2024-06-24 18:14:33,078  -thread-12  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=.ppUsZUjStnFmyVKHFRKei5jVZc1gjhA loaded from CI360 API, and saved to CreativeCache table, duration[ms]: 234
2024-06-24 18:14:33,094  -thread-12  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_22 loaded from application cache, duration[ms]: 0
2024-06-24 18:14:33,094  -thread-12  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = e807ede5-b392-3d89-9827-c364c7a6f1e8, UPDATED_DTTM = 2024-06-24 18:14:33.094, CREATIVE_VERSION_ID = .ppUsZUjStnFmyVKHFRKei5jVZc1gjhA, CONTROL_GROUP_FLG = , IDENTITY_ID = e807ede5-b392-3d89-9827-c364c7a6f1e8, SPOT_ID = , LEAD_ID = e807ede5-b392-3d89-9827-c364c7a6f1e8_055c33d3-df56-4136-9afb-5e345c732de2_*************, CREATIVE_ID = 14d280a0-278c-40c8-bf6a-4eca81841b10, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = IB, SUBJECT_ID = 345729, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = mpsltS4QjVRH_LKCyyLTns6mfV_M5u2., CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = c8843c8b54d4428ccdc773447a9965549bd5a29efb5717d338f2146caad153b10d90f5b39ad45c5e54889579c87e51f6, CONTACT_DTTM = 2024-06-24 18:14:32.247, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 055c33d3-df56-4136-9afb-5e345c732de2, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 6968e755-674c-4b93-b841-46f7d482227d, EMAIL_IMPRINT_URL = , SESSION_ID = 345729, TASK_ID = 4d1ed051-8e59-42e0-b8da-0e8195a13402, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:14:33.094, 
2024-06-24 18:14:33,095  -thread-12  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:14:33,103  -thread-12  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:15:24,661  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:16:21,721  -thread-14  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 18:16:21,722  -thread-14  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"286577","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"286577","session":"286577","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH2","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"286577","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"286577","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","guid":"f8512d58-483b-4ac5-8527-b07c2c0201d3","applicationId":"SAS360Proxy","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"f8512d58-483b-4ac5-8527-b07c2c0201d3"}
2024-06-24 18:16:21,722  -thread-14  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 18:16:21,722  -thread-14  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:16:21,722  -thread-14  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:21,722  -thread-14  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, UPDATED_DTTM = 2024-06-24 18:16:21.722, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, SPOT_ID = , LEAD_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 286577, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 18:16:21.553, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = f8512d58-483b-4ac5-8527-b07c2c0201d3, EMAIL_IMPRINT_URL = , SESSION_ID = 286577, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:16:21.722, 
2024-06-24 18:16:21,722  -thread-14  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:21,733  -thread-14  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:21,787  -thread-15  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 18:16:21,787  -thread-15  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"286577","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH2","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","guid":"1b244a59-3fe8-4c94-83fe-16ea7497a9fa","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"286577","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"286577","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"286577","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"286577","event_category":"unifiedAndEngage","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"1b244a59-3fe8-4c94-83fe-16ea7497a9fa"}
2024-06-24 18:16:21,787  -thread-15  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 18:16:21,787  -thread-15  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 18:16:21,787  -thread-15  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_19 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:21,787  -thread-15  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, UPDATED_DTTM = 2024-06-24 18:16:21.787, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, SPOT_ID = , LEAD_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = 286577, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 18:16:21.659, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = 1b244a59-3fe8-4c94-83fe-16ea7497a9fa, EMAIL_IMPRINT_URL = , SESSION_ID = 286577, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:16:21.787, 
2024-06-24 18:16:21,787  -thread-15  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:21,798  -thread-15  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:24,669  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:16:25,340  -thread-16  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 18:16:25,340  -thread-16  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"*********","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"*********","session":"*********","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","text":"Test CH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"*********","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","textEmailContact":"<EMAIL>","message_id":"0","sessionId":"*********","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"4884d30d-f140-4e72-a1a5-ec93f5e9d828","applicationId":"SAS360Proxy","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"4884d30d-f140-4e72-a1a5-ec93f5e9d828"}
2024-06-24 18:16:25,340  -thread-16  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 18:16:25,340  -thread-16  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:16:25,340  -thread-16  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:25,341  -thread-16  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 18:16:25.34, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = *********, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 18:16:25.187, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 4884d30d-f140-4e72-a1a5-ec93f5e9d828, EMAIL_IMPRINT_URL = , SESSION_ID = *********, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:16:25.34, 
2024-06-24 18:16:25,341  -thread-16  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:25,349  -thread-16  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:25,576  -thread-17  INFO   c.a.c.a.Event    Event received, event: c_X_phsjkjjd, will be processed...
2024-06-24 18:16:25,576  -thread-17  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"externalCode":"TSK_97","channel_user_id":"*********","channelType":"external","outboundProperties.primaryVisualization":"BUTTON","Image":"fsgfsdg","creative_id":"4f2a7d44-1fa0-41aa-b693-e3ac21b3670e","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","internalTenantId":"********","sentEmail":"Y","response_tracking_code":"37bd20de-68e6-43cd-ba5b-8c330d246c10","primaryClickMeaning":"DISMISS_CLICK","activity_id":"23171abe-f2d0-4a63-a054-96bc8f250fe5","creative_version_id":"jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y","eventName":"c_X_phsjkjjd","text":"Test CH","activity_trigger_event_id":"a7121ab1-bdda-4092-83aa-f61c05d438d8","event_channel":"external","task_version_id":"xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C","textEmailContact":"<EMAIL>","primaryVisualization":"BUTTON","activity_task_type":"EVENT","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"43cb9213-2a02-47f1-9014-10a6db53799b","applicationId":"SAS360Proxy","communicationKind":"MARKETING","eventDesignedName":"c_X_phsjkjjd","subject_id":"*********","outboundProperties.timeSlot":"9-18","generatedTimestamp":"*************","timeSlot":"9-18","session":"*********","screen_info":"x@","task_id":"2b772d1a-68ba-4444-889d-2800218ec724","outboundProperties.Image":"fsgfsdg","activity_ia_tag_value":"a7121ab1-bdda-4092-83aa-f61c05d438d8","channel_user_type":"subject_id","parent_event":"external","eventname":"c_X_phsjkjjd","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"bc324933-5f23-45a0-80c1-34ce9e9aadd8","variant_id":"0","outboundProperties.communicationKind":"MARKETING","outboundProperties.primaryClickMeaning":"DISMISS_CLICK","creative_content":"fsdafsdfgsfdgsfd","parent_eventname":"Ext_Sit_Event","event":"c_X_phsjkjjd","channelId":"*********","timestamp":"*************","internal_tenant_id":"********","message_id":"0","sessionId":"*********","event_category":"unifiedAndEngage","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_X_phsjkjjd","account":"f0cb22506600016b1805ee8b"},"rowKey":"43cb9213-2a02-47f1-9014-10a6db53799b"}
2024-06-24 18:16:25,576  -thread-17  DEBUG  c.a.c.c.TaskCache    TaskVersionId=xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C loaded from application cache, duration[ms]: 0
2024-06-24 18:16:25,576  -thread-17  DEBUG  c.a.c.c.CreativeCache    CreativeVersionId=jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y loaded from application cache, duration[ms]: 0
2024-06-24 18:16:25,576  -thread-17  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_19 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:25,576  -thread-17  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 18:16:25.576, CREATIVE_VERSION_ID = jgu9tP2Z2Dt.Cx_zFynwJ4fPlpfhUo4Y, CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_37bd20de-68e6-43cd-ba5b-8c330d246c10_*************, CREATIVE_ID = 4f2a7d44-1fa0-41aa-b693-e3ac21b3670e, CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Onboarding, CONTACT_POLICY_PRODUCT_CODE = PK, SUBJECT_ID = *********, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = xyXsR3hjHp6NrAkCFPc9A35a4lKN2s6C, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_1, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 18:16:25.325, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 37bd20de-68e6-43cd-ba5b-8c330d246c10, CAMPAIGN_MESSAGE_CD = MSG_19, AUD_OCCURENCE_ID = , CONTACT_ID = 43cb9213-2a02-47f1-9014-10a6db53799b, EMAIL_IMPRINT_URL = , SESSION_ID = *********, TASK_ID = 2b772d1a-68ba-4444-889d-2800218ec724, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:16:25.576, 
2024-06-24 18:16:25,576  -thread-17  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:25,581  -thread-17  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:30,705  -thread-18  INFO   c.a.c.a.Event    Event received, event: c_DCH, will be processed...
2024-06-24 18:16:30,705  -thread-18  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"463860","externalCode":"TSK_95","generatedTimestamp":"*************","channel_user_id":"463860","session":"463860","screen_info":"x@","task_id":"********-4dff-43c7-92a6-5a74243beb01","channelType":"external","channel_user_type":"subject_id","parent_event":"external","eventname":"c_DCH","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"448e1dd2-6e51-3023-a56e-ba36e24be8af","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"88c4ac86-77f4-4ff8-a1fa-15a06ec5f5d3","variant_id":"0","internalTenantId":"********","response_tracking_code":"06b9ab19-a082-4bc9-9858-47595f992ca8","eventName":"c_DCH","parent_eventname":"Ext_Sit_Event","event":"c_DCH","channelId":"463860","timestamp":"*************","event_channel":"external","internal_tenant_id":"********","task_version_id":"0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG","Text":"Test CH","message_id":"0","sessionId":"463860","event_category":"unifiedAndEngage","imprint_id":"0","event_datetime_utc":"*************","datahub_id":"448e1dd2-6e51-3023-a56e-ba36e24be8af","guid":"38fb058b-740b-4246-b66b-f29d4a6fef98","applicationId":"SAS360Proxy","customer_id":"33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c","event_designed_name":"c_DCH","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_DCH"},"rowKey":"38fb058b-740b-4246-b66b-f29d4a6fef98"}
2024-06-24 18:16:30,706  -thread-18  DEBUG  c.a.c.c.TaskCache    TaskVersionId=0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG loaded from application cache, duration[ms]: 0
2024-06-24 18:16:30,706  -thread-18  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:16:30,706  -thread-18  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:30,706  -thread-18  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, UPDATED_DTTM = 2024-06-24 18:16:30.706, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af, SPOT_ID = , LEAD_ID = 448e1dd2-6e51-3023-a56e-ba36e24be8af_06b9ab19-a082-4bc9-9858-47595f992ca8_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = PU_KONS, SUBJECT_ID = 463860, MESSAGE_ID = 0, IMPRINT_ID = 0, CI360_CONTACT_CHANNEL_NM = external, TASK_VERSION_ID = 0Q9RMQVlsQB_CbeW7y76rbYOrXG0qeBG, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = MARKETING, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 33a1c5ca4227efd37dfd2b0bf6f645ffd189c07d1912be75ebad71e9988add2f453ae735584c8c9fbb3ad32b0850a22c, CONTACT_DTTM = 2024-06-24 18:16:30.589, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 06b9ab19-a082-4bc9-9858-47595f992ca8, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 38fb058b-740b-4246-b66b-f29d4a6fef98, EMAIL_IMPRINT_URL = , SESSION_ID = 463860, TASK_ID = ********-4dff-43c7-92a6-5a74243beb01, CONTACT_CHANNEL_CD = PSH, INSERTED_DTTM = 2024-06-24 18:16:30.706, 
2024-06-24 18:16:30,706  -thread-18  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:30,713  -thread-18  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:31,144  -thread-19  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 18:16:31,145  -thread-19  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"*********","testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"eadf9947-5e69-4e7a-9228-3326a922bdcb","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"af3610c7-ead4-4b06-8e88-d8985c9df954","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","recipientDomain":"sas.com","goal_guid":"a0672149-b594-4203-a50a-57d26706852b","event_category":"unifiedAndEngage","imprint_id":"74589f81-caa6-49f6-8797-e122ed2882bb","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/7/cixniceu/74589f81-caa6-49f6-8797-e122ed2882bb.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"98aa80dd-d6ae-428f-9500-0b0ca64eca29","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"98aa80dd-d6ae-428f-9500-0b0ca64eca29"}
2024-06-24 18:16:31,145  -thread-19  DEBUG  c.a.c.c.TaskCache    TaskVersionId=ILCptqICvNX61e0N64fqrQ1.9qkEMj9u loaded from application cache, duration[ms]: 0
2024-06-24 18:16:31,145  -thread-19  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:16:31,145  -thread-19  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_22 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:31,145  -thread-19  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 18:16:31.145, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_af3610c7-ead4-4b06-8e88-d8985c9df954_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = *********, MESSAGE_ID = , IMPRINT_ID = 74589f81-caa6-49f6-8797-e122ed2882bb, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = ILCptqICvNX61e0N64fqrQ1.9qkEMj9u, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 18:16:30.964, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = af3610c7-ead4-4b06-8e88-d8985c9df954, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = 98aa80dd-d6ae-428f-9500-0b0ca64eca29, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/7/cixniceu/74589f81-caa6-49f6-8797-e122ed2882bb.html, SESSION_ID = , TASK_ID = eadf9947-5e69-4e7a-9228-3326a922bdcb, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 18:16:31.145, 
2024-06-24 18:16:31,145  -thread-19  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:31,162  -thread-19  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:31,163  -thread-19  DEBUG  c.a.c.a.Event    message to kafka: {"creator":"SAS360","Products":["ACCOUNTS_PAYMENTS"],"cuid":*********,"personFirtsName":null,"businessSummaryCauseCode":"CH/05/SEGD","created":"2024-06-24T18:16:31.162696+02:00","timeSent":"2024-06-24T18:16:31.162696+02:00","externalId":"de352c11-06a4-310d-ba7b-71107b6d63a1_af3610c7-ead4-4b06-8e88-d8985c9df954_*************","emailSubject":"TEST SIT EMAIL","emailURL":"https://d3on7v574i947w.cloudfront.net/7/cixniceu/74589f81-caa6-49f6-8797-e122ed2882bb.html","communicationCode":"ILCptqICvNX61e0N64fqrQ1.9qkEMj9u","communicationName":"TODO","relatedCuid":null,"personEmail":null,"personLastName":null,"communicationKind":"INFO","campaignName":"CampBrief_CHStreamTEst","campaignCode":"MSG_22"}
2024-06-24 18:16:31,182  -thread-20  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 18:16:31,182  -thread-20  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"286577","testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"b3062d5f-81c2-4d13-a177-75c0fee9f491","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"6c31fbda-ff8c-4ff7-8973-61d521993c1a","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7","recipientDomain":"sas.com","goal_guid":"3fb9ea52-4e6d-48c3-af06-cb5e6c3f239a","event_category":"unifiedAndEngage","imprint_id":"4f115d0e-0cbc-4cfc-81f6-406865d2deff","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/4/cixniceu/4f115d0e-0cbc-4cfc-81f6-406865d2deff.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964","guid":"c9dacecb-8ec3-457e-a246-407ec26d9ec2","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"c9dacecb-8ec3-457e-a246-407ec26d9ec2"}
2024-06-24 18:16:31,182  -thread-20  DEBUG  c.a.c.c.TaskCache    TaskVersionId=TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:31,182  -thread-20  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:16:31,182  -thread-20  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_22 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:31,182  -thread-20  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, UPDATED_DTTM = 2024-06-24 18:16:31.182, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964, SPOT_ID = , LEAD_ID = bbcf6ba5-aaf8-3d36-9841-913c70e1c964_6c31fbda-ff8c-4ff7-8973-61d521993c1a_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = Information, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = 286577, MESSAGE_ID = , IMPRINT_ID = 4f115d0e-0cbc-4cfc-81f6-406865d2deff, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7, CAMP_SUBTYPE_CODE = Technical, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = , CONTACT_DTTM = 2024-06-24 18:16:30.964, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = 6c31fbda-ff8c-4ff7-8973-61d521993c1a, CAMPAIGN_MESSAGE_CD = MSG_22, AUD_OCCURENCE_ID = , CONTACT_ID = c9dacecb-8ec3-457e-a246-407ec26d9ec2, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/4/cixniceu/4f115d0e-0cbc-4cfc-81f6-406865d2deff.html, SESSION_ID = , TASK_ID = b3062d5f-81c2-4d13-a177-75c0fee9f491, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 18:16:31.182, 
2024-06-24 18:16:31,182  -thread-20  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:31,199  -thread-20  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:31,200  -thread-20  DEBUG  c.a.c.a.Event    message to kafka: {"creator":"SAS360","Products":["ACCOUNTS_PAYMENTS"],"cuid":286577,"personFirtsName":null,"businessSummaryCauseCode":"CH/05/SEGD","created":"2024-06-24T18:16:31.199825+02:00","timeSent":"2024-06-24T18:16:31.199825+02:00","externalId":"bbcf6ba5-aaf8-3d36-9841-913c70e1c964_6c31fbda-ff8c-4ff7-8973-61d521993c1a_*************","emailSubject":"TEST SIT EMAIL","emailURL":"https://d3on7v574i947w.cloudfront.net/4/cixniceu/4f115d0e-0cbc-4cfc-81f6-406865d2deff.html","communicationCode":"TRq3D9nHDKPjKJfRxe9HirS6_wEDsCf7","communicationName":"TODO","relatedCuid":null,"personEmail":null,"personLastName":null,"communicationKind":"INFO","campaignName":"CampBrief_CHStreamTEst","campaignCode":"MSG_22"}
2024-06-24 18:16:31,517  -thread-21  INFO   c.a.c.a.Event    Event received, event: c_send, will be processed...
2024-06-24 18:16:31,517  -thread-21  DEBUG  c.a.c.a.Event    Received payload: 
{"tenantId":********,"attributes":{"subject_id":"*********","testFlag":"0","generatedTimestamp":"*************","screen_info":"x@","task_id":"66c84cac-7b00-41a7-a7c8-74fd9693d965","channelType":"email","emailSubject":"TEST SIT EMAIL","eventname":"c_send","event_uid":"26752e5e-c3cc-45d6-b39d-a4347c567788","vid":"de352c11-06a4-310d-ba7b-71107b6d63a1","extendedCustomEventWithRevenueFlag":"false","parent_event_uid":"a47c0bb9-9e3b-4dc7-8cdc-0e35efaf6766","variant_id":"0","internalTenantId":"********","response_tracking_code":"a6251cc1-344f-4e23-88d9-6fba8b401d9b","variant":"0","eventName":"c_send","event":"c_send","timestamp":"*************","event_channel":"email","internal_tenant_id":"********","task_version_id":"XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P","recipientDomain":"airbank.cz","goal_guid":"942e528c-9698-48f1-ac21-d51853f6ea71","event_category":"unifiedAndEngage","imprint_id":"3e63e9d5-ccdc-45ed-943e-04b663c801e6","emailImprintURL":"https://d3on7v574i947w.cloudfront.net/3/cixniceu/3e63e9d5-ccdc-45ed-943e-04b663c801e6.html","emailSendAgentId":"0faa5245-b66c-41b3-8eeb-defa88836703","datahub_id":"de352c11-06a4-310d-ba7b-71107b6d63a1","guid":"0969e27d-a6c2-47bf-8026-980e930644c9","customer_id":"424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300","event_designed_name":"c_send","account":"f0cb22506600016b1805ee8b","eventDesignedName":"c_send"},"rowKey":"0969e27d-a6c2-47bf-8026-980e930644c9"}
2024-06-24 18:16:31,517  -thread-21  DEBUG  c.a.c.c.TaskCache    TaskVersionId=XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P loaded from application cache, duration[ms]: 0
2024-06-24 18:16:31,517  -thread-21  DEBUG  c.a.c.c.CreativeCache    creativeId not available, skip creative cache...
2024-06-24 18:16:31,517  -thread-21  DEBUG  c.a.c.c.CampaignCache    CamapignCD=MSG_14 loaded from application cache, duration[ms]: 0
2024-06-24 18:16:31,517  -thread-21  DEBUG  c.a.c.a.Event    Parameters validation: VISITOR_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, UPDATED_DTTM = 2024-06-24 18:16:31.517, CREATIVE_VERSION_ID = , CONTROL_GROUP_FLG = , IDENTITY_ID = de352c11-06a4-310d-ba7b-71107b6d63a1, SPOT_ID = , LEAD_ID = de352c11-06a4-310d-ba7b-71107b6d63a1_a6251cc1-344f-4e23-88d9-6fba8b401d9b_*************, CREATIVE_ID = , CONTACT_STATUS_CD = _11, CAMP_TYPE_CODE = X-Sell, CONTACT_POLICY_PRODUCT_CODE = MOBILITA, SUBJECT_ID = *********, MESSAGE_ID = , IMPRINT_ID = 3e63e9d5-ccdc-45ed-943e-04b663c801e6, CI360_CONTACT_CHANNEL_NM = email, TASK_VERSION_ID = XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P, CAMP_SUBTYPE_CODE = Cash Loan, CONTACT_POLICY_TYPE_CODE = cp_4, COMM_TYPE_CODE = INFO, LOGIN_ID = , OCCURRENCE_ID = , AUDIENCE_ID = , BUSINESS_CONTEXT_CD = , CUSTOMER_ID = 424c1511dc90449927bf71048b109dafef9cb4fd5aa4421eaa87f92ebe3f2d04fff550059432f807c0b7fa89f3eb4300, CONTACT_DTTM = 2024-06-24 18:16:31.347, CONTACT_DT = 2024-06-24 00:00:00.0, RTC_ID = a6251cc1-344f-4e23-88d9-6fba8b401d9b, CAMPAIGN_MESSAGE_CD = MSG_14, AUD_OCCURENCE_ID = , CONTACT_ID = 0969e27d-a6c2-47bf-8026-980e930644c9, EMAIL_IMPRINT_URL = https://d3on7v574i947w.cloudfront.net/3/cixniceu/3e63e9d5-ccdc-45ed-943e-04b663c801e6.html, SESSION_ID = , TASK_ID = 66c84cac-7b00-41a7-a7c8-74fd9693d965, CONTACT_CHANNEL_CD = Email, INSERTED_DTTM = 2024-06-24 18:16:31.517, 
2024-06-24 18:16:31,517  -thread-21  DEBUG  c.a.c.a.Event    Validation: OK
2024-06-24 18:16:31,525  -thread-21  DEBUG  c.a.c.a.Event    Event inserted into database
2024-06-24 18:16:31,525  -thread-21  DEBUG  c.a.c.a.Event    message to kafka: {"creator":"SAS360","Products":["LOANS"],"cuid":*********,"personFirtsName":null,"businessSummaryCauseCode":"CH/05/SEGD","created":"2024-06-24T18:16:31.525472+02:00","timeSent":"2024-06-24T18:16:31.525472+02:00","externalId":"de352c11-06a4-310d-ba7b-71107b6d63a1_a6251cc1-344f-4e23-88d9-6fba8b401d9b_*************","emailSubject":"TEST SIT EMAIL","emailURL":"https://d3on7v574i947w.cloudfront.net/3/cixniceu/3e63e9d5-ccdc-45ed-943e-04b663c801e6.html","communicationCode":"XKwW7s6.1ZlNAHxENnC8bwGsYLQiyi9P","communicationName":"TODO","relatedCuid":null,"personEmail":null,"personLastName":null,"communicationKind":"INFO","campaignName":"Campaign Brief test","campaignCode":"MSG_14"}
2024-06-24 18:17:24,678  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:18:24,686  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:19:24,694  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:20:24,703  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:21:24,710  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:22:24,718  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:23:24,727  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:24:24,735  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:25:24,743  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:26:24,751  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:27:24,759  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:28:24,768  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:29:24,776  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:30:24,784  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:31:24,792  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:32:24,800  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:33:24,809  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:34:24,817  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:35:24,827  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:36:24,836  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:37:24,844  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:38:24,852  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:39:24,860  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:40:24,869  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:41:24,877  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:42:24,885  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:43:24,893  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:44:24,901  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:45:24,908  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:46:24,916  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:47:24,924  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:48:24,932  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:49:24,940  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:50:24,948  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:51:24,956  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:52:24,963  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:53:24,975  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:54:24,983  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:55:24,991  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:56:25,000  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:57:25,008  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:58:25,016  main        INFO   c.a.c.a.Agent    running
2024-06-24 18:59:25,025  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:00:25,033  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:01:25,041  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:02:25,049  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:03:25,057  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:04:25,066  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:05:25,074  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:06:25,082  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:07:25,090  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:08:25,098  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:09:25,106  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:10:25,114  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:11:25,122  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:12:25,130  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:13:25,138  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:14:25,146  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:15:25,154  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:16:25,163  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:17:25,170  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:18:25,178  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:19:25,186  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:20:25,193  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:21:25,201  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:22:25,208  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:23:25,215  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:24:25,222  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:25:25,228  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:26:25,235  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:27:25,242  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:28:25,249  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:29:25,256  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:30:25,262  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:31:25,269  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:32:25,276  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:33:25,283  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:34:25,290  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:35:25,298  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:36:25,305  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:37:25,312  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:38:25,318  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:39:25,325  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:40:25,332  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:41:25,338  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:42:25,344  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:43:25,351  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:44:25,357  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:45:25,364  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:46:25,371  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:47:25,377  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:48:25,384  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:49:25,391  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:50:25,397  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:51:25,404  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:52:25,411  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:53:25,417  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:54:25,424  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:55:25,431  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:56:25,437  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:57:25,443  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:58:25,451  main        INFO   c.a.c.a.Agent    running
2024-06-24 19:59:25,458  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:00:25,465  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:01:25,470  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:02:25,476  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:03:25,482  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:04:25,488  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:05:25,494  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:06:25,501  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:07:25,507  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:08:25,514  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:09:25,520  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:10:25,527  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:11:25,533  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:12:25,538  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:13:25,544  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:14:25,550  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:15:25,555  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:16:25,562  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:17:25,569  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:18:25,576  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:19:25,583  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:20:25,589  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:21:25,596  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:22:25,603  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:23:25,611  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:24:25,618  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:25:25,625  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:26:25,632  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:27:25,638  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:28:25,644  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:29:25,651  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:30:25,658  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:31:25,665  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:32:25,671  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:33:25,679  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:34:25,686  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:35:25,693  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:36:25,700  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:37:25,708  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:38:25,715  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:39:25,721  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:40:25,728  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:41:25,733  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:42:25,740  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:43:25,746  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:44:25,752  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:45:25,757  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:46:25,763  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:47:25,769  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:48:25,774  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:49:25,781  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:50:25,787  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:51:25,792  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:52:25,799  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:53:25,805  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:54:25,812  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:55:25,818  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:56:25,825  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:57:25,832  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:58:25,838  main        INFO   c.a.c.a.Agent    running
2024-06-24 20:59:25,845  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:00:25,851  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:01:25,858  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:01:31,242  5623216-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-24 21:01:31,243  5623216-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: null
2024-06-24 21:01:31,243  5623216-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-24 21:01:46,244  5623216-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 21:01:46,244  5623216-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-24 21:01:46,249  5623216-24  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-24 21:01:46,250  5623216-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1006: Session Closed
2024-06-24 21:01:46,250  5623216-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-24 21:01:46,367  5623216-20  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-24 21:01:46,419  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-24 21:01:46,420  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.125
2024-06-24 21:01:46,420  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-24 21:01:46,420  5623216-23  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-24 21:02:01,250  5623216-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 21:02:01,251  5623216-24  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:189)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.onEof(WebSocketCoreSession.java:254)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.fillAndParse(WebSocketConnection.java:491)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onFillable(WebSocketConnection.java:349)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:314)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.ssl.SslConnection$DecryptedEndPoint.onFillable(SslConnection.java:558)
	at org.eclipse.jetty.io.ssl.SslConnection.onFillable(SslConnection.java:379)
	at org.eclipse.jetty.io.ssl.SslConnection$2.succeeded(SslConnection.java:146)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:421)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:390)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:277)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:199)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:411)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:969)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1194)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1149)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 21:02:25,864  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:03:25,871  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:04:25,878  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:05:25,885  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:06:25,892  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:07:25,898  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:08:25,905  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:09:25,911  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:10:25,918  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:11:25,924  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:12:25,931  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:13:25,937  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:14:25,946  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:15:25,953  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:16:25,959  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:17:25,966  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:18:25,973  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:19:25,979  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:20:25,986  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:21:25,993  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:22:26,000  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:23:26,007  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:24:26,013  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:25:26,020  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:26:26,027  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:27:26,034  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:28:26,041  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:29:26,048  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:30:26,054  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:31:26,061  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:32:26,068  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:33:26,074  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:34:26,081  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:35:26,088  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:36:26,094  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:37:26,101  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:38:26,108  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:39:26,114  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:40:26,121  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:41:26,128  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:42:26,134  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:43:26,140  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:44:26,147  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:45:26,153  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:46:26,160  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:47:26,167  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:48:26,174  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:49:26,180  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:50:26,187  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:51:26,194  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:52:26,200  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:53:26,206  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:54:26,213  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:55:26,219  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:56:26,225  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:57:26,231  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:58:26,238  main        INFO   c.a.c.a.Agent    running
2024-06-24 21:59:26,243  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:00:26,249  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:01:26,255  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:02:26,261  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:03:26,267  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:04:26,273  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:05:26,279  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:06:26,284  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:07:26,291  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:08:26,297  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:09:26,303  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:10:26,309  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:11:26,316  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:12:26,322  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:13:26,328  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:14:26,334  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:15:26,339  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:16:26,345  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:17:26,351  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:18:26,357  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:19:26,362  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:20:26,369  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:21:26,375  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:22:26,381  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:23:26,388  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:24:26,395  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:25:26,401  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:26:26,409  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:27:26,416  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:28:26,423  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:29:26,430  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:30:26,436  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:31:26,445  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:32:26,451  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:33:26,458  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:34:26,464  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:35:26,471  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:36:26,477  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:37:26,483  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:38:26,490  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:39:26,496  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:40:26,503  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:41:26,510  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:42:26,516  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:43:26,523  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:44:26,531  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:45:26,537  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:46:26,544  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:47:26,551  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:48:26,558  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:49:26,566  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:50:26,573  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:51:26,580  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:52:26,587  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:53:26,594  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:54:26,602  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:55:26,608  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:56:26,615  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:57:26,622  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:58:26,629  main        INFO   c.a.c.a.Agent    running
2024-06-24 22:59:26,635  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:00:26,641  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:01:26,648  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:02:26,654  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:03:26,660  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:04:26,666  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:05:26,673  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:05:58,106  5623216-24  ERROR  c.s.m.a.s.StreamWebSocket    Event stream connection to gateway failed: null
2024-06-24 23:05:58,106  5623216-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: null
2024-06-24 23:05:58,106  5623216-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-24 23:06:13,107  5623216-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 23:06:13,107  5623216-24  INFO   c.s.m.a.s.CI360Agent    Event stream connection maxTextMessageSize 1048576
2024-06-24 23:06:13,108  5623216-24  WARN   c.s.m.a.s.StreamWebSocket    Event stream connection closed: [1006] Session Closed
2024-06-24 23:06:13,108  5623216-24  ERROR  c.a.c.a.Agent    Stream to cloud closed, GATEWAY_CONNECTION_FAILED: 1006: Session Closed
2024-06-24 23:06:13,108  5623216-24  DEBUG  c.a.c.a.Agent    Passed compareAndSet test
2024-06-24 23:06:13,225  5623216-21  INFO   c.s.m.a.s.StreamWebSocket    Event stream connected
2024-06-24 23:06:13,788  5623216-18  INFO   c.s.m.a.s.StreamWebSocket    Agent version: v2402
2024-06-24 23:06:13,789  5623216-18  INFO   c.s.m.a.s.StreamWebSocket    Gateway version: 1.0.126
2024-06-24 23:06:13,789  5623216-18  INFO   c.s.m.a.s.StreamWebSocket    Gateway supported versions: v2406,v2405,v2404,v2403,v2402,v2401,v2312
2024-06-24 23:06:13,789  5623216-18  INFO   c.s.m.a.s.StreamWebSocket    Gateway warning versions: v2311,v2310,v2309
2024-06-24 23:06:26,680  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:06:28,108  5623216-24  INFO   c.s.m.a.s.CI360Agent    Creating event stream connection
2024-06-24 23:06:28,108  5623216-24  ERROR  c.a.c.a.Agent    ErrorCode:CH_00 - Not possible to establish connection with tenant, ALREADY_CONNECTED: null
com.sas.mkt.agent.sdk.CI360AgentException: null
	at com.sas.mkt.agent.sdk.CI360Agent.startStream(CI360Agent.java:270)
	at cz.ab.ci360.agent_ch_stream.Agent$2.streamClosed(Agent.java:189)
	at com.sas.mkt.agent.sdk.StreamWebSocket.onWebSocketClose(StreamWebSocket.java:174)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.notifyOnClose(JettyWebSocketFrameHandler.java:322)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onClosed(JettyWebSocketFrameHandler.java:306)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$0(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$1(WebSocketCoreSession.java:272)
	at org.eclipse.jetty.util.Callback$4.completed(Callback.java:184)
	at org.eclipse.jetty.util.Callback$Completing.succeeded(Callback.java:344)
	at org.eclipse.jetty.websocket.common.JettyWebSocketFrameHandler.onError(JettyWebSocketFrameHandler.java:269)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.lambda$closeConnection$2(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.handle(WebSocketCoreSession.java:118)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.closeConnection(WebSocketCoreSession.java:284)
	at org.eclipse.jetty.websocket.core.internal.WebSocketCoreSession.onEof(WebSocketCoreSession.java:254)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.fillAndParse(WebSocketConnection.java:491)
	at org.eclipse.jetty.websocket.core.internal.WebSocketConnection.onFillable(WebSocketConnection.java:349)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:314)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.ssl.SslConnection$DecryptedEndPoint.onFillable(SslConnection.java:558)
	at org.eclipse.jetty.io.ssl.SslConnection.onFillable(SslConnection.java:379)
	at org.eclipse.jetty.io.ssl.SslConnection$2.succeeded(SslConnection.java:146)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:421)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:390)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:277)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:199)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:411)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:969)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1194)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1149)
	at java.base/java.lang.Thread.run(Thread.java:829)
2024-06-24 23:07:26,687  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:08:26,694  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:09:26,701  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:10:26,708  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:11:26,715  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:12:26,722  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:13:26,729  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:14:26,736  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:15:26,742  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:16:26,748  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:17:26,754  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:18:26,760  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:19:26,766  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:20:26,772  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:21:26,778  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:22:26,785  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:23:26,791  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:24:26,798  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:25:26,805  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:26:26,811  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:27:26,819  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:28:26,825  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:29:26,831  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:30:26,837  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:31:26,844  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:32:26,851  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:33:26,857  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:34:26,864  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:35:26,870  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:36:26,877  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:37:26,884  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:38:26,891  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:39:26,898  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:40:26,905  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:41:26,911  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:42:26,918  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:43:26,925  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:44:26,931  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:45:26,937  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:46:26,944  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:47:26,950  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:48:26,957  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:49:26,963  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:50:26,969  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:51:26,976  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:52:26,983  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:53:26,990  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:54:26,996  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:55:27,002  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:56:27,009  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:57:27,015  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:58:27,022  main        INFO   c.a.c.a.Agent    running
2024-06-24 23:59:27,029  main        INFO   c.a.c.a.Agent    running
