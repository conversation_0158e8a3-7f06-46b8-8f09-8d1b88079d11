package cz.ab.ci360.cache;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class TaskCacheEntry {

    public String taskVersionId;
    public String taskId;

    public CacheVariable tsk_comm_chan_code = new CacheVariable("tsk_comm_chan_code", "XNA");
    public CacheVariable tsk_comm_camp_name = new CacheVariable("tsk_comm_camp_name","-1");
    public CacheVariable tsk_camp_type = new CacheVariable("tsk_camp_type","XNA");
    public CacheVariable tsk_camp_subtype = new CacheVariable("tsk_camp_subtype","XNA");
    public CacheVariable tsk_camp_product = new CacheVariable("tsk_camp_product","XNA");
    public CacheVariable tsk_camp_buss_cause_cd = new CacheVariable("tsk_camp_buss_cause_cd","XNA");
    public CacheVariable tsk_camp_comm_type = new CacheVariable("tsk_camp_comm_type","XNA");
    public CacheVariable tsk_cp_type = new CacheVariable("tsk_cp_type","XNA");
    public CacheVariable tsk_cp_product = new CacheVariable("tsk_cp_product","XNA");

    public List<String> getCacheVariableList() {
        Field[] fields = TaskCacheEntry.class.getFields();
        List<String> variables = new ArrayList<>();
        for(Field f : fields) {
            if( f.getType().isAssignableFrom(CacheVariable.class) ) {
                variables.add(f.getName());
            }
        }
        return variables;
    }

}