package cz.ab.ci360.common;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.config.SslConfigs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.Future;


public class Kafka {

    private static final Logger logger = (Logger) LoggerFactory.getLogger(Kafka.class);
    private static Producer<String, String> producer;
    private static String topic;

    public static void init(Config config)
    {

        //load and log config parameters
        String SERVER = config.loadKeyValue("kafka.server");
        String SSL_TRUSTSTORE_LOCATION_CONFIG = config.loadKeyValue("kafka.SSL_TRUSTSTORE_LOCATION_CONFIG");
        String SSL_TRUSTSTORE_PASSWORD_CONFIG = config.loadKeyValueBase64("kafka.SSL_TRUSTSTORE_PASSWORD_CONFIG");
        String SSL_KEYSTORE_LOCATION_CONFIG = config.loadKeyValue("kafka.SSL_KEYSTORE_LOCATION_CONFIG");
        String SSL_KEYSTORE_PASSWORD_CONFIG = config.loadKeyValueBase64("kafka.SSL_KEYSTORE_PASSWORD_CONFIG");
        String SSL_KEY_PASSWORD_CONFIG = config.loadKeyValueBase64("kafka.SSL_KEY_PASSWORD_CONFIG");
        String SSL_PROTOCOL_CONFIG = config.loadKeyValue("kafka.SSL_PROTOCOL_CONFIG");
        String SSL_ENABLED_PROTOCOLS_CONFIG = config.loadKeyValue("kafka.SSL_ENABLED_PROTOCOLS_CONFIG");
        String SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG = config.loadKeyValue("kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG");

        //setup Kafka properties
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, SERVER);

        //configure the following three settings for SSL Encryption
        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SSL");
        props.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, SSL_TRUSTSTORE_LOCATION_CONFIG);
        props.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, SSL_TRUSTSTORE_PASSWORD_CONFIG);

        // configure the following three settings for SSL Authentication
        props.put(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG, SSL_KEYSTORE_LOCATION_CONFIG);
        props.put(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG, SSL_KEYSTORE_PASSWORD_CONFIG);
        props.put(SslConfigs.SSL_KEY_PASSWORD_CONFIG, SSL_KEY_PASSWORD_CONFIG);
        props.put(SslConfigs.SSL_PROTOCOL_CONFIG, SSL_PROTOCOL_CONFIG);
        props.put(SslConfigs.SSL_ENABLED_PROTOCOLS_CONFIG, SSL_ENABLED_PROTOCOLS_CONFIG);
        props.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");

        //init Kafka producer
        Kafka.producer = new KafkaProducer<String, String>(props);
    }

    public static void stop() {
        //close Kafka producer
        producer.flush();
        producer.close();
    }

    public static void setTopic(String topic) {
        Kafka.topic = topic;
    }

    public static void sendMessage(String message) {
        String key = generateKey();
        ProducerRecord<String, String> record = new ProducerRecord<String, String>(Kafka.topic, key , message );
        Kafka.producer.send(record);
    }

    public static Future<RecordMetadata> sendMessageFuture(String message) {
        //send message and return Future
        String key = generateKey();
        ProducerRecord<String, String> record = new ProducerRecord<String, String>(Kafka.topic, key , message );
        final Future<RecordMetadata> futRec= Kafka.producer.send(record);
        return futRec;
    }

    public static void sendMessageCallback(String message) {
        //send message and async process callback (log metadata)
        String key = generateKey();
        ProducerRecord<String, String> record = new ProducerRecord<String, String>(Kafka.topic, key , message );

        Kafka.producer.send(record,
                new Callback() {
                    public void onCompletion(RecordMetadata metadata, Exception e) {
                        if(e != null) {
                            logger.error(e.toString());
                        } else {
                            logger.info("callback: "+ metadata.toString());
                        }
                    }
                }
        );
    }

    private static String generateKey() {
        Random random = new Random();
        int min, max;
        min=10000;max=99999;
        int res=random.nextInt(max - min) + min;

        String key=String.format("%s%s", new SimpleDateFormat("yyyyMMddHHmmssSSS0").format(new Date()),res);

        return key;
    }

}
