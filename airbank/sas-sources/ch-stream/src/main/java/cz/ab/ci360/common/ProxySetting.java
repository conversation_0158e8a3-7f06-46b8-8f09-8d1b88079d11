package cz.ab.ci360.common;

public class ProxySetting {
    public static String server;
    public static int port;
    public static String realm;
    public static String user;
    public static String pwd;
    public static String nonProxyHosts;


    public static void init(Config config) {
        server = config.loadKeyValue("proxy.server");
        port = config.loadKeyValueAsInteger("proxy.port");

        realm = config.loadKeyValue("proxy.realm");
        user = config.loadKeyValue("proxy.user");
        pwd = config.loadKeyValueBase64("proxy.pwd");

        nonProxyHosts = config.loadKeyValue("proxy.non_proxy_hosts");
    }
}
