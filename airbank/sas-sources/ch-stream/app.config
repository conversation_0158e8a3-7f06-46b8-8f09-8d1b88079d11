#CI360 settings
ci360.gatewayHost=extapigwservice-eu-prod.ci360.sas.com
ci360.jwt=
ci360.tenantID=f0cb22506600016b1805ee8b
ci360.clientSecret=TWpVeE5ERXhNemx0TTJNelpEZ3laek5qYkdKcWFUTmhNekpxWldreE0ybHFZMk13
ci360.token=ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SmpiR2xsYm5SSlJDSTZJbVl3WTJJeU1qVXdOall3TURBeE5tSXhPREExWldVNFlpSjkuU1MwVXBORlQtdVFvbGNIVktISGl5aDRBak1MZnhiT3ZwaDFCTXRXbFlRQQ==

#Agent, agent.max_threads = maximal opened parallel threads for processing
agent.max_threads=5000
agent.kafka=false

#Event
event.process_event_with_prefix=c_
event.ignore_events=c_activitystart,c_abtestpathassignment
event.direct_event=c_direct

#Database
db.server=DBS01.DE11.NP.AB:1621
db.service_name=MNDE11DW.NP.AB
db.database=APP_CAMPAIGN_CDM2
db.user=APP_CAMPAIGN_CDM2
db.pwd=QVBQX0NBTVBBSUdOX0NETTI=
db.init_connection_pool_size=10
db.max_connection_pool_size=200

#Proxy server
proxy.server=proxy.np.ab
proxy.port=3128
proxy.realm=proxy
proxy.user=sascloud_user
proxy.pwd=cGdSTjFVZGFzNUk1NWFiZXJ0eHg=
proxy.non_proxy_hosts=app-sas-test.np.ab


#KAFKA
kafka.server=kafka.np.ab:9092
kafka.topic=sentCampaingEmail
kafka.SSL_TRUSTSTORE_LOCATION_CONFIG=./cert/kafka-test.truststore.jks
kafka.SSL_TRUSTSTORE_PASSWORD_CONFIG=aGVzbG8
kafka.SSL_KEYSTORE_LOCATION_CONFIG=./cert/kafka-test.keystore.jks
kafka.SSL_KEYSTORE_PASSWORD_CONFIG=aGVzbG8
kafka.SSL_KEY_PASSWORD_CONFIG=aGVzbG8
kafka.SSL_PROTOCOL_CONFIG=SSL
kafka.SSL_ENABLED_PROTOCOLS_CONFIG=TLSv1.2
kafka.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG=
