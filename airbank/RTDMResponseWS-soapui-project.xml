<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="584e08b9-a9ac-446b-8732-200898df9521" activeEnvironment="Default" name="RTDMResponseWS" resourceRoot="" soapui-version="5.7.2" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="03f454d1-369c-43e9-9cec-0de5f42f7a29" wsaVersion="NONE" name="RTDMResponseWSSoap11" type="wsdl" bindingName="{http://airbank.cz/sas_agent_kafka/ws}RTDMResponseWSSoap11" soapVersion="1_1" anonymous="optional" definition="file:/C:/Users/<USER>/Downloads/RTDMResponseWS.wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="file:\C:\Users\<USER>\Downloads\RTDMResponseWS.wsdl"><con:part><con:url>file:\C:\Users\<USER>\Downloads\RTDMResponseWS.wsdl</con:url><con:content><![CDATA[<wsdl:definitions targetNamespace="http://airbank.cz/sas_agent_kafka/ws" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:sch="http://airbank.cz/sas_agent_kafka/ws" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://airbank.cz/sas_agent_kafka/ws">
  <wsdl:types>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://airbank.cz/sas_agent_kafka/ws" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="publishContactHistoryRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="taskCode" type="xs:string"/>
            <xs:element minOccurs="0" name="error" type="xs:boolean"/>
            <xs:element minOccurs="0" name="result" type="xs:boolean"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="publishContactHistoryResponse">
        <xs:complexType>
          <xs:sequence>
            <!--Empty response-->
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="sendBHResponseRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="taskCode" type="xs:string"/>
            <xs:element name="executed" type="xs:string"/>
            <xs:element minOccurs="0" name="result" type="xs:boolean"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="sendBHResponseResponse">
        <xs:complexType>
          <xs:sequence>
            <!--Empty response-->
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="publishContactHistoryResponse">
    <wsdl:part element="tns:publishContactHistoryResponse" name="publishContactHistoryResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="sendBHResponseResponse">
    <wsdl:part element="tns:sendBHResponseResponse" name="sendBHResponseResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="publishContactHistoryRequest">
    <wsdl:part element="tns:publishContactHistoryRequest" name="publishContactHistoryRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="sendBHResponseRequest">
    <wsdl:part element="tns:sendBHResponseRequest" name="sendBHResponseRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="RTDMResponseWS">
    <wsdl:operation name="publishContactHistory">
      <wsdl:input message="tns:publishContactHistoryRequest" name="publishContactHistoryRequest"></wsdl:input>
      <wsdl:output message="tns:publishContactHistoryResponse" name="publishContactHistoryResponse"></wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendBHResponse">
      <wsdl:input message="tns:sendBHResponseRequest" name="sendBHResponseRequest"></wsdl:input>
      <wsdl:output message="tns:sendBHResponseResponse" name="sendBHResponseResponse"></wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="RTDMResponseWSSoap11" type="tns:RTDMResponseWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="publishContactHistory">
      <soap:operation soapAction=""/>
      <wsdl:input name="publishContactHistoryRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="publishContactHistoryResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendBHResponse">
      <soap:operation soapAction=""/>
      <wsdl:input name="sendBHResponseRequest">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="sendBHResponseResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="RTDMResponseWSService">
    <wsdl:port binding="tns:RTDMResponseWSSoap11" name="RTDMResponseWSSoap11">
      <soap:address location="http://localhost:8080/ws"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://localhost:8080/ws</con:endpoint></con:endpoints><con:operation id="62ae898a-2824-4ea0-b89b-6e2c6a8a8f92" isOneWay="false" action="" name="publishContactHistory" bindingOperationName="publishContactHistory" type="Request-Response" outputName="publishContactHistoryResponse" inputName="publishContactHistoryRequest" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="6f777287-c391-4dd9-b33d-906fbb8dc61b" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://localhost:8080/ws</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://airbank.cz/sas_agent_kafka/ws">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <ws:publishContactHistoryRequest>\r
         <ws:cuid>1</ws:cuid>\r
         <ws:taskCode>2</ws:taskCode>\r
         <!--Optional:-->\r
         <ws:error>true</ws:error>\r
         <!--Optional:-->\r
         <ws:result>false</ws:result>\r
      </ws:publishContactHistoryRequest>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:username>x</con:username><con:password>x</con:password><con:selectedAuthProfile>Basic</con:selectedAuthProfile><con:addedBasicAuthenticationTypes>Basic</con:addedBasicAuthenticationTypes><con:authType>Global HTTP Settings</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://airbank.cz/sas_agent_kafka/ws/RTDMResponseWS/publishContactHistoryRequest"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="bee61d4b-c345-45fb-9229-b2d21f1fd5af" isOneWay="false" action="" name="sendBHResponse" bindingOperationName="sendBHResponse" type="Request-Response" outputName="sendBHResponseResponse" inputName="sendBHResponseRequest" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="d438673a-c597-4a2e-9f2c-baf3195053ca" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://localhost:8080/ws</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://airbank.cz/sas_agent_kafka/ws">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <ws:sendBHResponseRequest>\r
         <ws:cuid>1</ws:cuid>\r
         <ws:taskCode>3</ws:taskCode>\r
         <ws:executed>2</ws:executed>\r
         <!--Optional:-->\r
         <ws:result>false</ws:result>\r
      </ws:sendBHResponseRequest>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:username>x</con:username><con:password>x</con:password><con:selectedAuthProfile>Basic</con:selectedAuthProfile><con:addedBasicAuthenticationTypes>Basic</con:addedBasicAuthenticationTypes><con:authType>Global HTTP Settings</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://airbank.cz/sas_agent_kafka/ws/RTDMResponseWS/sendBHResponseRequest"/><con:wsrmConfig version="1.2"/></con:call></con:operation></con:interface><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/></con:soapui-project>