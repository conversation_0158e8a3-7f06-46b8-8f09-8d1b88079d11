openapi: 3.1.0
info:
  title: Airtest Customers
  description: Airtest Customers REST API Documentation
  contact:
    url: https://wiki.airbank.cz/display/AT/Airtest+customers
  version: 1.0.0
servers:
  - url: http://airtest-customers.te00.ingress.np.ab
  - url: http://airtest-customers.te10.ingress.np.ab
components:
  schemas:
    ErrorResponse:
      title: ErrorResponse
      type: object
      description: 'Response for 4xx errors'
      required:
        - errorCode
        - errorMessage
      properties:
        errorCode:
          type: string
        errorMessage:
          type: string

    AcquireCustomerRequest:
      title: AcquireCustomerRequest
      type: object
      description: 'Input data to search and acquire some customer'
      required:
        - acquirer
      properties:
        filter:
          $ref: '#/components/schemas/AcquireCustomerFilter'
        tweak:
          description: 'Specifies how to modify acquired customer'
          $ref: '#/components/schemas/AcquireCustomerTweak'
          nullable: true
        acquirer:
          type: string
          description: 'Who wants to acquire the customer, for example ''CMS FT - testNewCard'''

    AcquireCustomerResponse:
      title: AcquireCustomerResponse
      type: object
      description: 'Acquired customer data'
      required:
        - customer
      properties:
        customer:
          $ref: '#/components/schemas/AcquiredCustomer'

    FindCustomersResponse:
      title: FindCustomersResponse
      type: object
      description: 'List of customers, that were found by input filter'
      required:
        - customers
      properties:
        customers:
          type: array
          items:
            $ref: '#/components/schemas/AcquiredCustomer'

    ReserveCustomerRequest:
      title: ReserveCustomerRequest
      type: object
      description: 'Used for reserving  chosen customer'
      required:
        - acquirer
        - reservedFor
      properties:
        acquirer:
          type: string
          description: 'Who wants to reserve the customer, for example ''CMS FT - testNewCard''. Needs to be same value when reserving and releasing customer.'
        reservedFor:
          type: integer
          description: 'Specified for how many minutes the customer should be reserved'

    ReserveCustomerResponse:
      title: ReserveCustomerResponse
      type: object
      description: 'Reserved customer data'
      required:
        - customerType
        - customer
      properties:
        customerType:
          $ref: '#/components/schemas/GeneratedCustomerType'
          description: 'Disclaimer: This is the original type when customer was created. If the customer was modified after acquire, the type may not match its current state.'
        customer:
          $ref: '#/components/schemas/AcquiredCustomer'

    ReleaseCustomerRequest:
      title: ReleaseCustomerRequest
      type: object
      description: 'Used for releasing chosen customer'
      required:
        - acquirer
      properties:
        acquirer:
          type: string
          description: 'Who wants to release the customer, for example ''CMS FT - testNewCard''. Needs to be same value when reserving and releasing customer.'

    NewCustomerRequest:
      title: NewCustomerRequest
      type: object
      description: 'To specify new customer parameters'
      properties:
        gender:
          $ref: '#/components/schemas/Gender'
        citizenship:
          description: 'Specify required citizenship, using 2-letter ISO country code. Returned birth place is set according to this value. Default: CZ.'
          type: string
          nullable: true
        targetLegalForm:
          $ref: '#/components/schemas/TargetLegalFormType'
          description: 'Set this to generate entrepreneur or company data. In that case, the customer is immediately mocked in Bisnode. Default: null (=just retail data generated).'
          deprecated: true
        minAge:
          description: 'Default is set to minimal adult age.'
          type: integer
          nullable: true
        maxAge:
          description: 'Default is set to some mean age.'
          type: integer
          nullable: true
        addressLocation:
          $ref: '#/components/schemas/AddressLocation'
        primaryDocumentType:
          description: 'Specifies desired primary document for customer'
          $ref: '#/components/schemas/DocumentType'

    NewCustomerResponse:
      title: NewCustomerResponse
      type: object
      required:
        - name
        - surname
        - gender
        - birthdate
        - permanentAddress
        - citizenship
        - phoneNumber
        - email
        - username
        - password
        - identityDocument
      properties:
        name:
          type: string
        surname:
          type: string
        gender:
          $ref: '#/components/schemas/Gender'
        birthDate:
          type: string
          format: date
        permanentAddress:
          $ref: '#/components/schemas/RuianAddress'
        citizenship:
          type: string
        phoneNumber:
          type: string
        email:
          type: string
        username:
          type: string
        password:
          type: string
        identityDocument:
          $ref: '#/components/schemas/IdentityDocument'
        birthNumber:
          type: string
        czechBirthPlace:
          $ref: '#/components/schemas/CzechBirthPlace'
        foreignBirthPlace:
          $ref: '#/components/schemas/ForeignBirthPlace'
        identificationNumber:
          type: string
          description: 'Generated ICO, when targetLegalForm was specified.'
        vatNumber:
          type: string
          description: 'Generated VAT number, when targetLegalForm was specified.'
        companyName:
          type: string
          description: 'Generated company name, when targetLegalForm was specified.'
        myAirSecrets:
          $ref: '#/components/schemas/MyAirSecrets'
          description: 'Not null when customer is created with pair MyAir option'

    MockBisnodeRequest:
      title: MockBisnodeRequest
      type: object
      required:
        - cuid
        - targetLegalForm
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of existing retail customer that will be mocked.'
        targetLegalForm:
          $ref: '#/components/schemas/TargetLegalFormType'

    MockBisnodeResponse:
      title: MockBisnodeResponse
      description: 'Attributes that were generated to mock customer in Bisnode.'
      type: object
      required:
        - identificationNumber
        - vatNumber
        - companyName
      properties:
        identificationNumber:
          type: string
          description: 'Generated ICO.'
        vatNumber:
          type: string
          description: 'Generated VAT number.'
        companyName:
          type: string
          description: 'Generated company name.'

    ApplyForStockEtfRequest:
      title: ApplyForStockEtfRequest
      type: object
      required:
        - cuid
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of existing retail adult customer.'

    GenerateCustomerRequest:
      title: GenerateCustomerRequest
      type: object
      description: 'Attributes to fine-tune generator process'
      required:
        - requester
      properties:
        requester:
          description: 'A person or system who requested this customer, used for logging. Mandatory.'
          type: string
        activateCard:
          description: 'To specify whether the card should be activated, default true'
          type: boolean
        initialBalance:
          description: 'To specify initial balance on customer accounts. When 0 is set, no initial transfer will be issued. 
              Default is between 30 000 - 35 000 CZK.'
          type: number
        minAge:
          description: 'Default is set to minimal adult age.'
          type: integer
          nullable: true
        maxAge:
          description: 'Default is set to some mean age.'
          type: integer
          nullable: true
        primaryDocumentType:
          description: 'Specifies desired primary document for customer'
          $ref: '#/components/schemas/DocumentType'
        startRobIdentification:
          description: 'Specifies if generated customer should be put into queue to start rob identification. Default is true'
          type: boolean
          nullable: true

    GenerateCustomerResponse:
      title: GenerateCustomerResponse
      allOf:
        - $ref: '#/components/schemas/NewCustomerResponse'
        - type: object
          required:
            - cuid
            - customer
          properties:
            cuid:
              type: integer
              format: int64
              description: 'CUID of the generated customer'
            customer:
              $ref: '#/components/schemas/AcquiredCustomer'
              description: 'This deprecates other attributes in this object and will replace them eventually'

    ResetQuietPeriodRequest:
      title: ResetQuietPeriodRequest
      type: object
      description: Request to reset quiet period of generator scheduler. One of 'all' or 'customerTypes' must be specified.
      properties:
        all:
          description: 'true when quiet period for all customer types should be reset'
          type: boolean
        customerTypes:
          description: 'To specify customer type for which quiet period should be reset. If ''all'' is set to true, customer types must not be declared'
          type: array
          items:
            $ref: '#/components/schemas/GeneratedCustomerType'

    GeneratorStatusResponse:
      title: GeneratorStatusResponse
      type: object
      description: Details about current state of customer generators.
      required:
        - states
      properties:
        states:
          description: 'Entry for each supported customer type'
          type: array
          items:
            $ref: '#/components/schemas/GeneratorStatus'

    RandomAddressRequest:
      title: RandomAddressRequest
      type: object
      description: 'To specify address location'
      properties:
        addressLocation:
          $ref: '#/components/schemas/AddressLocation'

    RandomAddressResponse:
      title: RandomAddressResponse
      type: object
      required:
        - address
      properties:
        address:
          $ref: '#/components/schemas/RuianAddress'

    GenerateDocumentRequest:
      title: GenerateDocumentRequest
      type: object
      description: 'To specify generated document properties'
      required:
        - documentType
      properties:
        documentType:
          description: 'To specify what type of document should be generated'
          $ref: '#/components/schemas/DocumentType'
        country:
          description: 'To specify from which country is the document from in ISO alpha 2 code format - default CZ.
            For document types that are accepted only from Czechia, this parameter is ignored and set to CZ automatically.'
          nullable: true
          type: string

    GenerateDocumentResponse:
      title: GenerateDocumentResponse
      type: object
      required:
        - identityDocument
      properties:
        identityDocument:
          $ref: '#/components/schemas/IdentityDocument'

    IncomingInstantPaymentRequest:
      title: IncomingInstantPaymentRequest
      type: object
      description: 'To specify payment variables'
      required:
        - accountNumber
        - paymentAmount
      properties:
        accountNumber:
          description: 'To specify credited account number for transfer'
          type: string
        paymentAmount:
          description: 'To specify amount to be transferred'
          type: integer
          format: int64
        isActivationPayment:
          description: 'To specify whether the payment serves to activate general contract (=specific message for receiver), default false'
          type: boolean
        senderName:
          description: 'To specify sender name'
          type: string
        senderAccountNumber:
          description: 'To specify senders account number'
          type: string
        senderBankCode:
          description: 'To specify senders bank code'
          type: string

    CreateCvakPendingTransactionStickerRequest:
      allOf:
        - $ref: '#/components/schemas/CreateTransactionRequest'
        - type: object
          title: CreateCvakPendingTransactionStickerRequest
          description: 'Transaction variables for transaction created by sticker'
          required:
            - stickerId
          properties:
            stickerId:
              description: 'To specify sticker where pending transaction will be created'
              type: string

    CreateCvakPendingTransactionPartnerRequest:
      allOf:
        - $ref: '#/components/schemas/CreateTransactionRequest'
        - type: object
          title: CreateCvakPendingTransactionPartnerRequest
          description: 'Transaction variables for transaction created by partner without stickers'
          required:
            - merchantId
          properties:
            merchantId:
              description: 'uses merchant without stickers (e.g. e-commerce)'
              type: long

    CreateTransactionRequest:
      title: CreateTransactionRequest
      type: object
      description: 'Common object for create transaction request in CVAK'
      required:
        - amount
        - refType
      properties:
        amount:
          description: 'To specify amount of the transaction'
          type: number
        requestorUrl:
          description: 'URL to redirect customer from banking app back to payment gateway when payment is finished'
          type: string
        merchantTransactionReferenceId:
          description: 'Internal transaction ID in merchant systems'
          type: string
        variableSymbol:
          description: 'Payment order variable symbol (VS) 10 digits'
          type: string
        shopName:
          description: 'Merchant (shop) name: intended to be used by payment gateway when individual merchants are not know to cvak (they are not registered in merchant (PCM) entity)'
          type: string
        creditAccountId:
          description: 'Creditor account id - an account id in the PCM'
          type: long
        refType:
          description: 'Class name of the child object'
          type: string

    CreateCvakPendingTransactionResponse:
      title: CreateCvakPendingTransactionResponse
      type: object
      required:
        - transactionId
      properties:
        transactionId:
          type: string
          description: 'Transaction id of newly created pending transaction'

    CleanCvakStickerRequest:
      title: CleanCvakStickerRequest
      type: object
      description: 'Pending transactions for specified sticker will be canceled'
      required:
        - stickerId
      properties:
        stickerId:
          description: 'sticker where pending transaction will be canceled'
          type: string
        knownTransactionId:
          description: 'known transaction (i.e. it was created by the caller) will be canceled immediately, other transactions 
          will be given some time to be finished by its creator before cancellation'
          type: string

    UniqueSipoResponse:
      title: UniqueSipoResponse
      type: object
      required:
        - sipo
      properties:
        sipo:
          type: string

    CreateVirtualCardRequest:
      title: CreateVirtualCardRequest
      type: object
      required:
        - cuid
        - accountNumber
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of existing retail customer. Must be generated by airtest-customers and already acquired.'
        accountNumber:
          type: string
          description: 'Virtual card will be created for this account number.'

    CreateVirtualCardResponse:
      title: CreateVirtualCardResponse
      type: object
      required:
        - virtualCard
      properties:
        virtualCard:
          $ref: '#/components/schemas/PaymentCard'

    PairMyAirRequest:
      title: PairMyAirResponse
      type: object
      description: 'To specify who to pair mobile app to'
      required:
        - cuid
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of existing retail customer.'

    PairMyAirResponse:
      title: PairMyAirResponse
      type: object
      required:
        - myAirSecrets
      properties:
        myAirSecrets:
          $ref: '#/components/schemas/MyAirSecrets'

    BindDisponentRequest:
      title: BindDisponentRequest
      type: object
      description: 'To specify how to create disponent.'
      required:
        - cuid
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of existing retail customer. Will become owner.'
        disponentCuid:
          type: integer
          format: int64
          description: 'CUID of existing retail customer. Will become disponent. If not provided, new customer data will be generated - generally similar to BU client.'

    BindDisponentResponse:
      title: BindDisponentResponse
      type: object
      description: 'Bound disponent data'
      required:
        - disponent
      properties:
        disponent:
          $ref: '#/components/schemas/AcquiredCustomer'

    AddChildRequest:
      title: CreateChildRequest
      type: object
      description: 'To specify how to create child disponent/cardholder.'
      required:
        - cuid
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of existing retail customer. Will become owner.'
        createDisponent:
          type: boolean
          description: 'True to create a disponent access. Default is true.'
        createCardholder:
          type: boolean
          description: 'True to create a cardholder access. Default is true.'

    AddChildResponse:
      title: AddChildResponse
      type: object
      description: 'Child data'
      required:
        - child
      properties:
        child:
          $ref: '#/components/schemas/AcquiredCustomer'

    AcquireCustomerFilter:
      title: AcquireCustomerFilter
      type: object
      description: 'Input filter to search for the customer'
      properties:
        customerType:
          description: 'Required type, default is BU.'
          $ref: '#/components/schemas/GeneratedCustomerType'
        gender:
          description: 'Required gender, default is random gender.'
          $ref: '#/components/schemas/Gender'
        foreignCharNames:
          description: 'Search customer that has first name or surname with non-czech characters, see XR-2344. Default false.'
          type: boolean
        minAge:
          description: 'Include only customers that are older or the same age as specified'
          type: integer
          format: int
        maxAge:
          description: 'Include only customers that are younger or the same age as specified'
          type: integer
          format: int
        createdFrom:
          description: 'Include only customers generated during the specified date or after. The date is environment date (shifted in TSH environments). Default is no limit.'
          type: string
          format: date
        createdTo:
          description: 'Include only customers generated during the specified date or before. The date is environment date (shifted in TSH environments). Default is no limit.'
          type: string
          format: date
        robIdentified:
          description: 'Include only customers that are identified by ROB - if false or null, no guarantee that customer is identified. Default false. For testing - currently only for type Disponent'
          type: boolean

    AcquireCustomerTweak:
      title: AcquireCustomerTweak
      type: object
      description: 'Specifies how to modify acquired customer'
      properties:
        targetLegalForm:
          $ref: '#/components/schemas/TargetLegalFormType'
          description: 'Deprecated. Use /customers/mock-bisnode instead'
          deprecated: true
        finalBalance:
          type: number
          description: 'Specifies desired balance in account currency on customer´s primary account (for child customer types it is their child account).'

    AcquiredCustomer:
      title: AcquiredCustomer
      type: object
      description: 'Data of the acquired customer'
      required:
        - cuid
        - createdTime
        - username
        - name
        - surname
        - gender
        - salutation
        - birthDate
        - phone
        - email
        - password
        - contractId
        - businessContractId
        - generalContracts
        - identificationDocuments
        - ownerAccounts
        - disponentAccounts
        - businessAccounts
      properties:
        cuid:
          type: integer
          format: int64
          description: 'CUID of the acquired customer'
        ownerCuid:
          type: integer
          format: int64
          description: 'When the returned cuid is disponent or card holder, this is the cuid of corresponding GC owner'
        ownerUsername:
          type: string
          description: 'When the returned cuid is disponent or card holder, this is the username of corresponding GC owner'
        createdTime:
          description: 'Time when customer was generated'
          type: string
          format: date-time
        username:
          description: 'IB login name'
          type: string
        name:
          description: 'First name'
          type: string
        surname:
          description: 'Surname'
          type: string
        gender:
          description: 'Gender'
          $ref: '#/components/schemas/Gender'
        salutation:
          description: 'Salutation'
          type: string
        birthNumber:
          description: 'Birth number, when it is CZ citizen'
          type: string
        birthDate:
          description: 'Birth date of customer or company director'
          type: string
          format: date
        identificationNumber:
          type: string
          description: 'Identification number of the acquired customer, when customer is entrepreneur type'
        phone:
          description: 'Primary phone number'
          type: string
        email:
          description: 'Primary email'
          type: string
        password:
          type: string
          description: 'IB password of the acquired customer'
        myAirSecrets:
          description: 'Not null when customer is acquired with pair MyAir option'
          $ref: '#/components/schemas/MyAirSecrets'
        generalContracts:
          description: 'General contracts as returned by OBS WS ObsContractWs.getGeneralContracts.'
          nullable: false
          type: array
          items:
            $ref: '#/components/schemas/GeneralContract'
        identificationDocuments:
          description: 'Identity documents provided by customer'
          nullable: false
          type: array
          items:
            $ref: '#/components/schemas/IdentificationDocument'
        ownerAccounts:
          description: 'All accounts where customer is owner'
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        disponentAccounts:
          description: 'All accounts where customer is disponent'
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        businessAccounts:
          description: 'All accounts of legal entity that is client connected to'
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'

    RelationToContractType:
      title: RelationToContractType
      type: string
      enum:
        - OWNER
        - CARD_HOLDER
        - DISPONENT
        - ENTITLED

    GeneralContractType:
      title: GeneralContractType
      type: string
      enum:
        - RETAIL
        - ENTREPRENEUR
        - LEGAL_ENTITY

    GeneralContract:
      title: GeneralContract
      type: object
      description: 'Data about customer''s general contracts'
      required:
        - id
        - type
        - relationToContractType
        - contractNumber
        - completionId
        - profileId
        - ownerCuid
      properties:
        id:
          type: integer
          format: int64
          description: 'OBS system ID of customer''s general contract.'
        type:
          $ref: '#/components/schemas/GeneralContractType'
        relationToContractType:
          $ref: '#/components/schemas/RelationToContractType'
        contractNumber:
          type: string
          description: 'OBS business ID of customer''s general contract - the one that is also visible for customer in GC PDF.'
        completionId:
          type: integer
          format: int64
          description: 'OBS system ID of completion of general contract.'
        profileId:
          type: integer
          format: int64
          description: 'OBS system ID of a profile that is used by customer when logging in different roles.'
        ownerCuid:
          type: integer
          format: int64
          description: 'CUID of owner of this GC'

    ForeignBirthPlace:
      title: ForeignBirthPlace
      type: object
      required:
        - country
        - location
      properties:
        country:
          type: string
        location:
          type: string

    CzechBirthPlace:
      title: CzechBirthPlace
      type: object
      required:
        - town
        - townCode
        - district
        - pragueDistrictCode
        - locationText
        - zipCode
      properties:
        town:
          type: string
        townCode:
          type: integer
          format: int64
        district:
          type: string
        pragueDistrictCode:
          type: integer
          format: int64
        zipCode:
          type: string
        locationText:
          type: string

    IdentityDocument:
      title: IdentityDocument
      type: object
      required:
        - documentType
        - number
        - validFrom
        - validTo
        - country
      properties:
        documentType:
          $ref: '#/components/schemas/DocumentType'
        number:
          type: string
        validFrom:
          type: string
          format: date
        validTo:
          type: string
          format: date
        country:
          type: string

    RuianAddress:
      title: RuianAddress
      type: object
      required:
        - id
        - houseNumber
        - houseNumberParts
        - streetOrLocality
        - streetOrLocalityToDisplay
        - street
        - town
        - townToDisplay
        - townCode
        - locality
        - district
        - zipcode
        - locationText
      properties:
        id:
          type: integer
          format: int64
          description: ID of RUIAN address, real state could be viewed at https://vdp.cuzk.cz/vdp/ruian/adresnimista/234567
          nullable: false
        houseNumber:
          type: string
          nullable: false
        houseNumberParts:
          $ref: '#/components/schemas/HouseNumberParts'
          nullable: false
        streetPlain:
          type: string
          description: null if address does not have street
          nullable: true
        streetOrLocality:
          type: string
          description: if address has street then street, otherwise locality
          nullable: false
        streetOrLocalityToDisplay:
          type: string
          description: if address has street then street, otherwise locality with ' (část obce)' suffix
          nullable: false
        street:
          type: string
          description: same as streetOrLocality (kept for backward compatibility)
          nullable: false
        town:
          type: string
          nullable: false
        townToDisplay:
          type: string
          description: town with district
          nullable: false
        townCode:
          type: integer
          format: int64
          nullable: false
        locality:
          type: string
          description: Část obce
          nullable: false
        area:
          type: string
          description: Městská část/obvod
          nullable: true
        district:
          type: string
          description: Okres
          nullable: false
        zipCode:
          type: string
          nullable: false
        pragueDistrictCode:
          type: integer
          format: int64
          nullable: true
        locationText:
          type: string
          description: town with district used for birthplace
          nullable: false

    HouseNumberParts:
      title: HouseNumberParts
      type: object
      properties:
        houseNumber:
          type: string
        sequenceNumber:
          type: string

    Gender:
      title: Gender
      type: string
      enum:
        - M
        - F

    AddressLocation:
      title: AddressLocation
      type: string
      description: 'Optional parameter. If not specified, there is 10% probability to get address with locality, 15% with Prague and 75% with town out of Prague'
      enum:
        - PRAGUE
        - TOWN
        - NO_STREET

    DocumentType:
      title: DocumentType
      type: string
      enum:
        - DRIVE
        - ID_CARD
        - PASSPORT
        - DOC_OF_LEGAL_BINDING
        - STAY_PERMIT
        - FOREIGN_ID_CARD

    GeneratedCustomerType:
      title: GeneratedCustomerType
      description: Type of customer generated by airtest client generators.
      type: string
      enum:
        - ALLBUSUDK
        - BU
        - BU_EUR
        - BU_JUNIOR
        - BU_YESTERDAY
        - BU_IN_DEBIT_YESTERDAY
        - BUDK_JUNIOR
        - BUHU
        - BUSU
        - BUSUDK
        - BUSUDK_FOREIGNER
        - CARDHOLDER
        - CLIENT_SMOKE
        - CLIENT_WITH_TWO_CHILD_DISPO_CARD
        - DISPONENT
        - DISPONENT_CARDHOLDER
        - DISPONENT_CARDHOLDER_CHILD
        - DISPONENT_CARDHOLDER_JUNIOR
        - DISPONENT_CHILD
        - DISPONENT_JUNIOR
        - ENTREPRENEUR_FON
        - ENTREPRENEUR_FON_YESTERDAY
        - ENTREPRENEUR_NEW_GC
        - ENTREPRENEUR_NEW_GC_YESTERDAY
        - MAX
        - MORTGAGE_DRAWN
        - MORTGAGE_DRAWN_OTB
        - MORTGAGE_NEW_UNFINISHED
        - MORTGAGE_REF_UNFINISHED
        - MYAIR_ONBOARDED
        - OVERDRAFT
        # Firma s jednim jednatelem, vytvorena pres zrychlenou zadost
        - PO_SHORT_ONE_DIRECTOR
        - ROB_NOT_IDENTIFIED
        - SU
        - SU_EUR


    TargetLegalFormType:
      title: TargetLegalFormType
      description: Wanted company type when mocking in Bisnode.
      type: string
      enum:
        - ENTREPRENEUR
        - PO_ONE_DIRECTOR

    GeneratorStatus:
      title: GeneratorStatus
      description: 'Detail about current generator state for specific customer type'
      type: object
      properties:
        customerType:
          $ref: '#/components/schemas/GeneratedCustomerType'
        targetCount:
          type: integer
        currentCount:
          type: integer
        quietPeriod:
          type: integer
          format: int64
          description: 'Current quiet period in ms'
        nextRun:
          type: string
          format: date-time

    MyAirSecrets:
      title: MyAirSecrets
      description: 'Data for the mobile device'
      type: object
      required:
        - installationId
        - password
        - passwordSalt
        - masterSecret
      properties:
        installationId:
          type: string
        password:
          type: string
        passwordSalt:
          type: string
        masterSecret:
          type: string

    IdentificationDocument:
      title: IdentificationDocument
      type: object
      required:
        - groupRelation
        - documentType
        - documentName
        - documentNumber
        - issueDate
        - validTo
        - country
      properties:
        groupRelation:
          description: 'Specifies document group https://wiki.airbank.cz/display/SA/DocumentGroupRelation'
          type: string
        documentType:
          description: 'Specifies document type - https://wiki.airbank.cz/display/SA/IdentificationDocumentType'
          type: string
        documentName:
          description: 'Czech translation of documentType'
          type: string
        documentNumber:
          description: 'Number of the identification document'
          type: string
        issueDate:
          description: 'Date when the document was issued on'
          type: string
          format: date
        validTo:
          description: 'Date when the document´s validity ends on'
          type: string
          format: date
        country:
          description: 'Specifies country, where document was issued'
          type: string

    PaymentCard:
      title: paymentCard
      type: object
      required:
        - id
        - pan
        - validity
      properties:
        id:
          description: 'Specifies card ID'
          type: integer
          format: int64
        pan:
          description: 'Specifies cards whole PAN'
          type: string
        validity:
          description: 'Specifies month when the card expires'
          type: string
          format: date

    BankAccount:
      title: BankAccount
      type: object
      required:
        - name
        - accountNumber
        - current
        - currency
        - isPrimary
        - cards
      properties:
        name:
          description: 'Specifies account name'
          type: string
        accountNumber:
          description: 'Specifies account number'
          type: string
        current:
          description: 'Specifies if account is current'
          type: boolean
        currency:
          description: 'Specifies account currency'
          type: string
        isPrimary:
          description: 'Specifies if account is set as primary'
          type: boolean
        cards:
          description: 'Not empty when customer has any card'
          type: array
          items:
            $ref: '#/components/schemas/PaymentCard'
paths:
  /customers/acquire:
    post:
      tags:
        - customers-controller
      summary: 'Find and acquire a free customer according to input filter'
      operationId: acquireCustomer
      requestBody:
        description: request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AcquireCustomerRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AcquireCustomerResponse'
        "404":
          description: No such customer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/find:
    get:
      tags:
        - customers-controller
      summary: 'Find existing customer according to input filter'
      operationId: findCustomer
      parameters:
        - in: query
          name: searchFilter
          description: '
              Filter may contain values in several formats. 
              The search is done using this order: phone/email in CIF, when not found then birth number in OBS, 
              when not found then CUID/OBS personId, when not found then by login in OBS.
          '
          schema:
            type: string
          required: true
      responses:
        "200":
          description: 'Found customer(s) or empty list when the filter did not find any'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FindCustomersResponse'

  /customers/reserve/{cuid}:
    post:
      tags:
        - customers-controller
      operationId: reserveCustomer
      parameters:
        - in: path
          name: cuid
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReserveCustomerRequest'
      responses:
        "200":
          description: 'Reserved'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReserveCustomerResponse'
        "400":
          description: 'BAD_REQUEST - Bad input parameters, CUSTOMER_ACTION_REJECTED - Customer is already reserved'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


  /customers/release/{cuid}:
    post:
      tags:
        - customers-controller
      operationId: releaseCustomer
      parameters:
        - in: path
          name: cuid
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReleaseCustomerRequest'
      responses:
        "200":
          description: 'Customer was released for future use'
        "400":
          description: 'Bad input parameters'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/new:
    post:
      tags:
        - customers-controller
      operationId: generateNewCustomerData
      requestBody:
        description: request
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewCustomerRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewCustomerResponse'
        "400":
          description: Bad input filter
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/mock-bisnode:
    post:
      tags:
        - customers-controller
      operationId: mockBisnode
      requestBody:
        description: 'Take data of existing retail customer (or generate new data for walk-in) and mock them in bisnode to simulate target customer type'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockBisnodeRequest'
        required: true
      responses:
        "200":
          description: 'Bisnode mock is set up to mock data'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockBisnodeResponse'
        "400":
          description: 'Bad input, for example not existing cuid or invalid target customer type'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/tuning/apply-for-stock-etf:
    post:
      tags:
        - customers-tuning-controller
      operationId: applyForStockEtf
      requestBody:
        description: '
          Prepare customer for Stock/ETF investment - go through the AMS application for stocks.
          The application is implemented only in MyAir, so MyAir is paired and set as auth element, if needed.
          The CUID must be in airtest DB and must be of a correct type - owner, no child, no FOP.'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyForStockEtfRequest'
        required: true
      responses:
        "200":
          description: 'AMS application approved'
        "400":
          description: 'Bad input, for example non-adult customer'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/tuning/create-virtual-card:
    post:
      tags:
        - customers-tuning-controller
      operationId: createVirtualCard
      requestBody:
        description: 'Create virtual card processed in GPE for chosen account number. Only available for environments where GPE is configured.'
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVirtualCardRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateVirtualCardResponse'
        "400":
          description: 'Bad input parameters'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/tuning/pair-myair:
    post:
      tags:
        - customers-tuning-controller
      operationId: pairMyAir
      description: 'Pairs mobile app to customer. If he has mobile app, new one won´t be paired.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PairMyAirRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PairMyAirResponse'


  /customers/tuning/bind-disponent:
    post:
      tags:
        - customers-tuning-controller
      operationId: bindDisponent
      description: 'Creates a disponent access for new customer/selected cuid if provided. Disponent access is to all accounts.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BindDisponentRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BindDisponentResponse'
        "400":
          description: Bad input parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


  /customers/tuning/add-child:
    post:
      tags:
        - customers-tuning-controller
      operationId: addChild
      description: 'Creates a disponent/cardholder access for a child. Special child account is created, to which access is granted.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddChildRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddChildResponse'
        "400":
          description: Bad input parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


  /customers/generate/{customerType}:
    post:
      tags:
        - generator-controller
      operationId: generateCustomer
      parameters:
        - in: path
          name: customerType
          description: 'Desired type of generated customer'
          required: true
          schema:
            $ref: '#/components/schemas/GeneratedCustomerType'
      requestBody:
        description: 'To fine tune customer parameters'
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateCustomerRequest'
      responses:
        "200":
          description: 'Customer was successfully generated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateCustomerResponse'
        "400":
          description: 'Bad input parameters'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/generator-management/resetQuietPeriod:
    post:
      description: "When customer generator encounters an error, it suspends generating for some amount of time. This time increases continually, if any error
        is encountered again, up to some maximum. This time is called a quiet period.
        This method resets the quiet period for one or more customer generators. It is used by admin (when the error causing generator failure is fixed) 
        to start generator immediately and shorten the waiting till the end of the quiet period"
      tags:
        - generator-management-controller
      operationId: resetQuietPeriod
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetQuietPeriodRequest'
      responses:
        "200":
          description: 'Quiet period has been reset'
        "400":
          description: 'Bad input parameters'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/generator-status/status:
    get:
      description: 'Returns current state of customer generators'
      tags:
        - generator-status-controller
      operationId: status
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GeneratorStatusResponse'


  /customers/unpair-myair/{cuid}:
    post:
      description: 'Unpairs all active mobile apps and changes primary authorization element to SMS'
      tags:
        - customers-controller
      operationId: unpairMyair
      parameters:
        - in: path
          name: cuid
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: 'Mobile apps are unpaired and primary element set to SMS'

  /documents/identification/generate:
    post:
      description: 'Generates identification document with random number unused in environment'
      tags:
        - documents-controller
      operationId: generateDocument
      requestBody:
        description: request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateDocumentRequest'
        required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateDocumentResponse'

  /addresses/random:
    post:
      tags:
        - addresses-controller
      operationId: getRandomRuianAddress
      requestBody:
        description: request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RandomAddressRequest'
        required: false
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RandomAddressResponse'

  /sipo/unique:
    post:
      tags:
        - sipo-controller
      operationId: generateUniqueSipo
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UniqueSipoResponse'

  /payments/instant/incoming:
    post:
      tags:
        - payments-controller
      operationId: processIncomingInstantPayment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IncomingInstantPaymentRequest'
        required: true
      responses:
        "200":
          description: OK

  /payments/cvak/createPending:
    post:
      tags:
        - cvak-transactions-controller
      operationId: createCvakTransaction
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/CreateCvakPendingTransactionStickerRequest'
                - $ref: '#/components/schemas/CreateCvakPendingTransactionPartnerRequest'
              discriminator:
                propertyName: refType
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCvakPendingTransactionResponse'

  /payments/cvak/cleanSticker:
    post:
      tags:
        - cvak-transactions-controller
      operationId: cleanCvakSticker
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CleanCvakStickerRequest'
        required: true
      responses:
        "200":
          description: 'Sticker has no pending transaction'
        "400":
          description: 'Bad input parameters'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payments/cvak/cancelTransaction:
    post:
      tags:
        - cvak-transactions-controller
      operationId: cancelTransaction
      parameters:
        - in: query
          description: 'transaction ID to cancel'
          name: transactionId
          required: true
          schema:
            type: string
      responses:
        "200":
          description: 'Transaction is cancelled'
        "400":
          description: 'Bad input parameters'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'