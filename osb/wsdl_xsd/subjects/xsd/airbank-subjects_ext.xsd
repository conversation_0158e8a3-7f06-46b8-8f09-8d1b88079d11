<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="https://airbank.bisnode.cz/subjests_ext" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="https://airbank.bisnode.cz/subjests_ext"
           attributeFormDefault="unqualified" elementFormDefault="qualified">
	<xs:element name="query_result">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="head"/>
				<xs:element ref="data"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="head">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="total_count"/>
				<xs:element ref="performance_info"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="total_count" type="xs:integer"/>
	<xs:element name="performance_info">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="filter"/>
				<xs:element ref="output"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="filter" type="xs:integer"/>
	<xs:element name="output" type="xs:integer"/>
	<xs:element name="data">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" ref="subject"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="subject">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="entity_id"/>
				<xs:element ref="registration_number"/>
				<xs:element ref="vat_identification_number"/>
				<xs:element ref="name"/>
				<xs:element ref="date_of_establishment"/>
				<xs:element ref="nace"/>
				<xs:element ref="type"/>
				<xs:element ref="active"/>
				<xs:element ref="country_code"/>
				<xs:element ref="town"/>
				<xs:element ref="locality"/>
				<xs:element ref="street"/>
				<xs:element ref="house_number"/>
				<xs:element ref="postcode"/>
				<xs:element ref="empl_cnt"/>
				<xs:element ref="empl_category_lo_bound"/>
				<xs:element ref="empl_category_up_bound"/>
				<xs:element ref="phones"/>
				<xs:element ref="webs"/>
				<xs:element ref="emails"/>
				<xs:element ref="secondary_naces"/>
				<xs:element ref="account_numbers"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="entity_id" type="xs:long"/>
	<xs:element name="registration_number" type="xs:string"/>
	<xs:element name="vat_identification_number" type="xs:string"/>
	<xs:element name="name" type="xs:string"/>
	<xs:element name="date_of_establishment" type="xs:date"/>
	<xs:element name="type">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="code"/>
				<xs:element ref="text"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="active" type="xs:boolean"/>
	<xs:element name="country_code" type="xs:string"/>
	<xs:element name="town" type="xs:string"/>
	<xs:element name="locality" type="xs:string"/>
	<xs:element name="street" type="xs:string"/>
	<xs:element name="house_number" type="xs:string"/>
	<xs:element name="postcode" type="xs:string"/>

	<xs:element name="empl_cnt">
		<xs:simpleType>
			<xs:union>
				<xs:simpleType>
					<xs:restriction base='xs:string'>
						<xs:length value="0"/>
					</xs:restriction>
				</xs:simpleType>
				<xs:simpleType>
					<xs:restriction base='xs:long' />
				</xs:simpleType>
			</xs:union>
		</xs:simpleType>
	</xs:element>
	<xs:element name="empl_category_lo_bound">
		<xs:simpleType>
			<xs:union>
				<xs:simpleType>
					<xs:restriction base='xs:string'>
						<xs:length value="0"/>
					</xs:restriction>
				</xs:simpleType>
				<xs:simpleType>
					<xs:restriction base='xs:long' />
				</xs:simpleType>
			</xs:union>
		</xs:simpleType>
	</xs:element>
	<xs:element name="empl_category_up_bound">
		<xs:simpleType>
			<xs:union>
				<xs:simpleType>
					<xs:restriction base='xs:string'>
						<xs:length value="0"/>
					</xs:restriction>
				</xs:simpleType>
				<xs:simpleType>
					<xs:restriction base='xs:long' />
				</xs:simpleType>
			</xs:union>
		</xs:simpleType>
	</xs:element>

	<xs:element name="phones">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" maxOccurs="unbounded" ref="phone"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="phone" type="xs:string"/>
	<xs:element name="webs">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" maxOccurs="unbounded" ref="web"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="web" type="xs:string"/>
	<xs:element name="emails">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" maxOccurs="unbounded" ref="email"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="email" type="xs:string"/>
	<xs:element name="secondary_naces">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" maxOccurs="unbounded" ref="nace"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="account_numbers">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="account_number" maxOccurs="unbounded" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element type="xs:string" name="full_account_number"/>
							<xs:element type="xs:string" name="account_number"/>
							<xs:element type="xs:string" name="bank_code"/>
							<xs:element type="xs:boolean" name="multiple_usage"/>
							<xs:element type="xs:string" name="iban"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="nace">
		<xs:complexType>
			<xs:sequence>
				<xs:element minOccurs="0" maxOccurs="unbounded" ref="node"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="node">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="code"/>
				<xs:element ref="text"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="code" type="xs:string"/>
	<xs:element name="text" type="xs:string"/>
</xs:schema>
