<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://airbank.cz/osb/bisnode/monitoring"
            xmlns="http://airbank.cz/osb/bisnode/monitoring" jxb:version="2.1" elementFormDefault="qualified">

    <xsd:element name="MonitoringRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="group_id" type="xsd:long" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>group id</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="date_from" type="xsd:string" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>date from (as yyyyMMdd)</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="date_to" type="xsd:string" maxOccurs="1" minOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>date to (as yyyyMMdd)</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="MonitoringResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="report" minOccurs="1" maxOccurs="1">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="row" minOccurs="0" maxOccurs="unbounded">
                                <xsd:annotation>
                                    <xsd:documentation>List of monitoring records</xsd:documentation>
                                </xsd:annotation>
                                <xsd:complexType>
                                    <xsd:sequence>
                                        <xsd:element name="data_type">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element type="xsd:int" name="data_dt"/>
                                        <xsd:element type="xsd:long" name="ent_id"/>
                                        <xsd:element name="name">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                        <xsd:element name="reg_nbr">
                                          <xsd:simpleType>
                                            <xsd:restriction base="xsd:string">
                                              <xsd:minLength value="1" />
                                            </xsd:restriction>
                                          </xsd:simpleType>
                                        </xsd:element>                                        
                                    </xsd:sequence>
                                </xsd:complexType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>