<wsdl:definitions targetNamespace="http://airbank.cz/notification/ws/notificationWS" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:com="http://arbes.com/ib/core/ppf/ws/common/" xmlns="http://airbank.cz/notification/ws/notificationWS">
  <wsdl:types>
    <xsd:schema targetNamespace="http://airbank.cz/notification/ws/notificationWS/">
      <xsd:import namespace="http://airbank.cz/notification/ws/notificationWS" schemaLocation="NotificationWS.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="registerPushTokenRequest">
    <wsdl:part name="registerPushTokenRequest" element="registerPushTokenRequest"/>
  </wsdl:message>  
  <wsdl:message name="registerPushTokenResponse">
    <wsdl:part name="registerPushTokenResponse" element="registerPushTokenResponse"/>
  </wsdl:message> 
  <wsdl:message name="sendAuthorizationNotificationRequest">
    <wsdl:part name="sendAuthorizationNotificationRequest" element="sendAuthorizationNotificationRequest"/>
  </wsdl:message>  
  <wsdl:message name="sendAuthorizationNotificationResponse">
    <wsdl:part name="sendAuthorizationNotificationResponse" element="sendAuthorizationNotificationResponse"/>
  </wsdl:message>
   <wsdl:message name="sendAuthDeviceChangeNotificationRequest">
    <wsdl:part name="sendAuthDeviceChangeNotificationRequest" element="sendAuthDeviceChangeNotificationRequest"/>
  </wsdl:message>  
  <wsdl:message name="sendAuthDeviceChangeNotificationResponse">
    <wsdl:part name="sendAuthDeviceChangeNotificationResponse" element="sendAuthDeviceChangeNotificationResponse"/>
  </wsdl:message>
   <wsdl:message name="sendInformationNotificationRequest">
    <wsdl:part name="sendInformationNotificationRequest" element="sendInformationNotificationRequest"/>
  </wsdl:message>  
  <wsdl:message name="sendInformationNotificationResponse">
    <wsdl:part name="sendInformationNotificationResponse" element="sendInformationNotificationResponse"/>
  </wsdl:message> 
  <wsdl:message name="sendPushNotification2ClientsRequest">
    <wsdl:part name="sendPushNotification2ClientsRequest" element="sendPushNotification2ClientsRequest"/>
  </wsdl:message>
  <wsdl:message name="sendPushNotification2ClientsResponse">
    <wsdl:part name="sendPushNotification2ClientsResponse" element="sendPushNotification2ClientsResponse"/>
  </wsdl:message>
  <wsdl:message name="sendTravelInsuranceNotificationRequest">
    <wsdl:part name="sendTravelInsuranceNotificationRequest" element="sendTravelInsuranceNotificationRequest"/>
  </wsdl:message>
  <wsdl:message name="sendTravelInsuranceNotificationResponse">
    <wsdl:part name="sendTravelInsuranceNotificationResponse" element="sendTravelInsuranceNotificationResponse"/>
  </wsdl:message>
  <wsdl:message name="sendRemoteNotificationMessageRequest">
    <wsdl:part name="sendRemoteNotificationMessageRequest" element="sendRemoteNotificationMessageRequest"/>
  </wsdl:message>
  <wsdl:message name="sendRemoteNotificationMessageResponse">
    <wsdl:part name="sendRemoteNotificationMessageResponse" element="sendRemoteNotificationMessageResponse"/>
  </wsdl:message>
  <wsdl:message name="sendDeepLinkPushNotification2ClientRequest">
    <wsdl:part name="sendDeepLinkPushNotification2ClientRequest" element="sendDeepLinkPushNotification2ClientRequest"/>
  </wsdl:message>
  <wsdl:message name="sendDeepLinkPushNotification2ClientResponse">
    <wsdl:part name="sendDeepLinkPushNotification2ClientResponse" element="sendDeepLinkPushNotification2ClientResponse"/>
  </wsdl:message>
  <wsdl:message name="sendNfcTokenUpdatedNotificationRequest">
    <wsdl:part name="sendNfcTokenUpdatedNotificationRequest" element="sendNfcTokenUpdatedNotificationRequest"/>
  </wsdl:message>
  <wsdl:message name="sendNfcTokenUpdatedNotificationResponse">
    <wsdl:part name="sendNfcTokenUpdatedNotificationResponse" element="sendNfcTokenUpdatedNotificationResponse"/>
  </wsdl:message>
  <wsdl:message name="sendPromoAdNotificationsRequest">
    <wsdl:part name="sendPromoAdNotificationsRequest" element="sendPromoAdNotificationsRequest"/>
  </wsdl:message>
  <wsdl:message name="sendPromoAdNotificationsResponse">
    <wsdl:part name="sendPromoAdNotificationsResponse" element="sendPromoAdNotificationsResponse"/>
  </wsdl:message>
  <wsdl:message name="sendDiscountProgramRedemptionNotificationRequest">
    <wsdl:part name="sendDiscountProgramRedemptionNotificationRequest" element="sendDiscountProgramRedemptionNotificationRequest"/>
  </wsdl:message>
  <wsdl:message name="sendDiscountProgramRedemptionNotificationResponse">
    <wsdl:part name="sendDiscountProgramRedemptionNotificationResponse" element="sendDiscountProgramRedemptionNotificationResponse"/>
  </wsdl:message>
  <wsdl:message name="sendNfcTokenCumulativeAmountUpdateNotificationRequest">
    <wsdl:part name="sendNfcTokenCumulativeAmountUpdateNotificationRequest" element="sendNfcTokenCumulativeAmountUpdateNotificationRequest"/>
  </wsdl:message>
  <wsdl:message name="sendNfcTokenCumulativeAmountUpdateNotificationResponse">
    <wsdl:part name="sendNfcTokenCumulativeAmountUpdateNotificationResponse" element="sendNfcTokenCumulativeAmountUpdateNotificationResponse"/>
  </wsdl:message>
  <wsdl:portType name="NotificationWS">
    <wsdl:operation name="registerPushToken">
      <wsdl:input message="registerPushTokenRequest"/>
      <wsdl:output message="registerPushTokenResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendAuthorizationNotification">
      <wsdl:input message="sendAuthorizationNotificationRequest"/>
      <wsdl:output message="sendAuthorizationNotificationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendAuthDeviceChangeNotification">
      <wsdl:input message="sendAuthDeviceChangeNotificationRequest"/>
      <wsdl:output message="sendAuthDeviceChangeNotificationResponse"/>
    </wsdl:operation>    
    <wsdl:operation name="sendInformationNotification">
      <wsdl:input message="sendInformationNotificationRequest"/>
      <wsdl:output message="sendInformationNotificationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendPushNotification2Clients">
      <wsdl:input message="sendPushNotification2ClientsRequest"/>
      <wsdl:output message="sendPushNotification2ClientsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendTravelInsuranceNotification">
      <wsdl:input message="sendTravelInsuranceNotificationRequest"/>
      <wsdl:output message="sendTravelInsuranceNotificationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendRemoteNotificationMessage">
      <wsdl:input message="sendRemoteNotificationMessageRequest"/>
      <wsdl:output message="sendRemoteNotificationMessageResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendDeepLinkPushNotification2Client">
      <wsdl:input message="sendDeepLinkPushNotification2ClientRequest"/>
      <wsdl:output message="sendDeepLinkPushNotification2ClientResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendNfcTokenUpdatedNotification">
      <wsdl:input message="sendNfcTokenUpdatedNotificationRequest"/>
      <wsdl:output message="sendNfcTokenUpdatedNotificationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendPromoAdNotifications">
      <wsdl:input message="sendPromoAdNotificationsRequest"/>
      <wsdl:output message="sendPromoAdNotificationsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendDiscountProgramRedemptionNotification">
      <wsdl:input message="sendDiscountProgramRedemptionNotificationRequest"/>
      <wsdl:output message="sendDiscountProgramRedemptionNotificationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="sendNfcTokenCumulativeAmountUpdateNotification">
      <wsdl:input message="sendNfcTokenCumulativeAmountUpdateNotificationRequest"/>
      <wsdl:output message="sendNfcTokenCumulativeAmountUpdateNotificationResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="NotificationWSSoap11" type="NotificationWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="registerPushToken">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>    
    <wsdl:operation name="sendAuthorizationNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendAuthDeviceChangeNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendInformationNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendPushNotification2Clients">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendTravelInsuranceNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendRemoteNotificationMessage">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendDeepLinkPushNotification2Client">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendNfcTokenUpdatedNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendPromoAdNotifications">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendDiscountProgramRedemptionNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="sendNfcTokenCumulativeAmountUpdateNotification">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="NotificationWSService">
    <wsdl:port binding="NotificationWSSoap11" name="NotificationWSSoap11">
      <soap:address location="http://TO-BE-SPECIFIED/NotificationWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>