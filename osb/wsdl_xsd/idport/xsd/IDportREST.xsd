<?xml version = '1.0' encoding = 'UTF-8'?>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://airbank.cz/IDport/ws/IDportWS" xmlns:tns="http://airbank.cz/IDport/ws/IDportWS" elementFormDefault="qualified" xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" nxsd:version="JSON" nxsd:encoding="UTF-8">

        <xs:element name="getConsentsRequest">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="alias" type="tns:Alias" />
                    <xs:element name="requiredAttributes" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
                    <xs:element name="tenant" type="xs:string" minOccurs="0" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="getConsentsResponse">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="status" type="xs:string" />
                    <xs:element name="data" type="tns:Data" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="getConsentsError">
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="status" type="xs:string" />
                    <xs:element name="error" type="xs:string" />
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:complexType name="Alias">
            <xs:sequence>
                <xs:element name="alias" type="xs:long" />
            </xs:sequence>
        </xs:complexType>


        <xs:complexType name="Data">
            <xs:sequence>
                <xs:element name="attributes" type="tns:Attribute" minOccurs="0" maxOccurs="unbounded" />
                <xs:element name="muid" type="tns:Muid" />
                <xs:element name="domainAgreements" type="tns:DomainAgreements" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>

        <xs:complexType name="Muid">
            <xs:sequence>
                <xs:element name="tenant" type="xs:string" />
                <xs:element name="value" type="xs:long" />
            </xs:sequence>
        </xs:complexType>

        <xs:complexType name="DomainAgreements">
            <xs:sequence>
                <xs:element name="domain" type="xs:string" minOccurs="0" maxOccurs="1" />
                <xs:element name="domainId" type="xs:string" minOccurs="0" maxOccurs="1" />
                <xs:element name="agreements" type="tns:Agreements" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>

        <xs:complexType name="Agreements">
            <xs:sequence>
                <xs:element name="id" type="xs:string" />
                <xs:element name="status" type="xs:string" />
            </xs:sequence>
        </xs:complexType>
        
        <xs:complexType name="Attribute">
            <xs:sequence>
                <xs:element name="type" type="xs:string" minOccurs="0" />
                <xs:element name="value" type="xs:string" minOccurs="0" />
                <xs:element name="legalReason" type="tns:LegalReason" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>
        
        <xs:complexType name="LegalReason">
            <xs:sequence>
                <xs:element name="reason" type="xs:string" />
                <xs:element name="validity" type="xs:string" minOccurs="0" />
            </xs:sequence>
        </xs:complexType>

    </xs:schema>
