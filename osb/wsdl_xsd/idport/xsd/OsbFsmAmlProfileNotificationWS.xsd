<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/ws/ProcessCustomerAmlRiskDataSet" elementFormDefault="qualified" targetNamespace="http://airbank.cz/osb/ws/ProcessCustomerAmlRiskDataSet">


    <xs:element name="processCustomerAmlRiskDataSetRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Distribuuje notifikaci o změnách AML rizikovosti fyzických osob</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
                <xs:element name="riskCategorySet" type="AmlRiskCategory" minOccurs="0" maxOccurs="1" />
                <xs:element name="riskFactorChange" type="RiskFactorChangeTO" minOccurs="1" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="processCustomerAmlRiskDataSetResponse">
        <xs:complexType>
        </xs:complexType>
    </xs:element>



    <xs:complexType name="RiskFactorChangeTO">
        <xs:sequence>
            <xs:element name="riskFactor" type="RiskFactorTO" minOccurs="1" maxOccurs="1" />
            <xs:element name="change" type="NotificationChange" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AmlRiskCategory">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACCEPTED" />
            <xs:enumeration value="DECLINED" />
            <xs:enumeration value="RISKFUL" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="NotificationChange">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDED" />
            <xs:enumeration value="REMOVED" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="RiskFactorTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BUSINESS_SUBJECT" />
            <xs:enumeration value="FAU_REPORT" />
            <xs:enumeration value="OTHER" />
            <xs:enumeration value="INTERNAL_BLACKLIST" />
            <xs:enumeration value="NO_ECONOMIC_ACTIVITY" />
            <xs:enumeration value="NOMINEE_STRUCTURE" />
            <xs:enumeration value="OPAQUE_OWNERSHIP" />
            <xs:enumeration value="PEP" />
            <xs:enumeration value="RISK_COUNTRY_ORIGIN" />
            <xs:enumeration value="RISK_COUNTRY_TRADE" />
            <xs:enumeration value="RISK_EXECUTIVE" />
            <xs:enumeration value="SANCTIONS" />
            <xs:enumeration value="SUSPICIOUS_BEHAVIOUR" />
            <xs:enumeration value="VIRTUAL_CURRENCY_TRADE" />
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
