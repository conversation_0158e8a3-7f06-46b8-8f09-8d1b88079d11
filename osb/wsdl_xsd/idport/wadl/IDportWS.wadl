<?xml version="1.0" encoding="utf-8"?>
<application xmlns:soa="http://www.oracle.com/soa/rest"
             xmlns:tns="http://airbank.cz/IDport/ws/IDportWS" xmlns:xml="http://www.w3.org/XML/1998/namespace"
             xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://wadl.dev.java.net/2009/02 http://www.w3.org/Submission/wadl/wadl.xsd"
             xmlns="http://wadl.dev.java.net/2009/02">

  <grammars>
      <xs:schema>
            <xs:import namespace="http://airbank.cz/IDport/ws/IDportWS" schemaLocation="../xsd/IDport.xsd"/>
        </xs:schema>
  </grammars>
  
  <resources>
    <resource path="/consent">
      <method name="POST" soa:wsdlOperation="getConsents">
        <request>
          <representation mediaType="application/json" element="tns:getConsentsRequest">
          </representation>
        </request>
        <response status="200">
          <representation mediaType="application/json" element="tns:getConsentsResponse">
          </representation>
        </response>
        <response status="400 401 500" soa:wsdlFaultName="getConsentsError">
          <representation mediaType="application/json" element="tns:getConsentsError">
          </representation>
        </response>
      </method>
    </resource>
    
    <resource path="/v1/ext/logout">
      <method name="POST" soa:wsdlOperation="removeConsent">
        <request>
          <representation mediaType="application/json" element="tns:removeConsentRequest">
          </representation>
        </request>
        <response status="200">
          <representation mediaType="application/json" element="tns:removeConsentResponse">
          </representation>
        </response>
        <response status="400 500" soa:wsdlFaultName="removeConsentError">
          <representation mediaType="application/json" element="tns:removeConsentError">
          </representation>
        </response>
      </method>
    </resource>
    
    <resource path="/int/notify">
      <method name="POST" soa:wsdlOperation="notify">
        <request>
          <representation mediaType="application/json" element="tns:notifyRequest">
          </representation>
        </request>
        <response status="200">
          <representation mediaType="application/json" element="tns:notifyResponse">
          </representation>
        </response>
        <response status="400 500" soa:wsdlFaultName="notifyError">
          <representation mediaType="application/json" element="tns:notifyError">
          </representation>
        </response>
      </method>
    </resource>
    
  </resources>
</application>