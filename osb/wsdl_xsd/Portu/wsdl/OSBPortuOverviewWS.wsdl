<?xml version="1.0" encoding="UTF-8"?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/portu/portuProxy" targetNamespace="http://airbank.cz/osb/portu/portuProxy">
        <wsdl:types>
            <xs:schema targetNamespace="http://airbank.cz/osb/portu/portuProxy">
                <xs:include schemaLocation="../xsd/OSBPortuOverviewWS.xsd" />
            </xs:schema>
            <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xs:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="GetRequest">
            <wsdl:part element="GetRequest" name="GetRequest" />
        </wsdl:message>
        <wsdl:message name="GetResponse">
            <wsdl:part element="GetResponse" name="GetResponse" />
        </wsdl:message>
        <wsdl:message name="GetFault">
            <wsdl:part element="commonFault:CoreFaultElement" name="GetFault" />
        </wsdl:message>


        <wsdl:portType name="PortuProxyPort">
            <wsdl:operation name="GetOperation">
                <wsdl:input message="GetRequest" />
                <wsdl:output message="GetResponse" />
                <wsdl:fault name="GetFault" message="GetFault" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="PortuProxyServiceBinding" type="PortuProxyPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="GetOperation">
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="GetFault">
                    <soap:fault name="GetFault" use="literal" />
                </wsdl:fault>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="PortuProxyPortService">
            <wsdl:documentation>Private web service</wsdl:documentation>
            <wsdl:port name="PortuProxyPortService" binding="PortuProxyServiceBinding">
                <soap:address location="http://localhost:8000/ws/" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
