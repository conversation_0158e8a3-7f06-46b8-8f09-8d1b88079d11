<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
                  xmlns="http://airbank.cz/complaint/ws/complaintWS" targetNamespace="http://airbank.cz/complaint/ws/complaintWS">
  <wsdl:types>
    <xsd:schema targetNamespace="http://airbank.cz/complaint/ws/complaintWS/">
      <xsd:import namespace="http://airbank.cz/complaint/ws/complaintWS"
                  schemaLocation="ComplaintWS.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="createComplaintRequest">
    <wsdl:part name="createComplaintRequest" element="createComplaintRequest"></wsdl:part>
  </wsdl:message>  
  <wsdl:message name="createNonClientComplaintRequest">
    <wsdl:part name="createNonClientComplaintRequest" element="createNonClientComplaintRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="createClientComplaintRequest">
    <wsdl:part name="createClientComplaintRequest" element="createClientComplaintRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="createComplaintResponse">
    <wsdl:part name="createComplaintResponse" element="createComplaintResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getOverviewURLRequest">
    <wsdl:part name="getOverviewURLRequest" element="getOverviewURLRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getOverviewURLResponse">
    <wsdl:part name="getOverviewURLResponse" element="getOverviewURLResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getDetailURLRequest">
    <wsdl:part name="getDetailURLRequest" element="getDetailURLRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getDetailURLResponse">
    <wsdl:part name="getDetailURLResponse" element="getDetailURLResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComplaintsRequest">
    <wsdl:part name="getComplaintsRequest" element="getComplaintsRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComplaintsResponse">
    <wsdl:part name="getComplaintsResponse" element="getComplaintsResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="addCommentRequest">
    <wsdl:part name="addCommentRequest" element="addCommentRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="addCommentResponse">
    <wsdl:part name="addCommentResponse" element="addCommentResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="OSBComplaintWS">
    <wsdl:operation name="createNonClientComplaint">
      <wsdl:input message="createNonClientComplaintRequest"/>
      <wsdl:output message="createComplaintResponse"/>
    </wsdl:operation>
    <wsdl:operation name="createComplaint">
      <wsdl:input message="createComplaintRequest"/>
      <wsdl:output message="createComplaintResponse"/>
    </wsdl:operation>    
    <wsdl:operation name="createClientComplaint">
      <wsdl:input message="createClientComplaintRequest"/>
      <wsdl:output message="createComplaintResponse"/>
    </wsdl:operation>
    <wsdl:operation name="getComplaints">
      <wsdl:input message="getComplaintsRequest"/>
      <wsdl:output message="getComplaintsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="getDetailURL">
      <wsdl:input message="getDetailURLRequest"/>
      <wsdl:output message="getDetailURLResponse"/>
    </wsdl:operation>
    <wsdl:operation name="getOverviewURL">
      <wsdl:input message="getOverviewURLRequest"/>
      <wsdl:output message="getOverviewURLResponse"/>
    </wsdl:operation>
    <wsdl:operation name="addComment">
      <wsdl:input message="addCommentRequest"/>
      <wsdl:output message="addCommentResponse"/>
    </wsdl:operation>    
  </wsdl:portType>
  <wsdl:binding name="ComplaintWSSoap11" type="OSBComplaintWS">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="createNonClientComplaint">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createComplaint">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>    
    <wsdl:operation name="createClientComplaint">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComplaints">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDetailURL">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getOverviewURL">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addComment">
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ComplaintWSService">
    <wsdl:port binding="ComplaintWSSoap11" name="ComplaintWSSoap11">
      <soap:address location="http://TO-BE-SPECIFIED/OBSComplaintWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
