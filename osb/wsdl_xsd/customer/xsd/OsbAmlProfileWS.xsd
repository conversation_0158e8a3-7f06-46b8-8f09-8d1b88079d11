<?xml version="1.0" encoding="UTF-8"?>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/ws/amlprofile" targetNamespace="http://airbank.cz/osb/ws/amlprofile" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" jxb:version="2.1" elementFormDefault="qualified">


        <xsd:element name="SearchCustomerAmlProfilesRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="riskCategory" type="xsd:string" minOccurs="0" />
                    <xsd:element name="riskFactor" type="xsd:string" minOccurs="0" />
                    <xsd:element name="resultListFromCuid" type="xsd:long" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SearchCustomerAmlProfilesResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="customerAmlProfileWithUndesirableData" type="CustomerAmlProfileWithUndesirableData" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="totalCount" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SearchGeneralContractCustomerAmlProfilesRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="resultListFromGeneralContactId" type="xsd:long" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>

        <xsd:element name="SearchGeneralContractCustomerAmlProfilesResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="generalContactCustomerAmlProfile" type="GeneralContactCustomerAmlProfile" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="totalCount" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>





        <xsd:complexType name="CustomerAmlProfileWithUndesirableData">
            <xsd:annotation>
                <xsd:documentation>
                    Aml rizikovost FON. Pokud AML rizikovost FON není evidována je kategorie rizikovosti ACCEPTED a nejsou evidovány žádné faktory zvýšené rizikovosti.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="generalCustomer" type="GeneralCustomerWithIdentificationDataToDisplay" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikátor klienta</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="amlProfile" type="AmlProfile" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Příznak manuálního prověřování FON na AML rizika</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="undesirableData" type="UndesirableCustomer" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Poslední (nejnovější) evidovaný záznam manuálního provvěřování FON osoby na AML rizika
                                </jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>


        <xsd:complexType name="GeneralContractCustomerAmlProfile">
            <xsd:sequence>
                <xsd:element name="generalContractId" type="xsd:long" />
                <xsd:element name="relation" type="xsd:string" />
                <xsd:element name="customerAmlProfileWithUndesirableData" type="CustomerAmlProfileWithUndesirableData" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="GeneralContactCustomerAmlProfile">
            <xsd:sequence>
                <xsd:element name="generalContractId" type="xsd:long" minOccurs="1" maxOccurs="1" />
                <xsd:element name="relation" type="Relation" minOccurs="1" maxOccurs="1" />
                <xsd:element name="customerAmlProfileWithUndesirableData" type="CustomerAmlProfileWithUndesirableData" minOccurs="1" maxOccurs="1" />
            </xsd:sequence>
        </xsd:complexType>



        <xsd:complexType name="GeneralCustomerWithIdentificationDataToDisplay">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                    <xsd:element name="legalSegment" type="xsd:string" />
                    <xsd:element name="nameToDisplay" type="xsd:string" />
                    <xsd:element name="identificationNumber" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="birthDate" type="xsd:date" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="AmlProfile">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="amlManualVerificationFlag" type="xsd:boolean" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="amlRiskData" type="AmlRiskData" minOccurs="1" maxOccurs="1" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="AmlRiskData">
            <xsd:annotation>
                <xsd:documentation>AML rizikovost</xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="AmlRiskDataBase">
                    <xsd:sequence>
                        <xsd:element name="riskCategoryFrom" type="xsd:date" minOccurs="0">
                            <xsd:annotation>
                                <xsd:appinfo>
                                    <jxb:property>
                                        <jxb:javadoc>Od kdy daná riziková kategorie rizikovosti platí</jxb:javadoc>
                                    </jxb:property>
                                </xsd:appinfo>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="fsmRequestId" type="xsd:long" minOccurs="0">
                            <xsd:annotation>
                                <xsd:appinfo>
                                    <jxb:property>
                                        <jxb:javadoc>
                                            Odkaz na fsm požadavek, kterým byl aml profil upraven
                                        </jxb:javadoc>
                                    </jxb:property>
                                </xsd:appinfo>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>


        <xsd:complexType name="AmlRiskDataBase">
            <xsd:annotation>
                <xsd:documentation>Základní objekt Aml rizikovosti.</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="riskCategory" type="xsd:string">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Kategorie rizikovosti osoby podle číselníku AmlRiskCategory</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="riskFactors" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Kolekce faktorů zvýšeného rizika (může být prázdná). Jednotlivé položky jsou z číselníku RiskFactor.
                                </jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>

        <xsd:complexType name="UndesirableCustomer">
            <xsd:sequence>
                <xsd:sequence>
                    <xsd:element name="insertReason" type="InsertReason" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="note" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="validFrom" type="xsd:date" minOccurs="1" maxOccurs="1" />
                    <xsd:element name="validTo" type="xsd:date" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>




        <xsd:simpleType name="InsertReason">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="0 nevyplněno" />
                <xsd:enumeration value="1 Blaze" />
                <xsd:enumeration value="2 manuální" />
                <xsd:enumeration value="3 BASIC_ACCOUNT" />
                <xsd:enumeration value="4 ECP" />
            </xsd:restriction>
        </xsd:simpleType>


        <xsd:simpleType name="Relation">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="OWNER" />
                <xsd:enumeration value="DISPONENT" />
                <xsd:enumeration value="CARD_HOLDER" />
                <xsd:enumeration value="ENTITLED" />
            </xsd:restriction>
        </xsd:simpleType>


        <xsd:simpleType name="RiskFactor">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="BUSINESS_SUBJECT">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Rizikový předmět podnikáníRizikový předmět podnikání</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="FAU_REPORT">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Hlášení FAU</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="INTERNAL_BLACKLIST">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Hlášení FAU</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="NO_ECONOMIC_ACTIVITY">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Hlášení FAU</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="NOMINEE_STRUCTURE">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Hlášení FAU</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="OPAQUE_OWNERSHIP">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Hlášení FAU</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="OTHER">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jiné</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="PEP">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Politicky exponovaná osoba</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="RISK_COUNTRY_ORIGIN">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Klient má původ v rizikové zemi</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="RISK_COUNTRY_TRADE">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Klient obchoduje s rizikovou zemí</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="RISK_EXECUTIVE">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Klient obchoduje s rizikovou zemí</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="SANCTIONS">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Sankce</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="SUSPICIOUS_BEHAVIOUR">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Podezřelé chování</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="VIRTUAL_CURRENCY_TRADE">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Klient obchoduje s virtuální měnou</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
            </xsd:restriction>
        </xsd:simpleType>


        <xsd:simpleType name="AmlRiskCategory">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="ACCEPTED">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Přijatelný</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="DECLINED">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Nepřijatelný</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
                <xsd:enumeration value="RISKFUL">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Rizikový</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:enumeration>
            </xsd:restriction>
        </xsd:simpleType>


    </xsd:schema>
