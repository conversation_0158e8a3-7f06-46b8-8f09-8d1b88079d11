<?xml version="1.0" encoding="UTF-8" ?>
    <wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://osb.abank.cz/osb/ws/Anonymization" targetNamespace="http://osb.abank.cz/osb/ws/Anonymization" xmlns:common="http://airbank.cz/common/ws/fault">

        <wsdl:types>
            <xs:schema targetNamespace="http://osb.abank.cz/osb/ws/Anonymization">
                <xs:include schemaLocation="AnonymizationWS.xsd" />
            </xs:schema>
            <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xs:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xs:schema>
        </wsdl:types>

        <wsdl:message name="AnonymizeRequest">
            <wsdl:part element="anonymizeRequest" name="AnonymizeRequest" />
        </wsdl:message>
        <wsdl:message name="AnonymizeResponse">
            <wsdl:part element="anonymizeResponse" name="AnonymizeResponse" />
        </wsdl:message>
        <wsdl:message name="AnonymizeFaultMessage">
            <wsdl:part element="common:CoreFaultElement" name="AnonymizeFaultMessage" />
        </wsdl:message>

        <wsdl:message name="AnonymizeEntrepreneursRequest">
            <wsdl:part element="anonymizeEntrepreneursRequest" name="AnonymizeEntrepreneursRequest" />
        </wsdl:message>
        <wsdl:message name="AnonymizeEntrepreneursResponse">
            <wsdl:part element="anonymizeEntrepreneursResponse" name="AnonymizeEntrepreneursResponse" />
        </wsdl:message>
        <wsdl:message name="AnonymizeEntrepreneursFaultMessage">
            <wsdl:part element="common:CoreFaultElement" name="AnonymizeEntrepreneursFaultMessage" />
        </wsdl:message>

        <wsdl:portType name="Anonymization">
            <wsdl:operation name="anonymize">
                <xs:documentation>Poskytování dat z AB do HC</xs:documentation>
                <wsdl:input message="AnonymizeRequest" />
                <wsdl:output message="AnonymizeResponse" />
            </wsdl:operation>
            <wsdl:operation name="anonymizeEntrepreneur">
                <wsdl:documentation>Operation is used to anonymize entrepreneurs in CIF database.</wsdl:documentation>
                <wsdl:input message="AnonymizeEntrepreneursRequest" name="AnonymizeEntrepreneursRequest" />
                <wsdl:output message="AnonymizeEntrepreneursResponse" name="AnonymizeEntrepreneursResponse" />
                <wsdl:fault message="AnonymizeEntrepreneursFaultMessage" name="AnonymizeEntrepreneursFaultMessage" />
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="AnonymizationBinding" type="Anonymization">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="anonymize">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
            <wsdl:operation name="anonymizeEntrepreneur">
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="AnonymizeEntrepreneursFaultMessage">
                    <soap:fault name="AnonymizeEntrepreneursFaultMessage" use="literal" />
                </wsdl:fault>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="AnonymizationWS">
            <wsdl:port binding="AnonymizationBinding" name="AnonymizationPort">
            </wsdl:port>
        </wsdl:service>

        <wsdl:service name="AnonymizationService">
            <wsdl:port binding="AnonymizationBinding" name="AnonymizationPort">
                <soap:address location="http://TO-BE-SPECIFIED/osb/AnonymizationWS" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
