<?xml version="1.0" encoding="UTF-8" ?>
    <!-- $Id: 40b88ecbd90649c36cae627be8cd2857269a1f4c $ -->
    <definitions targetNamespace="http://osb.abank.cz/customer/view" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://osb.abank.cz/customer/view" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
        <types>
            <xsd:schema targetNamespace="http://osb.abank.cz/customer/view" elementFormDefault="qualified">
                <xsd:include schemaLocation="Customer360ViewWS.xsd" />
            </xsd:schema>
        </types>
        <message name="getCustomerBasicProfileRequest">
            <part name="getCustomerBasicProfileRequest" element="tns:getCustomerBasicProfileRequest" />
        </message>
        <message name="getCustomerBasicProfileResponse">
            <part name="getCustomerBasicProfileResponse" element="tns:getCustomerBasicProfileResponse" />
        </message>
        <message name="identifyCustomersRequest">
            <part name="identifyCustomersRequest" element="tns:identifyCustomersRequest" />
        </message>
        <message name="identifyCustomersResponse">
            <part name="identifyCustomersResponse" element="tns:identifyCustomersResponse" />
        </message>
        <message name="getCustomerDetailRequest">
            <part name="getCustomerDetailRequest" element="tns:getCustomerDetailRequest" />
        </message>
        <message name="getCustomerDetailResponse">
            <part name="getCustomerDetailResponse" element="tns:getCustomerDetailResponse" />
        </message>
        <message name="getCustomerServicesRequest">
            <part name="getCustomerServicesRequest" element="tns:getCustomerServicesRequest" />
        </message>
        <message name="getCustomerServicesResponse">
            <part name="getCustomerServicesResponse" element="tns:getCustomerServicesResponse" />
        </message>
        <message name="getCustomerToBankRelationsRequest">
            <part name="getCustomerToBankRelationsRequest" element="tns:getCustomerToBankRelationsRequest" />
        </message>
        <message name="getCustomerToBankRelationsResponse">
            <part name="getCustomerToBankRelationsResponse" element="tns:getCustomerToBankRelationsResponse" />
        </message>

        <message name="getCustomerWelcomeFlagRequest">
            <part name="getCustomerWelcomeFlagRequest" element="tns:getCustomerWelcomeFlagRequest" />
        </message>
        <message name="getCustomerWelcomeFlagResponse">
            <part name="getCustomerWelcomeFlagResponse" element="tns:getCustomerWelcomeFlagResponse" />
        </message>

        <message name="searchCustomerToGeneralContractRelationsRequest">
            <part name="searchCustomerToGeneralContractRelationsRequest" element="tns:searchCustomerToGeneralContractRelationsRequest" />
        </message>
        <message name="searchCustomerToGeneralContractRelationsResponse">
            <part name="searchCustomerToGeneralContractRelationsResponse" element="tns:searchCustomerToGeneralContractRelationsResponse" />
        </message>

        <portType name="Customer360ViewPort">
            <operation name="getCustomerBasicProfile">
                <input message="tns:getCustomerBasicProfileRequest" />
                <output message="tns:getCustomerBasicProfileResponse" />
            </operation>
            <operation name="identifyCustomers">
                <input message="tns:identifyCustomersRequest" />
                <output message="tns:identifyCustomersResponse" />
            </operation>
            <operation name="getCustomerDetail">
                <input message="tns:getCustomerDetailRequest" />
                <output message="tns:getCustomerDetailResponse" />
            </operation>
            <operation name="getCustomerServices">
                <input message="tns:getCustomerServicesRequest" />
                <output message="tns:getCustomerServicesResponse" />
            </operation>
            <operation name="getCustomerToBankRelations">
                <input message="tns:getCustomerToBankRelationsRequest" />
                <output message="tns:getCustomerToBankRelationsResponse" />
            </operation>

            <operation name="getCustomerWelcomeFlag">
                <input message="tns:getCustomerWelcomeFlagRequest" />
                <output message="tns:getCustomerWelcomeFlagResponse" />
            </operation>

            <operation name="searchCustomerToGeneralContractRelations">
                <input message="tns:searchCustomerToGeneralContractRelationsRequest" />
                <output message="tns:searchCustomerToGeneralContractRelationsResponse" />
            </operation>

        </portType>
        <binding name="Customer360ViewSoap11" type="tns:Customer360ViewPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <operation name="getCustomerBasicProfile">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="identifyCustomers">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="getCustomerDetail">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="getCustomerServices">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="getCustomerToBankRelations">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

            <operation name="getCustomerWelcomeFlag">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

            <operation name="searchCustomerToGeneralContractRelations">
                <soap:operation soapAction="" />
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

        </binding>
        <service name="Customer360ViewService">
            <port binding="tns:Customer360ViewSoap11" name="Customer360ViewSoap11">
                <soap:address location="https://osb-int.banka.hci/Customer360ViewWS" />
            </port>
        </service>
    </definitions>
