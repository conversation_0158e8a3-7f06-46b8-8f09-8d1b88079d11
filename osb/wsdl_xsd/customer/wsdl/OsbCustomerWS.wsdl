<?xml version="1.0" encoding="UTF-8" ?>
    <wsdl:definitions targetNamespace="http://airbank.cz/osb/UnidentifiedCustomer" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:common="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/osb/UnidentifiedCustomer">

        <wsdl:types>
            <xsd:schema targetNamespace="http://airbank.cz/osb/UnidentifiedCustomer">
                <xsd:include schemaLocation="OsbCustomerWS.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
            </xsd:schema>
        </wsdl:types>

        <wsdl:message name="ProcessUnidentifiedCustomerRequest">
            <wsdl:part element="ProcessUnidentifiedCustomerRequest" name="ProcessUnidentifiedCustomerRequest" />
        </wsdl:message>
        <wsdl:message name="ProcessUnidentifiedCustomerResponse">
            <wsdl:part element="ProcessUnidentifiedCustomerResponse" name="ProcessUnidentifiedCustomerResponse" />
        </wsdl:message>
        <wsdl:message name="AddLightCustomerAddressRequest">
            <wsdl:part element="AddLightCustomerAddressRequest" name="AddLightCustomerAddressRequest" />
        </wsdl:message>
        <wsdl:message name="AddLightCustomerAddressResponse">
            <wsdl:part element="AddLightCustomerAddressResponse" name="AddLightCustomerAddressResponse" />
        </wsdl:message>
        <wsdl:message name="AddLightCustomerAddressFaultMessage">
            <wsdl:part element="common:CoreFaultElement" name="AddLightCustomerAddressFault"/>
        </wsdl:message>

        <wsdl:portType name="UnidentifiedCustomerPort">
            <wsdl:operation name="ProcessUnidentifiedCustomer">
                <wsdl:input message="ProcessUnidentifiedCustomerRequest" />
                <wsdl:output message="ProcessUnidentifiedCustomerResponse" />
            </wsdl:operation>
            <wsdl:operation name="AddLightCustomerAddress">
                <wsdl:input message="AddLightCustomerAddressRequest" />
                <wsdl:output message="AddLightCustomerAddressResponse" />
                <wsdl:fault message="AddLightCustomerAddressFaultMessage" name="AddLightCustomerAddressFaultMessage"/>
            </wsdl:operation>
        </wsdl:portType>

        <wsdl:binding name="UnidentifiedCustomerBinding" type="UnidentifiedCustomerPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <wsdl:operation name="ProcessUnidentifiedCustomer">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
            </wsdl:operation>
            <wsdl:operation name="AddLightCustomerAddress">
                <soap:operation soapAction="" />
                <wsdl:input>
                    <soap:body use="literal" />
                </wsdl:input>
                <wsdl:output>
                    <soap:body use="literal" />
                </wsdl:output>
                <wsdl:fault name="AddLightCustomerAddressFaultMessage">
                    <soap:fault name="AddLightCustomerAddressFaultMessage" use="literal"/>
                </wsdl:fault>
            </wsdl:operation>
        </wsdl:binding>

        <wsdl:service name="UnidentifiedCustomerWS">
            <wsdl:port binding="UnidentifiedCustomerBinding" name="UnidentifiedCustomerPort">
                <soap:address location="http://TO-BE-SPECIFIED/osb/UnidentifiedCustomerWS" />
            </wsdl:port>
        </wsdl:service>

    </wsdl:definitions>
