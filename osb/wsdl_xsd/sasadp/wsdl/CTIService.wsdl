<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions name="CTIService" targetNamespace="http://phone.cti/CTIService"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy"
                  xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://phone.cti/CTIService"
                  xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract"
                  xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
                  xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
                  xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing"
                  xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy"
                  xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <wsdl:types>
        <xsd:schema targetNamespace="http://phone.cti/CTIService">
            <xsd:include schemaLocation="../xsd/CTIService.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="callListNotification">
        <wsdl:part name="parameters" element="tns:callListNotification"/>
    </wsdl:message>
    <wsdl:message name="callListNotificationResponse">
        <wsdl:part name="parameters" element="tns:callListNotificationResponse"/>
    </wsdl:message>
    <wsdl:message name="UserSignal">
        <wsdl:part name="parameters" element="tns:UserSignal"/>
        <wsdl:part name="callGroupNameH" element="tns:CallGroupNameHeader"/>
    </wsdl:message>
    <wsdl:message name="UserSignalResponse">
        <wsdl:part name="parameters" element="tns:UserSignalResponse"/>
    </wsdl:message>
    <wsdl:message name="callRemove">
        <wsdl:part name="parameters" element="tns:callRemove"/>
    </wsdl:message>
    <wsdl:message name="callRemoveResponse">
        <wsdl:part name="parameters" element="tns:callRemoveResponse"/>
    </wsdl:message>

    <wsdl:portType msc:usingSession="false" name="ICTIService">
        <wsdl:operation name="callListNotification">
            <wsdl:input wsaw:Action="http://phone.cti/CTIService/callListNotification"
                        name="callListNotification" message="tns:callListNotification"/>
            <wsdl:output wsaw:Action="http://phone.cti/CTIService/callListNotificationResponse"
                         name="callListNotificationResponse" message="tns:callListNotificationResponse"/>
        </wsdl:operation>
        <wsdl:operation name="UserSignal">
            <wsdl:input wsaw:Action="http://phone.cti/CTIService/UserSignal" name="UserSignal" message="tns:UserSignal"/>
            <wsdl:output wsaw:Action="http://phone.cti/CTIService/UserSignalResponse" name="UserSignalResponse" message="tns:UserSignalResponse"/>
        </wsdl:operation>
        <wsdl:operation name="callRemove">
            <wsdl:input wsaw:Action="http://phone.cti/CTIService/callRemove" name="callRemove" message="tns:callRemove"/>
            <wsdl:output wsaw:Action="http://phone.cti/CTIService/callRemoveResponse" name="callRemoveResponse" message="tns:callRemoveResponse"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CTIService" type="tns:ICTIService">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="callListNotification">
            <soap:operation soapAction="http://phone.cti/CTIService/callListNotification" style="document"/>
            <wsdl:input name="callListNotification">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="callListNotificationResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UserSignal">
            <soap:operation soapAction="http://phone.cti/CTIService/UserSignal" style="document"/>
            <wsdl:input name="UserSignal">
                <soap:header message="tns:UserSignal" part="callGroupNameH" use="literal"/>
                <soap:body use="literal" parts="parameters"/>
            </wsdl:input>
            <wsdl:output name="UserSignalResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="callRemove">
            <soap:operation soapAction="http://phone.cti/CTIService/callRemove" style="document"/>
            <wsdl:input name="callRemove">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="callRemoveResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CTIService">
        <wsdl:port name="CTIService" binding="tns:CTIService">
            <soap:address location="https://127.0.0.1:8000/CTIService"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
