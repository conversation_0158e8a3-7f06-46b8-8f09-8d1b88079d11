<?xml version="1.0" encoding="UTF-8" ?>
    <schema targetNamespace="http://osb.airbank.cz/document/notification" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.airbank.cz/document/notification">
        <element name="documentCreatedRequest">
            <annotation>
                <documentation>Notification about creating document.</documentation>
            </annotation>
            <complexType>
                <sequence>
                    <element name="notifiedSystem" type="tns:SystemCodeType" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>System that receives the notification. ALL for all of supported systems.</documentation>
                        </annotation>
                    </element>
                    <element name="documentIdent" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>Identification of created document</documentation>
                        </annotation>
                    </element>
                    <element name="status" type="tns:CreationStatusType" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>creation status of document OK/ERROR</documentation>
                        </annotation>
                    </element>
                </sequence>
            </complexType>
        </element>
        <element name="documentCreatedResponse" />
        <simpleType name="SystemCodeType">
            <restriction base="string">
                <enumeration value="ALL">
                    <annotation>
                        <documentation>All of supported systems</documentation>
                    </annotation>
                </enumeration>
                <enumeration value="INS">
                    <annotation>
                        <documentation>Insurance application</documentation>
                    </annotation>
                </enumeration>
                <enumeration value="BINF">
                    <annotation>
                        <documentation>BINF application</documentation>
                    </annotation>
                </enumeration>
                <enumeration value="TOPAS">
                    <annotation>
                        <documentation>TOPAS application</documentation>
                    </annotation>
                </enumeration>
                <enumeration value="AMS">
                    <annotation>
                        <documentation>AMS application</documentation>
                    </annotation>
                </enumeration>
            </restriction>
        </simpleType>
        <simpleType name="CreationStatusType">
            <restriction base="string">
                <enumeration value="OK">
                    <annotation>
                        <documentation>Document was successfully created</documentation>
                    </annotation>
                </enumeration>
                <enumeration value="ERROR">
                    <annotation>
                        <documentation>Document was not created</documentation>
                    </annotation>
                </enumeration>
            </restriction>
        </simpleType>
    </schema>
