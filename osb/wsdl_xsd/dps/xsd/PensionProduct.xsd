<?xml version = '1.0' encoding = 'UTF-8'?>
    <schema xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.airbank.cz/dps/pensionProduct" targetNamespace="http://osb.airbank.cz/dps/pensionProduct" elementFormDefault="qualified">

        <simpleType name="GetCodeTableType">
            <annotation>
                <documentation>Type of code table. Values: state, gender</documentation>
            </annotation>
            <restriction base="string">
                <enumeration value="state" />
                <enumeration value="gender" />
            </restriction>
        </simpleType>

        <complexType name="PersonalIdentificationNumber">
            <sequence>
                <element name="personalIdentificationNumber" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="ContractNumber">
            <sequence>
                <element name="contractNumber" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="CodeTableItem">
            <sequence>
                <element name="code" type="string" minOccurs="0" />
                <element name="name" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="CodeTable">
            <sequence>
                <element name="item" type="tns:CodeTableItem" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="Contract">
            <sequence>
                <element name="contractNumber" type="string" />
                <element name="dateOfSignature" type="dateTime" />
                <element name="contributionPerMonth" type="int" />
                <element name="investQuestionScore" type="int" minOccurs="0" />
                <element name="investQuestionRefused" type="boolean" />
                <element name="client" type="tns:Client" />
                <element name="beneficiaries" type="tns:Beneficiary" minOccurs="0" maxOccurs="unbounded" />
                <element name="investmentStrategies" type="tns:InvestmentStrategy" maxOccurs="unbounded" />
                <element name="attachment" type="tns:Attachment" />
            </sequence>
        </complexType>

        <complexType name="Address">
            <sequence>
                <element name="street" type="string" minOccurs="0" />
                <element name="streetSecondLine" type="string" minOccurs="0" />
                <element name="descriptiveNumber" type="string" minOccurs="0" />
                <element name="zipCode" type="string" minOccurs="0" />
                <element name="town" type="string" minOccurs="0" />
                <element name="state" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="Client">
            <sequence>
                <element name="identityDocNumber" type="string" minOccurs="0" />
                <element name="identityDocIssuedBy" type="string" minOccurs="0" />
                <element name="identityDocValidUntil" type="dateTime" minOccurs="0" />
                <element name="surname" type="string" minOccurs="0" />
                <element name="firstName" type="string" minOccurs="0" />
                <element name="titleBefore" type="string" minOccurs="0" />
                <element name="titleAfter" type="string" minOccurs="0" />
                <element name="gender" type="string" minOccurs="0" />
                <element name="dateOfBirth" type="dateTime" minOccurs="0" />
                <element name="placeOfBirth" type="string" minOccurs="0" />
                <element name="personalIdentificationNumber" type="string" minOccurs="0" />
                <element name="income" type="string" minOccurs="0" />
                <element name="sourceOfIncome" type="string" minOccurs="0" />
                <element name="sourceOfIncomeTypeOfBusiness" type="string" minOccurs="0" />
                <element name="sourceOfAssets" type="string" minOccurs="0" />
                <element name="taxDomicile" type="string" minOccurs="0" />
                <element name="vatId" type="string" minOccurs="0" />
                <element name="address" type="tns:Address" minOccurs="0" />
                <element name="contact" type="tns:Contact" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="UpdateClient">
            <sequence>
                <element name="identityDocNumber" type="string" minOccurs="0" />
                <element name="identityDocIssuedBy" type="string" minOccurs="0" />
                <element name="identityDocValidUntil" type="dateTime" minOccurs="0" />
                <element name="surname" type="string" minOccurs="0" />
                <element name="firstName" type="string" minOccurs="0" />
                <element name="titleBefore" type="string" minOccurs="0" />
                <element name="titleAfter" type="string" minOccurs="0" />
                <element name="dateOfBirth" type="dateTime" minOccurs="0" />
                <element name="placeOfBirth" type="string" minOccurs="0" />
                <element name="sourceOfIncome" type="string" minOccurs="0" />
                <element name="income" type="string" minOccurs="0" />
                <element name="sourceOfIncomeTypeOfBusiness" type="string" minOccurs="0" />
                <element name="sourceOfAssets" type="string" minOccurs="0" />
                <element name="address" type="tns:Address" minOccurs="0" />
                <element name="contact" type="tns:Contact" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="UpdateContract">
            <sequence>
                <element name="contractNumber" type="string" />
                <element name="client" type="tns:UpdateClient" />
            </sequence>
        </complexType>

        <complexType name="Contact">
            <sequence>
                <element name="phoneNumber" type="string" minOccurs="0" />
                <element name="phoneNumberTwo" type="string" minOccurs="0" />
                <element name="email" type="string" minOccurs="0" />
                <element name="address" type="tns:Address" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="Beneficiary">
            <sequence>
                <element name="id" type="int" minOccurs="0" />
                <element name="firstName" type="string" minOccurs="0" />
                <element name="surname" type="string" minOccurs="0" />
                <element name="gender" type="string" minOccurs="0" />
                <element name="dateOfBirth" type="dateTime" minOccurs="0" />
                <element name="percentage" type="int" minOccurs="0" />
                <element name="address" type="tns:Address" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="InvestmentStrategy">
            <sequence>
                <element name="investmentFund" type="string" minOccurs="0" />
                <element name="depositPercentage" type="int" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="Attachment">
            <sequence>
                <element name="authorizationId" type="string" />
                <element name="documentUuid" type="string" />
            </sequence>
        </complexType>

        <complexType name="EffectiveDate">
            <sequence>
                <element name="effectiveDate" type="dateTime" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="Answer">
            <sequence>
                <element name="answerId" type="long" minOccurs="0" />
                <element name="content" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="Question">
            <sequence>
                <element name="questionId" type="long" minOccurs="0" />
                <element name="content" type="string" minOccurs="0" />
                <element name="minNumberAnswers" type="long" minOccurs="0" />
                <element name="maxNumberAnswers" type="long" minOccurs="0" />
                <element name="answers" type="tns:Answer" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="QuestionSet">
            <sequence>
                <element name="questionSet" type="tns:Question" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="TargetEntity">
            <sequence>
                <element name="id" type="string" minOccurs="0" />
                <element name="entityType" type="string" minOccurs="0" />
                <element name="parentEntity" type="tns:TargetEntity" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="ValidationResultItem">
            <sequence>
                <element name="code" type="string" minOccurs="0" />
                <element name="severity" type="string" minOccurs="0" />
                <element name="message" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="ValidationResult">
            <sequence>
                <element name="propertyName" type="string" minOccurs="0" />
                <element name="targetEntity" type="tns:TargetEntity" minOccurs="0" />
                <element name="validationResults" type="tns:ValidationResultItem" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="ValidationResults">
            <sequence>
                <element name="validationError" type="tns:ValidationResult" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="Questionnaire">
            <sequence>
                <element name="questionnaireField" type="tns:QuestionnaireField" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="QuestionnaireField">
            <sequence>
                <element name="questionId" type="long" minOccurs="0" />
                <element name="answerId" type="long" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="InvestmentFund">
            <sequence>
                <element name="fundType" type="string" minOccurs="0" />
                <element name="fundName" type="string" minOccurs="0" />
                <element name="maxDepositPercentage" type="decimal" minOccurs="0" />
                <element name="recommendedDepositPercentage" type="decimal" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="Strategy">
            <sequence>
                <element name="strategy" type="string" minOccurs="0" />
                <element name="strategyName" type="string" minOccurs="0" />
                <element name="investmentFunds" type="tns:InvestmentFund" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="QuestionnaireResult">
            <sequence>
                <element name="resultScore" type="decimal" minOccurs="0" />
                <element name="resultStrategy" type="string" minOccurs="0" />
                <element name="strategies" type="tns:Strategy" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="Document">
            <sequence>
                <element name="personalIdentificationNumber" type="string" />
                <element name="contractNumber" type="string" />
                <element name="documentType" type="string" />
                <element name="name" type="string" />
                <element name="content" type="string" />
            </sequence>
        </complexType>

        <complexType name="MeetingRecord">
            <sequence>
                <element name="personalIdentificationNumber" type="string" />
                <element name="contractNumber" type="string" />
                <element name="documentUuid" type="string" />
            </sequence>
        </complexType>

        <complexType name="ErrorsType">
            <sequence>
                <element name="Code" type="int" />
                <element name="TimeStamp" type="dateTime" />
                <element name="Id" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="ErrorList">
            <sequence>
                <element name="errorItem" type="tns:ErrorItem" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="ErrorItem">
            <sequence>
                <element name="Message" type="string" minOccurs="0" />
                <element name="errorCode" type="string" minOccurs="0" />
                <element name="propertyName" type="string" minOccurs="0" />
                <element name="errorMessage" type="string" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="SmlouvaType">
            <sequence minOccurs="1" maxOccurs="1">
                <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                <element name="CisloSmlouvy" nillable="true">
                    <annotation>
                        <documentation xml:lang="en">
                            Contract number. Example value: 0200000648
                        </documentation>
                    </annotation>
                    <simpleType>
                        <restriction base="string">
                            <pattern value="[0-9]{0,10}" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="Kampan" type="string" nillable="true" />
                <element name="RefId" type="string" nillable="true" />
                <element name="IdMA" type="int" nillable="true" />
                <element name="ServisRefId" type="string" nillable="true" />
                <element name="ServisIdMA" type="string" nillable="true" />
                <element name="VznikPrevodem" type="boolean" nillable="true" />
                <element name="PredPsFondIco" nillable="true">
                    <simpleType>
                        <restriction base="string">
                            <pattern value="[0-9]{0,8}" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="PredPsCisloSmlouvy" nillable="true">
                    <simpleType>
                        <restriction base="string">
                            <pattern value="[0-9]{0,10}" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="DatumPodpisuSml" type="date" nillable="true" />
                <element name="PodpisKdo" type="string" nillable="true" />
                <element name="DatumUcinnostiSml" type="date" nillable="true" />
                <element name="VerzePodminek" type="int" nillable="true" />
                <element name="MesVkladKlient" nillable="true">
                    <simpleType>
                        <restriction base="decimal">
                            <minInclusive value="0" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="IntervalPlaceni" type="int" nillable="true" />
                <element name="ZdaVkladZamest" type="boolean" nillable="true" />
                <element name="DanOptimum" type="boolean" nillable="true" />
                <element name="RovnyPodil" type="boolean" nillable="true" />
                <element name="ElRocniVypis" type="boolean" nillable="true" />
                <element name="ElDanPotvrzeni" type="boolean" nillable="true" />
                <element name="SouhlasMarketing" type="boolean" nillable="true" />
                <element name="InvDotaznikSkore" type="int" nillable="true" />
                <element name="InvDotaznikNe" type="string" nillable="true" />
                <element name="Klient" minOccurs="1" maxOccurs="1">
                    <complexType>
                        <sequence>
                            <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                            <element name="DokladTyp" type="tns:DokladType" nillable="true" />
                            <element name="DokladCislo" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="DokladVydal" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="DokladPlatnostDo" type="date" nillable="true" />
                            <element name="Identifikovan" type="boolean" nillable="true" />
                            <element name="IdentZpusob" type="string" nillable="true" />
                            <element name="IdentCisloUctu" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="IdentBanka" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="IdentIBAN" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="IdentBIC" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Prijmeni" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Jmeno" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="TitulPred" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="TitulPo" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="25" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Pohlavi" type="tns:PohlaviType" nillable="true" />
                            <element name="DatumNarozeni" type="date" nillable="true" />
                            <element name="MistoNarozeni" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="StatObcanstvi" type="tns:StatType" nillable="true" />
                            <element name="TypKlienta" type="string" nillable="true" />
                            <element name="RodneCislo" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <pattern value="(\d{9,10})" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Pep" type="boolean" nillable="true" />
                            <element name="UsTaxResident" type="boolean" nillable="true" />
                            <element name="GreenCard" type="boolean" nillable="true" />
                            <element name="Adresa" type="tns:AdresaType" minOccurs="1" maxOccurs="1" />
                            <element name="Kontakt" minOccurs="0" maxOccurs="1">
                                <complexType>
                                    <sequence>
                                        <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                                        <element name="Telefon" type="string" nillable="true" />
                                        <element name="Telefon2" type="string" nillable="true" />
                                        <element name="email" type="tns:EmailType" nillable="true" />
                                        <element name="KontaktRadek2" type="string" nillable="true" />
                                        <element name="Adresa" type="tns:AdresaType" minOccurs="0" maxOccurs="1" />
                                    </sequence>
                                </complexType>
                            </element>
                        </sequence>
                    </complexType>
                </element>
                <element name="Zamestnavatel" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation xml:lang="en">
                            Employer
                        </documentation>
                    </annotation>
                    <complexType>
                        <sequence>
                            <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                            <element name="Nazev" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Ico" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <pattern value="[0-9]{0,8}" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Adresa" type="tns:AdresaType" minOccurs="1" maxOccurs="1" />
                        </sequence>
                    </complexType>
                </element>
                <element name="UrcenaOsoba" minOccurs="0" maxOccurs="unbounded">
                    <complexType>
                        <sequence>
                            <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                            <element name="Prijmeni" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Jmeno" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="DatumNarozeni" type="date" nillable="true" />
                            <element name="Vztah" type="string" nillable="true" />
                            <element name="Podil" nillable="true">
                                <simpleType>
                                    <restriction base="integer">
                                        <minInclusive value="0" />
                                        <maxInclusive value="100" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Pohlavi" type="tns:PohlaviType" nillable="true" />
                            <element name="Adresa" type="tns:AdresaType" minOccurs="0" maxOccurs="1" />
                        </sequence>
                    </complexType>
                </element>
                <element name="Zastupce" minOccurs="0" maxOccurs="unbounded">
                    <complexType>
                        <sequence>
                            <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                            <element name="DokladTyp" type="tns:DokladType" nillable="true" />
                            <element name="DokladCislo" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="DokladVydal" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="DokladPlatnostDo" type="date" nillable="true" />
                            <element name="Identifikovan" type="boolean" nillable="true" />
                            <element name="Prijmeni" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Representative Surname Example value: Kocour
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Jmeno" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Representative Name Example value: Daniel
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="TitulPred" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Title before name Example value: Ing.
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="TitulPo" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Title after name Example value: Csc.
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Pohlavi" type="tns:PohlaviType" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Sex Example value: M
                                    </documentation>
                                </annotation>
                            </element>
                            <element name="DatumNarozeni" type="date" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Birth date Example value: 2000-09-25
                                    </documentation>
                                </annotation>
                            </element>
                            <element name="MistoNarozeni" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Birth Place Example value: Prague
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="StatObcanstvi" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Citizen Ship Example value: CZE
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="RodneCislo" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Personal identification number Example value: 887544668877
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <pattern value="(\d{9,10})" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="ZastPlatnostOd" type="date" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Representative's Authorization is valid from this Date Example value: 2000-09-25
                                    </documentation>
                                </annotation>
                            </element>
                            <element name="ZastPlatnostDo" type="date" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Representative's Authorization is valid until this Date Example value: 2000-09-25
                                    </documentation>
                                </annotation>
                            </element>
                            <element name="TypZastupce" nillable="true">
                                <simpleType>
                                    <restriction base="string" />
                                </simpleType>
                            </element>
                            <element name="Adresa" type="tns:AdresaType" minOccurs="1" maxOccurs="1">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Address of Representative
                                    </documentation>
                                </annotation>
                            </element>
                            <element name="Kontakt" minOccurs="0" maxOccurs="1">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Representative Contact
                                    </documentation>
                                </annotation>
                                <complexType>
                                    <sequence>
                                        <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                                        <element name="Telefon" type="string" nillable="true">
                                            <annotation>
                                                <documentation xml:lang="en">
                                                    Phone Example value: 724333444
                                                </documentation>
                                            </annotation>
                                        </element>
                                        <element name="Telefon2" type="string" nillable="true">
                                            <annotation>
                                                <documentation xml:lang="en">
                                                    Phone2 Example value: 724123444
                                                </documentation>
                                            </annotation>
                                        </element>
                                        <element name="email" type="tns:EmailType" nillable="true">
                                            <annotation>
                                                <documentation xml:lang="en">
                                                    Email Example value: <EMAIL>
                                                </documentation>
                                            </annotation>
                                        </element>
                                        <element name="KontaktRadek2" type="string" nillable="true">
                                            <annotation>
                                                <documentation xml:lang="en">
                                                    Example value: u paní Z. Vomáčkové
                                                </documentation>
                                            </annotation>
                                        </element>
                                        <element name="Adresa" type="tns:AdresaType" minOccurs="0" maxOccurs="1">
                                            <annotation>
                                                <documentation xml:lang="en">
                                                    Contact Address
                                                </documentation>
                                            </annotation>
                                        </element>
                                    </sequence>
                                </complexType>
                            </element>
                        </sequence>
                    </complexType>
                </element>
                <element name="InvStrategie" minOccurs="1" maxOccurs="unbounded">
                    <complexType>
                        <sequence>
                            <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                            <element name="Id" nillable="true">
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="Nazev" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Name of investment fund. The fund is identified by ID, so this is just informational field Example value: "Povinný konzervativní fond NN Penzijní společnosti, a.s."
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="string">
                                        <minLength value="0" />
                                        <maxLength value="999" />
                                    </restriction>
                                </simpleType>
                            </element>
                            <element name="PodilVkladu" nillable="true">
                                <annotation>
                                    <documentation xml:lang="en">
                                        Deposit Share. Total sum of all shares must be 100 Example value: 20
                                    </documentation>
                                </annotation>
                                <simpleType>
                                    <restriction base="integer">
                                        <minInclusive value="0" />
                                        <maxInclusive value="100" />
                                    </restriction>
                                </simpleType>
                            </element>
                        </sequence>
                    </complexType>
                </element>
            </sequence>
        </complexType>

        <complexType name="AdresaType">
            <sequence>
                <element name="GrpId" type="tns:GrpIdType" nillable="false" />
                <element name="Ulice" nillable="true">
                    <simpleType>
                        <restriction base="string">
                            <minLength value="0" />
                            <maxLength value="999" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="CisloPopisne" nillable="true">
                    <simpleType>
                        <restriction base="string">
                            <minLength value="0" />
                            <maxLength value="999" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="PSC" nillable="true">
                    <simpleType>
                        <restriction base="string">
                            <minLength value="0" />
                            <maxLength value="999" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="Obec" nillable="true">
                    <simpleType>
                        <restriction base="string">
                            <minLength value="0" />
                            <maxLength value="999" />
                        </restriction>
                    </simpleType>
                </element>
                <element name="Stat" type="tns:StatType" nillable="true" />
            </sequence>
        </complexType>
        <simpleType name="EmailType">
            <restriction base="string">
                <minLength value="0" />
                <maxLength value="999" />
            </restriction>
        </simpleType>
        <simpleType name="StatType">
            <annotation>
                <documentation xml:lang="en">
                    2 digit country code as specified in ISO 3166 alpha-2 or 3 digit country code as specified in ISO 3166 alpha-3
                </documentation>
            </annotation>
            <restriction base="string">
                <minLength value="0" />
                <maxLength value="999" />
            </restriction>
        </simpleType>
        <simpleType name="DokladType">
            <restriction base="string" />
        </simpleType>
        <simpleType name="PohlaviType">
            <restriction base="string" />
        </simpleType>
        <simpleType name="GrpIdType">
            <restriction base="int" />
        </simpleType>

        <complexType name="PenezniUcetType">
            <sequence>
                <element name="PrispevkyKlienta" type="decimal" minOccurs="0" />
                <element name="PrispevkyZamestnavatele" type="decimal" minOccurs="0" />
                <element name="StatniPrispevky" type="decimal" minOccurs="0" />
                <element name="VynosyCiziFondy" type="decimal" minOccurs="0" />
                <element name="PrispevkyCelkem" type="decimal" minOccurs="0" />
                <element name="ProbihaInvestovani" type="decimal" minOccurs="0" />
                <element name="StavKeDni" type="date" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="MajetekFondyType">
            <sequence>
                <element name="Id" type="int" minOccurs="0" />
                <element name="Nazev" type="string" minOccurs="0" />
                <element name="NazevZkr" type="string" minOccurs="0" />
                <element name="DatumPJ" type="date" minOccurs="0" nillable="true" />
                <element name="KurzPJ" type="decimal" minOccurs="0" />
                <element name="PocetPJ" type="decimal" minOccurs="0" />
                <element name="HodnotaZustatku" type="decimal" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="InvStrategieType">
            <sequence>
                <element name="Id" type="int" minOccurs="0" />
                <element name="Nazev" type="string" minOccurs="0" />
                <element name="NazevZkr" type="string" minOccurs="0" />
                <element name="PodilVkladu" type="int" minOccurs="0" />
                <element name="StavKeDni" type="date" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="DobaSporeniType">
            <sequence>
                <element name="OkMesice" type="int" />
                <element name="OkMesicePrevedene" type="int" />
                <element name="StavKeDni" type="date" minOccurs="0" />
            </sequence>
        </complexType>

        <complexType name="InformaceOSmlouveType">
            <sequence>
                <element name="FazeSmlouvy" type="int" />
            </sequence>
        </complexType>

        <complexType name="ObdobiVypisuType">
            <sequence>
                <element name="DatumOd" type="date" />
                <element name="DatumDo" type="date" />
                <element name="NextIncrementFrom" type="string" />
            </sequence>
        </complexType>

        <complexType name="SmlouvaVypisuType">
            <sequence>
                <element name="CisloSmlouvy" type="string" />
                <element name="FazeSmlouvy" type="string" />
                <element name="SjednanaVysePrispevku" type="decimal" />
                <element name="DatumPodpisuSmlouvy" type="date" />
                <element name="DatumUcinnostiSmlouvy" type="date" />
                <element name="DatumUkonceniSporeni" type="date" nillable="true" />
                <element name="DatumZruseni" type="date" nillable="true" />
                <element name="Transakce" type="tns:TransakceType" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="TransakceType">
            <sequence>
                <element name="TranId" type="long" />
                <element name="SubTranId" type="long" />
                <element name="Code" type="string" />
                <element name="TypTransakce" type="tns:TypTransakceType" />
                <element name="Popis" type="string" />
                <element name="DatumTransakce" type="date" />
                <element name="TrCastka" type="decimal" />
                <element name="StrukturaTransakce" type="tns:StrukturaTransakceType" minOccurs="0" maxOccurs="unbounded" />
            </sequence>
        </complexType>

        <complexType name="StrukturaTransakceType">
            <sequence>
                <element name="Id" type="string" />
                <element name="Nazev" type="string" />
                <element name="NazevZkr" type="string" />
                <element name="DatumPJ" type="date" />
                <element name="KurzPJ" type="decimal" />
                <element name="PocetPJ" type="decimal" />
                <element name="Castka" type="decimal" />
            </sequence>
        </complexType>

        <simpleType name="TypTransakceType">
            <restriction base="string">
                <enumeration value="K" />
                <enumeration value="Z" />
                <enumeration value="S" />
            </restriction>
        </simpleType>
    </schema>
