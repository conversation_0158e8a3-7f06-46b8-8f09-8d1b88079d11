<?xml version= '1.0' encoding= 'UTF-8' ?>
<definitions name="DetectorAPI"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    targetNamespace="http://airbank.cz/osb/disgram/detector"
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/osb/disgram/detector"
    xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">

  <documentation>Integration APIs for systems providing information for detection of offer</documentation>
  
  <plnk:partnerLinkType name="DetectorAPI">
      <plnk:role name="DetectorAPIProvider" portType="Detector_ptt"/>
  </plnk:partnerLinkType>
  
  <types>
    <xs:schema>
      <xs:import schemaLocation="../xsd/Detector.xsd" namespace="http://airbank.cz/osb/disgram/detector"/>
    </xs:schema>
  </types>

  <message name="receiveBatchRequest">
    <part name="receiveBatchRequest" element="tns:receiveBatchRequest"/>
  </message>
  <message name="receiveBatchResponse">
    <part name="receiveBatchResponse" element="tns:receiveBatchResponse"/>
  </message>
  <message name="receiveBatchFault">
    <part name="receiveBatchFault" element="tns:receiveBatchFault"/>
  </message>
  
  <message name="transactionsRequest">
    <part name="transactionsRequest" element="tns:transactionsRequest"/>
  </message>
  <message name="transactionsResponse">
    <part name="transactionsResponse" element="tns:transactionsResponse"/>
  </message>
  <message name="transactionsFault">
    <part name="transactionsFault" element="tns:transactionsFault"/>
  </message>

  <portType name="Detector_ptt">
    <operation name="receiveBatch">
      <input message="tns:receiveBatchRequest"/>
      <output message="tns:receiveBatchResponse"/>
      <fault name="ErrorMessage" message="tns:receiveBatchFault"/>
    </operation>
    <operation name="transactions">
      <input message="tns:transactionsRequest"/>
      <output message="tns:transactionsResponse"/>
      <fault name="ErrorMessage" message="tns:transactionsFault"/>
    </operation>
  </portType>

  <binding name="Detector_ptt-binding" type="tns:Detector_ptt">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="receiveBatch">
      <soap:operation soapAction="receiveBatch"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="transactions">
      <soap:operation soapAction="transactions"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>

</definitions>
