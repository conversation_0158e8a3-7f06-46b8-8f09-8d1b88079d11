<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://osb.banka.hci/SMS/SMSMangementWS" xmlns="http://osb.banka.hci/SMS/SMSMangementWS" xmlns:Q1="http://osb.banka.hci/SMS/SMSMangementWS/XSD" xmlns:xs="http://www.w3.org/2001/XMLSchema">

	<xs:import schemaLocation="../xsd/SMSTypesTO.xsd" namespace="http://osb.banka.hci/SMS/SMSMangementWS/XSD"/>

	<xs:element name="sendSMSToClientRequest">
		<xs:annotation>
			<xs:documentation> Method used for sending SMS to the clients primary phone
				number. C<PERSON> is identified by his/her CUID.
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SMSRequestItem" type="Q1:SMSToClientInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToClientResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SMSResponse" type="Q1:result"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToClientFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fault" type="Q1:faultInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToMultipleClientsRequest">
		<xs:annotation>
			<xs:documentation> Method used for sending SMS to multiple clients
				All clients are identified by his/her CUID. There is a cap for max. number of SMS per
				request.
				The cap depends on SMS gateway performance and needs to be configured.
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element maxOccurs="unbounded" name="SMSRequestItem" type="Q1:SMSToClientInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="sendSMSToMultipleClientsResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SMSResponse" type="Q1:result"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToMultipleClientsFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fault" type="Q1:faultInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToPhoneNumberRequest">
		<xs:annotation>
			<xs:documentation> Method used for sending SMS to a concrete
				phone number.
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SMSRequestItem" type="Q1:SMSToPhoneNumberInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="sendSMSToPhoneNumberResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SMSResponse" type="Q1:result"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToPhoneNumberFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fault" type="Q1:faultInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToMultiplePhoneNumbersRequest">
		<xs:annotation>
			<xs:documentation> Method used for sending SMS to multiple concrete
				phone numbers.There is a cap for max. number of SMS per request.
				The cap depends on SMS
				gateway performance and needs to be configured.
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element maxOccurs="unbounded" name="SMSRequestItem" type="Q1:SMSToPhoneNumberInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="sendSMSToMultiplePhoneNumbersResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="SMSResponse" type="Q1:result"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="sendSMSToMultiplePhoneNumbersFault">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fault" type="Q1:faultInfo"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

</xs:schema>
