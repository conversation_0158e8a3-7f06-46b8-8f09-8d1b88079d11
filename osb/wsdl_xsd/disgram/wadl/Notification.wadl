<application
    xmlns="http://wadl.dev.java.net/2009/02"
    xmlns:soa="http://www.oracle.com/soa/rest"
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/notification/ws/notificationWS">
   <doc title="DiscountProgramNotification">DiscountProgramNotification</doc>
   <grammars>
     <xs:schema>
       <xs:import schemaLocation="../../../OSB/Resources/push-server/wsdl/NotificationWS.xsd" namespace="http://airbank.cz/notification/ws/notificationWS"/>
     </xs:schema>
   </grammars>
   <resources>
      <resource path="/sendDiscountProgramRedemptionNotification">
         <method name="POST" soa:wsdlOperation="sendDiscountProgramRedemptionNotification">
            <request>
               <representation mediaType="application/json" element="tns:sendDiscountProgramRedemptionNotificationRequest"/>
            </request>
            <response status="200 202">
               <representation mediaType="application/json" element="tns:sendDiscountProgramRedemptionNotificationResponse"/>
            </response>
         </method>
      </resource>
   </resources>
</application>