<?xml version = '1.0' encoding = 'UTF-8'?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/openapi/accessManagement1" targetNamespace="http://airbank.cz/osb/openapi/accessManagement1" elementFormDefault="qualified" xmlns:nxsd="http://xmlns.oracle.com/pcbpel/nxsd" nxsd:version="JSON" nxsd:jsonTopLevelArray="true" nxsd:encoding="UTF-8">
   <xsd:element name="getPairedAppsResponse">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element name="pairedApp" maxOccurs="unbounded">
               <xsd:complexType>
                  <xsd:sequence>
                     <xsd:element name="scope" minOccurs="0"/>
                     <xsd:element name="redirect_uri" type="xsd:string"/>
                     <xsd:element name="created_at" type="xsd:integer"/>
                     <xsd:element name="app_name" type="xsd:string"/>
                     <xsd:element name="token_id" type="xsd:string"/>
                     <xsd:element name="consumer_id" type="xsd:string"/>
                  </xsd:sequence>
               </xsd:complexType>
            </xsd:element>
         </xsd:sequence>
      </xsd:complexType>
   </xsd:element>
<xsd:annotation xmlns="">
      <xsd:appinfo>NXSDSAMPLE=</xsd:appinfo>
      <xsd:appinfo>USEHEADER=false</xsd:appinfo>
   </xsd:annotation>
</xsd:schema>