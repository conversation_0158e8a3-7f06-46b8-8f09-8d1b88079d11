<?xml version= '1.0' encoding= 'UTF-8' ?>
<wsdl:definitions
     name="RestReference"
     targetNamespace="http://xmlns.oracle.com/OSB-rewrite/DevPortal/RestReference"
     xmlns:tns="http://xmlns.oracle.com/OSB-rewrite/DevPortal/RestReference"
     xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype"
     xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
     xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
     xmlns:inp3="http://airbank.cz/osb/openapi/accessManagement"
    >
    <plnk:partnerLinkType name="RestReference">
        <plnk:role name="RestReferenceProvider" portType="tns:RestReference_ptt"/>
    </plnk:partnerLinkType>
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://airbank.cz/osb/openapi/accessManagement" schemaLocation="commonOpenApiApplications.xsd"/>
        </xsd:schema>
    </wsdl:types>
    
    <wsdl:message name="getProductApprovedApplications_inputMessage">
        <wsdl:part name="request" element="inp3:getProductApprovedApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="getProductApprovedApplications_outputMessage">
        <wsdl:part name="reply" element="inp3:getProductApprovedApplicationsResponse"/>
    </wsdl:message>
    
    <wsdl:portType name="RestReference_ptt">
        <wsdl:operation name="getProductApprovedApplications">
            <wsdl:input message="tns:getProductApprovedApplications_inputMessage"/>
            <wsdl:output message="tns:getProductApprovedApplications_outputMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    
    <wsdl:binding name="RestReference_ptt-binding" type="tns:RestReference_ptt">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getProductApprovedApplications">
            <soap:operation soapAction="getProductApprovedApplications"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
</wsdl:definitions>
