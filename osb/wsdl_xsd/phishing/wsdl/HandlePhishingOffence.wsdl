<?xml version="1.0" encoding="UTF-8" ?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://osb.abank.cz/osb/ws/HandlePhishingOffence" targetNamespace="http://osb.abank.cz/osb/ws/HandlePhishingOffence">

    <wsdl:types>
        <xs:schema targetNamespace="http://osb.abank.cz/osb/ws/HandlePhishingOffence">
            <xs:include schemaLocation="../xsd/HandlePhishingOffenceWS.xsd" />
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="HandlePhishingOffenceRequest">
        <wsdl:part element="HandlePhishingOffenceRequest" name="HandlePhishingOffenceRequest" />
    </wsdl:message>
    <wsdl:message name="HandlePhishingOffenceResponse">
        <wsdl:part element="HandlePhishingOffenceResponse" name="HandlePhishingOffenceResponse" />
    </wsdl:message>

    <wsdl:message name="VerifyPhishingOffenceRequest">
        <wsdl:part element="VerifyPhishingOffenceRequest" name="VerifyPhishingOffenceRequest" />
    </wsdl:message>
    <wsdl:message name="VerifyPhishingOffenceResponse">
        <wsdl:part element="VerifyPhishingOffenceResponse" name="VerifyPhishingOffenceResponse" />
    </wsdl:message>

    <wsdl:message name="GetIpAddressEvaluationResponse">
        <wsdl:part element="GetIpAddressEvaluationResponse" name="GetIpAddressEvaluationResponse" />
    </wsdl:message>
    <wsdl:message name="GetIpAddressEvaluationRequest">
        <wsdl:part element="GetIpAddressEvaluationRequest" name="GetIpAddressEvaluationRequest" />
    </wsdl:message>

    <wsdl:portType name="HandlePhishingOffence">
        <wsdl:operation name="HandlePhishingOffence">
            <xs:documentation>Rozhraní webové operace pro uložení důvodu a výsledku reakce na phishingový útok (PhishingOffence)</xs:documentation>
            <wsdl:input message="HandlePhishingOffenceRequest" />
            <wsdl:output message="HandlePhishingOffenceResponse" />
        </wsdl:operation>

        <wsdl:operation name="VerifyPhishingOffence">
			<xs:documentation>Rozhraní webové operace pro ověření podezření na phishingový útok</xs:documentation>
            <wsdl:input message="VerifyPhishingOffenceRequest" />
            <wsdl:output message="VerifyPhishingOffenceResponse" />
        </wsdl:operation>

        <wsdl:operation name="GetIpAddressEvaluation">
            <wsdl:input message="GetIpAddressEvaluationRequest" />
            <wsdl:output message="GetIpAddressEvaluationResponse" />
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="HandlePhishingOffenceBinding" type="HandlePhishingOffence">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="HandlePhishingOffence">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="VerifyPhishingOffence">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="GetIpAddressEvaluation">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="HandlePhishingOffenceWS">
        <wsdl:port binding="HandlePhishingOffenceBinding" name="HandlePhishingOffencePort">
            <soap:address location="/osb/ws/phishingOffence" />
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
