<?xml version = '1.0' encoding = 'UTF-8'?>
<application xmlns:soa="http://www.oracle.com/soa/rest" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="http://airbank.cz/osb/ws/phishingWhiteList" xmlns="http://wadl.dev.java.net/2009/02">
   <doc title="PhishingWhiteListWS">RestReference</doc>
   
   <grammars>
      <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://airbank.cz/osb/ws/phishingWhiteList" schemaLocation="../xsd/PhishingWhiteListREST.xsd"/>
        </xsd:schema>
   </grammars>
   <resources>
   
    <resource path="/map_of_sets/ClientsVPNIPs">
         <method name="GET" soa:wsdlOperation="GetClientsVPNIPs">
            <request>
               <representation mediaType="application/json" element="tns:GetClientsVPNIPsRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetClientsVPNIPsResponse"/>
            </response>
         </method>
      </resource>
      
      <resource path="/sets/WhitelistedReferer">
         <method name="GET" soa:wsdlOperation="GetReferers">
            <request>
               <representation mediaType="application/json" element="tns:GetReferersRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetReferersResponse"/>
            </response>
         </method>
      </resource>
      
      <resource path="/sets/WhitelistedBrowserLanguage">
         <method name="GET" soa:wsdlOperation="GetBrowserLanguages">
            <request>
               <representation mediaType="application/json" element="tns:GetBrowserLanguagesRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetBrowserLanguagesResponse"/>
            </response>
         </method>
      </resource>
      
      <resource path="/sets/WhitelistedCountry">
         <method name="GET" soa:wsdlOperation="GetCountries">
            <request>
               <representation mediaType="application/json" element="tns:GetCountriesRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetCountriesResponse"/>
            </response>
         </method>
      </resource>
      
      <resource path="/sets/WhitelistedIP">
         <method name="GET" soa:wsdlOperation="GetIPs">
            <request>
               <representation mediaType="application/json" element="tns:GetIPsRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetIPsResponse"/>
            </response>
         </method>
      </resource>
      
      <resource path="/sets/WhitelistedCuid">
         <method name="GET" soa:wsdlOperation="GetCuids">
            <request>
               <representation mediaType="application/json" element="tns:GetCuidsRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetCuidsResponse"/>
            </response>
         </method>
      </resource>
      
      <resource path="/sets/WhitelistedCookie">
         <method name="GET" soa:wsdlOperation="GetCookies">
            <request>
               <representation mediaType="application/json" element="tns:GetCookiesRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:GetCookiesResponse"/>
            </response>
         </method>
      </resource>
      
    </resources>
</application>
