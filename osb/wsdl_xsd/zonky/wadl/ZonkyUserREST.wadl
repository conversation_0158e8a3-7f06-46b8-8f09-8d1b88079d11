<?xml version = '1.0' encoding = 'UTF-8'?>
<application xmlns:soa="http://www.oracle.com/soa/rest" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="http://airbank.cz/osb/ws/ZonkyUser" xmlns:obj="http://airbank.cz/osb/ws/ZonkyUserObj" xmlns="http://wadl.dev.java.net/2009/02">
   <doc title="ZonkyUserWS">RestReference</doc>
   
   <grammars>
      <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://airbank.cz/osb/ws/ZonkyUser" schemaLocation="../xsd/ZonkyUserREST.xsd"/>
        </xsd:schema>
      <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://airbank.cz/osb/ws/ZonkyUserObj" schemaLocation="../xsd/ZonkyUserObjREST.xsd"/>
        </xsd:schema>
   </grammars>
   <resources>
   
        <resource path="/users/{cuid}/products">
         <method name="GET" soa:wsdlOperation="SearchZonkyUsers">
            <request>
               <param name="cuid" style="template" soa:expression="$property.cuid" default="" type="xsd:string"/>
               <representation mediaType="application/json" element="tns:SearchZonkyUsersRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="tns:SearchZonkyUsersResponse"/>
            </response>
         </method>
        </resource>
      
        <resource path="/users/{cuid}/can-anonymize">
         <method name="GET" soa:wsdlOperation="CanAnonymize">
            <request>
               <param name="cuid" style="template" soa:expression="$msg.CanAnonymizeRequest/obj:cuid" default="" type="xsd:int"/>
               <representation mediaType="application/json" element="obj:CanAnonymizeRequest"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="obj:CanAnonymizeResponse"/>
            </response>
         </method>
        </resource>
        
    </resources>
</application>
