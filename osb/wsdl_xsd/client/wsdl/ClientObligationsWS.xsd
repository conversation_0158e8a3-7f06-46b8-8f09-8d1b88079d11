<?xml version="1.0" encoding="UTF-8"?>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://airbank.cz/osb/ClientObligations" targetNamespace="http://airbank.cz/osb/ClientObligations" elementFormDefault="qualified">

        <xs:element name="GetCreditExposureRequest">
            <xs:annotation>
                <xs:documentation>Komplexní typ definujíc<PERSON> request webové operace ClientObligationsWS.getCreditExposure</xs:documentation>
            </xs:annotation>
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="cuid" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Jednoznačný identifikátor klienta.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:choice>
                        <xs:element name="includedDataSource" type="SourceName" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Seznam datových zdro<PERSON>, kter<PERSON> mají být zahrnuty do výstupu.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="excludedDataSource" type="SourceName" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Seznam datových zdrojů, které nemají být zahrnuty do výstupu.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:choice>
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:element name="GetCreditExposureResponse">
            <xs:annotation>
                <xs:documentation>Komplexní typ definující response webové operace ClientObligationsWS.getCreditExposure</xs:documentation>
            </xs:annotation>
            <xs:complexType>
                <xs:sequence>
                    <xs:element name="data" type="CreditExposure" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Získaná data o úvěrové angažovanosti.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="dataSource" type="DataSource" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Informace o zdroji (ze kterého bylo požadováno získat data) a o stavu získání dat</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:complexType>
        </xs:element>

        <xs:simpleType name="SourceName">
            <xs:annotation>
                <xs:documentation>Enumerace datových zdrojů - Značek (Air Bank, Zonky, ...) v rámci stejné organizace.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="AIRBANK">
                    <xs:annotation>
                        <xs:documentation>Air Bank</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="ZONKY">
                    <xs:annotation>
                        <xs:documentation>Zonky</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="DataSource">
            <xs:annotation>
                <xs:documentation>Komplexní typ definující informace týkající se zdroje dat.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="sourceName" type="SourceName">
                    <xs:annotation>
                        <xs:documentation>Název zdroje dat (obchodní značky/organizace).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="state" type="DataReceiptState">
                    <xs:annotation>
                        <xs:documentation>Stav získání dat.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:simpleType name="DataReceiptState">
            <xs:annotation>
                <xs:documentation>Enumerace stavů získání dat ze zdrojových systémů.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="OK">
                    <xs:annotation>
                        <xs:documentation>Data úspěšně nalezena a získána, anebo pro daný CUID nebyla nalezena (prázdná response zdrojové služby, nebo chyba typu "pro daný CUID nenalezena žádná relevantní data").
                        </xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="FAILED">
                    <xs:annotation>
                        <xs:documentation>Některá se služeb systémů volaných pro získání dat z daného zdroje vrátila chybu a nebo skončila timeoutem.
                        </xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="CreditExposure">
            <xs:annotation>
                <xs:documentation>Komplexní typ definující úvěrovou angažovanost klienta z daného datového zdroje.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="instalmentLoan" type="InstalmentLoan" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Seznam splátkových úvěrů klienta (HU, KONS, HYNEW, HYREF).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="nonInstalmentLoan" type="NonInstalmentLoan" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Seznam nesplátkových úvěrů klienta (KTK).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="application" type="Application" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Seznam žádostí klienta o úvěrové produkty (HU, KONS, HYNEW, HYREF, KTK).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pastDueDebt" type="PastDueDebt" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Seznam aktuálních pohledávek po splatnosti za klientem.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="dataSource" type="SourceName">
                    <xs:annotation>
                        <xs:documentation>Název zdroje, ze kterého data pocházejí.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:complexType name="InstalmentLoan">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující informace splátkových úvěrech klienta (HU, KONS, HYNEW, HYREF).</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="loanNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Číslo úvěru (např. HU00006234).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Číslo žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="coDebtorCuid" type="xs:long" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>spoludluznik</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="InstalmentLoanProductType">
                    <xs:annotation>
                        <xs:documentation>Typ produktu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="status" type="ProductStatus">
                    <xs:annotation>
                        <xs:documentation>Stav produktu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="loanAmount" type="MonetaryAmount">
                    <xs:annotation>
                        <xs:documentation>Počáteční výše úvěru.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="amountToRepay" type="MonetaryAmount" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Počáteční výše úvěru.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="approvalDate" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Počáteční výše úvěru.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="relationType" type="PersonRelationType" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Pojištění schopnosti splácet úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="unpaidPrincipal" type="MonetaryAmount" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Zbývající jistina.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="installmentAmount" type="MonetaryAmount">
                    <xs:annotation>
                        <xs:documentation>Výše splátky.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="lastPaymentHolidayStart" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Počátek platnosti posledních splátkových prázdnin.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="interestRate" type="InterestRate" maxOccurs="2">
                    <xs:annotation>
                        <xs:documentation>Úroková sazba (u HU a KONS se posílá standardní i bonusová).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="closeDate" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum ukončení produktu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="insurance" type="Insurance" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Pojištění schopnosti splácet úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>


        <xs:simpleType name="PersonRelationType">
            <xs:annotation>
                <xs:documentation>Enumerace typů žádostí o úvěrový produkt.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="OWNER">
                    <xs:annotation>
                        <xs:documentation>Owner.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CODEBTOR">
                    <xs:annotation>
                        <xs:documentation>Codebtor.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:simpleType name="InstalmentLoanProductType">
            <xs:annotation>
                <xs:documentation>Enumerace typů splátkových produktů.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="STD">
                    <xs:annotation>
                        <xs:documentation>Neúčelový standardní hotovostní úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="REF">
                    <xs:annotation>
                        <xs:documentation>Refinancování úvěru (nahrazeno konsolidací).</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CON">
                    <xs:annotation>
                        <xs:documentation>Konsolidace úvěru.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="HYREF">
                    <xs:annotation>
                        <xs:documentation>Převedená hypotéka.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="HYNEW">
                    <xs:annotation>
                        <xs:documentation>Standardní hypotéka.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="SPLIT">
                    <xs:annotation>
                        <xs:documentation>rozlozeni platby</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:simpleType name="ProductStatus">
            <xs:annotation>
                <xs:documentation>Enumerace stavů splátkových (HU, KONS, HYNEW, HYREF) i nesplátkových (KTK) produktů.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="NEW">
                    <xs:annotation>
                        <xs:documentation>Nový.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CANCELLED_BY_CLIENT">
                    <xs:annotation>
                        <xs:documentation>Stornovaný klientem.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CANCELLED_BY_BANK">
                    <xs:annotation>
                        <xs:documentation>Stornovaný bankou.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="REJECTED">
                    <xs:annotation>
                        <xs:documentation>Zamítnutý.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="PENDING">
                    <xs:annotation>
                        <xs:documentation>Čeká na načerpání.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="DRAWING">
                    <xs:annotation>
                        <xs:documentation>V čerpání</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="ACTIVE">
                    <xs:annotation>
                        <xs:documentation>Aktivní.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="REPAID_STD">
                    <xs:annotation>
                        <xs:documentation>Splaceno standardně.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="REPAID_PREMATURELY">
                    <xs:annotation>
                        <xs:documentation>Splaceno předčasně.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="REPAID_CONSOLIDATION">
                    <xs:annotation>
                        <xs:documentation>Splaceno konsolidací.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="WITHDRAWN_UNPAID">
                    <xs:annotation>
                        <xs:documentation>Odstoupený nedoplacený úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="WITHDRAWN_PAID">
                    <xs:annotation>
                        <xs:documentation>Odstoupený doplacený úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CALLED_DUE_UNPAID">
                    <xs:annotation>
                        <xs:documentation>Zesplatněný nedoplacený úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CALLED_DUE_PAID">
                    <xs:annotation>
                        <xs:documentation>Zesplatněný doplacený úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="WRITE_OFF_UNPAID">
                    <xs:annotation>
                        <xs:documentation>Odepsaný nedoplacený úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="WRITE_OFF_PAID">
                    <xs:annotation>
                        <xs:documentation>Odepsaný doplacený úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="TO_WRITTEN_OFF_UNPAID">
                    <xs:annotation>
                        <xs:documentation>Úvěr k odpisu nedoplacený.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="TO_WRITTEN_OFF_PAID">
                    <xs:annotation>
                        <xs:documentation>Úvěr k odpisu doplacený.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="MonetaryAmount">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující částku a kód měny.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="currency" type="CurrencyCode">
                    <xs:annotation>
                        <xs:documentation>Kód měny.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="amount" type="xs:decimal">
                    <xs:annotation>
                        <xs:documentation>Peněžní částka.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:simpleType name="CurrencyCode">
            <xs:annotation>
                <xs:documentation>Třípísmenný ISO kód</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:length value="3" />
                <xs:pattern value="[A-Z]*" />
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="InterestRate">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující informace úrokové sazbě úvěrového produktu.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="rate" type="xs:decimal">
                    <xs:annotation>
                        <xs:documentation>Úroková sazba.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="InterestRateType">
                    <xs:annotation>
                        <xs:documentation>Typ úrokové sazby.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:simpleType name="InterestRateType">
            <xs:annotation>
                <xs:documentation>Enumerace typů úrokových sazeb.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="STDRATE">
                    <xs:annotation>
                        <xs:documentation>Standardní úroková sazba.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="PRIMERATE">
                    <xs:annotation>
                        <xs:documentation>Bonusová úroková sazba.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="FIXRATE">
                    <xs:annotation>
                        <xs:documentation>Hypotéky s fixní úrokovou sazbou.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="FLOATRATE">
                    <xs:annotation>
                        <xs:documentation>Hypotéky s plovoucí úrokovou sazbou.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="FLOATRATECAP">
                    <xs:annotation>
                        <xs:documentation>Úrokový strop hypotéky s plovoucí úrokovou sazbou.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="Insurance">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující informace pojištění schopnosti splácet úvěr.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="number" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Číslo pojištění (např. PU1000008779).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="premium" type="MonetaryAmount">
                    <xs:annotation>
                        <xs:documentation>Aktuální výše pojistného dle splátkového kalendáře.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:complexType name="NonInstalmentLoan">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující informace o nesplátkových úvěrech klienta (KTK).</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="loanNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Číslo úvěru (např. KTK00001742).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Číslo žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="NonInstalmentLoanProductType">
                    <xs:annotation>
                        <xs:documentation>Typ produktu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="status" type="ProductStatus">
                    <xs:annotation>
                        <xs:documentation>Stav produktu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="loanAmount" type="MonetaryAmount">
                    <xs:annotation>
                        <xs:documentation>Výše limitu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="amountToRepay" type="MonetaryAmount" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Počáteční výše úvěru.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="approvalDate" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Počáteční výše úvěru.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="currentUtilizedAmount" type="MonetaryAmount">
                    <xs:annotation>
                        <xs:documentation>Vyčerpaný limit.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="interestRate" type="xs:decimal">
                    <xs:annotation>
                        <xs:documentation>Úroková sazba.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="closeDate" type="xs:date" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum ukončení produktu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:simpleType name="NonInstalmentLoanProductType">
            <xs:annotation>
                <xs:documentation>Enumerace typů nesplátkových produktů.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="OVERDRAFT">
                    <xs:annotation>
                        <xs:documentation>Kontokorent.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="Application">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující žádosti klienta o úvěrové produkty (HU, KONS, HYNEW, HYREF, KTK).</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="applicationId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Číslo žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Číslo obálky.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="LoanProductApplicationType">
                    <xs:annotation>
                        <xs:documentation>Typ žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="status" type="ApplicationStatusType">
                    <xs:annotation>
                        <xs:documentation>Stav žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationSentToScoringDate" type="xs:dateTime" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum zaslání žádosti do scoringu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationFinishedDate" type="xs:dateTime" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum nastavení žádosti do finálního stavu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Důvod zamítnutí žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="loanAmountPreScoring" type="MonetaryAmount" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Výše úvěru požadovaná klientem před zasláním do scoringu.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="signDate" type="xs:dateTime" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Datum podpisu žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

        <xs:simpleType name="LoanProductApplicationType">
            <xs:annotation>
                <xs:documentation>Enumerace typů žádostí o úvěrový produkt.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="CASH_LOAN">
                    <xs:annotation>
                        <xs:documentation>Hotovostní úvěr.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CONSOLIDATION">
                    <xs:annotation>
                        <xs:documentation>Konsolidace.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="MORTGAGE">
                    <xs:annotation>
                        <xs:documentation>Nová hypotéka.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="MORTGAGE_REF">
                    <xs:annotation>
                        <xs:documentation>Refinancování hypotéky.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="OVERDRAFT">
                    <xs:annotation>
                        <xs:documentation>Kontokorent.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="SPLIT_PAYMENT">
                    <xs:annotation>
                        <xs:documentation>Rozložení platby.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>

        </xs:simpleType>

        <xs:simpleType name="ApplicationStatusType">
            <xs:annotation>
                <xs:documentation>Enumerace stavů žádostí.</xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string">
                <xs:enumeration value="DEMO">
                    <xs:annotation>
                        <xs:documentation>Iniciální stav žádosti.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="LEAD">
                    <xs:annotation>
                        <xs:documentation>Žádost je z lead stránek.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="UNFINISHED">
                    <xs:annotation>
                        <xs:documentation>Rozpracovaná.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="WAITING">
                    <xs:annotation>
                        <xs:documentation>Nedostupnost BRKI.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="MANUALPAIRING">
                    <xs:annotation>
                        <xs:documentation>Manuálně párovaná.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="VIP_PAUSED">
                    <xs:annotation>
                        <xs:documentation>Prověřovaná VIP, EI.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="WAIT_FOR_OFFER">
                    <xs:annotation>
                        <xs:documentation>Segmentace.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="UNDERWRITING">
                    <xs:annotation>
                        <xs:documentation>Underwriting.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="ALTERNATIVE_OFFER">
                    <xs:annotation>
                        <xs:documentation>Alternativní nabídka.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="CANCELLED">
                    <xs:annotation>
                        <xs:documentation>Stornovaná.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="REJECTED">
                    <xs:annotation>
                        <xs:documentation>Zamítnutá.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="VERIFY">
                    <xs:annotation>
                        <xs:documentation>Prověřovaná (warningy).</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="APPROVED">
                    <xs:annotation>
                        <xs:documentation>Schválená.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="MANUALVERIFY">
                    <xs:annotation>
                        <xs:documentation>Manuálně ověřovaná.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="COMPLETION">
                    <xs:annotation>
                        <xs:documentation>Kompletace.</xs:documentation>
                    </xs:annotation>
                </xs:enumeration>
            </xs:restriction>
        </xs:simpleType>

        <xs:complexType name="PastDueDebt">
            <xs:annotation>
                <xs:documentation>Komplexní typ obsahující informace o aktuálních pohledávkách po splatnosti za klientem.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="productNumber" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Číslo produktu dle typu pohledávky (loanNumber, insuranceNumber, accountNumber...).</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="origin" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Původ pohledávky po splatnosti - hodnota z MDM číselníku PAST_DUE_DEBT_ORIGIN</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="internalDPDWithTolerance" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Aktuální Interní DPD (DayPastDue) při zahrnutí DPD tolerance.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pastDueAmount" type="MonetaryAmount">
                    <xs:annotation>
                        <xs:documentation>Aktuální výše dlužné částky.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="calledDue" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Příznak, zda je zesplatněno.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>

    </xs:schema>
