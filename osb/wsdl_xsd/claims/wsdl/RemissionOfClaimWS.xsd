<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://airbank.cz/RemissionOfClaim/ws/RemissionOfClaimWS" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="getAllRemissionOfClaimsRequest">
    <xs:complexType>
      <xs:sequence>
        <xs:element type="xs:string" name="cuid"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="getAllRemissionOfClaimsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="RemissionOfClaim" maxOccurs="unbounded" minOccurs="0">
          <xs:complexType>
            <xs:sequence>
              <xs:element type="xs:string" name="ticketId"/>
              <xs:element type="xs:string" name="status"/>
              <xs:element type="xs:string" name="url"/>
              <xs:element type="xs:decimal" name="remissionOfClaimAmount"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>