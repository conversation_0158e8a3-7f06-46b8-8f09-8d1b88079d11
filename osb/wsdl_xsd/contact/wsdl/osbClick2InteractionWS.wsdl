<?xml version="1.0" encoding="UTF-8" ?>
    <definitions targetNamespace="http://osb.abank.cz/click2interaction/" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://osb.abank.cz/click2interaction/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
        <types>
            <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://osb.abank.cz/click2interaction/">
                <xsd:include schemaLocation="osbClick2InteractionWS.xsd" />
            </xsd:schema>
        </types>
        <message name="Click2CallRequest">
            <part element="tns:Click2CallRequest" name="Click2CallRequest" />
        </message>
        <message name="Click2CallResponse">
            <part element="tns:Click2CallResponse" name="Click2CallResponse" />
        </message>
        <message name="Click2OfflineContactRequest">
            <part element="tns:Click2OfflineContactRequest" name="Click2OfflineContactRequest" />
        </message>
        <message name="Click2OfflineContactResponse">
            <part element="tns:Click2OfflineContactResponse" name="Click2OfflineContactResponse" />
        </message>

        <message name="Click2CustomerDetailRequest">
            <part element="tns:Click2CustomerDetailRequest" name="Click2CustomerDetailRequest" />
        </message>
        <message name="Click2CustomerDetailResponse">
            <part element="tns:Click2CustomerDetailResponse" name="Click2CustomerDetailResponse" />
        </message>

        <portType name="ClickInteractionWSPort">
            <operation name="Click2Call">
                <input message="tns:Click2CallRequest" name="Click2CallRequest" />
                <output message="tns:Click2CallResponse" name="Click2CallResponse" />
            </operation>
            <operation name="Click2OfflineContact">
                <input message="tns:Click2OfflineContactRequest" name="Click2OfflineContactRequest" />
                <output message="tns:Click2OfflineContactResponse" name="Click2OfflineContactResponse" />
                <!--
      Possible faults:
      UNKNOWN_ADDRESSEE
      WRONG_ADDRESSEE
      UNSUPPORTED_INITIATION 
      IB_CONCEPT_FAILED
      -->
            </operation>

            <operation name="Click2CustomerDetail">
                <input message="tns:Click2CustomerDetailRequest" name="Click2CustomerDetailRequest" />
                <output message="tns:Click2CustomerDetailResponse" name="Click2CustomerDetailResponse" />
            </operation>
        </portType>
        <binding name="ClickInteractionWSSoap11" type="tns:ClickInteractionWSPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <operation name="Click2Call">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="Click2OfflineContact">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

            <operation name="Click2CustomerDetail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
        </binding>
        <service name="ClickInteractionWSService">
            <port binding="tns:ClickInteractionWSSoap11" name="ClickInteractionWSSoap11">
                <soap:address location="http://TO-BE-SPECIFIED/osb/ws/Click2InteractionWS" />
            </port>
        </service>
    </definitions>
