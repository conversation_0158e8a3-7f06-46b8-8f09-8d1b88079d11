<?xml version="1.0" encoding="UTF-8" ?>
    <!-- $Id: 6591705deda57e055515bca4ac99f685cd329e0a $ -->
    <definitions targetNamespace="http://osb.abank.cz/contact/" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://osb.abank.cz/contact/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:common="http://airbank.cz/common/ws/fault" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
        <types>
            <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://osb.abank.cz/contact/">
                <xsd:include schemaLocation="osbContactWS.xsd" />
                <xsd:include schemaLocation="osbContactWS_extended.xsd" />
            </xsd:schema>
            <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
                <xsd:include schemaLocation="../xsd/commonSoapFault.xsd" />
            </xsd:schema>
        </types>
        <message name="ReceiveCallRequest">
            <part element="tns:ReceiveCallRequest" name="ReceiveCallRequest" />
        </message>
        <message name="ReceiveCallResponse">
            <part element="tns:ReceiveCallResponse" name="ReceiveCallResponse" />
        </message>
        <message name="RegisterEstablishedFromBankCallRequest">
            <part element="tns:RegisterEstablishedFromBankCallRequest" name="RegisterEstablishedFromBankCallRequest" />
        </message>
        <message name="RegisterEstablishedFromBankCallResponse">
            <part element="tns:RegisterEstablishedFromBankCallResponse" name="RegisterEstablishedFromBankCallResponse" />
        </message>
        <message name="RegisterUnestablishedFromBankCallRequest">
            <part element="tns:RegisterUnestablishedFromBankCallRequest" name="RegisterUnestablishedFromBankCallRequest" />
        </message>
        <message name="RegisterUnestablishedFromBankCallResponse">
            <part element="tns:RegisterUnestablishedFromBankCallResponse" name="RegisterUnestablishedFromBankCallResponse" />
        </message>
        <message name="AddContactBusinessSummaryRequest">
            <part element="tns:AddContactBusinessSummaryRequest" name="AddContactBusinessSummaryRequest" />
        </message>
        <message name="AddContactBusinessSummaryResponse">
            <part element="tns:AddContactBusinessSummaryResponse" name="AddContactBusinessSummaryResponse" />
        </message>
        <message name="GetContactBusinessSummaryRequest">
            <part element="tns:GetContactBusinessSummaryRequest" name="GetContactBusinessSummaryRequest" />
        </message>
        <message name="GetContactBusinessSummaryResponse">
            <part element="tns:GetContactBusinessSummaryResponse" name="GetContactBusinessSummaryResponse" />
        </message>
        <message name="RegisterFromBankFailoverContactRequest">
            <part element="tns:RegisterFromBankFailoverContactRequest" name="RegisterFromBankFailoverContactRequest" />
        </message>
        <message name="RegisterFromBankFailoverContactResponse">
            <part element="tns:RegisterFromBankFailoverContactResponse" name="RegisterFromBankFailoverContactResponse" />
        </message>
        <message name="RegisterToBankFailoverContactRequest">
            <part element="tns:RegisterToBankFailoverContactRequest" name="RegisterToBankFailoverContactRequest" />
        </message>
        <message name="RegisterToBankFailoverContactResponse">
            <part element="tns:RegisterToBankFailoverContactResponse" name="RegisterToBankFailoverContactResponse" />
        </message>
        <message name="ReceiveEmailRequest">
            <part element="tns:ReceiveEmailRequest" name="ReceiveEmailRequest" />
        </message>
        <message name="ReceiveEmailResponse">
            <part element="tns:ReceiveEmailResponse" name="ReceiveEmailResponse" />
        </message>
        <message name="ReceiveIBMessageRequest">
            <part element="tns:ReceiveIBMessageRequest" name="ReceiveIBMessageRequest" />
        </message>
        <message name="ReceiveIBMessageResponse">
            <part element="tns:ReceiveIBMessageResponse" name="ReceiveIBMessageResponse" />
        </message>
        <message name="RevokeIBMessageRequest">
            <part element="tns:RevokeIBMessageRequest" name="RevokeIBMessageRequest" />
        </message>
        <message name="RevokeIBMessageResponse">
            <part element="tns:RevokeIBMessageResponse" name="RevokeIBMessageResponse" />
        </message>
        <message name="RevokeIBMessageFaultMessage">
            <part element="common:CoreFaultElement" name="RevokeIBMessageFaultMessage" />
        </message>
        <message name="SendEmailRequest">
            <part element="tns:SendEmailRequest" name="SendEmailRequest" />
        </message>
        <message name="SendEmailResponse">
            <part element="tns:SendEmailResponse" name="SendEmailResponse" />
        </message>
        <message name="RevokeConceptRequest">
            <part element="tns:RevokeConceptRequest" name="RevokeConceptRequest" />
        </message>
        <message name="RevokeConceptResponse">
            <part element="tns:RevokeConceptResponse" name="RevokeConceptResponse" />
        </message>
        <message name="AssignContactToCustomerRequest">
            <part element="tns:AssignContactToCustomerRequest" name="AssignContactToCustomerRequest" />
        </message>
        <message name="AssignContactToCustomerResponse">
            <part element="tns:AssignContactToCustomerResponse" name="AssignContactToCustomerResponse" />
        </message>
        <message name="UnassignContactFromCustomerRequest">
            <part element="tns:UnassignContactFromCustomerRequest" name="UnassignContactFromCustomerRequest" />
        </message>
        <message name="UnassignContactFromCustomerResponse">
            <part element="tns:UnassignContactFromCustomerResponse" name="UnassignContactFromCustomerResponse" />
        </message>
        <message name="RegisterContactFinishRequest">
            <part element="tns:RegisterContactFinishRequest" name="RegisterContactFinishRequest" />
        </message>
        <message name="RegisterContactFinishResponse">
            <part element="tns:RegisterContactFinishResponse" name="RegisterContactFinishResponse" />
        </message>
        <message name="CreateReplyOfflineConceptRequest">
            <part element="tns:CreateReplyOfflineConceptRequest" name="CreateReplyOfflineConceptRequest" />
        </message>
        <message name="CreateReplyOfflineConceptResponse">
            <part element="tns:CreateReplyOfflineConceptResponse" name="CreateReplyOfflineConceptResponse" />
        </message>
        <message name="CreateAdHocOfflineConceptRequest">
            <part element="tns:CreateAdHocOfflineConceptRequest" name="CreateAdHocOfflineConceptRequest" />
        </message>
        <message name="CreateAdHocOfflineConceptResponse">
            <part element="tns:CreateAdHocOfflineConceptResponse" name="CreateAdHocOfflineConceptResponse" />
        </message>
        <message name="OverrideContactRelationsRequest">
            <part element="tns:OverrideContactRelationsRequest" name="OverrideContactRelationsRequest" />
        </message>
        <message name="OverrideContactRelationsResponse">
            <part element="tns:OverrideContactRelationsResponse" name="OverrideContactRelationsResponse" />
        </message>
        <message name="GetContactHistoryRequest">
            <part element="tns:GetContactHistoryRequest" name="GetContactHistoryRequest" />
        </message>
        <message name="GetContactHistoryResponse">
            <part element="tns:GetContactHistoryExtendedResponse" name="GetContactHistoryResponse" />
        </message>
        <message name="GetContactDetailRequest">
            <part element="tns:GetContactDetailExtendedRequest" name="GetContactDetailRequest" />
        </message>
        <message name="GetContactDetailResponse">
            <part element="tns:GetContactDetailExtendedResponse" name="GetContactDetailResponse" />
        </message>
        <message name="GetContactDetailFaultMessage">
            <part element="common:CoreFaultElement" name="GetContactDetailFault" />
        </message>
        <message name="SendIBMessageRequest">
            <part element="tns:SendIBMessageRequest" name="SendIBMessageRequest" />
        </message>
        <message name="SendIBMessageResponse">
            <part element="tns:SendIBMessageResponse" name="SendIBMessageResponse" />
        </message>
        <message name="SendSMSRequest">
            <part element="tns:SendSMSRequest" name="SendSMSRequest" />
        </message>
        <message name="SendSMSResponse">
            <part element="tns:SendSMSResponse" name="SendSMSResponse" />
        </message>
        <message name="SendContactAlertRequest">
            <part element="tns:SendContactAlertRequest" name="SendContactAlertRequest" />
        </message>
        <message name="SendContactAlertResponse">
            <part element="tns:SendContactAlertResponse" name="SendContactAlertResponse" />
        </message>
        <message name="RegisterSupplementaryContactRequest">
            <part element="tns:RegisterSupplementaryContactRequest" name="RegisterSupplementaryContactRequest" />
        </message>
        <message name="RegisterSupplementaryContactResponse">
            <part element="tns:RegisterSupplementaryContactResponse" name="RegisterSupplementaryContactResponse" />
        </message>
        <message name="RegisterAssistanceRequest">
            <part element="tns:RegisterAssistanceRequest" name="RegisterAssistanceRequest" />
        </message>
        <message name="RegisterAssistanceResponse">
            <part element="tns:RegisterAssistanceResponse" name="RegisterAssistanceResponse" />
        </message>
        <message name="AddExternalRelationsRequest">
            <part element="tns:AddExternalRelationsRequest" name="AddExternalRelationsRequest" />
        </message>
        <message name="AddExternalRelationsResponse">
            <part element="tns:AddExternalRelationsResponse" name="AddExternalRelationsResponse" />
        </message>
        <message name="AddDefaultBusinessSummaryRequest">
            <part element="tns:AddDefaultBusinessSummaryRequest" name="AddDefaultBusinessSummaryRequest" />
        </message>
        <message name="AddDefaultBusinessSummaryResponse">
            <part element="tns:AddDefaultBusinessSummaryResponse" name="AddDefaultBusinessSummaryResponse" />
        </message>
        <message name="GetUnansweredCallsRequest">
            <part element="tns:GetUnansweredCallsRequest" name="GetUnansweredCallsRequest" />
        </message>
        <message name="GetUnansweredCallsResponse">
            <part element="tns:GetUnansweredCallsResponse" name="GetUnansweredCallsResponse" />
        </message>

        <message name="RegisterContactAlertRequest">
            <part element="tns:RegisterContactAlertRequest" name="RegisterContactAlertRequest" />
        </message>
        <message name="RegisterContactAlertResponse">
            <part element="tns:RegisterContactAlertResponse" name="RegisterContactAlertResponse" />
        </message>
        <message name="RegisterContactAlertFaultMessage">
            <part element="common:CoreFaultElement" name="RegisterContactAlertFault" />
        </message>
        <message name="GetUnansweredCallDetailRequest">
            <part element="tns:GetUnansweredCallDetailRequest" name="GetUnansweredCallDetailRequest" />
        </message>
        <message name="GetUnansweredCallDetailResponse">
            <part element="tns:GetUnansweredCallDetailResponse" name="GetUnansweredCallDetailResponse" />
        </message>
        <message name="GetUnansweredCallDetailFaultMessage">
            <part element="common:CoreFaultElement" name="GetUnansweredCallDetailFault" />
        </message>
        <message name="SetMessageReadRequest">
            <part element="tns:SetMessageReadRequest" name="SetMessageReadRequest" />
        </message>
        <message name="SetMessageReadResponse">
            <part element="tns:SetMessageReadResponse" name="SetMessageReadResponse" />
        </message>
        <message name="RegisterUrlClickRequest">
            <part element="tns:RegisterUrlClickRequest" name="RegisterUrlClickRequest" />
        </message>
        <message name="RegisterUrlClickResponse">
            <part element="tns:RegisterUrlClickResponse" name="RegisterUrlClickResponse" />
        </message>

        <message name="ReceiveWebChatRequest">
            <part element="tns:ReceiveWebChatRequest" name="ReceiveWebChatRequest" />
        </message>
        <message name="ReceiveWebChatResponse">
            <part element="tns:ReceiveWebChatResponse" name="ReceiveWebChatResponse" />
        </message>
        <message name="ReceiveWebChatFaultMessage">
            <part element="common:CoreFaultElement" name="ReceiveWebChatFaultMessage" />
        </message>

        <message name="ReceiveMAChatRequest">
            <part element="tns:ReceiveMAChatRequest" name="ReceiveMAChatRequest" />
        </message>
        <message name="ReceiveMAChatResponse">
            <part element="tns:ReceiveMAChatResponse" name="ReceiveMAChatResponse" />
        </message>
        <message name="ReceiveMAChatFaultMessage">
            <part element="common:CoreFaultElement" name="ReceiveMAChatFaultMessage" />
        </message>

        <message name="SetWebCampaignAnswerRequest">
            <part element="tns:SetWebCampaignAnswerRequest" name="SetWebCampaignAnswerRequest" />
        </message>
        <message name="SetWebCampaignAnswerResponse">
            <part element="tns:SetWebCampaignAnswerResponse" name="SetWebCampaignAnswerResponse" />
        </message>
        <message name="SetWebCampaignAnswerFaultMessage">
            <part element="common:CoreFaultElement" name="SetWebCampaignAnswerFaultMessage" />
        </message>

        <message name="AddVoiceChatRequest">
            <part element="tns:AddVoiceChatRequest" name="AddVoiceChatRequest" />
        </message>
        <message name="AddVoiceChatResponse">
            <part element="tns:AddVoiceChatResponse" name="AddVoiceChatResponse" />
        </message>
        <message name="AddVoiceChatFaultMessage">
            <part element="common:CoreFaultElement" name="AddVoiceChatFault" />
        </message>

        <message name="AddCallConnectionIdRequest">
            <part element="tns:AddCallConnectionIdRequest" name="AddCallConnectionIdRequest" />
        </message>
        <message name="AddCallConnectionIdResponse">
            <part element="tns:AddCallConnectionIdResponse" name="AddCallConnectionIdResponse" />
        </message>
        <message name="AddCallConnectionIdFaultMessage">
            <part element="common:CoreFaultElement" name="AddCallConnectionIdFault" />
        </message>
        
        <message name="SendSmsToPrimaryAndHistoricalMobileRequest">
            <part element="tns:SendSmsToPrimaryAndHistoricalMobileRequest" name="SendSmsToPrimaryAndHistoricalMobileRequest" />
        </message>
        <message name="SendSmsToPrimaryAndHistoricalMobileResponse">
            <part element="tns:SendSmsToPrimaryAndHistoricalMobileResponse" name="SendSmsToPrimaryAndHistoricalMobileResponse" />
        </message>
        <message name="SendEmailToPrimaryAndHistoricalEmailRequest">
            <part element="tns:SendEmailToPrimaryAndHistoricalEmailRequest" name="SendEmailToPrimaryAndHistoricalEmailRequest" />
        </message>
        <message name="SendEmailToPrimaryAndHistoricalEmailResponse">
            <part element="tns:SendEmailToPrimaryAndHistoricalEmailResponse" name="SendEmailToPrimaryAndHistoricalEmailResponse" />
        </message>

        <portType name="ContactWSPort">
            <operation name="ReceiveCall">
                <documentation>
                    Store received call to CML. Response: - contactId
                </documentation>
                <input message="tns:ReceiveCallRequest" name="ReceiveCallRequest" />
                <output message="tns:ReceiveCallResponse" name="ReceiveCallResponse" />
            </operation>
            <operation name="RegisterEstablishedFromBankCall">
                <documentation>
                    Store from bank call to CML. Response: - contactId Response fault codes: - Contact.DoesNotExist - specified predecessor contact was not found - Contact.Contact.PredecessorBelongsToOtherPerson - predecessor contact has not mathed cuid
                </documentation>
                <input message="tns:RegisterEstablishedFromBankCallRequest" name="RegisterEstablishedFromBankCallRequest" />
                <output message="tns:RegisterEstablishedFromBankCallResponse" name="RegisterEstablishedFromBankCallResponse" />
            </operation>
            <operation name="RegisterUnestablishedFromBankCall">
                <documentation>
                    Store from bank call which was not technically established to CML. Response: - contactId Response fault codes: - Contact.DoesNotExist - specified concept call for CLICK2CALL was not found
                </documentation>
                <input message="tns:RegisterUnestablishedFromBankCallRequest" name="RegisterUnestablishedFromBankCallRequest" />
                <output message="tns:RegisterUnestablishedFromBankCallResponse" name="RegisterUnestablishedFromBankCallResponse" />
            </operation>
            <operation name="AddContactBusinessSummary">
                <documentation>
                    Operation adds BusinessSummary to specified contact. Following attributes are always mandatory: - contactId - operatorBusinessSummary Response: - employeeNumber - contactId - predecessorContactId Response fault codes: - Contact.DoesNotExist - specified contact was not found
                </documentation>
                <input message="tns:AddContactBusinessSummaryRequest" name="AddContactBusinessSummaryRequest" />
                <output message="tns:AddContactBusinessSummaryResponse" name="AddContactBusinessSummaryResponse" />
            </operation>
            <operation name="GetContactBusinessSummary">
                <documentation>
                    Operation returns business summary for contact and employer or return initial business summary for contact from its predecessors. Following attributes are always mandatory: - contactId - employeeNumber Response fault codes: - Contact.DoesNotExist - specified contact was not found
                </documentation>
                <input message="tns:GetContactBusinessSummaryRequest" name="GetContactBusinessSummaryRequest" />
                <output message="tns:GetContactBusinessSummaryResponse" name="GetContactBusinessSummaryResponse" />
            </operation>
            <operation name="RegisterFromBankFailoverContact">
                <documentation>
                    Store from bank failover email to CML. Response: - contactId
                </documentation>
                <input message="tns:RegisterFromBankFailoverContactRequest" name="RegisterFromBankFailoverContactRequest" />
                <output message="tns:RegisterFromBankFailoverContactResponse" name="RegisterFromBankFailoverContactResponse" />
            </operation>
            <operation name="RegisterToBankFailoverContact">
                <documentation>
                    Store from bank failover email to CML. Response: - contactId
                </documentation>
                <input message="tns:RegisterToBankFailoverContactRequest" name="RegisterToBankFailoverContactRequest" />
                <output message="tns:RegisterToBankFailoverContactResponse" name="RegisterToBankFailoverContactResponse" />
            </operation>
            <operation name="ReceiveEmail">
                <documentation>
                    Operation is used to create email contact in CML database. Following attribute is always mandatory: - emailContact Response fault codes: - Contact.DoesNotExist - specified contact was not found
                </documentation>
                <input message="tns:ReceiveEmailRequest" name="ReceiveEmailRequest" />
                <output message="tns:ReceiveEmailResponse" name="ReceiveEmailResponse" />
            </operation>
            <operation name="ReceiveIBMessage">
                <documentation>
                    Operation is used for receiving incoming IB message. Following attribute is always mandatory: - contactData Response: - contactId Response fault codes: - Contact.DoesNotExist - specified predecessor contact was not found
                </documentation>
                <input message="tns:ReceiveIBMessageRequest" name="ReceiveIBMessageRequest" />
                <output message="tns:ReceiveIBMessageResponse" name="ReceiveIBMessageResponse" />
            </operation>
            <operation name="RevokeIBMessage">
                <documentation>
                    Revokes IB message for the given contact ID Following attribute is always mandatory: - contactId Response codes: - OK - contact was succesfully revoked Response fault codes: - Contact.DoesNotExist - specified contact was not found
                </documentation>
                <input message="tns:RevokeIBMessageRequest" name="RevokeIBMessageRequest" />
                <output message="tns:RevokeIBMessageResponse" name="RevokeIBMessageResponse" />
                <fault message="tns:RevokeIBMessageFaultMessage" name="RevokeIBMessageFault" />
            </operation>
            <operation name="SendEmail">
                <input message="tns:SendEmailRequest" name="SendEmailRequest" />
                <output message="tns:SendEmailResponse" name="SendEmailResponse" />
            </operation>
            <operation name="SendSMS">
                <documentation>
                    Request : - sms.text - text of the contact without diacritics is guaranteed delivery but if messages is with diacritics messages server removes it - sms.text - text length max 160 characters is quaranteed delivery
                </documentation>
                <input message="tns:SendSMSRequest" name="SendSMSRequest" />
                <output message="tns:SendSMSResponse" name="SendSMSResponse" />
            </operation>
            <operation name="SendIBMessage">
                <input message="tns:SendIBMessageRequest" name="SendIBMessageRequest" />
                <output message="tns:SendIBMessageResponse" name="SendIBMessageResponse" />
            </operation>
            <operation name="SendContactAlert">
                <input message="tns:SendContactAlertRequest" name="SendContactAlertRequest" />
                <output message="tns:SendContactAlertResponse" name="SendContactAlertResponse" />
            </operation>
            <operation name="AssignContactToCustomer">
                <documentation>
                    Operation is used for binding incoming contact to customer. Following attributes are always mandatory: - contactId - cuid Response fault codes: - Contact.DoesNotExist - specified contact was not found - Contact.WrongDirection - specified contact has wrong direction
                </documentation>
                <input message="tns:AssignContactToCustomerRequest" name="AssignContactToCustomerRequest" />
                <output message="tns:AssignContactToCustomerResponse" name="AssignContactToCustomerResponse" />
            </operation>
            <operation name="RegisterContactFinish">
                <documentation>
                    Operation is used to finish contact, which is already stored in CML. Following attributes are always mandatory: - contactId - contactFinish - contentlessClassification Response fault codes: - Contact.DoesNotExist - contact does not exist - Contact.IncorrectCreator - incorrect creator data - Contact.ForbiddenRegisteringContactFinish - processing way for outgoing offline contact is forbidden - Contact.ForbiddenProcessingWay - processing way is not allowed - Contact.MandatoryContentlessClassification - contentless classification is required - Contact.ContentlessClassificationTerminationError - ContentlessClassification can be overwritten only with same operator - Contact.ContentlessClassificationContactWithBusinessSummary - Cant add ContentlessClassification on contact with business summary
                </documentation>
                <input message="tns:RegisterContactFinishRequest" name="RegisterContactFinishRequest" />
                <output message="tns:RegisterContactFinishResponse" name="RegisterContactFinishResponse" />
            </operation>
            <operation name="UnassignContactFromCustomer">
                <documentation>
                    Operation is used to unassign specified contact. This includes removing cuid and all relations with predecessors. Following attributes are always mandatory: - contactId Response fault codes: - Contact.DoesNotExist - contact does not exist - Contact.PrintedContactMustHaveCuid - cannot unassign printed contact - Contact.IbContactMustHaveCuid - cannot unassign IB contact
                </documentation>
                <input message="tns:UnassignContactFromCustomerRequest" name="UnassignContactFromCustomerRequest" />
                <output message="tns:UnassignContactFromCustomerResponse" name="UnassignContactFromCustomerResponse" />
            </operation>
            <operation name="OverrideContactRelations">
                <documentation>
                    Operation is used for overriding contact relations for given child contact. Following attribute is always mandatory: - contactId Response fault codes: - Contact.DoesNotExist - specified contact was not found - Contact.PredecessorBelongsToOtherPerson - specified contact and some of predecessor has different cuids - Contact.CantCreateCycle - Graph acyclicity violation

                </documentation>
                <input message="tns:OverrideContactRelationsRequest" name="OverrideContactRelationsRequest" />
                <output message="tns:OverrideContactRelationsResponse" name="OverrideContactRelationsResponse" />
            </operation>
            <operation name="CreateReplyOfflineConcept">
                <documentation>
                    Operation is used for creating reply offline concept. Following attribute is always mandatory: - predecessorContatId - cuid - channel Response: - conceptContactId Response fault codes: - Contact.DoesNotExist - specified contact predecessor was not found
                </documentation>
                <input message="tns:CreateReplyOfflineConceptRequest" name="CreateReplyOfflineConceptRequest" />
                <output message="tns:CreateReplyOfflineConceptResponse" name="CreateReplyOfflineConceptResponse" />
            </operation>
            <operation name="CreateAdHocOfflineConcept">
                <documentation>
                    Operation is used for creating adhoc offline concept. Following attribute is always mandatory: - employeeNumber - cuid - channel Response: - conceptContactId Response fault codes: - Contact.DoesNotExist - specified contact predecessor was not found
                </documentation>
                <input message="tns:CreateAdHocOfflineConceptRequest" name="CreateAdHocOfflineConceptRequest" />
                <output message="tns:CreateAdHocOfflineConceptResponse" name="CreateAdHocOfflineConceptResponse" />
            </operation>
            <operation name="RevokeConcept">
                <documentation>
                    Revokes contact for the given contact ID Following attribute is always mandatory: - contactId Response codes: - OK - concept was succesfully revoked - PREDECESSOR_WITH_REPLY - exists at least one contact with reply, which is bounded to the concept in conversation tree Response fault codes: - Contact.DoesNotExist - specified contact was not found - Contact.ContactAlreadySent - specified contact was sent
                </documentation>
                <input message="tns:RevokeConceptRequest" name="RevokeConceptRequest" />
                <output message="tns:RevokeConceptResponse" name="RevokeConceptResponse" />
            </operation>
            <operation name="GetContactDetail">
                <documentation>
                    Returns detail about contact for the given contact ID Following attribute is always mandatory: - contactId

                </documentation>
                <input message="tns:GetContactDetailRequest" name="GetContactDetailRequest" />
                <output message="tns:GetContactDetailResponse" name="GetContactDetailResponse" />
                <fault message="tns:GetContactDetailFaultMessage" name="GetContactDetailFault" />
            </operation>
            <operation name="GetContactHistory">
                <documentation>
                    Operation returns contact history for specified filters. Following attributes are always mandatory: mandatoryFilter Optional attributes: optionalFilter outputSetting
                </documentation>
                <input message="tns:GetContactHistoryRequest" name="GetContactHistoryRequest" />
                <output message="tns:GetContactHistoryResponse" name="GetContactHistoryResponse" />
            </operation>
            <operation name="RegisterSupplementaryContact">
                <input message="tns:RegisterSupplementaryContactRequest" name="RegisterSupplementaryContactRequest" />
                <output message="tns:RegisterSupplementaryContactResponse" name="RegisterSupplementaryContactResponse" />
            </operation>
            <operation name="RegisterAssistance">
                <documentation>
                    Kompozitní služba OSB, která vykrývá nedostatek IB/LP, které nepracují s departmentDn pobočky a místo toho posílají tzv posId, které je identifikátorem z OBS. pracují pouze s employeeNumber - aktuální zařazení asistenta v organizační struktuře neznají. přeloží volání na parametry, které akceptuje CML zajišťuje failover volání CML / OBS součást integračního scénáře: XR-2472 Karta klienta - Příjem návštěvy na pobočce - E2E Story model proběhne tedy OK i když ne vždy musí vrátit contactId.
                </documentation>
                <input message="tns:RegisterAssistanceRequest" name="RegisterAssistanceRequest" />
                <output message="tns:RegisterAssistanceResponse" name="RegisterAssistanceResponse" />
            </operation>
            <operation name="AddExternalRelations">
                <documentation>
                    Add list of external relations to existing contact.
                </documentation>
                <input message="tns:AddExternalRelationsRequest" name="AddExternalRelationsRequest" />
                <output message="tns:AddExternalRelationsResponse" name="AddExternalRelationsResponse" />
            </operation>
            <operation name="AddDefaultBusinessSummary">
                <documentation>
                    Operation adds default business summary for operator to concept. Following attributes are always mandatory: - conceptContactId - defaultOperatorBusinessSummary Response fault codes: - Contact.DoesNotExist - specified concept was not found in CML DB
                </documentation>
                <input message="tns:AddDefaultBusinessSummaryRequest" name="AddDefaultBusinessSummaryRequest" />
                <output message="tns:AddDefaultBusinessSummaryResponse" name="AddDefaultBusinessSummaryResponse" />
            </operation>
            <operation name="GetUnansweredCalls">
                <documentation>
                    Operation returns unestablished calls for specified filters. Following attributes are always mandatory: person dialedFrom Response: - collection of unanswered calls
                </documentation>
                <input message="tns:GetUnansweredCallsRequest" name="GetUnansweredCallsRequest" />
                <output message="tns:GetUnansweredCallsResponse" name="GetUnansweredCallsResponse" />
            </operation>

            <operation name="RegisterContactAlert">
                <documentation>
                    Operation save alert message to another contact in contact history (email / ib contact / call) include contact relations. Following attributes are always mandatory: - predecessor - alertContact Response: - contactId Response fault codes: - Contact.PredecessorNotFromBankDirection - the predecessor of alert contact is not outgoing contact - Contact.PredecessorNotSupportedChannel - the predecessor of alert contact must be IB, EMAIL or CALL - Contact.PredecessorNotCorrectlyProcessed - the predecessor of alert contact is not sent or call is not connected - Contact.MessageBodyTooShort - sms text must contains at least three characters
                </documentation>
                <input message="tns:RegisterContactAlertRequest" name="RegisterContactAlertRequest" />
                <output message="tns:RegisterContactAlertResponse" name="RegisterContactAlertResponse" />
                <fault message="tns:RegisterContactAlertFaultMessage" name="RegisterContactAlertFault" />
            </operation>
            <operation name="GetUnansweredCallDetail">
                <documentation>
                    Operation returns unestablished call for the given contact ID. Following attributes are always mandatory: - contactId Response: - unanswered call Response fault codes: - Contact.DoesNotExist - contact does not exist - Contact.NotUnansweredCall - contact is not unanswered call
                </documentation>
                <input message="tns:GetUnansweredCallDetailRequest" name="GetUnansweredCallDetailRequest" />
                <output message="tns:GetUnansweredCallDetailResponse" name="GetUnansweredCallDetailResponse" />
                <fault message="tns:GetUnansweredCallDetailFaultMessage" name="GetUnansweredCallDetailFault" />
            </operation>
            <operation name="SetMessageRead">
                <documentation>
                    Operation marks message as read, when the IB message is opened by client. Following attribute is always mandatory: - contactId Response fault codes: - Contact.DoesNotExist - specified contact was not found
                </documentation>
                <input message="tns:SetMessageReadRequest" name="SetMessageReadRequest" />
                <output message="tns:SetMessageReadResponse" name="SetMessagedReadResponse" />
            </operation>
            <operation name="RegisterUrlClick">
                <documentation>
                    Operation marks link in IB message as used, when client clicks on link. Following attribute is always mandatory: - contactId Response fault codes: - Feedback.NotExists - feedback does not exist - Feedback.ElementUrlUndefined - specified element was not defined
                </documentation>
                <input message="tns:RegisterUrlClickRequest" name="RegisterUrlClickRequest" />
                <output message="tns:RegisterUrlClickResponse" name="RegisterUrlClickResponse" />
            </operation>

            <operation name="ReceiveWebChat">
                <documentation>
                    Store received web chat to CML. Response: - contactId
                </documentation>
                <input message="tns:ReceiveWebChatRequest" name="ReceiveWebChatRequest" />
                <output message="tns:ReceiveWebChatResponse" name="ReceiveWebChatResponse" />
                <fault message="tns:ReceiveWebChatFaultMessage" name="ReceiveWebChatFault" />
            </operation>

            <operation name="ReceiveMAChat">
                <documentation>
                    Store received mobile chat to CML. Response fault codes: - WebChat.SessionId.DoesNotExist - ma chat with specified sessionId was not found - WebChat.LastTraceId.DoesNotExist - ma chat with specified lastTraceId was not found
                </documentation>
                <input message="tns:ReceiveMAChatRequest" name="ReceiveMAChatRequest" />
                <output message="tns:ReceiveMAChatResponse" name="ReceiveMAChatResponse" />
                <fault message="tns:ReceiveMAChatFaultMessage" name="ReceiveMAChatFault" />
            </operation>

            <operation name="SetWebCampaignAnswer">
                <documentation>
                    Operation stores campaign client answers for given contact or promo ad id. Following attributes are always mandatory: - cryptogram - sent - questionId - one of text, result Response fault codes: - Contact.DoesNotExist - specified contact was not found - PromoAd.DoesNotExist - specified promo ad was not found - Contact.CryptogramDecryptionFailed - decryption of cryptogram to contact id failed - Contact.TextEmpty - text of message cannot be empty - Contact.ResultEmpty - result cannot be empty - Contact.TextOrResultMandatory - one of text or result must be filled
                </documentation>
                <input message="tns:SetWebCampaignAnswerRequest" name="SetWebCampaignAnswerRequest" />
                <output message="tns:SetWebCampaignAnswerResponse" name="SetWebCampaignAnswerResponse" />
                <fault message="tns:SetWebCampaignAnswerFaultMessage" name="SetWebCampaignAnswerFault" />
            </operation>

            <operation name="AddVoiceChat">
                <documentation>
                    Operation stores voice chat transcription to existing contact. Following attributes are always mandatory: - sessionId - lastTraceId - finishContact Response fault codes: - Contact.DoesNotExist - specified contact was not found - WebChat.SessionId.DoesNotExist - cannot find web chat interaction with sessionId - WebChat.LastTraceId.DoesNotExist - cannot find web chat interaction with lastTraceId
                </documentation>
                <input message="tns:AddVoiceChatRequest" name="AddVoiceChatRequest" />
                <output message="tns:AddVoiceChatResponse" name="AddVoiceChatResponse" />
                <fault message="tns:AddVoiceChatFaultMessage" name="AddVoiceChatFault" />
            </operation>

            <operation name="AddCallConnectionId">
                <documentation>

                </documentation>
                <input message="tns:AddCallConnectionIdRequest" name="AddCallConnectionIdRequest" />
                <output message="tns:AddCallConnectionIdResponse" name="AddCallConnectionIdResponse" />
                <fault message="tns:AddCallConnectionIdFaultMessage" name="AddCallConnectionIdFault" />
            </operation>
            
            <operation name="SendSmsToPrimaryAndHistoricalMobile">
                <input message="tns:SendSmsToPrimaryAndHistoricalMobileRequest" name="SendSmsToPrimaryAndHistoricalMobileRequest" />
                <output message="tns:SendSmsToPrimaryAndHistoricalMobileResponse" name="SendSmsToPrimaryAndHistoricalMobileResponse" />
            </operation>
            <operation name="SendEmailToPrimaryAndHistoricalEmail">
                <input message="tns:SendEmailToPrimaryAndHistoricalEmailRequest" name="SendEmailToPrimaryAndHistoricalEmailRequest" />
                <output message="tns:SendEmailToPrimaryAndHistoricalEmailResponse" name="SendEmailToPrimaryAndHistoricalEmailResponse" />
            </operation>

        </portType>
        <binding name="ContactWSSoap11" type="tns:ContactWSPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <operation name="ReceiveCall">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterEstablishedFromBankCall">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterUnestablishedFromBankCall">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="AddContactBusinessSummary">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="GetContactBusinessSummary">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterFromBankFailoverContact">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterToBankFailoverContact">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="ReceiveEmail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="ReceiveIBMessage">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RevokeIBMessage">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="RevokeIBMessageFault">
                    <soap:fault name="RevokeIBMessageFault" use="literal" />
                </fault>
            </operation>
            <operation name="SendEmail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="SendSMS">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="SendIBMessage">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="SendContactAlert">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="AssignContactToCustomer">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="UnassignContactFromCustomer">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterContactFinish">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="OverrideContactRelations">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="CreateReplyOfflineConcept">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="CreateAdHocOfflineConcept">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RevokeConcept">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="GetContactDetail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="GetContactDetailFault">
                    <soap:fault name="GetContactDetailFault" use="literal" />
                </fault>
            </operation>
            <operation name="GetContactHistory">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterSupplementaryContact">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterAssistance">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="AddExternalRelations">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="AddDefaultBusinessSummary">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="GetUnansweredCalls">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

            <operation name="RegisterContactAlert">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="RegisterContactAlertFault">
                    <soap:fault name="RegisterContactAlertFault" use="literal" />
                </fault>
            </operation>
            <operation name="GetUnansweredCallDetail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="GetUnansweredCallDetailFault">
                    <soap:fault name="GetUnansweredCallDetailFault" use="literal" />
                </fault>
            </operation>
            <operation name="SetMessageRead">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="RegisterUrlClick">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

            <operation name="ReceiveWebChat">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="ReceiveWebChatFault">
                    <soap:fault name="ReceiveWebChatFault" use="literal" />
                </fault>
            </operation>

            <operation name="ReceiveMAChat">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="ReceiveMAChatFault">
                    <soap:fault name="ReceiveMAChatFault" use="literal" />
                </fault>
            </operation>

            <operation name="SetWebCampaignAnswer">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="SetWebCampaignAnswerFault">
                    <soap:fault name="SetWebCampaignAnswerFault" use="literal" />
                </fault>
            </operation>

            <operation name="AddVoiceChat">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="AddVoiceChatFault">
                    <soap:fault name="AddVoiceChatFault" use="literal" />
                </fault>
            </operation>

            <operation name="AddCallConnectionId">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
                <fault name="AddCallConnectionIdFault">
                    <soap:fault name="AddCallConnectionIdFault" use="literal" />
                </fault>
            </operation>
            
            <operation name="SendSmsToPrimaryAndHistoricalMobile">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
            <operation name="SendEmailToPrimaryAndHistoricalEmail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>

        </binding>
        <service name="ContactWSService">
            <port binding="tns:ContactWSSoap11" name="ContactWSSoap11">
                <soap:address location="http://TO-BE-SPECIFIED/cml/ws/CmlContactWS" />
            </port>
        </service>
    </definitions>
