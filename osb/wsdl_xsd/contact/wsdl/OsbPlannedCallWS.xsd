<?xml version="1.0" encoding="utf-8" ?>
    <!-- $Id: 6b307cc67afcf858a327a57874102158345709e8 $ -->
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:plcon="http://osb.airbank.cz/cml/ws/plannedCall" xmlns:comm="http://osb.airbank.cz/contact/common/dto" xmlns:con="http://osb.abank.cz/contact/" xmlns:res="http://osb.airbank.cz/contact/result/dto" targetNamespace="http://osb.airbank.cz/cml/ws/plannedCall" xmlns:creator="http://osb.abank.cz/creator">
        <xsd:import namespace="http://osb.airbank.cz/contact/common/dto" schemaLocation="../xsd/Common.xsd" />
        <xsd:import namespace="http://osb.airbank.cz/contact/result/dto" schemaLocation="../xsd/Result.xsd" />
        <xsd:import namespace="http://osb.abank.cz/contact/" schemaLocation="../xsd/ContactShared.xsd" />
        <xsd:import namespace="http://osb.abank.cz/creator" schemaLocation="../xsd/Creator.xsd" />
        <xsd:simpleType name="PlannedCallIdTO">
            <xsd:annotation>
                <xsd:documentation>
                    Planned call identification in CML. Composite attribute system,idPlannedCall must be globally unique. Source system is responsible for idPlannedCall uniqueness inside in system.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string" />
        </xsd:simpleType>
        <xsd:complexType name="RequiredCallerBaseTO">
            <xsd:annotation>
                <xsd:documentation>
                    Specific data of department which are required for execution of planned call.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="departmentQueueCode" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Code that identifies department queue
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="operatorEmployeeNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Number that identifies an employee
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="RequiredCallerBaseDetailTO">
            <xsd:annotation>
                <xsd:documentation>
                    Specific data of department which are required for execution of planned call.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="departmentQueueCode" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Code that identifies department queue
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="operatorEmployeeNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Number that identifies an employee
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="RequiredCallerDetailTO">
            <xsd:annotation>
                <xsd:documentation>
                    Required caller base data + department.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:RequiredCallerBaseDetailTO">
                    <xsd:sequence>
                        <xsd:element name="department" type="xsd:string">
                            <xsd:annotation>
                                <xsd:documentation>
                                    LDAP identification of employee department
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="RequiredCallerTO">
            <xsd:annotation>
                <xsd:documentation>
                    Required caller base data + department.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:RequiredCallerBaseTO">
                    <xsd:sequence>
                        <xsd:element name="department" type="xsd:string">
                            <xsd:annotation>
                                <xsd:documentation>
                                    LDAP identification of employee department
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="PlannedCallHolderTO">
            <xsd:annotation>
                <xsd:documentation>
                    Owner of contact
                </xsd:documentation>
            </xsd:annotation>
            <xsd:choice>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            cuid - id of client
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="phoneNumber" type="comm:PhoneNumberTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            phoneNumber - non-bank client identified by phone number
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
        </xsd:complexType>
        <xsd:complexType name="PlannedCallTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with planned call detail
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                <xsd:element name="cuid" type="xsd:long" minOccurs="0" />
                <xsd:element name="firstName" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            firstName - first name of the client
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="lastName" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            lastName - last name of the client
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="standardPhone" type="comm:PhoneNumberTO" />
                <xsd:element name="actualState" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            actualState - actual state of the planned contact
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="subject" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            subject of plannedCall
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="scheduledTime" type="plcon:ScheduledDateTimeTO" />
                <xsd:element name="changeCreation" type="plcon:StatusChangeTimeTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            created - when was the planned contact created
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="changeFinishing" type="plcon:StatusChangeTimeResultDetailTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            terminated - when was the planned contact terminated
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="requiredCaller" type="plcon:RequiredCallerDetailTO" />
                <xsd:element name="businessProcess" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            businessProcess - business process
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="communicationBatch" type="comm:CommunicationBatchBaseDetailTO" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="CampaignResultBaseTO">
            <xsd:annotation>
                <xsd:documentation>
                    result of campaign
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="text" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            text of campaign result
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="StatusChangeTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with status change information about the call contact
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="operatorType" type="comm:CreatorDiscriminator" />
                <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            employeeNumber - number that identifies an employee
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeDepartment" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            employeeDepartment - number that identifies an employee department
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="StatusChangeTimeTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with status change information about the call contact
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:StatusChangeTO">
                    <xsd:sequence>
                        <xsd:element name="creation" type="xsd:dateTime">
                            <xsd:annotation>
                                <xsd:documentation>
                                    Time of status change creation
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="StatusChangeTimeResultDetailTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with status change information about the call contact
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:StatusChangeTimeTO">
                    <xsd:sequence>
                        <xsd:element name="result" type="res:ResultTO" minOccurs="0" />
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="StatusChangeDetailTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with status change information about the call contact
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:StatusChangeTO">
                    <xsd:sequence>
                        <xsd:element name="meaning" type="xsd:string">
                            <xsd:annotation>
                                <xsd:documentation>
                                    meaning - meaning of change
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="contactId" type="comm:ContactIdTO" minOccurs="0" />
                        <xsd:element name="created" type="xsd:dateTime">
                            <xsd:annotation>
                                <xsd:documentation>
                                    created - timestamp of status change
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="reason" type="xsd:string" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>
                                    reason of plannedContact change
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="result" type="res:ResultTO" minOccurs="0" />
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="StatusChangeFinishTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with status change information for finish planned call
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="creator:CreatorTO">
                    <xsd:sequence>
                        <xsd:element name="reason" type="xsd:string">
                            <xsd:annotation>
                                <xsd:documentation>
                                    reason - the reason for the termination of the planned call
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="result" type="res:ResultTO" minOccurs="0" />
                        <xsd:element name="contactId" type="comm:ContactIdTO" minOccurs="0" />
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="StatusChangeReplanTO">
            <xsd:annotation>
                <xsd:documentation>
                    Element with status change information for replan planned call
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:StatusChangeTO">
                    <xsd:sequence>
                        <xsd:element name="reason" type="xsd:string" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>
                                    reason - the reason for rescheduling the call
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="contactId" type="comm:ContactIdTO" minOccurs="0" />
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
        <xsd:complexType name="DaySchedule">
            <xsd:sequence>
                <xsd:element name="date" type="xsd:date">
                    <xsd:annotation>
                        <xsd:documentation>
                            date - date of planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="dayTime">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="MORNING" />
                            <xsd:enumeration value="AFTERNOON" />
                            <xsd:enumeration value="EVENING" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="DateTimeSchedule">
            <xsd:sequence>
                <xsd:element name="dateTime" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            dateTime - date and time of planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="FromSchedule">
            <xsd:sequence>
                <xsd:element name="date" type="xsd:date">
                    <xsd:annotation>
                        <xsd:documentation>
                            date - date of planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="AsapSchedule">
            <xsd:annotation>
                <xsd:documentation>
                    Handle the call as soon as possible
                </xsd:documentation>
            </xsd:annotation>
        </xsd:complexType>
        <xsd:complexType name="ScheduledDateTimeBaseTO">
            <xsd:annotation>
                <xsd:documentation>
                    Specifies time when operator should create call
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:choice>
                    <xsd:element name="daySchedule" type="plcon:DaySchedule" />
                    <xsd:element name="dateTimeSchedule" type="plcon:DateTimeSchedule" />
                    <xsd:element name="fromSchedule" type="plcon:FromSchedule" />
                    <xsd:element name="asapSchedule" type="plcon:AsapSchedule" />
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>
        <xsd:complexType name="ScheduledDateTimeTO">
            <xsd:annotation>
                <xsd:documentation>
                    ScheduledDateTime specifies time when operator should create call
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexContent>
                <xsd:extension base="plcon:ScheduledDateTimeBaseTO">
                    <xsd:sequence>
                        <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>
                                    expiration - shared date of expiration
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>

        <xsd:complexType name="OriginTO">
            <xsd:annotation>
                <xsd:documentation>
                    The origin of the planned contact
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="way" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Way of contact origin
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="context" type="con:OriginContext" />
            </xsd:sequence>
        </xsd:complexType>

        <xsd:element name="CreatePlannedCallRequest">
            <xsd:annotation>
                <xsd:documentation>
                    Element with information about the planned call
                </xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="0" />
                    <xsd:element name="personPhone" type="comm:PhoneNumberTO" />
                    <xsd:element name="firstName" type="xsd:string" minOccurs="0" />
                    <xsd:element name="lastName" type="xsd:string" minOccurs="0" />
                    <xsd:element name="email" type="xsd:string" minOccurs="0" />
                    <xsd:element name="creator" type="comm:CreatorDiscriminator" />
                    <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0" />
                    <xsd:element name="employeeDepartmentDn" type="xsd:string" minOccurs="0" />
                    <xsd:element name="contactId" type="comm:ContactIdTO" minOccurs="0" />
                    <xsd:element name="schedule" type="plcon:ScheduledDateTimeTO" />
                    <xsd:element name="requiredCaller" type="plcon:RequiredCallerBaseTO" />
                    <xsd:element name="origin" type="plcon:OriginTO" />
                    <xsd:element name="subject" type="xsd:string" />
                    <xsd:element name="message" type="xsd:string" minOccurs="0" />
                    <xsd:element name="externalRelations" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="CreatePlannedCallResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="RevokePlannedCallRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                    <xsd:element name="statusChange" type="plcon:StatusChangeTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="RevokePlannedCallResponse">
            <xsd:complexType>
                <xsd:sequence/>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="FinishPlannedCallRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                    <xsd:element name="statusChange" type="plcon:StatusChangeFinishTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="FinishPlannedCallResponse">
            <xsd:complexType>
                <xsd:sequence/>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="ReplanPlannedCallRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                    <xsd:element name="schedule" type="plcon:ScheduledDateTimeBaseTO" />
                    <xsd:element name="statusChange" type="plcon:StatusChangeReplanTO" />
                    <xsd:element name="standardPhone" type="comm:PhoneNumberTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="ReplanPlannedCallResponse">
            <xsd:complexType>
                <xsd:sequence/>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="GetPlannedCallsRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="contactHolder" type="plcon:PlannedCallHolderTO" minOccurs="0" />
                    <xsd:element name="service" type="xsd:string" minOccurs="0" />
                    <xsd:element name="departmentQueueCode" type="xsd:string" minOccurs="0" />
                    <xsd:element name="processingStatus" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:documentation>
                                status - list of statuses, linked together with clause IN
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="externalRelation" type="comm:ExternalRelationBaseTO" minOccurs="0" />
                    <xsd:element name="excludeCreationContactId" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="GetPlannedCallsResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCall" type="plcon:PlannedCallTO" minOccurs="0" maxOccurs="unbounded" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="GetPlannedCallDetailRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="GetPlannedCallDetailResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                    <xsd:element name="cuid" type="xsd:long" minOccurs="0" />
                    <xsd:element name="firstName" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                firstName - first name of the client
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="lastName" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                lastName - last name of the client
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="actualState" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>
                                actualState - actual state of the planned contact
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="statusChangeDetail" type="plcon:StatusChangeDetailTO" maxOccurs="unbounded" />
                    <xsd:element name="scheduledTime" type="plcon:ScheduledDateTimeTO" />
                    <xsd:element name="requiredCaller" type="plcon:RequiredCallerDetailTO" />
                    <xsd:element name="origin" type="plcon:OriginTO" />
                    <xsd:element name="message" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>
                                message - campaign message
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="subject" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>
                                subject - campaign subject
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="externalRelations" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="phoneNumber" type="comm:PhoneNumberTO" />
                    <xsd:element name="email" type="xsd:string" minOccurs="0" />
                    <xsd:element name="communicationBatch" type="comm:CommunicationBatchDetailTO" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="CreateWebCallbackRequest">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>CIF identifier</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="personPhone" type="comm:PhoneNumberTO" />
                    <xsd:element name="email" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>email - for mortgage callback is always mandatory</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="firstName" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>firstName - first name of the client</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="lastName" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>lastName - last name of the client</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="schedule" type="plcon:ScheduledDateTimeTO" />
                    <xsd:element name="message" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>message - unstructured text</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="domainOfInterest" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>domainOfInterest - domain of interest of the client</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="campaignId" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>campaignId - campaign identifier</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="applicationId" type="xsd:long" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>applicationId - application identifier</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="CreateWebCallbackResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="plannedCallId" type="plcon:PlannedCallIdTO" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
