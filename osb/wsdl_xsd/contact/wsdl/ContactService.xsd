<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://osb.banka.hci/ContactService/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/ContactService/"
	xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

	<xsd:complexType name="ContactBase" abstract="true">
		<xsd:sequence>
			<xsd:element name="typeCode" type="xsd:string" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>
						kód typu kontaktu
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="subject" type="xsd:string" minOccurs="1" maxOccurs="1" />
			<xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>
						CUID klienta
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="idApplication" type="xsd:long" minOccurs="0" maxOccurs="1" />
			<xsd:element name="idMessage" type="xsd:string" minOccurs="1" maxOccurs="1" />
			<xsd:element name="system" type="xsd:string" minOccurs="1" maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>
						source system requesting sending message (OBS,LCS,SAS)
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<xsd:element name="SendEmailWithContactRequest">
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="tns:ContactBase">
					<xsd:sequence>
						<xsd:element name="recipient" type="xsd:string" minOccurs="1" maxOccurs="1">
							<xsd:annotation>
								<xsd:documentation>email address of the recipient</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="content" type="tns:content" minOccurs="1" maxOccurs="1">
							<xsd:annotation>
								<xsd:documentation>email content</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="attachments" minOccurs="0">
							<xsd:complexType>
								<xsd:sequence>
									<xsd:element name="attachment" minOccurs="1" maxOccurs="unbounded" type="tns:attachment"></xsd:element>
								</xsd:sequence>
							</xsd:complexType>
						</xsd:element>
					</xsd:sequence>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>

	<xsd:complexType name="content">
		<xsd:sequence>
			<xsd:element name="contentType" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>content type (text/plain, text/html, image/jpg etc.)</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="content" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>content of the attachment. base64 encoded email attachment. pictures, pdf, etc.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<xsd:complexType name="attachment">
		<xsd:sequence>
			<xsd:element name="contentType" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>mime type of the content</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="content" type="xsd:base64Binary">
				<xsd:annotation>
					<xsd:documentation>content of the attachment. base64 encoded email attachment. pictures, pdf, etc.
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="attachmentName" type="xsd:string">
				<xsd:annotation>
					<xsd:documentation>name of the attachment</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<xsd:element name="SendEmailWithContactResponse">
		<xsd:complexType>
			<xsd:sequence />
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="SendSMSWithContactResponse">
		<xsd:complexType>
			<xsd:sequence />
		</xsd:complexType>
	</xsd:element>

	<xsd:element name="SendSMSWithContactRequest">
		<xsd:complexType>
			<xsd:complexContent>
				<xsd:extension base="tns:ContactBase">
					<xsd:sequence>
						<xsd:element name="phoneNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
							<xsd:annotation>
								<xsd:documentation>sms phone numberk</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="message" type="xsd:string" minOccurs="1" maxOccurs="1">
							<xsd:annotation>
								<xsd:documentation>sms message</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:sequence>
				</xsd:extension>
			</xsd:complexContent>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
