<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/ContactService/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ContactService" targetNamespace="http://osb.banka.hci/ContactService/">
	<wsdl:types>
		<xsd:schema targetNamespace="http://osb.banka.hci/ContactService/" elementFormDefault="qualified">
			<xsd:include schemaLocation="ContactService.xsd" />
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="SendEmailWithContactRequest">
		<wsdl:part element="tns:SendEmailWithContactRequest" name="SendEmailWithContactRequest" />
	</wsdl:message>
	<wsdl:message name="SendEmailWithContactResponse">
		<wsdl:part element="tns:SendEmailWithContactResponse" name="SendEmailWithContactResponse" />
	</wsdl:message>
	<wsdl:message name="SendSMSWithContactRequest">
		<wsdl:part element="tns:SendSMSWithContactRequest" name="SendSMSWithContactRequest" />
	</wsdl:message>
	<wsdl:message name="SendSMSWithContactResponse">
		<wsdl:part element="tns:SendSMSWithContactResponse" name="SendSMSWithContactResponse" />
	</wsdl:message>

	<wsdl:portType name="ContactService">
		<wsdl:operation name="SendEmailWithContact">
			<wsdl:input message="tns:SendEmailWithContactRequest" />
			<wsdl:output message="tns:SendEmailWithContactResponse" />
		</wsdl:operation>
		<wsdl:operation name="SendSMSWithContact">
			<wsdl:input message="tns:SendSMSWithContactRequest" />
			<wsdl:output message="tns:SendSMSWithContactResponse" />
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="ContactServiceSOAP" type="tns:ContactService">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="SendEmailWithContact">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="SendSMSWithContact">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="ContactService">
		<wsdl:port binding="tns:ContactServiceSOAP" name="ContactServiceSOAP">
			<soap:address location="https://osb-int.target.banka.hci/System/Client/ContactWSProxy" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
