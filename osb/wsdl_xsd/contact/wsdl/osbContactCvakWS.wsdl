<?xml version="1.0" encoding="UTF-8" ?>
    <!-- $Id: 230aa1f90c0444b47f732428078fcf007f1e445f $ -->
    <definitions targetNamespace="http://osb.abank.cz/contact/cvak/" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://osb.abank.cz/contact/cvak/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
        <types>
            <xsd:schema targetNamespace="http://osb.abank.cz/contact/cvak/">
                <xsd:include schemaLocation="osbContactCvakWS.xsd" />
            </xsd:schema>
        </types>
        <message name="SendEmailRequest">
            <part element="tns:SendEmailRequest" name="SendEmailRequest" />
        </message>
        <message name="SendEmailResponse">
            <part element="tns:SendEmailResponse" name="SendEmailResponse" />
        </message>
        
        <portType name="ContactWSPort">
            <operation name="SendEmail">
                <input message="tns:SendEmailRequest" name="SendEmailRequest" />
                <output message="tns:SendEmailResponse" name="SendEmailResponse" />
            </operation>
        </portType>
        <binding name="ContactWSSoap11" type="tns:ContactWSPort">
            <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
            <operation name="SendEmail">
                <input>
                <soap:body use="literal" />
                </input>
                <output>
                    <soap:body use="literal" />
                </output>
            </operation>
        </binding>
        <service name="ContactWSService">
            <port binding="tns:ContactWSSoap11" name="ContactWSSoap11">
                <soap:address location="http://TO-BE-SPECIFIED/cml/ws/CmlContactWS" />
            </port>
        </service>
    </definitions>
