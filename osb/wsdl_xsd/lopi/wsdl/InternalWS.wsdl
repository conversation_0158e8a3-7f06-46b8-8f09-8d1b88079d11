<?xml version= '1.0' encoding= 'UTF-8' ?>
<wsdl:definitions name="InternalWS" targetNamespace="http://osb.airbank.cz/lopi/internal"
                  xmlns:tns="http://osb.airbank.cz/lopi/internal" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">

  <wsdl:types>
    <schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://osb.airbank.cz/lopi/internal"
            elementFormDefault="qualified">
      <include schemaLocation="InternalWS.xsd"/>
    </schema>
  </wsdl:types>

  <wsdl:message name="updateStateRequest">
    <wsdl:part name="updateStateRequest" element="tns:updateStateRequest"/>
  </wsdl:message>
  <wsdl:message name="updateStateResponse">
    <wsdl:part name="findResponse" element="tns:updateStateResponse"/>
  </wsdl:message>

  <wsdl:portType name="InternalPort">
    <wsdl:operation name="updateState">
      <wsdl:input message="tns:updateStateRequest"/>
      <wsdl:output message="tns:updateStateResponse"/>
    </wsdl:operation>
  </wsdl:portType>

  <wsdl:binding name="InternalSOAP" type="tns:InternalPort">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="updateState">
      <soap:operation style="document" soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>

  <wsdl:service name="InternalService">
    <wsdl:port binding="tns:InternalSOAP" name="InternalSOAP">
      <soap:address location="http://loclhost/search"/>
    </wsdl:port>
  </wsdl:service>

</wsdl:definitions>
