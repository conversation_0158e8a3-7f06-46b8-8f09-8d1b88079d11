<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- STEP2 SCT Schema, camt.029.001.03, January 22th 2019, Release November 2019 -->
<!-- STEP2 Set choice for Ustrd and Strd, each one mandatory for CR 3649, December 21th 2018 -->
<!-- STEP2 Eleven occurrences are allowed for CR 3648, Nov 29th 2018 -->
<!-- STEP2 Add costraints to Amount for CR 3534, Dec 4th 2015 -->
<!-- STEP2 Added field IntrBkSttlmAmt, July 15th 2013, related to CR 3468 -->
<!-- STEP2 Spaces Allowed in Local Instrument for CR 3404, March 23th 2012 -->
<!-- STEP2 ISODate with pattern restriction YYYY-MM-DD, January 26th 2010 -->
<!-- STEP2 ISODateTime with pattern restriction YYYY-MM-DDThh:mm:dd..., January 26th 2010 -->
<!-- STEP2 String with whiteSpace collapse and pattern restriction "\S+.*", January 26th 2010 -->
<!-- STEP2 Originator Name length limited to 70 instead of 140, May 23th 2010 -->
<!-- STEP2 Added field ClrSys, March 25th 2011, related to CR 3366 -->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:camt.029.001.03" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:iso:std:iso:20022:tech:xsd:camt.029.001.03" elementFormDefault="qualified">
	<xs:element name="Document" type="Document"/>
	<xs:complexType name="S2SCTAccountIdentification4Choice">
		<xs:sequence>
			<xs:element name="IBAN" type="IBAN2007Identifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:minInclusive value="0"/>
			<xs:fractionDigits value="2"/>
			<xs:totalDigits value="18"/>
			<xs:pattern value="[0-9]{0,15}([\.]([0-9]{0,2})){0,1}"/>
			<!--xs:pattern added on 29-11-07-->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="S2SCTCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="S2SCTCurrencyCode" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="S2SCTCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AnyBICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="BICIdentifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTBranchAndFinancialInstitutionIdentification4">
		<xs:sequence>
			<xs:element name="FinInstnId" type="S2SCTFinancialInstitutionIdentification7"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTCancellationIndividualStatus1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RJCR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CancellationStatusReason1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="PaymentCancellationRejection1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCancellationStatusReasonInformation1">
		<xs:sequence>
			<xs:element name="Orgtr" type="S2SCTPartyIdentification321"/>
			<xs:element name="Rsn" type="CancellationStatusReason1Choice"/>
			<!-- STEP2 Eleven occurrences are allowed for CR 3648, Nov 29th 2018 -->
			<xs:element name="AddtlInf" type="Max105Text" minOccurs="0" maxOccurs="11"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CaseAssignment2">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTId7"/>
			<xs:element name="Assgnr" type="S2SCTParty7Choice"/>
			<xs:element name="Assgne" type="S2SCTParty7Choice"/>
			<xs:element name="CreDtTm" type="ISODateTime"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTCashAccount16">
		<xs:sequence>
			<xs:element name="Id" type="S2SCTAccountIdentification4Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CategoryPurpose1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalCategoryPurpose1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CountryCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CreditDebitCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CRDT"/>
			<xs:enumeration value="DBIT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CreditorReferenceInformation2">
		<xs:sequence>
			<xs:element name="Tp" type="CreditorReferenceType2"/>
			<xs:element name="Ref" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CreditorReferenceType1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="S2SCTDocumentType3Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTDocumentType3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCOR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CreditorReferenceType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="CreditorReferenceType1Choice"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DateAndPlaceOfBirth">
		<xs:sequence>
			<xs:element name="BirthDt" type="ISODate"/>
			<xs:element name="PrvcOfBirth" type="Max35Text" minOccurs="0"/>
			<xs:element name="CityOfBirth" type="Max35Text"/>
			<xs:element name="CtryOfBirth" type="CountryCode"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Document">
		<xs:sequence>
			<xs:element name="RsltnOfInvstgtn" type="S2SCTResolutionOfInvestigationV03"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DocumentAdjustment1">
		<xs:sequence>
			<xs:element name="Amt" type="S2SCTCurrencyAndAmount"/>
			<xs:element name="CdtDbtInd" type="CreditDebitCode" minOccurs="0"/>
			<xs:element name="Rsn" type="Max4Text" minOccurs="0"/>
			<xs:element name="AddtlInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DocumentType5Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MSIN"/>
			<xs:enumeration value="CNFA"/>
			<xs:enumeration value="DNFA"/>
			<xs:enumeration value="CINV"/>
			<xs:enumeration value="CREN"/>
			<xs:enumeration value="DEBN"/>
			<xs:enumeration value="HIRI"/>
			<xs:enumeration value="SBIN"/>
			<xs:enumeration value="CMCN"/>
			<xs:enumeration value="SOAC"/>
			<xs:enumeration value="DISP"/>
			<xs:enumeration value="BOLD"/>
			<xs:enumeration value="VCHR"/>
			<xs:enumeration value="AROI"/>
			<xs:enumeration value="TSUT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalCategoryPurpose1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- Spaces Allowed in Local Instrument, 2012-03-23 -->
	<xs:simpleType name="ExternalLocalInstrument1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
			<!--	<xs:pattern value="\S+.*"/>	-->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalOrganisationIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalPersonIdentification1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTExternalServiceLevel1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SEPA"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTFinancialInstitutionIdentification7">
		<xs:sequence>
			<xs:element name="BIC" type="BICIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericOrganisationIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice" minOccurs="0"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericPersonIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text"/>
			<xs:element name="SchmeNm" type="PersonIdentificationSchemeName1Choice" minOccurs="0"/>
			<xs:element name="Issr" type="Max35Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="IBAN2007Identifier">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="[0-9]{4,4}\-[0-9]{2,2}\-[0-9]{2,2}[T][0-9]{2,2}:[0-9]{2,2}:[0-9]{2,2}[\S]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTInvestigationExecutionConfirmation3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RJCR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTInvestigationStatus2Choice">
		<xs:sequence>
			<xs:element name="Conf" type="S2SCTInvestigationExecutionConfirmation3Code"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Spaces Allowed in Local Instrument, 2012-03-23 -->
	<xs:complexType name="S2SCTLocalInstrument2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalLocalInstrument1Code"/>
			<xs:element name="Prtry" type="S2SCTId8"/>
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Max105Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="105"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max140Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="140"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="35"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max4Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max70Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
			<xs:whiteSpace value="collapse"/>
			<xs:pattern value="\S+.*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTOrganisationIdentification41">
		<xs:sequence>
			<xs:choice>
				<xs:element name="BICOrBEI" type="AnyBICIdentifier" minOccurs="0"/>
				<xs:element name="Othr" type="GenericOrganisationIdentification1" minOccurs="0"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOrganisationIdentification4">
		<xs:sequence>
			<xs:element name="BICOrBEI" type="AnyBICIdentifier"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganisationIdentificationSchemeName1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTOrgnlMsgNmId">
		<xs:restriction base="xs:string">
			<xs:pattern value="pacs\.008[A-Za-z0-9\.]{0,27}"/>
			<xs:pattern value="PACS\.008[A-Za-z0-9\.]{0,27}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="S2SCTId7">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|']){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="OriginalGroupInformation3">
		<xs:sequence>
			<xs:element name="OrgnlMsgId" type="S2SCTId7"/>
			<xs:element name="OrgnlMsgNmId" type="S2SCTOrgnlMsgNmId"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTOriginalTransactionReference13">
		<xs:sequence>
			<!--  Added field IntrBkSttlmAmt, July 15th 2013, related to CR 3468 -->
			<xs:element name="IntrBkSttlmAmt">
				<xs:complexType>
					<xs:simpleContent>
						<xs:restriction base="S2SCTCurrencyAndAmount">
							<xs:maxInclusive value="999999999.99"/>
							<xs:minInclusive value="0.01"/>
						</xs:restriction>
						<!-- STEP2 Add costraints to Amount for CR 3534, Dec 4th 2015 -->
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="IntrBkSttlmDt" type="ISODate"/>
			<xs:element name="SttlmInf" type="S2SCTSettlementInformation13"/>
			<xs:element name="PmtTpInf" type="S2SCTPaymentTypeInformation22"/>
			<xs:element name="RmtInf" type="S2SCTRemittanceInformation5" minOccurs="0"/>
			<xs:element name="UltmtDbtr" type="S2SCTPartyIdentification322" minOccurs="0"/>
			<xs:element name="Dbtr" type="S2SCTPartyIdentification32"/>
			<xs:element name="DbtrAcct" type="S2SCTCashAccount16"/>
			<xs:element name="DbtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4"/>
			<xs:element name="CdtrAgt" type="S2SCTBranchAndFinancialInstitutionIdentification4"/>
			<xs:element name="Cdtr" type="S2SCTPartyIdentification32"/>
			<xs:element name="CdtrAcct" type="S2SCTCashAccount16"/>
			<xs:element name="UltmtCdtr" type="S2SCTPartyIdentification322" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty6Choice2">
		<xs:sequence>
			<xs:choice>
				<xs:element name="OrgId" type="S2SCTOrganisationIdentification41"/>
				<xs:element name="PrvtId" type="S2SCTPersonIdentification5"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty6Choice">
		<xs:sequence>
			<xs:element name="OrgId" type="S2SCTOrganisationIdentification4"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTParty7Choice">
		<xs:sequence>
			<xs:element name="Agt" type="S2SCTBranchAndFinancialInstitutionIdentification4"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification321">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Nm" type="Max70Text"/>
				<xs:element name="Id" type="S2SCTParty6Choice"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification32">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty6Choice2" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPartyIdentification322">
		<xs:sequence>
			<xs:element name="Nm" type="Max70Text" minOccurs="0"/>
			<xs:element name="PstlAdr" type="S2SCTPostalAddress6" minOccurs="0"/>
			<xs:element name="Id" type="S2SCTParty6Choice2" minOccurs="0"/>
			<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- 2010-03-29 Code AGNT removed -->
	<xs:simpleType name="PaymentCancellationRejection1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LEGL"/>
			<xs:enumeration value="CUST"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="S2SCTPaymentTransactionInformation33">
		<xs:sequence>
			<xs:element name="CxlStsId" type="S2SCTId7"/>
			<xs:element name="OrgnlGrpInf" type="OriginalGroupInformation3"/>
			<xs:element name="OrgnlInstrId" type="S2SCTId7" minOccurs="0"/>
			<xs:element name="OrgnlEndToEndId" type="Max35Text"/>
			<xs:element name="OrgnlTxId" type="S2SCTId7"/>
			<xs:element name="TxCxlSts" type="S2SCTCancellationIndividualStatus1Code"/>
			<xs:element name="CxlStsRsnInf" type="S2SCTCancellationStatusReasonInformation1"/>
			<xs:element name="Assgnr" type="S2SCTParty7Choice" minOccurs="0"/>
			<xs:element name="OrgnlTxRef" type="S2SCTOriginalTransactionReference13"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPaymentTypeInformation22">
		<xs:sequence>
			<xs:element name="SvcLvl" type="S2SCTServiceLevel8Choice"/>
			<xs:element name="LclInstrm" type="S2SCTLocalInstrument2Choice" minOccurs="0"/>
			<xs:element name="CtgyPurp" type="CategoryPurpose1Choice" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPersonIdentification5">
		<xs:sequence>
			<xs:choice>
				<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth" minOccurs="0"/>
				<xs:element name="Othr" type="GenericPersonIdentification1" minOccurs="0"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentificationSchemeName1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTPostalAddress6">
		<xs:sequence>
			<xs:element name="Ctry" type="CountryCode" minOccurs="0"/>
			<xs:element name="AdrLine" type="Max70Text" minOccurs="0" maxOccurs="2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentInformation3">
		<xs:sequence>
			<xs:element name="Tp" type="S2SCTReferredDocumentType2" minOccurs="0"/>
			<xs:element name="Nb" type="Max35Text" minOccurs="0"/>
			<xs:element name="RltdDt" type="ISODate" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReferredDocumentType1Choice">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Cd" type="DocumentType5Code"/>
				<xs:element name="Prtry" type="Max35Text"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTReferredDocumentType2">
		<xs:sequence>
			<xs:element name="CdOrPrtry" type="ReferredDocumentType1Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceAmount1">
		<xs:sequence>
			<xs:element name="DuePyblAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="DscntApldAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="CdtNoteAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="TaxAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
			<xs:element name="AdjstmntAmtAndRsn" type="DocumentAdjustment1" minOccurs="0"/>
			<xs:element name="RmtdAmt" type="S2SCTCurrencyAndAmount" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTRemittanceInformation5">
		<xs:choice>
			<!-- STEP2 Set choice for Ustrd and Strd, each one mandatory for CR 3649, December 21th 2018 -->
			<xs:element name="Ustrd" type="Max140Text"/>
			<xs:element name="Strd" type="S2SCTStructuredRemittanceInformation7"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="S2SCTResolutionOfInvestigationV03">
		<xs:sequence>
			<xs:element name="Assgnmt" type="CaseAssignment2"/>
			<xs:element name="Sts" type="S2SCTInvestigationStatus2Choice"/>
			<xs:element name="CxlDtls" type="S2SCTUnderlyingTransaction3"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTServiceLevel8Choice">
		<xs:sequence>
			<xs:element name="Cd" type="S2SCTExternalServiceLevel1Code"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTSettlementInformation13">
		<xs:sequence>
			<xs:element name="SttlmMtd" type="S2SCTSettlementMethod1Code"/>
			<!-- Add Clearing system, March 25th 2011, related to CR 3366 -->
			<xs:element name="ClrSys" type="S2SCTClearingSystemIdentification1Choice"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTClearingSystemIdentification1Choice">
		<xs:sequence>
			<xs:element name="Prtry" type="Max35Text"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="S2SCTSettlementMethod1Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CLRG"/>
		</xs:restriction>
		<!-- 20100222 Clearing System Removed  -->
	</xs:simpleType>
	<xs:complexType name="S2SCTStructuredRemittanceInformation7">
		<xs:sequence>
			<xs:element name="RfrdDocInf" type="S2SCTReferredDocumentInformation3" minOccurs="0"/>
			<xs:element name="RfrdDocAmt" type="S2SCTRemittanceAmount1" minOccurs="0"/>
			<xs:element name="CdtrRefInf" type="CreditorReferenceInformation2" minOccurs="0"/>
			<xs:element name="Invcr" type="S2SCTPartyIdentification32" minOccurs="0"/>
			<xs:element name="Invcee" type="S2SCTPartyIdentification32" minOccurs="0"/>
			<xs:element name="AddtlRmtInf" type="Max140Text" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="S2SCTUnderlyingTransaction3">
		<xs:sequence>
			<xs:element name="TxInfAndSts" type="S2SCTPaymentTransactionInformation33" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Added new type S2SCTId8 on 2012-03-23-->
	<xs:simpleType name="S2SCTId8">
		<xs:restriction base="xs:string">
			<xs:pattern value="([A-Za-z0-9]|[+|\?|/|\-|:|\(|\)|\.|,|'|\s]){1,35}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
