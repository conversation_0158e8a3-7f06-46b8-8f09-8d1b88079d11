<?xml version="1.0" encoding="windows-1250" ?>
    <!-- $Id: ae27b8e5eb55dc8822995b9d6d2b57b0ebbe959b $ -->
    <xsd:schema targetNamespace="http://osb.airbank.cz/endpoint/" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.airbank.cz/endpoint/">
        <xsd:element name="getApplicationBaseEndpointRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="application" type="tns:Application" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getApplicationBaseEndpointResponse" type="tns:uri" />
        <xsd:element name="getApplicationURIRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence/>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getApplicationURIResponse" type="tns:uri" />
        <xsd:element name="getBusinessSummaryURIRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:choice>
                        <xsd:element name="contactId" type="xsd:string" />
                        <xsd:element name="departmentDn" type="xsd:string" />
                    </xsd:choice>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getBusinessSummaryURIResponse" type="tns:uri" />
        <xsd:element name="getCustomerURIRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="contactId" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCustomerURIResponse" type="tns:uri" />
        <xsd:element name="getLeadPagesURIRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="employeeNumber" type="xsd:string" />
                    <xsd:element name="employeeName" type="xsd:string" />
                    <xsd:element name="callId" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getLeadPagesURIResponse" type="tns:uri" />
        <xsd:element name="getRequestDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="requestId" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getRequestDetailURIResponse" type="tns:uri" />
        <xsd:element name="getSubjectRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getSubjectResponse" type="tns:uri" />

        <xsd:element name="getEntrepreneurURIRequest">
            <xsd:annotation>
                <xsd:documentation>requested application URI name</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getEntrepreneurURIResponse" type="tns:uri" />

        <xsd:element name="getEntrepreneurDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>collection URI for requested cuid</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getEntrepreneurDetailURIResponse" type="tns:uri" />
        
        <xsd:simpleType name="Application">
            <xsd:restriction base="xsd:string">
                <xsd:enumeration value="OBS/WO" />
                <xsd:enumeration value="IB" />
                <xsd:enumeration value="AMG" />
                <xsd:enumeration value="CSP" />
                <xsd:enumeration value="LCS" />
            </xsd:restriction>
        </xsd:simpleType>
        <xsd:element name="getCollectionURIRequest">
            <xsd:annotation>
                <xsd:documentation>collection URI for requested cuid</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCollectionURIResponse" type="tns:uri" />
        <xsd:element name="getClientDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>collection URI for requested cuid</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="cuid" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getClientDetailURIResponse" type="tns:uri" />
        <xsd:element name="getLoanDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>loan detail URI for requested loan number</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="loanNumber" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getLoanDetailURIResponse" type="tns:uri" />
        <xsd:element name="getMortgageDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>Mortgage loan detail URI for requested loan number</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="mortgageNumber" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getMortgageDetailURIResponse" type="tns:uri" />
        <xsd:element name="getCompletionDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>completion detail URI for requested id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="completionId" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getCompletionDetailURIResponse" type="tns:uri" />
        <xsd:element name="getApplicationDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>application detail URI for requested id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="applicationId" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getApplicationDetailURIResponse" type="tns:uri" />
        <xsd:element name="getEnvelopeDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>envelope detail URI for requested id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="envelopeId" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getEnvelopeDetailURIResponse" type="tns:uri" />
        <xsd:element name="getComplaintDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>jira complaint detail URI for ticket id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="ticketId" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getComplaintDetailURIResponse" type="tns:uri" />
        <xsd:element name="getComplaintCommentURIRequest">
            <xsd:annotation>
                <xsd:documentation>jira complaint comment URI for ticket and comment id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="ticketId" type="xsd:string" />
                    <xsd:element name="commentId" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getRemissionOfClaimURIRequest">
            <xsd:annotation>
                <xsd:documentation>jira Remission of claim URI for ticket id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="personName" type="xsd:string" />
                    <xsd:element name="personSurname" type="xsd:string" />
                    <xsd:element name="cuid" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getRemissionOfClaimURIResponse" type="tns:uri" />
        <xsd:element name="getRemissionOfClaimDetailURIRequest">
            <xsd:annotation>
                <xsd:documentation>Business Jira URI</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getRemissionOfClaimDetailURIResponse">
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="uri" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getComplaintCommentURIResponse" type="tns:uri" />
        <xsd:element name="getZonkyUserUriRequest">
            <xsd:annotation>
                <xsd:documentation>collection URI for requested cuid</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="userId" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getZonkyUserUriResponse" type="tns:uri" />
        <xsd:element name="getContractURIRequest">
            <xsd:annotation>
                <xsd:documentation>collection URI for requested id</xsd:documentation>
            </xsd:annotation>
            <xsd:complexType>
                <xsd:sequence>
                    <xsd:element name="generalContractId" type="xsd:long" />
                </xsd:sequence>
            </xsd:complexType>
        </xsd:element>
        <xsd:element name="getContractURIResponse" type="tns:uri" />

        <xsd:complexType name="uri">
            <xsd:sequence>
                <xsd:element name="uri" type="xsd:anyURI">
                    <xsd:annotation>
                        <xsd:documentation>requested URI</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>

    </xsd:schema>
