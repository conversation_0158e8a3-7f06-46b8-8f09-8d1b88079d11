<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://mdes.abank.cz/mpamanagement/common"
            targetNamespace="http://mdes.abank.cz/mpamanagement/common" elementFormDefault="qualified">
  <xsd:complexType name="BasicEntity" abstract="true">
    <xsd:sequence>
      <xsd:element name="responseHost" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ResponseEntity" abstract="true">
    <xsd:complexContent>
    <xsd:extension base="BasicEntity">
     <xsd:sequence>
      <xsd:element name="responseId" type="xsd:string"/>
      <xsd:element name="errors" type="Error" minOccurs="0" maxOccurs="unbounded"/>
     </xsd:sequence>
    </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="RequestEntity" abstract="true">
    <xsd:complexContent>
    <xsd:extension base="BasicEntity">
     <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string"/>
      <xsd:element name="destination" type="xsd:string" minOccurs="0"/>
     </xsd:sequence>
    </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="Response" abstract="true">
    <xsd:complexContent>
      <xsd:extension base="ResponseEntity">
        <xsd:sequence>
          <xsd:element name="tokens" maxOccurs="unbounded" type="Token"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="Request" abstract="true">
    <xsd:complexContent>
      <xsd:extension base="RequestEntity">
        <xsd:sequence>
          <xsd:element name="tokenUniqueReferences" maxOccurs="unbounded" type="xsd:string"/>
          <xsd:element name="causedBy" type="CausedBy"/>
          <xsd:element name="reasonCode" type="ReasonCodeClient"/>
          <xsd:element name="reason" type="xsd:string"/>
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="Token">
    <xsd:sequence>
      <xsd:element name="tokenUniqueReference" type="xsd:string"/>
      <xsd:element name="status" minOccurs="0" type="xsd:string"/>
      <xsd:element name="suspendedBy" minOccurs="0" type="xsd:string"/>
      <xsd:element name="errorCode" type="xsd:string"/>
      <xsd:element name="errorDescription" type="xsd:string"/>
      <xsd:element name="errors" type="Error" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Error">
    <xsd:sequence>
      <xsd:element name="source" type="xsd:string"/>
      <xsd:element name="errorCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="reasonCode" type="xsd:string"/>
      <xsd:element name="description" type="xsd:string"/>
      <xsd:element name="recoverable" type="xsd:boolean" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="TokenType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="EMBEDDED_SE"/>
      <xsd:enumeration value="CLOUD"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NotEligibleReasons">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="OS_NOT_SUPPORTED"/>
      <xsd:enumeration value="OS_VERSION_NOT_SUPPORTED"/>
      <xsd:enumeration value="DEVICE_TYPE_NOT_SUPPORTED"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReasonCodeClient">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="DEVICE_LOST"/>
      <xsd:enumeration value="DEVICE_STOLEN"/>
      <xsd:enumeration value="ACCOUNT_CLOSED"/>
      <xsd:enumeration value="SUSPECTED_FRAUD"/>
      <xsd:enumeration value="OTHER"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CausedBy">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="CARDHOLDER"/>
      <xsd:enumeration value="TOKEN_REQUESTOR"/>
      <xsd:enumeration value="PAYMENT_APP_PROVIDER"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DecisionReason">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="APPROVED"/>
      <xsd:enumeration value="DECLINED"/>
      <xsd:enumeration value="REQUIRE_ADDITIONAL_AUTHENTICATION"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
