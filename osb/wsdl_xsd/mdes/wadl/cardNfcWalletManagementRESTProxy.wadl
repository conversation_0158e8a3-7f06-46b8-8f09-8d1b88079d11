<?xml version = '1.0' encoding = 'UTF-8'?>
<application xmlns:soa="http://www.oracle.com/soa/rest" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="http://xmlns.oracle.com/OSB-rewrite/MDES/CardNfcWalletManagementRESTProxy" xmlns:ns0="http://osb.airbank.cz/mdes/card/nfc/wallet" xmlns="http://wadl.dev.java.net/2009/02">
   <doc title="CardNfcWalletManagementRESTProxy">CardNfcWalletManagement</doc>
   <grammars>
      <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://osb.airbank.cz/mdes/card/nfc/wallet" schemaLocation="../xsd/cardNfcWalletManagementRESTProxy.xsd"/>
        </xsd:schema>
   </grammars>
   <resources>
      <resource path="/notifyTokenUpdated">
         <method name="POST" soa:wsdlOperation="notifyTokenUpdated">
            <request>
               <representation mediaType="application/json" element="cns:notifyTokenUpdatedRequest" xmlns:cns="http://osb.airbank.cz/mdes/card/nfc/wallet"/>
            </request>
            <response status="200">
               <representation mediaType="application/json" element="cns:notifyTokenUpdatedResponse" xmlns:cns="http://osb.airbank.cz/mdes/card/nfc/wallet"/>
            </response>
         </method>
      </resource>
   </resources>
</application>
