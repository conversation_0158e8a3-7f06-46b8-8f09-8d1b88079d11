<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://mdes.abank.cz/mpamanagement/"
            targetNamespace="http://mdes.abank.cz/mpamanagement/" elementFormDefault="qualified"
            xmlns:comm="http://mdes.abank.cz/mpamanagement/common">
  <xsd:import schemaLocation="../xsd/MDESCommon.xsd" namespace="http://mdes.abank.cz/mpamanagement/common"/>          
  <xsd:element name="RegisterRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:RequestEntity">
          <xsd:sequence>
            <xsd:element name="paymentAppId" type="xsd:string"/>
            <xsd:element name="paymentAppInstanceId" type="xsd:string"/>
            <xsd:element name="rnsInfo" type="RnsInfo"/>
            <xsd:element name="publicKeyFingerprint" type="xsd:base64Binary"/>
            <xsd:element name="rgk" type="xsd:base64Binary"/>
            <xsd:element name="deviceFingerprint" type="xsd:base64Binary"/>
            <xsd:element name="newMobilePin" type="xsd:base64Binary"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RegisterResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:ResponseEntity">
          <xsd:sequence>
            <xsd:element name="mobileKeysetId" type="xsd:string"/>
            <xsd:element name="mobileKeys" type="MobileKey"/>
            <xsd:element name="remoteManagementUrl" type="xsd:string"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PkCertificateRequest">
    <xsd:complexType>
      <xsd:sequence>
        <!-- empty sequence -->
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PkCertificateResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="pkixCertData" type="xsd:base64Binary"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="SetMobilePinRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:RequestEntity">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string"/>
            <xsd:element name="newMobilePin" type="xsd:base64Binary"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SetMobilePinResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:ResponseEntity">
          <xsd:sequence>
            <!-- -->
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UnregisterRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:RequestEntity">
          <xsd:sequence>
            <xsd:element name="paymentAppInstanceId" type="xsd:string"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UnregisterResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="comm:ResponseEntity">
          <xsd:sequence>
            <!-- -->
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="MobileKey">
    <xsd:sequence>
      <xsd:element name="transportKey" type="xsd:base64Binary"/>
      <xsd:element name="macKey" type="xsd:base64Binary"/>
      <xsd:element name="dataEncryptionKey" type="xsd:base64Binary"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RnsInfo">
    <xsd:sequence>
      <xsd:element name="gcmRegistrationId" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  </xsd:schema>