<wsdl:definitions name="CardNfcWalletManagementRESTProxy" targetNamespace="http://xmlns.oracle.com/OSB-rewrite/MDES/CardNfcWalletManagementRESTProxy" xmlns:tns="http://xmlns.oracle.com/OSB-rewrite/MDES/CardNfcWalletManagementRESTProxy" xmlns:inp1="http://osb.airbank.cz/mdes/card/nfc/wallet" xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">
    <plnk:partnerLinkType name="CardNfcWalletManagementRESTProxy">
        <plnk:role name="CardNfcWalletManagementRESTProxyProvider" portType="tns:CardNfcWalletManagementRESTProxy_ptt"/>
    </plnk:partnerLinkType>
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import namespace="http://osb.airbank.cz/mdes/card/nfc/wallet" schemaLocation="../xsd/cardNfcWalletManagementRESTProxy.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="Rest_EmptyMessage"/>
    
    <wsdl:message name="notifyTokenUpdated_inputMessage">
      <wsdl:part name="request" element="inp1:notifyTokenUpdatedRequest"/>
    </wsdl:message>
    <wsdl:message name="notifyTokenUpdated_outputMessage">
      <wsdl:part name="reply" element="inp1:notifyTokenUpdatedResponse"/>
    </wsdl:message>
    <wsdl:portType name="CardNfcWalletManagementRESTProxy_ptt">
        <wsdl:operation name="notifyTokenUpdated">
            <wsdl:input message="tns:notifyTokenUpdated_inputMessage"/>
            <wsdl:output message="tns:notifyTokenUpdated_outputMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CardNfcWalletManagementRESTProxy_ptt-binding" type="tns:CardNfcWalletManagementRESTProxy_ptt">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="notifyTokenUpdated">
            <soap:operation soapAction="notifyTokenUpdated"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
</wsdl:definitions>