<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: 9fd8ffbecfd0f895dd6f514705038a4f9a3e74f0 $ -->
<xsd:schema targetNamespace="http://osb.airbank.cz/ms/ws/EmailManagement" elementFormDefault="qualified"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.airbank.cz/ms/ws/EmailManagement"
            xmlns:dto="http://osb.airbank.cz/ms/ws/EmailManagement/dto" xmlns:core="http://osb.airbank.cz/ms/ws/core">
  <xsd:import schemaLocation="../xsd/EmailDto.xsd" namespace="http://osb.airbank.cz/ms/ws/EmailManagement/dto"/>
  <xsd:import namespace="http://osb.airbank.cz/ms/ws/core" schemaLocation="../xsd/Core.xsd"/>
  <xsd:element name="sendEmailRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="message" minOccurs="1" maxOccurs="unbounded">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="muid" type="core:MUID">
                <xsd:annotation>
                  <xsd:documentation>message identification properties</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="emailHeader" type="dto:emailHeader">
                <xsd:annotation>
                  <xsd:documentation>email</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element name="emailBody" type="dto:emailBody">
          <xsd:annotation>
            <xsd:documentation>email</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="sendDateFrom" type="xsd:dateTime" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Specifies the date and time, when the message
							should be sent to a receiver.
						</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation> date and time at which Email sender will stop
							trying to send the Email.
						</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="sendEmailResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="core:result"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="sendEmailFault">
    <xsd:complexType>
    <xsd:sequence>
      <xsd:element name="fault" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  </xsd:element>
</xsd:schema>
