<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: 127f14f6fb6d4c4a31a3f565725ed7a2d14e63f7 $ -->
<xsd:schema targetNamespace="http://osb.airbank.cz/ms/ws/EmailManagement/dto" elementFormDefault="qualified"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.airbank.cz/ms/ws/EmailManagement/dto"
            xmlns:core="http://osb.airbank.cz/ms/ws/core">
  <xsd:import namespace="http://osb.airbank.cz/ms/ws/core" schemaLocation="Core.xsd"/>
  <xsd:complexType name="emailHeader">
    <xsd:sequence>
      <xsd:element name="recipient" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>email address of the recipient</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sender" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>email address of the sender email. if not specified, default email is picked.
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReplyTo" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Reply-To email header.
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="inReplyTo" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>In-Reply-To: Message-ID of the message that this is a reply to. Used to link related messages together. This field only applies for reply messages.
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="emailBody">
    <xsd:sequence>
      <xsd:element name="subject" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>email subject</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="content" type="tns:content" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>email content</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attachments" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="attachment" minOccurs="1" maxOccurs="unbounded" type="tns:attachment"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="uidDocuments" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="uidDocument" type="xsd:string" minOccurs="1" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="content">
    <xsd:sequence>
      <xsd:element name="contentType" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>content type (text/plain, text/html, image/jpg etc.)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="content" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>content of the attachment. base64 encoded email attachment. pictures, pdf, etc.
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="attachment">
    <xsd:sequence>
      <xsd:choice>
        <xsd:sequence>
          <xsd:element name="contentType" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>mime type of the content</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="content" type="xsd:base64Binary">
            <xsd:annotation>
              <xsd:documentation>content of the attachment. base64 encoded email attachment. pictures, pdf, etc.
              </xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="attachmentName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>name of the attachment</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
        <xsd:sequence>
          <xsd:element name="uuid" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Dms document identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:choice>
      <xsd:element name="encryptionSettings" type="tns:encryptionSettings" minOccurs="0">
          <xsd:annotation>
              <xsd:documentation>object that indicate pdf encryption. if encryption settings occur and attachment is not pdf, exception id raised
              </xsd:documentation>
          </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="encryptionSettings">
    <xsd:sequence>
      <xsd:element name="encryptionMethod" type="tns:EncryptionType" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>encryption method</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pwdValue" type="xsd:string" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>password to encrypt pdf attachment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="EncryptionType">
    <xsd:restriction base="xsd:string">
      <xsd:annotation>
        <xsd:documentation>
                    Type of encryption methods. Only AES is now supported
        </xsd:documentation>
      </xsd:annotation>
      <xsd:enumeration value="AES128"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
