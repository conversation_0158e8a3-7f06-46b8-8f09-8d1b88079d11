<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/lap-obligations" targetNamespace="http://homecredit.net/lap-obligations">

    <wsdl:types>
        <xsd:schema targetNamespace="http://homecredit.net/lap-obligations">
            <xsd:include schemaLocation="../xsd/Obligations.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="UpdatePairsResponse">
        <wsdl:part element="tns:UpdatePairsResponse" name="UpdatePairsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetCandidateObligationsResponse">
        <wsdl:part element="tns:GetCandidateObligationsResponse" name="GetCandidateObligationsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetPairsResponse">
        <wsdl:part element="tns:GetPairsResponse" name="GetPairsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetCandidateObligationsRequest">
        <wsdl:part element="tns:GetCandidateObligationsRequest" name="GetCandidateObligationsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="UpdatePairsRequest">
        <wsdl:part element="tns:UpdatePairsRequest" name="UpdatePairsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetPairsRequest">
        <wsdl:part element="tns:GetPairsRequest" name="GetPairsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetManualPairingObligationsRequest">
        <wsdl:part element="tns:GetManualPairingObligationsRequest" name="GetManualPairingObligationsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetManualPairingObligationsResponse">
        <wsdl:part element="tns:GetManualPairingObligationsResponse" name="GetManualPairingObligationsResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:portType name="ObligationsWS">
        <wsdl:operation name="UpdatePairs">
            <wsdl:input message="tns:UpdatePairsRequest" name="UpdatePairsRequest">
            </wsdl:input>
            <wsdl:output message="tns:UpdatePairsResponse" name="UpdatePairsResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCandidateObligations">
            <wsdl:input message="tns:GetCandidateObligationsRequest" name="GetCandidateObligationsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetCandidateObligationsResponse" name="GetCandidateObligationsResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPairs">
            <wsdl:input message="tns:GetPairsRequest" name="GetPairsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetPairsResponse" name="GetPairsResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetManualPairingObligations">
            <wsdl:input message="tns:GetManualPairingObligationsRequest" name="GetManualPairingObligationsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetManualPairingObligationsResponse" name="GetManualPairingObligationsResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ObligationsWSSoap11" type="tns:ObligationsWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="UpdatePairs">
            <soap:operation soapAction=""/>
            <wsdl:input name="UpdatePairsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="UpdatePairsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCandidateObligations">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetCandidateObligationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetCandidateObligationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPairs">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetPairsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetPairsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetManualPairingObligations">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetManualPairingObligationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetManualPairingObligationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ObligationsWSService">
        <wsdl:port binding="tns:ObligationsWSSoap11" name="ObligationsWSSoap11">
            <soap:address location="/ws/ObligationsWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
