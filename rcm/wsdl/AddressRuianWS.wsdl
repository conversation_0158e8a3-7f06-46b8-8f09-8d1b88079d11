<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/rcm/ws/addressruian"
                  targetNamespace="http://airbank.cz/rcm/ws/addressruian">
    <wsdl:types>
        <xs:schema xmlns="http://airbank.cz/rcm/ws/addressruian" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified"
                   elementFormDefault="qualified" targetNamespace="http://airbank.cz/rcm/ws/addressruian">
            <xs:include schemaLocation="../xsd/AddressRuianWS.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="GetRuianElementsRequest">
        <wsdl:part element="tns:GetRuianElementsRequest" name="GetRuianElementsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetRuianElementsResponse">
        <wsdl:part element="tns:GetRuianElementsResponse" name="GetRuianElementsResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:portType name="AddressRuianWS">
        <wsdl:operation name="GetRuianElements">
            <wsdl:input message="tns:GetRuianElementsRequest" name="GetRuianElementsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetRuianElementsResponse" name="GetRuianElementsResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="AddressRuianWSSoap11" type="tns:AddressRuianWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetRuianElements">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetRuianElementsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetRuianElementsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="AddressRuianWSService">
        <wsdl:port binding="tns:AddressRuianWSSoap11" name="AddressRuianWSSoap11">
            <soap:address location="/ws/AddressRuianWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>