<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/rcm/ws/uir"
                  targetNamespace="http://airbank.cz/rcm/ws/uir">
    <wsdl:types>
        <xs:schema xmlns="http://airbank.cz/rcm/ws/uir" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified"
                   elementFormDefault="qualified" targetNamespace="http://airbank.cz/rcm/ws/uir">
            <xs:include schemaLocation="../xsd/AddressUirWS.xsd"/>
        </xs:schema>
    </wsdl:types>
    <wsdl:message name="GetTownsResponse">
        <wsdl:part element="tns:GetTownsResponse" name="GetTownsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetHousesResponse">
        <wsdl:part element="tns:GetHousesResponse" name="GetHousesResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetZipCodesRequest">
        <wsdl:part element="tns:GetZipCodesRequest" name="GetZipCodesRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetZipCodesResponse">
        <wsdl:part element="tns:GetZipCodesResponse" name="GetZipCodesResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetStreetsRequest">
        <wsdl:part element="tns:GetStreetsRequest" name="GetStreetsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetHousesRequest">
        <wsdl:part element="tns:GetHousesRequest" name="GetHousesRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetStreetsResponse">
        <wsdl:part element="tns:GetStreetsResponse" name="GetStreetsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetTownsRequest">
        <wsdl:part element="tns:GetTownsRequest" name="GetTownsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetAddressFormatRequest">
        <wsdl:part element="tns:GetAddressFormatRequest" name="GetAddressFormatRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetAddressFormatResponse">
        <wsdl:part element="tns:GetAddressFormatResponse" name="GetAddressFormatResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetAddressToDisplayAttributesRequest">
        <wsdl:part element="tns:GetAddressToDisplayAttributesRequest" name="GetAddressToDisplayAttributesRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetAddressToDisplayAttributesResponse">
        <wsdl:part element="tns:GetAddressToDisplayAttributesResponse" name="GetAddressToDisplayAttributesResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:portType name="AddressUirWS">
        <wsdl:operation name="GetTowns">
            <wsdl:input message="tns:GetTownsRequest" name="GetTownsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetTownsResponse" name="GetTownsResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetHouses">
            <wsdl:input message="tns:GetHousesRequest" name="GetHousesRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetHousesResponse" name="GetHousesResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetZipCodes">
            <wsdl:input message="tns:GetZipCodesRequest" name="GetZipCodesRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetZipCodesResponse" name="GetZipCodesResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetStreets">
            <wsdl:input message="tns:GetStreetsRequest" name="GetStreetsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetStreetsResponse" name="GetStreetsResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAddressFormat">
            <wsdl:input message="tns:GetAddressFormatRequest" name="GetAddressFormatRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetAddressFormatResponse" name="GetAddressFormatResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAddressToDisplayAttributes">
            <wsdl:input message="tns:GetAddressToDisplayAttributesRequest" name="GetAddressToDisplayAttributesRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetAddressToDisplayAttributesResponse" name="GetAddressToDisplayAttributesResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="AddressUirWSSoap11" type="tns:AddressUirWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetTowns">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetTownsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetTownsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetHouses">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetHousesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetHousesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetZipCodes">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetZipCodesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetZipCodesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetStreets">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetStreetsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetStreetsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAddressFormat">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetAddressFormatRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetAddressFormatResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetAddressToDisplayAttributes">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetAddressToDisplayAttributesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetAddressToDisplayAttributesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="AddressUirWSService">
        <wsdl:port binding="tns:AddressUirWSSoap11" name="AddressUirWSSoap11">
            <soap:address location="/ws/mn/AddressUirWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>