<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/rcm/ws/brkicreditbureau" targetNamespace="http://airbank.cz/rcm/ws/brkicreditbureau">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/rcm/ws/brkicreditbureau">
            <xsd:include schemaLocation="../xsd/CzechBankingCreditBureau.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="GetPrintReportRequest">
        <wsdl:part element="tns:GetPrintReportRequest" name="GetPrintReportRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetPrintReportResponse">
        <wsdl:part element="tns:GetPrintReportResponse" name="GetPrintReportResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetClientObligationsRequest">
        <wsdl:part element="tns:GetClientObligationsRequest" name="GetClientObligationsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetClientObligationsResponse">
        <wsdl:part element="tns:GetClientObligationsResponse" name="GetClientObligationsResponse">
        </wsdl:part>
    </wsdl:message>


    <wsdl:portType name="CzechBankingCreditBureauWS">
        <wsdl:operation name="GetPrintReport">
            <wsdl:input message="tns:GetPrintReportRequest" name="GetPrintReportRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetPrintReportResponse" name="GetPrintReportResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetClientObligations">
            <wsdl:input message="tns:GetClientObligationsRequest" name="GetClientObligationsRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetClientObligationsResponse" name="GetClientObligationsResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CzechBankingCreditBureauWSSoap" type="tns:CzechBankingCreditBureauWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetPrintReport">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetPrintReportRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetPrintReportResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetClientObligations">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetClientObligationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetClientObligationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="CzechBankingCreditBureauWSService">
        <wsdl:port binding="tns:CzechBankingCreditBureauWSSoap" name="CzechBankingCreditBureauWSSoap">
            <soap:address location="/ws/CzechBankingCreditBureauWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
