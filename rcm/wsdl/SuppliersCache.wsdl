<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/rcm/suppliers" xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  targetNamespace="http://homecredit.net/rcm/suppliers">

    <wsdl:types>
        <xs:schema targetNamespace="http://homecredit.net/rcm/suppliers">
            <xs:include schemaLocation="../xsd/SuppliersCache.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="AccountNumberIdentificationRequest">
        <wsdl:part element="tns:AccountNumberIdentificationRequest" name="AccountNumberIdentificationRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="EconomicalSubjectRequest">
        <wsdl:part element="tns:EconomicalSubjectRequest" name="EconomicalSubjectRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="EconomicalSubjectResponse">
        <wsdl:part element="tns:EconomicalSubjectResponse" name="EconomicalSubjectResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="AccountNumberIdentificationResponse">
        <wsdl:part element="tns:AccountNumberIdentificationResponse" name="AccountNumberIdentificationResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="GetEconomicalSubjectsPartialRequest">
        <wsdl:part element="tns:GetEconomicalSubjectsPartialRequest" name="GetEconomicalSubjectsPartialRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetEconomicalSubjectsPartialResponse">
        <wsdl:part element="tns:GetEconomicalSubjectsPartialResponse" name="GetEconomicalSubjectsPartialResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:portType name="suppliersService">
        <wsdl:operation name="AccountNumberIdentification">
            <wsdl:input message="tns:AccountNumberIdentificationRequest" name="AccountNumberIdentificationRequest">
            </wsdl:input>
            <wsdl:output message="tns:AccountNumberIdentificationResponse" name="AccountNumberIdentificationResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EconomicalSubject">
            <wsdl:input message="tns:EconomicalSubjectRequest" name="EconomicalSubjectRequest">
            </wsdl:input>
            <wsdl:output message="tns:EconomicalSubjectResponse" name="EconomicalSubjectResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetEconomicalSubjectsPartial">
            <wsdl:input message="tns:GetEconomicalSubjectsPartialRequest" name="GetEconomicalSubjectsPartialRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetEconomicalSubjectsPartialResponse" name="GetEconomicalSubjectsPartialResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="suppliersServiceSoap11" type="tns:suppliersService">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="AccountNumberIdentification">
            <soap:operation soapAction=""/>
            <wsdl:input name="AccountNumberIdentificationRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="AccountNumberIdentificationResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EconomicalSubject">
            <soap:operation soapAction=""/>
            <wsdl:input name="EconomicalSubjectRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="EconomicalSubjectResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetEconomicalSubjectsPartial">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetEconomicalSubjectsPartialRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetEconomicalSubjectsPartialResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="suppliersServiceService">
        <wsdl:port binding="tns:suppliersServiceSoap11" name="suppliersServiceSoap11">
            <soap:address location="/suppliersService/"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
