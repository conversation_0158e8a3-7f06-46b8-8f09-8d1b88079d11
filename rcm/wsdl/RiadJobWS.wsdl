<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/rcm/ws/riadjob"
                  targetNamespace="http://airbank.cz/rcm/ws/riadjob">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/rcm/ws/riadjob">
            <xs:include schemaLocation="../xsd/RiadJob.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="ProcessNewRecordsRequest">
        <wsdl:part element="ProcessNewRecordsRequest" name="ProcessNewRecordsRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessNewRecordsResponse">
        <wsdl:part element="ProcessNewRecordsResponse" name="ProcessNewRecordsResponse"/>
    </wsdl:message>

    <wsdl:message name="ProcessWaitingRecordsRequest">
        <wsdl:part element="ProcessWaitingRecordsRequest" name="ProcessWaitingRecordsRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessWaitingRecordsResponse">
        <wsdl:part element="ProcessWaitingRecordsResponse" name="ProcessWaitingRecordsResponse"/>
    </wsdl:message>

    <wsdl:message name="ResetFailedRecordsRequest">
        <wsdl:part element="ResetFailedRecordsRequest" name="ResetFailedRecordsRequest"/>
    </wsdl:message>
    <wsdl:message name="ResetFailedRecordsResponse">
        <wsdl:part element="ResetFailedRecordsResponse" name="ResetFailedRecordsResponse"/>
    </wsdl:message>

    <wsdl:portType name="RiadJob">
        <wsdl:operation name="ProcessNewRecords">
            <wsdl:input message="ProcessNewRecordsRequest"/>
            <wsdl:output message="ProcessNewRecordsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="ProcessWaitingRecords">
            <wsdl:input message="ProcessWaitingRecordsRequest"/>
            <wsdl:output message="ProcessWaitingRecordsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="ResetFailedRecords">
            <wsdl:input message="ResetFailedRecordsRequest"/>
            <wsdl:output message="ResetFailedRecordsResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="RiadJobBinding" type="RiadJob">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="ProcessNewRecords">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ProcessWaitingRecords">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ResetFailedRecords">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="RiadJobService">
        <wsdl:port name="RiadJobPort" binding="RiadJobBinding">
            <soap:address location="http://localhost:8000/ws"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>