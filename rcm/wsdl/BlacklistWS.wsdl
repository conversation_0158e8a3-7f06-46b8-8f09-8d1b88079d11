<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:tns="http://airbank.cz/rcm/blacklistws"
                  targetNamespace="http://airbank.cz/rcm/blacklistws">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/rcm/blacklistws">
            <xsd:include schemaLocation="../xsd/BlacklistWS.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="RunOFACUpdateRequest">
        <wsdl:part element="tns:RunOFACUpdateRequest" name="RunOFACUpdateRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="RunOFACUpdateResponse">
        <wsdl:part element="tns:RunOFACUpdateResponse" name="RunOFACUpdateResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="RunEUUpdateRequest">
        <wsdl:part element="tns:RunEUUpdateRequest" name="RunEUUpdateRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="RunEUUpdateResponse">
        <wsdl:part element="tns:RunEUUpdateResponse" name="RunEUUpdateResponse">
        </wsdl:part>
    </wsdl:message>


    <wsdl:portType name="BlacklistWS">
        <wsdl:operation name="RunOFACUpdate">
            <wsdl:input message="tns:RunOFACUpdateRequest" name="RunOFACUpdateRequest">
            </wsdl:input>
            <wsdl:output message="tns:RunOFACUpdateResponse" name="RunOFACUpdateResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="RunEUUpdate">
            <wsdl:input message="tns:RunEUUpdateRequest" name="RunEUUpdateRequest">
            </wsdl:input>
            <wsdl:output message="tns:RunEUUpdateResponse" name="RunEUUpdateResponse">
            </wsdl:output>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="BlacklistWSSoap11" type="tns:BlacklistWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="RunOFACUpdate">
            <soap:operation soapAction=""/>
            <wsdl:input name="RunOFACUpdateRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="RunOFACUpdateResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="RunEUUpdate">
            <soap:operation soapAction=""/>
            <wsdl:input name="RunEUUpdateRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="RunEUUpdateResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="BlacklistWSService">
        <wsdl:port binding="tns:BlacklistWSSoap11" name="BlacklistWSSoap11">
            <soap:address location="http://localhost:8080/rcm/ws/BlacklistWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
