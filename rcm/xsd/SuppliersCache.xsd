<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:suppliers="http://homecredit.net/rcm/suppliers" xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           targetNamespace="http://homecredit.net/rcm/suppliers" jxb:version="2.1" elementFormDefault="qualified">

    <xs:element name="EconomicalSubjectRequest">
        <xs:annotation>
            <xs:documentation>Request, pro získání ekonomických subjektů.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="economicalSubjectFilter" type="suppliers:EconomicalSubjectFilter" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc><PERSON>z<PERSON> filtrů pro získání ekonomických subjektů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="noRemoteCall" type="xs:boolean" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Pokud je vyplněno a nastaveno na true, služba neprovolává BisNode, pouze se pokusí vrátit data z cache, bez ohledu na její stáří.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="EconomicalSubjectResponse">
        <xs:annotation>
            <xs:documentation>Response, obsahující ekonomické subjekty.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="economicalSubject" type="suppliers:EconomicalSubject" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam ekonomických subjektů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AccountNumberIdentificationRequest">
        <xs:annotation>
            <xs:documentation>
                Request, pro dohledání ekonomického subjektu dle čísla účtu v RCM.
                Pouze účty, které mají nejsou použity více subjekty dle atributu (multipleUsage=false) jsou vyhledávány.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountNumber" type="xs:string" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Číslo účtu.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="notActiveSubjects" type="xs:boolean" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Nepovinný příznak, zda se mají vrátit i neaktivní (všechny) subjekty.
                                    Pokud je {@code null}, nebo je {@code false}, jsou vráceny pouze aktivní subjekty dle aktivních účtů.
                                    Pokud je vyplněno {@code true}, pak se vyhledává ve všech účtech (platné i neplatné) a vrací se subjekty ve všech stavech.
                                </jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AccountNumberIdentificationResponse">
        <xs:annotation>
            <xs:documentation>Response, pro dohledání ekonomického subjektu dle čísla účtu v RCM.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="economicalSubject" type="suppliers:EconomicalSubject" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam ekonomických subjektů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="wrongAccountNumber" type="suppliers:InvalidAccountNumber" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam nedohledaných a nevalidních čísel účtů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetEconomicalSubjectsPartialRequest">
        <xs:annotation>
            <xs:documentation>Request, pro získání ekonomických subjektů s naplněnou podmnožinou atributů
                EconomicalSubject(subjectId, identificationNumber, name, country, status, bankAccounts(*)).
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="economicalSubjectFilters" type="suppliers:EconomicalSubjectPartialFilter" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>List filtrů pro získání ekonomických subjektů.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetEconomicalSubjectsPartialResponse">
        <xs:annotation>
            <xs:documentation>Response, obsahující ekonomické subjekty pro každý vstupní filtr.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filterResults" type="suppliers:EconomicalSubjectPartialFilterResult" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>List výsledků k vstupním filtrům, zachovávající stejné pořadí v listu se vstupním filtrem.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="EconomicalSubjectPartialFilter">
        <xs:annotation>
            <xs:documentation>Filter pro získání ekonomických subjektů.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="subjectId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Unikátní identifikátor ekonomického subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EconomicalSubjectPartialFilterResult">
        <xs:annotation>
            <xs:documentation>Výsledek vyhledávání, dle vstupního EconomicalSubjectPartialFilter filtru.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="economicalSubject" type="suppliers:EconomicalSubject" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nalezený ekonomický subjekt, nebo {@code null} pokud nebyl subjekt nalezen.
                                Vyplněné atributy EconomicalSubject(subjectId, identificationNumber, name, country, status, bankAccounts(*)).
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EconomicalSubjectFilter">
        <xs:annotation>
            <xs:documentation>Filter pro získání ekonomických subjektů.</xs:documentation>
        </xs:annotation>
        <xs:choice minOccurs="1" maxOccurs="1">
            <xs:sequence>
                <xs:element name="country" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Země ekonomického subjektu, povolené hodnoty (CZ, SK)</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:choice minOccurs="1" maxOccurs="1">
                    <xs:element name="identificationNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Identifikační číslo (IČO).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="taxIdentificationNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Daňové identifikační číslo (DIČ).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Název subjektu.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
            <xs:element name="subjectId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Unikátní identifikátor ekonomického subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="EconomicalSubject">
        <xs:annotation>
            <xs:documentation>Ekonomický subjekt.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="subjectId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Unikátní identifikátor ekonomického subjektu v RCM.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="identificationNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikační číslo (IČO).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="taxIdentificationNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Daňové identifikační číslo (DIČ).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="country" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Země ekonomického subjektu, povolené hodnoty (CZ, SK)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="address" type="suppliers:Address" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Adresa subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="contacts" type="suppliers:Contact" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kontakt na subjekt.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bankAccount" type="suppliers:BankAccount" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Bankovní spojení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="state" type="suppliers:State" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Stav ekonomického subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="subjectNace" type="suppliers:SubjectNace" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obor činosti subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="syncTime" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas synchronizace dat z Bisnode.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateOfEstablishment" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum založení subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeesCount" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet zaměstnanců.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeesCountCategoryLowerBound" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Dolní hranice kategorie pro rozsah počtu zaměstnanců.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeesCountCategoryUpperBound" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Horní hranice kategorie pro rozsah počtu zaměstnanců.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BankAccount">
        <xs:annotation>
            <xs:documentation>Bankovní spojení na subjekt.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="accountNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Základ čísla účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="prefix" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Předčíslí.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bankCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód banky.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="iban" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IBAN.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="multipleUsage" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Je účet sdílen mezi více ekonomickými subjekty podle primárního zdroje?
                                Běžnou praxí je použití účtu pro skupinovou registraci DPH.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="validTo" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Platnost čísla účtu. Je vyplněno pouze v případě, že Bisnode v další odpovědi neodešle toto číslo účtu. V RCM je tedy
                                zneplatněno. Přitom platných čísel může být více. Pozn. Tento atribut je zaveden z důvodu, že při vyhledávání subjektů je číslo
                                účtu platné vždy (i to s vyplněnou platností). Zároveň je potřeba mít aktuální data subjektu.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Address">
        <xs:annotation>
            <xs:documentation>Adresa subjektu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="street" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ulice.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="houseNumber" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo popisné/orientační.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="city" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obec.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="locality" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Městská část.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="zip" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>PSČ.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Contact">
        <xs:annotation>
            <xs:documentation>Kontakt na subjket.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="email" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kontaktní emailová adresa.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="phone" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kontaktní telefonní číslo.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="web" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Webová stránky subjektu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="State">
        <xs:annotation>
            <xs:documentation>Stav subjektu.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Aktivivní</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NOT_ACTIVE">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Neaktivní</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="SubjectNace">
        <xs:annotation>
            <xs:documentation>Obor činnosti subjektu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="mainEconomicActivity" type="suppliers:EconomicActivity" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hlavní ekonomická činnost.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="secondaryEconomicActivity" type="suppliers:EconomicActivity" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Vedlejší ekonomická činnost.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EconomicActivity">
        <xs:annotation>
            <xs:documentation>Ekonomická činnost.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="industry" type="suppliers:Nace" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Odvětví.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="nace" type="suppliers:Nace" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ekonomická činnost.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Nace">
        <xs:annotation>
            <xs:documentation>NACE je zkratka pro klasifikaci ekonomických činností vydávanou Evropskou komisí. Zkratka NACE pochází z francouzštiny
                (Nomenclature statistique des activités économiques dans la Communauté européenne), používá se obvykle zkratka NACE.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="text" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Text oboru činnost.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InvalidAccountNumber">
        <xs:annotation>
            <xs:documentation>Neplatné číslo účtu.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="accountNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo účtu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Stav. OK, FAIL.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
