<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified"
           xmlns="http://airbank.cz/rcm/ws/ruian/autocomplete"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://airbank.cz/rcm/ws/ruian/autocomplete">

    <xs:element name="GetSuggestionsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="address" type="Address" />
                <xs:element name="part" type="AddressPart" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetSuggestionsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="suggestions" type="xs:string" minOccurs="0" maxOccurs="unbounded" nillable="true" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SuggestZipRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="address" type="Address" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SuggestZipResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="zip" type="xs:string" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:complexType name="Address">
        <xs:sequence>
            <xs:element name="country" type="xs:string" />
            <xs:element name="town" type="xs:string" minOccurs="0"/>
            <xs:element name="street" type="xs:string" minOccurs="0"/>
            <xs:element name="house" type="xs:string" minOccurs="0"/>
            <xs:element name="zip" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AddressPart">
        <xs:annotation>
            <xs:documentation>Part of address to search suggestions</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="TOWN">
                <xs:annotation>
                    <xs:documentation>Town</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="JUST_TOWN">
                <xs:annotation>
                    <xs:documentation>Town without district</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="STREET">
                <xs:annotation>
                    <xs:documentation>Street</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HOUSE">
                <xs:annotation>
                    <xs:documentation>House</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ZIP">
                <xs:annotation>
                    <xs:documentation>Zip</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>