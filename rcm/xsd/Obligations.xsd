<?xml version = "1.0" encoding = "UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" xmlns="http://homecredit.net/lap-obligations"
            targetNamespace="http://homecredit.net/lap-obligations" jxb:version="2.1" elementFormDefault="qualified">

    <xsd:element name="UpdatePairsRequest">
        <xsd:annotation>
            <xsd:documentation>Request pro uložení párů.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="pairs" type="Pair" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Eviduje obchodní případy závazků k<PERSON>ů v registrech.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="removePairs" type="CandidateObligation" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam závzků pro odebrání.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdatePairsResponse">
        <xsd:annotation>
            <xsd:documentation>Response obsahující status o uložení párů.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="notFoundRemovePairs" type="CandidateObligation" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam nenalezených kandidátních závazků z requestu.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="removePairsOutputData" type="RemovePairsStatus">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Indikace stavu odebrání kandidátních závazků.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="updatePairsOutputData" type="UpdatePairsStatus">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Status o uložení párů.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCandidateObligationsRequest">
        <xsd:annotation>
            <xsd:documentation>Request pro získání kandidátních závazků.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="withRemovedCandidateObligations" type="xsd:boolean" minOccurs="0">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Atribut definující, zda je požadováno v odpovědi vracet i zamítnuté kandidátní závazky.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="candidateObligationRequests" type="CandidateObligationRequests" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Požadavek na získání kandidátních závazků. Vždy alespoň jeden atribut dvojice (requestId, number) je povinný.
                                    Parametry v rámci jedné dvojice se vyhodnocují jako AND a v kolekci jako OR.
                                </jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCandidateObligationsResponse">
        <xsd:annotation>
            <xsd:documentation>Response obahující data o kandidátních závazích.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="status" type="CommonStatus">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Status indikující, zda získání kandidátních závzků proběhlo v pořádku nebo ne.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="candidateObligations" type="GetCandidateObligation" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Status indikující, zda získání kandidátních závzků proběhlo v pořádku nebo ne.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetPairsRequest">
        <xsd:annotation>
            <xsd:documentation>Request pro zíkání údajů o párování.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikace klienta. Identifikátor CIF.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="withRemovedCandidateObligations" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Zahrnout odebrané závazky.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetPairsResponse">
        <xsd:annotation>
            <xsd:documentation>Response obsahující údaje o párování.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="status" type="CommonStatus">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Status indikující, zda získání párování proběhlo v pořádku nebo ne.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="pairs" type="Pair" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Eviduje obchodní případy závazků klientů v registrech.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="rejectedCandidateObligations" type="CandidateObligation" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Zamítnutý kandidátní závazek uvedený klientem pro konsolidaci.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="Pair">
        <xsd:annotation>
            <xsd:documentation>Eviduje obchodní případy závazků klientů v registrech.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="creditBureauName" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Name of credit bureau register. Registr. (BRKI-NRKI nebo PPFGroup.)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="obligationOrigin" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V případě registru BRKI-NRKI jsou jednotlivé finanční instituce anonymizovány a tedy v tomto poli bude text
                                'BRKI-NRKI'. Identifikátor závazku je pak unikátní přes celý registr.
                                V případě závazku registru PPFGroup je znám původce (CZ/SK/EKISCZ/EKISSK) a identifikátor závazku je unikátní jen v rámci
                                původce, tedy budou zde uváděni konkrétní původci závazku.
                                Pole je povinné, nelze evidovat hodnoty NUL
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="obligationId" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace obchodního případu o závazku dle definice registru (registr = atribut creditBureau). Např. pro registr
                                BRKI-NRKI to je CCBOperationCode, pro registr PPFGroup to je creditId.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ke jakému klintovi je obchodní případ závazku evidován. Identifikátor CIF. Povinný údaj.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="hash" type="Hash">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ukazatel na kontrakt.
                                Reprezentace kontraktu uváděného klientem povoleného ke konsolidaci vzhledem k párovacímu mechanismu, který je závislý na
                                úvěrovém registru.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Hash">
        <xsd:annotation>
            <xsd:documentation>Ukazatel na kontrakt.
                Reprezentace kontraktu uváděného klientem povoleného ke konsolidaci vzhledem k párovacímu mechanismu, který je závislý na
                úvěrovém registru.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Popisný ukazatel složený z párovaných skutečných závazků registru. Pokud jsou v kontraktech určených ke konsolidaci
                                uvedených klientem dle párovacího mechanismu nerozlišitelné kontrakty, mají stejný hash (= párují se na tytéž skutečné závazky
                                registru).
                                Hash je tedy atribut, který určuje primární systém v okamžiku párování. Pokud více kontraktů určených ke konsolidaci by bylo
                                párováno stejně (tedy na téže skutečné závazky), pak hash těchto kontraktů musí být stejný. Naopak nelze jeden skutečný závazek
                                "připárovat" k různým hashům v rámci téhož registru.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="candidateObligationsCount" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet kontraktů uváděných klientem povolených ke konsolidaci (CandidateObligation), které jsou (nemusí být na téže
                                žádosti) vzhledem k párovacímu mechanismu nerozlišitelné (= párovací mechanismus různým uváděným závazkům přiřadí vzhledem k
                                registru BRKI (= registr, kde nejsou publikování původci závazků) stejné skutečné závazky). Povinný atribut. Číslo >= 0.
                                Atribut dopočítává RCM tak, že spočítá, kolikrát se tentýž hash vyskytuje mezi různými kontrakty uváděnými klientem povolené ke
                                konsolidaci (CandidateObligation), jejichž obchodní případy úvěrů jsou ještě validní (CandidateObligation.credit.valid = true) a
                                zároveň kontrakty nebyly odstraněny (CandidateObligation.removed is NULL)
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="candidateObligations" type="CandidateObligation" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kontrakt uváděný klientem ke konsolidaci.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="GetCandidateObligation">
        <xsd:annotation>
            <xsd:documentation>Kontrakt uváděný klientem povolený ke konsolidaci.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="requestId" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace žádosti o refinancování/konsolidaci.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="creditValid" type="xsd:boolean">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Indikace, zda se jedná o platný obchodní případ.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="fiCode" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód finanční instituce.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="number" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace kontraktu uváděného klientem povoleného ke konsolidaci.Číslo generuje systém IB a každé nové přídání
                                závazku ke konsolidaci vygeneruje nové číslo. Číslo je tedy unikátní napříč všemi žádostmi a není možné, aby bylo duplicitní.
                                Nejsou povoleny NULL values.
                                Poznámka pro vývoj:
                                Je dohodnou s DWH, že systém RCM neumožní do pole CO_NUMBER vložit duplicitní hodnotu. Tato skutečnost bude chráněna pomocí DB
                                unique constraint.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace klienta. Identifikátor CIF.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="paid" type="xsd:dateTime" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas, od kdy je evidováno doplacení.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="hashes" type="HashCreditBureau" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Pole hash ukazatelů a jejich atributů. Pokud jsou kontrakty uváděné klientem povolené ke konsolidaci pro párovací
                                mechanismus daného registru nerozlišitelné, mají stejný hash. Hash se neuvádí, pokud je kontrakt uváděný klientem povolený ke
                                konsolidaci 100 párovatelný v daném registru na skutečný závazek v registru.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="HashCreditBureau">
        <xsd:annotation>
            <xsd:documentation>Pole hash ukazatelů a jejich atributů. Pokud jsou kontrakty uváděné klientem povolené ke konsolidaci pro párovací
                mechanismus daného registru nerozlišitelné, mají stejný hash. Hash se neuvádí, pokud je kontrakt uváděný klientem povolený ke
                konsolidaci 100 párovatelný v daném registru na skutečný závazek v registru.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="creditBureauName" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Name of credit bureau register. Registr. (BRKI-NRKI nebo PPFGroup.)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="code" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Popisný ukazatel složený z párovaných skutečných závazků registru. Pokud jsou v kontraktech určených ke konsolidaci
                                uvedených klientem dle párovacího mechanismu nerozlišitelné kontrakty, mají stejný hash (= párují se na tytéž skutečné závazky
                                registru).
                                Hash je tedy atribut, který určuje primární systém v okamžiku párování. Pokud více kontraktů určených ke konsolidaci by bylo
                                párováno stejně (tedy na téže skutečné závazky), pak hash těchto kontraktů musí být stejný. Naopak nelze jeden skutečný závazek
                                "připárovat" k různým hashům v rámci téhož registru.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="candidateObligationsCount" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet kontraktů uváděných klientem povolených ke konsolidaci (CandidateObligation), které jsou (nemusí být na téže
                                žádosti) vzhledem k párovacímu mechanismu nerozlišitelné (= párovací mechanismus různým uváděným závazkům přiřadí vzhledem k
                                registru BRKI (= registr, kde nejsou publikování původci závazků) stejné skutečné závazky). Povinný atribut. Číslo >= 0.
                                Atribut dopočítává RCM tak, že spočítá, kolikrát se tentýž hash vyskytuje mezi různými kontrakty uváděnými klientem povolené ke
                                konsolidaci (CandidateObligation), jejichž obchodní případy úvěrů jsou ještě validní (CandidateObligation.credit.valid = true) a
                                zároveň kontrakty nebyly odstraněny (CandidateObligation.removed is NULL)
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="paidObligationsCount" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet skutečných závazků, které jsou evidovány jako doplacené. Povinný atribut. Číslo >= 0. Výchozí hodnota 0.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CandidateObligation">
        <xsd:annotation>
            <xsd:documentation>Kandidátní závazek uvedený klientem pro konsolidaci.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor žádosti.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="financialInstitutionCode" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód finanční instituce původního (kandidátního) závazku</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="candidateObligationId" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID kandidátního závazku.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="UpdatePairsStatus">
        <xsd:annotation>
            <xsd:documentation>Status o uložení párů.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OK">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V pořádku.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FAILED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Problém při uložení v RCM.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOTVALIDPAIRDATA">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Neplatná data pro párování.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NOTVALIDPAIRING">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Neplatná párování.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="CommonStatus">
        <xsd:annotation>
            <xsd:documentation>Status.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OK">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V pořádku.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FAILED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Problém při uložení v RCM.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="RemovePairsStatus">
        <xsd:annotation>
            <xsd:documentation>Status pro odebrání kandidátních závzků.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OK">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V pořádku.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FAILED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Problém při uložení v RCM.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SOME_CANDIDATE_OBLIGATIONS_NOT_FOUND">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Některé kandidátní závazky z requestu nebyly nalezeny.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="CandidateObligationRequests">
        <xsd:annotation>
            <xsd:documentation>Požadavek na získání kandidátních závazků. Vždy alespoň jeden atribut dvojice (requestId, number) je povinný.
                Parametry v rámci jedné dvojice se vyhodnocují jako AND a v kolekci jako OR.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="requestId" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace obchodního případu = žádosti o refinancovaní / konsolidaci.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="number" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace kontraktu uváděného klientem povoleného ke konsolidaci. Identifikace kontraktu uváděného klientem
                                povoleného ke konsolidaci. Číslo generuje systém IB a každé nové přídání závazku ke konsolidaci vygeneruje nové číslo. Číslo je
                                tedy unikátní napříč všemi žádostmi a není možné, aby bylo duplicitní.
                                Nejsou povoleny NULL values.
                                Poznámka pro vývoj:
                                Je dohodnou s DWH, že systém RCM neumožní do pole CO_NUMBER vložit duplicitní hodnotu. Tato skutečnost bude chráněna pomocí DB
                                unique constraint.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="GetManualPairingObligationsRequest">
        <xsd:annotation>
            <xsd:documentation>Request pro vytvoření návrhů závazků pro manuální párování.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikace klienta. Identifikátor CIF.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="pairingCandidateObligation" type="PairingCandidateObligation" minOccurs="1" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>List závazků pro manuální párování.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="PairingCandidateObligation">
        <xsd:annotation>
            <xsd:documentation>Kandidátní závazek pro manuální párování.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="fiCode" type="xsd:string" minOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kód finanční instituce</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="number" type="xsd:string" minOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace kontraktu uváděného klientem povoleného ke konsolidaci.Číslo generuje systém IB a každé nové přídání
                                závazku ke konsolidaci vygeneruje nové číslo. Číslo je tedy unikátní napříč všemi žádostmi a není možné, aby bylo duplicitní.
                                Nejsou povoleny NULL values.
                                Poznámka pro vývoj:
                                Je dohodnou s DWH, že systém RCM neumožní do pole CO_NUMBER vložit duplicitní hodnotu. Tato skutečnost bude chráněna pomocí DB
                                unique constraint.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="obligationType" type="xsd:string" minOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ závazku.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="GetManualPairingObligationsResponse">
        <xsd:annotation>
            <xsd:documentation>Response obsahující návrhy závazků pro manuální párování.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="pairingObligation" type="PairingObligation" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>List návrhů mezi kandidátním závazkem a skutečnými závazky z registrů.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="status" type="CommonStatus">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Status indikující, zda získání závazků s registru, které odpovídají kandidátním závazkům, proběhlo v pořádku nebo ne.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="PairingObligation">
        <xsd:annotation>
            <xsd:documentation>Návrh mezi kandidátním závazkem a skutečnými závazky z registrů.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="number" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace kontraktu uváděného klientem povoleného ke konsolidaci.Číslo generuje systém IB a každé nové přídání
                                závazku ke konsolidaci vygeneruje nové číslo. Číslo je tedy unikátní napříč všemi žádostmi a není možné, aby bylo duplicitní.
                                Nejsou povoleny NULL values.
                                Poznámka pro vývoj:
                                Je dohodnou s DWH, že systém RCM neumožní do pole CO_NUMBER vložit duplicitní hodnotu. Tato skutečnost bude chráněna pomocí DB
                                unique constraint.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="obligationInfo" type="ObligationInfo" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>List informací o skutečných závazích z registrů.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ObligationInfo">
        <xsd:annotation>
            <xsd:documentation>Informace o závazku z registru.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="actualBalance" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Aktuální částka k doplacení</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="creditBureauName" type="xsd:string" minOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Název credit bureau register. Registr. (BRKI-NRKI nebo PPFGroup).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="dateOfOrigin" type="xsd:date" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum načerpání úvěru.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="installmentAmount" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výše splátky.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanAmount" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Částka úvěru</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanType" type="xsd:string" minOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ úvěru - viz číselník ContractType (opType), který reprezentuje reportované úvěry z jiných bank do registru BRKI.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="numberOfInstallments" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet splátek</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="obligationId" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace obchodního případu o závazku dle definice registru (registr = atribut creditBureau). Např. pro registr
                                BRKI-NRKI to je CCBOperationCode, pro registr PPFGroup to je creditId.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="obligationOrigin" type="xsd:string" minOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V případě registru BRKI-NRKI jsou jednotlivé finanční instituce anonymizovány a tedy v tomto poli bude text
                                'BRKI-NRKI'. Identifikátor závazku je pak unikátní přes celý registr.
                                V případě závazku registru PPFGroup je znám původce (CZ/SK/EKISCZ/EKISSK) a identifikátor závazku je unikátní jen v rámci
                                původce, tedy budou zde uváděni konkrétní původci závazku.
                                Pole je povinné, nelze evidovat hodnoty NUL
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
