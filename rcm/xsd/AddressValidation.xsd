<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:faultCommon="http://airbank.cz/common/ws/fault"
            xmlns:addressCommon="http://airbank.cz/rcm/ws/address/common"
            elementFormDefault="qualified"
            jxb:version="2.1"
            xmlns="http://airbank.cz/rcm/ws/validation/address"
            targetNamespace="http://airbank.cz/rcm/ws/validation/address">

    <xsd:import namespace="http://airbank.cz/common/ws/fault" schemaLocation="commonSoapFault.xsd"/>
    <xsd:import namespace="http://airbank.cz/rcm/ws/address/common" schemaLocation="AddressCommon.xsd"/>

    <xsd:element name="ValidateAddressRequest">
        <xsd:complexType>
            <xsd:annotation>
                <xsd:documentation>Požadavek na validaci adresy.</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="address" type="addressCommon:SimpleAddress">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Adresa.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ValidateAddressResponse">
        <xsd:complexType>
            <xsd:annotation>
                <xsd:documentation>Odpověď na validaci adresy.</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="validationResult" type="faultCommon:ValidationResultTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Seznam validačních chyb.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="addressFormat" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Úroveň shody adresy s číselníkem.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
