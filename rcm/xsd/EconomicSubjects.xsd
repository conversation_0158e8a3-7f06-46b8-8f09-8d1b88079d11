<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified"
           xmlns="http://airbank.cz/rcm/ws/economicsubjects"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://airbank.cz/rcm/ws/economicsubjects">

    <xs:element name="CheckEconomicSubjectRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="firstName" type="xs:string"/>
                <xs:element name="lastName" type="xs:string"/>
                <xs:element name="dateOfBirth" type="xs:date"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CheckEconomicSubjectResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="economicSubject" type="xs:boolean"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="IdentifyEconomicSubjectRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="legalSegment" type="LegalSegment"/>
                <xs:element name="identificationNumber" type="xs:string"/>
                <xs:element name="businessProcessEvent" type="BusinessProcessEvent"/>
                <xs:element name="eventIdentification" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="IdentifyEconomicSubjectResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="0"/>
                <xs:element name="status" type="SubjectIdentificationStatus"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="BusinessProcessEvent">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ONBOARDING"/>
            <xs:enumeration value="EXTERNAL_MASTER_UPDATE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SubjectIdentificationStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IDENTIFIED"/>
            <xs:enumeration value="NOT_FOUND"/>
            <xs:enumeration value="UNSUPPORTED_LEGAL_FORM"/>
            <xs:enumeration value="UNSUPPORTED_RELATIONSHIP_STRUCTURE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="LegalSegment">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ENTREPRENEUR"/>
            <xs:enumeration value="LEGAL_ENTITY"/>
            <xs:enumeration value="OTHER"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="SubscribeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identificationNumber" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SubscribeResponse">
        <xs:complexType>
        </xs:complexType>
    </xs:element>

    <xs:element name="UnsubscribeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UnsubscribeResponse">
        <xs:complexType>
        </xs:complexType>
    </xs:element>
</xs:schema>