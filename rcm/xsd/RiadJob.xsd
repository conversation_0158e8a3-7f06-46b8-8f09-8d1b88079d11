<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified"
           xmlns="http://airbank.cz/rcm/ws/riadjob"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://airbank.cz/rcm/ws/riadjob">

    <xs:element name="ProcessNewRecordsRequest">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessNewRecordsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessWaitingRecordsRequest">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessWaitingRecordsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResetFailedRecordsRequest">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResetFailedRecordsResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

</xs:schema>