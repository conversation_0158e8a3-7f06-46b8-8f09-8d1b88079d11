<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/obs/ws/obsOverdraftWS"
                  xmlns="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
                  name="obsOverdraftWS"
                  targetNamespace="http://airbank.cz/obs/ws/obsOverdraftWS">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/obsOverdraftWS">
            <xsd:include schemaLocation="OBSOverdraftWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="getOverdraftLimitParamsRequest">
        <part element="tns:getOverdraftLimitParamsRequest" name="parameters"/>
    </message>
    <message name="getOverdraftLimitParamsResponse">
        <part element="tns:getOverdraftLimitParamsResponse" name="parameters"/>
    </message>
    <message name="getOverdraftLimitParamsFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getOverdraftParamsRequest">
        <part element="tns:getOverdraftParamsRequest" name="parameters"/>
    </message>
    <message name="getOverdraftParamsResponse">
        <part element="tns:getOverdraftParamsResponse" name="parameters"/>
    </message>
    <message name="getOverdraftParamsFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getOverdraftPreContractDocumentRequest">
        <part name="parameters" element="tns:getOverdraftPreContractDocumentRequest"/>
    </message>
    <message name="getOverdraftPreContractDocumentResponse">
        <part name="parameters" element="tns:getOverdraftPreContractDocumentResponse"/>
    </message>
    <message name="getOverdraftPreContractDocumentFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="createOverdraftRequest">
        <part element="tns:createOverdraftRequest" name="parameters"/>
    </message>
    <message name="createOverdraftResponse">
        <part element="tns:createOverdraftResponse" name="parameters"/>
    </message>
    <message name="createOverdraftFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="findOverdraftRequest">
        <part element="tns:findOverdraftRequest" name="parameters"/>
    </message>
    <message name="findOverdraftResponse">
        <part element="tns:findOverdraftResponse" name="parameters"/>
    </message>
    <message name="findOverdraftFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getOverdraftDetailRequest">
        <part element="tns:getOverdraftDetailRequest" name="parameters"/>
    </message>
    <message name="getOverdraftDetailResponse">
        <part element="tns:getOverdraftDetailResponse" name="parameters"/>
    </message>
    <message name="getOverdraftDetailFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="cancelOverdraftRequest">
        <part element="tns:cancelOverdraftRequest" name="parameters"/>
    </message>
    <message name="cancelOverdraftResponse">
        <part element="tns:cancelOverdraftResponse" name="parameters"/>
    </message>
    <message name="cancelOverdraftFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="terminateOverdraftRequest">
        <part element="tns:terminateOverdraftRequest" name="parameters"/>
    </message>
    <message name="terminateOverdraftResponse">
        <part element="tns:terminateOverdraftResponse" name="parameters"/>
    </message>
    <message name="terminateOverdraftFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getAllOverdraftDetailsRequest">
        <part element="tns:getAllOverdraftDetailsRequest" name="parameters"/>
    </message>
    <message name="getAllOverdraftDetailsResponse">
        <part element="tns:getAllOverdraftDetailsResponse" name="parameters"/>
    </message>
    <message name="getAllOverdraftDetailsFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="calculateOverdraftInterestRequest">
        <part element="tns:calculateOverdraftInterestRequest" name="parameters"/>
    </message>
    <message name="calculateOverdraftInterestResponse">
        <part element="tns:calculateOverdraftInterestResponse" name="parameters"/>
    </message>
    <message name="calculateOverdraftInterestFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getSuspensionHistoryRequest">
        <part element="tns:getSuspensionHistoryRequest" name="parameters"/>
    </message>
    <message name="getSuspensionHistoryResponse">
        <part element="tns:getSuspensionHistoryResponse" name="parameters"/>
    </message>
    <message name="getSuspensionHistoryFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="suspendOverdraftRequest">
        <part element="tns:suspendOverdraftRequest" name="parameters"/>
    </message>
    <message name="suspendOverdraftResponse">
        <part element="tns:suspendOverdraftResponse" name="parameters"/>
    </message>
    <message name="suspendOverdraftFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <portType name="obsOverdraftWS">
        <operation name="getOverdraftLimitParams">
            <documentation>
            </documentation>
            <input message="tns:getOverdraftLimitParamsRequest"/>
            <output message="tns:getOverdraftLimitParamsResponse"/>
            <fault name="fault" message="tns:getOverdraftLimitParamsFault"/>
        </operation>
        <operation name="getOverdraftParams">
            <documentation>
            </documentation>
            <input message="tns:getOverdraftParamsRequest"/>
            <output message="tns:getOverdraftParamsResponse"/>
            <fault name="fault" message="tns:getOverdraftParamsFault"/>
        </operation>
        <operation name="getOverdraftPreContractDocument">
            <documentation>metoda na zakladě vstupních parametrů vrátí html verzi předsmluvního formuláře a id na PDF verzi, kde spustí paralelní
                asynchroní generování.

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován technicalContext: ne

                Chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:getOverdraftPreContractDocumentRequest"/>
            <output message="tns:getOverdraftPreContractDocumentResponse"/>
            <fault name="fault" message="tns:getOverdraftPreContractDocumentFault"/>
        </operation>

        <operation name="createOverdraft">
            <documentation>
            </documentation>
            <input message="tns:createOverdraftRequest"/>
            <output message="tns:createOverdraftResponse"/>
            <fault name="fault" message="tns:createOverdraftFault"/>
        </operation>
        <operation name="getOverdraftDetail">
            <documentation>
            </documentation>
            <input message="tns:getOverdraftDetailRequest"/>
            <output message="tns:getOverdraftDetailResponse"/>
            <fault name="fault" message="tns:getOverdraftDetailFault"/>
        </operation>
        <operation name="findOverdraft">
            <documentation>
            </documentation>
            <input message="tns:findOverdraftRequest"/>
            <output message="tns:findOverdraftResponse"/>
            <fault name="fault" message="tns:findOverdraftFault"/>
        </operation>
        <operation name="cancelOverdraft">
            <documentation>
            </documentation>
            <input message="tns:cancelOverdraftRequest"/>
            <output message="tns:cancelOverdraftResponse"/>
            <fault name="fault" message="tns:cancelOverdraftFault"/>
        </operation>
        <operation name="terminateOverdraft">
            <documentation>
            </documentation>
            <input message="tns:terminateOverdraftRequest"/>
            <output message="tns:terminateOverdraftResponse"/>
            <fault name="fault" message="tns:terminateOverdraftFault"/>
        </operation>
        <operation name="getAllOverdraftDetails">
            <documentation>
                List of overdrafts of given owner (cuid)
                errors:
                code / attribute / value : description
                GENERAL_ERROR /GENERAL_ERROR / : general error
                CLERR_TIMEOUT / GENERAL_ERROR / : timeout, operation couldn't be executed in timely fashion
                CLERR_NO_DATA_FOUND / cuid / : Overdraft owner not found
            </documentation>
            <input message="tns:getAllOverdraftDetailsRequest"/>
            <output message="tns:getAllOverdraftDetailsResponse"/>
            <fault name="fault" message="tns:getAllOverdraftDetailsFault"/>
        </operation>
        <operation name="calculateOverdraftInterest">
            <documentation>
            </documentation>
            <input message="tns:calculateOverdraftInterestRequest"/>
            <output message="tns:calculateOverdraftInterestResponse"/>
            <fault name="fault" message="tns:calculateOverdraftInterestFault"/>
        </operation>
        <operation name="getSuspensionHistory">
            <documentation>
                History of utilization suspension of given overdraft
                errors:
                code / attribute / value : description
                GENERAL_ERROR /GENERAL_ERROR / : general error
                CLERR_TIMEOUT / GENERAL_ERROR / : timeout, operation couldn't be executed in timely fashion
                CLERR_NO_DATA_FOUND / idOverdraft / : Overdraft not found
            </documentation>
            <input message="tns:getSuspensionHistoryRequest"/>
            <output message="tns:getSuspensionHistoryResponse"/>
            <fault name="fault" message="tns:getSuspensionHistoryFault"/>
        </operation>
        <operation name="suspendOverdraft">
            <documentation>
                suspension of given overdraft
                errors:
                code / attribute / value : description
                GENERAL_ERROR /GENERAL_ERROR / : general error
                CLERR_TIMEOUT / GENERAL_ERROR / : timeout, operation couldn't be executed in timely fashion
                CLERR_NO_DATA_FOUND / idOverdraft / : Overdraft not found
            </documentation>
            <input message="tns:suspendOverdraftRequest"/>
            <output message="tns:suspendOverdraftResponse"/>
            <fault name="fault" message="tns:suspendOverdraftFault"/>
        </operation>
    </portType>

    <binding name="obsOverdraftWSSOAP" type="tns:obsOverdraftWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="getOverdraftLimitParams">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getOverdraftParams">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getOverdraftPreContractDocument">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="createOverdraft">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="findOverdraft">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getOverdraftDetail">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getAllOverdraftDetails">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="cancelOverdraft">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="terminateOverdraft">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="calculateOverdraftInterest">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getSuspensionHistory">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="suspendOverdraft">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>

    <service name="obsOverdraftWS">
        <documentation></documentation>
        <port binding="tns:obsOverdraftWSSOAP" name="obsOverdraftWSSOAP">
            <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsOverdraftWS"/>
        </port>
    </service>

</definitions>
