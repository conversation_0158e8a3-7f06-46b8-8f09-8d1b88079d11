<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsSecuritySettingsWS/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
	<xsd:import schemaLocation="../xsd/AutentizationAuthorization.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
	<xsd:element name="setPasswordRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="newPassword" type="xsd:base64Binary" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>nové heslo - viz wiki https://wiki.apedie.abank.cz/pages/viewpage.action?pageId=*********</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="authorization" type="Q1:AuthType" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>autorizační údaje</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setPasswordResponse">
		<xsd:complexType>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setUsernameRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="newUsername" type="xsd:string" maxOccurs="1" minOccurs="1">
					<xsd:annotation>
						<xsd:documentation>nové uživatelské jméno</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="authorization" type="Q1:AuthType" maxOccurs="1" minOccurs="1">
					<xsd:annotation>
						<xsd:documentation>autorizační údaje</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setUsernameResponse">
		<xsd:complexType>
			<xsd:sequence>

			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="resetPasswordFromBranchRequest">
		<xsd:complexType>
			<xsd:sequence>

				<xsd:element name="userName" type="xsd:string" maxOccurs="1" minOccurs="1">
					<xsd:annotation>
						<xsd:documentation>uživatelské přihlašovací jméno</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="resetPasswordFromBranchResponse">
		<xsd:complexType>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="resetPasswordFromBranchFault">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="resetPasswordFromBranchFault" type="xsd:string">
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setLoginBySMSRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="loginBySMS" type="xsd:boolean" maxOccurs="1" minOccurs="1">
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setLoginBySMSResponse">
		<xsd:complexType>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getLoginBySMSRequest">
		<xsd:complexType>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getLoginBySMSResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="loginBySMS" type="xsd:boolean" maxOccurs="1" minOccurs="1">
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>

</xsd:schema>
