<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified"
    xmlns:inv="http://airbank.cz/obs/ws/investmentAccounting"
    targetNamespace="http://airbank.cz/obs/ws/investmentAccountingWS">

    <import namespace="http://airbank.cz/obs/ws/investmentAccounting" schemaLocation="../xsd/investmentAccounting.xsd" />

    <!--   isClientAbleToPay request-->
    <element name="isClientAbleToPayRequest">
        <annotation>
            <documentation>
                ověření dostatečné solventnosti klienta pro úhradu nakoupovaných cp včetně poplatků
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="cuid" type="long" />                                  <!-- unikátní identifikátor klienta, kter<PERSON>ho se operace týká-->
                <element name="accountNumber" type="inv:accountNumber" />
                <element name="totalBuyAmount" type="inv:amountAndCurrency" />                         <!--   celková cena nákupu = assetMarketPrice + accruedInterest + sum fees, je pouze součtem dílčích elementů, tj. není nutné tuto hodnotu mít v requestu vyplněnou, součet můžeme provést na straně OBS, pokud se ukáže že ho k něčemu potřebujeme, resp. z dílčích částek součet provést dokážeme, ale z celku detail už nevymyslíme -->
            </sequence>
        </complexType>
    </element>


    <!--   isClientAbleToPayResponse -->
    <element name="isClientAbleToPayResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!--   ReserveBuyTotalAmountRequest-->
    <element name="reserveBuyTotalAmountRequest">
        <annotation>
            <documentation>
                stržení celkové částky nákupu z BU klienta při zadání příkazu k nákupu,
                obsahuje jak celkovou hodnotu nákupu tak dílčí složky tvořící celkovou hodnotu nákupu,
                téměř totožnou strukturu má element chargeTradeBuy, bylo by tedy možné provést sloučení
                do jednoho typu s rozlišením konkrétní operace pomocí operationTypeSpecification
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references"/>                          <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                                  <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="assetIdentification"
                         type="inv:assetIdentificationFull"/>    <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
                <element name="postingEntry" type="inv:postingEntryReBTA"/>
            </sequence>
        </complexType>
    </element>

    <!--   ReserveBuyTotalAmountResponse -->
    <element name="reserveBuyTotalAmountResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
                <element name="capostedAmount" type="inv:amountAndCurrency" minOccurs="0"/>
                <element name="exchangeRate" type="decimal" minOccurs="0"/>
            </sequence>
        </complexType>
    </element>

    <!--   partlyCancelBuyResponseRequest-->
    <element name="partlyCancelBuyRequest">
        <annotation>
            <documentation>
                při částečném vypořádání nákupu je část prostředků vácena klientovi prostřednictvím
                služby s totožnou vnitřní strukturou jako má služba pro rezervaci prostředků reserveBuyTotalAmount
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references"/>                          <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                                  <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="assetIdentification"
                         type="inv:assetIdentificationFull"/>    <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
                <element name="postingEntry" type="inv:postingEntryPCanBR"/>
            </sequence>
        </complexType>
    </element>

    <!--   partlyCancelBuyRequestResponse -->
    <element name="partlyCancelBuyResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>

    <element name="chargeSettlementBuyRequest">
        <annotation>
            <documentation>
              účtování při zobchodování nákupu cp - tj. finální cena cp,
              celkem naběhlé auv na cp k datu nákupu, poplatky spojené s nákupem
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references">
                    <annotation>
                        <documentation>
                            reference k operaci, popis uveden u typu "references"
                        </documentation>
                    </annotation>
                </element>
                <element name="cuid" type="long">
                    <annotation>
                        <documentation>
                            unikátní identifikátor klienta, kterého se operace týká
                        </documentation>
                    </annotation>
                </element>
                <element name="assetIdentification" type="inv:assetIdentificationFull">
                    <annotation>
                        <documentation>
                            identifikace cenného papíru, kterého se operace týká,
                            popis uveden u typu "assetIdentificationFull"
                        </documentation>
                    </annotation>
                </element>
                <element name="postingEntry" type="inv:postingEntryChrSeB"/>
            </sequence>
        </complexType>
    </element>

    <element name="chargeSettlementBuyResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>

    <!--   chargeClientBuyPaymentRequest     -->
    <element name="chargeClientBuyPaymentRequest">
        <annotation>
            <documentation>zaúčtování platby klientského nákupu CP; podle účetních předpisů v10 obsahuje celkovou částku za nákup CP a poplatky spojené s nákupem
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references"/>                        <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                                <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="assetIdentification"
                         type="inv:assetIdentificationFull"/>  <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
                <element name="postingEntry" type="inv:postingEntryCHRCBP"/>
            </sequence>
        </complexType>
    </element>

    <!--  chargeClientBuyPaymentResponse -->
    <element name="chargeClientBuyPaymentResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- settleCounterpartySumPayment -->
    <element name="settleCounterpartySumPaymentRequest">

        <annotation>
            <documentation>vypořádání kumulovaných částek z/na účet Zúčtování s trhem CP a protistranou pri nákupu, prodeji, výplatě, kuponu, splatnosti cp
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="operationTypeSpecification"
                         type="inv:counterPartySettlementType"/>     <!-- bližší určení typu operace výčtovým typem counterPartySettlementType -->
                <element name="references"
                         type="inv:references"/>                                     <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="assetIdentification"
                         type="inv:assetIdentificationISIN"/>               <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationISIN" -->
                <element name="postingEntry" type="inv:postingEntrySTLCSP"/>
            </sequence>
        </complexType>
    </element>

    <!--  SettleCounterpartySumPaymentResponse -->
    <element name="settleCounterpartySumPaymentResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- SettleFeeRequest -->
    <element name="settleFeeRequest">
        <annotation>
            <documentation>
                sumárni platba poplatku ven z banky,
                vypořádání poplatku spojeného s operací nad konkrétním cp bude obsahovat identifikaci cp
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references">
                    <annotation>
                        <documentation>
                            reference k operaci, popis uveden u typu "references"
                        </documentation>
                    </annotation>
                </element>
                <element name="instrumentContext" type="inv:instrumentType">
                    <annotation>
                        <documentation>
                            Kontext, ve kterém je služba volaná (A - IBKR, C - české podíláky a dluhopisy).
                            Atribut je funkčně ekvivalentní instrumentType, ale protože jde o netransakční poplatky,
                            nelze je jednoznačně přiřadit např. k akcii/ETF, jde jen identifikovat, komu ho platíme (za oba typy nástrojů).
                            Je plněný stejnými kódy jako instrumentType, abychom ho mohli použít pro jeho sloupec v parametrizaci.
                        </documentation>
                    </annotation>
                </element>
                <element name="postingEntry" type="inv:postingEntryStlFee"/>
            </sequence>
        </complexType>
    </element>

    <!--  SettleFee Response -->
    <element name="settleFeeResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- updateAssetValueRequest -->
    <element name="bulkUpdateAssetValueRequest">
        <annotation>
            <documentation>
                denní přecenění (tržní hodnota + auv), přecenění přes klienta a jeho investice
                hromadný request po x klienech, počet položek (klientů) plněných v requestu by měl být parametrizovatelný
            </documentation>
        </annotation>
        <complexType>
            <sequence maxOccurs="500">
                <element name="updateAssetValue" type="inv:updateAssetValue"/>
            </sequence>
        </complexType>
    </element>

    <!--  bulkUpdateAssetValueResponse -->
    <element name="bulkUpdateAssetValueResponse">
        <complexType>
            <sequence maxOccurs="unbounded">
                <element name="result" type="inv:bulkAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- bulkReversedUpdateAssetValueRequest -->
    <element name="bulkReversedUpdateAssetValueRequest">
        <annotation>
            <documentation>
                storno předchozího přecenění CP před zaúčtováním přecenění nového
            </documentation>
        </annotation>
        <complexType>
            <sequence maxOccurs="500">
                <element name="reversedUpdateAssetValue" type="inv:reversedUpdateAssetValue"/>
            </sequence>
        </complexType>
    </element>

    <!--  bulkReversedUpdateAssetValueRequest -->
    <element name="bulkReversedUpdateAssetValueResponse">
        <complexType>
            <sequence maxOccurs="unbounded">
                <element name="result" type="inv:bulkAccResult"/>
            </sequence>
        </complexType>
    </element>

    <!-- receiveCouponRequest -->
    <element name="receiveCouponRequest">
        <annotation>
            <documentation>
                výplata kuponu klientovi na účet, o částku kuponu je snížena evidované hodnota auv na podrozvaze
            </documentation>
        </annotation>
        <complexType>
            <sequence maxOccurs="1000">
                <element name="receiveCoupon" type="inv:receiveCoupon"/>
            </sequence>
        </complexType>
    </element>


    <!-- receiveCoupon Response -->
    <element name="receiveCouponResponse">
        <complexType>
            <sequence maxOccurs="unbounded">
                <element name="result" type="inv:bulkAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- ChargeStandaloneFeeRequest -->
    <element name="chargeStandaloneFeeRequest">
        <annotation>
            <documentation>
                pro obsluhu situací, kdy klient platí poplatek, který není součástí jiné operace (nákup, prodej, převod) - např. mgm. fee
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references"/>                 <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                                 <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="postingEntry" type="inv:postingEntryCHRST"/>
            </sequence>
        </complexType>
    </element>


    <!-- ChargeStandaloneFeeResponse -->
    <element name="chargeStandaloneFeeResponse">
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- TradeSellOrMaturityRequest -->
    <element name="tradeSellOrMaturityRequest">
        <annotation>
            <documentation>
                účtování při zobchodování prodeje nebo splatnosti cp
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="operationTypeSpecification"
                         type="inv:maturitySellIdentification"/>     <!-- bližší určení typu operace výčtovým typem maturitySellIdentification -->
                <element name="references"
                         type="inv:references"/>                                     <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                                             <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="assetIdentification"
                         type="inv:assetIdentificationFull"/>                <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
                <element name="postingEntry" type="inv:postingEntryTRDSLM"/>
            </sequence>
        </complexType>
    </element>


    <!-- TradeSellOrMaturityResponse -->
    <element name="tradeSellOrMaturityResponse">
        <annotation>
            <documentation>
                TBD !
                synchronni
                OK - transakce yrealiozovana penize zarezervovany
                technicka chyba - SOAP fault, neco se nepovedlo v procesu zpracovani
                business chyba - nejsou prachy + reference obchodu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>


    <!-- ChargeSellOrMaturityRequest -->
    <element name="chargeSellOrMaturityRequest">
        <annotation>
            <documentation>
                zúčtování vůči klientovi při vypořádání prodeje nebo splatnosti
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="operationTypeSpecification"
                         type="inv:maturitySellIdentification"/> <!-- bližší určení typu operace výčtovým typem maturitySellIdentification -->
                <element name="references"
                         type="inv:references"/>                 <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid"
                         type="long"/>                           <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="fxRate"
                         type="decimal" minOccurs="0"/>          <!-- prodejní kurz měny instrumentu vs. měny účtu, na kterou převedeme peníze, vyplněno jen pokud se měny liší -->
                <element name="assetIdentification"
                         type="inv:assetIdentificationFull"/>    <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
                <element name="postingEntry" type="inv:postingEntryCHRSLM"/>
            </sequence>
        </complexType>
    </element>

    <!-- ChargeSellOrMaturityResponse -->
    <element name="chargeSellOrMaturityResponse">
        <annotation>
            <documentation>
                TBD !
                OK - transakce zrealiozovana penize zarezervovany
                technicka chyba - SOAP fault, neco se nepovedlo v procesu zpracovani
                business chyba - nejsou prachy + reference obchodu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
                <element name="exchangeRate" type="decimal" minOccurs="0"/>
                <element name="paymentAmount" type="inv:amountAndCurrency" minOccurs="0"/>
            </sequence>
        </complexType>
    </element>

    <!-- ChargeTransferRequest -->
    <element name="chargeTransferRequest">
        <annotation>
            <documentation>
                účtování při převodu CP z / na klienta
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="operationTypeSpecification"
                         type="inv:transferDirection"/>             <!-- bližší určení typu operace výčtovým typem transferDirection -->
                <element name="references"
                         type="inv:references"/>                                    <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                                            <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="assetIdentification"
                         type="inv:assetIdentificationFull"/>              <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
                <element name="postingEntry" type="inv:postingEntryCHRTRN"/>
            </sequence>
        </complexType>
    </element>


    <!-- ChargeTransferResponse -->
    <element name="chargeTransferResponse">
        <annotation>
            <documentation>
                TBD !
                synchronni
                OK - transakce zrealizovana penize zarezervovany
                technicka chyba - SOAP fault, neco se nepovedlo v procesu zpracovani
                business chyba - nejsou prachy + reference obchodu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>

   <!-- chargeCouponPaymentLiabilityRequest -->
    <element name="chargeCouponPaymentLiabilityRequest">
        <annotation>
            <documentation>
                zaúčtování závazku výplaty kuponu k datu ex-kuponu
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references"
                         type="inv:references"/>                                <!-- reference k operaci, popis uveden u typu "references" -->
                <element name="cuid" type="long"/>                              <!-- unikátní identifikátor klienta, kterého se operace týká-->
                <element name="assetIdentification"
                         type="inv:assetIdentificationISIN"/>                   <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationISIN" -->
                <element name="postingEntry" type="inv:postingEntryCHRCPL"/>
            </sequence>
        </complexType>
    </element>

    <!-- chargeCouponPaymentLiabilityResponse -->
    <element name="chargeCouponPaymentLiabilityResponse">
        <annotation>
            <documentation>
                synchronni
                OK - transakce provedena
                SOAP fault - neco se nepovedlo v procesu zpracovani requestu
                NOT_ACCOUNTED - business příčina způsobila nerealizaci transakce
                ERROR - technická chyba při realizaci taransakce způsobila nerealizaci transakce
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult"/>
            </sequence>
        </complexType>
    </element>

    <element name="blockBuyAmountRequest">
        <annotation>
            <documentation>
                Request to block requested amount for investment order.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="cuid" type="long">
                    <annotation>
                        <documentation>CUID</documentation>
                    </annotation>
                </element>
                <element name="references" type="inv:references">
                    <annotation>
                        <documentation>References</documentation>
                    </annotation>
                </element>
                <element name="assetIdentification" type="inv:assetIdentificationFull">
                    <annotation>
                        <documentation>Asset identification</documentation>
                    </annotation>
                </element>
                <element name="blockItem" type="inv:blockItem">
                    <annotation>
                        <documentation>Block item</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="blockBuyAmountResponse">
        <annotation>
            <documentation>
                Response returning hold/blocking identification of requested amount for investment order.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="idHold" type="long">
                    <annotation>
                        <documentation>Bank hold ID</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="receiveDividendRequest">
        <annotation>
            <documentation>
                Request to account dividend for specified client a type of stocks.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="dividend" type="inv:dividend" maxOccurs="1000">
                    <annotation>
                        <documentation>Dividend</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="receiveDividendResponse">
        <annotation>
            <documentation>
                Response returning references to processed trades.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="bulkAccResult" type="inv:bulkAccResult" maxOccurs="1000">
                    <annotation>
                        <documentation>Result of processed bulk</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="IBKRAccountTransactionRequest">
        <annotation>
            <documentation>
                Request to account transactions over the Master Account (MA) / Proprietary Account (PA) used by AB for equity/ETL trades.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="transactionAmount" type="inv:amountAndCurrency">
                    <annotation>
                        <documentation>Transaction amount</documentation>
                    </annotation>
                </element>
                <element name="ibkrTransactionType" type="inv:ibkrTransactionType">
                    <annotation>
                        <documentation>IBKR transaction type</documentation>
                    </annotation>
                </element>
                <element name="valuta" type="date">
                    <annotation>
                        <documentation>Datum valuty</documentation>
                    </annotation>
                </element>
                <element name="references" type="inv:references" minOccurs="0">
                    <annotation>
                        <documentation>References</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="IBKRAccountTransactionResponse">
        <annotation>
            <documentation>
                Response returning result of accounting.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult">
                    <annotation>
                        <documentation>Result</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="chargeClientSellRequest">
        <annotation>
            <documentation>
                Služba zaúčtovává úhrady poplatků trhu vůči IBKR (Interactive brokers).
                The service accounts for the payment of market fees to IBKR (Interactive brokers).
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="references" type="inv:references"/>
                <element name="cuid" type="long"/>
                <element name="assetIdentification" type="inv:assetIdentificationFull" minOccurs="0"/>
                <element name="postingEntry" type="inv:postingEntryStlFee"/>
            </sequence>
        </complexType>
    </element>

    <element name="chargeClientSellResponse">
        <annotation>
            <documentation>
                Odpověď na zaúčtování.
                Response returning result of accounting.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="inv:investAccResult">
                    <annotation>
                        <documentation>Result</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
