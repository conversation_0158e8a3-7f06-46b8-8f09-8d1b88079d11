<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema targetNamespace="http://airbank.cz/obs/ws/rewardWS"
        xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified"
        xmlns:rwd="http://airbank.cz/obs/ws/reward">

    <import namespace="http://airbank.cz/obs/ws/reward" schemaLocation="../xsd/reward.xsd"/>


    <!--   reward request-->
    <element name="rewardRequest">
        <annotation>
            <documentation>
                vyplaceni odmeny
            </documentation>
        </annotation>
        <complexType>
            <sequence maxOccurs="100">
                <element name="rewardBulk" type="rwd:rewardItem"/>
            </sequence>
        </complexType>
    </element>

    <!--   rewardResponse -->
    <element name="rewardResponse">
        <complexType>
            <sequence>
                <element name="resultBulk" type="rwd:rewardResp"/>
            </sequence>
        </complexType>
    </element>



    <!--   getRewardPayoutStatus request-->
    <element name="getRewardPayoutStatusRequest">
        <annotation>
            <documentation>
                pro zadaného cuida a kód odměny vrátí informace, zda byl dosažen limit pro vyplácení odměn a dosud vyplacenou částku
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="cuid" type="long"/>
                <element name="rewardCode" type="string"/>
            </sequence>
        </complexType>
    </element>

    <!--   getRewardPayoutStatus response -->
    <element name="getRewardPayoutStatusResponse">
        <annotation>
            <documentation>
                pro zadaného cuida a kód odměny vrátí informace, zda byl dosažen limit pro vyplácení odměn a dosud vyplacenou částku
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="limitReached" type="boolean">
                    <annotation>
                        <documentation>
                            byl/nebyl dosažen limit pro vyplacení odměny nastavený v parametrech odměny resp. true se vrací tehdy, pokud již klientovi bylo vyplaceno maximální povolené množství odměn daného typu
                        </documentation>
                    </annotation>
                </element>
                <element name="amountPaid" type="decimal">
                    <annotation>
                        <documentation>
                            celková suma odměn (v CZK) daného typu vyplacená klientovi za období, na kterém je sledován limit (pro roční limity je to rok)
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
