<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAutentizationAutorizationWS/"
  xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema">

  <xsd:import schemaLocation="../xsd/IdentificationTypeTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />
  <xsd:import schemaLocation="../xsd/AutentizationAuthorization.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />
  <xsd:import schemaLocation="../xsd/Common.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />

  <xsd:element name="resetPasswordRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="userName" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>user name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="birthday" type="xsd:date" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>birthday of user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="deliveryChannel" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              kanal doruceni noveho hesla
              SMS - default - odesila SMS
              RESPONSE - vygenerovane heslo v odpovedi
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authID" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>ID of external authorization</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cuid" type="xsd:long" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>CUID of disponent</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="resetPasswordResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:CommonResponse">
          <xsd:sequence>
            <xsd:element name="result" type="Q1:AuthResultType">
              <xsd:annotation>
                <xsd:documentation>Result of authorization</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="remainingAttempts" type="xsd:int" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>Remaining attempts for authorization</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="encryptedPassword" type="xsd:base64Binary" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>Encrypted password</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="sendAuthSMSRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:SendAuthSMSType">
          <xsd:sequence>
            <xsd:element name="actionCode" type="Q1:ActionType" minOccurs="0"/>
            <xsd:element name="operationType" type="xsd:string" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>
                  Prostý text, který slouží jen pro logování
                </xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="mobileNumber" type="xsd:string" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>
                  Autorizační sms se pošle na zadané
                  číslo. Použítí
                  pří zmeně
                  autorizačního mobilu.
                </xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0">
              <xsd:annotation>
                <xsd:documentation>
				  bezne se bere cuid z businessContextu, ale u resetu bezpecnostnich prvku je potreba ho poslat v requestu, protoze klient neni prihlaseny
                </xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getUserSecurityElementRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="userName" type="xsd:string"/>
        <xsd:element name="birthday" type="xsd:date"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getUserSecurityElementResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="Q1:AuthResultType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getLoggedUserSecurityElementRequest">
    <xsd:complexType>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getLoggedUserSecurityElementResponse">
    <xsd:complexType>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getAuthTypesRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="actionTypeCode" type="Q1:ActionType">
          <xsd:annotation>
            <xsd:documentation>
              Viz: ActionType v Common.xsd
            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="idContract" type="xsd:long" minOccurs="0">
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getAuthTypesResponse">
    <xsd:annotation>
      <xsd:documentation>Možné typy (kódy):
        PWD_OTP - heslo+SMS - použití v
        přihlášené aplikaci
        OTP - SMS - použití v přihlášené aplikaci
        PWDCMD - heslo
        - použití v přihlášené aplikaci na změnu stávajícího hesla
        PWD -
        username+heslo pro login operátora - login operátora
        QUESTION -
        bezpečnostní otázky - login / použití v přihlášené aplikaci
        PWD_PPF -
        username+heslo+třetí údaj, případně SMS - login klienta
        NO_AUTH - žádná
        autorizace pro daný typ není potřebná (Je pouze fake autorizační typ, v
        OBS není implementován. Pouze je na žádost HCI natvrdo ve WebService).
        SIGNPAD - autorizace signpadem. (Je pouze fake autorizační typ, v OBS není
        implementován. Pouze je na žádost HCI natvrdo ve WebService).
        BLUE_SIGN -
        autorizace ručním podpisem. (Je pouze fake autorizační typ, v OBS není
        implementován. Pouze je na žádost HCI natvrdo ve WebService).
        NO_AUTH_SPB - uz se nepouziva
        PWD_SPB - uz se nepouziva
        PICTURE - security pictures
      </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:annotation>
        <xsd:documentation>povolené autorizační/autentizační typy da
        </xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
        <xsd:element name="identificationType" type="Q1:ObsIdentificationTypeTO" minOccurs="0" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>
              Uživatelské/operátorské
              autentizační/autorizační typy
              setříděné podle priority. (Typy
              autorizací na výstupu pro aktuálně
              dotazovaný kanál můžou být omezeny
              na základě informací z blazu).

            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="operLoginRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="param" type="Q1:LoginAuthType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="operLoginResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="Q1:AuthResultType"/>
        <xsd:element name="operCuid" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Zamestnanecke cislo operatora</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fullName" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Jméno a příjmení operátora</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="operUsername" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>username operátora</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="logoutRequest">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="sendAuthSMSResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="Q1:SendAuthSMSResultType">
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyPasswordRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="password" type="xsd:base64Binary">
          <xsd:annotation>
            <xsd:documentation>statické heslo - see wiki https://wiki.apedie.abank.cz/pages/viewpage.action?pageId=*********</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="actionCode" type="Q1:ActionType">
          <xsd:annotation>
            <xsd:documentation>kód akce</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyPasswordResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="Q1:AuthResultType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="logoutResponse">
    <xsd:complexType></xsd:complexType>
  </xsd:element>

  <xsd:element name="operLogoutRequest">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="operLogoutResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyOperatorPasswordRequest">
    <xsd:annotation>
      <xsd:documentation>heslo operátora</xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="password" type="xsd:base64Binary">
          <xsd:annotation>
            <xsd:documentation>statické heslo - see wiki https://wiki.apedie.abank.cz/pages/viewpage.action?pageId=*********</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyOperatorPasswordResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="Q1:AuthResultType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyOperatorPasswordFault">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="verifyOperatorPasswordFault" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyAuthentizationRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="actionTypeCode" type="Q1:ActionType" minOccurs="0"/>
        <xsd:element name="authorization" type="Q1:AuthType"/>
        <xsd:element name="mobileNumber" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>mobilní číslo (pouze pro actionTypeCode = SIGN_REG nebo CONFIRMPASSWORD)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authorizerCuid" type="xsd:long" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>cuid autorizujici osoby. je-li vyplneno, ma prednost pred hodnotou z business contextu</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyAuthentizationResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="Q1:AuthResultType">
          <xsd:annotation>
            <xsd:documentation>Result of authorization</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remainingAttempts" type="xsd:int" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Remaining attempts for authorization</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyAuthentizationFault">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="verifyAuthentizationFault" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyActionAllowanceRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="actionTypeCode" type="Q1:ActionType">
          <xsd:annotation>
            <xsd:documentation>code of action</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyActionAllowanceResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="allowad" type="xsd:boolean">
          <xsd:annotation>
            <xsd:documentation>true when action is allowed</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyICCAuthenticationRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>cuid of customer</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="operatorId" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>employee number of operator</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="sessionId" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>id of authorized session</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="callId" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>call id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="verifyICCAuthenticationResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="result" type="Q1:AuthResultType">
          <xsd:annotation>
            <xsd:documentation>result of authorization</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="blockChannelRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cuid" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>customer identification by CIF id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="channelCode">
          <xsd:annotation>
          <xsd:documentation>code of blocked channel</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="IB"/>
            <xsd:enumeration value="BRANCH"/>
            <xsd:enumeration value="OPENAPI"/>
            <xsd:enumeration value="ICC"/>
            <xsd:enumeration value="SPB"/>
          </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="businessProcess" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>business process code of blocked channel</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="ANTIPHISHING"/>
              <xsd:enumeration value="PROCESSING_CLIENT_DEATH"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="blockChannelResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="logInToICCRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cuid" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>customer identification by CIF id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="operatorId" type="xsd:long">
          <xsd:annotation>
            <xsd:documentation>employee number of operator</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="authId" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>id of external authorization</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="logInToICCResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="sendLoginNotificationRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="loginEventId" type="xsd:string"/>
        <xsd:element name="businessProcessEvent" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="sendLoginNotificationResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="markSMSUnusableRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="operationDesc" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="markSMSUnusableResponse">
    <xsd:complexType>
      <xsd:sequence>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getAccessStatusRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="CUID" type="xsd:long"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

  <xsd:element name="getAccessStatusResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accessStatus" type="Q1:AccessStatusType" maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>Stavy přístupů do banky pro klienta na vstupu</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="pwdPpfIdenticationLocked" type="xsd:boolean">
          <xsd:annotation>
            <xsd:documentation>v tabulce Bc_PersonIdentification pro typ identifikace PWD_PPF a platný záznam platí, že LOCKEDUNTIL je větší než aktuální datum/čas</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
