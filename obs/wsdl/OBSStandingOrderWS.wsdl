<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsStandingOrderWS/" xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="obsStandingOrderWS"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsStandingOrderWS/" xmlns:com="http://arbes.com/ib/core/ppf/ws/common/">
    <types>
        <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsStandingOrderWS/">
            <xsd:include schemaLocation="OBSStandingOrderWS.xsd"/>
        </xsd:schema>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <xsd:import
                namespace="http://arbes.com/ib/core/ppf/ws/common/"
                schemaLocation="../xsd/Common.xsd">
            </xsd:import>
        </xsd:schema>
    </types>

    <message name="fault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="findStandingOrderRequest1">
        <part name="parameters" element="tns:findStandingOrderRequest"/>
    </message>
    <message name="findStandingOrderResponse1">
        <part name="parameters" element="tns:findStandingOrderResponse"/>
    </message>

    <message name="findStandingOrderNgRequest">
        <part name="parameters" element="tns:findStandingOrderNgRequest"/>
    </message>
    <message name="findStandingOrderNgResponse">
        <part name="parameters" element="tns:findStandingOrderNgResponse"/>
    </message>

    <message name="getAccountPocketMoneyPaymentsRequest">
        <part name="parameters" element="tns:getAccountPocketMoneyPaymentsRequest"/>
    </message>
    <message name="getAccountPocketMoneyPaymentsResponse">
        <part name="parameters" element="tns:getAccountPocketMoneyPaymentsResponse"/>
    </message>

    <message name="getStandingOrderRequest1">
        <part name="parameters" element="tns:getStandingOrderRequest"/>
    </message>
    <message name="getStandingOrderResponse1">
        <part name="parameters" element="tns:getStandingOrderResponse"/>
    </message>

    <message name="setStandingOrderRequest1">
        <part name="parameters" element="tns:setStandingOrderRequest"/>
    </message>
    <message name="setStandingOrderResponse1">
        <part name="parameters" element="tns:setStandingOrderResponse"/>
    </message>

    <message name="editStandingOrderRequest1">
        <part name="parameters" element="tns:editStandingOrderRequest"/>
    </message>
    <message name="editStandingOrderResponse1">
        <part name="parameters" element="tns:editStandingOrderResponse"/>
    </message>

    <message name="deleteStandingOrderRequest">
        <part name="parameters" element="tns:deleteStandingOrderRequest"/>
    </message>
    <message name="deleteStandingOrderResponse">
        <part name="parameters" element="tns:deleteStandingOrderResponse"/>
    </message>

    <message name="getStandingOrderInstanceInfoRequest">
        <part name="parameters" element="tns:getStandingOrderInstanceInfoRequest"/>
    </message>
    <message name="getStandingOrderInstanceInfoResponse">
        <part name="parameters" element="tns:getStandingOrderInstanceInfoResponse"/>
    </message>

    <message name="getStandingOrderPaymentsRequest">
        <part name="parameters" element="tns:getStandingOrderPaymentsRequest"/>
    </message>
    <message name="getStandingOrderPaymentsResponse">
        <part name="parameters" element="tns:getStandingOrderPaymentsResponse"/>
    </message>

    <message name="suspendStandingOrderRequest">
        <part name="parameters" element="tns:suspendStandingOrderRequest"/>
    </message>
    <message name="suspendStandingOrderResponse">
        <part name="parameters" element="tns:suspendStandingOrderResponse"/>
    </message>

    <portType name="obsStandingOrderWS">
        <operation name="findStandingOrder">
            <documentation>filtr pro trvalé platby

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
CLERR_UNSUPPORTED_FILTER / xx / : filtr nepodporuje daný filtrovací atribut xx
CLERR_UNSUPPORTED_ORDER / xx / : filtr nepodporuje daný řadící atribut xx
</documentation>
            <input message="tns:findStandingOrderRequest1"/>
            <output message="tns:findStandingOrderResponse1"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="findStandingOrderNg">
            <documentation>filtr pro trvalé platby - nová generace umožňující vyhledávat pro více profilů v jednom volání

kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_INVALID_VALUE / isSavings / : filtr nepodporuje zaslanou hodnotu atributu
CLERR_IS_MANDATORY / profileIDs idBankAccountDebit / : alespoň jeden filtrovací atribut z uvedené dvojice musí být uveden
            </documentation>
            <input message="tns:findStandingOrderNgRequest"/>
            <output message="tns:findStandingOrderNgResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

    <operation name="getAccountPocketMoneyPayments">
        <documentation>Poskytuje informace o kapesném zasílaném na zadaný účet z jiného účtu na stejné RS.
                            Nekontroluje dispoziční oprávnění zadané osoby k debetním účtům.
                            Kontroluje pouze její dispoziční právo k zadanému kreditnímu účtu.
                            kód / atribut / hodnota : popis
                            GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                            CLERR_ACCESS / GENERAL_ERROR / : uživatel nemá právo na účet
                            INVALID_PARAMETER / cuid, idBankAccount / : osoba nebo účet není vyplněn
                            NO_DATA_FOUND / cuid, idBankAccount / : osoba nebo účet nebyl nelezen
                            ACCOUNT_NOT_ALLOWED / idBankAccount / : osoba nemá k účtu dispoziční oprávnění
        </documentation>
        <input message="tns:getAccountPocketMoneyPaymentsRequest"/>
        <output message="tns:getAccountPocketMoneyPaymentsResponse"/>
        <fault name="fault" message="tns:fault"/>
    </operation>

        <operation name="getStandingOrder">
            <documentation>metoda pro načtení dat trvalého příkazu

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet

            </documentation>
            <input message="tns:getStandingOrderRequest1"/>
            <output message="tns:getStandingOrderResponse1"/>
            <fault name="fault" message="tns:fault"/>
        </operation>
        <operation name="setStandingOrder">
            <documentation>metoda pro založení / editaci trvalého příkazu

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
CLERR_CORE_SO  / / :  jádro odmítlo přijmout platbu
ERROR_BAD_SIGNATURE / signature / : neplatná autorizace
CLERR_IS_MANDATORY / standingOrderName / : povinná položka
CLERR_IS_MANDATORY / periodicityUnit / : povinná položka
CLERR_IS_MANDATORY / contraBankCode  / : povinná položka
CLERR_IS_MANDATORY / contraAccountNumber / : povinná položka
CLERR_IS_MANDATORY / requiredCurrency  / :povinná položka
CLERR_IS_MANDATORY / amountInRequiredCurrency  / :povinná položka
CLERR_IS_MANDATORY / validFrom  / :povinná položka
CLERR_NO_DATA_FOUND / contraBankCountry  / :cílová země příjemce nenalezena
CLERR_NO_DATA_FOUND / contraAccountCountry  / :cílová země účtu nenalezena
ERR_WWW_INV_ACC_FROM // :uživatel nemá právo na účet
WWW_INV_ACCOUNT_TYPE // :špatný typ účtu (trvalý příkaz jde nastavit pouze z BÚ)
WWW_INV_BANKCODE / bankCodeCredit  / : neplatný kód banky
WWW_INV_CURRENCY / accountCurrencyCredit  / : neplatný kód měny
WWW_CZ_ACCOUNT_NUMBER / accountNumberCredit  / :číslo protiúčtu neodpovídá platnému českému formátu
WWW_IBAN_NUMBER / accountNumberCredit  / :neplatný IBAN protiúčtu
ERR_BAD_VALIDITY / validFrom  / :datum je v minulosti
ERR_ACC_NUM_CREDIT / accountNumberCredit  / :chyba při urceni ciloveho uctu u tuzemske platby
CLERR_NO_DATA_FOUND / idEnvelopeDebit  / :nenalezena zdrojová obálka
CLERR_NO_DATA_FOUND / idEnvelopeCredit  / :nenalezena cílová obálka
ERROR_ACCOUNT_BLOCKED / ACCOUNT_ERROR / :účet je blokován
CLERR_PATTERN_ERROR / CONSTSYMBOL / : neplatný konstantní symbol
CLERR_PATTERN_ERROR / VARSYMBOL / : neplatný variabilní symbol
CLERR_PATTERN_ERROR / SPECSYMBOL / : neplatný specifický symbol
ERR_RQT_PAYMENTREASON_NOTENTERED / PAYMENTREASON / : důvod platby je vyžadován
CLERR_TRNLIMIT_EXCEEDED / amount / : překročen transakční limit
CLERR_TRNLIMIT_EXCEEDED_ICC / amount / : překročen transakční limit pro ICC
CLERR_NUMBER_IS_GREATER / amount / maximalni castka : byla prekrocena maximalni castka
CLERR_CURRENCY_NOT_PERMITED / currency : z tohoto uctu neni mozne zadat trvaly prikaz v dane mene
CLERR_INVALID_TRUSTLEVEL / accountTrustLevel / : Invalid trust level for credit account
INVALID_CREDIT_ACCOUNT / contraAccountNumber / : chybne cislo kreditniho uctu
ERR_BLACKLISTED_ACCOUNT / accountNumberDebit / : the account is blacklisted, the transaction cannot be created
ERR_BLACKLISTED_ACCOUNT / accountNumberCredit / : the account is blacklisted, the transaction cannot be created

soft alerts:
code / atribute / value : description
WRN_SUBOPTIMAL_CONVERSION / idBankAccountDebit / : Payment order with suboptimal conversion
WRN_MULTIPLE_TRN_TODAY / / : Instance trvaleho prikazu byla jiz realizovana k aktualnimu dni
WRN_INSUFFICIENT_FUNDS / amount / : There are insufficient funds on debit account (only for payment orders due today)
</documentation>
            <input message="tns:setStandingOrderRequest1"/>
            <output message="tns:setStandingOrderResponse1"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="deleteStandingOrder">
            <documentation>ukončí platnost stávajícího a ukončí platnost předgenerovaných transakcí


možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
ERROR_ACCOUNT_BLOCKED / ACCOUNT_ERROR / : účet je blokován</documentation>
            <input message="tns:deleteStandingOrderRequest"/>
            <output message="tns:deleteStandingOrderResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="getStandingOrderInstanceInfo">
            <documentation>
                Nalezení informací o instancích trvalých plateb a pravidelných spoření

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_ACCESS / GENERAL_ERROR / :  uživatel nemá právo na účet
            </documentation>
            <input message="tns:getStandingOrderInstanceInfoRequest"/>
            <output message="tns:getStandingOrderInstanceInfoResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>
        <operation name="getStandingOrderPayments">
            <documentation>
                Vrátí informace o stavu trvalé platby, ID transakcí a data jejich splatnosti
            </documentation>
            <input message="tns:getStandingOrderPaymentsRequest"/>
            <output message="tns:getStandingOrderPaymentsResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="suspendStandingOrder">
            <documentation>
                Pozastaví trvalou platbu nebo pravidelné spoření, přičemž během pozastavení
                nedochází ke generování transakcí z daného předpisu.

možné chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR / GENERAL_ERROR / : obecná chyba
CLERR_INVALID_VALUE / validFrom / : datum počátku platnosti pozastavení je v minulosti
CLERR_INVALID_VALUE / validTo / : datum konce platnosti pozastavení předchází nebo je stejné s datem jeho počátku platnosti
ERR_SO_ALREADY_SUSPENDED / ERR_SO_ALREADY_SUSPENDED / : pokus o pozastavení již pozastavené trvalé platby nebo pravidelného spoření
            </documentation>
            <input message="tns:suspendStandingOrderRequest"/>
            <output message="tns:suspendStandingOrderResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>
    </portType>
    <binding name="obsStandingOrderWSSOAP" type="tns:obsStandingOrderWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="findStandingOrder">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="findStandingOrderNg">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="getAccountPocketMoneyPayments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
              <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="getStandingOrder">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="setStandingOrder">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="deleteStandingOrder">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="getStandingOrderInstanceInfo">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="getStandingOrderPayments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="suspendStandingOrder">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
    </binding>
    <service name="obsStandingOrderWS">
        <port binding="tns:obsStandingOrderWSSOAP" name="obsStandingOrderWSSOAP">
            <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsStandingOrderWS" />
        </port>
    </service>
</definitions>
