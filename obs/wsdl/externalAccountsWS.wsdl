<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
        xmlns="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        xmlns:tns="http://airbank.cz/obs/ws/externalAccountsWS"
        targetNamespace="http://airbank.cz/obs/ws/externalAccountsWS">

    <types>
        <xsd:schema>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
            <xsd:import namespace="http://airbank.cz/obs/ws/externalAccountsWS" schemaLocation="externalAccountsWS.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="createAccountRequest">
        <part name="parameters" element="tns:createAccountRequest" />
    </message>

    <message name="createAccountResponse">
        <part name="parameters" element="tns:createAccountResponse" />
    </message>

    <message name="changeAccountStatusRequest">
        <part name="parameters" element="tns:changeAccountStatusRequest" />
    </message>

    <message name="changeAccountStatusResponse">
        <part name="parameters" element="tns:changeAccountStatusResponse" />
    </message>

    <message name="getTransactionsRequest">
        <part name="parameters" element="tns:getTransactionsRequest" />
    </message>

    <message name="getTransactionsResponse">
        <part name="parameters" element="tns:getTransactionsResponse" />
    </message>

    <message name="getBalancesRequest">
        <part name="parameters" element="tns:getBalancesRequest" />
    </message>

    <message name="getBalancesResponse">
        <part name="parameters" element="tns:getBalancesResponse" />
    </message>

    <message name="createLocalPaymentsRequest">
        <part name="parameters" element="tns:createLocalPaymentsRequest" />
    </message>

    <message name="createLocalPaymentsResponse">
        <part name="parameters" element="tns:createLocalPaymentsResponse" />
    </message>

    <message name="getPaymentStatusesRequest">
        <part name="parameters" element="tns:getPaymentStatusesRequest" />
    </message>

    <message name="getPaymentStatusesResponse">
        <part name="parameters" element="tns:getPaymentStatusesResponse" />
    </message>

    <message name="getTurnoverStatsRequest">
        <part name="parameters" element="tns:getTurnoverStatsRequest" />
    </message>

    <message name="getTurnoverStatsResponse">
        <part name="parameters" element="tns:getTurnoverStatsResponse" />
    </message>

    <portType name="externalAccountsPortType">
        <operation name="createAccount">
            <documentation>
                vytvoření účtu
            </documentation>
            <input message="tns:createAccountRequest" />
            <output message="tns:createAccountResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="changeAccountStatus">
            <documentation>
                změna stavu účtu (blokace, uzavření)
            </documentation>
            <input message="tns:changeAccountStatusRequest" />
            <output message="tns:changeAccountStatusResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getTransactions">
            <documentation>
                dotaz na (účetní) pohyby na účtu za období
            </documentation>
            <input message="tns:getTransactionsRequest" />
            <output message="tns:getTransactionsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getBalances">
            <documentation>
                dotaz na zůstatky účtů
            </documentation>
            <input message="tns:getBalancesRequest" />
            <output message="tns:getBalancesResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="createLocalPayments">
            <documentation>
                vytvoření domácích platebních příkazů
            </documentation>
            <input message="tns:createLocalPaymentsRequest" />
            <output message="tns:createLocalPaymentsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getPaymentStatuses">
            <documentation>
                dotaz na stavy plateb
            </documentation>
            <input message="tns:getPaymentStatusesRequest" />
            <output message="tns:getPaymentStatusesResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getTurnoverStats">
            <documentation>
                dotaz na statistiku denních obratů
            </documentation>
            <input message="tns:getTurnoverStatsRequest" />
            <output message="tns:getTurnoverStatsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

    </portType>

    <binding name="externalAccountsBinding" type="tns:externalAccountsPortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="createAccount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="changeAccountStatus">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getTransactions">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getBalances">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createLocalPayments">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPaymentStatuses">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getTurnoverStats">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="externalAccountsService">
        <port binding="tns:externalAccountsBinding" name="externalAccountsPort">
            <soap:address location="/ws/externalAccountsWS" />
        </port>
    </service>

</definitions>