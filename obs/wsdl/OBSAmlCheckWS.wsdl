<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsAmlCheckWS/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    name="obsAmlCheckWS"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAmlCheckWS/"
    >

    <types>
        <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAmlCheckWS/">
            <xsd:include schemaLocation="OBSAmlCheckWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="amlCheckRequest">
        <part element="tns:amlCheckRequest" name="parameters"/>
    </message>

    <message name="amlCheckResponse">
        <part name="parameters" element="tns:amlCheckResponse"/>
    </message>

    <message name="amlCheckSmeCustomerRequest">
        <part element="tns:amlCheckSmeCustomerRequest" name="parameters"/>
    </message>

    <message name="amlCheckSmeCustomerResponse">
        <part name="parameters" element="tns:amlCheckSmeCustomerResponse"/>
    </message>

    <message name="amlCheckLegalEntityRequest">
        <part element="tns:amlCheckLegalEntityRequest" name="parameters"/>
    </message>

    <message name="amlCheckLegalEntityResponse">
        <part name="parameters" element="tns:amlCheckLegalEntityResponse"/>
    </message>

    <portType name="obsAmlCheck">
        <operation name="amlCheck">
            <documentation>
                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován securityContext: pouze pro systém IB

                Chyby:
                kód / atribut / hodnota - popis
                ERROR_AML_ID / / - neni vyplněno idAplication nebo cuid. Musí být uveden alespoň jeden atribut z těchto dvou.
                GENERAL_ERROR /GENERAL_ERROR / - obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:amlCheckRequest"/>
            <output message="tns:amlCheckResponse"/>
            <fault name="fault" message="tns:faultMessage"/>
        </operation>
        <operation name="amlCheckSmeCustomer">
            <documentation>
                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován securityContext: pouze pro systém IB

                Chyby:
                kód / atribut - popis
                GENERAL_ERROR /GENERAL_ERROR - obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR - systém nestihl zpracovat požadavek včas, případné změny byly odrolovány
            </documentation>
            <input message="tns:amlCheckSmeCustomerRequest"/>
            <output message="tns:amlCheckSmeCustomerResponse"/>
            <fault name="fault" message="tns:faultMessage"/>
        </operation>
        <operation name="amlCheckLegalEntity">
            <documentation>
                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován securityContext: pouze pro systém IB

                Chyby:
                kód / atribut - popis
                GENERAL_ERROR /GENERAL_ERROR - obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR - systém nestihl zpracovat požadavek včas, případné změny byly odrolovány
            </documentation>
            <input message="tns:amlCheckLegalEntityRequest"/>
            <output message="tns:amlCheckLegalEntityResponse"/>
            <fault name="fault" message="tns:faultMessage"/>
        </operation>
    </portType>

    <binding name="obsAmlCheckSOAP" type="tns:obsAmlCheck">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="amlCheck">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </fault>
        </operation>
        <operation name="amlCheckSmeCustomer">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </fault>
        </operation>
        <operation name="amlCheckLegalEntity">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </fault>
        </operation>
    </binding>

    <service name="obsAmlCheckWS">
        <port binding="tns:obsAmlCheckSOAP" name="obsAmlCheckSOAP">
            <soap:address location="/ws/obsAmlCheckWS"/>
        </port>
    </service>
</definitions>
