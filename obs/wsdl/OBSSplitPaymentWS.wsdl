<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/obs/ws/obsSplitPaymentWS"
                  xmlns="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
                  name="obsSplitPaymentWS"
                  targetNamespace="http://airbank.cz/obs/ws/obsSplitPaymentWS">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/obsSplitPaymentWS">
            <xsd:include schemaLocation="OBSSplitPaymentWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="getSplitPaymentParamsRequest">
        <part element="tns:getSplitPaymentParamsRequest" name="parameters"/>
    </message>
    <message name="getSplitPaymentParamsResponse">
        <part element="tns:getSplitPaymentParamsResponse" name="parameters"/>
    </message>
    <message name="getSplitPaymentParamsFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getSplitPaymentLimitParamsRequest">
        <part element="tns:getSplitPaymentLimitParamsRequest" name="parameters"/>
    </message>
    <message name="getSplitPaymentLimitParamsResponse">
        <part element="tns:getSplitPaymentLimitParamsResponse" name="parameters"/>
    </message>
    <message name="getSplitPaymentLimitParamsFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="getSplitPaymentPreContractDocumentRequest">
        <part name="parameters" element="tns:getSplitPaymentPreContractDocumentRequest"/>
    </message>
    <message name="getSplitPaymentPreContractDocumentResponse">
        <part name="parameters" element="tns:getSplitPaymentPreContractDocumentResponse"/>
    </message>
    <message name="getSplitPaymentPreContractDocumentFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="createSplitPaymentRequest">
        <part element="tns:createSplitPaymentRequest" name="parameters"/>
    </message>
    <message name="createSplitPaymentResponse">
        <part element="tns:createSplitPaymentResponse" name="parameters"/>
    </message>
    <message name="createSplitPaymentFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <message name="cancelSplitPaymentRequest">
        <part element="tns:cancelSplitPaymentRequest" name="parameters"/>
    </message>
    <message name="cancelSplitPaymentResponse">
        <part element="tns:cancelSplitPaymentResponse" name="parameters"/>
    </message>
    <message name="cancelSplitPaymentFault">
        <part name="parameters" element="com:ErrorsListType"/>
    </message>

    <portType name="obsSplitPaymentWS">
        <operation name="getSplitPaymentParams">
            <documentation>
            </documentation>
            <input message="tns:getSplitPaymentParamsRequest"/>
            <output message="tns:getSplitPaymentParamsResponse"/>
            <fault name="fault" message="tns:getSplitPaymentParamsFault"/>
        </operation>
        <operation name="getSplitPaymentLimitParams">
            <documentation>Metoda poskytne produktovou parametrizaci z OBS pro produkt Rozložení platby (Split payment)</documentation>
            <input message="tns:getSplitPaymentLimitParamsRequest"/>
            <output message="tns:getSplitPaymentLimitParamsResponse"/>
            <fault name="fault" message="tns:getSplitPaymentLimitParamsFault"/>
        </operation>
        <operation name="getSplitPaymentPreContractDocument">
            <documentation>metoda na zakladě vstupních parametrů vrátí id na PDF verzi generovaného dokumentu.

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován technicalContext: ne

                Chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
            </documentation>
            <input message="tns:getSplitPaymentPreContractDocumentRequest"/>
            <output message="tns:getSplitPaymentPreContractDocumentResponse"/>
            <fault name="fault" message="tns:getSplitPaymentPreContractDocumentFault"/>
        </operation>
        <operation name="createSplitPayment">
            <documentation>
            </documentation>
            <input message="tns:createSplitPaymentRequest"/>
            <output message="tns:createSplitPaymentResponse"/>
            <fault name="fault" message="tns:createSplitPaymentFault"/>
        </operation>
        <operation name="cancelSplitPayment">
            <documentation>
            </documentation>
            <input message="tns:cancelSplitPaymentRequest"/>
            <output message="tns:cancelSplitPaymentResponse"/>
            <fault name="fault" message="tns:cancelSplitPaymentFault"/>
        </operation>
    </portType>

    <binding name="obsSplitPaymentWSSOAP" type="tns:obsSplitPaymentWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="getSplitPaymentParams">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getSplitPaymentLimitParams">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="getSplitPaymentPreContractDocument">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>
        <operation name="createSplitPayment">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="cancelSplitPayment">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>

    <service name="obsSplitPaymentWS">
        <documentation></documentation>
        <port binding="tns:obsSplitPaymentWSSOAP" name="obsSplitPaymentWSSOAP">
            <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsSplitPaymentWS"/>
        </port>
    </service>

</definitions>
