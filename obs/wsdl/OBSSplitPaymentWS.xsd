<schema
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        targetNamespace="http://airbank.cz/obs/ws/obsSplitPaymentWS">

  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/BinDocument.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/ContractTO.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Application.xsd"/>
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/TransactionTO.xsd"/>

  <element name="getSplitPaymentParamsRequest">
    <complexType>
      <sequence>
        <element name="splitPaymentAmount" type="decimal">
          <annotation>
            <documentation>Částka k rozložení = výše úvěru.</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getSplitPaymentParamsResponse">
    <complexType>
      <sequence>
        <element name="productVariantCode" type="string">
          <annotation>
            <documentation>Kód produktové varianty</documentation>
          </annotation>
        </element>
        <element name="idProductVariant" type="long">
          <annotation>
            <documentation>Identifikátor produktové varianty</documentation>
          </annotation>
        </element>
        <element name="splitPaymentAmount" type="decimal">
          <annotation>
            <documentation>Výše úvěru</documentation>
          </annotation>
        </element>
        <element name="repaymentPeriod" type="int">
          <annotation>
            <documentation>Délka splácení (počet splátek)</documentation>
          </annotation>
        </element>
        <element name="utilizationDate" type="date">
          <annotation>
            <documentation>Datum čerpání</documentation>
          </annotation>
        </element>
        <element name="instalmentAmount" type="decimal">
          <annotation>
            <documentation>Výše splátky</documentation>
          </annotation>
        </element>
        <element name="lastInstalmentAmount" type="decimal">
          <annotation>
            <documentation>Výše poslední splátky (může se lišit od standardní výše splátky)</documentation>
          </annotation>
        </element>
        <element name="providingCharges" type="decimal">
          <annotation>
            <documentation>Poplatek za poskytnutí služby</documentation>
          </annotation>
        </element>
        <element name="interestRate" type="decimal">
          <annotation>
            <documentation>Úroková sazba v %</documentation>
          </annotation>
        </element>
        <element name="annualPercentageRate" type="decimal">
          <annotation>
            <documentation>Roční procentní sazba nákladů v %</documentation>
          </annotation>
        </element>
        <element name="totalAmount" type="decimal">
          <annotation>
            <documentation>Celkové náklady na úvěr (odpovídá součtu všech splátek a poplatku)</documentation>
          </annotation>
        </element>
        <element name="firstInstalmentDate" type="date">
          <annotation>
            <documentation>Datum první splátky</documentation>
          </annotation>
        </element>
        <element name="lastInstalmentDate" type="date">
          <annotation>
            <documentation>Datum poslední splátky</documentation>
          </annotation>
        </element>
        <element name="instalmentDay" type="int">
          <annotation>
            <documentation>Den v měsíci pro splácení</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getSplitPaymentLimitParamsRequest">
    <complexType>
      <sequence/>
    </complexType>
  </element>

  <element name="getSplitPaymentLimitParamsResponse">
    <complexType>
      <sequence>
        <element name="minCredit" type="decimal">
          <annotation>
            <documentation>Produktové minimum</documentation>
          </annotation>
        </element>
        <element name="maxCredit" type="decimal">
          <annotation>
            <documentation>Produktové maximum</documentation>
          </annotation>
        </element>
        <element name="repaymentPeriod" type="int">
          <annotation>
            <documentation>Počet splátek (fixní)</documentation>
          </annotation>
        </element>
        <element name="interestRate" type="decimal">
          <annotation>
            <documentation>Úroková sazba</documentation>
          </annotation>
        </element>
        <element name="providingCharges" type="decimal">
          <annotation>
            <documentation>Poplatek za zřízení služby</documentation>
          </annotation>
        </element>
        <element name="paymentAvailabilityForSplit" type="int">
          <annotation>
            <documentation>Počet dní od provedení transakce, kdy platbu považujeme za rozložitelnou</documentation>
          </annotation>
        </element>
        <element name="defaultInstallmentDay" type="int">
          <annotation>
            <documentation>Den v měsíci pro splácení</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getSplitPaymentPreContractDocumentRequest">
    <complexType>
      <sequence>
        <element name="idApplication" type="long">
          <annotation>
            <documentation>ID zadosti</documentation>
          </annotation>
        </element>
        <element name="idRealizedTransaction" type="long">
          <annotation>
            <documentation>transakce, kterou bude split payment rozkládat</documentation>
          </annotation>
        </element>
        <element name="splitPaymentAmount" type="decimal">
          <annotation>
            <documentation>částka, na kterou bude úvěr vytvořen</documentation>
          </annotation>
        </element>
        <element name="repaymentPeriod" type="int">
          <annotation>
            <documentation>počet splátek nového úvěru</documentation>
          </annotation>
        </element>
        <element name="instalmentAmount" type="decimal">
          <annotation>
            <documentation>výše splátky</documentation>
          </annotation>
        </element>
        <element name="providingCharges" type="decimal">
          <annotation>
            <documentation>poplatek za poskytnutí služby</documentation>
          </annotation>
        </element>
        <element name="totalAmount" type="decimal">
          <annotation>
            <documentation>celková částka (součet splátek a poplatku)</documentation>
          </annotation>
        </element>
        <element name="interestRate" type="decimal">
          <annotation>
            <documentation>úroková sazba</documentation>
          </annotation>
        </element>
        <element name="annualPercentageRate" type="decimal">
          <annotation>
            <documentation>RPSN</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getSplitPaymentPreContractDocumentResponse">
    <complexType>
      <sequence>
        <element name="idPdfBinDocument" type="com:BinDocumentIdentType">
          <annotation>
            <documentation>identifikátor vzniklého dokumentu v DMS ve formatu PDF</documentation>
          </annotation>
        </element>
        <element name="idHtmlBinDocument" type="com:BinDocumentIdentType">
          <annotation>
            <documentation>identifikátor vzniklého dokumentu v DMS ve formatu HTML</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="createSplitPaymentRequest">
    <complexType>
      <sequence>
        <element name="cuid" type="long" />
        <element name="idRealizedTransaction" type="long" />
        <element name="loanAmount" type="decimal" />
        <element name="annuity" type="decimal" />
        <element name="interestRate" type="decimal" />
        <element name="term" type="int" />
        <element name="instalmentDay" type="int" />
      </sequence>
    </complexType>
  </element>

  <element name="createSplitPaymentResponse">
    <complexType>
      <sequence>
        <element name="idLoan" type="long" />
        <element name="loanNumber" type="string" />
      </sequence>
    </complexType>
  </element>

  <element name="cancelSplitPaymentRequest">
    <complexType>
      <sequence>
        <element name="idLoan" type="long" />
      </sequence>
    </complexType>
  </element>

  <element name="cancelSplitPaymentResponse">
    <complexType>
      <sequence/>
    </complexType>
  </element>

</schema>
