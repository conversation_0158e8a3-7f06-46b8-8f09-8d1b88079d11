<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions
        xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        xmlns:tns="http://airbank.cz/obs/ws/documentPaymentsWS"
        targetNamespace="http://airbank.cz/obs/ws/documentPaymentsWS">
        
    <wsdl:types>
        <xsd:schema>
            <xsd:import namespace="http://airbank.cz/obs/ws/documentPaymentsWS" schemaLocation="documentPaymentsWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </wsdl:types>
    
    <wsdl:message name="findDocumentsRequest">
        <wsdl:part name="findDocumentsRequest" element="tns:findDocumentsRequest" />
    </wsdl:message>
    <wsdl:message name="findDocumentsResponse">
        <wsdl:part name="findDocumentsResponse" element="tns:findDocumentsResponse" />
    </wsdl:message>
    
    <wsdl:message name="pairRequest">
        <wsdl:part name="pairRequest" element="tns:pairRequest" />
    </wsdl:message>
    <wsdl:message name="pairResponse">
        <wsdl:part name="pairResponse" element="tns:pairResponse" />
    </wsdl:message>
    
    <wsdl:message name="unpairRequest">
        <wsdl:part name="unpairRequest" element="tns:unpairRequest" />
    </wsdl:message>
    <wsdl:message name="unpairResponse">
        <wsdl:part name="unpairResponse" element="tns:unpairResponse" />
    </wsdl:message>
    
    <wsdl:message name="findPaymentsRequest">
        <wsdl:part name="findPaymentsRequest" element="tns:findPaymentsRequest" />
    </wsdl:message>
    <wsdl:message name="findPaymentsResponse">
        <wsdl:part name="findPaymentsResponse" element="tns:findPaymentsResponse" />
    </wsdl:message>
    
    <wsdl:message name="faultMessage">
        <wsdl:part name="faultMessage" element="com:ErrorsListType" />
    </wsdl:message>
        
    <wsdl:portType name="documentPayments">
        <wsdl:operation name="findDocuments">
            <wsdl:documentation>Find DOOR documents by payment id (id of payment order, realized transaction, card hold).

Assumptions:
logged in user - no

Faults (format - "code / attribute / value : description"):
GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.</wsdl:documentation>
            <wsdl:input message="tns:findDocumentsRequest" />
            <wsdl:output message="tns:findDocumentsResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>
        <wsdl:operation name="pair">
            <wsdl:documentation>Unpair DOOR documents and payments.
When one idDocument is defined, paid all idPayments to this idDocument.
When one idPayment is defined, paid all idDocuments to this idPayment.
Other inputs have unpredictable behavior.

Assumptions:
logged in user - no

Faults (format - "code / attribute / value : description"):
GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
CLERR_NO_DATA_FOUND / idPayment / : Id payment not found.</wsdl:documentation>
            <wsdl:input message="tns:pairRequest" />
            <wsdl:output message="tns:pairResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>
        <wsdl:operation name="unpair">
            <wsdl:documentation>Unpair DOOR document and a payment. When idPayment missing, unpair all payment on DOOR document.

Assumptions:
logged in user - no

Faults (format - "code / attribute / value : description"):
GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
</wsdl:documentation>
            <wsdl:input message="tns:unpairRequest" />
            <wsdl:output message="tns:unpairResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>
        <wsdl:operation name="findPayments">
            <wsdl:documentation>Find payments by DOOR document ids.
For each document is returned collection of realized transactions and collection of payment orders.

Assumptions:
logged in user - no

Faults (format - "code / attribute / value : description"):
GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
</wsdl:documentation>
            <wsdl:input message="tns:findPaymentsRequest" />
            <wsdl:output message="tns:findPaymentsResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>
    </wsdl:portType>
    
    <wsdl:binding name="documentPaymentsSOAP" type="tns:documentPayments">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="findDocuments">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="pair">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="unpair">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="findPayments">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
        
    <wsdl:service name="documentPaymentsWS">
        <wsdl:port binding="tns:documentPaymentsSOAP" name="documentPaymentsSOAP">
            <soap:address location="/ws/documentPaymentsWS" />
        </wsdl:port>
    </wsdl:service>
    
</wsdl:definitions>