<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
        xmlns="http://schemas.xmlsoap.org/wsdl/"
        xmlns:tns="http://airbank.cz/obs/ws/pensionContractWS"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        targetNamespace="http://airbank.cz/obs/ws/pensionContractWS"
>
    <types>
        <xsd:schema>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
            <xsd:import namespace="http://airbank.cz/obs/ws/pensionContractWS" schemaLocation="pensionContractWS.xsd" />
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="createMeetingRecordRequest">
        <part name="parameters" element="tns:createMeetingRecordRequest" />
    </message>

    <message name="createMeetingRecordResponse">
        <part name="parameters" element="tns:createMeetingRecordResponse" />
    </message>

    <message name="createContractRequest">
        <part name="parameters" element="tns:createContractRequest" />
    </message>

    <message name="createContractResponse">
        <part name="parameters" element="tns:createContractResponse" />
    </message>

    <message name="createProductRequest">
        <part name="parameters" element="tns:createProductRequest" />
    </message>

    <message name="createProductResponse">
        <part name="parameters" element="tns:createProductResponse" />
    </message>

    <portType name="pensionContractPortType">
        <operation name="createMeetingRecord">
            <documentation>

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createMeetingRecordRequest" />
            <output message="tns:createMeetingRecordResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createContract">
            <documentation>

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createContractRequest" />
            <output message="tns:createContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createProduct">
            <documentation>

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                INVALID_PARAMETER / completionId / :
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createProductRequest" />
            <output message="tns:createProductResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
    </portType>

    <binding name="pensionContractBinding" type="tns:pensionContractPortType">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document" />

        <operation name="createMeetingRecord">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal" />
            </fault>
        </operation>

        <operation name="createContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal" />
            </fault>
        </operation>

        <operation name="createProduct">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal" />
            </fault>
        </operation>
    </binding>

    <service name="pensionContractService">
        <port name="pensionContractPort" binding="tns:pensionContractBinding">
            <soap:address location="/ws/pensionContractWS" />
        </port>
    </service>

</definitions>