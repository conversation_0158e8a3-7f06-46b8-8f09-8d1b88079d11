<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
        xmlns="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        xmlns:tns="http://airbank.cz/obs/ws/mortgageLoanWS"
        targetNamespace="http://airbank.cz/obs/ws/mortgageLoanWS">

    <types>
        <xsd:schema>
            <xsd:import namespace="http://airbank.cz/obs/ws/mortgageLoanWS" schemaLocation="mortgageLoanWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </types>

    <message name="getMortgageLoanParamsRequest">
        <part name="getMortgageLoanParamsRequest" element="tns:getMortgageLoanParamsRequest" />
    </message>

    <message name="getMortgageLoanParamsResponse">
        <part name="getMortgageLoanParamsResponse" element="tns:getMortgageLoanParamsResponse" />
    </message>

    <message name="getBasicParamsRequest">
        <part name="getBasicParamsRequest" element="tns:getBasicParamsRequest" />
    </message>

    <message name="getBasicParamsResponse">
        <part name="getBasicParamsResponse" element="tns:getBasicParamsResponse" />
    </message>

    <message name="setExtraTransferRequest">
        <part name="setExtraTransferRequest" element="tns:setExtraTransferRequest" />
    </message>

    <message name="setExtraTransferResponse">
        <part name="setExtraTransferResponse" element="tns:setExtraTransferResponse" />
    </message>

    <message name="getChangeParamsRequest">
        <part name="getChangeParamsRequest" element="tns:getChangeParamsRequest" />
    </message>

    <message name="getChangeParamsResponse">
        <part name="getChangeParamsResponse" element="tns:getChangeParamsResponse" />
    </message>

    <message name="setChangeParamsRequest">
        <part name="setChangeParamsRequest" element="tns:setChangeParamsRequest" />
    </message>

    <message name="setChangeParamsResponse">
        <part name="setChangeParamsResponse" element="tns:setChangeParamsResponse" />
    </message>

    <message name="getChangeLimitParamsRequest">
        <part name="getChangeLimitParamsRequest" element="tns:getChangeLimitParamsRequest" />
    </message>

    <message name="getChangeLimitParamsResponse">
        <part name="getChangeLimitParamsResponse" element="tns:getChangeLimitParamsResponse" />
    </message>

    <message name="getMrtgInterestRateHistRequest">
        <part name="parameters" element="tns:getMrtgInterestRateHistRequest" />
    </message>

    <message name="getMrtgInterestRateHistResponse">
        <part name="parameters" element="tns:getMrtgInterestRateHistResponse" />
    </message>

    <message name="findPaymentScheduleEventRequest">
        <part name="parameters" element="tns:findPaymentScheduleEventRequest" />
    </message>

    <message name="findPaymentScheduleEventResponse">
        <part name="parameters" element="tns:findPaymentScheduleEventResponse" />
    </message>

    <message name="getPaymentScheduleEventRequest">
        <part name="parameters" element="tns:getPaymentScheduleEventRequest" />
    </message>

    <message name="getPaymentScheduleEventResponse">
        <part name="parameters" element="tns:getPaymentScheduleEventResponse" />
    </message>

    <message name="getPaymentScheduleTransactionRequest">
        <part name="parameters" element="tns:getPaymentScheduleTransactionRequest" />
    </message>

    <message name="getPaymentScheduleTransactionResponse">
        <part name="parameters" element="tns:getPaymentScheduleTransactionResponse" />
    </message>

    <message name="getPDFPaymentScheduleDocRequest">
        <part name="parameters" element="tns:getPDFPaymentScheduleDocRequest" />
    </message>

    <message name="getPDFPaymentScheduleDocResponse">
        <part name="parameters" element="tns:getPDFPaymentScheduleDocResponse" />
    </message>

    <message name="createMortgageLoanRequest">
        <part name="parameters" element="tns:createMortgageLoanRequest" />
    </message>

    <message name="createMortgageLoanResponse">
        <part name="parameters" element="tns:createMortgageLoanResponse" />
    </message>

    <message name="utilizeMortgageLoanRequest">
        <part name="parameters" element="tns:utilizeMortgageLoanRequest" />
    </message>

    <message name="utilizeMortgageLoanResponse">
        <part name="parameters" element="tns:utilizeMortgageLoanResponse" />
    </message>

    <message name="finishDrawingRequest">
        <part name="parameters" element="tns:finishDrawingRequest" />
    </message>

    <message name="finishDrawingResponse">
        <part name="parameters" element="tns:finishDrawingResponse" />
    </message>

    <message name="updateMortgageLoanRequest">
        <part name="parameters" element="tns:updateMortgageLoanRequest" />
    </message>

    <message name="updateMortgageLoanResponse">
        <part name="parameters" element="tns:updateMortgageLoanResponse" />
    </message>

    <message name="updateObligationRequest">
        <part name="parameters" element="tns:updateObligationRequest" />
    </message>

    <message name="updateObligationResponse">
        <part name="parameters" element="tns:updateObligationResponse" />
    </message>

    <message name="cancelMortgageLoanApplicationRequest">
        <part name="parameters" element="tns:cancelMortgageLoanApplicationRequest" />
    </message>

    <message name="cancelMortgageLoanApplicationResponse">
        <part name="parameters" element="tns:cancelMortgageLoanApplicationResponse" />
    </message>

    <message name="getOverviewRequest">
        <part name="parameters" element="tns:getOverviewRequest" />
    </message>

    <message name="getOverviewResponse">
        <part name="parameters" element="tns:getOverviewResponse" />
    </message>

    <message name="getMortgageDetailRequest">
        <part name="parameters" element="tns:getMortgageDetailRequest" />
    </message>

    <message name="getMortgageDetailResponse">
        <part name="parameters" element="tns:getMortgageDetailResponse" />
    </message>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>


    <portType name="mortgageLoan">

        <operation name="getOverview">
            <documentation>
                Get overview (most common, efficiently retrievable data) of mortgage loans.

                Assumptions: logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request.
                CLERR_NO_DATA_FOUND / profileId / value : profileId doesn't exist.
            </documentation>
            <input message="tns:getOverviewRequest" />
            <output message="tns:getOverviewResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getMortgageDetail">
            <documentation>
                Return mortage details.

                Assumptions: logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                INVALID_PARAMETER / - / cuid : The CUID has not been entered.
                PERSON_DOESNT_EXIST / - / cuid : The specified person does not exist in OBS.
            </documentation>
            <input message="tns:getMortgageDetailRequest" />
            <output message="tns:getMortgageDetailResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getMortgageLoanParams">
            <documentation>
                Return requested mortage loan params.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getMortgageLoanParamsRequest" />
            <output message="tns:getMortgageLoanParamsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getBasicParams">
            <documentation>
                Return basic mortage loan params.

                MortgageType input: HYNEW/HYREF

                Assumptions:
                logged in user - no

            </documentation>
            <input message="tns:getBasicParamsRequest" />
            <output message="tns:getBasicParamsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="setExtraTransfer">
            <documentation>Create extra transfer on mortgage loan.
                Return recalculated payment schedules before and after creation extra transfer.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_ACCESS / idLoan / : The loan is not accessible for logged user.
                CLERR_NO_DATA_FOUND / idLoan / : The loan does not exist.
                CLERR_NO_DATA_FOUND / currency / : The currency not exist.
                CLERR_MONTHLY_FINAL / GENERAL_ERROR / : Monthly final job is running.
                ERROR_BAD_SIGNATURE / signature / : Invalid authorization of extra instalment.
                MAXPERDAY / GENERAL_ERROR / : Exceeded maximum number of extra instalments per day.
                TRNREALIZATION / GENERAL_ERROR / : Error while realizing transaction of extra instalment.
                EXECUTION / GENERAL_ERROR / : The loan tied with the internal account for repayment.
                AMOUNTOVERREMAINING / amount / : The amount is greater than amount for full repayment.
                MINEXPAYMENT / amount / : The amount is lower than minimum available amount.
                LOWBALANCE / amount / : The amount is greater than available balance of debit account for transfer.
                NO_MORTGAGE_LOAN / idLoan / : The loan does not mortgage loan.
            </documentation>
            <input message="tns:setExtraTransferRequest" />
            <output message="tns:setExtraTransferResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getChangeParams">
            <documentation>Returns new parameters of mortgage loan after change.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getChangeParamsRequest" />
            <output message="tns:getChangeParamsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="setChangeParams">
            <documentation>Set change to the parameters of of mortgage loan.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:setChangeParamsRequest" />
            <output message="tns:setChangeParamsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getChangeLimitParams">
            <documentation>Returns limit parameters of mortgage loan.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getChangeLimitParamsRequest" />
            <output message="tns:getChangeLimitParamsResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="findPaymentScheduleEvent">
            <documentation>filter for payment schedulo of mortgage loan

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:findPaymentScheduleEventRequest" />
            <output message="tns:findPaymentScheduleEventResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getPaymentScheduleEvent">
            <documentation>return a detail of payment schedule items

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getPaymentScheduleEventRequest" />
            <output message="tns:getPaymentScheduleEventResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getPaymentScheduleTransaction">
            <documentation>return a list of transactions for payment schedule event

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getPaymentScheduleTransactionRequest" />
            <output message="tns:getPaymentScheduleTransactionResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getPDFPaymentScheduleDoc">
            <documentation>operation starts generating of report for payment schedule of mortgage loan and return id of binary document

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getPDFPaymentScheduleDocRequest" />
            <output message="tns:getPDFPaymentScheduleDocResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

        <operation name="getMrtgInterestRateHist">
            <documentation>Returns history of the mortgage loan interest rate by given ID of the loan
                - Actual Interest Rate is always valid from the Validity Date till next payment
                - If the loan has set the Variable Interest Rate the Interest Rate Cap is returned as well
                - If the next payment is in the current month the current month is not listed

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:getMrtgInterestRateHistRequest" />
            <output message="tns:getMrtgInterestRateHistResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createMortgageLoan">
            <documentation>Create a mortgage loan and return its number

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:createMortgageLoanRequest" />
            <output message="tns:createMortgageLoanResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="utilizeMortgageLoan">
            <documentation>
                Utilize a mortage loan.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CLERR_NO_DATA_FOUND / mortgageLoanNumber / : The loan does not exist.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                FEE_FAILED_TO_BE_PAID / chargeExceedingLTVFee / : If the exceeding LTV fee failed to be paid. Mostly due to insufficient account balance.
                ALREADY_CHARGED / chargeExceedingLTVFee / : The exceeding LTV fee has already been charged.
            </documentation>
            <input message="tns:utilizeMortgageLoanRequest" />
            <output message="tns:utilizeMortgageLoanResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="finishDrawing">
            <documentation>
                Switch mortgage from drawing state to active state and start to repay it.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CLERR_NO_DATA_FOUND / mortgageNumber / : The mortgage does not exist.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:finishDrawingRequest" />
            <output message="tns:finishDrawingResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="updateMortgageLoan">
            <documentation>
                služba pro aktualizaci hypotéky

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CLERR_NO_DATA_FOUND / mortgageLoanNumber / : The loan does not exist.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:updateMortgageLoanRequest" />
            <output message="tns:updateMortgageLoanResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="updateObligation">
            <documentation>
                služba pro aktualizaci zajištění

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CLERR_NO_DATA_FOUND / mortgageLoanNumber / : The loan does not exist.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:updateObligationRequest" />
            <output message="tns:updateObligationResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="cancelMortgageLoanApplication">
            <documentation>
                Performs cancel or reject application about mortgage loan.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CLERR_NO_DATA_FOUND / mortgageLoanNumber / : The loan does not exist.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:cancelMortgageLoanApplicationRequest" />
            <output message="tns:cancelMortgageLoanApplicationResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
    </portType>

    <binding name="mortgageLoanSOAP" type="tns:mortgageLoan">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="getOverview">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getMortgageDetail">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getMortgageLoanParams">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getBasicParams">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setExtraTransfer">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getChangeParams">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setChangeParams">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getChangeLimitParams">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="findPaymentScheduleEvent">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPaymentScheduleEvent">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPaymentScheduleTransaction">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getPDFPaymentScheduleDoc">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getMrtgInterestRateHist">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createMortgageLoan">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="utilizeMortgageLoan">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="finishDrawing">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="updateMortgageLoan">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="updateObligation">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="cancelMortgageLoanApplication">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="mortgageLoanWS">
        <port binding="tns:mortgageLoanSOAP" name="mortgageLoanSOAP">
            <soap:address location="/ws/mortgageLoanWS" />
        </port>
    </service>

</definitions>