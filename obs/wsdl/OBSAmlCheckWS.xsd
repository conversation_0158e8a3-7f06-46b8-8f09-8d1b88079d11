<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAmlCheckWS/">

    <element name="amlCheckResponse">
        <complexType>
            <sequence>
                <element name="amlRequirementID" type="int" minOccurs="0">
                    <annotation>
                        <documentation>Pokud je amlRequirementID = null - AML proběhl vpořádku.
                            Pokud se vrátí ID požadavku, potom AML neproběhl vpořádku a generoval se požadavek s tímto ID.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="amlCheckRequest">
        <complexType>
            <annotation>
                <documentation>Credentials of applicant which are to be checked
                    for AML.
                </documentation>
            </annotation>
            <sequence>
                <element name="applicationID" type="long" minOccurs="0">
                    <annotation>
                        <documentation></documentation>
                    </annotation>
                </element>
                <element name="applicantName" type="string"/>
                <element name="applicantSurname" type="string"/>
                <element name="applicantDateOfBirth" type="date" minOccurs="0"/>
                <element name="cuid" type="long" minOccurs="0"/>
                <element name="applicantCitizenship" type="string" maxOccurs="unbounded"/>
                <element name="applicantState" type="string"/>
                <element name="applicantContactState" type="string">
                    <annotation>
                        <documentation>
                            korespondencni adresa - stat
                        </documentation>
                    </annotation>
                </element>
                <element name="applicantIsPoliticallyExposed" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>politicky exponovana osoba</documentation>
                    </annotation>
                </element>
                <element name="completionPhase" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>udava, jestli se metoda vola z kompletace (true) nebo ze zadosti (false)</documentation>
                    </annotation>
                </element>
                <element name="businessProcessEvent" type="string" minOccurs="0">
                    <annotation>
                        <documentation>udalost obchodniho procesu</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="amlCheckSmeCustomerRequest">
        <complexType>
            <sequence>
                <element name="SmeCuid" type="long">
                    <annotation>
                        <documentation>CUID podnikatele</documentation>
                    </annotation>
                </element>
                <element name="CustomerCuid" type="long">
                    <annotation>
                        <documentation>CUID fyzicke osoby</documentation>
                    </annotation>
                </element>
                <element name="applicationID" type="long" minOccurs="0">
                    <annotation>
                        <documentation></documentation>
                    </annotation>
                </element>
                <element name="applicantName" type="string"/>
                <element name="applicantSurname" type="string"/>
                <element name="applicantDateOfBirth" type="date" minOccurs="0"/>
                <element name="applicantCitizenship" type="string" maxOccurs="unbounded"/>
                <element name="applicantState" type="string"/>
                <element name="applicantContactState" type="string">
                    <annotation>
                        <documentation>
                            korespondencni adresa - stat
                        </documentation>
                    </annotation>
                </element>
                <element name="applicantIsPoliticallyExposed" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>politicky exponovana osoba</documentation>
                    </annotation>
                </element>
                <element name="completionPhase" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>udava, jestli se metoda vola z kompletace (true) nebo ze zadosti (false)</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="amlCheckSmeCustomerResponse">
        <complexType>
            <sequence>
                <element name="amlRequirementID" type="int" minOccurs="0">
                    <annotation>
                        <documentation>Pokud je amlRequirementID prazdne, AML proběhl v pořádku.
                            Pokud se vrátí ID požadavku, potom AML neproběhl v pořádku a generoval se požadavek s tímto ID.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="amlCheckLegalEntityRequest">
        <complexType>
            <sequence>
                <element name="CUID" type="long">
                    <annotation>
                        <documentation>CUID pravnicke osoby</documentation>
                    </annotation>
                </element>
                <element name="businessProcessEvent" type="string">
                    <annotation>
                        <documentation>Obchodní událost vedoucí na provedení AML kontroly právnické osoby</documentation>
                    </annotation>
                </element>
                <element name="businessProcessEventRelatedCuid" type="long">
                    <annotation>
                        <documentation>CUID FON osoby, která vyvolala obchodní událost. Zde: FON cuid žadatele</documentation>
                    </annotation>
                </element>
                <element name="applicationId" type="long">
                    <annotation>
                        <documentation>ID žádosti, která vyvolala kontrolu pro ONBOARDING i COMPLETION_ENTREPRENEUR_PRODUCT_FINALIZATION. Z hlediska AML je jak fáze žádosti, tak fáze kompletace jeden případ, kdy si někdo žádá o rámcovou smlouvu.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="amlCheckLegalEntityResponse">
        <complexType>
            <sequence/>
        </complexType>
    </element>

</schema>