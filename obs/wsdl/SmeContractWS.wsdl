<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    name="SmeContractWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://airbank.cz/obs/ws/SmeContractWS/"
    targetNamespace="http://airbank.cz/obs/ws/SmeContractWS/">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/SmeContractWS/">
            <xsd:include schemaLocation="SmeContractWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <message name="createContractDocumentRequest">
        <part name="parameters" element="tns:createContractDocumentRequest" />
    </message>

    <message name="createContractDocumentResponse">
        <part name="parameters" element="tns:createContractDocumentResponse" />
    </message>

    <portType name="SmeContractWS">

        <operation name="createContractDocument">
            <documentation>
                Create new Product completion and Contract document for specified Application type.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / CUID / value : CUID does not exist.
            </documentation>
            <input message="tns:createContractDocumentRequest" />
            <output message="tns:createContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

    </portType>

    <binding name="SmeContractWSSOAP" type="tns:SmeContractWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="createContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

    </binding>

    <service name="SmeContractWS">
        <port binding="tns:SmeContractWSSOAP" name="SmeContractWSSOAP">
            <soap:address location="/ws/SmeContractWS"/>
        </port>
    </service>

</definitions>
