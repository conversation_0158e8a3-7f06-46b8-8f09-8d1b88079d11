<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsAutentizationAutorizationWS/"
    xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    name="obsAutentizationAutorizationWS" targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAutentizationAutorizationWS/" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">

    <wsdl:types>
        <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsAutentizationAutorizationWS/">
          <xsd:include schemaLocation="OBSAutentizationAutorizationWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="resetPasswordRequest">
        <wsdl:part name="parameters" element="tns:resetPasswordRequest"/>
    </wsdl:message>
    <wsdl:message name="resetPasswordResponse">
        <wsdl:part name="parameters" element="tns:resetPasswordResponse"/>
    </wsdl:message>
    <wsdl:message name="sendAuthSMSRequest">
        <wsdl:part name="parameters" element="tns:sendAuthSMSRequest"/>
    </wsdl:message>
    <wsdl:message name="sendAuthSMSResponse">
        <wsdl:part name="parameters" element="tns:sendAuthSMSResponse"/>
    </wsdl:message>
    <wsdl:message name="logoutRequest">
        <wsdl:part name="parameters" element="tns:logoutRequest"/>
    </wsdl:message>
    <wsdl:message name="logoutResponse">
        <wsdl:part name="parameters" element="tns:logoutResponse"/>
    </wsdl:message>
    <wsdl:message name="getUserSecurityElementRequest">
        <wsdl:part name="parameters" element="tns:getUserSecurityElementRequest"/>
    </wsdl:message>
    <wsdl:message name="getUserSecurityElementResponse">
        <wsdl:part name="parameters" element="tns:getUserSecurityElementResponse"/>
    </wsdl:message>
    <wsdl:message name="getLoggedUserSecurityElementRequest">
        <wsdl:part name="parameters" element="tns:getLoggedUserSecurityElementRequest"/>
    </wsdl:message>
    <wsdl:message name="getLoggedUserSecurityElementResponse">
        <wsdl:part name="parameters" element="tns:getLoggedUserSecurityElementResponse"/>
    </wsdl:message>
    <wsdl:message name="getAuthTypesRequest">
        <wsdl:part name="parameters" element="tns:getAuthTypesRequest"/>
    </wsdl:message>
    <wsdl:message name="getAuthTypesResponse">
        <wsdl:part name="parameters" element="tns:getAuthTypesResponse"/>
    </wsdl:message>
    <wsdl:message name="operLoginRequest">
        <wsdl:part name="parameters" element="tns:operLoginRequest"/>
    </wsdl:message>
    <wsdl:message name="operLoginResponse">
        <wsdl:part name="parameters" element="tns:operLoginResponse"/>
    </wsdl:message>
    <wsdl:message name="verifyPasswordRequest">
        <wsdl:part name="parameters" element="tns:verifyPasswordRequest"/>
    </wsdl:message>
    <wsdl:message name="verifyPasswordResponse">
        <wsdl:part name="parameters" element="tns:verifyPasswordResponse"/>
    </wsdl:message>
    <wsdl:message name="operLogoutRequest">
        <wsdl:part name="parameters" element="tns:operLogoutRequest"/>
    </wsdl:message>
    <wsdl:message name="operLogoutResponse">
        <wsdl:part name="parameters" element="tns:operLogoutResponse"/>
    </wsdl:message>
    <wsdl:message name="resetPasswordFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="verifyPasswordFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="getUserSecurityElementFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="getLoggedUserSecurityElementFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="sendAuthSMSFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="logoutFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="getAuthTypesFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="operLoginFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="operLogoutFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="verifyOperatorPasswordRequest">
        <wsdl:part name="parameters" element="tns:verifyOperatorPasswordRequest"/>
    </wsdl:message>
    <wsdl:message name="verifyOperatorPasswordResponse">
        <wsdl:part name="parameters" element="tns:verifyOperatorPasswordResponse"/>
    </wsdl:message>
    <wsdl:message name="verifyOperatorPasswordFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="verifyAuthentizationRequest">
        <wsdl:part name="parameters" element="tns:verifyAuthentizationRequest"/>
    </wsdl:message>
    <wsdl:message name="verifyAuthentizationResponse">
        <wsdl:part name="parameters" element="tns:verifyAuthentizationResponse"/>
    </wsdl:message>
    <wsdl:message name="verifyAuthentizationFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="verifyActionAllowanceRequest">
        <wsdl:part name="parameters" element="tns:verifyActionAllowanceRequest"/>
    </wsdl:message>
    <wsdl:message name="verifyActionAllowanceResponse">
        <wsdl:part name="parameters" element="tns:verifyActionAllowanceResponse"/>
    </wsdl:message>
    <wsdl:message name="verifyActionAllowanceFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="verifyICCAuthenticationRequest">
        <wsdl:part name="parameters" element="tns:verifyICCAuthenticationRequest"/>
    </wsdl:message>
    <wsdl:message name="verifyICCAuthenticationResponse">
        <wsdl:part name="parameters" element="tns:verifyICCAuthenticationResponse"/>
    </wsdl:message>
    <wsdl:message name="verifyICCAuthenticationFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="blockChannelRequest">
        <wsdl:part name="parameters" element="tns:blockChannelRequest"/>
    </wsdl:message>
    <wsdl:message name="blockChannelResponse">
        <wsdl:part name="parameters" element="tns:blockChannelResponse"/>
    </wsdl:message>
    <wsdl:message name="blockChannelFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="logInToICCRequest">
        <wsdl:part name="parameters" element="tns:logInToICCRequest"/>
    </wsdl:message>
    <wsdl:message name="logInToICCResponse">
        <wsdl:part name="parameters" element="tns:logInToICCResponse"/>
    </wsdl:message>
    <wsdl:message name="logInToICCFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="sendLoginNotificationRequest">
      <wsdl:part name="parameters" element="tns:sendLoginNotificationRequest"/>
    </wsdl:message>
    <wsdl:message name="sendLoginNotificationResponse">
        <wsdl:part name="parameters" element="tns:sendLoginNotificationResponse"/>
    </wsdl:message>
    <wsdl:message name="sendLoginNotificationFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="markSMSUnusableRequest">
        <wsdl:part name="parameters" element="tns:markSMSUnusableRequest"/>
    </wsdl:message>
    <wsdl:message name="markSMSUnusableResponse">
        <wsdl:part name="parameters" element="tns:markSMSUnusableResponse"/>
    </wsdl:message>
    <wsdl:message name="markSMSUnusableFault">
        <wsdl:part name="parameters" element="xsd1:ErrorsListType"/>
    </wsdl:message>
    <wsdl:message name="getAccessStatusRequest">
      <wsdl:part name="parameters" element="tns:getAccessStatusRequest"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="getAccessStatusResponse">
      <wsdl:part name="parameters" element="tns:getAccessStatusResponse"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="getAccessStatusFault">
      <wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
    </wsdl:message>

    <wsdl:portType name="obsAutentizationAutorizationWS">
        <wsdl:operation name="getUserSecurityElement">
            <wsdl:documentation>Return security questions or security pictures that the user has set. Used for non logged in user.
                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / :   general system error
                CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time</wsdl:documentation>
            <wsdl:input message="tns:getUserSecurityElementRequest"/>
            <wsdl:output message="tns:getUserSecurityElementResponse"/>
            <wsdl:fault name="fault" message="tns:getUserSecurityElementFault"/>
        </wsdl:operation>

        <wsdl:operation name="getLoggedUserSecurityElement">
            <wsdl:documentation>Return security questions or security pictures that the user has set. Used for logged in user.
                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / :   general system error
                CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time
                GENERAL_ERROR_ACCESS / GENERAL_ERROR / : person is not logged</wsdl:documentation>
            <wsdl:input message="tns:getLoggedUserSecurityElementRequest"/>
            <wsdl:output message="tns:getLoggedUserSecurityElementResponse"/>
            <wsdl:fault name="fault" message="tns:getLoggedUserSecurityElementFault"/>
        </wsdl:operation>

        <wsdl:operation name="resetPassword">
            <wsdl:documentation>Verify security questions or security pictures.
                If verification is successful and the action is reset password, sent to the client mobile new password.

                Generated faults:
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / :   general system error
                CLERR_TIMEOUT / GENERAL_ERROR / : system was not able to process the request in time.
                ERROR_NUMBER_ANSWER / / : the number of input answers is correct
                ERROR_QCODE_EMPTY / codeXX / : X-th code of the answer is not filled
                ERROR_IS_MANDATORY / textXX / : X-th text of the answer is not filled
                CLERR_ENUM / codeXX / :  X-th code of the answer not found
                CLERR_ENUM / actionCode / : actionCode not found</wsdl:documentation>
            <wsdl:input message="tns:resetPasswordRequest"/>
            <wsdl:output message="tns:resetPasswordResponse"/>
            <wsdl:fault name="fault" message="tns:resetPasswordFault"/>
        </wsdl:operation>

        <wsdl:operation name="sendAuthSMS">
            <wsdl:documentation>poslání SMS v aplikaci
                Pro actionCode = SIGN_REG nebo CONFIRMPASSWORD je možno využití i pro nepřihlášeného uživatele na LP - pak je povinný parametr mobileNumber

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován businessContext:  ne

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / :   obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_WRONG_CONFIG / actionCode / : špatná konfigurace nebo nesprávný actionType kód
                CLERR_PLACEHOLDER / smsText / :sms text neobsahuje zástupku #SMSCODE# pro sms kód
                CLERR_IS_MANDATORY / mobileNumber / : pro actionCode SIGN_REG nebo CONFIRMPASSWORD je vyžadováno na vstupu mobilní číslo</wsdl:documentation>
            <wsdl:input message="tns:sendAuthSMSRequest"/>
            <wsdl:output message="tns:sendAuthSMSResponse"/>
            <wsdl:fault name="fault" message="tns:sendAuthSMSFault"/>
        </wsdl:operation>

        <wsdl:operation name="logout">
            <wsdl:documentation>odhlášení uživatel

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.

            </wsdl:documentation>
            <wsdl:input message="tns:logoutRequest"/>
            <wsdl:output message="tns:logoutResponse"/>
            <wsdl:fault name="fault" message="tns:logoutFault"/>
        </wsdl:operation>

        <wsdl:operation name="getAuthTypes">
            <wsdl:documentation>Metoda vrátí platné autorizační typy na základě kanálu a akce.

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován businessContext:  ne

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / :   obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_OWNER_SIGN_NEEDED / idContract / :  vlastník podepisuje dodatek jako první, držitel nebo disponent může následně -
                platí pouze pro podpis prohlášení disponenta/držitele
                CLERR_AFF_TYPE_USER / idContract / : prohlášení v kanálu IB může podepsat pouze D/D je li už klientem, jinak tato chyba
                CLERR_GENERAL_CONTRACT_NOT_COMPLETED / idProfile / : neni skompletovana ramcova smlouva
            </wsdl:documentation>
            <wsdl:input message="tns:getAuthTypesRequest"/>
            <wsdl:output message="tns:getAuthTypesResponse"/>
            <wsdl:fault name="fault" message="tns:getAuthTypesFault"/>
        </wsdl:operation>

        <wsdl:operation name="operLogin">
            <wsdl:documentation>Přihlášení pro operátora

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / :   obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_IS_MANDATORY /    username    / : položka je povinná
                CLERR_IS_MANDATORY /    password    / : položka je povinná
                CLERR_BRANCH_LOGIN_DENIED / cuid / : uživatel, který se přihloašuje s operátorem do pobočkového módu, nemá právo na pobočku</wsdl:documentation>
            <wsdl:input message="tns:operLoginRequest"/>
            <wsdl:output message="tns:operLoginResponse"/>
            <wsdl:fault name="fault" message="tns:operLoginFault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyPassword">
            <wsdl:documentation>Speciální metoda pro validaci statického hesla pro typ PWD_OTP.
                Používá se pro případ, kdy je pro autorizaci pomocí hesla a SMS potřeba heslo
                zkontrolovat samostatně, než prezentační vrstva odešle příkaz k odeslání sms.

                požadované podmínky:
                přihlášeného uživatel: ano
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / :   obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_IS_MANDATORY /    actionCode / : položka je povinná
                CLERR_WRONG_CONFIG  / actionCode / : špatná konfigurace nebo nesprávný actionType kód</wsdl:documentation>
            <wsdl:input message="tns:verifyPasswordRequest"/>
            <wsdl:output message="tns:verifyPasswordResponse"/>
            <wsdl:fault name="fault" message="tns:verifyPasswordFault"/>
        </wsdl:operation>

        <wsdl:operation name="operLogout">
            <wsdl:documentation>odhlášení operátora

                požadované podmínky:
                přihlášeného operátor: ano
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / :   obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.</wsdl:documentation>
            <wsdl:input message="tns:operLogoutRequest"/>
            <wsdl:output message="tns:operLogoutResponse"/>
            <wsdl:fault name="fault" message="tns:operLogoutFault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyOperatorPassword">
            <wsdl:documentation>validace hesla přihlášeného operátora, přípustné návratové hodnoty jsou FAILED a SUCCEEDED

                požadované podmínky:
                přihlášeného operátor: ano
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / : obecná chyba
                GENERAL_ERROR / operatorCUID / : operátor není přihlášen
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_IS_MANDATORY / password / : položka je povinná
            </wsdl:documentation>
            <wsdl:input message="tns:verifyOperatorPasswordRequest"/>
            <wsdl:output message="tns:verifyOperatorPasswordResponse"/>
            <wsdl:fault name="fault" message="tns:verifyOperatorPasswordFault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyAuthentization">
            <wsdl:documentation>obecná metoda pro verifikaci autorizačních údajů přihlášeného uživatele, přípustné návratové hodnoty jsou FAILED a SUCCEEDED
                Pro actionType code = SIGN_REG nobe CONFIRMPASSWORD je možno využití i pro nepřihlášeného uživatele na LP

                požadované podmínky:
                přihlášeného uživatele: ano
                vyžadován businessContext:  ano

                možné chyby:
                kód / atribut / hodnota : popis
                GENERAL_ERROR / GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CLERR_WRONG_CONFIG / actionCode / : špatná konfigurace nebo nesprávný actionType kód
                ERROR_BAD_SIGNATURE / signature / : neplatná autorizace
                ERROR_NUMBER_ANSWER / / : počet vstupních odpovědí není správný
                ERROR_QCODE_EMPTY / questionXX / : kód X-té odpovědi není vyplněn
                ERROR_IS_MANDATORY / questionXX / : text X-té odpovědi je povinný
                ERROR_ENUM_ERROR / questionXX / : nenalezen kód odpovědi
                ERROR_IS_MANDATORY / mobileNumber / : pro actionTypeCode SIGN_REG nebo CONFIRMPASSWORD je vyžadováno na vstupu mobilní číslo
            </wsdl:documentation>
            <wsdl:input message="tns:verifyAuthentizationRequest"/>
            <wsdl:output message="tns:verifyAuthentizationResponse"/>
            <wsdl:fault name="fault" message="tns:verifyAuthentizationFault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyActionAllowance">
            <wsdl:documentation>Verify action allowance by action code. Now is implemented for CHANGEPRIMPHONE and SET_SEC_ELEMENT_1 action only.

                required conditions:
                logged user: yes
                required businessContext:  yes

                Generated faults:
                kód / atribut / hodnota : popis
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / :   General system error
                CLERR_TIMEOUT / GENERAL_ERROR / : System was not able to process the request in time
                NOT_IMPLEMENTED / actionTypeCode / : Action code is not supported</wsdl:documentation>
            <wsdl:input message="tns:verifyActionAllowanceRequest"/>
            <wsdl:output message="tns:verifyActionAllowanceResponse"/>
            <wsdl:fault name="fault" message="tns:verifyActionAllowanceFault"/>
        </wsdl:operation>

        <wsdl:operation name="verifyICCAuthentication">
            <wsdl:documentation>Verify authorization of operator for the client

                required conditions:
                logged user: no
                required businessContext:  no

                Generated faults:
                kód / atribut / hodnota : popis
                code / attribute / value : description
                GENERAL_ERROR / GENERAL_ERROR / :   General system error
                CLERR_TIMEOUT / GENERAL_ERROR / : System was not able to process the request in time</wsdl:documentation>
            <wsdl:input message="tns:verifyICCAuthenticationRequest"/>
            <wsdl:output message="tns:verifyICCAuthenticationResponse"/>
            <wsdl:fault name="fault" message="tns:verifyICCAuthenticationFault"/>
        </wsdl:operation>

        <wsdl:operation name="blockChannel">
            <wsdl:documentation>Block channel for specified customer.

                required conditions:
                logged user: no
                required businessContext:  no

                Generated faults:
                kód / atribut / hodnota : popis
                code / attribute / value : description
                CLERR_NO_DATA_FOUND / cuid / : person with specific cuid not found
                GENERAL_ERROR / GENERAL_ERROR / :   General system error
                CLERR_TIMEOUT / GENERAL_ERROR / : System was not able to process the request in time</wsdl:documentation>
            <wsdl:input message="tns:blockChannelRequest"/>
            <wsdl:output message="tns:blockChannelResponse"/>
            <wsdl:fault name="fault" message="tns:blockChannelFault"/>
        </wsdl:operation>

        <wsdl:operation name="logInToICC">
            <wsdl:documentation>Login client to ICC via external system.

                required conditions:
                logged user: no
                required businessContext:  no

                Generated faults:
                code / attribute / value : description
                CLERR_NO_DATA_FOUND / cuid / : Person with specific cuid not found
                CLERR_NO_DATA_FOUND / operatorId / : Operator with specific id not found
                GENERAL_ERROR / GENERAL_ERROR / :   General system error
                CLERR_TIMEOUT / GENERAL_ERROR / : System was not able to process the request in time</wsdl:documentation>
            <wsdl:input message="tns:logInToICCRequest"/>
            <wsdl:output message="tns:logInToICCResponse"/>
      <wsdl:fault name="fault" message="tns:logInToICCFault"/>
    </wsdl:operation>

    <wsdl:operation name="sendLoginNotification">
        <wsdl:documentation>Sends information about unusual login to customer</wsdl:documentation>
        <wsdl:input message="tns:sendLoginNotificationRequest"/>
        <wsdl:output message="tns:sendLoginNotificationResponse"/>
        <wsdl:fault name="fault" message="tns:sendLoginNotificationFault"/>
    </wsdl:operation>

    <wsdl:operation name="markSMSUnusable">
        <wsdl:documentation>Marks all SMSs with this operationDesc as USED</wsdl:documentation>
        <wsdl:input message="tns:markSMSUnusableRequest"/>
        <wsdl:output message="tns:markSMSUnusableResponse"/>
        <wsdl:fault name="fault" message="tns:markSMSUnusableFault"/>
    </wsdl:operation>
    <wsdl:operation name="getAccessStatus">
        <wsdl:documentation>Pro daného klienta vrátí jeho přístupy a indikaci přizamknutí vstupu do IB.</wsdl:documentation>
        <wsdl:input message="tns:getAccessStatusRequest" />
        <wsdl:output message="tns:getAccessStatusResponse" />
        <wsdl:fault name="fault" message="tns:getAccessStatusFault"/>
    </wsdl:operation>

    </wsdl:portType>
    <wsdl:binding name="obsAutentizationAutorizationWSSOAP"
        type="tns:obsAutentizationAutorizationWS">
        <soap:binding style="document"
            transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="getUserSecurityElement">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getLoggedUserSecurityElement">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="resetPassword">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="sendAuthSMS">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="logout">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAuthTypes">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="operLogin">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="operLogout">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyPassword">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyOperatorPassword">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyAuthentization">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyActionAllowance">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyICCAuthentication">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="blockChannel">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="logInToICC">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="sendLoginNotification">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="markSMSUnusable">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getAccessStatus">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="obsAutentizationAutorizationWS">
        <wsdl:port binding="tns:obsAutentizationAutorizationWSSOAP" name="obsAutentizationAutorizationWSSOAP">
            <soap:address
                location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsAutentizationAutorizationWS" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
