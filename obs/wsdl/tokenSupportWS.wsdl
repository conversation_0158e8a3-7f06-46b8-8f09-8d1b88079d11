<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://airbank.cz/obs/ws/tokenSupportWS" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="tokenSupportWS" targetNamespace="http://airbank.cz/obs/ws/tokenSupportWS" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">
	<wsdl:types>
		<xsd:schema targetNamespace="http://airbank.cz/obs/ws/tokenSupportWS">
			<xsd:include schemaLocation="tokenSupportWS.xsd" />
			<xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
		</xsd:schema>
	</wsdl:types>
   <wsdl:message name="chargeFeeRequest">
  	<wsdl:part name="parameters" element="tns:chargeFeeRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="chargeFeeResponse">
  	<wsdl:part name="parameters" element="tns:chargeFeeResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="chargeFeeFault">
  	<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="tokenSupportWS">
    <wsdl:operation name="chargeFee">
    	<wsdl:documentation>požadované podmínky:
přihlášeného uživatele: ne
vyžadován technicalContext:  ne

možné soapFault chyby:
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba           	
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
    	</wsdl:documentation>
    	<wsdl:input message="tns:chargeFeeRequest"></wsdl:input>
    	<wsdl:output message="tns:chargeFeeResponse"></wsdl:output>
        <wsdl:fault name="fault" message="tns:chargeFeeFault"></wsdl:fault>
      </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="tokenSupportWSSOAP"
  	type="tns:tokenSupportWS">
  	<soap:binding style="document"
  		transport="http://schemas.xmlsoap.org/soap/http" /> 	
  	<wsdl:operation name="chargeFee">
  		<soap:operation soapAction="" />
  		<wsdl:input>
  			<soap:body use="literal" />
  		</wsdl:input>
  		<wsdl:output>
  			<soap:body use="literal" />
  		</wsdl:output>
  		<wsdl:fault name="fault">
  			<soap:fault use="literal" name="fault" />
  		</wsdl:fault>
  	</wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="tokenSupportWS">
    <wsdl:port binding="tns:tokenSupportWSSOAP" name="tokenSupportWSSOAP">
      <soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/tokenSupportWS"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
