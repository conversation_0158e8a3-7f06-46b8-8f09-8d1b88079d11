<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsCategorizationWS/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="obsCategorizationWS" targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCategorizationWS/" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">
	<wsdl:types>
		<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCategorizationWS/" xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
			<xsd:include schemaLocation="OBSCategorizationWS.xsd" />
			<xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
		</xsd:schema>
	</wsdl:types>
  <wsdl:message name="NewOperationRequest">
    <wsdl:part element="tns:setCategoryRequest" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="NewOperationResponse">
    <wsdl:part element="tns:setCategoryResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="getUsedAccountsRequest1">
  	<wsdl:part name="parameters" element="tns:getUsedAccountsRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUsedAccountsResponse1">
  	<wsdl:part name="parameters" element="tns:getUsedAccountsResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUsedMerchantRequest1">
  	<wsdl:part name="parameters" element="tns:getUsedMerchantRequest"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUsedMerchantResponse1">
  	<wsdl:part name="parameters" element="tns:getUsedMerchantResponse"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="setCategoryFault">
  	<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUsedAccountsFault">
  	<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUsedMerchantFault">
  	<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="obsCategorizationWS">
    <wsdl:operation name="setCategory">
      <wsdl:documentation>Tato operace nastavuje požadovanou kategorii na trasakci. Systém ARBES OBS vyhledá transakce buď podle čísla kreditní účtu transakce nebo podle jména obchodníka u karetní transakce nebo vezme všechny u Sazka transakcí a nastaví požadovanou kategorii (u nastavení kategorie pro skupinu transakcí, mění kategorii pouze u transakcí, které nemají ještě žádnou kategorii nastavenou).
Pro identifikaci profilu se použije Common.SecurityContext.idProfile

požadované podmínky:
přihlášeného uživatele: ano
vyžadován securityContext:  ano

možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
ERR_SUBJECT_NOT_CATEGORIZABLE / categorizationSubjectIdentification / : subject neni kategorizovatelny (napr. splatka uveru)
</wsdl:documentation>
      <wsdl:input message="tns:NewOperationRequest"/>
      <wsdl:output message="tns:NewOperationResponse"/>
            <wsdl:fault name="fault" message="tns:setCategoryFault"></wsdl:fault>
        </wsdl:operation>
    <wsdl:operation name="getUsedAccounts">
    	<wsdl:documentation>OBS vrátí seznam atributů posledních transakcí jednorázových platebních příkazů pro daného klienta grupované podle čísla protiúčtu. Vybírají se transakce pro všechny kategorie.
Třídění : kategorie, datum poslední platby desc
Pro identifikaci profilu se použije Common.SecurityContext.idProfile



možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
</wsdl:documentation>
    	<wsdl:input message="tns:getUsedAccountsRequest1"></wsdl:input>
    	<wsdl:output message="tns:getUsedAccountsResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:getUsedAccountsFault"></wsdl:fault>
        </wsdl:operation>
    <wsdl:operation name="getUsedMerchant">
    	<wsdl:documentation>OBS vrátí seznam atributů posledních transakcí plateb kartou pro daného klienta grupované podle Merchant name. Vybírají se transakce pro všechny kategorie.
Třídění : kategorie, datum poslední platby desc
Pro identifikaci profilu se použije Common.SecurityContext.idProfile

požadované podmínky:
přihlášeného uživatele: ne
vyžadován securityContext:  ano

možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
</wsdl:documentation>
    	<wsdl:input message="tns:getUsedMerchantRequest1"></wsdl:input>
    	<wsdl:output message="tns:getUsedMerchantResponse1"></wsdl:output>
            <wsdl:fault name="fault" message="tns:getUsedMerchantFault"></wsdl:fault>
        </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="obsCategorizationWSSOAP"
  	type="tns:obsCategorizationWS">
  	<soap:binding style="document"
  		transport="http://schemas.xmlsoap.org/soap/http" />
  	<wsdl:operation name="setCategory">
  		<soap:operation
  			soapAction="" />
  		<wsdl:input>
  			<soap:body use="literal" />

  		</wsdl:input>
  		<wsdl:output>
  			<soap:body use="literal" />
  		</wsdl:output>
  	</wsdl:operation>
  	<wsdl:operation name="getUsedAccounts">
  		<soap:operation
  			soapAction="" />
  		<wsdl:input>
  			<soap:body use="literal" />

  		</wsdl:input>
  		<wsdl:output>
  			<soap:body use="literal" />
  		</wsdl:output>
  	</wsdl:operation>
  	<wsdl:operation name="getUsedMerchant">
  		<soap:operation
  			soapAction="" />
  		<wsdl:input>
  			<soap:body use="literal" />

  		</wsdl:input>
  		<wsdl:output>
  			<soap:body use="literal" />
  		</wsdl:output>
  	</wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="obsCategorizationWS">
    <wsdl:port binding="tns:obsCategorizationWSSOAP" name="obsCategorizationWSSOAP">
      <soap:address location="http:/TO-BE-CHANGED/ib/core/ppf/ws/obsCategorizationWS/"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
