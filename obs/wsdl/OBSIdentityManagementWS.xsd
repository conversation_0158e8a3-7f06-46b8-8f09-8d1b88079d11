<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://arbes.com/ib/core/ppf/ws/obsIdentityManagementWS/">

  <xsd:element name="getUserInfoRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cuid" type="xsd:long" maxOccurs="1"
          minOccurs="1" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="getUserInfoResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="userName" type="xsd:string" maxOccurs="1"
          minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>uživatelské jméno klienta</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="firstName" type="xsd:string" maxOccurs="1"
          minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>jméno klienta</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="surName" type="xsd:string" maxOccurs="1"
          minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>příjmení klienta</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>

</xsd:schema>
