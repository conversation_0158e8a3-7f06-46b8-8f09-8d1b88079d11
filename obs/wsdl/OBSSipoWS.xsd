<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsSipoWS/" xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/" xmlns:Q2="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"/>
    <xsd:import schemaLocation="../xsd/SipoTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
    <xsd:import schemaLocation="../xsd/AutentizationAuthorization.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>
    <xsd:element name="setSipoRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="validate" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>
                            true - OBS pouze validuje data
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="authorization" type="Q1:AuthType">
                </xsd:element>
                <xsd:element name="sipo" type="Q1:SipoTO"></xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="setSipoResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="idSipo" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>primární klíč nově založeného / editovaného sipa
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="deleteSipoRequest">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="idSipo" type="xsd:long"></xsd:element>
                <xsd:element name="authorization" type="Q1:AuthType"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="deleteSipoResponse">
        <xsd:complexType>
            <xsd:sequence>

            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getSipoRequest">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="idSipo" type="xsd:long" maxOccurs="unbounded"></xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getSipoResponse">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="sipo" type="Q1:SipoTO" maxOccurs="unbounded" minOccurs="0">
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="findSipoRequest">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="filter" type="Q2:SelectFilter">
                    <xsd:annotation>
                        <xsd:documentation>
                            možná filtrace:
                            IDBANKACCOUNT - LONG - id účtu
                            IDCATEGORY - LONG - id kategorie

                            Řazení podle: NAME, LAST_REALIZED_TRAN_DATE
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="findSipoResponse">
        <xsd:complexType>
            <xsd:sequence>

                <xsd:element name="idSipo" type="xsd:long" maxOccurs="unbounded" minOccurs="0"></xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="getSipoInstanceInfoRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="idSipo" type="xsd:long" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="getSipoInstanceInfoResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="instanceInfo" type="Q1:SipoInstanceInfoTO" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>