<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:acc="http://arbes.com/ib/core/ppf/ws/obsAccountWS/"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    targetNamespace="http://airbank.cz/obs/ws/SmeAccountWS/">

    <import namespace="http://arbes.com/ib/core/ppf/ws/obsAccountWS/" schemaLocation="../wsdl/OBSAccountWS.xsd"/>
    <import schemaLocation="../xsd/ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>

    <element name="createCurrentAccountRequest">
        <annotation>
            <documentation>
                Request to create a new SME Current account (Podnikatelský účet).
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="acc:createAccountRequest" />
            </complexContent>
        </complexType>
    </element>

    <element name="createCurrentAccountResponse">
        <annotation>
            <documentation>
                Response returning created SME Current account.
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="acc:createAccountResponse">
                    <sequence>
                        <element name="accountNumber" type="string">
                            <annotation>
                                <documentation>
                                    Account number
                                </documentation>
                            </annotation>
                        </element>
                    </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="createSavingAccountRequest">
        <annotation>
            <documentation>
                Request to create a new SME Saving account (Podnikatelský spořicí účet).
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="acc:createAccountRequest" />
            </complexContent>
        </complexType>
    </element>

    <element name="createSavingAccountResponse">
        <annotation>
            <documentation>
                Response returning created SME Saving account.
            </documentation>
        </annotation>
        <complexType>
            <complexContent>
                <extension base="acc:createAccountResponse">
                    <sequence>
                        <element name="accountNumber" type="string">
                            <annotation>
                                <documentation>
                                    Account number
                                </documentation>
                            </annotation>
                        </element>
                    </sequence>
                </extension>
            </complexContent>
        </complexType>
    </element>

    <element name="setAccountActiveRequest">
        <annotation>
            <documentation>
                Request to activate SME Current/Saving account.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="accountNumber" type="string">
                    <annotation>
                        <documentation>
                            Account number
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="setAccountActiveResponse">
        <annotation>
            <documentation>
                Response returning result of activation of SME Current/Saving account (NULL is OK, otherwise SOAP fault is returned).
            </documentation>
        </annotation>
        <complexType>
            <sequence />
        </complexType>
    </element>

    <element name="createTerminationAccountDocumentRequest">
        <annotation>
            <documentation>
                Request to create new document for termination of account
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="GCID" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="IDBankAccount" type="long">
                    <annotation>
                        <documentation>ID of bankaccount</documentation>
                    </annotation>
                </element>
                <element name="envelopeID" type="long">
                    <annotation>
                        <documentation>ID of envelope</documentation>
                    </annotation>
                </element>
                <element name="ApplicationID" type="long">
                    <annotation>
                        <documentation>ID of application</documentation>
                    </annotation>
                </element>
				<element name="attachmentId" type="string" maxOccurs="2">
                    <annotation>
                        <documentation>Contract document (UUID)</documentation>
                    </annotation>
                </element>
                <element name="barCode" type="string">
                    <annotation>
                        <documentation>BarCode (search code) included in PDF version of document</documentation>
                    </annotation>
                </element>
                <element name="appendixNumber" type="string">
                    <annotation>
                        <documentation>Appendix number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createTerminationAccountDocumentResponse">
        <annotation>
            <documentation>
                Response returning created SME Current account.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="completion" type="com:CreateContractDocumentCompletionResTO">
                    <annotation>
                        <documentation>Created completions</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="terminateAccountRequest">
        <complexType>
            <sequence>
                <element name="IDBankAccount" type="long">
                    <annotation>
                        <documentation>ID of bankaccount to terminate</documentation>
                    </annotation>
                </element>
                <element name="transferAccountNumber" type="string" minOccurs="0">
                    <annotation>
                        <documentation>number of account where we want to send balance</documentation>
                    </annotation>
                </element>
                <element name="transferBankCode" type="string" minOccurs="0">
                    <annotation>
                        <documentation>bank code of account where we want to send balance</documentation>
                    </annotation>
                </element>
                <element name="IDCompletion" type="long">
                    <annotation>
                        <documentation>ID of completion for account termination</documentation>
                    </annotation>
                </element>
                <element name="closeDate" type="dateTime">
                    <annotation>
                        <documentation>date of account termination</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="terminateAccountResponse">
        <complexType>
            <sequence>
                <element name="terminationResult" type="boolean">
                    <annotation>
                        <documentation>true if termination was successful, otherwise false</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
