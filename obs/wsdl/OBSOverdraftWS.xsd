<schema
        xmlns="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://airbank.cz/obs/ws/obsOverdraftWS"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        xmlns:od="http://airbank.cz/obs/ws/overdraft"
        xmlns:loan="http://airbank.cz/obs/ws/loan">

  <import namespace="http://airbank.cz/obs/ws/overdraft" schemaLocation="../xsd/Overdraft.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/BinDocument.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/ContractTO.xsd" />
  <import namespace="http://airbank.cz/obs/ws/loan" schemaLocation="../xsd/LoanTO.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Application.xsd"/>

  <element name="getOverdraftLimitParamsRequest">
    <complexType>
      <sequence/>
    </complexType>
  </element>

  <element name="getOverdraftLimitParamsResponse">
    <complexType>
      <sequence>
        <element name="params" type="od:OverdraftLimitParams" />
      </sequence>
    </complexType>
  </element>


  <element name="getOverdraftParamsRequest">
    <complexType>
      <sequence>
        <element name="amount" type="long" />
      </sequence>
    </complexType>
  </element>

  <element name="getOverdraftParamsResponse">
    <complexType>
      <sequence>
        <element name="params" type="od:OverdraftParams">
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getOverdraftPreContractDocumentRequest">
    <complexType>
      <sequence>
        <element name="overdraftAmount" type="decimal">
          <annotation>
            <documentation>výše KTK</documentation>
          </annotation>
        </element>
        <element name="idApplication" type="long">
          <annotation>
            <documentation>externí identifikátor žádosti = id žádosti z IB</documentation>
          </annotation>
        </element>
        <element name="accountNumber" type="string">
          <annotation>
            <documentation>číslo účtu, ke kterému KTK poskytujeme</documentation>
          </annotation>
        </element>
        <element name="interestRate" type="decimal">
          <annotation>
            <documentation>Úrok</documentation>
          </annotation>
        </element>
        <element name="apr" type="decimal">
          <annotation>
            <documentation>RPSN</documentation>
          </annotation>
        </element>
        <element name="genDate" type="date">
          <annotation>
            <documentation>datum podání žádosti o KTK</documentation>
          </annotation>
        </element>
        <element name="promoFlag" type="boolean" minOccurs="1" maxOccurs="1">
          <annotation>
            <documentation>True if promo is used for this application.</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getOverdraftPreContractDocumentResponse">
    <complexType>
      <sequence>
        <element name="idHtmlBinDocument" type="com:BinDocumentIdentType">
          <annotation>
            <documentation>id binárního dokumentu pro html verzi</documentation>
          </annotation>
        </element>
        <element name="idPdfBinDocument" type="com:BinDocumentIdentType">
          <annotation>
            <documentation>id binárního dokumentu pro pdf verzi (obsah se generuje asynchroně)</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getOverdraftPreContractDocumentFault">
    <complexType>
      <sequence>
        <element name="getOverdraftPreContractDocumentFault" type="string"/>
      </sequence>
    </complexType>
  </element>

  <element name="createOverdraftRequest">
    <complexType>
      <sequence>
        <element name="cuid" type="long" />
        <element name="overdraftAmount" type="long" />
        <element name="idBankAccount" type="long" />
        <element name="riskGrade" type="string" />
        <element name="channelSaleLead" type="string" minOccurs="0" maxOccurs="1">
          <annotation>
            <documentation>channel of request for RS</documentation>
          </annotation>
        </element>
        <element name="idAgency" type="string" minOccurs="0" maxOccurs="1">
          <annotation>
            <documentation>branch or ecc identifier in obs</documentation>
          </annotation>
        </element>
        <element name="promoCode" type="string" minOccurs="0" maxOccurs="1">
          <annotation>
            <documentation>Promo code used for this overdraft.</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="createOverdraftResponse">
    <complexType>
      <sequence>
        <element name="overdraftNumber" type="string" />
        <element name="idOverdraft" type="long" />
      </sequence>
    </complexType>
  </element>

  <element name="findOverdraftRequest">
    <complexType>
      <sequence>
        <element type="long" name="idProfile" minOccurs="0" maxOccurs="1">
          <annotation>
            <documentation>id profilu - id vazby mezi osobou a rámcovou službou</documentation>
          </annotation>
        </element>
        <element type="long" name="cuid" minOccurs="0" maxOccurs="1">
          <annotation>
            <documentation>CUID klienta</documentation>
          </annotation>
        </element>
        <element name="status" type="loan:LoanStatusType" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="findOverdraftResponse">
    <complexType>
      <sequence>
        <element name="idOverdraft" type="long" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="getOverdraftDetailRequest">
    <complexType>
      <sequence>
        <element name="idOverdraft" type="long" minOccurs="0" maxOccurs="unbounded">
          <annotation>
            <documentation>ID kontokorentu</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getOverdraftDetailResponse">
    <complexType>
      <sequence>
        <element name="overdraftDetailTO" type="od:OverdraftDetailTO" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="cancelOverdraftRequest">
    <complexType>
      <sequence>
        <element name="idOverdraft" type="long" />
      </sequence>
    </complexType>
  </element>

  <element name="cancelOverdraftResponse">
    <complexType>
      <sequence>
        <element name="cancelled" type="boolean" />
      </sequence>
    </complexType>
  </element>

  <element name="terminateOverdraftRequest">
    <complexType>
      <sequence>
        <element name="idOverdraft" type="long" />
        <element name="terminateReason" type="string" />
      </sequence>
    </complexType>
  </element>

  <element name="terminateOverdraftResponse">
    <complexType>
      <sequence>
        <element name="accepted" type="boolean" />
        <element name="rejectReason" type="com:ReasonRejectionType" minOccurs="0" >
          <annotation>
            <documentation>Duvod zamitnuti ukonceni KTK</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getAllOverdraftDetailsRequest">
    <complexType>
      <sequence>
        <element name="cuid" type="long"/>
        <element name="actualGcOnly" type="boolean" minOccurs="0" default="true">
           <annotation>
             <documentation>ActualGCOnly=true - produkty pouze pro aktivní GC; ActualGCOnly=false - produkty jak pro aktivní GC, tak pro neaktivní GC</documentation>
           </annotation>
        </element>
        <element name="activeLoansOnly" type="boolean" minOccurs="0" default="false"/>
      </sequence>
    </complexType>
  </element>
  <element name="getAllOverdraftDetailsResponse">
    <complexType>
      <sequence>
        <element name="overdraft" type="od:OverdraftExtendedDetailTO" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>
  <element name="calculateOverdraftInterestRequest">
    <complexType>
      <sequence>
        <element name="amount" type="decimal"/>
        <element name="timeInterval" type="od:TimeIntervalType"/>
      </sequence>
    </complexType>
  </element>
  <element name="calculateOverdraftInterestResponse">
    <complexType>
      <sequence>
        <element name="calculatedAmount" type="decimal"/>
        <element name="interestFreeReserve" type="boolean"/>
        <element name="interestFreeReserveAmount" type="decimal" minOccurs="0"/>
      </sequence>
    </complexType>
  </element>

  <element name="getSuspensionHistoryRequest">
    <complexType>
      <sequence>
        <element name="idOverdraft" type="long"/>
      </sequence>
    </complexType>
  </element>
  <element name="getSuspensionHistoryResponse">
    <complexType>
      <sequence>
        <element name="suspensions" type="od:SuspensionDetail" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="suspendOverdraftRequest">
    <complexType>
        <sequence>
            <choice>
                <element name="idOverdraft" type="long"/>
                <element name="overdraftNumber" type="string"/>
            </choice>
            <element name="reasonCode" type="string" minOccurs="0">
                <annotation>
                    <documentation>
                        MDM - OVERDRAFT_UTILIZATION_SUSPENSION_REASON
                    </documentation>
                </annotation>
            </element>
        </sequence>

    </complexType>
  </element>
  <element name="suspendOverdraftResponse">
    <complexType/>
  </element>

</schema>
