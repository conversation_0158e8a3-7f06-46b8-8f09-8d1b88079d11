<schema
        xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:c="http://airbank.cz/obs/ws/contract"
        targetNamespace="http://airbank.cz/obs/ws/obsPersonalItemsProtectionWS">

  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/BinDocument.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/ContractTO.xsd" />
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Application.xsd"/>
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/TransactionTO.xsd"/>
  <import namespace="http://airbank.cz/obs/ws/contract" schemaLocation="../xsd/contract.xsd" />

  <element name="createContractDocumentRequest">
    <complexType>
      <sequence>
        <element name="cuid" type="long" />
        <element name="generalContractId" type="long" />
        <element name="envelopeId" type="long" />
        <element name="applicationId" type="long" />
        <element name="documentUuid" type="string" maxOccurs="2" />
        <element name="barCode" type="string" />
      </sequence>
    </complexType>
  </element>

  <element name="createContractDocumentResponse">
    <complexType>
      <sequence>
        <element name="idCompletion" type="long" />
      </sequence>
    </complexType>
  </element>

  <element name="createTerminationContractDocumentRequest">
    <complexType>
      <sequence>
        <element name="cuid" type="long" />
        <element name="generalContractId" type="long" />
        <element name="envelopeId" type="long" minOccurs="0"/>
        <element name="applicationId" type="long" minOccurs="0"/>
        <element name="signDetail" type="c:SignDetail" />
      </sequence>
    </complexType>
  </element>

  <element name="createTerminationContractDocumentResponse">
    <complexType>
      <sequence>
        <element name="idCompletion" type="long" />
      </sequence>
    </complexType>
  </element>
</schema>
