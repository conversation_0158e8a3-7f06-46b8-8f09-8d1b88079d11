<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://airbank.cz/obs/ws/termDepositContractWS"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    targetNamespace="http://airbank.cz/obs/ws/termDepositContractWS"
>
    <types>
        <xsd:schema>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
            <xsd:import namespace="http://airbank.cz/obs/ws/termDepositContractWS" schemaLocation="termDepositContractWS.xsd"/>
        </xsd:schema>
    </types>

    <message name="fault">
        <part name="fault" element="com:ErrorsListType"/>
    </message>

    <message name="checkContractEligibilityRequest">
        <part name="request" element="tns:checkContractEligibilityRequest"/>
    </message>

    <message name="checkContractEligibilityResponse">
        <part name="response" element="tns:checkContractEligibilityResponse"/>
    </message>

    <message name="createFixedDepositContractDocumentRequest">
        <part name="request" element="tns:createFixedDepositContractDocumentRequest"/>
    </message>

    <message name="createFixedDepositContractDocumentResponse">
        <part name="response" element="tns:createFixedDepositContractDocumentResponse"/>
    </message>

    <message name="createFixedDepositContractDocumentForRolloverUpdateRequest">
        <part name="request" element="tns:createFixedDepositContractDocumentForRolloverUpdateRequest"/>
    </message>

    <message name="createFixedDepositContractDocumentForRolloverUpdateResponse">
        <part name="response" element="tns:createFixedDepositContractDocumentForRolloverUpdateResponse"/>
    </message>

    <message name="createFixedDepositTerminateContractDocumentRequest">
        <part name="request" element="tns:createFixedDepositTerminateContractDocumentRequest"/>
    </message>

    <message name="createFixedDepositTerminateContractDocumentResponse">
        <part name="response" element="tns:createFixedDepositTerminateContractDocumentResponse"/>
    </message>

    <message name="createInvestmentCertificateConfirmationContractDocumentRequest">
        <part name="request" element="tns:createInvestmentCertificateConfirmationContractDocumentRequest"/>
    </message>

    <message name="createInvestmentCertificateConfirmationContractDocumentResponse">
        <part name="response" element="tns:createInvestmentCertificateConfirmationContractDocumentResponse"/>
    </message>

    <message name="createInvestmentCertificateContractDocumentRequest">
        <part name="request" element="tns:createInvestmentCertificateContractDocumentRequest"/>
    </message>

    <message name="createInvestmentCertificateContractDocumentResponse">
        <part name="response" element="tns:createInvestmentCertificateContractDocumentResponse"/>
    </message>

    <message name="createInvestmentToAirBankContractDocumentRequest">
        <part name="request" element="tns:createInvestmentToAirBankContractDocumentRequest"/>
    </message>

    <message name="createInvestmentToAirBankContractDocumentResponse">
        <part name="response" element="tns:createInvestmentToAirBankContractDocumentResponse"/>
    </message>

    <message name="saveInvestmentCertificateRequest">
        <part name="request" element="tns:saveInvestmentCertificateRequest"/>
    </message>

    <message name="saveInvestmentCertificateResponse">
        <part name="response" element="tns:saveInvestmentCertificateResponse"/>
    </message>

    <portType name="termDepositContractPortType">
        <operation name="checkContractEligibility">
            <input name="checkContractEligibilityRequest" message="tns:checkContractEligibilityRequest"/>
            <output name="checkContractEligibilityResponse" message="tns:checkContractEligibilityResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="createInvestmentCertificateConfirmationContractDocument">
            <input name="createInvestmentCertificateConfirmationContractDocumentRequest" message="tns:createInvestmentCertificateConfirmationContractDocumentRequest"/>
            <output name="createInvestmentCertificateConfirmationContractDocumentResponse" message="tns:createInvestmentCertificateConfirmationContractDocumentResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="createInvestmentCertificateContractDocument">
            <input name="createInvestmentCertificateContractDocumentRequest" message="tns:createInvestmentCertificateContractDocumentRequest"/>
            <output name="createInvestmentCertificateContractDocumentResponse" message="tns:createInvestmentCertificateContractDocumentResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="createInvestmentToAirBankContractDocument">
            <input name="createInvestmentToAirBankContractDocumentRequest" message="tns:createInvestmentToAirBankContractDocumentRequest"/>
            <output name="createInvestmentToAirBankContractDocumentResponse" message="tns:createInvestmentToAirBankContractDocumentResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="createFixedDepositContractDocument">
            <input name="createFixedDepositContractDocumentRequest" message="tns:createFixedDepositContractDocumentRequest"/>
            <output name="createFixedDepositContractDocumentResponse" message="tns:createFixedDepositContractDocumentResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="createFixedDepositContractDocumentForRolloverUpdate">
            <input name="createFixedDepositContractDocumentForRolloverUpdateRequest" message="tns:createFixedDepositContractDocumentForRolloverUpdateRequest"/>
            <output name="createFixedDepositContractDocumentForRolloverUpdateResponse" message="tns:createFixedDepositContractDocumentForRolloverUpdateResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="createFixedDepositTerminateContractDocument">
            <input name="createFixedDepositTerminateContractDocumentRequest" message="tns:createFixedDepositTerminateContractDocumentRequest"/>
            <output name="createFixedDepositTerminateContractDocumentResponse" message="tns:createFixedDepositTerminateContractDocumentResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>

        <operation name="saveInvestmentCertificate">
            <input name="saveInvestmentCertificateRequest" message="tns:saveInvestmentCertificateRequest"/>
            <output name="saveInvestmentCertificateResponse" message="tns:saveInvestmentCertificateResponse"/>
            <fault name="fault" message="tns:fault"/>
        </operation>
    </portType>

    <binding name="termDepositContractBinding" type="tns:termDepositContractPortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <operation name="checkContractEligibility">
            <soap:operation soapAction=""/>
            <input name="checkContractEligibilityRequest">
                <soap:body use="literal"/>
            </input>
            <output name="checkContractEligibilityResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="createFixedDepositContractDocument">
            <soap:operation soapAction=""/>
            <input name="createFixedDepositContractDocumentRequest">
                <soap:body use="literal"/>
            </input>
            <output name="createFixedDepositContractDocumentResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="createFixedDepositContractDocumentForRolloverUpdate">
            <soap:operation soapAction=""/>
            <input name="createFixedDepositContractDocumentForRolloverUpdateRequest">
                <soap:body use="literal"/>
            </input>
            <output name="createFixedDepositContractDocumentForRolloverUpdateResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="createFixedDepositTerminateContractDocument">
            <soap:operation soapAction=""/>
            <input name="createFixedDepositTerminateContractDocumentRequest">
                <soap:body use="literal"/>
            </input>
            <output name="createFixedDepositTerminateContractDocumentResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="createInvestmentCertificateConfirmationContractDocument">
            <soap:operation soapAction=""/>
            <input name="createInvestmentCertificateConfirmationContractDocumentRequest">
                <soap:body use="literal"/>
            </input>
            <output name="createInvestmentCertificateConfirmationContractDocumentResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="createInvestmentCertificateContractDocument">
            <soap:operation soapAction=""/>
            <input name="createInvestmentCertificateContractDocumentRequest">
                <soap:body use="literal"/>
            </input>
            <output name="createInvestmentCertificateContractDocumentResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="createInvestmentToAirBankContractDocument">
            <soap:operation soapAction=""/>
            <input name="createInvestmentToAirBankContractDocumentRequest">
                <soap:body use="literal"/>
            </input>
            <output name="createInvestmentToAirBankContractDocumentResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

        <operation name="saveInvestmentCertificate">
            <soap:operation soapAction=""/>
            <input name="saveInvestmentCertificateRequest">
                <soap:body use="literal"/>
            </input>
            <output name="saveInvestmentCertificateResponse">
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </fault>
        </operation>

    </binding>

    <service name="termDepositContractService">
        <port name="termDepositContractPort" binding="tns:termDepositContractBinding">
            <soap:address location="/ws/termdepositcontractws"/>
        </port>
    </service>
</definitions>