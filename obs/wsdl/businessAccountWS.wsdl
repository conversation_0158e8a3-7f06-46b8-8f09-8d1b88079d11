<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions
        xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
        xmlns:tns="http://airbank.cz/obs/ws/businessAccountWS"
        targetNamespace="http://airbank.cz/obs/ws/businessAccountWS">

    <wsdl:types>
        <xsd:schema>
            <xsd:import namespace="http://airbank.cz/obs/ws/businessAccountWS" schemaLocation="businessAccountWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="faultMessage">
        <wsdl:part name="faultMessage" element="com:ErrorsListType" />
    </wsdl:message>

    <wsdl:message name="setBusinessUsageRequest">
        <wsdl:part name="setBusinessUsageRequest" element="tns:setBusinessUsageRequest" />
    </wsdl:message>
    <wsdl:message name="setBusinessUsageResponse">
        <wsdl:part name="setBusinessUsageResponse" element="tns:setBusinessUsageResponse" />
    </wsdl:message>

    <wsdl:message name="clarificationRequestSentRequest">
        <wsdl:part name="clarificationRequestSentRequest" element="tns:clarificationRequestSentRequest" />
    </wsdl:message>
    <wsdl:message name="clarificationRequestSentResponse">
        <wsdl:part name="clarificationRequestSentResponse" element="tns:clarificationRequestSentResponse" />
    </wsdl:message>

    <wsdl:portType name="businessAccount">

        <wsdl:operation name="setBusinessUsage">
            <wsdl:documentation>
                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.</wsdl:documentation>
            <wsdl:input message="tns:setBusinessUsageRequest" />
            <wsdl:output message="tns:setBusinessUsageResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>

        <wsdl:operation name="clarificationRequestSent">
            <wsdl:documentation>
                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.</wsdl:documentation>
            <wsdl:input message="tns:clarificationRequestSentRequest" />
            <wsdl:output message="tns:clarificationRequestSentResponse" />
            <wsdl:fault message="tns:faultMessage" name="fault" />
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="businessAccountSOAP" type="tns:businessAccount">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <wsdl:operation name="setBusinessUsage">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="clarificationRequestSent">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault" />
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="businessAccountWS">
        <wsdl:port binding="tns:businessAccountSOAP" name="businessAccountSOAP">
            <soap:address location="/ws/businessAccountWS" />
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
