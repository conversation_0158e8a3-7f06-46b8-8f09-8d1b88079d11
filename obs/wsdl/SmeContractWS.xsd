<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:c="http://airbank.cz/obs/ws/contract"
    xmlns:tns="http://airbank.cz/obs/ws/SmeContractWS/"
    targetNamespace="http://airbank.cz/obs/ws/SmeContractWS/">

    <import schemaLocation="../xsd/contract.xsd" namespace="http://airbank.cz/obs/ws/contract" />
    <import schemaLocation="../xsd/ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/" />

    <complexType name="CreateContractDocumentApplication">
        <sequence>
            <element name="applicationType">
                <annotation>
                    <documentation>Application type</documentation>
                </annotation>
                <simpleType>
                    <restriction base="string">
                        <enumeration value="SME_CONTRACT" />
                        <enumeration value="SME_ACCOUNT" />
                        <enumeration value="SME_CARD" />
                        <enumeration value="SME_CONTRACT_PARTICIPANT" />
                    </restriction>
                </simpleType>
            </element>
            <element name="applicationId" type="long">
                <annotation>
                    <documentation>Application ID</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long">
                <annotation>
                    <documentation>CUID of client</documentation>
                </annotation>
            </element>
            <element name="productId" type="string" minOccurs="0">
                <annotation>
                    <documentation>Product ID</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <element name="createContractDocumentRequest">
        <annotation>
            <documentation>
                Request to create Product completion and Contract document for specified Application type.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="gcid" type="long">
                    <annotation>
                        <documentation>GCID of SME General contract</documentation>
                    </annotation>
                </element>
                <element name="envelopeId" type="long">
                    <annotation>
                        <documentation>Envelope ID</documentation>
                    </annotation>
                </element>
                <element name="appendixNumber" type="long" minOccurs="0">
                    <annotation>
                        <documentation>Appendix number</documentation>
                    </annotation>
                </element>
                <element name="attachmentId" type="string" maxOccurs="2">
                    <annotation>
                        <documentation>Contract document (UUID)</documentation>
                    </annotation>
                </element>
                <element name="barCode" type="string">
                    <annotation>
                        <documentation>BarCode (search code) included in PDF version of document</documentation>
                    </annotation>
                </element>
                <element name="application" type="tns:CreateContractDocumentApplication" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Collection of applications to create</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createContractDocumentResponse">
        <annotation>
            <documentation>
                Response returning collection of created completions.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="completion" type="com:CreateContractDocumentCompletionResTO" maxOccurs="unbounded">
                    <annotation>
                        <documentation>Created completion</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>
