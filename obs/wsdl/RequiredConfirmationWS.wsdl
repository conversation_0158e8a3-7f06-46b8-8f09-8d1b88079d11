<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions name="RequiredConfirmationWS"
    targetNamespace="http://airbank.cz/obs/ws/RequiredConfirmationWS"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://airbank.cz/obs/ws/RequiredConfirmationWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <documentation>
        Web Service for generating and sending confirmations
    </documentation>

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/RequiredConfirmationWS" elementFormDefault="qualified">
            <xsd:include schemaLocation="RequiredConfirmationWS.xsd" />
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="SendIncomingPaymentConfirmationRequest">
        <part name="SendIncomingPaymentConfirmationRequest" element="tns:SendIncomingPaymentConfirmationRequest" />
    </message>
    <message name="SendIncomingPaymentConfirmationResponse">
        <part name="SendIncomingPaymentConfirmationResponse" element="tns:SendIncomingPaymentConfirmationResponse"/>
    </message>
    <message name="SendCardPaymentConfirmationRequest">
        <part name="SendCardPaymentConfirmationRequest" element="tns:SendCardPaymentConfirmationRequest"/>
    </message>
    <message name="SendCardPaymentConfirmationResponse">
        <part name="SendCardPaymentConfirmationResponse" element="tns:SendCardPaymentConfirmationResponse"/>
    </message>
    <message name="SendAccountBalanceConfirmationRequest">
        <part name="SendAccountBalanceConfirmationRequest" element="tns:SendAccountBalanceConfirmationRequest"/>
    </message>
    <message name="SendAccountBalanceConfirmationResponse">
        <part name="SendAccountBalanceConfirmationResponse" element="tns:SendAccountBalanceConfirmationResponse"/>
    </message>
    <message name="SendAccountOwnershipConfirmationRequest">
        <part name="SendAccountOwnershipConfirmationRequest" element="tns:SendAccountOwnershipConfirmationRequest"/>
    </message>
    <message name="SendAccountOwnershipConfirmationResponse">
        <part name="SendAccountOwnershipConfirmationResponse" element="tns:SendAccountOwnershipConfirmationResponse"/>
    </message>

    <portType name="RequiredConfirmationWS">
        <operation name="SendIncomingPaymentConfirmation">
            <documentation>
                Potvrzeni o prichozi uhrade
            </documentation>
            <input message="tns:SendIncomingPaymentConfirmationRequest" />
            <output message="tns:SendIncomingPaymentConfirmationResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="SendCardPaymentConfirmation">
            <documentation>
                Potvrzeni o platbe debetni kartou
            </documentation>
            <input message="tns:SendCardPaymentConfirmationRequest" />
            <output message="tns:SendCardPaymentConfirmationResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="SendAccountBalanceConfirmation">
            <documentation>
                Potvrzeni o zustatku na uctu
            </documentation>
            <input message="tns:SendAccountBalanceConfirmationRequest" />
            <output message="tns:SendAccountBalanceConfirmationResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
        <operation name="SendAccountOwnershipConfirmation">
            <documentation>
                Potvrzeni o vlastnictvi uctu
            </documentation>
            <input message="tns:SendAccountOwnershipConfirmationRequest" />
            <output message="tns:SendAccountOwnershipConfirmationResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>
    </portType>

    <binding name="RequiredConfirmationSOAP" type="tns:RequiredConfirmationWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="SendIncomingPaymentConfirmation">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="SendCardPaymentConfirmation">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="SendAccountBalanceConfirmation">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="SendAccountOwnershipConfirmation">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="RequiredConfirmationWS">
        <port binding="tns:RequiredConfirmationSOAP" name="RequiredConfirmationWSSOAP">
            <soap:address location="/ws/RequiredConfirmationWS" />
        </port>
    </service>
</definitions>
