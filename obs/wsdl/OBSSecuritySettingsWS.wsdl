<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsSecuritySettingsWS/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="obsSecuritySettingsWS"
	targetNamespace="http://arbes.com/ib/core/ppf/ws/obsSecuritySettingsWS/" xmlns:xsd1="http://arbes.com/ib/core/ppf/ws/common/">
	<wsdl:types> 
		<xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsSecuritySettingsWS/" 
			xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
			<xsd:include schemaLocation="OBSSecuritySettingsWS.xsd" />
			<xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="setPasswordRequest">
		<wsdl:part name="parameters" element="tns:setPasswordRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setPasswordResponse">
		<wsdl:part name="parameters" element="tns:setPasswordResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setUsernameRequest">
		<wsdl:part name="parameters" element="tns:setUsernameRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setUsernameResponse">
		<wsdl:part name="parameters" element="tns:setUsernameResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setPasswordFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setUsernameFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>

	<wsdl:message name="resetPasswordFromBranchRequest">
		<wsdl:part name="parameters" element="tns:resetPasswordFromBranchRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="resetPasswordFromBranchResponse">
		<wsdl:part name="parameters" element="tns:resetPasswordFromBranchResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="resetPasswordFromBranchFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setLoginBySMSRequest">
		<wsdl:part name="parameters" element="tns:setLoginBySMSRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setLoginBySMSResponse">
		<wsdl:part name="parameters" element="tns:setLoginBySMSResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="setLoginBySMSFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getLoginBySMSRequest">
		<wsdl:part name="parameters" element="tns:getLoginBySMSRequest"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getLoginBySMSResponse">
		<wsdl:part name="parameters" element="tns:getLoginBySMSResponse"></wsdl:part>
	</wsdl:message>
	<wsdl:message name="getLoginBySMSFault">
		<wsdl:part name="parameters" element="xsd1:ErrorsListType"></wsdl:part>
	</wsdl:message>
	<wsdl:portType name="obsSecuritySettingsWS">
		<wsdl:operation name="setPassword">
			<wsdl:documentation>metoda nastaví nové statické heslo uživatele

požadované podmínky:
přihlášeného uživatele: ano
vyžadován securityContext:  ano
		
možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NO_DATA_FOUND / cuid / : nenalezem uživatel
CLERR_PASSWORD_USED / newPassword / : heslo bylo v minulosti použito
ERROR_BAD_SIGNATURE / signature / : neplatná autorizace	
</wsdl:documentation>
			<wsdl:input message="tns:setPasswordRequest"></wsdl:input>
			<wsdl:output message="tns:setPasswordResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:setPasswordFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="setUsername">
			<wsdl:documentation>metoda pro změnu přihlašovacího jména

požadované podmínky:
přihlášeného uživatele: ano
vyžadován securityContext:  ano

možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NO_DATA_FOUND / cuid / : nenalezem uživatel
PPFCLERR_USERNAME_USED / newUsername / :uživatelské jméno bylo už použito
</wsdl:documentation>
			<wsdl:input message="tns:setUsernameRequest"></wsdl:input>
			<wsdl:output message="tns:setUsernameResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:setUsernameFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="resetPasswordFromBranch">
			<wsdl:documentation>metoda nastaví heslo, které se vygeneruje systémem a pošle se na mobilní telefon klienta. 
			Metodu lze použí pouze, když je přihlášen pobočník na pobočce.
			
požadované podmínky:
přihlášeného uživatele: ano
vyžadován securityContext:  ano
			
možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
CLERR_NO_DATA_FOUND / userName / : nenalezem uživatel
CLERR_CONTRACT_IS_NOT_ACTIVE / userName / : vlastník nebo disponent nema zkompletovanou smlouvu.
CLERR_ACCESS / GENERAL_ERROR  / : uživatel nemá právo na danou operaci
			</wsdl:documentation>
			<wsdl:input message="tns:resetPasswordFromBranchRequest"></wsdl:input>
			<wsdl:output message="tns:resetPasswordFromBranchResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:resetPasswordFromBranchFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="setLoginBySMS">
			<wsdl:documentation>Nastaveni vynuceni sms pri login
        	
možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba       </wsdl:documentation>
			<wsdl:input message="tns:setLoginBySMSRequest"></wsdl:input>
			<wsdl:output message="tns:setLoginBySMSResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:setLoginBySMSFault"></wsdl:fault>
        </wsdl:operation>
		<wsdl:operation name="getLoginBySMS">
			<wsdl:documentation>Overeni uzivatelskeho vynuceni sms pri login
        	
možné chyby: 
kód / atribut / hodnota : popis
GENERAL_ERROR /GENERAL_ERROR / : obecná chyba       </wsdl:documentation>
			<wsdl:input message="tns:getLoginBySMSRequest"></wsdl:input>
			<wsdl:output message="tns:getLoginBySMSResponse"></wsdl:output>
            <wsdl:fault name="fault" message="tns:getLoginBySMSFault"></wsdl:fault>
        </wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="obsSecuritySettingsWSSOAP"
		type="tns:obsSecuritySettingsWS">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="setPassword">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="setUsername">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="resetPasswordFromBranch">
			<soap:operation
				soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="setLoginBySMS">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getLoginBySMS">
			<soap:operation soapAction="" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="obsSecuritySettingsWS">
		<wsdl:port binding="tns:obsSecuritySettingsWSSOAP" name="obsSecuritySettingsWSSOAP">
			<soap:address location="http://TO-BE-CHANGED/ib/core/ppf/ws/obsSecuritySettingsWS" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
