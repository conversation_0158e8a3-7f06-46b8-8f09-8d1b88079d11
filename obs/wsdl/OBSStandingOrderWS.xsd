<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsStandingOrderWS/"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsStandingOrderWS/"
    xmlns:aa="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tt="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:f="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" >
  <import namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" schemaLocation="../xsd/Filter.xsd"/>
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/AutentizationAuthorization.xsd"/>
  <import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/TransactionTO.xsd"/>

  <element name="findStandingOrderRequest">
    <annotation>
      <documentation/>
    </annotation>
    <complexType>
      <sequence>
        <element name="filter" type="f:SelectFilter">
          <annotation>
            <documentation>
              možnosti filtru:

              IDBANKACCOUNT - LONG - nepovinný atribut
              IDTARGETENVELOPE - LONG - nepovinny atribut, v pripade zadani se vraci pouze trvale prikazy ktere maji tuto obalku nastavenou jako cilovou
              SAVINGS - STRING - hodnoty Y nebo N - nepovinný atribut - pokud je Y, potom se vrací pouze předpisy trvalých plateb,
              které jsou typu pravidelné spoření (creditní účet je typu spořící účet a potří ke stejné rámcové smlouvě jako debitní účet)
              IDCATEGORY - LONG - id kategorie, nepovinné
              IDCONTRABANKACCOUNT - LONG (nepovinný atribut) - id protiuctu
              STATUS - STRING - hodnoty ACTIVE nebo SUSPENDED - nepovinný atribut - umožňuje omezit sadu vracených trvalých příkazů,
              pokud je ACTIVE, vrací se pouze aktivní trvalé příkazy, pokud je SUSPENDED, vrací se pouze pozastavené trvalé příkazy,
              při vynechání atributu se vracejí ACTIVE i SUSPENDED společně

              vazba mezi atributy je AND

              Řazení podle: NAME, LAST_REALIZED_TRAN_DATE, NEXT_REQUIRED_TRAN_DATE
            </documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="findStandingOrderResponse">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long" maxOccurs="unbounded" minOccurs="0"/>
      </sequence>
    </complexType>
  </element>

  <simpleType name="statusEnum">
    <restriction base="string">
      <enumeration value="ACTIVE">
        <annotation>
          <documentation>pro vyhledání pouze aktivních trvalých příkazů</documentation>
        </annotation>
      </enumeration>
      <enumeration value="SUSPENDED">
        <annotation>
          <documentation>pro vyhledání pouze pozastavených trvalých příkazů</documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>

  <simpleType name="orderByAttributeEnum">
    <restriction base="string">
      <enumeration value="NAME">
        <annotation>
          <documentation>název trvalého příkazu</documentation>
        </annotation>
      </enumeration>
      <enumeration value="ACCOUNTNAME">
        <annotation>
          <documentation>název účtu na kterém je nastaven TP</documentation>
        </annotation>
      </enumeration>
      <enumeration value="LAST_REALIZED_TRAN_DATE">
        <annotation>
          <documentation>datum poslední realizované transakce z TP</documentation>
        </annotation>
      </enumeration>
      <enumeration value="NEXT_REQUIRED_TRAN_DATE">
        <annotation>
          <documentation>datum následující předepsané transakce z TP</documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>

  <simpleType name="orderByDirectionEnum">
    <restriction base="string">
      <enumeration value="ASC">
        <annotation>
          <documentation>vzestupně</documentation>
        </annotation>
      </enumeration>
      <enumeration value="DESC">
        <annotation>
          <documentation>sestupně</documentation>
        </annotation>
      </enumeration>
    </restriction>
  </simpleType>

  <complexType name="recordsSortingType">
    <sequence>
      <element name="orderByAttribute" type="tns:orderByAttributeEnum">
        <annotation>
          <documentation>atribut podle kterého se mají řadit vracené záznamy</documentation>
        </annotation>
      </element>
      <element name="orderByDirection" type="tns:orderByDirectionEnum" default="ASC" minOccurs="0">
        <annotation>
          <documentation>způsob řazení vracených záznamů (vzestupně vs. sestupně), výchozí je vzestupné řazení</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>

  <element name="findStandingOrderNgRequest">
    <complexType>
      <sequence>
        <element name="idBankAccountDebit" type="long" minOccurs="0">
          <annotation>
            <documentation>vyhledávání podle identifikátoru účtu na kterém je trvalý příkaz nastaven</documentation>
          </annotation>
        </element>
        <element name="idTargetEnvelope" type="long" minOccurs="0">
          <annotation>
            <documentation>vyhledávání podle identifikátoru cílové obálky přiřazené trvalému příkazu</documentation>
          </annotation>
        </element>
        <element name="isSavings" type="boolean" minOccurs="0">
          <annotation>
            <documentation>
              true = pouze pravidelná spoření kde kreditní účet je typu spořící účet a patří ke stejné rámcové smlouvě jako debetní
              false = pouze standardní trvalé příkazy bez pravidelných spoření
            </documentation>
          </annotation>
        </element>
        <element name="idCategory" type="long" minOccurs="0">
          <annotation>
            <documentation>vyhledávání podle identifikátoru kategorie plateb</documentation>
          </annotation>
        </element>
        <element name="idBankAccountCredit" type="long" minOccurs="0">
          <annotation>
            <documentation>vyhledávání podle identifikátoru protiúčtu (cílového účtu)</documentation>
          </annotation>
        </element>
        <element name="status" type="tns:statusEnum" minOccurs="0">
          <annotation>
            <documentation>vyhledávání podle statusu trvalého příkazu, přičemž pokud atribut není vyplněn, vrací se TP ve stavu ACTIVE a SUSPENDED</documentation>
          </annotation>
        </element>
        <element name="profileIDs" type="long" maxOccurs="unbounded" minOccurs="0">
          <annotation>
            <documentation>identifikátory profilu, pro které se mají vyhledat trvalé příkazy</documentation>
          </annotation>
        </element>
        <element name="maxRecords" type="int" default="1000">
          <annotation>
            <documentation>maximální počet vracených záznamů, pokud není určeno je použita výchozí hodnota 1000</documentation>
          </annotation>
        </element>
        <element name="recordsSorting" type="tns:recordsSortingType" minOccurs="0" maxOccurs="3">
          <annotation>
            <documentation>slouží k definici atributů a způsobu jejich řazení (vzestupně x sestupně), řadit lze až do třetí úrovně</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="findStandingOrderNgResponse">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long" maxOccurs="unbounded" minOccurs="0"/>
      </sequence>
    </complexType>
  </element>

  <element name="getAccountPocketMoneyPaymentsRequest">
    <complexType>
      <sequence>
        <element name="idBankAccount" type="long"/>
        <element name="cuid" type="long">
          <annotation>
            <documentation>Identifikace osoby, která má dispoziční práva k uvedenému účtu.</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="getAccountPocketMoneyPaymentsResponse">
    <complexType>
      <sequence>
        <element name="pocketMoneyPayment" type="tt:PocketMoneyPaymentTO" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="getStandingOrderRequest">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="getStandingOrderResponse">
    <complexType>
      <sequence>
        <element name="standingOrder" type="tt:StandingOrderTO"
          maxOccurs="unbounded" minOccurs="0"/>
      </sequence>
    </complexType>
  </element>

  <element name="setStandingOrderRequest">
    <complexType>
      <sequence>
        <element name="standingOrder" type="tt:StandingOrderTO"/>
        <element name="authorization" type="aa:AuthType" minOccurs="0"/>
        <element name="validate" type="boolean">
          <annotation>
            <documentation>true - OBS pouze validuje data</documentation>
          </annotation>
        </element>
        <element name="skipSoftCheck" type="string" maxOccurs="unbounded" minOccurs="0">
          <annotation>
            <documentation>Soft checks that be skipped.</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="setStandingOrderResponse">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long" minOccurs="0">
          <annotation>
            <documentation>primární klíč nově založeného / editovaného trvalého příkazu</documentation>
          </annotation>
        </element>
        <element name="insufficientFundsFuture" type="boolean" minOccurs="0">
          <annotation>
            <documentation>there are insufficient funds on debit account for standing order due into future</documentation>
          </annotation>
        </element>
      </sequence>
    </complexType>
  </element>

  <element name="editStandingOrderRequest">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long"/>
      </sequence>
    </complexType>
  </element>

  <element name="editStandingOrderResponse">
    <complexType>
      <sequence>
      </sequence>
    </complexType>
  </element>

  <element name="deleteStandingOrderRequest">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long"/>
        <element name="authorization" type="aa:AuthType"/>
      </sequence>
    </complexType>
  </element>

  <element name="deleteStandingOrderResponse">
    <complexType>
      <sequence>
      </sequence>
    </complexType>
  </element>

  <element name="getStandingOrderInstanceInfoRequest">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="getStandingOrderInstanceInfoResponse">
    <complexType>
      <sequence>
        <element name="instanceInfo" type="tt:StandingOrderInstanceInfoTO"
          minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="getStandingOrderPaymentsRequest">
    <complexType>
      <sequence>
        <element name="IDStandingOrder" type="long"/>
      </sequence>
    </complexType>
  </element>

  <element name="getStandingOrderPaymentsResponse">
    <complexType>
      <sequence>
        <element name="instructionStatus" type="string"/>
        <element name="realizedPayments" type="tt:RealizedPayment" minOccurs="0" maxOccurs="unbounded"/>
      </sequence>
    </complexType>
  </element>

  <element name="suspendStandingOrderRequest">
    <complexType>
      <sequence>
        <element name="idStandingOrder" type="long"/>
        <element name="suspendedFrom" type="date"/>
        <element name="suspendedTo" type="date" minOccurs="0"/>
      </sequence>
    </complexType>
  </element>

  <element name="suspendStandingOrderResponse">
    <complexType>
      <sequence>
        <element name="resultStatus" minOccurs="0">
          <annotation>
            <documentation>v případě úspěšného provedení operace pozastavení trvalé platby obsahuje status výsledku</documentation>
          </annotation>
          <simpleType>
            <restriction base="string">
              <enumeration value="OK_SUSPENDED">
                <annotation>
                  <documentation>pozastavení trvalého příkazu bylo úspěšně provedeno</documentation>
                </annotation>
              </enumeration>
              <enumeration value="OK_WILL_BE_SUSPENDED">
                <annotation>
                  <documentation>pozastavení trvalého příkazu bylo úspěšně naplánováno na budoucí datum</documentation>
                </annotation>
              </enumeration>
            </restriction>
          </simpleType>
        </element>
      </sequence>
    </complexType>
  </element>
</schema>
