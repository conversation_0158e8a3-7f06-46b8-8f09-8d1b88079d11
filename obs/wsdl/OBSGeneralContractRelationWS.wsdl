<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions name="OBSGeneralContractRelationWS"
             targetNamespace="http://airbank.cz/obs/ws/OBSGeneralContractRelationWS/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://airbank.cz/obs/ws/OBSGeneralContractRelationWS/"
             xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/OBSGeneralContractRelationWS/">
            <xsd:include schemaLocation="OBSGeneralContractRelationWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
        </xsd:schema>
    </types>


    <!-- společný fault pro vsechny operace -->
    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType"/>
    </message>

    <message name="GetEntitledPersonsRequest">
        <part name="GetEntitledPersonsRequest" element="tns:GetEntitledPersonsRequest"/>
    </message>

    <message name="GetEntitledPersonsResponse">
        <part name="GetEntitledPersonsResponse" element="tns:GetEntitledPersonsResponse"/>
    </message>

    <portType name="OBSGeneralContractRelationWS">
        <operation name="GetEntitledPersons">
            <documentation>
                Ke každému zadanému SME cuid vrátí k jeho smlouvě navázanou (aktuálně platnou) oprávněnou osobu

                požadované podmínky:
                přihlášeného uživatele: ne
                vyžadován securityContext:  ne

                kód / atribut / hodnota : popis
                GENERAL_ERROR /GENERAL_ERROR / : obecná chyba
                CLERR_TIMEOUT / GENERAL_ERROR / : systém nestihl zpracovat požadavek v čas, případné změny byly odrolovány.
                CARDINALITY_OUT_OF_RANGE / jmeno kolekce / skutecny pocet prvku kolekce : pocet prvku kolekce je mimo definovany limit
            </documentation>
            <input message="tns:GetEntitledPersonsRequest"/>
            <output message="tns:GetEntitledPersonsResponse"/>
            <fault name="fault" message="tns:faultMessage"/>
        </operation>
    </portType>

    <binding name="OBSGeneralContractRelationWSSOAP" type="tns:OBSGeneralContractRelationWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="GetEntitledPersons">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="OBSGeneralContractRelationWS">
        <port binding="tns:OBSGeneralContractRelationWSSOAP" name="OBSGeneralContractRelationWSSOAP">
            <soap:address location="/ws/OBSGeneralContractRelationWS"/>
        </port>
    </service>

</definitions>
