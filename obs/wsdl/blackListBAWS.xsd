<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/blackListBAWS"
    targetNamespace="http://airbank.cz/obs/ws/blackListBAWS">


    <simpleType name="restrictionLevelEnum">
        <restriction base="string">
            <enumeration value="NOT_ALLOWED">
                <annotation>
                    <documentation>
                       platby s uctem s touto urovni restrikce nikdy neprovadime
                    </documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_RECOMMENDED">
                <annotation>
                    <documentation>
                       platby s uctem s touto urovni restrikce provadime po predchozim varovani/souhlasu uzivatele
                    </documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>


    <element name="isAccountBlacklistedRequest">
        <complexType>
            <sequence>
                <element name="accountNumber" type="string">
                    <annotation>
                        <documentation>
                           cislo uctu (vnitrostatni nebo IBAN)
                        </documentation>
                    </annotation>
                </element>
                <element name="bankCode" type="string">
                    <annotation>
                        <documentation>
                           kod banky u ktere je ucet veden (vnitrostatni nebo SWIFT/BIC)
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="isAccountBlacklistedResponse">
        <complexType>
            <sequence>
                <element name="isAccountBlacklisted" type="boolean">
                    <annotation>
                        <documentation>
                           TRUE pokud je ucet umisten na blacklistu uctu
                           FALSE pokud NEni ucet umisten na blacklistu uctu (FALSE vrati i v pripade ze neni vyplneny nektery z parametru isAccountNumber nebo isBankCode)
                        </documentation>
                    </annotation>
                </element>
                <element name="reasonCode" type="string" minOccurs="0">
                    <annotation>
                        <documentation>
                           duvod pro ktery je ucet umisten na blacklistu
                        </documentation>
                    </annotation>
                </element>
                <element name="restrictionLevel" type="tns:restrictionLevelEnum" minOccurs="0">
                    <annotation>
                        <documentation>
                            uroven restrikce
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>

