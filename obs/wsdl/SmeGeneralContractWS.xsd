<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:c="http://airbank.cz/obs/ws/contract"
    xmlns:tns="http://airbank.cz/obs/ws/SmeGeneralContractWS/"
    targetNamespace="http://airbank.cz/obs/ws/SmeGeneralContractWS/">

    <import schemaLocation="../xsd/contract.xsd" namespace="http://airbank.cz/obs/ws/contract"/>

    <complexType name="Operator">
        <annotation>
            <documentation>Operator/employee info</documentation>
        </annotation>
        <sequence>
            <element name="posId" type="string">
                <annotation>
                    <documentation>Point of sales identification, should be branch identifier, ecc identifier</documentation>
                </annotation>
            </element>
            <element name="operatorId" type="string">
                <annotation>
                    <documentation>Operator identification, should be employee number from LDAP</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="ApplicationProcessType">
        <annotation>
            <documentation>Možné způsoby založení žádosti pro SME</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="STANDARD">
                <annotation>
                    <documentation>Standardní žádost</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SHORT">
                <annotation>
                    <documentation>Zrychlená žádost</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <element name="createGeneralContractRequest">
        <annotation>
            <documentation>
                Request to create a new SME General contract for client specified by CUIDs for Entrepreneur (FOP) and Customer (FON).
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="cuidSME" type="long">
                    <annotation>
                        <documentation>CUID of SME (FOP, PO)</documentation>
                    </annotation>
                </element>
                <element name="cuidCustomer" type="long">
                    <annotation>
                        <documentation>CUID of Customer (FON)</documentation>
                    </annotation>
                </element>
                <element name="envelopeId" type="long">
                    <annotation>
                        <documentation>Envelope ID</documentation>
                    </annotation>
                </element>
                <element name="applicationId" type="long">
                    <annotation>
                        <documentation>Application ID</documentation>
                    </annotation>
                </element>
                <element name="creationDate" type="dateTime">
                    <annotation>
                        <documentation>Date of application</documentation>
                    </annotation>
                </element>
                <element name="username" type="string" minOccurs="0">
                    <annotation>
                        <documentation>Username</documentation>
                    </annotation>
                </element>
                <element name="generatePassword" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>Flag whether OBS should generate a password</documentation>
                    </annotation>
                </element>

                <element name="applicationProcessType" type="tns:ApplicationProcessType">
                    <annotation>
                        <documentation>Process založení žádosti</documentation>
                    </annotation>
                </element>
                <element name="expectedAnnualTurnoverCategory" type="string" minOccurs="0">
                    <annotation>
                        <documentation>očekávané roční obraty na účtech, hodnota z MDM ciselniku EXPECTED_ANNUAL_TURNOVER_CATEGORY</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="createGeneralContractResponse">
        <annotation>
            <documentation>
                Response returning GCID of created new SME General contract.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="gcid" type="long">
                    <annotation>
                        <documentation>SME General contract ID</documentation>
                    </annotation>
                </element>
                <element name="contractNumber" type="string">
                    <annotation>
                        <documentation>SME General contract number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="activateGeneralContractRequest">
        <annotation>
            <documentation>
                Request to activate SME General contract for mentioned GCID.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="gcid" type="long">
                    <annotation>
                        <documentation>SME General contract ID</documentation>
                    </annotation>
                </element>
                <element name="activationReason" type="c:ActivationReason">
                    <annotation>
                        <documentation>Activation reason</documentation>
                    </annotation>
                </element>
                <element name="operator" type="tns:Operator">
                    <annotation>
                        <documentation>Operator/employee info</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="activateGeneralContractResponse">
        <annotation>
            <documentation>
                Response returning result of activation of SME General contract (NULL is OK, otherwise SOAP fault is returned).
            </documentation>
        </annotation>
        <complexType>
            <sequence/>
        </complexType>
    </element>

    <element name="setAccountsPassiveRequest">
        <annotation>
            <documentation>
                Request to set accounts under SME General contract to passive (waitingforactivation) status.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="generalContractNumber" type="string">
                    <annotation>
                        <documentation>SME General contract number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="setAccountsPassiveResponse">
        <annotation>
            <documentation>
                Response returning result of setting of passive status (NULL is OK, otherwise SOAP fault is returned).
            </documentation>
        </annotation>
        <complexType>
            <sequence/>
        </complexType>
    </element>

    <element name="cancelGeneralContractRequest">
        <annotation>
            <documentation>
                Request to cancel SME General contract.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="generalContractNumber" type="string">
                    <annotation>
                        <documentation>SME General contract number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="cancelGeneralContractResponse">
        <annotation>
            <documentation>
                Response returning result of SME General contract cancellation (NULL is OK, otherwise SOAP fault is returned).
            </documentation>
        </annotation>
        <complexType>
            <sequence/>
        </complexType>
    </element>

    <element name="getExpectedAnnualTurnoverCategoryRequest">
        <annotation>
            <documentation>
                Request to get expected annual turnover category.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="generalContractNumber" type="string">
                    <annotation>
                        <documentation>SME General contract number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getExpectedAnnualTurnoverCategoryResponse">
        <annotation>
            <documentation>
                Response returning expected annual turnover category.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="ExpectedAnnualTurnoverCategory" type="string">
                    <annotation>
                        <documentation>Expected annual turnover category for general contract number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="setExpectedAnnualTurnoverCategoryRequest">
        <annotation>
            <documentation>
                Request to set expected annual turnover category.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="generalContractNumber" type="string">
                    <annotation>
                        <documentation>SME General contract number</documentation>
                    </annotation>
                </element>
                <element name="ExpectedAnnualTurnoverCategory" type="string">
                    <annotation>
                        <documentation>Expected annual turnover category for general contract number</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="setExpectedAnnualTurnoverCategoryResponse">
        <annotation>
            <documentation>
                Response returning info whether category was set.
            </documentation>
        </annotation>
        <complexType>
            <sequence>
                <element name="result" type="boolean">
                    <annotation>
                        <documentation>Return true if category was set</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

</schema>

