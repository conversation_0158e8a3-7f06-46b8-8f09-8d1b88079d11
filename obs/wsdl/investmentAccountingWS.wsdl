<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    name="investmentAccountingWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://airbank.cz/obs/ws/investmentAccountingWS"
    targetNamespace="http://airbank.cz/obs/ws/investmentAccountingWS">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/investmentAccountingWS">
            <xsd:include schemaLocation="investmentAccountingWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>
    <message name="isClientAbleToPayRequest">
        <part name="isClientAbleToPayRequest" element="tns:isClientAbleToPayRequest"/>
    </message>
    <message name="isClientAbleToPayResponse">
        <part name="isClientAbleToPayResponse" element="tns:isClientAbleToPayResponse"/>
    </message>
    <message name="reserveBuyTotalAmountRequest">
        <part name="reserveBuyTotalAmountRequest" element="tns:reserveBuyTotalAmountRequest"/>
    </message>
    <message name="reserveBuyTotalAmountResponse">
        <part name="reserveBuyTotalAmountResponse" element="tns:reserveBuyTotalAmountResponse"/>
    </message>
    <message name="partlyCancelBuyRequest">
        <part name="partlyCancelBuyRequest" element="tns:partlyCancelBuyRequest"/>
    </message>
    <message name="partlyCancelBuyResponse">
        <part name="partlyCancelBuyResponse" element="tns:partlyCancelBuyResponse"/>
    </message>
     <message name="chargeSettlementBuyRequest">
        <part name="chargeSettlementBuyRequest" element="tns:chargeSettlementBuyRequest"/>
    </message>
    <message name="chargeSettlementBuyResponse">
        <part name="chargeSettlementBuyResponse" element="tns:chargeSettlementBuyResponse"/>
    </message>
    <message name="chargeClientBuyPaymentRequest">
        <part name="chargeClientBuyPaymentRequest" element="tns:chargeClientBuyPaymentRequest"/>
    </message>
    <message name="chargeClientBuyPaymentResponse">
        <part name="chargeClientBuyPaymentResponse" element="tns:chargeClientBuyPaymentResponse"/>
    </message>
    <message name="settleCounterpartySumPaymentRequest">
        <part name="settleCounterpartySumPaymentRequest" element="tns:settleCounterpartySumPaymentRequest"/>
    </message>
    <message name="settleCounterpartySumPaymentResponse">
        <part name="settleCounterpartySumPaymentResponse" element="tns:settleCounterpartySumPaymentResponse"/>
    </message>
    <message name="settleFeeRequest">
        <part name="settleFeeRequest" element="tns:settleFeeRequest"/>
    </message>
    <message name="settleFeeResponse">
        <part name="settleFeeResponse" element="tns:settleFeeResponse"/>
    </message>
    <message name="bulkUpdateAssetValueRequest">
        <part name="bulkUpdateAssetValueRequest" element="tns:bulkUpdateAssetValueRequest"/>
    </message>
    <message name="bulkUpdateAssetValueResponse">
        <part name="bulkUpdateAssetValueResponse" element="tns:bulkUpdateAssetValueResponse"/>
    </message>
    <message name="bulkReversedUpdateAssetValueRequest">
        <part name="bulkReversedUpdateAssetValueRequest" element="tns:bulkReversedUpdateAssetValueRequest"/>
    </message>
    <message name="bulkReversedUpdateAssetValueResponse">
        <part name="bulkReversedUpdateAssetValueResponse" element="tns:bulkReversedUpdateAssetValueResponse"/>
    </message>
    <message name="receiveCouponRequest">
        <part name="receiveCouponRequest" element="tns:receiveCouponRequest"/>
    </message>
    <message name="receiveCouponResponse">
        <part name="receiveCouponResponse" element="tns:receiveCouponResponse"/>
    </message>
    <message name="chargeStandaloneFeeRequest">
        <part name="chargeStandaloneFeeRequest" element="tns:chargeStandaloneFeeRequest"/>
    </message>
    <message name="chargeStandaloneFeeResponse">
        <part name="chargeStandaloneFeeResponse" element="tns:chargeStandaloneFeeResponse"/>
    </message>
    <message name="tradeSellOrMaturityRequest">
        <part name="tradeSellOrMaturityRequest" element="tns:tradeSellOrMaturityRequest"/>
    </message>
    <message name="tradeSellOrMaturityResponse">
        <part name="tradeSellOrMaturityResponse" element="tns:tradeSellOrMaturityResponse"/>
    </message>
    <message name="chargeSellOrMaturityRequest">
        <part name="chargeSellOrMaturityRequest" element="tns:chargeSellOrMaturityRequest"/>
    </message>
    <message name="chargeSellOrMaturityResponse">
        <part name="chargeSellOrMaturityResponse" element="tns:chargeSellOrMaturityResponse"/>
    </message>
    <message name="chargeTransferRequest">
        <part name="chargeTransferRequest" element="tns:chargeTransferRequest"/>
    </message>
    <message name="chargeTransferResponse">
        <part name="chargeTransferResponse" element="tns:chargeTransferResponse"/>
    </message>
    <message name="chargeCouponPaymentLiabilityRequest">
        <part name="chargeCouponPaymentLiabilityRequest" element="tns:chargeCouponPaymentLiabilityRequest"/>
    </message>
    <message name="chargeCouponPaymentLiabilityResponse">
        <part name="chargeCouponPaymentLiabilityResponse" element="tns:chargeCouponPaymentLiabilityResponse"/>
    </message>
    <message name="blockBuyAmountRequest">
        <part name="parameters" element="tns:blockBuyAmountRequest" />
    </message>
    <message name="blockBuyAmountResponse">
        <part name="parameters" element="tns:blockBuyAmountResponse" />
    </message>
    <message name="receiveDividendRequest">
        <part name="parameters" element="tns:receiveDividendRequest" />
    </message>
    <message name="receiveDividendResponse">
        <part name="parameters" element="tns:receiveDividendResponse" />
    </message>
    <message name="IBKRAccountTransactionRequest">
        <part name="parameters" element="tns:IBKRAccountTransactionRequest" />
    </message>
    <message name="IBKRAccountTransactionResponse">
        <part name="parameters" element="tns:IBKRAccountTransactionResponse" />
    </message>
    <message name="chargeClientSellRequest">
        <part name="parameters" element="tns:chargeClientSellRequest" />
    </message>
    <message name="chargeClientSellResponse">
        <part name="parameters" element="tns:chargeClientSellResponse" />
    </message>

    <portType name="investmentAccounting">
        <operation name="isClientAbleToPay">
            <input message="tns:isClientAbleToPayRequest"/>
            <output message="tns:isClientAbleToPayResponse"/>
        </operation>
        <operation name="reserveBuyTotalAmount">
            <input message="tns:reserveBuyTotalAmountRequest"/>
            <output message="tns:reserveBuyTotalAmountResponse"/>
        </operation>
        <operation name="chargeSettlementBuy">
            <input message="tns:chargeSettlementBuyRequest"/>
            <output message="tns:chargeSettlementBuyResponse"/>
        </operation>
        <operation name="partlyCancelBuy">
            <input message="tns:partlyCancelBuyRequest" />
            <output message="tns:partlyCancelBuyResponse" />
        </operation>
        <operation name="chargeClientBuyPayment">
            <input message="tns:chargeClientBuyPaymentRequest"/>
            <output message="tns:chargeClientBuyPaymentResponse"/>
        </operation>
        <operation name="settleCounterpartySumPayment">
            <input message="tns:settleCounterpartySumPaymentRequest"/>
            <output message="tns:settleCounterpartySumPaymentResponse"/>
        </operation>
        <operation name="settleFee">
            <input message="tns:settleFeeRequest"/>
            <output message="tns:settleFeeResponse"/>
        </operation>
        <operation name="bulkUpdateAssetValue">
            <input message="tns:bulkUpdateAssetValueRequest"/>
            <output message="tns:bulkUpdateAssetValueResponse"/>
        </operation>
         <operation name="bulkReversedUpdateAssetValue">
            <input message="tns:bulkReversedUpdateAssetValueRequest"/>
            <output message="tns:bulkReversedUpdateAssetValueResponse"/>
        </operation>
        <operation name="receiveCoupon">
            <input message="tns:receiveCouponRequest"/>
            <output message="tns:receiveCouponResponse"/>
        </operation>
        <operation name="chargeStandaloneFee">
            <input message="tns:chargeStandaloneFeeRequest"/>
            <output message="tns:chargeStandaloneFeeResponse"/>
        </operation>
        <operation name="tradeSellOrMaturity">
            <input message="tns:tradeSellOrMaturityRequest"/>
            <output message="tns:tradeSellOrMaturityResponse"/>
        </operation>
        <operation name="chargeSellOrMaturity">
            <input message="tns:chargeSellOrMaturityRequest"/>
            <output message="tns:chargeSellOrMaturityResponse"/>
        </operation>
        <operation name="chargeTransfer">
            <input message="tns:chargeTransferRequest"/>
            <output message="tns:chargeTransferResponse"/>
        </operation>
        <operation name="chargeCouponPaymentLiability">
            <input message="tns:chargeCouponPaymentLiabilityRequest"/>
            <output message="tns:chargeCouponPaymentLiabilityResponse"/>
        </operation>
        <operation name="blockBuyAmount">
            <documentation>
                Block requested amount for investment order.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                NO_SUCH_CLIENT / CUID / value : CUID does not exist.
                NO_SUCH_ACCOUNT / debitAccount / value :  Bank account does not exist.
                CLIENT_ACCOUNT_MISMATCH / :  Bank account does not belong to specified client.
                NO_FXRATE / : Account currency and blocking currency are different and FXRate is not specified.
                INSUFFICIENT_BALANCE / :  Insufficient balance on bank account.
            </documentation>
            <input message="tns:blockBuyAmountRequest" />
            <output message="tns:blockBuyAmountResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
        <operation name="receiveDividend">
            <documentation>
                Account received bulk of dividends.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                NO_SUCH_CLIENT / CUID / value : CUID does not exist.
                NO_SUCH_ACCOUNT / debitAccount / value :  Bank account does not exist.
                CLIENT_ACCOUNT_MISMATCH / :  Bank account does not belong to specified client.
            </documentation>
            <input message="tns:receiveDividendRequest" />
            <output message="tns:receiveDividendResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
        <operation name="IBKRAccountTransaction">
            <documentation>
                Account transactions over the Master Account (MA) / Proprietary Account (PA) used by AB for equity/ETL trades.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
            </documentation>
            <input message="tns:IBKRAccountTransactionRequest" />
            <output message="tns:IBKRAccountTransactionResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
        <operation name="chargeClientSell">
            <documentation>
                Služba zaúčtovává úhrady poplatků trhu vůči IBKR (Interactive brokers).
                The service accounts for the payment of market fees to IBKR (Interactive brokers).

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CANCEL_NOT_SUPPORTED - Pokud by cancel indicator byl nastavený na true. / If the cancel indicator was set to true.
            </documentation>
            <input message="tns:chargeClientSellRequest" />
            <output message="tns:chargeClientSellResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
    </portType>

    <binding name="investmentAccountingSOAP" type="tns:investmentAccounting">
        <soap:binding style="document"
                      transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="isClientAbleToPay">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="reserveBuyTotalAmount">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="chargeSettlementBuy">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="partlyCancelBuy">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
        </operation>
        <operation name="chargeClientBuyPayment">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="settleCounterpartySumPayment">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="settleFee">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="bulkUpdateAssetValue">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="bulkReversedUpdateAssetValue">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="receiveCoupon">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="chargeStandaloneFee">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="tradeSellOrMaturity">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="chargeSellOrMaturity">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="chargeTransfer">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="chargeCouponPaymentLiability">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="blockBuyAmount">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="receiveDividend">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="IBKRAccountTransaction">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
        <operation name="chargeClientSell">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="investmentAccountingWS">
        <port binding="tns:investmentAccountingSOAP" name="investmentAccountingSOAP">
            <soap:address location="/ws/investmentAccountingWS"/>
        </port>
    </service>
</definitions>
