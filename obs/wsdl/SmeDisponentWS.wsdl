<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions name="SmeDisponentWS"
             targetNamespace="http://airbank.cz/obs/ws/SmeDisponentWS/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://airbank.cz/obs/ws/SmeDisponentWS/"
             xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/SmeDisponentWS/">
            <xsd:include schemaLocation="SmeDisponentWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="CheckContractEligibilityRequest">
        <part name="CheckContractEligibilityRequest" element="tns:CheckContractEligibilityRequest"/>
    </message>

    <message name="CheckContractEligibilityResponse">
        <part name="CheckContractEligibilityResponse" element="tns:CheckContractEligibilityResponse"/>
    </message>

    <message name="CreateContractDocumentRequest">
        <part name="CreateContractDocumentRequest" element="tns:CreateContractDocumentRequest"/>
    </message>

    <message name="CreateContractDocumentResponse">
        <part name="CreateContractDocumentResponse" element="tns:CreateContractDocumentResponse"/>
    </message>

    <message name="ActivateDisponentRequest">
        <part name="ActivateDisponentRequest" element="tns:ActivateDisponentRequest"/>
    </message>

    <message name="ActivateDisponentResponse">
        <part name="ActivateDisponentResponse" element="tns:ActivateDisponentResponse"/>
    </message>

    <message name="CreateTerminationContractDocumentRequest">
        <part name="CreateTerminationContractDocumentRequest" element="tns:CreateTerminationContractDocumentRequest"/>
    </message>

    <message name="CreateTerminationContractDocumentResponse">
        <part name="CreateTerminationContractDocumentResponse" element="tns:CreateTerminationContractDocumentResponse"/>
    </message>

    <message name="TerminateDisponentRequest">
        <part name="TerminateDisponentRequest" element="tns:TerminateDisponentRequest"/>
    </message>

    <message name="TerminateDisponentResponse">
        <part name="TerminateDisponentResponse" element="tns:TerminateDisponentResponse"/>
    </message>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <portType name="SmeDisponentWS">
        <operation name="CheckContractEligibility">
            <documentation>
                Check contract eligibility for adding new disponent
            </documentation>
            <input message="tns:CheckContractEligibilityRequest"/>
            <output message="tns:CheckContractEligibilityResponse"/>
        </operation>
        <operation name="CreateContractDocument">
            <documentation>
                Create documents for new disponent
            </documentation>
            <input message="tns:CreateContractDocumentRequest"/>
            <output message="tns:CreateContractDocumentResponse"/>
        </operation>
        <operation name="ActivateDisponent">
            <documentation>
                Activate new disponent
            </documentation>
            <input message="tns:ActivateDisponentRequest"/>
            <output message="tns:ActivateDisponentResponse"/>
        </operation>
        <operation name="CreateTerminationContractDocument">
            <documentation>
                Create termination documents for new disponent
            </documentation>
            <input message="tns:CreateTerminationContractDocumentRequest"/>
            <output message="tns:CreateTerminationContractDocumentResponse"/>
        </operation>
        <operation name="TerminateDisponent">
            <documentation>
                Terminate disponent
            </documentation>
            <input message="tns:TerminateDisponentRequest"/>
            <output message="tns:TerminateDisponentResponse"/>
        </operation>
    </portType>

    <binding name="SmeDisponentWSSOAP" type="tns:SmeDisponentWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="CheckContractEligibility">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="CreateContractDocument">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="ActivateDisponent">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="CreateTerminationContractDocument">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="TerminateDisponent">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>

    <service name="SmeDisponentWS">
        <port binding="tns:SmeDisponentWSSOAP" name="SmeDisponentWSSOAP">
            <soap:address location="/ws/SmeDisponentWS"/>
        </port>
    </service>

</definitions>
