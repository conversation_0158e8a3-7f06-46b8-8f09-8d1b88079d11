<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    name="SmeGeneralContractWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://airbank.cz/obs/ws/SmeGeneralContractWS/"
    targetNamespace="http://airbank.cz/obs/ws/SmeGeneralContractWS/">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/SmeGeneralContractWS/">
            <xsd:include schemaLocation="SmeGeneralContractWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <message name="createGeneralContractRequest">
        <part name="parameters" element="tns:createGeneralContractRequest" />
    </message>

    <message name="createGeneralContractResponse">
        <part name="parameters" element="tns:createGeneralContractResponse" />
    </message>

    <message name="activateGeneralContractRequest">
        <part name="parameters" element="tns:activateGeneralContractRequest" />
    </message>

    <message name="activateGeneralContractResponse">
        <part name="parameters" element="tns:activateGeneralContractResponse" />
    </message>

    <message name="setAccountsPassiveRequest">
        <part name="parameters" element="tns:setAccountsPassiveRequest" />
    </message>

    <message name="setAccountsPassiveResponse">
        <part name="parameters" element="tns:setAccountsPassiveResponse" />
    </message>

    <message name="cancelGeneralContractRequest">
        <part name="parameters" element="tns:cancelGeneralContractRequest" />
    </message>

    <message name="cancelGeneralContractResponse">
        <part name="parameters" element="tns:cancelGeneralContractResponse" />
    </message>

    <message name="getExpectedAnnualTurnoverCategoryRequest">
        <part name="parameters" element="tns:getExpectedAnnualTurnoverCategoryRequest" />
    </message>

    <message name="getExpectedAnnualTurnoverCategoryResponse">
        <part name="parameters" element="tns:getExpectedAnnualTurnoverCategoryResponse" />
    </message>

    <message name="setExpectedAnnualTurnoverCategoryRequest">
        <part name="parameters" element="tns:setExpectedAnnualTurnoverCategoryRequest" />
    </message>

    <message name="setExpectedAnnualTurnoverCategoryResponse">
        <part name="parameters" element="tns:setExpectedAnnualTurnoverCategoryResponse" />
    </message>

    <portType name="SmeGeneralContractWS">

        <operation name="createGeneralContract">
            <documentation>
                Create a new General contract for client specified by CUIDs for Entrepreneur (FOP) and Customer (FON).

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                UNKNOWN_CUSTOMER / cuidEntrepreneur / value : CUID of Entrepreneur (FOP) does not exist.
                UNKNOWN_CUSTOMER / cuidCustomer / value : CUID of Customer (FON) does not exist.
                ALREADY_OWNER / cuidEntrepreneur / value : Entrepreneur (FOP) is already owner of another SME General contract.
            </documentation>
            <input message="tns:createGeneralContractRequest" />
            <output message="tns:createGeneralContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="activateGeneralContract">
            <documentation>
                Activate SME General contract for mentioned GCID.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / gcid / value : General Contract for mentioned GCID does not exist.
                CLERR_WRONG_STATUS / status / value : General Contract has improper status.
                CLERR_DOCUMENT_NOT_SIGNED / gcid / value : General Contract is not signed.
                CLERR_BANKACCOUNT_NOT_FOUND / gcid / value : There is no account in state WAITINGFORACTIVATION or DEMO for mentioned GCID.
                CLERR_OPERATOR_NOT_SPECIFIED / operator / value : Operator is not fully specified (both posId and operatorId are mandatory).
            </documentation>
            <input message="tns:activateGeneralContractRequest" />
            <output message="tns:activateGeneralContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setAccountsPassive">
            <documentation>
                Set accounts under SME General contract to passive (waitingforactivation) status.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / generalContractNumber / value : General Contract for mentioned GC number not exist.
                CLERR_WRONG_STATUS / status / value : General Contract has improper status.
                CLERR_DOCUMENT_NOT_SIGNED / generalContractNumber / value : General Contract is not signed.
                CLERR_BANKACCOUNT_NOT_FOUND / generalContractNumber / value : There is no account in state DEMO for mentioned GC number.
            </documentation>
            <input message="tns:setAccountsPassiveRequest" />
            <output message="tns:setAccountsPassiveResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="cancelGeneralContract">
            <documentation>
                Cancel SME General contract.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / generalContractNumber / value : General Contract for mentioned GC number not exist.
                CLERR_WRONG_STATUS / status / value : General Contract has improper status.
            </documentation>
            <input message="tns:cancelGeneralContractRequest" />
            <output message="tns:cancelGeneralContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="getExpectedAnnualTurnoverCategory">
            <documentation>
                Get category of expected annual turnover.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / generalContractNumber / value : General Contract for mentioned GC number not exist.
            </documentation>
            <input message="tns:getExpectedAnnualTurnoverCategoryRequest" />
            <output message="tns:getExpectedAnnualTurnoverCategoryResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setExpectedAnnualTurnoverCategory">
            <documentation>
                Set category of expected annual turnover.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / generalContractNumber / value : General Contract for mentioned GC number not exist.
            </documentation>
            <input message="tns:setExpectedAnnualTurnoverCategoryRequest" />
            <output message="tns:setExpectedAnnualTurnoverCategoryResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

    </portType>

    <binding name="SmeGeneralContractWSSOAP" type="tns:SmeGeneralContractWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="createGeneralContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="activateGeneralContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setAccountsPassive">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="cancelGeneralContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="getExpectedAnnualTurnoverCategory">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setExpectedAnnualTurnoverCategory">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

    </binding>

    <service name="SmeGeneralContractWS">
        <port binding="tns:SmeGeneralContractWSSOAP" name="SmeGeneralContractWSSOAP">
            <soap:address location="/ws/SmeGeneralContractWS"/>
        </port>
    </service>

</definitions>
