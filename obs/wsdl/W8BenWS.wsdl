<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
    name="W8BenWS"
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    xmlns:tns="http://airbank.cz/obs/ws/W8BenWS/"
    targetNamespace="http://airbank.cz/obs/ws/W8BenWS/">

    <types>
        <xsd:schema targetNamespace="http://airbank.cz/obs/ws/W8BenWS/">
            <xsd:include schemaLocation="W8BenWS.xsd"/>
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd"/>
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="parameters" element="com:ErrorsListType" />
    </message>

    <message name="createW8BenContractRequest">
        <part name="parameters" element="tns:createW8BenContractRequest" />
    </message>

    <message name="createW8BenContractResponse">
        <part name="parameters" element="tns:createW8BenContractResponse" />
    </message>

    <message name="createW8BenContractDocumentRequest">
        <part name="parameters" element="tns:createW8BenContractDocumentRequest" />
    </message>

    <message name="createW8BenContractDocumentResponse">
        <part name="parameters" element="tns:createW8BenContractDocumentResponse" />
    </message>

    <message name="activateContractRequest">
        <part name="activateContractRequest" element="tns:activateContractRequest" />
    </message>

    <message name="activateContractResponse">
        <part name="activateContractResponse" element="tns:activateContractResponse" />
    </message>

    <portType name="W8BenWS">

        <operation name="createW8BenContract">
            <documentation>
                Create new W8BEN contract.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / GCID / value : GCID does not exist.
                CLERR_WRONG_STATUS / GCID / value : General contract has incorrect status.
                CLERR_NO_DATA_FOUND / CUID / value : CUID does not exist.
                ALREADY_ACTIVE / W8BENContract / : The client already has an active W8BEN contract.
                DIFFERENT_PARAMETERS / W8BENContract / : The client has an W8BEN contract in a proposal with different parameters.
            </documentation>
            <input message="tns:createW8BenContractRequest" />
            <output message="tns:createW8BenContractResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="createW8BenContractDocument">
            <documentation>
                Create new completion for W8BEN contract.

                Assumptions:
                logged in user - yes

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR /GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CLERR_NO_DATA_FOUND / GCID / value : GCID does not exist.
                CLERR_WRONG_STATUS / GCID / value : General contract has incorrect status.
                CONTRACT_NOT_FOUND / contractNumber / value : The W8BEN contract was not found or is not in appropriate state.
                INVALID_PARAMETER / contractNumber / value : The W8BEN contract number must be entered.
            </documentation>
            <input message="tns:createW8BenContractDocumentRequest" />
            <output message="tns:createW8BenContractDocumentResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="activateContract">
            <documentation>
                Activate W8BEN contract.

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                CONTRACT_NOT_FOUND / contractNumber / value : The W8BEN contract was not found or is not in appropriate state.
                INVALID_PARAMETER / contractNumber / value : The W8BEN contract number must be entered.
            </documentation>
            <input message="tns:activateContractRequest" />
            <output message="tns:activateContractResponse" />
            <fault message="tns:faultMessage" name="fault" />
        </operation>

    </portType>

    <binding name="W8BenWSSOAP" type="tns:W8BenWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />

        <operation name="createW8BenContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="createW8BenContractDocument">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="activateContract">
            <soap:operation soapAction="" />
            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

    </binding>

    <service name="W8BenWS">
        <port binding="tns:W8BenWSSOAP" name="W8BenWSSOAP">
            <soap:address location="/ws/W8BenWS"/>
        </port>
    </service>

</definitions>
