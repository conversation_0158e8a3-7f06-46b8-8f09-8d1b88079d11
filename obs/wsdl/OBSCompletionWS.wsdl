<?xml version="1.0" encoding="UTF-8"?>
<definitions
    xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:tns="http://arbes.com/ib/core/ppf/ws/obsCompletionWS/"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    name="obsCompletionWS"
    targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCompletionWS/">

    <types>
        <xsd:schema targetNamespace="http://arbes.com/ib/core/ppf/ws/obsCompletionWS/">
            <xsd:import namespace="http://arbes.com/ib/core/ppf/ws/common/" schemaLocation="../xsd/Common.xsd" />
            <xsd:include schemaLocation="OBSCompletionWS.xsd" />
        </xsd:schema>
    </types>

    <message name="faultMessage">
        <part name="faultMessage" element="com:ErrorsListType" />
    </message>

    <message name="getAllCompletionsRequest">
        <part element="tns:getAllCompletionsRequest" name="getAllCompletionsRequest" />
    </message>

    <message name="getAllCompletionsResponse">
        <part element="tns:getAllCompletionsResponse" name="getAllCompletionsResponse" />
    </message>

    <message name="approveCompletionsRequest">
        <part name="approveCompletionsRequest" element="tns:approveCompletionsRequest" />
    </message>

    <message name="approveCompletionsResponse">
        <part name="approveCompletionsResponse" element="tns:approveCompletionsResponse" />
    </message>

    <message name="setAccountForRemoteIdentificationRequest">
        <part name="setAccountForRemoteIdentificationRequest" element="tns:setAccountForRemoteIdentificationRequest" />
    </message>

    <message name="setAccountForRemoteIdentificationResponse">
        <part name="setAccountForRemoteIdentificationResponse" element="tns:setAccountForRemoteIdentificationResponse" />
    </message>

    <message name="setSignatureRequest">
        <part name="setSignatureRequest" element="tns:setSignatureRequest" />
    </message>

    <message name="setSignatureResponse">
        <part name="setSignatureResponse" element="tns:setSignatureResponse" />
    </message>

    <portType name="obsCompletionWS">
        <operation name="getAllCompletions">
            <input message="tns:getAllCompletionsRequest" />
            <output message="tns:getAllCompletionsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="approveCompletions">
            <input message="tns:approveCompletionsRequest" />
            <output message="tns:approveCompletionsResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setAccountForRemoteIdentification">
            <input message="tns:setAccountForRemoteIdentificationRequest" />
            <output message="tns:setAccountForRemoteIdentificationResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>

        <operation name="setSignature">
            <documentation>
                Invalidate signature and add a new one

                Assumptions:
                logged in user - no

                Faults (format - "code / attribute / value : description"):
                CROSS_CHANNEL_CHECK / CROSS_CHANNEL_CHECK / : Mismatch received and signature channels.
                GENERAL_ERROR / GENERAL_ERROR / : Unexpected error.
                CLERR_TIMEOUT / GENERAL_ERROR / : Timeout reached while processing request. All changes were reverted.
                 / completionId / : Wrong completion id
            </documentation>
            <input message="tns:setSignatureRequest" />
            <output message="tns:setSignatureResponse" />
            <fault name="fault" message="tns:faultMessage" />
        </operation>
    </portType>

    <binding name="obsCompletionWSSOAP" type="tns:obsCompletionWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <operation name="getAllCompletions">
            <soap:operation soapAction="" />

            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="approveCompletions">
            <soap:operation soapAction="" />

            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setAccountForRemoteIdentification">
            <soap:operation soapAction="" />

            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>

        <operation name="setSignature">
            <soap:operation soapAction="" />

            <input>
                <soap:body use="literal" />
            </input>
            <output>
                <soap:body use="literal" />
            </output>
            <fault name="fault">
                <soap:fault use="literal" name="fault" />
            </fault>
        </operation>
    </binding>

    <service name="obsCompletionWS">
        <port binding="tns:obsCompletionWSSOAP" name="obsCompletionWSSOAP">
            <soap:address location="/ws/obsCompletionWS"/>
        </port>
    </service>
</definitions>