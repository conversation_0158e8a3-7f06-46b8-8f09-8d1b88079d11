<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://airbank.cz/obs/ws/obsBalanceWS/">

    <element name="getTotalDepositBalanceRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long"/>
                <element name="targetCurrency" type="string">
                    <annotation>
                        <documentation>Požadovaná měna pro získaný zůstatek, obvykle alpha3.</documentation>
                    </annotation>
                </element>
                <element name="valueDate" type="date">
                    <annotation>
                        <documentation>Datum, k němuž je souhrnná částka žádána.</documentation>
                    </annotation>
                </element>
                <element name="includeOverdraftLimit" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>Pouze, pokud je uvedeno "Ano" je zahrnut čerpaný kontokorent, jina<PERSON> v<PERSON><PERSON>.</documentation>
                    </annotation>
                </element>
                <element name="includeEntitledRelation" type="boolean" minOccurs="0">
                    <annotation>
                        <documentation>Pouze, pokud je uvedeno "Ano" jsou zahrnuty i účty rámcové smlouvy, kde je vstupníi cuid oprávněnou osobou.
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getTotalDepositBalanceResponse">
        <complexType>
            <sequence>
                <element name="totalDepositBalance" type="decimal"/>
            </sequence>
        </complexType>
    </element>

</schema>
