<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
        xmlns:fl="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema"
        xmlns:ta="http://airbank.cz/obs/ws/common/trustedAccountsWS"
        xmlns:cp="http://airbank.cz/obs/ws/counterparty"
        targetNamespace="http://airbank.cz/obs/ws/trustedAccountsWS">

    <import schemaLocation="../xsd/Filter.xsd" namespace="http://homecredit.net/manhattan-ib/ws/cz/FilterSchema" />
    <import schemaLocation="../xsd/trustedAccounts.xsd" namespace="http://airbank.cz/obs/ws/common/trustedAccountsWS" />
    <import schemaLocation="../xsd/counterparty.xsd" namespace="http://airbank.cz/obs/ws/counterparty" />

    <element name="findWhitelistRequest">
        <complexType>
            <sequence>
                <element name="filter" type="fl:SelectFilter">
                    <annotation>
                        <documentation>
                            One of the following attributes can be used for ordering:
                            - CREATIONDATE

                            If there is no ordering attribute specified,
                            the result is ordered by CREATIONDATE
                            (both in descending order).
                        </documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="findWhitelistResponse">
        <complexType>
            <sequence>
                <element name="id" type="long" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>identifiers of whitelist items</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getWhitelistRequest">
        <complexType>
            <sequence>
                <element name="id" type="long" maxOccurs="unbounded">
                    <annotation>
                        <documentation>identifiers of whitelist items</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="getWhitelistResponse">
        <complexType>
            <sequence>
                <element name="whitelistItem" type="ta:WhiteListItemTO"  minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>detail of whitelist item</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="removeFromWhitelistRequest">
        <complexType>
            <sequence>
                <element name="id" type="long">
                    <annotation>
                        <documentation>identifier of whitelist item</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <element name="removeFromWhitelistResponse">
        <complexType>
            <sequence>
            </sequence>
        </complexType>
    </element>

    <element name="determineAccountTrustLevelRequest">
        <complexType>
            <sequence>
                <element name="bankAccount" type="cp:CounterpartyAccount" />
            </sequence>
        </complexType>
    </element>

    <element name="determineAccountTrustLevelResponse">
        <complexType>
            <sequence>
                <element name="accountTrustLevel" type="ta:AccountTrustLevel">
                    <annotation>
                        <documentation>trust level of account</documentation>
                    </annotation>
                </element>
                <element name="whitelistItem" type="ta:WhiteListItemTO" minOccurs="0">
                    <annotation>
                        <documentation>detail of whitelist item (for accountTrustLevel CUSTOM_WHITELISTED only)</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>
</schema>
