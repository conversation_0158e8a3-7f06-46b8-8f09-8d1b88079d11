<?xml version="1.0" encoding="UTF-8"?>
<!--
<PERSON><PERSON><PERSON><PERSON> definuj<PERSON><PERSON><PERSON> strukturu úpravy exekuce
-->

<xs:schema version="1.1"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="https://www.czech-ba.cz/eExekuce/ep"
           xmlns="https://www.czech-ba.cz/eExekuce/ep"
           elementFormDefault="qualified">

    <xs:include schemaLocation="DistraintCommon.xsd" />

    <xs:element name="upravaExekuce" type="UpravaExekuce">
        <xs:annotation>
            <xs:documentation></xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:complexType name="UpravaExekuce">
        <xs:sequence>
            <xs:element name="identifikace" type="IdentifikaceDokumentu">
                <xs:annotation>
                    <xs:documentation>Identifikačn<PERSON> údaje <PERSON>pravy exekuce</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="datumVydani" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Datum vydání úpravy exekuce ve formátu yyyy-mm-dd, napr. 2014-04-05. Mandatorní údaj, pro zpracování nemá význam</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="exekucniPrikaz" type="IdentifikaceDokumentu" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Identifikace exekučního příkazu, ke kterému se váže úprava exekuce</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CastecneZastaveni">
        <xs:annotation>
            <xs:documentation>Snížení vymáhané pohledávky</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="UpravaExekuce">
                <xs:sequence>
                    <xs:element name="castka" type="xs:decimal" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Částku, o kterou je snížena vymáhaná pohledávka. Měna podle základní měny v EP.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="UplneZastaveni">
        <xs:annotation>
            <xs:documentation>Úplné zastavené exekuce</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="UpravaExekuce">

            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="VyplataBezVlivuNaPohledavku">
        <xs:annotation>
            <xs:documentation>Výplata bez vlivu na výši vymáhané pohledávky iniciovaná výstavcem. Např. výplata sociální povahy.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="UpravaExekuce">
                <xs:sequence>
                    <xs:element name="castka" type="xs:decimal" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Částka k výplatě. Měna podle základní měny v EP.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="PrevzetiExekuce">
        <xs:annotation>
            <xs:documentation>Převzetí exekuce novým správcem</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="UpravaExekuce">
                <xs:sequence>
                    <xs:element name="novySpravce" type="Vystavce" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Identifikace nového správce exekuce</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="platebniUdaje" minOccurs="1" maxOccurs="1" type="PlatebniUdaje">
                        <xs:annotation>
                            <xs:documentation>Nové platební údaje pro úhradu všech pohledávek v exekuci.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GetAllDistraintsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>cuid of the person</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllDistraintsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="allDistrintsList" type="DistraintDataTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>can be null or empty</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


</xs:schema>