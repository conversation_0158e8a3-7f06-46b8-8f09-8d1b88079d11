<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
  xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/"
  xmlns="http://www.w3.org/2001/XMLSchema"
  elementFormDefault="qualified" version="1.0"
  xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
  
	<complexType name="ObsAddressTypeTO">
		<sequence>
			<element name="id" type="long" minOccurs="0" maxOccurs="1" />
			<element name="type" type="string" minOccurs="1" maxOccurs="1">
				<annotation>
					<documentation>
BASE - trvalá adresa 
COM - Komunikační/Korespondenční adresa
Kód bude specifikován později - Adresa pro doručení karty 
Kód bude specifikován později - Adresa pro doručení PINu
Kód bude specifikován později - Adresa do zaměstnání

					</documentation>
				</annotation>
			</element>
			<element name="typeName" type="string" minOccurs="1"
				maxOccurs="1">
				<annotation>
					<documentation>
						název typu adresy V základu jsou definované názvy pro
						typy: pro typ BASE je název - trvalá adresa pro typ COM
						je název - komunikační adresa

					</documentation>
				</annotation>
			</element>
			<element name="name" type="string" minOccurs="1" maxOccurs="1">
				<annotation>
					<documentation>
						Název adresy. V základu je name = typeName. Ve WO lze na
						formuláři adresy tento název změnit.
					</documentation>
				</annotation>
			</element>
			<element name="zip" type="string" minOccurs="1" maxOccurs="1" />
			<element name="city" type="string" minOccurs="1" maxOccurs="1" />
			<element name="countryCode" type="string" minOccurs="1"
				maxOccurs="1">
				<annotation>
					<documentation>kód zěmě podle ISO 3166-1</documentation>
				</annotation>
			</element>
			<element name="street" type="string" minOccurs="0" maxOccurs="1">
				<annotation>
					<documentation>ulice</documentation>
				</annotation>
			</element>
			<element name="addrHouseNumber" type="string" minOccurs="0"
				maxOccurs="1">
				<annotation>
					<documentation>číslo popisné</documentation>
				</annotation>
			</element>
			<element name="district" type="string" minOccurs="0"
				maxOccurs="1">

			</element>
			<element name="locality" type="string" minOccurs="0"
				maxOccurs="1">
			</element>
			<element name="region" type="string" minOccurs="0" maxOccurs="1"></element>
			<element name="trustLevel" type="string" minOccurs="0" maxOccurs="1"></element>
		</sequence>
	</complexType>
</schema>
