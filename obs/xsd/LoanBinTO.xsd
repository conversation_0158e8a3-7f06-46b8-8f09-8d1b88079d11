<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/common/" elementFormDefault="qualified"
  xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
	<include schemaLocation="ContractTO.xsd"/>
	<simpleType name="DeterminationType">
		<annotation>
			<documentation>typ určení úvěrového rámce</documentation>
		</annotation>
		<restriction base="string">
			<enumeration value="PRODUCT_MAXIMUM">
				<annotation>
					<documentation>produktové maximum (pokud není napočte nebo úvěrový rámec je nulový)</documentation>
				</annotation>
			</enumeration>
			<enumeration value="LOAN_AMOUNT">
				<annotation>
					<documentation>výše ú<PERSON> (pokud je napočtený úvěrový rámec menší jak aktulní čerpané úvěry, pak se vrací celková výše úvěrů)</documentation>
				</annotation>
			</enumeration>
			<enumeration value="COUNTED_LOANBIN">
				<annotation>
					<documentation>napočtený úvěr rámec</documentation>
				</annotation>
			</enumeration>
		</restriction>
	</simpleType>

	<simpleType name="ReasonOfferType">
		<annotation>
			<documentation>důvod nabídky</documentation>
		</annotation>
		<restriction base="string">
			<enumeration value="NULL">
				<annotation>
					<documentation>nulový úvěrový rámec</documentation>
				</annotation>
			</enumeration>
			<enumeration value="COUNTED">
				<annotation>
					<documentation>úvěrový rámec napočten </documentation>
				</annotation>
			</enumeration>
			<enumeration value="NOT_COUNTED">
				<annotation>
					<documentation>úvěrový rámec nenapočten</documentation>
				</annotation>
			</enumeration>
			<enumeration value="NOT_DISPLAY_IB">
				<annotation>
					<documentation>nezobrazovat v IB</documentation>
				</annotation>
			</enumeration>
		</restriction>
	</simpleType>

    <complexType name="LoanBinCommonTO">
      <sequence>
      	<element name="riskGrade" type="string" maxOccurs="1" minOccurs="0">
      		<annotation>
      			<documentation>
      				riskGrade vypočítaný v Blaze (z LAP vektoru) -
      				jednopísmenná hodnota
      			</documentation>
      		</annotation>
      	</element>
      	<element name="minCredit" type="decimal" maxOccurs="1" minOccurs="0">
      		<annotation>
      			<documentation>Minimální výše úvěru</documentation>
      		</annotation>
      	</element>
      	<element name="maxCredit" type="decimal" maxOccurs="1" minOccurs="0">
      		<annotation>
      			<documentation>Maximální výše úvěru</documentation>
      		</annotation>
      	</element>
      	<element name="maximalInstalment" type="decimal" maxOccurs="1" minOccurs="0">
      		<annotation>
      			<documentation>
      				Maximální výše splátky úvěru
      			</documentation>
      		</annotation>
      	</element>
      	<element name="minRepaymentPeriod" type="int" maxOccurs="1" minOccurs="0">
      		<annotation>
      			<documentation>
      				Minimální doba splácení (počet jednotek - měsíců)
      			</documentation>
      		</annotation>
      	</element>
      	<element name="maxRepaymentPeriod" type="int" maxOccurs="1" minOccurs="0">
      		<annotation>
      			<documentation>
      				Maximální doba splácení (počet jednotek - měsíců)
      			</documentation>
      		</annotation>
      	</element>
      	<element name="requireAppData" type="boolean" maxOccurs="1" minOccurs="1">
      		<annotation>
      			<documentation>
      				true - Požadovat aplikační data (jsou požadována
      				osobní data)
      			</documentation>
      		</annotation>
      	</element>
      	<element name="rejectReason" type="string" maxOccurs="1" minOccurs="0">
                        <annotation>
                          <documentation>důvod zamítnutí</documentation>
                        </annotation>
            </element>
            <element name="rejectReasonClient" type="string" maxOccurs="1" minOccurs="0">
                        <annotation>
                          <documentation>důvod zamítnutí klientem</documentation>
                        </annotation>
            </element>
            <element name="koCodes" type="string" maxOccurs="1" minOccurs="0">
              <annotation>
                <documentation>kódy zamítnutí oddělené středníkem</documentation>
              </annotation>
        </element>
      <element name="loanBinAmountRefin" type="decimal" maxOccurs="1" minOccurs="0">
              <annotation>
                  <documentation>globální limit pro refinancování. V WS-IB mappingu odpovida globalLimitRefinance.</documentation>
              </annotation>
          </element>
          <element name="maximalInstalmentRefin" type="decimal" maxOccurs="1" minOccurs="0">
              <annotation>
                  <documentation>výše splátky pro refinancování. V WS-IB mappingu odpovida annuityRefinance.</documentation>
              </annotation>
          </element>
          <element name="guaranteedLoanBinRefin" type="decimal" maxOccurs="1" minOccurs="0">
              <annotation>
                  <documentation>Výše sníženého garantovaného LoanBinu pro Refinancování. V WS-IB mappingu odpovida lowerGuaranteeRefinance.</documentation>
              </annotation>
          </element>
          <element name="reserveAmountRefin" type="decimal" maxOccurs="1" minOccurs="0">
              <annotation>
                  <documentation>výše reservy (polštáře) refinancovaného/konsolidovaného úvěru v AB. V WS-IB mappingu odpovida reserveAmount.</documentation>
              </annotation>
          </element>
      </sequence>
    </complexType>

    <complexType name="BasicLoanBinTO" abstract="true">
        <annotation>
        	<documentation>disponibilní úvěrový rámec - společná část pro všechny LB</documentation>
        </annotation>
        <sequence>
           <element name="rejectReason" type="string" maxOccurs="1" minOccurs="0">
               <annotation>
                   <documentation>Důvod zamítntutí/vynulování LB</documentation>
               </annotation>
           </element>
           <element name="rejectReasonClient" type="string"  maxOccurs="1" minOccurs="0">
               <annotation>
                   <documentation>Důvod zamítntutí/vynulování LB  pro klienta</documentation>
               </annotation>
           </element>
           <element name="koCodes" type="string" maxOccurs="1" minOccurs="0">
               <annotation>
                   <documentation>KOCodes - seznam KO kódů oddělených středníky</documentation>
               </annotation>
           </element>
           <element name="typeOfCalculation" type="tns:CountingType" maxOccurs="1" minOccurs="0">
               <annotation>
                   <documentation>typ nápočtu (při zakládání pro existujícího klienta IB posílá typ Scoring)</documentation>
               </annotation>
           </element>
           <element name="calculationCategory" type="tns:CalculationCategory" minOccurs="0" maxOccurs="1">
               <annotation>
                   <documentation>Kategorie výpočtu loan bin</documentation>
               </annotation>
           </element>
           </sequence>
    </complexType>

    <complexType name="AMSRiskLoanBinTO">
        <complexContent>
            <extension base="tns:BasicLoanBinTO">
                <sequence>
                     <element name="calculationType" type="tns:CountingType" minOccurs="1" maxOccurs="1"/>
                     <element name="calculationDate" type="dateTime" minOccurs="1" maxOccurs="1"/>
                     <element name="validTo" type="dateTime" minOccurs="1" maxOccurs="1"/>
                     <element name="priority" type="int" minOccurs="1" maxOccurs="1"/>
                     <element name="consAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="loanAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="maximalCreditExposureRisk" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="dispConsAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="dispLoanAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="guaranteedAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="lowerGuaranteedConsAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="lowerGuaranteedLoanAmount" minOccurs="0" maxOccurs="1" type="decimal"/>
                     <element name="globalLimitFastTrack" minOccurs="0" maxOccurs="1" type="decimal"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AvailableLoanBinTO">
        <annotation>
            <documentation>disponibilní úvěrový rámec</documentation>
        </annotation>
        <complexContent>
            <extension base="tns:BasicLoanBinTO">
                <sequence>
                    <element name="riskGrade" type="string" maxOccurs="1" minOccurs="0">
                        <annotation>
                            <documentation>
                                Stupeň rizikovosti (Riskgrade) - z úvěrového rámce, pokud není úvěrový rámec, pak NULL
                            </documentation>
                        </annotation>
                    </element>
                    <element name="requireAppData" type="boolean" maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>
                                Požadovat aplikační data - z úvěrového rámce, pokud není úvěrový rámec, pak plnit
                                hodnotou "Požadovat aplikační data"
                            </documentation>
                        </annotation>
                    </element>
                    <element name="availableLoanBins" type="Q1:AvailableLoanBinElementsTO" minOccurs="1" maxOccurs="unbounded">
                        <annotation>
                            <documentation>kolekce dostupnych uverovych ramcu podle produktovych typu / UVER nebo
                                KONSOLIDACE
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AvailableLoanBinElementsTO">
        <complexContent>
            <extension base="tns:BasicLoanBinElements">
                <sequence>
                    <element name="productType" type="Q1:ProductType" maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>Typ úvěrového produktu (LOAN/CONSOLIDATION</documentation>
                        </annotation>
                    </element>
                    <element name="minCredit" type="decimal" maxOccurs="1" minOccurs="0">
                        <annotation>
                            <documentation>Minimální výše úvěru - z úvěrového rámce, pokud není úvěrový rámec, pak NULL</documentation>
                        </annotation>
                    </element>
                    <element name="maxCredit" type="decimal" maxOccurs="1" minOccurs="0">
                        <annotation>
                            <documentation>Maximální výše úvěru - z úvěrového rámce, pokud není úvěrový rámec, pak NULL</documentation>
                        </annotation>
                    </element>
                    <element name="maximalInstalment" type="decimal" maxOccurs="1" minOccurs="0">
                        <annotation>
                            <documentation>
                                Maximální výše splátky - z úvěrového rámce, pokud není úvěrový rámec, pak NULL
                            </documentation>
                        </annotation>
                    </element>
                    <element name="minRepaymentPeriod" type="int" maxOccurs="1"  minOccurs="0">
                        <annotation>
                            <documentation>
                                Minimální doba splácení úvěru - z úvěrového rámce, pokud není úvěrový rámec, pak NULL
                            </documentation>
                        </annotation>
                    </element>
                    <element name="maxRepaymentPeriod" type="int" maxOccurs="1"  minOccurs="0">
                        <annotation>
                            <documentation>
                                Maximální doba splácení úvěru - z úvěrového rámce, pokud není úvěrový rámec, pak NULL
                            </documentation>
                        </annotation>
                    </element>
                    <element name="dispoLoanBinAmount" type="decimal"  maxOccurs="1" minOccurs="0">
                        <annotation>
                            <documentation>
                                Disponibilní výše úvěrového rámce pro daný typ produktu,  pokud není úvěrový rámec, pak NULL
                            </documentation>
                        </annotation>
                    </element>
                    <element name="dispoProductMaxAmount" type="decimal"  maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>
                                Disponibilní výše produktového maxima pro daný typ produktu
                            </documentation>
                        </annotation>
                    </element>
                    <element name="uncompletedAmount" type="decimal"  maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>
                                Suma úvěrů pro daný typ produktu ve stavu Demo + Suma úvěrů pro daný typ produktu ve stavu Čeká na načerpání
                            </documentation>
                        </annotation>
                    </element>
                    <element name="completedUnpaidAmount" type="decimal"  maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>
                                Suma nesplacených jistin úvěrů pro daný typ produktu (načerpaných), pokud nejsou aktivní úvěry pak = 0
                            </documentation>
                        </annotation>
                    </element>
                    <element name="displayLoanBin" type="boolean"  maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>
                                Příznak zda v IB zobrazit nabídku dispo úvěrového rámce
                            </documentation>
                        </annotation>
                    </element>
                    <element name="displayProductMax" type="boolean"  maxOccurs="1" minOccurs="1">
                        <annotation>
                            <documentation>
                                Příznak zda v IB zobrazit nabídku dispo produktového maxima
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="BasicLoanBinElements" abstract="true">
        <sequence>
            <element name="loanBinAmount" type="decimal" maxOccurs="1" minOccurs="0">
                <annotation>
                    <documentation>
                        Výše úvěrového rámce pro daný typ produktu, pokud není úvěrový rámec, pak NULL
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="NewLoanBinForExistingTO">
    	<complexContent>
    		<extension base="tns:LoanBinCommonTO">
    			<sequence>
    				<element name="loanBinAmount" type="decimal" maxOccurs="1" minOccurs="1">
    					<annotation>
    						<documentation>
    							výše napočteného rámce ze Scóringu
    						</documentation>
    					</annotation>
    				</element>
    				<element name="countingType" type="tns:CountingType" maxOccurs="1" minOccurs="1">
    					<annotation>
    						<documentation>
    							typ nápočtu (při zakládání pro
    							existujícího klienta IB posílá typ
    							Scoring)
    						</documentation>
    					</annotation>
    				</element>
    				<element name="scoringTime" type="dateTime" maxOccurs="1" minOccurs="1">
    					<annotation>
    						<documentation>
    							datum a čas kdy proběhl scoring v
    							systému Blaze
    						</documentation>
    					</annotation>
    				</element>
    				<element name="expirationScoring" type="dateTime" maxOccurs="1" minOccurs="1">
    					<annotation>
    						<documentation>
    							vypršení platnosti scoringu
    						</documentation>
    					</annotation>
    				</element>
    				<element name="guaranteedLoanBin" type="decimal" maxOccurs="1" minOccurs="1">
    					<annotation>
    						<documentation>Garantovaný LB </documentation>
    					</annotation></element>
    			  <element name="maxInvolvment" type="decimal" maxOccurs="1" minOccurs="0">
              <annotation>
                <documentation>Maximalni uverova angazovanost</documentation>
              </annotation>
            </element>
    				<element name="globalLimitFastTrack" type="decimal" maxOccurs="1" minOccurs="0">
    					<annotation>
    						<documentation>Global limit nápočtený metodou fast track </documentation>
    					</annotation>
            </element>
    			</sequence>
    		</extension>
    	</complexContent>
    </complexType>
    <complexType name="VisibilityEntity">
        <sequence>
            <element name="contractId" type="long" minOccurs="1" maxOccurs="1"/>
            <element name="displayForLoan" type="boolean" minOccurs="0" maxOccurs="1"/>
            <element name="displayForConsolidation" type="boolean" minOccurs="0" maxOccurs="1"/>
        </sequence>
    </complexType>

    <complexType name="NotProcessedId">
        <sequence>
            <element name="contractId" type="long" minOccurs="1" maxOccurs="1"/>
        </sequence>
    </complexType>

    <simpleType name="CountingType">
        <annotation>
        	<documentation>typ nápočtu</documentation>
        </annotation>
        <restriction base="string">
    		<enumeration value="MANUAL">
    			<annotation>
    				<documentation>manuální</documentation>
    			</annotation></enumeration>
    		<enumeration value="SCORING">
    			<annotation>
    				<documentation>scoring</documentation>
    			</annotation></enumeration>
    		<enumeration value="DAILY">
    			<annotation>
    				<documentation>denní</documentation>
    			</annotation></enumeration>
    		<enumeration value="MONTHLY">
    			<annotation>
    				<documentation>měsíční</documentation>
    			</annotation></enumeration>
    		<enumeration value="SHORT_MONTHLY">
    			<annotation>
    				<documentation>měsíční zkrácený?</documentation>
    			</annotation></enumeration>
    	</restriction>
    </simpleType>

    <simpleType name="CalculationCategory">
        <annotation>
            <documentation>kategorie typu nápočtu</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="TRN_MEDIUM_PROBABILITY">
                <annotation>
                    <documentation>měsíční nabídkový pro klienty bez úvěru (z transakcí)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="TRN_HIGH_PROBABILITY">
                <annotation>
                    <documentation>měsíční předschválený pro klienty bez úvěru (z transakcí)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AD_HIGH_PROBABILITY">
                <annotation>
                    <documentation>měsíční předschválený pro klienty s úvěrem (z aplikačních dat)</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AD_MEDIUM_PROBABILITY">
                <annotation>
                    <documentation>měsíční nabídkový pro klienty s úvěrem (z aplikačních dat)</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>
</schema>
