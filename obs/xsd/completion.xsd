<?xml version="1.0" encoding="UTF-8"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/completion"
    xmlns:com="http://arbes.com/ib/core/ppf/ws/common/"
    targetNamespace="http://airbank.cz/obs/ws/completion">

    <import schemaLocation="../xsd/ContractTO.xsd" namespace="http://arbes.com/ib/core/ppf/ws/common/"/>

    <simpleType name="Status">
        <restriction base="string">
            <enumeration value="INPROGRESS">
                <annotation>
                    <documentation>ready for completion</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SUSPENDED">
                <annotation>
                    <documentation>completion is paused for some reason</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCELLED">
                <annotation>
                    <documentation>completion is cancelled</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REJECTED">
                <annotation>
                    <documentation>completion is rejected</documentation>
                </annotation>
            </enumeration>
            <enumeration value="COMPLETED">
                <annotation>
                    <documentation>is complete</documentation>
                </annotation>
            </enumeration>
            <enumeration value="MANUAL_VERIFICATION">
                <annotation>
                    <documentation>manual completion</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <simpleType name="AppendixStatus">
        <restriction base="string">
            <enumeration value="TOSIGN"/>
            <enumeration value="TOINFORM"/>
            <enumeration value="CANCELEDBYBANK"/>
            <enumeration value="CANCELEDBYCLIENT"/>
            <enumeration value="TERMINATED"/>
            <enumeration value="AUTHORIZED"/>
            <enumeration value="SENT"/>
        </restriction>
    </simpleType>

    <complexType name="Appendix">
        <sequence>
            <element name="number" type="long" />
            <element name="status" type="tns:AppendixStatus" />
        </sequence>
    </complexType>

    <simpleType name="ManualCheckResult">
        <restriction base="string">
            <enumeration value="BACKOFFICECHECK"/>
            <enumeration value="NODDDOC"/>
            <enumeration value="NODDSIGN"/>
            <enumeration value="NOIDCARD"/>
            <enumeration value="NOREMIDENT"/>
            <enumeration value="INCOMPLIDCARD"/>
            <enumeration value="INCOMPLREMIDENT"/>
            <enumeration value="OTHEROWNER"/>
            <enumeration value="MANUALPAIRING"/>
            <enumeration value="NOINCOMEDOC"/>
            <enumeration value="NOINCOMEDOCVBU"/>
            <enumeration value="NOPERMRESIDENCE"/>
            <enumeration value="NOTLEGIDCARD"/>
            <enumeration value="NOTLEGREMIDENT"/>
            <enumeration value="NOTRANSFERDOC"/>
            <enumeration value="NOSIGN"/>
            <enumeration value="NODOCBLUE"/>
            <enumeration value="NODISPONENTFORM"/>
            <enumeration value="NOWORKEXTEND"/>
            <enumeration value="SUBJECTCHECK"/>
            <enumeration value="TECHFAULT"/>
            <enumeration value="DOCERRMEDIASERV"/>
            <enumeration value="NOMANDATDOCS"/>
            <enumeration value="VERIFICATION"/>
            <enumeration value="RISKAPPROVAL"/>
            <enumeration value="NOINCOMEDOCPOVP"/>
            <enumeration value="OTHER"/>
        </restriction>
    </simpleType>

    <complexType name="ManualCheck">
        <sequence>
            <element name="result" type="tns:ManualCheckResult" />
            <element name="description" type="string" minOccurs="0" />
        </sequence>
    </complexType>

    <complexType name="Signature">
        <sequence>
            <element name="date" type="dateTime" >
                <annotation>
                    <documentation>signature date</documentation>
                </annotation>
            </element>
            <element name="channel" type="com:SignChannelType" />
            <element name="place" type="string" />
            <element name="posId" type="string" minOccurs="0">
                <annotation>
                    <documentation>branch id</documentation>
                </annotation>
            </element>
            <element name="signatureType" minOccurs="0" >
                <complexType>
                    <sequence>
                        <element name="contractSignType" type="com:ContractSignType"  minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="createdBy" type="string" minOccurs="0">
                <annotation>
                    <documentation>operator employee number</documentation>
                </annotation>
            </element>
            <element name="internalCode" type="string" minOccurs="0"/>
        </sequence>
    </complexType>

    <complexType name="Completion">
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>completion id</documentation>
                </annotation>
            </element>
            <element name="parentId" type="long" minOccurs="0">
                <annotation>
                    <documentation>id of completion which is parent of the current</documentation>
                </annotation>
            </element>
            <element name="type" type="com:ContractType">
                <annotation>
                    <documentation>completion type</documentation>
                </annotation>
            </element>
            <element name="status" type="tns:Status" />
            <element name="applicationId" type="long" minOccurs="0" />
            <element name="createdAt" type="dateTime" />
            <element name="completedAt" type="dateTime" minOccurs="0" />
            <element name="completedBy" type="string" minOccurs="0">
                <annotation>
                    <documentation>operator employee number</documentation>
                </annotation>
            </element>
            <element name="signature" type="tns:Signature" minOccurs="0" />
            <element name="appendix" type="tns:Appendix" minOccurs="0" />
            <element name="suspendedAt" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>date of suspension</documentation>
                </annotation>
            </element>
            <element name="manualCheck" type="tns:ManualCheck" minOccurs="0" />
            <element name="envelopeId" type="long" minOccurs="0">
                <annotation>
                    <documentation>
                        AMS envelope identifier
                    </documentation>
                </annotation>
            </element>
            <element name="channelProposed" type="string"/>
            <element name="generalContractId" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID of client contract</documentation>
                </annotation>
            </element>
            <element name="contractSubmitWay" type="com:PersDocDistChannelSentType" minOccurs="0">
                <annotation>
                    <documentation>Channel through the document was sent to bank</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
</schema>