<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    xmlns:tns="http://airbank.cz/obs/ws/investmentAccounting"
    targetNamespace="http://airbank.cz/obs/ws/investmentAccounting">

    <complexType name="postingEntryAssRee">
        <sequence>
            <element name="assetMarketPrice" type="tns:amountItem"/>                             <!-- tržní cena držených cenných papírů -->
            <element name="accruedInterest" type="tns:amountItem"
                     minOccurs="0"/>                                                             <!-- reálně naběhlý úrok na držené cenný papíry včetně promítnutí nákupu po datu exkuponu, takový cenný papír bude mít od data nákupu do data výplaty kuponu v accruedInterest nulovou hodnotu -->
       </sequence>
    </complexType>

    <complexType name="updateAssetValue">
        <sequence>
            <element name="references"
                     type="tns:referencesUAV"/>                                             <!-- reference k operaci, popis uveden u typu "references" -->
            <element name="cuid" type="long"/>                                           <!-- unikátní identifikátor klienta, kterého se operace týká-->
            <element name="assetIdentification" type="tns:assetIdentificationFull"/>     <!-- identifikace přeceňovaného cp, bližsí popis uveden u typu "assetIdentificationFull" -->
            <element name="postingEntry" type="tns:postingEntryAssRee"/>                                            <!-- položky k zaúčtování -->
        </sequence>
    </complexType>


    <complexType name="reversedUpdateAssetValue">
        <sequence>
            <element name="references"
                     type="tns:referencesUAV"/>                                         <!-- reference k operaci storna přecenění, refrence rozšířena o refrenci na stornovanou položku-->
        </sequence>
    </complexType>


    <complexType name="receiveCoupon">
        <sequence>
            <element name="references" type="tns:references"/>                             <!-- reference k operaci, popis uveden u typu "references" -->
            <element name="cuid" type="long"/>                                     <!-- unikátní identifikátor klienta, kterého se operace týká-->
            <element name="assetIdentification" type="tns:assetIdentificationFull"/>       <!-- identifikace cenného papíru, kterého se operace týká, popis uveden u typu "assetIdentificationFull" -->
            <element name="postingEntryRC" type="tns:postingEntryRC"/>
        </sequence>
    </complexType>


    <complexType name="postingEntryReBTA">
        <sequence>
            <element name="totalBuyAmount" type="tns:amountItem"/>                              <!--   celková cena nákupu = assetMarketPrice + accruedInterest, je pouze součtem dílčích elementů, tj. není nutné tuto hodnotu mít v requestu vyplněnou, součet můžeme provést na straně OBS, pokud se ukáže že ho k něčemu potřebujeme, resp. z dílčích částek součet provést dokážeme, ale z celku detail už nevymyslíme -->
            <element name="assetMarketPrice" type="tns:amountItem"/>                            <!--   tržní cena nakoupených CP   -->
            <element name="accruedInterest" type="tns:amountItem" minOccurs="0"/>               <!--   celkové auv v ceně nákupu dluhopisu, pokud je cp nakupován po datu ex-kuponu tak 0 -->
            <element name="fees" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>           <!--   vyčíslení dílčích poplatků vztahujících se k nákupu -->
        </sequence>
    </complexType>

    <complexType name="postingEntryPCanBR">
        <sequence>
          <element name="returnedBuyAmount" type="tns:amountItem"/>                      <!--   vrácená část rezervované hodnoty na nákup -->
          <element name="fees" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>      <!--   vyčíslení vracené části poplatků vztahujících se k nákupu -->
        </sequence>
    </complexType>

    <complexType name="postingEntryChrSeB">
        <sequence>
            <element name="assetMarketPrice" type="tns:amountItem">
                <annotation>
                    <documentation>
                        tržní cena nakoupených CP
                    </documentation>
                </annotation>
            </element>
            <element name="accruedInterest" type="tns:amountItem" minOccurs="0">
                <annotation>
                    <documentation>
                        celkové auv v ceně nákupu dluhopisu, pokud je cp nakupován po datu ex-kuponu tak 0
                        vyčíslení dílčích poplatků vztahujících se k nákupu
                    </documentation>
                </annotation>
            </element>
            <element name="totalBAmount" type="tns:amountItem" minOccurs="0">
                <annotation>
                    <documentation>
                        celková částka k vyplacení do Interactive Brokers
                    </documentation>
                </annotation>
            </element>
            <element name="netFeeProfit" type="tns:amountItem" minOccurs="0">
                <annotation>
                    <documentation>
                        čistý výnos z poplatků
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="postingEntryCHRCBP">
        <sequence>
            <element name="tradeAmount" type="tns:amountItem"/>                          <!-- nákupní cena CP x počet nakoupených dluhopisů + AUV -->
            <element name="fees" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>    <!-- vyčíslení dílčích poplatků vztahujících se k nákupu -->
            <element name="filled" type="boolean"/>                                      <!-- TRUE - obchod byl dokončen, uvolníme blokaci; FALSE - jedná se o částečné zpracování obchodu, ponížíme blokaci -->
        </sequence>
    </complexType>


    <complexType name="postingEntrySTLCSP">
        <sequence>
            <element name="settlementAmount" type="tns:amountItem"/>                     <!-- částka vypořádání mezi bankou a protistranou obchodu blíže určená pomocí operationTypeSpecification -->
            <element name="fees" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>    <!--   vyčíslení dílčích poplatků vztahujících se k nákupu  -->
        </sequence>
    </complexType>

    <complexType name="postingEntryStlFee">
        <sequence>
                <element name="feeSettlement" type="tns:fee"/>                                  <!-- poplatek k vypořádání -->
        </sequence>
    </complexType>

    <complexType name="postingEntryRC">
        <sequence>
            <element name="coupon" type="tns:amountItem"/>                              <!-- čistá (již zdaněná) hodnota kuponu k výplatě na účet klienta -->
            <element name="accruedInterest" type="tns:amountItem"/>                     <!-- hrubá hodnota kuponu tj. celoková AUV odpovídající vyplacenému kuponu k odůčtování z podrozvahy -->
            <element name="purchaseAccruedInt" type="tns:amountItem"/>             <!--  AUV    -->
        </sequence>
    </complexType>

    <complexType name="postingEntryCHRST">
        <sequence>
            <element name="feePayment" type="tns:fee"/>                                   <!-- poplatek, pokud se jedná o poplatek vč. DPH, tak částka včetně DPH" -->
            <element name="vat" type="tns:amountItem" minOccurs="0"/>                                    <!-- DPH - DPH z poplatku -->
        </sequence>
    </complexType>

    <complexType name="postingEntryTRDSLM">
        <sequence>
            <element name="totalIncomeAmount" type="tns:amountItem"/>                          <!-- potvrzená celková částka za prodané cp nebo získaná z titulu splatnosti jistiny cenného papíru -->
        </sequence>
    </complexType>

    <complexType name="postingEntryCHRSLM">
        <sequence>
            <element name="netProfit" type="tns:amountItem"/>                                    <!-- čistá částka za prodej nebo jistina cp při splatnosti k připsání na účet klienta -->
            <element name="fees" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>                                               <!-- vyčíslení poplatků spojených s prodejem -->
            <element name="assetMarketPrice" type="tns:amountItem"/>                             <!-- tržní cena prodaných cp nebo tržní cena cp k datu splatnosti, o této hodnotě je účtováno na podrozvaze -->
            <element name="accruedInterest" type="tns:amountItem" minOccurs="0"/>                              <!-- auv na prodané cp, o této hodnotě je účtováno na podrozvaze -->
        </sequence>
    </complexType>


    <complexType name="postingEntryCHRTRN">
        <sequence>
            <element name="assetMarketPrice" type="tns:amountItem"/>                         <!-- tržní cena nabytých resp. pozbytých cp, o této hodnotě je účtováno na podrozvaze -->
            <element name="accruedInterest" type="tns:amountItem" minOccurs="0"/>                          <!-- naběhlý úrok na nabytých resp. pozbytých cp -->
            <element name="fees" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>                                           <!-- vyčíslení poplatků sppjených s převodem -->
        </sequence>
    </complexType>


    <complexType name="postingEntryCHRCPL">
        <sequence>
            <element name="coupon" type="tns:amountItem"/>                         <!-- hodnota budoucí kuponové platby vyčíslená k datu ex-kuponu -->
        </sequence>
    </complexType>


    <complexType name="assetReevaluation">                                               <!-- typ pro přecenění cp -->
        <sequence>
            <element name="references"
                     type="tns:references"/>                                             <!-- reference k operaci, popis uveden u typu "references" -->
            <element name="cuid" type="long"/>                                           <!-- unikátní identifikátor klienta, kterého se operace týká-->
            <element name="assetIdentification" type="tns:assetIdentificationISIN"/>     <!-- identifikace přeceňovaného cp, bližsí popis uveden u typu "assetIdentificationISIN" -->
            <element name="postingEntry" type="tns:postingEntryAssRee"/>                                            <!-- položky k zaúčtování -->
        </sequence>
    </complexType>


    <complexType name="fee">                                                                                   <!-- typ pro poplatek -->
        <sequence>
            <element name="feeType"
                     type="string"/>                              <!-- kódové označení typu poplatku, bylo by možná vhodné omezit enumerací, jak se potom ale zachovat v p´řípadě ad-hoc účtování poplatku enumerací neurčenému - bude pro tento případ enumerace obsahovat položku JINYPOPL o jaký poplatek se jedná vyplyne z popisu uvedeného u položky ? -->
            <element name="amount"
                     type="tns:amountItem"/>                               <!-- částka poplatku k zaúčtování, bližší specifikace uvedena u typu amountItem -->
        </sequence>
    </complexType>


    <complexType
            name="fees">                                                                                 <!-- typ pro sdružení více instancí různých poplatků spojených s operací -->
        <sequence>
            <element name="fee" type="tns:fee" minOccurs="0" maxOccurs="unbounded"/>                                <!-- jednotlivé poplatky k zaúčtování -->
        </sequence>
    </complexType>

    <complexType name="assetIdentificationISIN">                                                              <!-- základní identifikace cenného papíru -->
        <sequence>
            <element name="isin" type="string"/>                                                             <!-- International Securities Identification Number -->
        </sequence>
    </complexType>


    <complexType name="assetIdentificationFull">                                                              <!-- identifikace cenného papíru kompletní-->
        <complexContent>
            <extension
                    base="tns:assetIdentificationISIN">                                                           <!-- International Securities Identification Number -->
                <sequence>
                    <element name="isoCurrencyCode" type="string"/>                  <!-- ISO alpha kód měny denominace cenného papíru -->
                    <element name="name" type="string"/>                             <!-- název cenného papíru -->
                    <element name="issuer" type="string"/>                           <!-- emitent cenného papíru -->
                    <element name="instrumentType" type="tns:instrumentType"/>       <!-- typ cenného papíru -->
                    <element name="symbol" type="string"/>                           <!-- zkratka akcie -->
                </sequence>
            </extension>
        </complexContent>
    </complexType>


    <simpleType name="counterPartySettlementType">                                              <!-- typ vypořádání s protistranou -->
        <restriction base="string">
            <enumeration value="buy"/>
            <enumeration value="sell"/>
            <enumeration value="coupon"/>
            <enumeration value="maturity"/>
        </restriction>
    </simpleType>


    <complexType name="references">                                                             <!-- reference k účtovaným položkám -->
        <sequence>
            <element name="orderReference" type="long"
                     minOccurs="0"/>                                <!-- číslo příkazu k nákupu nebo prodeji, reference držící dohromady všechny operace vztahující se k jednomu nákupu, prodeji -->
            <element name="tradeReference" type="long"
                     minOccurs="0"/>                                <!-- reference operace, drží pohromadě všechny položky jedné metody -->
            <element name="cancelIndicator"
                     type="boolean"/>                               <!-- identifikace storna, v případě true hodnoty podnět pro OBS k provedení storna dle refrence operace a dalších identifikátorů v metodě, v případě true hodnoty budou všechny elementy v elementu postingEntry prázdné -->
        </sequence>
    </complexType>


    <complexType name="referencesUAV">                              <!-- rozšířená reference o položku reference na předchozí provedené přecenění ke stornu -->
      <complexContent>
            <extension
                    base="tns:references">
                <sequence>
                    <element name="tradeReferenceForReversedUpdate" type="long" minOccurs="0"/>              <!-- trade reference položky přecenění která má být stornována-->
                    <element name="orderReferenceForReversedUpdate" type="long" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>


     <simpleType name="instrumentType">                             <!-- typ cenného papíru totožnou enumeraci používá např. služba InvestmentOrderWS-->
        <restriction base="string">
            <enumeration value="D"/>                                <!-- dluhopis -->
            <enumeration value="C"/>                                <!-- podílový fond -->
            <enumeration value="A"/>                                <!-- akcie -->
            <enumeration value="E"/>                                <!-- ETL -->
        </restriction>
    </simpleType>


    <simpleType name="transferDirection">                                                       <!-- specifikace směru převodu -->
        <restriction base="string">
            <enumeration value="toClient"/>
            <enumeration value="fromClient"/>
        </restriction>
    </simpleType>

    <simpleType name="maturitySellIdentification">                                              <!-- rozlišení prodeje a splatnosti cp -->
        <restriction base="string">
            <enumeration value="sell"/>
            <enumeration value="maturity"/>
        </restriction>
    </simpleType>


    <complexType name="amountItem">                                                            <!-- typ pro kompletní specifikaci částky k zaúčtování -->
        <sequence>
            <element name="amountInCurrency" type="tns:amountAndCurrency"/>       <!-- částka k zaúčtování-->
            <element name="amountInNationalCurrency" type="tns:amountAndCurrency"/>           <!-- přepočet částky na narodni menu, typicky CZK -->
            <element name="debitAccount" type="tns:accountNumber"/>
            <element name="creditAccount" type="tns:accountNumber"/>
            <element name="valuta" type="date"/>                    <!-- valuta operace dle TOPAS -->
            <element name="infoText" type="string"/>                <!-- textová informace k částce -->
            <element name="variableSymbol" minOccurs="0">
                <simpleType>
                    <restriction base="string">
                        <maxLength value="10"/>
                    </restriction>
                </simpleType>
            </element>
            <element name="operationTypeSpecification" type="tns:updateAssetDirection"/>
        </sequence>
    </complexType>

    <complexType name="amountAndCurrency">
        <sequence>
            <element name="amount" type="decimal"/>                  <!-- částka k zaúčtování-->
            <element name="isoCurrencyCode" type="string"/>          <!-- ISO alpha kód měny ve které je částka uvedena -->
        </sequence>
    </complexType>

    <complexType name="czechNationalAccountNumber">                                          <!-- typ pro definici čísla účtu v českém národním tvaru -->
        <sequence>
            <element name="accountNumber" type="string"/>
            <element name="paymentSystemCode" type="string"/>
        </sequence>
    </complexType>


    <complexType name="internationalAccountNumber">                                          <!-- typ pro deifnici čísla účtu v mezináridním formátu -->
        <sequence>
            <element name="iban" type="string"/>
            <element name="isoCurrencyCode" type="string"/>
        </sequence>
    </complexType>



    <complexType name="accountNumber">                                                       <!-- typ pro definici čísla účtu -->
        <sequence>
            <element name="czechNationalAccountNumber" type="tns:czechNationalAccountNumber" minOccurs="0"/>      <!-- číslo účtu dle definice ČNB pro tuzemské platby v CZK -->
            <element name="internationalAccountNumber" type="tns:internationalAccountNumber" minOccurs="0"/>      <!-- číslo účtu v mezinárodním formátu pro platby v jiných měnách než CZK -->
        </sequence>
    </complexType>

    <simpleType name="investAccResult">
        <restriction base="string">
            <enumeration value="ACCOUNTED">
                <annotation>
                    <documentation>Zaúčtováno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="NOT_ACCOUNTED">
                <annotation>
                    <documentation>Nezaúčtováno</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ERROR">
                <annotation>
                    <documentation>Chyba</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>


    <complexType name="bulkAccResult">
        <sequence>
            <element name="references" type="tns:references"/>
            <element name="result" type="tns:investAccResult"/>
            <element name="errorCode" type="string" minOccurs="0"/>
        </sequence>
    </complexType>


    <simpleType name="updateAssetDirection">                                                       <!-- P = kladne, N = zaporne -->
        <restriction base="string">
            <enumeration value="P"/>
            <enumeration value="N"/>
        </restriction>
    </simpleType>

    <complexType name="blockItem">
        <sequence>
            <element name="blockAmount" type="tns:amountAndCurrency"/>
            <element name="FXRate" type="decimal" minOccurs="0"/>
            <element name="debitAccount" type="tns:accountNumber"/>
            <element name="infoText" type="string"/>
            <element name="variableSymbol" minOccurs="0">
                <simpleType>
                    <restriction base="string">
                        <maxLength value="10"/>
                    </restriction>
                </simpleType>
            </element>
            <element name="operationTypeSpecification" type="tns:updateAssetDirection"/>
        </sequence>
    </complexType>

    <complexType name="dividend">
        <sequence>
            <element name="cuid" type="long"/>
            <element name="references" type="tns:references"/>
            <element name="assetIdentification" type="tns:assetIdentificationFull"/>
            <element name="dividendAmount" type="tns:amountAndCurrency"/>
            <element name="dividendTax" type="tns:amountAndCurrency"/>
            <element name="valuta" type="date"/>
        </sequence>
    </complexType>

    <simpleType name="ibkrTransactionType">
        <restriction base="string">
            <enumeration value="depositMA"/>       <!-- dotace MA z AB -->
            <enumeration value="withdrawMA"/>      <!-- vyvedení prostředků z MA do AB -->
            <enumeration value="interestMA"/>      <!-- připsání úroků z prostředků na MA -->
            <enumeration value="dividendMA"/>      <!-- připsání prostředků na výplatu dividend klientům na MA -->
            <enumeration value="unknownMA"/>       <!-- neidentifikovaná MA transakce (nezapadá do žádné z výše uvedených kategorií pro MA) -->
            <enumeration value="depositPA"/>       <!-- dotace PA z AB -->
            <enumeration value="withdrawPA"/>      <!-- vyvedení prostředků z PA do AB -->
            <enumeration value="interestPA"/>      <!-- připsání úroků na PA -->
            <enumeration value="unknownPA"/>       <!-- neidentifikovaná PA operace (nezapadá do žádné z výše uvedených kategorií pro PA) -->
            <enumeration value="buyFromMA"/>       <!-- úhrada nákupu akcie -->
            <enumeration value="sellToMA"/>        <!-- prostředky za prodanou akcii -->
            <enumeration value="roundingMA"/>      <!-- rekonciliace Master Account -->
            <enumeration value="roundingPA"/>      <!-- rekonciliace Propriet Account -->
        </restriction>
    </simpleType>

</schema>
