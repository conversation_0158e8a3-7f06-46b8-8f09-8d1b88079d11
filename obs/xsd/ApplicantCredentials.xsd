<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
            xmlns="http://www.w3.org/2001/XMLSchema"
            elementFormDefault="qualified" version="1.0"
>

    <xsd:complexType name="AmlRequirement">
        <xsd:sequence>
            <xsd:element name="amlRequirementID" type="xsd:int" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>Pokud je amlRequirementID = null - AML proběhl vpořádku.
                        Pokud se vrátí ID požadavku, potom AML neproběhl vpořádku a generoval se požadavek s tímto ID.
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ApplicantCredentials">
        <xsd:annotation>
            <xsd:documentation>Credentials of applicant which are to be checked
                for AML.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationID" type="xsd:long" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation></xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="applicantName" type="xsd:string" maxOccurs="1" minOccurs="1"/>
            <xsd:element name="applicantSurname" type="xsd:string" maxOccurs="1" minOccurs="1"/>
            <xsd:element name="applicantDateOfBirth" type="xsd:date" minOccurs="0"/>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0"/>
            <xsd:element name="applicantCitizenship" type="xsd:string" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="applicantState" type="xsd:string" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="applicantContactState" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:documentation>
                        korespondencna adresa - stat
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
