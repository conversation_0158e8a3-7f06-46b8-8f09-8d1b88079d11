<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema
    xmlns="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://airbank.cz/obs/ws/OBSPersonWS/"
    elementFormDefault="qualified"
    xmlns:tns="http://airbank.cz/obs/ws/OBSPersonWS/"
    xmlns:lo="http://airbank.cz/obs/ws/loan">

    <import namespace="http://airbank.cz/obs/ws/loan" schemaLocation="LoanTO.xsd"/>

    <element name="findPersonRequest">
        <complexType>
            <choice>
                <element name="documentNumber" type="string">
                    <annotation>
                        <documentation><PERSON><PERSON><PERSON> smlouvy.</documentation>
                    </annotation>
                </element>
                <element name="accountNumber" type="string">
                    <annotation>
                        <documentation><PERSON><PERSON>lo účtu.</documentation>
                    </annotation>
                </element>
                <element name="loanNumber" type="string">
                    <annotation>
                        <documentation><PERSON><PERSON><PERSON>.</documentation>
                    </annotation>
                </element>
                <element name="username" type="string">
                    <annotation>
                        <documentation>Přihlašovací jméno do IB.</documentation>
                    </annotation>
                </element>
            </choice>
        </complexType>
    </element>

    <element name="findPersonResponse">
        <complexType>
            <sequence>
                <element name="cuid" type="long" minOccurs="0" maxOccurs="unbounded">
                    <annotation>
                        <documentation>The unique ID of the customer.</documentation>
                    </annotation>
                </element>
            </sequence>
        </complexType>
    </element>

    <simpleType name="SexType">
        <restriction base="string">
            <enumeration value="M" />
            <enumeration value="F" />
        </restriction>
    </simpleType>

    <simpleType name="LoanClientType">
        <restriction base="string">
            <enumeration value="WALKIN" />
            <enumeration value="FIRSTLOANEE" />
            <enumeration value="LOANEE" />
            <enumeration value="EX_LOANEE" />
        </restriction>
    </simpleType>

    <element name="anonymizePersonsRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long" maxOccurs="unbounded" />
            </sequence>
        </complexType>
    </element>

    <element name="anonymizePersonsResponse">
        <complexType />
    </element>

    <element name="getLoanClientTypeRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long" />
                <element name="clientType" type="lo:ClientType" />
            </sequence>
        </complexType>
    </element>

    <element name="getLoanClientTypeResponse">
        <complexType>
            <sequence>
                <element name="loanClientType" type="tns:LoanClientType" />
            </sequence>
        </complexType>
    </element>

    <element name="terminateDeceasedPersonsSettingsRequest">
        <complexType>
            <sequence>
                <element name="cuid" type="long" />
            </sequence>
        </complexType>
    </element>

    <element name="terminateDeceasedPersonsSettingsResponse">
        <complexType />
    </element>

</schema>
