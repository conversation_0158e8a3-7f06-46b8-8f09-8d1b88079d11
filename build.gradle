plugins {
    id "com.commercehub.gradle.plugin.avro" version "0.9.1"
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}
apply plugin: 'io.spring.dependency-management'
group = 'com.glowbyte.hc'
def customVersion = System.getenv("CI_COMMIT_SHORT_SHA") ?: "unknown_commit"
def customVersionTime = System.getenv("CUSTOM_TIME") ?: "unknown_build_time"
version = "${customVersionTime}-${customVersion}"
sourceCompatibility = '1.8'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
    maven {
        url "https://maven.repository.redhat.com/earlyaccess/all/"
    }
    maven {
        url 'https://jitpack.io'
    }
}


//Для решения проблемы с авро классами
//https://youtrack.jetbrains.com/issue/IDEA-152581
//https://stackoverflow.com/questions/5170620/unable-to-use-intellij-with-a-generated-sources-folder
sourceSets {
    main {
        java {
            srcDir "${buildDir.absolutePath}/generated-main-avro-java"
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.rabbitmq:amqp-client'
    implementation 'org.apache.httpcomponents:httpclient'
    implementation 'org.springframework:spring-web'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-web-services'
    implementation "org.springframework.boot:spring-boot-starter-amqp"

    implementation 'org.springframework:spring-jms'
    implementation 'org.springframework.kafka:spring-kafka'

    implementation 'org.apache.avro:avro:1.8.2'
    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter:2.1.1'

    implementation group: 'org.json', name: 'json', version: '20090211'

    /*compile group: 'javax.jms', name: 'javax.jms-api', version: '2.0.1'*/
    implementation 'javax.jms:javax.jms-api:2.0'
    implementation 'org.springframework.boot:spring-boot-starter-web' // REST + RestTemplate

    implementation 'org.projectlombok:lombok'
    implementation 'joda-time:joda-time:2.12.1'
    implementation 'org.jetbrains:annotations:20.1.0'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    implementation 'io.confluent:kafka-avro-serializer:5.3.0'


    implementation 'com.facebook.business.sdk:facebook-java-business-sdk:22.0.0'

    implementation 'com.google.api-ads:google-ads:36.1.0'
    implementation 'com.google.guava:guava:32.0.0-jre'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.35.0'
    implementation 'com.google.api-client:google-api-client:1.33.0'

    implementation 'io.github.jasonchentt:tiktok-business-api-sdk-official:1.0.5'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    implementation 'io.github.resilience4j:resilience4j-spring-boot2:1.7.1'

    testImplementation 'org.springframework.kafka:spring-kafka-test'
    testImplementation "org.springframework.amqp:spring-rabbit-test"
    testImplementation ('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }

    testImplementation group: 'com.h2database', name: 'h2', version: '1.3.148'
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
}

test {
    useJUnitPlatform()
}

avro {
    stringType = 'CharSequence'  // Use CharSequence instead of String
}

apply plugin: 'java'
task myJavadoc(type: Javadoc) {
    source = 'src/main/java/com'
    title = 'Kafka Adapter'
    options.overview = "overview.html"
    destinationDir = file("javadoc/")
    classpath = project.sourceSets.main.runtimeClasspath
}

