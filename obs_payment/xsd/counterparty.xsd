<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:tns="http://airbank.cz/obs/ws/counterparty"
        targetNamespace="http://airbank.cz/obs/ws/counterparty">

    <xsd:simpleType name="CounterpartyType">
        <xsd:annotation>
            <xsd:documentation>
                Counter party type.
                BN - BISNODE
                CU - CUSTOM
                BO - BANK OWN
                BE - BANK EXTERNAL
                BI - BANK INTERNAL
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="BN"/>
            <xsd:enumeration value="CU"/>
            <xsd:enumeration value="BO"/>
            <xsd:enumeration value="BE"/>
            <xsd:enumeration value="BI"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="AbstractCounterparty" abstract="true">
        <xsd:sequence>
            <xsd:element name="id" type="xsd:long" minOccurs="0"/>
            <xsd:element name="authorId" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Customer identification - CUID.
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contractNumber" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Contract business identification.
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="visible" type="xsd:boolean">
                <xsd:annotation>
                    <xsd:documentation>
                        Is this counterparty visible for client?
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="bankAccount" type="tns:CounterpartyAccount" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="brand" type="tns:Brand" minOccurs="0"/>
            <xsd:element name="reuseExisting" type="xsd:boolean" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="InternalCounterparty" abstract="true">
        <xsd:complexContent>
            <xsd:extension base="tns:AbstractCounterparty">
                <xsd:sequence>
                    <xsd:element name="name" type="xsd:string"/>
                    <xsd:element name="regNumber" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="taxNumber" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="email" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="web" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="BisnodeCounterparty">
        <xsd:annotation>
            <xsd:documentation>
                All information about this counterparty are stored
                in bisnode (RCM) system.
                This is only reference to external counterparty.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="tns:AbstractCounterparty">
                <xsd:sequence>
                    <xsd:element name="externalId" type="xsd:long">
                        <xsd:annotation>
                            <xsd:documentation>Reference to RCM.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="CustomCounterparty">
        <xsd:annotation>
            <xsd:documentation>
                This counterparty is created by client (in DOOR).
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="tns:InternalCounterparty"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="ExternalBankCounterparty">
        <xsd:complexContent>
            <xsd:extension base="tns:InternalCounterparty"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="InternalBankCounterparty">
        <xsd:complexContent>
            <xsd:extension base="tns:AbstractCounterparty">
                <xsd:sequence>
                    <xsd:element name="name" type="xsd:string"/>
                    <xsd:element name="counterpartyLabel" type="xsd:string"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="OwnBankCounterparty">
        <xsd:annotation>
            <xsd:documentation>
                Account and its counterparty are owned by the same owner.
                This counterparty is create by process in CLOVER.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="tns:InternalCounterparty"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="CounterpartyAccount" abstract="true">
        <xsd:sequence>
            <xsd:element name="accountNumber" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="DomesticAccount">
        <xsd:complexContent>
            <xsd:extension base="tns:CounterpartyAccount">
                <xsd:sequence>
                    <xsd:element name="prefix" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                Account number prefix
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="bankCode" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>
                                Local bank code.
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="IBAN">
        <xsd:complexContent>
            <xsd:extension base="tns:CounterpartyAccount"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="InternalAccount">
        <xsd:annotation>
            <xsd:documentation>Non-modulo internal account of Air Bank</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="tns:CounterpartyAccount"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="ForeignAccount">
        <xsd:complexContent>
            <xsd:extension base="tns:CounterpartyAccount">
                <xsd:sequence>
                    <xsd:element name="BIC" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>
                                BIC / ABA code.
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="CounterpartyRelations">
        <xsd:annotation>
            <xsd:documentation>Relations between counterparty and its identifications.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="idCounterparty" type="xsd:long"/>
            <xsd:choice>
                <xsd:element name="idCounterpartyIdent" type="xsd:long" maxOccurs="unbounded"/>
                <xsd:element name="bankAccount" type="tns:CounterpartyAccount" maxOccurs="unbounded"/>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ListCounterpartiesFilter">
        <xsd:annotation>
           <xsd:documentation>Filter object used in listCounterparties method</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="type" type="tns:CounterpartyType" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        type of the counterparties to be returned
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="accountNumberCustomer" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        customer account number (under the contract number from business context) having counterparties to be searched for
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="visibleOnly" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        visibility (primarily for IB - if TRUE, only visible counterparties are returned)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="accountVerifiedByTransaction" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                            Limits counter party's bank account to those which were confirmed by any transaction.
                            TRUE - if counter party has more than one account, then return its details and only those accounts
                            which were used for any transactions.
                            If counter party has more than one account but none of those accounts was used in any transaction
                            do not return details of such a counter party
                            if counter party has only one account and it was never used in transaction do not return details
                            of such a counter party
                            FALSE (empty) - do not check transactions/bank accounts
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="namePart" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        the whole name or its part to be used in search for counter party. Can be empty
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="withAccounts" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        TRUE - return counterparty if it has at least one account
                        FALSE (empty) - do not check bank accounts
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="usedInLastPeriod" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        TRUE - return only counterparties used in last period (global parameter)
                        FALSE (empty) - return all counterparties (no filter applied)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="maxRecords" type="xsd:int" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Maximal number of returned records
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="sortedAttribute" type="tns:SortedAttribute" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Definition of sort criteria for returned data
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Brand">
        <xsd:sequence>
            <xsd:element name="id" type="xsd:long"/>
            <xsd:element name="name" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Brand name
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="logoFileName" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Link to logo filename
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BrandCounterpartyAssociation">
        <xsd:sequence>
            <xsd:element name="idCounterparty" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>
                        Unique Counterparty identification
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="idBrand" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Unique Brand identification
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="SortedAttribute">
        <xsd:annotation>
            <xsd:documentation>Definition of ordering type for single attribute.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="orderBy" type="tns:OrderBy"/>
            <xsd:element name="orderType" type="tns:OrderType"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="OrderBy">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="FAVORITE"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="OrderType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ASC"/>
            <xsd:enumeration value="DESC"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
