<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://arbes.com/ib/core/ppf/ws/common/"
	xmlns:tns="http://arbes.com/ib/core/ppf/ws/xsd/" xmlns="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" version="1.0"
	xmlns:Q1="http://arbes.com/ib/core/ppf/ws/common/">
	<xsd:complexType name="PaymentReasonTypeTO">
		<xsd:sequence>
			<xsd:element name="name" type="xsd:string" minOccurs="1"
				maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>popis důvodu platby</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="code" type="xsd:string" minOccurs="1"
				maxOccurs="1">
				<xsd:annotation>
					<xsd:documentation>kód důvodu platby
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>			
		</xsd:sequence>
	</xsd:complexType>
</schema>
