<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/asm/ws/application"
                  targetNamespace="http://airbank.cz/asm/ws/application">
    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/asm/ws/application">
            <xsd:include schemaLocation="../xsd/ApplicationWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="FindApplicationsRequest">
        <wsdl:part element="FindApplicationsRequest" name="FindApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="FindApplicationsResponse">
        <wsdl:part element="FindApplicationsResponse" name="FindApplicationsResponse"/>
    </wsdl:message>
    <wsdl:message name="FindApplicationsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FindApplicationsFault"/>
    </wsdl:message>
    <wsdl:message name="SetBranchCodeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SetBranchCodeFault"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationDetailByFilterFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationDetailByFilterFault"/>
    </wsdl:message>

    <wsdl:message name="SetBranchCodeRequest">
        <wsdl:part name="SetBranchCodeRequest" element="SetBranchCodeRequest"/>
    </wsdl:message>
    <wsdl:message name="SetBranchCodeResponse">
        <wsdl:part name="SetBranchCodeResponse" element="SetBranchCodeResponse"/>
    </wsdl:message>

    <wsdl:message name="FindGCApplicationsForCairActivationRequest">
        <wsdl:part name="FindGCApplicationsForCairActivationRequest" element="FindGCApplicationsForCairActivationRequest"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairActivationResponse">
        <wsdl:part name="FindGCApplicationsForCairActivationResponse" element="FindGCApplicationsForCairActivationResponse"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairActivationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FindGCApplicationsForCairActivationFault"/>
    </wsdl:message>

    <wsdl:message name="FindGCApplicationsForCairBySessionUuidRequest">
        <wsdl:part name="FindGCApplicationsForCairBySessionUuidRequest" element="FindGCApplicationsForCairBySessionUuidRequest"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairBySessionUuidResponse">
        <wsdl:part name="FindGCApplicationsForCairBySessionUuidResponse" element="FindGCApplicationsForCairBySessionUuidResponse"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairBySessionUuidFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FindGCApplicationsForCairBySessionUuidFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationDetailByFilterRequest">
        <wsdl:part name="GetApplicationDetailByFilterRequest" element="GetApplicationDetailByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationDetailByFilterResponse">
        <wsdl:part name="GetApplicationDetailByFilterResponse" element="GetApplicationDetailByFilterResponse"/>
    </wsdl:message>

    <wsdl:portType name="Application">
        <wsdl:operation name="FindApplications">
            <wsdl:input message="FindApplicationsRequest"/>
            <wsdl:output message="FindApplicationsResponse"/>
            <wsdl:fault name="FindApplicationsFault" message="FindApplicationsFault"/>
        </wsdl:operation>

        <wsdl:operation name="SetBranchCode">
            <wsdl:input name="SetBranchCodeRequest" message="SetBranchCodeRequest"/>
            <wsdl:output name="SetBranchCodeResponse" message="SetBranchCodeResponse"/>
            <wsdl:fault name="SetBranchCodeFault" message="SetBranchCodeFault"/>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairActivation">
            <wsdl:input name="FindGCApplicationsForCairActivationRequest" message="FindGCApplicationsForCairActivationRequest"/>
            <wsdl:output name="FindGCApplicationsForCairActivationResponse" message="FindGCApplicationsForCairActivationResponse"/>
            <wsdl:fault name="FindGCApplicationsForCairActivationFault" message="FindGCApplicationsForCairActivationFault"/>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairBySessionUuid">
            <wsdl:input name="FindGCApplicationsForCairBySessionUuidRequest" message="FindGCApplicationsForCairBySessionUuidRequest"/>
            <wsdl:output name="FindGCApplicationsForCairBySessionUuidResponse" message="FindGCApplicationsForCairBySessionUuidResponse"/>
            <wsdl:fault name="FindGCApplicationsForCairBySessionUuidFault" message="FindGCApplicationsForCairBySessionUuidFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationDetailByFilter">
            <wsdl:input name="GetApplicationDetailByFilterRequest" message="GetApplicationDetailByFilterRequest"/>
            <wsdl:output name="GetApplicationDetailByFilterResponse" message="GetApplicationDetailByFilterResponse"/>
            <wsdl:fault name="GetApplicationDetailByFilterFault" message="GetApplicationDetailByFilterFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ApplicationBinding" type="Application">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="FindApplications">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FindApplicationsFault">
                <soap:fault name="FindApplicationsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="SetBranchCode">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetBranchCodeFault">
                <soap:fault name="SetBranchCodeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairActivation">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FindGCApplicationsForCairActivationFault">
                <soap:fault name="FindGCApplicationsForCairActivationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairBySessionUuid">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FindGCApplicationsForCairBySessionUuidFault">
                <soap:fault name="FindGCApplicationsForCairBySessionUuidFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationDetailByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationDetailByFilterFault">
                <soap:fault name="GetApplicationDetailByFilterFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ApplicationWS">
        <wsdl:port name="ApplicationPort" binding="ApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/asm/application"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
