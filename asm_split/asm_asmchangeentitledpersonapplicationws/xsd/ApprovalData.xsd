<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/asm/ws/approvaldata/common" jxb:version="2.1"
           targetNamespace="http://airbank.cz/asm/ws/approvaldata/common">

    <xs:complexType name="Application" abstract="true">
        <xs:annotation>
            <xs:documentation>Information about application (set of applications), depends on application hierarchy.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of application.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="ApplicationStatus">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>application status</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Envelope" abstract="true">
        <xs:annotation>
            <xs:documentation>Envelope (set of applications) relevant for approval process. Applications has its hierarchy and main application define envelope
                type.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of main application.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateCreated" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of envelope creation.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="channel" type="Channel" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>channel</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicant" type="Person">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>applicant</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="smeCustomerApplicant" type="SmeCustomer">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>applicant</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ContractEnvelope">
        <xs:annotation>
            <xs:documentation>Contract Envelope.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Envelope">
                <xs:sequence>
                    <xs:element name="currentAccountApplication" type="CurrentAccountApplication" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="savingAccountApplication" type="SavingAccountApplication" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="cardApplication" type="CardApplication" minOccurs="0"/>
                    <xs:element name="contractApplication" type="ContractApplication"/>
                    <xs:element name="contractParticipantApplication" type="ContractParticipantApplication" minOccurs="2" maxOccurs="2"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="DisponentEnvelope">
        <xs:complexContent>
            <xs:extension base="Envelope">
                <xs:sequence>
                    <xs:element name="disponentApplication" type="PersonRequestApplication"/>
                    <xs:element name="affidavitApplication" type="Affidavit"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CurrentAccountEnvelope">
        <xs:complexContent>
            <xs:extension base="Envelope">
                <xs:sequence>
                    <xs:element name="cardApplication" type="CardApplication" minOccurs="0"/>
                    <xs:element name="currentAccountApplication" type="CurrentAccountApplication"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="SavingAccountEnvelope">
        <xs:complexContent>
            <xs:extension base="Envelope">
                <xs:sequence>
                    <xs:element name="savingAccountApplication" type="SavingAccountApplication"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AuthResetEnvelope">
        <xs:complexContent>
            <xs:extension base="Envelope">
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CardEnvelope">
        <xs:complexContent>
            <xs:extension base="Envelope">
                <xs:sequence>
                    <xs:element name="affidavitApplication" type="Affidavit" minOccurs="0"/>
                    <xs:element name="cardApplication" type="CardApplication"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ChangeEntitledPersonEnvelope">
        <xs:annotation>
            <xs:documentation>Change Entitled Person Envelope.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Envelope">
                <xs:sequence>
                    <xs:element name="changeEntitledPersonApplication" type="ChangeEntitledPersonApplication"/>
                    <xs:element name="contractParticipantApplication" type="ContractParticipantApplication" minOccurs="2" maxOccurs="2"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="ChannelType">
        <xs:annotation>
            <xs:documentation>Channel type for relevant process type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ICC"/>
            <xs:enumeration value="IB"/>
            <xs:enumeration value="BRANCH"/>
            <xs:enumeration value="SPB"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ContractApplication">
        <xs:annotation>
            <xs:documentation>Information about general contract application.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="recommendatoryCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>code of recommendation also known as Member Get Member code.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="urlReferer" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>identifier of web, which links to Air/Bank web site.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="internalCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Branch officer code.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="processCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>identifier of short or full process.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="legalSegment" type="LegalSegment" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="PersonPreviousApplication">
        <xs:annotation>
            <xs:documentation>information about person's previous loan applications</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of previous application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateCreate" type="xs:dateTime">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of creation of application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationType" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>type of application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationStatus" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>status of application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="koCodes" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>hard check codes, delimited by "|"</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reject reason stored on application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="changedAppDataKey" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>attribute name, that was changed</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="changedAppDataValue" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>origin value (from previous application)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicant" type="Person" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>applicant - main debtor</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ChangeEntitledPersonApplication">
        <xs:annotation>
            <xs:documentation>Information about change entitled person application.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="legalSegment" type="LegalSegment"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Channel">
        <xs:annotation>
            <xs:documentation>Information about channel (set of channels) for relevant business process time stamps.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="employeeCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of employee, who assist applicant during acquisition process.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelProcessTime" type="xs:dateTime">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>timestamp for current channel process type.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="fingerPrint" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>is defined as hash of next web browser parameters: plugins, supercookies, timezone, fonts, video.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cookies" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about cookies, relevant for channel internet banking.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="ipAddress" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ip address of computer, where application was originated.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="branchOffice" type="BranchOffice" minOccurs="0"/>
            <xs:element name="channelProcessType" type="ChannelProcessType"/>
            <xs:element name="channelType" type="ChannelType"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountApplication" abstract="true">
        <xs:annotation>
            <xs:documentation>account application</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="currency" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>set currency for account</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="systemEnforcedRequest" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about enforced account (was not required by applicant, but necessary for loan). Relevant only for
                                        current account application.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="SavingAccountApplication">
        <xs:annotation>
            <xs:documentation>Information about general contract application.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AccountApplication">
                <xs:sequence>
                    <xs:sequence>
                    </xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CurrentAccountApplication">
        <xs:annotation>
            <xs:documentation>Information about general contract application.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AccountApplication">
                <xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Person">
        <xs:annotation>
            <xs:documentation>person information (set of persons related to application)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="birthDate" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of person birth.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthPlace" type="BirthPlace" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>place of person´s birth.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="citizenship" type="xs:string" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>citizenship, alpha2 country code is expected, ISO 3166-1.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="gender" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>gender, CIF REGISTER (Reg_number = 12)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name1" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>first name of person.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name2" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>last name of person.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of person, defined in CIF system.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>birth number is identifier of person used in czech republic. 9 or 10 digits number which is unique. Birth number is
                                optional due to foreigners.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="politicallyExposed" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Politically exposed person.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="contact" type="Contact" maxOccurs="unbounded"/>
            <xs:element name="personFlag" type="PersonFlag" minOccurs="0"/>
            <xs:element name="address" type="Address" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="document" type="Document" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="personType" type="PersonType"/>
            <xs:element name="relation" type="Relation" minOccurs="0"/>
            <xs:element name="identificationNumber" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Address">
        <xs:annotation>
            <xs:documentation>Person address (set of addresses).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="country" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>address country, alpha2 code is expected, defined by ISO 3166-1</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="streetOrLocality" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Address street name</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="streetNumber" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Address street number</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="town" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Address town name</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="zipCode" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>address zip code.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="addressType" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>address type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CardApplication">
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="requiredPersonRole" type="PersonType">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>person type, for who is debit card required, CMS REISTER (REG. 718).</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coupleAccountApplicationId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>identifier of associated account request</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isVirtualCardRequired" type="xs:boolean">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about requirement for virtual card</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isIssueFeeRequired" type="xs:boolean">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about fee charge</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coupleAccountNumber" type="xs:string" minOccurs="0"/>
                    <xs:element name="replacedCardNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>card number (PCI-DSS compliant) of replacing card.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="xs:long"/>
                    <xs:element name="cuidApplicant" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>cuid of applicant</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requiredCardDevice" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Indicates, if we create physical card through gpe</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="DuplicityClient">
        <xs:annotation>
            <xs:documentation>cross checks against data persists in application management system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="parameterType" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>type of duplicity parameter</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name1" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>first name</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="name2" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Sure name</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>birth number (for czech citizens)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of customer from CIF</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birth" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of bitrh</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="citizenship" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>country code for citizenship (ISO-3166)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Contact">
        <xs:annotation>
            <xs:documentation>contact (set of contacts) related to person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="value" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>contact value</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="callingCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calling code, relevant for contact types using phone.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="contactType" type="ContactType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>contact type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SameParameterCheck">
        <xs:annotation>
            <xs:documentation>same parameter checks against data persists in application management system. (relevant for application -- for new general
                contract information about contract applications)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="parameterType" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>attribute from application. Ve vektoru je definováno:
                                Typ parametru, pro který se má použít krížová kontrola :
                                "1" : Birth number
                                "2" : Email
                                "3" : Mobil number
                                "4" : IP address
                                "5" : Cookies
                                "6" : Fingerprint
                                "7" : Other phone
                                "8" : Main employment phone
                                "9" : Second employment phone
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="duplicityOtherClientCount" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of duplicity records found in primary system, which is not connected to current applicant.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="duplicitySameClientCount" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of duplicity records found in primary system, which is connected to current applicant.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="countShortInterval" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of applications for current applicant in short interval</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="countLongInterval" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of applications for current applicant in long interval</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ChannelProcessType">
        <xs:annotation>
            <xs:documentation>Timestamp for some business relevant cases in application life cycle.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVE_CLIENT"/>
            <xs:enumeration value="APPROVE"/>
            <xs:enumeration value="LEAD"/>
            <xs:enumeration value="REQUEST"/>
            <xs:enumeration value="FINISHED"/>
            <xs:enumeration value="CURRENT"/>
            <xs:enumeration value="SIGN"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PersonType">
        <xs:annotation>
            <xs:documentation>type of person, related to data origin.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPLICANT"/>
            <xs:enumeration value="DISPONENT"/>
            <xs:enumeration value="CARD_HOLDER"/>
            <xs:enumeration value="DISPONENT_AND_CARD_HOLDER"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="BranchOffice">
        <xs:annotation>
            <xs:documentation>Branch office information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="officeId" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of branch office</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PersonRequestApplication">
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="flagClientIsLegalRepresentative" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information if client is legal representative of person included in application.</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>unique identifier of person, defined in CIF system</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requiredPersonRole" type="PersonType">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>person type</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuidApplicant" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>cuid of applicant</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="Affidavit">
        <xs:annotation>
            <xs:documentation>Specific person flag (attributes with special meaning)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of previous application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="requiredPerson" type="Person">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>required person</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="requiredPersonRole" type="PersonType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>person type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuidApplicant" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>cuid of applicant</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="AffidavitStatus">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>status</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PersonFlag">
        <xs:annotation>
            <xs:documentation>Specific person flag (attributes with special meaning), Puvodne REG_NUMBER = 887, nove cif_param.flag_type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="personAmlStatus" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about actual anti money laundering (AML) person status</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="manualAmlCheckStatus" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about actual AML verification (manually by operator)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="relatedParty" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>person with some relationship to PPF group.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Document">
        <xs:annotation>
            <xs:documentation>Document information. For person is expected only document records from document group "IDENTIFICATION" or "FINACIAL".
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="number" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>number of document</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="country" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>country, where document was issued.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isPrimary" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document flag defines if document is set as primary.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="issueDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date when document was issued.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="validTo" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date when document ends its validity.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveryChannel" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>channel which was used for delivery to Air/Bank</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentType" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroup" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document group (FINANCIAL, IDENTIFICATION, ...)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikační číslo dokumentu - číslo dodaného dokladu v AMS</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifTime" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="code" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Strojový kód identifikačního dokladu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="identificationNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČO zaměstnavatele</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="taxNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Daňové identifikační číslo - DIČ</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="completionByOperator" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace operátora, nebo systému provádějícího kompletaci.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="processingDate" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas kompletace dokumentu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentStatusOrigin" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj změny stavu dokumentu: MANUAL/OCR.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="faceMatchScore" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek porovnání fotografí, pokud se prováděl.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj dokladu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="newIdentification" type="NewIdentification" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nová identifikace osoby.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="NewIdentification">
        <xs:annotation>
            <xs:documentation>
                Nová identifikace osoby.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="businessProcessEvent" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Operátorem zamýšlená úprava, může být jen
                                IDENTITY_CLIENT_CORRECTION
                                IDENTITY_LEGAL_CHANGE
                                Obecně se jedná o položky MDM číselníku IdentityProcessEvent.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operatorEmployeeNumber" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identifikace operatora podle LDAP
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="identification" type="Identification">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Budoucí identifikace klienta podle specifikace v CIF.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Identification">
        <xs:choice>
            <xs:element name="simpleIdentification" type="SimpleIdentification"/>
            <xs:element name="standardIdentification" type="StandardIdentification"/>
            <xs:element name="fullIdentification" type="FullIdentification"/>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="SimpleIdentification">
        <xs:annotation>
            <xs:documentation>
                Jednoduchá identifikace
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="quintupleIdentity" type="QuintupleIdentity">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita pětice
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="StandardIdentification">
        <xs:annotation>
            <xs:documentation>
                Standardní identifikace
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="quintupleIdentity" type="QuintupleIdentity">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita pětice
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="aifoIdentity" type="AifoIdentity" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita AIFO
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FullIdentification">
        <xs:annotation>
            <xs:documentation>
                Plná identifikace
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="quintupleIdentity" type="QuintupleIdentity">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita pětice
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthNumberIdentity" type="BirthNumberIdentity">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita českého rodného čísla
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="aifoIdentity" type="AifoIdentity" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita AIFO
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="QuintupleIdentity">
        <xs:sequence>
            <xs:element name="firstName" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Jméno
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Příjmení
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthDate" type="xs:date">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Datum narození
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthPlace" type="BirthPlace">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Místo narození
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BirthPlace">
        <xs:annotation>
            <xs:documentation>Místo narození</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="discriminator" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Položky z BirthPlaceDiscriminator.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="stateToDisplay" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Stát narození k zobrazení
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="placeToDisplay" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Místo narození k zobrazení
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:choice minOccurs="0">
                <xs:element name="ruianBirthPlace" type="RuianBirthPlace">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Místo narození podle RUIAN (implicitně CZ)
                                </jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="countryBirthPlace" type="CountryBirthPlace">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Místo narození určením země a textové specifikace přesného místa narození
                                </jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="outsideCountryBirthPlace" type="OutsideCountryBirthPlace">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Místo narození určené popisem mimo určení země
                                </jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RuianBirthPlace">
        <xs:choice>
            <xs:element name="townBirthPlace" type="TownBirthPlace"/>
            <xs:element name="pragueMunicipalDistrictBirthPlace" type="PragueMunicipalDistrictBirthPlace"/>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="TownBirthPlace">
        <xs:sequence>
            <xs:element name="townCode" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Kód obce podle specifikace RUIAN
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PragueMunicipalDistrictBirthPlace">
        <xs:sequence>
            <xs:element name="pragueMunicipalDistrictCode" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Kód městského obvodu Prahy v RUIAN
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CountryBirthPlace">
        <xs:sequence>
            <xs:element name="alpha2Code" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Dvoupísmenná hodnota z číselníku Country (CIF Codelist items)
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="location" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Textová specifikace přesného místa narození pro danou zemi.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OutsideCountryBirthPlace">
        <xs:sequence>
            <xs:element name="place" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Textová specifikace ne zcela přesného místa narození, například "na moři"
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AifoIdentity">
        <xs:sequence>
            <xs:element name="aifo" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Agendový identifikator fyzické osoby
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BirthNumberIdentity">
        <xs:sequence>
            <xs:element name="birthNumber" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                České rodné číslo
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ContactType">
        <xs:annotation>
            <xs:documentation>contact type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRIMARY_EMAIL"/>
            <xs:enumeration value="PRIMARY_MOBILE"/>
            <xs:enumeration value="PHONE"/>
            <xs:enumeration value="ACCOUNT_EMAIL"/>
            <xs:enumeration value="EMPLOYMENT_PHONE"/>
            <xs:enumeration value="ACCOUNT_MOBILE"/>
            <xs:enumeration value="EMAIL"/>
            <xs:enumeration value="ENTREPRENEUR_DATA_BOX"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="DocumentType">
        <xs:annotation>
            <xs:documentation>document type, relevant for document group.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACC_AGREEMENT"/>
            <xs:enumeration value="ACC_CONFIRMATION"/>
            <xs:enumeration value="ACC_STATEMENT"/>
            <xs:enumeration value="APARTMENT_LEASE"/>
            <xs:enumeration value="BILL_OF_EXCHANGE"/>
            <xs:enumeration value="BIRTH"/>
            <xs:enumeration value="BUSINESS_CERTIFICATE"/>
            <xs:enumeration value="BUSINESS_LICENCE"/>
            <xs:enumeration value="CONCESSION"/>
            <xs:enumeration value="DRIVE"/>
            <xs:enumeration value="EMPLOYMENT_CONTRACT"/>
            <xs:enumeration value="ENERGY_BILL"/>
            <xs:enumeration value="FOREIGN_ID_CARD"/>
            <xs:enumeration value="GUN"/>
            <xs:enumeration value="ID_CARD"/>
            <xs:enumeration value="ID_CARD_SUBSTITUTED"/>
            <xs:enumeration value="INCOME"/>
            <xs:enumeration value="LAND_REGISTRY"/>
            <xs:enumeration value="LEASE_AGREEMENT"/>
            <xs:enumeration value="LICENSE_AGREEMENT"/>
            <xs:enumeration value="LIVING_DECLARATION"/>
            <xs:enumeration value="MARRIAGE_CERTIFICATE"/>
            <xs:enumeration value="MOBILE_PHONE_BILL"/>
            <xs:enumeration value="OTHER"/>
            <xs:enumeration value="OTHER_ADDRESS"/>
            <xs:enumeration value="OTHER_BUSINESS"/>
            <xs:enumeration value="OTHER_ID"/>
            <xs:enumeration value="OTHER_INCOME"/>
            <xs:enumeration value="PASSPORT"/>
            <xs:enumeration value="PAYSLIP"/>
            <xs:enumeration value="PHONE_BILL"/>
            <xs:enumeration value="PURCHASE_CONTRACT"/>
            <xs:enumeration value="RETIREMENT"/>
            <xs:enumeration value="RETIREMENT_CONFIRMATION"/>
            <xs:enumeration value="SERVICE_RENT"/>
            <xs:enumeration value="SHARE_STATEMENT"/>
            <xs:enumeration value="SIPO"/>
            <xs:enumeration value="SPOUSES_JOINT_ASSETS"/>
            <xs:enumeration value="STAY_PERMIT"/>
            <xs:enumeration value="STUDENT_CONFIRMATION"/>
            <xs:enumeration value="TAXOFF_NODEBT"/>
            <xs:enumeration value="TAX_RETURN"/>
            <xs:enumeration value="TRADE_REGISTER"/>
            <xs:enumeration value="LOAN_CONTRACT"/>
            <xs:enumeration value="CC_STATEMENT"/>
            <xs:enumeration value="OTHER_OBLIGATION_DOCUMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- Abstract response -->

    <xs:complexType name="AbstractResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Default application response.</xs:documentation>
        </xs:annotation>
    </xs:complexType>

    <xs:complexType name="Relation">
        <xs:annotation>
            <xs:documentation>Vztah</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="role" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ vztahu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid2" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Cuid spoludlužníka</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AffidavitStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVE"/>
            <xs:enumeration value="REJECTED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Contract">
        <xs:sequence>
            <xs:element name="contractId" type="xs:long"/>
            <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor obálky v AMS.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelProposed" type="xs:string"/>
            <xs:element name="dateActivated" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum aktivace.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateClosed" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum ukončení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="reasonClosed" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Důvod ukončení.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="gcStatus" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Stav rámcové smlouvy.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="actualFUP" type="xs:int" minOccurs="0"/>
            <xs:element name="automaticallyCompletedContract" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Automaticky zkompletováno.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debitCards" type="DebitCard" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>List debetních karet..</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="contractSignInternalCode" type="xs:string" minOccurs="0"/>
            <xs:element name="signDate" type="xs:date" minOccurs="0"/>
            <xs:element name="isActivedOnBranch" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Informace, zda je doručená rámcová smlouva aktivována na pobočce.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isFake" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Informace, zda je doručená rámcová smlouva falsifikát.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isInkSign" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Informace zda je fyzický podpis na papírovém dokumentu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isSignPadSign" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Informace zda je fyzický podpis RS na SignPadu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DebitCard">
        <xs:annotation>
            <xs:documentation>Debetní karta.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cardNumber" type="xs:string"/>
            <xs:element name="dateActivated" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="isCurrentlyVirtual" type="xs:boolean" minOccurs="0"/>
            <xs:element name="coupleAccountNumber" type="xs:long"/>
            <xs:element name="cardType" type="xs:string"/>
            <xs:element name="cuid" type="xs:long"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApplicationStatus">
        <xs:annotation>
            <xs:documentation>AMS application status.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEMO">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Special status until first updateXXX WS method. It means, that the application was not actually created and is
                                internally used just as a cache.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="UNFINISHED">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was created and there was no scoring executed.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="APPROVED">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was approved (either after scoring invocation or because there was no scoring).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="REJECTED">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was rejected after scoring.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CANCELLED">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was cancelled by user or it expired.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VERIFY">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is being scored and it is in VERIFY workflow.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="COMPLETION">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is finished by client and bank employees assembled all documentation.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="SmeCustomer">
        <xs:annotation>
            <xs:documentation>person information (set of persons related to application)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of person, defined in CIF system.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="identificationNumber" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČO podnikatele.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="address" type="Address" minOccurs="2" maxOccurs="2"/>
            <xs:element name="personType" type="PersonType"/>
            <xs:element name="relation" type="Relation"/>
            <xs:element name="legalSegment" type="LegalSegment"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ContractParticipantApplication">
        <xs:annotation>
            <xs:documentation>Information about contract participant application.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="Application">
                <xs:sequence>
                    <xs:element name="participantType" type="xs:string">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>BUSINESS_ENTITY, FORMER_PERSON</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="xs:long">
                        <xs:annotation>
                            <xs:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>cuid</jxb:javadoc>
                                </jxb:property>
                            </xs:appinfo>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="LegalSegment">
        <xs:annotation>
            <xs:documentation>Legal segment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ENTREPRENEUR"/>
            <xs:enumeration value="LEGAL_ENTITY"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
