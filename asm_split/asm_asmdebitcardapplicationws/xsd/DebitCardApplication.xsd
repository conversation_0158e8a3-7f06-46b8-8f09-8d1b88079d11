<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns="http://airbank.cz/asm/ws/application/debitcard"
            targetNamespace="http://airbank.cz/asm/ws/application/debitcard"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:simpleType name="MailingAddressRoleTO">
        <xsd:annotation>
            <xsd:documentation>Role of mailing address.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="REPORTS"/>
            <xsd:enumeration value="CORRESPONDENCE"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
