<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:faultCommon="http://airbank.cz/common/ws/fault"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/asm/ws/identity"
           targetNamespace="http://airbank.cz/asm/ws/identity">

    <xs:import namespace="http://airbank.cz/common/ws/fault" schemaLocation="commonSoapFault.xsd"/>

    <xs:element name="ValidateApplicantWithNewIdentificationRequest">
        <xs:annotation>
            <xs:documentation>Request for applicant with new identification validation.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deliveredDocumentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Delivered document ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ValidateApplicantWithNewIdentificationResponse">
        <xs:annotation>
            <xs:documentation>Response for applicant with new identification validation</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="validationResult" type="faultCommon:ValidationResultTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Validation results</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ChangeCustomerIdentitiesRequest">
        <xs:annotation>
            <xs:documentation>Request for change customer identities.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deliveredDocumentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Delivered document ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ChangeCustomerIdentitiesResponse">
        <xs:annotation>
            <xs:documentation>Response for change customer identities</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

</xs:schema>