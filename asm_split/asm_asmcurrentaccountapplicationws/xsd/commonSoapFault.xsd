<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://airbank.cz/common/ws/fault"
           targetNamespace="http://airbank.cz/common/ws/fault">

    <xs:complexType name="FaultArgument">
        <xs:annotation>
            <xs:documentation>Fault argument containing name and value</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" type="xs:string"></xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="ValidationResultArgument">
        <xs:annotation>
            <xs:documentation>Validation result argument containing name and value</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" type="xs:string"></xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>


    <xs:complexType name="CoreFault" abstract="true">
        <xs:annotation>
            <xs:documentation>Base for all application faults. It contains message (as part of standard SOAP fault) and optional arguments.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="argument" type="FaultArgument" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Optional fault arguments (they can be different for each fault type).</xs:documentation>
                </xs:annotation>
            </xs:element>

        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BusinessFault">
        <xs:annotation>
            <xs:documentation>Base for all business faults.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="CoreFault">
                <xs:sequence>
                    <xs:element name="code" minOccurs="1"
                                maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>
                                Fault code (specific codes are described
                                for each operation, that can generate
                                business fault).
                            </xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:minLength value="1"></xs:minLength>
                                <xs:maxLength value="100"></xs:maxLength>
                                <xs:pattern value="[a-zA-Z0-9._]*"></xs:pattern>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="SystemFault">
        <xs:annotation>
            <xs:documentation>Base for all system faults.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="CoreFault">
                <xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="ValidationResultTO">
        <xs:annotation>
            <xs:documentation>Single validation result.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Validation error code (specific codes must be specified for each operation).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="attributeName" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Attribute name, that caused the validation error (if there is no corresponding attribute, it is not present).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="message" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Non-localized error description (should not be shown to users).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="argument" type="ValidationResultArgument" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Optional validation error arguments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="suppressingAttributeName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Attribute name, that can suppress this validation error.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ValidationFault">
        <xs:annotation>
            <xs:documentation>Base for all validation faults. Each fault contains at least one validation result.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="CoreFault">
                <xs:sequence>
                    <xs:element name="validationResults" type="ValidationResultTO" minOccurs="1" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                Validation results
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AccessDeniedFault">
        <xs:annotation>
            <xs:documentation>Fault raised when the operator has unsufficient privileges to perform some action.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="CoreFault">
                <xs:sequence>
                    <xs:element name="allowedRoles" type="xs:string" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                               Roles allowed to execute the checked operation or empty list.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="currentRoles" type="xs:string" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                Current operator roles when checking the access operation or empty list.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AccessDeniedForActionFault">
        <xs:annotation>
            <xs:documentation>Fault raised when the operator has unsufficient privileges to perform some action because of missing actions.</xs:documentation>
        </xs:annotation>

        <xs:complexContent>
            <xs:extension base="CoreFault">
                <xs:sequence>
                    <xs:element name="allowedActions" type="xs:string" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                               Roles allowed to execute the checked operation or empty list.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="currentActions" type="xs:string" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                Current operator roles when checking the access operation or empty list.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="CoreFaultElement" type="CoreFault"/>

</xs:schema>