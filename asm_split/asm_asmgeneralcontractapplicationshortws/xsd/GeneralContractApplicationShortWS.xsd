<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns="http://airbank.cz/asm/ws/application/generalcontractshort"
            targetNamespace="http://airbank.cz/asm/ws/application/generalcontractshort"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>

    <xsd:element name="StartRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerApplicantCuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>CUID of customer applicant</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StartResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:StartResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitBusinessDetailsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitBusinessDetailsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="identificationNumber" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Identification number (IČ).</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateBusinessDetailsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="identificationNumber" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Identification number (IČ).</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddressSameAsBusiness" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Flag indicating that mailing address is same as business address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Mailing address.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mailingAddressConfirmedByClient" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Indicates that client confirmed that mailing address is valid even it is not present in registry.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="pureRegistryData" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Whether economical data was suggested from registry and was not changed by client</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="legalSegment" type="appCommon:LegalSegmentTypeTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateBusinessDetailsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
                <xsd:element name="lopiToken" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitLegalMandatoryDataRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitLegalMandatoryDataResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="legalSegment" type="appCommon:LegalSegmentTypeTO"/>
                <xsd:element name="customerTaxDeclaration" type="appCommon:CustomerTaxDeclarationTO" minOccurs="0"/>
                <xsd:element name="legalEntityTaxDeclaration" type="appCommon:LegalEntityTaxDeclarationTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateLegalMandatoryDataRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="customerTaxDeclaration" type="appCommon:CustomerTaxDeclarationTO" minOccurs="0"/>
                <xsd:element name="legalEntityTaxDeclaration" type="appCommon:LegalEntityTaxDeclarationTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateLegalMandatoryDataResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitChangeProfileRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitChangeProfileResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="generalContract" type="appCommon:GeneralContractTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateChangeProfileRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateChangeProfileResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
