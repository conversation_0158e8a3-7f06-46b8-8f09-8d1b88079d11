<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/asm/ws/application/generalcontractshort"
                  targetNamespace="http://airbank.cz/asm/ws/application/generalcontractshort">
    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/asm/ws/application/generalcontractshort">
            <xsd:include schemaLocation="../xsd/GeneralContractApplicationShortWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <wsdl:message name="InitBusinessDetailsRequest">
        <wsdl:part element="InitBusinessDetailsRequest" name="InitBusinessDetailsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBusinessDetailsResponse">
        <wsdl:part element="InitBusinessDetailsResponse" name="InitBusinessDetailsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBusinessDetailsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBusinessDetailsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateBusinessDetailsRequest">
        <wsdl:part element="UpdateBusinessDetailsRequest" name="UpdateBusinessDetailsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBusinessDetailsResponse">
        <wsdl:part element="UpdateBusinessDetailsResponse" name="UpdateBusinessDetailsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBusinessDetailsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBusinessDetailsFault"/>
    </wsdl:message>

    <wsdl:message name="InitLegalMandatoryDataRequest">
        <wsdl:part element="InitLegalMandatoryDataRequest" name="InitLegalMandatoryDataRequest"/>
    </wsdl:message>
    <wsdl:message name="InitLegalMandatoryDataResponse">
        <wsdl:part element="InitLegalMandatoryDataResponse" name="InitLegalMandatoryDataResponse"/>
    </wsdl:message>
    <wsdl:message name="InitLegalMandatoryDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitLegalMandatoryDataFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateLegalMandatoryDataRequest">
        <wsdl:part element="UpdateLegalMandatoryDataRequest" name="UpdateLegalMandatoryDataRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateLegalMandatoryDataResponse">
        <wsdl:part element="UpdateLegalMandatoryDataResponse" name="UpdateLegalMandatoryDataResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateLegalMandatoryDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateLegalMandatoryDataFault"/>
    </wsdl:message>

    <wsdl:message name="InitChangeProfileRequest">
        <wsdl:part element="InitChangeProfileRequest" name="InitChangeProfileRequest"/>
    </wsdl:message>
    <wsdl:message name="InitChangeProfileResponse">
        <wsdl:part element="InitChangeProfileResponse" name="InitChangeProfileResponse"/>
    </wsdl:message>
    <wsdl:message name="InitChangeProfileFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitChangeProfileFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateChangeProfileRequest">
        <wsdl:part element="UpdateChangeProfileRequest" name="UpdateChangeProfileRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateChangeProfileResponse">
        <wsdl:part element="UpdateChangeProfileResponse" name="UpdateChangeProfileResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateChangeProfileFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateChangeProfileFault"/>
    </wsdl:message>

    <wsdl:portType name="GeneralContractApplicationShort">
        <wsdl:operation name="Start">
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitBusinessDetails">
            <wsdl:input message="InitBusinessDetailsRequest"/>
            <wsdl:output message="InitBusinessDetailsResponse"/>
            <wsdl:fault name="InitBusinessDetailsFault" message="InitBusinessDetailsFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateBusinessDetails">
            <wsdl:input message="UpdateBusinessDetailsRequest"/>
            <wsdl:output message="UpdateBusinessDetailsResponse"/>
            <wsdl:fault name="UpdateBusinessDetailsFault" message="UpdateBusinessDetailsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitLegalMandatoryData">
            <wsdl:input message="InitLegalMandatoryDataRequest"/>
            <wsdl:output message="InitLegalMandatoryDataResponse"/>
            <wsdl:fault name="InitLegalMandatoryDataFault" message="InitLegalMandatoryDataFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateLegalMandatoryData">
            <wsdl:input message="UpdateLegalMandatoryDataRequest"/>
            <wsdl:output message="UpdateLegalMandatoryDataResponse"/>
            <wsdl:fault name="UpdateLegalMandatoryDataFault" message="UpdateLegalMandatoryDataFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitChangeProfile">
            <wsdl:input message="InitChangeProfileRequest"/>
            <wsdl:output message="InitChangeProfileResponse"/>
            <wsdl:fault name="InitChangeProfileFault" message="InitChangeProfileFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateChangeProfile">
            <wsdl:input message="UpdateChangeProfileRequest"/>
            <wsdl:output message="UpdateChangeProfileResponse"/>
            <wsdl:fault name="UpdateChangeProfileFault" message="UpdateChangeProfileFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="GeneralContractApplicationShortBinding" type="GeneralContractApplicationShort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitBusinessDetails">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBusinessDetailsFault">
                <soap:fault name="InitBusinessDetailsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateBusinessDetails">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBusinessDetailsFault">
                <soap:fault name="UpdateBusinessDetailsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitLegalMandatoryData">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitLegalMandatoryDataFault">
                <soap:fault name="InitLegalMandatoryDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateLegalMandatoryData">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateLegalMandatoryDataFault">
                <soap:fault name="UpdateLegalMandatoryDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitChangeProfile">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitChangeProfileFault">
                <soap:fault name="InitChangeProfileFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateChangeProfile">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateChangeProfileFault">
                <soap:fault name="UpdateChangeProfileFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="GeneralContractApplicationShortWS">
        <wsdl:port name="GeneralContractApplicationPort" binding="GeneralContractApplicationShortBinding">
            <soap:address location="http://TO-BE-SPECIFIED/asm/application/generalcontract"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
