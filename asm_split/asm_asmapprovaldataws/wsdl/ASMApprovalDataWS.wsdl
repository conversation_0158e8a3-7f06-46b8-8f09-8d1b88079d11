<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/asm/ws/approvaldata"
                  targetNamespace="http://airbank.cz/asm/ws/approvaldata">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/asm/ws/approvaldata">
            <xs:include schemaLocation="../xsd/ApprovalDataWS.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="GetApprovalDataResponse">
        <wsdl:part element="GetApprovalDataResponse" name="GetApprovalDataResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApprovalDataRequest">
        <wsdl:part element="GetApprovalDataRequest" name="GetApprovalDataRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApprovalDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApprovalDataFault"/>
    </wsdl:message>

    <wsdl:message name="GetPersonPreviousApplicationResponse">
        <wsdl:part element="GetPersonPreviousApplicationResponse" name="GetPersonPreviousApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPersonPreviousApplicationRequest">
        <wsdl:part element="GetPersonPreviousApplicationRequest" name="GetPersonPreviousApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonPreviousApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetPersonPreviousApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="GetCrossClientCheckByFilterResponse">
        <wsdl:part element="GetCrossClientCheckByFilterResponse" name="GetCrossClientCheckByFilterResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCrossClientCheckByFilterRequest">
        <wsdl:part element="GetCrossClientCheckByFilterRequest" name="GetCrossClientCheckByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCrossClientCheckByFilterFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCrossClientCheckByFilterFault"/>
    </wsdl:message>

    <wsdl:portType name="GetApprovalDataPort">
        <wsdl:operation name="GetApprovalData">
            <wsdl:input message="GetApprovalDataRequest"/>
            <wsdl:output message="GetApprovalDataResponse"/>
            <wsdl:fault name="GetApprovalDataFault" message="GetApprovalDataFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetPersonPreviousApplication">
            <wsdl:input message="GetPersonPreviousApplicationRequest"/>
            <wsdl:output message="GetPersonPreviousApplicationResponse"/>
            <wsdl:fault name="GetPersonPreviousApplicationFault" message="GetPersonPreviousApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCrossClientCheckByFilter">
            <wsdl:input message="GetCrossClientCheckByFilterRequest"/>
            <wsdl:output message="GetCrossClientCheckByFilterResponse"/>
            <wsdl:fault name="GetCrossClientCheckByFilterFault" message="GetCrossClientCheckByFilterFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="GetApprovalDataBinding" type="GetApprovalDataPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetApprovalData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApprovalDataFault">
                <soap:fault name="GetApprovalDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetPersonPreviousApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPersonPreviousApplicationFault">
                <soap:fault name="GetPersonPreviousApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCrossClientCheckByFilter">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCrossClientCheckByFilterFault">
                <soap:fault name="GetCrossClientCheckByFilterFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="GetApprovalDataService">
        <wsdl:port binding="GetApprovalDataBinding" name="GetApprovalDataPort">
            <soap:address location="https://asm.banka.hci/asm/ws/ASMGetApprovalDataWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
