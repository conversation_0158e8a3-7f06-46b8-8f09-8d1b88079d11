<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns="http://airbank.cz/asm/ws/application/applicationmanagement"
            targetNamespace="http://airbank.cz/asm/ws/application/applicationmanagement"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>

    <xsd:element name="ContractBeingClosedRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="generalContractCompletionId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>GC completion ID</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="closeReason" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Close/cancel reason.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="ownerCuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Owner CUID</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="generalContractType" type="appCommon:GeneralContractType">
                    <xsd:annotation>
                        <xsd:documentation>General contract type</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ContractBeingClosedResponse">
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="CancelApplicationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the envelope containing application.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>

                <xsd:element name="cancelReason" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Reason for cancelling the application. There are no constraints, because this is generated internally
                            in Blaze.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>

                <xsd:element name="cancelReasonClient" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Reason for cancelling the application as communicated to client. There are no constraints, because
                            this is generated internally in Blaze.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="CancelApplicationResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCurrentTaskRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the envelope containing application.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCurrentTaskResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeVersion" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Current version of the application envelope.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>

                <xsd:element name="taskId" type="appCommon:TaskIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Id of the current task (the list of all possible values is given in the description of each service).
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetApplicationTerminationInfoRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the envelope containing application.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetApplicationTerminationInfoResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="reason" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Termination reason</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="reasonClient" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Termination reason as communicated to client</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="way" type="ApplicationTerminationWay">
                    <xsd:annotation>
                        <xsd:documentation>Termination way</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="severityLevel" type="ApplicationTerminationSeverityLevel">
                    <xsd:annotation>
                        <xsd:documentation>Termination severity level</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:simpleType name="ApplicationTerminationWay">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CANCEL"/>
            <xsd:enumeration value="REJECT"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApplicationTerminationSeverityLevel">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="INFO"/>
            <xsd:enumeration value="WARN"/>
            <xsd:enumeration value="ERROR"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
