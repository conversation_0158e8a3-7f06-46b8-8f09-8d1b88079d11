<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns="http://airbank.cz/asm/ws/application/savingaccount"
            targetNamespace="http://airbank.cz/asm/ws/application/savingaccount"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>

    <xsd:element name="StartRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerApplicantCuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>CUID of customer applicant</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="idProfile" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            profile identification (id of relation between subject and general contract in OBS)
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StartResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:StartResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitAccountParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitRequestMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitAccountParametersResponse">
        <xsd:complexType>
            <xsd:annotation>
                <xsd:documentation>Response data returned when initializing accountParametersTask screen.</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:InitResponseMetadata"/>
                <xsd:element name="possibleCurrencies" type="appCommon:CurrencyCodeTO" minOccurs="1" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>
                            List of 3-letter currency ISO codes, in which user can create saving account.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="accountParams" type="appCommon:SavingAccountParamsTO" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Account attributes</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateAccountParametersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateRequestMetadata"/>
                <xsd:element name="accountParams" type="appCommon:SavingAccountParamsTO" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Account attributes</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateAccountParametersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:UpdateResponseMetadata"/>
                <xsd:element name="lopiToken" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
