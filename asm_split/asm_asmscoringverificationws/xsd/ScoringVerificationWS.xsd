<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:common="http://airbank.cz/asm/ws/scoring/verification/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/asm/ws/scoring/verification"
           targetNamespace="http://airbank.cz/asm/ws/scoring/verification">

    <xs:import namespace="http://airbank.cz/asm/ws/scoring/verification/common" schemaLocation="ScoringVerification.xsd"/>

    <xs:element name="ProcessApplicationScoringResultRequest">
        <xs:annotation>
            <xs:documentation>Scoring data collected by asynchronous LAP process. Contains scoring results based on LAP Vector specification.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:ApplicationScoringResultRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessApplicationScoringResultResponse">
        <xs:annotation>
            <xs:documentation>Empty confirmation message. Should not throw any exception otherwise is something wrong. (Will be spec.)</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

</xs:schema>