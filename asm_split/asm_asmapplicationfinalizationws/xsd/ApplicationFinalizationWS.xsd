<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
            xmlns:appCommon="http://airbank.cz/asm/ws/application/common"
            xmlns="http://airbank.cz/asm/ws/application/finalization"
            targetNamespace="http://airbank.cz/asm/ws/application/finalization"
            jxb:version="2.1" elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/asm/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xsd:include schemaLocation="ApplicationFinalization.xsd"/>

    <xsd:complexType name="AbstractInitFinalizationRequest" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Generic init request parameters for the application finalization.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="finalizationId" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>Id of the application finalization process.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AbstractUpdateFinalizationRequest" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Generic update request parameters for the application finalization.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="metadata" type="appCommon:BaseUpdateRequestMetadata"/>
            <xsd:element name="finalizationId" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>Id of the application finalization process.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AbstractInitSignResponse" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Generic response for all signature inits</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="metadata" type="appCommon:BaseInitResponseMetadata"/>
            <xsd:element name="deliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Possible delivery channel</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="selectedDeliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Delivery channel, that was already selected as part of some previous application finalization run. If it is
                        null, then there was no previous delivery channel selected.
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contractDocument" type="appCommon:PhysicalDocumentTO" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Contract document with metadata.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contractType" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Signed contract type.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:choice>
                <xsd:element name="signatureType" maxOccurs="unbounded" nillable="true">
                    <xsd:annotation>
                        <xsd:documentation>Possible signature types</xsd:documentation>
                    </xsd:annotation>
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:minLength value="1"/>
                            <xsd:maxLength value="20"/>
                            <xsd:pattern value="[A-Z_]*"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element name="signatureTypeError" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Signature types are received using OBS method GetAuthTypes, which can return following errors:
                            CLERR_OWNER_SIGN_NEEDED, CLERR_AFF_TYPE_USER, CLERR_GENERAL_CONTRACT_NOT_COMPLETED.
                            In that case, signatureType is empty and error is filled here.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AbstractInitBranchSignResponse" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Generic response for all branch signature inits</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AbstractInitSignResponse">
                <xsd:sequence>
                    <xsd:element name="internalCode" type="appCommon:InternalCodeTO" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:documentation>Possible internal codes</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:element name="StartRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="applicationId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>ID of application to finalize</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StartResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseStartResponseMetadata"/>
                <xsd:element name="finalizationId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the finalization process. It must be used in all subsequent calls.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationTypes" type="appCommon:ApplicationTypeTO" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Application types of apps from the envelope related to finalizationId</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="legalSegment" type="appCommon:LegalSegmentTypeTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignOverInternetRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest"/>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignOverInternetResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignOverInternetRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateSignRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="AbstractUpdateSignRequest" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Generic request for all signature updates</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AbstractUpdateFinalizationRequest">
                <xsd:sequence>
                    <xsd:element name="authId" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Authorization ID from CASE.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="contractType" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Signed contract type.</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:element name="UpdateSignOverInternetResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignOverMobileRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest"/>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignOverMobileResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignOverMobileRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateSignRequest">
                    <xsd:sequence>
                        <xsd:element name="internalCode" type="xsd:string" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>Branch officer internal code.</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignOverMobileResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignAtBranchRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignAtBranchResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitBranchSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignAtBranchRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateFinalizationRequest">
                    <xsd:sequence>
                        <xsd:element name="signatureType" type="BranchSignatureType">
                            <xsd:annotation>
                                <xsd:documentation>Selected signature type</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="AbstractUpdateBranchSignRequest" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Generic request for all branch signature updates</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AbstractUpdateSignRequest">
                <xsd:sequence>
                    <xsd:element name="internalCode" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Internal code</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:element name="UpdateSignAtBranchResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitBlueSignRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitBlueSignResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitBranchSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateBlueSignRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateBranchSignRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateBlueSignResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignPadRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignPadResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitBranchSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignPadRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateFinalizationRequest">
                    <xsd:sequence>
                        <xsd:element name="customerSignatureUuid" type="xsd:string">
                            <xsd:annotation>
                                <xsd:documentation>UUID of file with customer signature</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignPadResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignPadConfirmRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignPadConfirmResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitBranchSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignPadConfirmRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateBranchSignRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignPadConfirmResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignAtBranchByPwdRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignAtBranchByPwdResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitBranchSignResponse">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignAtBranchByPwdRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateBranchSignRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignAtBranchByPwdResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignAtBranchBySmsRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSignAtBranchBySmsResponse">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitBranchSignResponse">
                    <xsd:sequence>
                        <xsd:element name="secondApplicantCuid" type="xsd:long" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>Customer ID of second applicant (disponent/cardholder/co-debtor).</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignAtBranchBySmsRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateBranchSignRequest">
                    <xsd:sequence>
                        <xsd:element name="secondApplicantCuid" type="xsd:long" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>Customer ID of second applicant (disponent/cardholder/co-debtor).</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateSignAtBranchBySmsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetInProgressApplicationsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerCuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Cuid of applicant, which we are uploading documents for</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="profileId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Profile ID</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="relationsToContract" type="xsd:string" minOccurs="1" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetInProgressApplicationsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="application" type="InProgressApplicationTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>All in progress applications</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitDocumentUploadRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                    <xsd:sequence>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitDocumentUploadResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseInitResponseMetadata"/>
                <xsd:element name="documentGroup" type="DocumentGroupTO" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>Collection of document groups</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationId" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Application ID, which we are uploading documents for</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contractType" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Contract completion type, which we are uploading documents for</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Envelope ID, which we are uploading documents for</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Cuid of applicant, which we are uploading documents for</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="identificationDocumentValidTo" type="xsd:date" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Primary identification document expiration date.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateDocumentUploadRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateFinalizationRequest">
                    <xsd:sequence>
                        <xsd:element name="uploadDocumentsPostpone" type="xsd:dateTime" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>Upload documents postponed to date</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="identificationDocumentValidTo" type="xsd:date" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>Primary identification document expiration date.</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                        <xsd:element name="document" type="DeliveredDocumentWithGroupTO" minOccurs="0" maxOccurs="unbounded">
                            <xsd:annotation>
                                <xsd:documentation>list of delivered documents to create/update with related document groups</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateDocumentUploadResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitDeclarationRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitDeclarationResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseInitResponseMetadata"/>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the application envelope (its main application will be finalized)</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="disponentOrCardHolderCompletionId" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Completion ID of disponent authorization application or card holder (if there is such application in envelope).
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateDeclarationRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateFinalizationRequest">
                    <xsd:sequence>
                        <xsd:element name="accDeclaration" type="xsd:boolean">
                            <xsd:annotation>
                                <xsd:documentation>If true acc declaration will be generated.</xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateDeclarationResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitNoDocumentUploadRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractInitFinalizationRequest">
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitNoDocumentUploadResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseInitResponseMetadata"/>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the application envelope (its main application will be finalized)</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationType" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>Type of current application to finalization</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the application in AMS</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="completionId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the contract completion (in OBS) to finalize</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateNoDocumentUploadRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="AbstractUpdateFinalizationRequest"/>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="UpdateNoDocumentUploadResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="metadata" type="appCommon:BaseUpdateResponseMetadata"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HasRequiredDocumentsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="applicationId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>ID of application to check required documents for.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HasRequiredDocumentsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="hasRequiredDocuments" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>Flag whether there are required documents for application from request.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="NewContractActivatedRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="generalContractType" type="appCommon:GeneralContractType">
                    <xsd:annotation>
                        <xsd:documentation>Client general contract type.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="generalContractCompletionId" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>Client general contract completion ID.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="applicationCreatedDate" type="xsd:date">
                    <xsd:annotation>
                        <xsd:documentation>Date of application creation.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contractActivationDate" type="xsd:date">
                    <xsd:annotation>
                        <xsd:documentation>Date of application activation.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of customer.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="envelopeId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of envelope</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="activationReason" type="ActivationReason">
                    <xsd:annotation>
                        <xsd:documentation>Reason of activation.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="identificationPayment" type="IdentificationPaymentTO" minOccurs="0">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identification Payment.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="NewContractActivatedResponse">
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="GetCompletionIdForEnvelopeRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="envelopeId" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Application envelope ID</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCompletionIdForEnvelopeResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="completionId" type="xsd:long" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:documentation>Contract completion ID</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCurrentTaskRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="finalizationId" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>Id of the application finalization process.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCurrentTaskResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="taskId" type="appCommon:TaskIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Id of the current task
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>
