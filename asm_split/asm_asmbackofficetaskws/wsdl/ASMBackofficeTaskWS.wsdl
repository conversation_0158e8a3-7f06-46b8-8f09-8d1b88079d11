<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/asm/ws/backoffice/task"
                  targetNamespace="http://airbank.cz/asm/ws/backoffice/task">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/asm/ws/backoffice/task">
            <xs:include schemaLocation="../xsd/BackofficeTaskWS.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="FinishManualTaskRequest">
        <wsdl:part element="FinishManualTaskRequest" name="FinishManualTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="FinishManualTaskResponse">
        <wsdl:part element="FinishManualTaskResponse" name="FinishManualTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="FinishManualTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FinishManualTaskFault"/>
    </wsdl:message>

    <wsdl:message name="FinishManualTaskWarningRequest">
        <wsdl:part element="FinishManualTaskWarningRequest" name="FinishManualTaskWarningRequest"/>
    </wsdl:message>
    <wsdl:message name="FinishManualTaskWarningResponse">
        <wsdl:part element="FinishManualTaskWarningResponse" name="FinishManualTaskWarningResponse"/>
    </wsdl:message>
    <wsdl:message name="FinishManualTaskWarningFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FinishManualTaskWarningFault"/>
    </wsdl:message>

    <wsdl:message name="PostponeManualTaskRequest">
        <wsdl:part element="PostponeManualTaskRequest" name="PostponeManualTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="PostponeManualTaskResponse">
        <wsdl:part element="PostponeManualTaskResponse" name="PostponeManualTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="PostponeManualTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="PostponeManualTaskFault"/>
    </wsdl:message>

    <wsdl:message name="CancelPostponeManualTaskRequest">
        <wsdl:part element="CancelPostponeManualTaskRequest" name="CancelPostponeManualTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelPostponeManualTaskResponse">
        <wsdl:part element="CancelPostponeManualTaskResponse" name="CancelPostponeManualTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelPostponeManualTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelPostponeManualTaskFault"/>
    </wsdl:message>

    <wsdl:message name="AssignCompletionTasksToOcrRequest">
        <wsdl:part element="AssignCompletionTasksToOcrRequest" name="AssignCompletionTasksToOcrRequest"/>
    </wsdl:message>
    <wsdl:message name="AssignCompletionTasksToOcrResponse">
        <wsdl:part element="AssignCompletionTasksToOcrResponse" name="AssignCompletionTasksToOcrResponse"/>
    </wsdl:message>
    <wsdl:message name="AssignCompletionTasksToOcrFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="AssignCompletionTasksToOcrFault"/>
    </wsdl:message>

    <wsdl:message name="AssignApplicationsRequest">
        <wsdl:part element="AssignApplicationsRequest" name="AssignApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="AssignApplicationsResponse">
        <wsdl:part element="AssignApplicationsResponse" name="AssignApplicationsResponse"/>
    </wsdl:message>
    <wsdl:message name="AssignApplicationsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="AssignApplicationsFault"/>
    </wsdl:message>

    <wsdl:message name="ChangeResultPropositionRequest">
        <wsdl:part element="ChangeResultPropositionRequest" name="ChangeResultPropositionRequest"/>
    </wsdl:message>
    <wsdl:message name="ChangeResultPropositionResponse">
        <wsdl:part element="ChangeResultPropositionResponse" name="ChangeResultPropositionResponse"/>
    </wsdl:message>
    <wsdl:message name="ChangeResultPropositionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ChangeResultPropositionFault"/>
    </wsdl:message>

    <wsdl:message name="GetFirstManualTaskInPersonalQueueRequest">
        <wsdl:part element="GetFirstManualTaskInPersonalQueueRequest" name="GetFirstManualTaskInPersonalQueueRequest"/>
    </wsdl:message>
    <wsdl:message name="GetFirstManualTaskInPersonalQueueResponse">
        <wsdl:part element="GetFirstManualTaskInPersonalQueueResponse" name="GetFirstManualTaskInPersonalQueueResponse"/>
    </wsdl:message>
    <wsdl:message name="GetFirstManualTaskInPersonalQueueFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetFirstManualTaskInPersonalQueueFault"/>
    </wsdl:message>

    <wsdl:message name="GetFirstManualTaskInPublicQueuesRequest">
        <wsdl:part element="GetFirstManualTaskInPublicQueuesRequest" name="GetFirstManualTaskInPublicQueuesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetFirstManualTaskInPublicQueuesResponse">
        <wsdl:part element="GetFirstManualTaskInPublicQueuesResponse" name="GetFirstManualTaskInPublicQueuesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetFirstManualTaskInPublicQueuesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetFirstManualTaskInPublicQueuesFault"/>
    </wsdl:message>

    <wsdl:message name="AssignManualTasksInCategoryRequest">
        <wsdl:part element="AssignManualTasksInCategoryRequest" name="AssignManualTasksInCategoryRequest"/>
    </wsdl:message>
    <wsdl:message name="AssignManualTasksInCategoryResponse">
        <wsdl:part element="AssignManualTasksInCategoryResponse" name="AssignManualTasksInCategoryResponse"/>
    </wsdl:message>
    <wsdl:message name="AssignManualTasksInCategoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="AssignManualTasksInCategoryFault"/>
    </wsdl:message>

    <wsdl:portType name="TaskPort">
        <wsdl:operation name="FinishManualTask">
            <wsdl:input message="FinishManualTaskRequest"/>
            <wsdl:output message="FinishManualTaskResponse"/>
            <wsdl:fault name="FinishManualTaskFault" message="FinishManualTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="FinishManualTaskWarning">
            <wsdl:input message="FinishManualTaskWarningRequest"/>
            <wsdl:output message="FinishManualTaskWarningResponse"/>
            <wsdl:fault name="FinishManualTaskWarningFault" message="FinishManualTaskWarningFault"/>
        </wsdl:operation>

        <wsdl:operation name="PostponeManualTask">
            <wsdl:input message="PostponeManualTaskRequest"/>
            <wsdl:output message="PostponeManualTaskResponse"/>
            <wsdl:fault name="PostponeManualTaskFault" message="PostponeManualTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="CancelPostponeManualTask">
            <wsdl:input message="CancelPostponeManualTaskRequest"/>
            <wsdl:output message="CancelPostponeManualTaskResponse"/>
            <wsdl:fault name="CancelPostponeManualTaskFault" message="CancelPostponeManualTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="AssignCompletionTasksToOcr">
            <wsdl:input message="AssignCompletionTasksToOcrRequest"/>
            <wsdl:output message="AssignCompletionTasksToOcrResponse"/>
            <wsdl:fault name="AssignCompletionTasksToOcrFault" message="AssignCompletionTasksToOcrFault"/>
        </wsdl:operation>

        <wsdl:operation name="AssignApplications">
            <wsdl:input message="AssignApplicationsRequest"/>
            <wsdl:output message="AssignApplicationsResponse"/>
            <wsdl:fault name="AssignApplicationsFault" message="AssignApplicationsFault"/>
        </wsdl:operation>

        <wsdl:operation name="ChangeResultProposition">
            <wsdl:input message="ChangeResultPropositionRequest"/>
            <wsdl:output message="ChangeResultPropositionResponse"/>
            <wsdl:fault name="ChangeResultPropositionFault" message="ChangeResultPropositionFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetFirstManualTaskInPersonalQueue">
            <wsdl:input message="GetFirstManualTaskInPersonalQueueRequest"/>
            <wsdl:output message="GetFirstManualTaskInPersonalQueueResponse"/>
            <wsdl:fault name="GetFirstManualTaskInPersonalQueueFault" message="GetFirstManualTaskInPersonalQueueFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetFirstManualTaskInPublicQueues">
            <wsdl:input message="GetFirstManualTaskInPublicQueuesRequest"/>
            <wsdl:output message="GetFirstManualTaskInPublicQueuesResponse"/>
            <wsdl:fault name="GetFirstManualTaskInPublicQueuesFault" message="GetFirstManualTaskInPublicQueuesFault"/>
        </wsdl:operation>

        <wsdl:operation name="AssignManualTasksInCategory">
            <wsdl:input message="AssignManualTasksInCategoryRequest"/>
            <wsdl:output message="AssignManualTasksInCategoryResponse"/>
            <wsdl:fault name="AssignManualTasksInCategoryFault" message="AssignManualTasksInCategoryFault"/>
        </wsdl:operation>
        
    </wsdl:portType>

    <wsdl:binding name="TaskBinding" type="TaskPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="FinishManualTask">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FinishManualTaskFault">
                <soap:fault name="FinishManualTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="FinishManualTaskWarning">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FinishManualTaskWarningFault">
                <soap:fault name="FinishManualTaskWarningFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="PostponeManualTask">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="PostponeManualTaskFault">
                <soap:fault name="PostponeManualTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CancelPostponeManualTask">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelPostponeManualTaskFault">
                <soap:fault name="CancelPostponeManualTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="AssignCompletionTasksToOcr">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="AssignCompletionTasksToOcrFault">
                <soap:fault name="AssignCompletionTasksToOcrFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="AssignApplications">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="AssignApplicationsFault">
                <soap:fault name="AssignApplicationsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ChangeResultProposition">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ChangeResultPropositionFault">
                <soap:fault name="ChangeResultPropositionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetFirstManualTaskInPersonalQueue">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetFirstManualTaskInPersonalQueueFault">
                <soap:fault name="GetFirstManualTaskInPersonalQueueFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetFirstManualTaskInPublicQueues">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetFirstManualTaskInPublicQueuesFault">
                <soap:fault name="GetFirstManualTaskInPublicQueuesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="AssignManualTasksInCategory">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="AssignManualTasksInCategoryFault">
                <soap:fault name="AssignManualTasksInCategoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="TaskService">
        <wsdl:port name="ASMTaskPort" binding="TaskBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ws/asm/backoffice/task"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
