<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns:botc="http://airbank.cz/asm/ws/backoffice/task/common"
           xmlns="http://airbank.cz/asm/ws/backoffice/task"
           targetNamespace="http://airbank.cz/asm/ws/backoffice/task">

    <xs:import namespace="http://airbank.cz/asm/ws/backoffice/task/common" schemaLocation="BackofficeTaskCommon.xsd"/>

    <xs:element name="FinishManualTaskRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Task ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="note" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Operator note</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="resultProposition" type="botc:ManualTaskResultTo" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Result proposition - OK / NOT OK</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="params" type="botc:ParamTo" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Specific params for specific manual task.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="FinishManualTaskResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="FinishManualTaskWarningRequest">
        <xs:annotation>
            <xs:documentation>
                Completes the manual activity warning - saves the results of warning, calculates the warningResult.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Manual task ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="warningResult" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The result of the warning. Filled by WARNING_ACTIVITY_RESULT codelist defined by MDM.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="operatorId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ID of the operator.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="FinishManualTaskWarningResponse">
        <xs:complexType>
            <!--No payload-->
        </xs:complexType>
    </xs:element>

    <xs:element name="PostponeManualTaskRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Task ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="validFrom" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Postponed to date(time)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="note" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Operator's note</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="reasonCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Reason why postponed</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="generatePostponeExpirationEvent" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>flag if postpone expiration event will be generared or not</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="PostponeManualTaskResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelPostponeManualTaskRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Task ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelPostponeManualTaskResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignCompletionTasksToOcrRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignCompletionTasksToOcrResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="UnassignCompletionTasksFromOcrRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UnassignCompletionTasksFromOcrResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignApplicationsRequest">
        <xs:annotation>
            <xs:documentation>Assign active tasks in applications for operator</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="operatorId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>LDAP id of operator</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of applications to assign</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="assignReason" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The assign reason from the code list (not null in the case the operator = the current operator)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="assignReasonDescription" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The assign reason description</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="generateAssignmentEvent" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>If true then generate an assignment event</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignApplicationsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of application id that was not assign</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ChangeResultPropositionRequest">
        <xs:annotation>
            <xs:documentation>Change the result proposition on the specified task, with possibility to finish the task</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Task ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="resultProposition" type="botc:ManualTaskResultTo" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Result proposition - OK / NOT OK</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ChangeResultPropositionResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFirstManualTaskInPersonalQueueRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="operatorId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Operator ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFirstManualTaskInPersonalQueueResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="manualTask" type="botc:ManualTaskTO" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFirstManualTaskInPublicQueuesRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="operatorId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Operator ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFirstManualTaskInPublicQueuesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="manualTask" type="botc:ManualTaskTO" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignManualTasksInCategoryRequest">
        <xs:annotation>
            <xs:documentation>
                Assigns all tasks in category derived from manual task provided by manualTaskId.
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="manualTaskId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Task ID derive category from</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="operatorId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Operator ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignManualTasksInCategoryResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

</xs:schema>
