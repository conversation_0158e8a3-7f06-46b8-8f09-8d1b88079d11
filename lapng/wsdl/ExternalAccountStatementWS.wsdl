<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/lap/ws/externalaccountstatement"
                  targetNamespace="http://airbank.cz/lap/ws/externalaccountstatement">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/lap/ws/externalaccountstatement">
            <xsd:include schemaLocation="../xsd/ExternalAccountStatementWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="FetchAndInterpretAccountStatementResponse">
        <wsdl:part element="FetchAndInterpretAccountStatementResponse" name="FetchAndInterpretAccountStatementResponse"/>
    </wsdl:message>
    <wsdl:message name="FetchAndInterpretAccountStatementRequest">
        <wsdl:part element="FetchAndInterpretAccountStatementRequest" name="FetchAndInterpretAccountStatementRequest"/>
    </wsdl:message>
    <wsdl:message name="FetchAndInterpretAccountStatementFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FetchAndInterpretAccountStatementFault"/>
    </wsdl:message>

    <wsdl:message name="InterpretAccountStatementResponse">
        <wsdl:part element="InterpretAccountStatementResponse" name="InterpretAccountStatementResponse"/>
    </wsdl:message>
    <wsdl:message name="InterpretAccountStatementRequest">
        <wsdl:part element="InterpretAccountStatementRequest" name="InterpretAccountStatementRequest"/>
    </wsdl:message>
    <wsdl:message name="InterpretAccountStatementFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InterpretAccountStatementFault"/>
    </wsdl:message>

    <wsdl:portType name="ExternalAccountStatementWS">
        <wsdl:operation name="FetchAndInterpretAccountStatement">
            <wsdl:input message="FetchAndInterpretAccountStatementRequest"/>
            <wsdl:output message="FetchAndInterpretAccountStatementResponse"/>
            <wsdl:fault name="FetchAndInterpretAccountStatementFault" message="FetchAndInterpretAccountStatementFault"/>
        </wsdl:operation>
        <wsdl:operation name="InterpretAccountStatement">
            <wsdl:input message="InterpretAccountStatementRequest"/>
            <wsdl:output message="InterpretAccountStatementResponse"/>
            <wsdl:fault name="InterpretAccountStatementFault" message="InterpretAccountStatementFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ExternalAccountStatementBinding" type="ExternalAccountStatementWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="FetchAndInterpretAccountStatement">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FetchAndInterpretAccountStatementFault">
                <soap:fault name="FetchAndInterpretAccountStatementFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="InterpretAccountStatement">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InterpretAccountStatementFault">
                <soap:fault name="InterpretAccountStatementFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="ExternalAccountStatementWS">
        <wsdl:port binding="ExternalAccountStatementBinding" name="ExternalAccountStatementPort">
            <soap:address location="https://lap.banka.hci/lap/ext/ExternalAccountStatementWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>