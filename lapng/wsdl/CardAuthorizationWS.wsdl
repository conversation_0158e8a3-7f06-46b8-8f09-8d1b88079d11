<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:aph="http://airbank.cz/lap/ws/antiphishing"
                  xmlns:tns="http://airbank.cz/lap/ws/cardauth"
                  xmlns="http://airbank.cz/lap/ws/cardauth"
                  targetNamespace="http://airbank.cz/lap/ws/cardauth">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/lap/ws/antiphishing">
            <xsd:include schemaLocation="../xsd/AntiphishingWS.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="ApproveCardAuthorizationResponse">
        <wsdl:part element="aph:ApproveCardAuthorizationResponse" name="ApproveCardAuthorizationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveCardAuthorizationRequest">
        <wsdl:part element="aph:ApproveCardAuthorizationRequest" name="ApproveCardAuthorizationRequest"/>
    </wsdl:message>

    <wsdl:portType name="AntiphishingWS">
        <wsdl:operation name="ApproveCardAuthorization">
            <wsdl:input message="ApproveCardAuthorizationRequest"/>
            <wsdl:output message="ApproveCardAuthorizationResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="AntiphishingWSBinding" type="tns:AntiphishingWS">
        <soap:binding style="document"
                      transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="ApproveCardAuthorization">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="AntiphishingWS">
        <wsdl:port binding="tns:AntiphishingWSBinding" name="AntiphishingWSBinding">
            <soap:address location="https://lap.banka.hci/lap/ext/ApprovalProcessWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
