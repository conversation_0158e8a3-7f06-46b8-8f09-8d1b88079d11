<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/lap/ws/antiphishing"
                  targetNamespace="http://airbank.cz/lap/ws/antiphishing">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/lap/ws/antiphishing">
            <xsd:include schemaLocation="../xsd/AntiphishingWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="ApproveLoginEventResponse">
        <wsdl:part element="ApproveLoginEventResponse" name="ApproveLoginEventResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveLoginEventRequest">
        <wsdl:part element="ApproveLoginEventRequest" name="ApproveLoginEventRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveLoginEventFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveLoginEventFault"/>
    </wsdl:message>

    <wsdl:message name="ApprovePaymentOrderResponse">
        <wsdl:part element="ApprovePaymentOrderResponse" name="ApprovePaymentOrderResponse"/>
    </wsdl:message>
    <wsdl:message name="ApprovePaymentOrderRequest">
        <wsdl:part element="ApprovePaymentOrderRequest" name="ApprovePaymentOrderRequest"/>
    </wsdl:message>
    <wsdl:message name="ApprovePaymentOrderFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApprovePaymentOrderFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveBatchPaymentsResponse">
        <wsdl:part element="ApproveBatchPaymentsResponse" name="ApproveBatchPaymentsResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveBatchPaymentsRequest">
        <wsdl:part element="ApproveBatchPaymentsRequest" name="ApproveBatchPaymentsRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveBatchPaymentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveBatchPaymentsFault"/>
    </wsdl:message>

    <wsdl:message name="HandlePhishingOffenceResponse">
        <wsdl:part element="HandlePhishingOffenceResponse" name="HandlePhishingOffenceResponse"/>
    </wsdl:message>
    <wsdl:message name="HandlePhishingOffenceRequest">
        <wsdl:part element="HandlePhishingOffenceRequest" name="HandlePhishingOffenceRequest"/>
    </wsdl:message>
    <wsdl:message name="HandlePhishingOffenceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveLoginEventFault"/>
    </wsdl:message>

    <wsdl:message name="VerifyPhishingOffenceResponse">
        <wsdl:part element="VerifyPhishingOffenceResponse" name="VerifyPhishingOffenceResponse"/>
    </wsdl:message>
    <wsdl:message name="VerifyPhishingOffenceRequest">
        <wsdl:part element="VerifyPhishingOffenceRequest" name="VerifyPhishingOffenceRequest"/>
    </wsdl:message>
    <wsdl:message name="VerifyPhishingOffenceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveLoginEventFault"/>
    </wsdl:message>

    <wsdl:message name="HandleSuspiciousLoginEventResponse">
        <wsdl:part element="HandleSuspiciousLoginEventResponse" name="HandleSuspiciousLoginEventResponse"/>
    </wsdl:message>
    <wsdl:message name="HandleSuspiciousLoginEventRequest">
        <wsdl:part element="HandleSuspiciousLoginEventRequest" name="HandleSuspiciousLoginEventRequest"/>
    </wsdl:message>
    <wsdl:message name="HandleSuspiciousLoginEventFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveLoginEventFault"/>
    </wsdl:message>

    <wsdl:message name="HandleSuspiciousPaymentOrderEventResponse">
        <wsdl:part element="HandleSuspiciousPaymentOrderEventResponse" name="HandleSuspiciousPaymentOrderEventResponse"/>
    </wsdl:message>
    <wsdl:message name="HandleSuspiciousPaymentOrderEventRequest">
        <wsdl:part element="HandleSuspiciousPaymentOrderEventRequest" name="HandleSuspiciousPaymentOrderEventRequest"/>
    </wsdl:message>
    <wsdl:message name="HandleSuspiciousPaymentOrderEventFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApprovePaymentOrderFault"/>
    </wsdl:message>

    <wsdl:message name="HandleSuspiciousAuthFacePictureResponse">
        <wsdl:part element="HandleSuspiciousAuthFacePictureResponse" name="HandleSuspiciousAuthFacePictureResponse"/>
    </wsdl:message>
    <wsdl:message name="HandleSuspiciousAuthFacePictureRequest">
        <wsdl:part element="HandleSuspiciousAuthFacePictureRequest" name="HandleSuspiciousAuthFacePictureRequest"/>
    </wsdl:message>
    <wsdl:message name="HandleSuspiciousAuthFacePictureFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveLoginEventFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveDevicePairingResponse">
        <wsdl:part element="ApproveDevicePairingResponse" name="ApproveDevicePairingResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveDevicePairingRequest">
        <wsdl:part element="ApproveDevicePairingRequest" name="ApproveDevicePairingRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveDevicePairingFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveDevicePairingFault"/>
    </wsdl:message>

    <wsdl:message name="HandleNotifyClientResponse">
        <wsdl:part element="HandleNotifyClientResponse" name="HandleNotifyClientResponse"/>
    </wsdl:message>
    <wsdl:message name="HandleNotifyClientRequest">
        <wsdl:part element="HandleNotifyClientRequest" name="HandleNotifyClientRequest"/>
    </wsdl:message>
    <wsdl:message name="HandleNotifyClientFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="HandleNotifyClientFault"/>
    </wsdl:message>

    <wsdl:message name="HandleVerifyClientResponse">
        <wsdl:part element="HandleVerifyClientResponse" name="HandleVerifyClientResponse"/>
    </wsdl:message>
    <wsdl:message name="HandleVerifyClientRequest">
        <wsdl:part element="HandleVerifyClientRequest" name="HandleVerifyClientRequest"/>
    </wsdl:message>

    <wsdl:message name="HandleVerifyClientFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="HandleVerifyClientFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveDeviceAccessResponse">
        <wsdl:part element="ApproveDeviceAccessResponse" name="ApproveDeviceAccessResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveDeviceAccessRequest">
        <wsdl:part element="ApproveDeviceAccessRequest" name="ApproveDeviceAccessRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveDeviceAccessFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApproveDeviceAccessFault"/>
    </wsdl:message>

    <wsdl:portType name="AntiphishingWS">
        <wsdl:operation name="ApproveLoginEvent">
            <wsdl:input message="ApproveLoginEventRequest"/>
            <wsdl:output message="ApproveLoginEventResponse"/>
            <wsdl:fault name="ApproveLoginEventFault" message="ApproveLoginEventFault"/>
        </wsdl:operation>
        <wsdl:operation name="ApprovePaymentOrder">
            <wsdl:input message="ApprovePaymentOrderRequest"/>
            <wsdl:output message="ApprovePaymentOrderResponse"/>
            <wsdl:fault name="ApprovePaymentOrderFault" message="ApprovePaymentOrderFault"/>
        </wsdl:operation>
        <wsdl:operation name="ApproveBatchPayments">
            <wsdl:input message="ApproveBatchPaymentsRequest"/>
            <wsdl:output message="ApproveBatchPaymentsResponse"/>
            <wsdl:fault name="ApproveBatchPaymentsFault" message="ApproveBatchPaymentsFault"/>
        </wsdl:operation>
        <wsdl:operation name="handlePhishingOffence">
            <wsdl:input message="HandlePhishingOffenceRequest"/>
            <wsdl:output message="HandlePhishingOffenceResponse"/>
            <wsdl:fault name="HandlePhishingOffenceFault" message="HandlePhishingOffenceFault"/>
        </wsdl:operation>
        <wsdl:operation name="VerifyPhishingOffence">
            <wsdl:input message="VerifyPhishingOffenceRequest"/>
            <wsdl:output message="VerifyPhishingOffenceResponse"/>
            <wsdl:fault name="VerifyPhishingOffenceFault" message="VerifyPhishingOffenceFault"/>
        </wsdl:operation>
        <wsdl:operation name="HandleSuspiciousLoginEvent">
            <wsdl:input message="HandleSuspiciousLoginEventRequest"/>
            <wsdl:output message="HandleSuspiciousLoginEventResponse"/>
            <wsdl:fault name="HandleSuspiciousLoginEventFault" message="HandleSuspiciousLoginEventFault"/>
        </wsdl:operation>
        <wsdl:operation name="HandleSuspiciousPaymentOrderEvent">
            <wsdl:input message="HandleSuspiciousPaymentOrderEventRequest"/>
            <wsdl:output message="HandleSuspiciousPaymentOrderEventResponse"/>
            <wsdl:fault name="HandleSuspiciousPaymentOrderEventFault" message="HandleSuspiciousPaymentOrderEventFault"/>
        </wsdl:operation>
        <wsdl:operation name="HandleSuspiciousAuthFacePicture">
            <wsdl:input message="HandleSuspiciousAuthFacePictureRequest"/>
            <wsdl:output message="HandleSuspiciousAuthFacePictureResponse"/>
            <wsdl:fault name="HandleSuspiciousAuthFacePictureFault" message="HandleSuspiciousAuthFacePictureFault"/>
        </wsdl:operation>
        <wsdl:operation name="ApproveDevicePairing">
            <wsdl:input message="ApproveDevicePairingRequest"/>
            <wsdl:output message="ApproveDevicePairingResponse"/>
            <wsdl:fault name="ApproveDevicePairingFault" message="ApproveDevicePairingFault"/>
        </wsdl:operation>
        <wsdl:operation name="HandleNotifyClient">
            <wsdl:input message="HandleNotifyClientRequest"/>
            <wsdl:output message="HandleNotifyClientResponse"/>
            <wsdl:fault name="HandleNotifyClientFault" message="HandleNotifyClientFault"/>
        </wsdl:operation>
        <wsdl:operation name="HandleVerifyClient">
            <wsdl:input message="HandleVerifyClientRequest"/>
            <wsdl:output message="HandleVerifyClientResponse"/>
            <wsdl:fault name="HandleVerifyClientFault" message="HandleVerifyClientFault"/>
        </wsdl:operation>
        <wsdl:operation name="ApproveDeviceAccess">
            <wsdl:input message="ApproveDeviceAccessRequest"/>
            <wsdl:output message="ApproveDeviceAccessResponse"/>
            <wsdl:fault name="ApproveDeviceAccessFault" message="ApproveDeviceAccessFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="AntiphishingBinding" type="AntiphishingWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="ApproveLoginEvent">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveLoginEventFault">
                <soap:fault name="ApproveLoginEventFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApprovePaymentOrder">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApprovePaymentOrderFault">
                <soap:fault name="ApprovePaymentOrderFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveBatchPayments">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveBatchPaymentsFault">
                <soap:fault name="ApproveBatchPaymentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="handlePhishingOffence">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HandlePhishingOffenceFault">
                <soap:fault name="HandlePhishingOffenceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="VerifyPhishingOffence">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="VerifyPhishingOffenceFault">
                <soap:fault name="VerifyPhishingOffenceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HandleSuspiciousLoginEvent">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HandleSuspiciousLoginEventFault">
                <soap:fault name="HandleSuspiciousLoginEventFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HandleSuspiciousPaymentOrderEvent">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HandleSuspiciousPaymentOrderEventFault">
                <soap:fault name="HandleSuspiciousPaymentOrderEventFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HandleSuspiciousAuthFacePicture">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HandleSuspiciousAuthFacePictureFault">
                <soap:fault name="HandleSuspiciousAuthFacePictureFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveDevicePairing">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveDevicePairingFault">
                <soap:fault name="ApproveDevicePairingFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HandleNotifyClient">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HandleNotifyClientFault">
                <soap:fault name="HandleNotifyClientFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HandleVerifyClient">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HandleVerifyClientFault">
                <soap:fault name="HandleVerifyClientFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveDeviceAccess">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveDeviceAccessFault">
                <soap:fault name="ApproveDeviceAccessFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="AntiphishingWSBinding">
        <wsdl:port binding="AntiphishingBinding" name="ApprovalProcessBinding">
            <soap:address location="https://lap.banka.hci/lap/ext/ApprovalProcessWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>