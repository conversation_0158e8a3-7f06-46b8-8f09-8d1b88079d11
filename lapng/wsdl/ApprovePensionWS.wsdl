<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/lap/ws/approvepension"
                  targetNamespace="http://airbank.cz/lap/ws/approvepension">

    <wsdl:types>
        <xsd:schema targetNamespace="http://airbank.cz/lap/ws/approvepension">
            <xsd:include schemaLocation="../xsd/ApprovePension.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="ApprovePensionRequest">
        <wsdl:part element="ApprovePensionRequest" name="ApprovePensionRequest"/>
    </wsdl:message>
    <wsdl:message name="ApprovePensionResponse">
        <wsdl:part element="ApprovePensionResponse" name="ApprovePensionResponse"/>
    </wsdl:message>
    <wsdl:message name="ApprovePensionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ApprovePensionFault"/>
    </wsdl:message>

    <wsdl:portType name="ApprovePensionWS">
        <wsdl:operation name="ApprovePension">
            <wsdl:input message="ApprovePensionRequest"/>
            <wsdl:output message="ApprovePensionResponse"/>
            <wsdl:fault name="ApprovePensionFault" message="ApprovePensionFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ApprovePensionBinding" type="ApprovePensionWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="ApprovePension">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ApprovePensionWS">
        <wsdl:port binding="ApprovePensionBinding" name="ApprovePensionWS">
            <soap:address location="https://airbank.cz/lap/ws/approvepension"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
