<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:common="http://homecredit.net/lap/ws/approvalprocess/common"
                  xmlns="http://homecredit.net/lap/ws/approvalprocess/approvalprocessws"
                  targetNamespace="http://homecredit.net/lap/ws/approvalprocess/approvalprocessws">

    <wsdl:types>
        <xsd:schema targetNamespace="http://homecredit.net/lap/ws/approvalprocess/approvalprocessws">
            <xsd:include schemaLocation="../xsd/ApprovalProcessWS.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="ApproveNewGeneralContractResponse">
        <wsdl:part element="ApproveNewGeneralContractResponse" name="ApproveNewGeneralContractResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveNewGeneralContractRequest">
        <wsdl:part element="ApproveNewGeneralContractRequest" name="ApproveNewGeneralContractRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveNewGeneralContractFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveNewGeneralContractFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveLoanApplicationResponse">
        <wsdl:part element="ApproveLoanApplicationResponse" name="ApproveLoanApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveLoanApplicationRequest">
        <wsdl:part element="ApproveLoanApplicationRequest" name="ApproveLoanApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveLoanApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveLoanApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveCurrentAccountApplicationResponse">
        <wsdl:part element="ApproveCurrentAccountApplicationResponse" name="ApproveCurrentAccountApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveCurrentAccountApplicationRequest">
        <wsdl:part element="ApproveCurrentAccountApplicationRequest" name="ApproveCurrentAccountApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveCurrentAccountApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveCurrentAccountApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveSavingAccountApplicationResponse">
        <wsdl:part element="ApproveSavingAccountApplicationResponse" name="ApproveSavingAccountApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveSavingAccountApplicationRequest">
        <wsdl:part element="ApproveSavingAccountApplicationRequest" name="ApproveSavingAccountApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveSavingAccountApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveSavingAccountApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveChildSavingAccountApplicationResponse">
        <wsdl:part element="ApproveChildSavingAccountApplicationResponse" name="ApproveChildSavingAccountApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveChildSavingAccountApplicationRequest">
        <wsdl:part element="ApproveChildSavingAccountApplicationRequest" name="ApproveChildSavingAccountApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveChildSavingAccountApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveChildSavingAccountApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveNewPersonResponse">
        <wsdl:part element="ApproveNewPersonResponse" name="ApproveNewPersonResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveNewPersonRequest">
        <wsdl:part element="ApproveNewPersonRequest" name="ApproveNewPersonRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveNewPersonFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveNewPersonFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveDebitCardResponse">
        <wsdl:part element="ApproveDebitCardResponse" name="ApproveDebitCardResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveDebitCardRequest">
        <wsdl:part element="ApproveDebitCardRequest" name="ApproveDebitCardRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveDebitCardFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveDebitCardFault"/>
    </wsdl:message>

    <wsdl:message name="VerifyDocumentResponse">
        <wsdl:part element="VerifyDocumentResponse" name="VerifyDocumentResponse"/>
    </wsdl:message>
    <wsdl:message name="VerifyDocumentRequest">
        <wsdl:part element="VerifyDocumentRequest" name="VerifyDocumentRequest"/>
    </wsdl:message>
    <wsdl:message name="VerifyDocumentFault">
        <wsdl:part element="common:coreFaultElement" name="VerifyDocumentFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveChangeLoanResponse">
        <wsdl:part element="ApproveChangeLoanResponse" name="ApproveChangeLoanResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveChangeLoanRequest">
        <wsdl:part element="ApproveChangeLoanRequest" name="ApproveChangeLoanRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveChangeLoanFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveChangeLoanFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveInvestmentCertificateApplicationResponse">
        <wsdl:part element="ApproveInvestmentCertificateApplicationResponse" name="ApproveInvestmentCertificateApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveInvestmentCertificateApplicationRequest">
        <wsdl:part element="ApproveInvestmentCertificateApplicationRequest" name="ApproveInvestmentCertificateApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveInvestmentCertificateApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveInvestmentCertificateApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="CancelApprovalProcessResponse">
        <wsdl:part element="CancelApprovalProcessResponse" name="CancelApprovalProcessResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="CancelApprovalProcessRequest">
        <wsdl:part element="CancelApprovalProcessRequest" name="CancelApprovalProcessRequest">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="ApproveDocumentInCompletionResponse">
        <wsdl:part element="ApproveDocumentInCompletionResponse" name="ApproveDocumentInCompletionResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveDocumentInCompletionRequest">
        <wsdl:part element="ApproveDocumentInCompletionRequest" name="ApproveDocumentInCompletionRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveDocumentInCompletionFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveDocumentInCompletionFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveApplicationInCompletionResponse">
        <wsdl:part element="ApproveApplicationInCompletionResponse" name="ApproveApplicationInCompletionResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveApplicationInCompletionRequest">
        <wsdl:part element="ApproveApplicationInCompletionRequest" name="ApproveApplicationInCompletionRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveApplicationInCompletionFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveApplicationInCompletionFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveMortgageApplicationResponse">
        <wsdl:part element="ApproveMortgageApplicationResponse" name="ApproveMortgageApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveMortgageApplicationRequest">
        <wsdl:part element="ApproveMortgageApplicationRequest" name="ApproveMortgageApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveMortgageApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveMortgageApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveMortgageRefApplicationResponse">
        <wsdl:part element="ApproveMortgageRefApplicationResponse" name="ApproveMortgageRefApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveMortgageRefApplicationRequest">
        <wsdl:part element="ApproveMortgageRefApplicationRequest" name="ApproveMortgageRefApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveMortgageRefApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveMortgageRefApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveOverdraftApplicationResponse">
        <wsdl:part element="ApproveOverdraftApplicationResponse" name="ApproveOverdraftApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveOverdraftApplicationRequest">
        <wsdl:part element="ApproveOverdraftApplicationRequest" name="ApproveOverdraftApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveOverdraftApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveOverdraftApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveStockEtfApplicationResponse">
        <wsdl:part element="ApproveStockEtfApplicationResponse" name="ApproveStockEtfApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveStockEtfApplicationRequest">
        <wsdl:part element="ApproveStockEtfApplicationRequest" name="ApproveStockEtfApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveStockEtfApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveStockEtfApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ProcessCompletionPreDrawnCheckResponse">
        <wsdl:part element="ProcessCompletionPreDrawnCheckResponse" name="ProcessCompletionPreDrawnCheckResponse"/>
    </wsdl:message>
    <wsdl:message name="ProcessCompletionPreDrawnCheckRequest">
        <wsdl:part element="ProcessCompletionPreDrawnCheckRequest" name="ProcessCompletionPreDrawnCheckRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessCompletionPreDrawnCheckFault">
        <wsdl:part element="common:coreFaultElement" name="ProcessCompletionPreDrawnCheckFault"/>
    </wsdl:message>

    <wsdl:message name="ApproveTermDepositApplicationResponse">
        <wsdl:part element="ApproveTermDepositApplicationResponse" name="ApproveTermDepositApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ApproveTermDepositApplicationRequest">
        <wsdl:part element="ApproveTermDepositApplicationRequest" name="ApproveTermDepositApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ApproveTermDepositApplicationFault">
        <wsdl:part element="common:coreFaultElement" name="ApproveTermDepositApplicationFault"/>
    </wsdl:message>

    <wsdl:portType name="LAPApprovalProcessWS">
        <wsdl:operation name="ApproveNewGeneralContract">
            <wsdl:input message="ApproveNewGeneralContractRequest"/>
            <wsdl:output message="ApproveNewGeneralContractResponse"/>
            <wsdl:fault name="ApproveNewGeneralContractFault" message="ApproveNewGeneralContractFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveLoanApplication">
            <wsdl:input message="ApproveLoanApplicationRequest"/>
            <wsdl:output message="ApproveLoanApplicationResponse"/>
            <wsdl:fault name="ApproveLoanApplicationFault" message="ApproveLoanApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveCurrentAccountApplication">
            <wsdl:input message="ApproveCurrentAccountApplicationRequest"/>
            <wsdl:output message="ApproveCurrentAccountApplicationResponse"/>
            <wsdl:fault name="ApproveCurrentAccountApplicationFault" message="ApproveCurrentAccountApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveSavingAccountApplication">
            <wsdl:input message="ApproveSavingAccountApplicationRequest"/>
            <wsdl:output message="ApproveSavingAccountApplicationResponse"/>
            <wsdl:fault name="ApproveSavingAccountApplicationFault" message="ApproveSavingAccountApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveChildSavingAccountApplication">
            <wsdl:input message="ApproveChildSavingAccountApplicationRequest"/>
            <wsdl:output message="ApproveChildSavingAccountApplicationResponse"/>
            <wsdl:fault name="ApproveChildSavingAccountApplicationFault" message="ApproveChildSavingAccountApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveNewPerson">
            <wsdl:input message="ApproveNewPersonRequest"/>
            <wsdl:output message="ApproveNewPersonResponse"/>
            <wsdl:fault name="ApproveNewPersonFault" message="ApproveNewPersonFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveDebitCard">
            <wsdl:input message="ApproveDebitCardRequest"/>
            <wsdl:output message="ApproveDebitCardResponse"/>
            <wsdl:fault name="ApproveDebitCardFault" message="ApproveDebitCardFault"/>
        </wsdl:operation>

        <wsdl:operation name="VerifyDocument">
            <wsdl:input message="VerifyDocumentRequest"/>
            <wsdl:output message="VerifyDocumentResponse"/>
            <wsdl:fault name="VerifyDocumentFault" message="VerifyDocumentFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveChangeLoan">
            <wsdl:input message="ApproveChangeLoanRequest"/>
            <wsdl:output message="ApproveChangeLoanResponse"/>
            <wsdl:fault name="ApproveChangeLoanFault" message="ApproveChangeLoanFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveInvestmentCertificateApplication">
            <wsdl:input message="ApproveInvestmentCertificateApplicationRequest"/>
            <wsdl:output message="ApproveInvestmentCertificateApplicationResponse"/>
            <wsdl:fault name="ApproveInvestmentCertificateApplicationFault" message="ApproveInvestmentCertificateApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="CancelApprovalProcess">
            <wsdl:input message="CancelApprovalProcessRequest" name="CancelApprovalProcessRequest">
            </wsdl:input>
            <wsdl:output message="CancelApprovalProcessResponse" name="CancelApprovalProcessResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="ApproveDocumentInCompletion">
            <wsdl:input message="ApproveDocumentInCompletionRequest"/>
            <wsdl:output message="ApproveDocumentInCompletionResponse"/>
            <wsdl:fault name="ApproveDocumentInCompletionFault" message="ApproveDocumentInCompletionFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveApplicationInCompletion">
            <wsdl:input message="ApproveApplicationInCompletionRequest"/>
            <wsdl:output message="ApproveApplicationInCompletionResponse"/>
            <wsdl:fault name="ApproveApplicationInCompletionFault" message="ApproveApplicationInCompletionFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveMortgageApplication">
            <wsdl:input message="ApproveMortgageApplicationRequest"/>
            <wsdl:output message="ApproveMortgageApplicationResponse"/>
            <wsdl:fault name="ApproveMortgageApplicationFault" message="ApproveMortgageApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveMortgageRefApplication">
            <wsdl:input message="ApproveMortgageRefApplicationRequest"/>
            <wsdl:output message="ApproveMortgageRefApplicationResponse"/>
            <wsdl:fault name="ApproveMortgageRefApplicationFault" message="ApproveMortgageRefApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveOverdraftApplication">
            <wsdl:input message="ApproveOverdraftApplicationRequest"/>
            <wsdl:output message="ApproveOverdraftApplicationResponse"/>
            <wsdl:fault name="ApproveOverdraftApplicationFault" message="ApproveOverdraftApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveStockEtfApplication">
            <wsdl:input message="ApproveStockEtfApplicationRequest"/>
            <wsdl:output message="ApproveStockEtfApplicationResponse"/>
            <wsdl:fault name="ApproveStockEtfApplicationFault" message="ApproveStockEtfApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ProcessCompletionPreDrawnCheck">
            <wsdl:input message="ProcessCompletionPreDrawnCheckRequest"/>
            <wsdl:output message="ProcessCompletionPreDrawnCheckResponse"/>
            <wsdl:fault name="ProcessCompletionPreDrawnCheckFault" message="ProcessCompletionPreDrawnCheckFault"/>
        </wsdl:operation>

        <wsdl:operation name="ApproveTermDepositApplication">
            <wsdl:input message="ApproveTermDepositApplicationRequest"/>
            <wsdl:output message="ApproveTermDepositApplicationResponse"/>
            <wsdl:fault name="ApproveTermDepositApplicationFault" message="ApproveTermDepositApplicationFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="InvokeApprovalProcessBinding" type="LAPApprovalProcessWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="ApproveNewGeneralContract">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveNewGeneralContractFault">
                <soap:fault name="ApproveNewGeneralContractFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveLoanApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveLoanApplicationFault">
                <soap:fault name="ApproveLoanApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveCurrentAccountApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveCurrentAccountApplicationFault">
                <soap:fault name="ApproveCurrentAccountApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveSavingAccountApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveSavingAccountApplicationFault">
                <soap:fault name="ApproveSavingAccountApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveChildSavingAccountApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveChildSavingAccountApplicationFault">
                <soap:fault name="ApproveChildSavingAccountApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveNewPerson">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveNewPersonFault">
                <soap:fault name="ApproveNewPersonFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveDebitCard">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveDebitCardFault">
                <soap:fault name="ApproveDebitCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="VerifyDocument">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="VerifyDocumentFault">
                <soap:fault name="VerifyDocumentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveChangeLoan">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveChangeLoanFault">
                <soap:fault name="ApproveChangeLoanFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveInvestmentCertificateApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveInvestmentCertificateApplicationFault">
                <soap:fault name="ApproveInvestmentCertificateApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CancelApprovalProcess">
            <soap:operation soapAction=""/>
            <wsdl:input name="CancelApprovalProcessRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="CancelApprovalProcessResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="ApproveDocumentInCompletion">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveDocumentInCompletionFault">
                <soap:fault name="ApproveDocumentInCompletionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveApplicationInCompletion">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveApplicationInCompletionFault">
                <soap:fault name="ApproveApplicationInCompletionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveMortgageApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveMortgageApplicationFault">
                <soap:fault name="ApproveMortgageApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveMortgageRefApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveMortgageRefApplicationFault">
                <soap:fault name="ApproveMortgageRefApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveOverdraftApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <!--<soap:header message="bc:businessContext" part="parameters" use="literal" />
                <soap:header message="tc:technicalContext" part="parameters" use="literal" />-->
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <!--<soap:header message="tc:technicalContext" part="parameters" use="literal" />-->
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveOverdraftApplicationFault">
                <soap:fault name="ApproveOverdraftApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveStockEtfApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveStockEtfApplicationFault">
                <soap:fault name="ApproveStockEtfApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ProcessCompletionPreDrawnCheck">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ProcessCompletionPreDrawnCheckFault">
                <soap:fault name="ProcessCompletionPreDrawnCheckFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ApproveTermDepositApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ApproveTermDepositApplicationFault">
                <soap:fault name="ApproveTermDepositApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="ApprovalProcessBinding">
        <wsdl:port binding="InvokeApprovalProcessBinding" name="ApprovalProcessBinding">
            <soap:address location="https://lap.banka.hci/lap/ext/ApprovalProcessWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
