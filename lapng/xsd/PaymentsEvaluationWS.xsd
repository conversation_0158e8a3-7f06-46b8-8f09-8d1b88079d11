<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
        targetNamespace="http://airbank.cz/lap/ws/paymentsEvaluation" xmlns="http://airbank.cz/lap/ws/paymentsEvaluation"
        jxb:version="2.1" elementFormDefault="qualified">

    <xsd:element name="PaymentRiskCategorizationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="idProcess" type="xsd:long"/>
                <xsd:element name="envelopeId" type="xsd:long" minOccurs="0"/>
                <xsd:element name="applicationId" type="xsd:long" minOccurs="0"/>
                <xsd:element name="cuid" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="PaymentRiskCategorizationResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="PaymentGroupEvaluationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="idProcess" type="xsd:long"/>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="envelopeId" type="xsd:long" minOccurs="0"/>
                <xsd:element name="applicationId" type="xsd:long" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="PaymentGroupEvaluationResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>