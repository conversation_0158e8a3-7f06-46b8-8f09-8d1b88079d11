<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
        targetNamespace="http://airbank.cz/lap/ws/antiphishing" xmlns="http://airbank.cz/lap/ws/antiphishing"
        jxb:version="3.0" elementFormDefault="qualified">

    <xsd:element name="ApproveLoginEventRequest">
        <xsd:annotation>
            <xsd:documentation>Approve new login request.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApproveLoginEventResponse">
        <xsd:annotation>
            <xsd:documentation>Approve new login response.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="decision" type="Decision" maxOccurs="unbounded"/>
                <xsd:element name="nextActivity" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="businessProcessEvent" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="Decision">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ obsahující informace o rozhodnutí pro daný ApprovalSubject.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="approvalSubject" type="ApprovalSubject"/>
            <xsd:element name="result" type="Result"/>
            <xsd:element name="receiptId" type="xsd:string"/>
            <xsd:element name="reason" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ApprovalSubject">
        <xsd:annotation>
            <xsd:documentation>Enumerace definující předmět schvalování v rámci antiphishingu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="LOGIN_EVENT"/>
            <xsd:enumeration value="INHERENCE"/>
            <xsd:enumeration value="POSSESSION_OMISSION"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="Result">
        <xsd:annotation>
            <xsd:documentation>Enumerace definující rozhodnutí k danému ApprovalSubject v rámci antiphishingu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="APPROVE"/>
            <xsd:enumeration value="REJECT"/>
            <xsd:enumeration value="VERIFY"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="NextActivity">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ evidující aktivity předepsané BLAZEm v rámci Login Event scoringu.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="code" type="NextActivityCode"/>
            <xsd:element name="numericalOrder" type="xsd:long"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="EntitledPersonType">
        <xsd:annotation>
            <xsd:documentation>Oprávněná osoba k účtu s legal segment =  ENTREPRENEUR nebo LEGAL_ENTITY.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="smeCuid" type="xsd:long" minOccurs="0"/>
            <xsd:element name="generalContractNumber" type="xsd:string" minOccurs="0"/>
            <xsd:element name="cuid " type="xsd:long"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="NextActivityCode">
        <xsd:annotation>
            <xsd:documentation>Enumerace definující typ navazující aktivity předepsané BLAZE-m</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="HANDLE_PHISHING_OFFENCE"/>
            <xsd:enumeration value="NOTIFY_CLIENT"/>
            <xsd:enumeration value="VERIFY_CLIENT"/>
            <xsd:enumeration value="VERIFY_PHISHING_OFFENCE"/>
            <xsd:enumeration value="HANDLE_SUSPICIOUS_LOGIN_EVENT"/>
            <xsd:enumeration value="HANDLE_SUSPICIOUS_PAYMENT_ORDER_EVENT"/>
            <xsd:enumeration value="DEFER_PAYMENT"/>
            <xsd:enumeration value="CARD_AUTHORIZATION_NOTIFY_CLIENT"/>
            <xsd:enumeration value="CARD_AUTHORIZATION_VERIFY_CLIENT"/>
            <xsd:enumeration value="DEVICE_ACCESS_VERIFY_SMS"/>
            <xsd:enumeration value="DEVICE_ACCESS_VERIFY_BIOMETRY"/>
            <xsd:enumeration value="DEVICE_ACCESS_VERIFY_LIVENESSCHECK"/>
            <xsd:enumeration value="DEVICE_ACCESS_VERIFY_PASSWORD"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:element name="ApprovePaymentOrderRequest">
        <xsd:annotation>
            <xsd:documentation>Approve new payment order request.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="paymentOrderEventId" type="xsd:string"/>
                <xsd:element name="sessionId" type="xsd:string"/>
                <xsd:element name="quickPaymentOrder" type="xsd:boolean" default="false"/>
                <xsd:element name="paymentOrder" type="PaymentOrder"/>
                <xsd:element name="handledByOperator" type="xsd:boolean" default="false"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="PaymentOrder" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Abstraktní komplexní typ obsahující základní informace o platebním příkazu.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="amountInRequiredCurrency" type="xsd:decimal"/>
            <xsd:element name="requiredCurrency" type="xsd:string"/>
            <xsd:element name="channel" type="ChannelType"/>
            <xsd:element name="validFrom" type="xsd:dateTime"/>
            <xsd:element name="extSource" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ChannelType">
        <xsd:annotation>
            <xsd:documentation>Číselník definující možné kanály, kterými může klient přistupovat do AB.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="IB"/>
            <xsd:enumeration value="BRANCH"/>
            <xsd:enumeration value="OPEN_API"/>
            <xsd:enumeration value="MA"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="DomesticPaymentOrder" >
        <xsd:annotation>
            <xsd:documentation>Komplexní typ obsahující informace o tuzemském platebním příkazu.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="PaymentOrder">
                <xsd:sequence>
                    <xsd:element name="contraAccountNumberPrefix" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="contraAccountNumber" type="xsd:string"/>
                    <xsd:element name="contraBankCode" type="xsd:string"/>
                    <xsd:element name="addToWhitelist" type="xsd:boolean"/>
                    <xsd:element name="accountTrustLevel" type="AccountTrustLevel"/>
                    <xsd:element name="processInstantly" type="xsd:boolean"/>
                    <xsd:element name="transactionType" type="TransactionType"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="AccountTrustLevel">
        <xsd:annotation>
            <xsd:documentation>Číselník definující úroveň důvěryhodnosti účtu v kontextu daného klienta (evidováno v OBS)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CUSTOM_WHITELISTED"/>
            <xsd:enumeration value="OWN"/>
            <xsd:enumeration value="UNKNOWN"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="ForeignPaymentOrder">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ obsahující informace o příkazu k úkhradě na zahraniční bankovní účet.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="PaymentOrder">
                <xsd:sequence>
                    <xsd:element name="contraBankCode" type="xsd:string"/>
                    <xsd:element name="contraAccountNumber" type="xsd:string"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:element name="ApprovePaymentOrderResponse">
        <xsd:annotation>
            <xsd:documentation>Approve new payment order response.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requiredAuthorizationParameters" type="AuthorizationParameters"/>
                <xsd:element name="nextActivity" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="decision" type="Result" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApproveBatchPaymentsRequest">
        <xsd:annotation>
            <xsd:documentation>Approve batch payment request.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="batchPaymentEventId" type="xsd:string"/>
                <xsd:element name="sessionId" type="xsd:string"/>
                <xsd:element name="batchPayments" type="BatchSection"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="BatchSection">
        <xsd:sequence>
            <xsd:element name="id" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>internal identifier of batch section</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="batchId" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>internal identifier of batch this batch section was uploaded in</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="filename" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>name of file the batch section was uploaded in</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="uploadedAt" type="xsd:dateTime">
                <xsd:annotation>
                    <xsd:documentation>point in time the file with batch section was uploaded at</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="accountNumber" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>account number the batch section is connected to</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="accountName" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>name of the account the batch section is connected to</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="amountSum" type="xsd:decimal">
                <xsd:annotation>
                    <xsd:documentation>sum of amount of all payments in batch section</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="amountCurrency" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>currency of amountSum</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="paymentCount" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>number of payments in batch section</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="uploader" type="xsd:long">
                <xsd:annotation>
                    <xsd:documentation>cuid of the customer who uploaded this batch section</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="preConfirmer" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>cuid of the customer who handed over this batch section to confirmation</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="preConfirmedAt" type="xsd:dateTime" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>point in time this batch section was handed over to confirmation</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="ApproveBatchPaymentsResponse">
        <xsd:annotation>
            <xsd:documentation>Approve batch payment response.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="requiredAuthorizationParameters" type="AuthorizationParameters"/>
                <xsd:element name="nextActivity" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="decision" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="AuthorizationParameters">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ obsahující informace o požadovaných parametrech autorizace, kterými klient autorizuje svůj požadavek (např. příkaz k úhradě).</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="authLevel" type="AuthLevel"/>
            <xsd:element name="allowedCombination" type="SecurityElementCategoriesCombination" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="AuthLevel">
        <xsd:annotation>
            <xsd:documentation>Enumerace definující stupeň vyžadované autentizace</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ZERO"/>
            <xsd:enumeration value="ONE"/>
            <xsd:enumeration value="TWO"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="SecurityElementCategoriesCombination">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ obsahující informace o povolených kombinacích bezpečnostních elementů použitelných k autorizaci požadavku klienta (např. příkazu k úhradě).</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="elementCategory" type="SecurityElementCategory" maxOccurs="2"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="SecurityElementCategory">
        <xsd:annotation>
            <xsd:documentation>Enumerace obsahující jednotlivé kategorie bezpečnostních elementů, kterými klient autorizuje svůj požadavek (např. příkaz k úhradě)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="INHERENCE"/>
            <xsd:enumeration value="KNOWLEDGE"/>
            <xsd:enumeration value="POSSESSION"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:element name="HandlePhishingOffenceRequest">
        <xsd:annotation>
            <xsd:documentation>HandlePhishingOffence request.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="affectedCuid" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                <xsd:element name="businessProcessEvent" type="xsd:string"/>
                <xsd:element name="createIccNote" type="xsd:boolean" minOccurs="0"/>
                <xsd:choice>
                    <xsd:element name="automaticallyInitializedHandlePhishingOffence" type="AutomaticallyInitializedHandlePhishingOffenceRequest"/>
                    <xsd:element name="manuallyInitializedHandlePhishingOffence" type="ManuallyInitializedHandlePhishingOffenceRequest"/>
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandlePhishingOffenceResponse">
        <xsd:annotation>
            <xsd:documentation>HandlePhishingOffence response.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="activity" type="Activity" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="processResult" type="ProcessResult"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="ManuallyInitializedHandlePhishingOffenceRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.handlePhishingOffence, provolané manuálním úkonem
                (operátora), obsahující informace klientovi, na jehož účty byl proveden phishingový útok.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor operátora.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="affectedClientNotification" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Příznak, zda je požadováno zaslat notifikaci klienovi, na jehož účty byl
                                proveden phishingový útok.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="blockingReason" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hodnota z MDM číselníku AuthFacePictureBlockingReason.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AutomaticallyInitializedHandlePhishingOffenceRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.handlePhishingOffence, provolané automaticky časovačem,
                obsahující informace klientovi, na jehož účty byl proveden phishingový útok.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:choice>
                <xsd:element name="sessionId" type="xsd:string" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="installationId" type="xsd:string" minOccurs="0" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikátor zařízení.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
            <xsd:element name="requestId" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor události přihlášení v OSB.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="callsessionId" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jednoznačný identifikátor události přihlášení v OSB.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Activity">
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string"/>
            <xsd:element name="startDate" type="xsd:dateTime" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="result" type="ActivityResult"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ProcessResult">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SUCCESS"/>
            <xsd:enumeration value="SOMETHING_WRONG"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ActivityResult">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="DONE"/>
            <xsd:enumeration value="FAILED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:element name="VerifyPhishingOffenceRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.verifyPhishingOffence obsahující informace o přihlášení k ověření podezření na phishingový útok.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor klienta.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:choice>
                    <xsd:element name="sessionId" type="xsd:string">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="installationId" type="xsd:string">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Identifikátor zařízení.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="VerifyPhishingOffenceResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.verifyPhishingOffence.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="HandleSuspiciousPaymentOrderEventRequest">
        <xsd:annotation>
            <xsd:documentation>
                Request webové operace pro provedení akcí souvisejících s podezřelým platebním příkazem
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor klienta, který zadal platební příkaz</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="paymentOrderEventId" type="xsd:string" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Unikátní identifikátor požadavku týkajícího se příkazu k úhradě (zahrnuje vytváření i změny) generovaný v IB BE (pro všechny podporované kanály)</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="amountInRequiredCurrency" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Částka požadované transakce</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="requiredCurrency" type="xsd:string" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Měna požadované transakce</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleSuspiciousPaymentOrderEventResponse">
        <xsd:annotation>
            <xsd:documentation>
                Response webové operace pro provedení akcí souvisejících s podezřelým platebním příkazem
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="HandleSuspiciousLoginEventRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.handleSuspiciousLoginEvent.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor klienta.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="sessionId" type="xsd:string">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor události přihlášení v RDR.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleSuspiciousLoginEventResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.handleSuspiciousLoginEvent.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="HandleSuspiciousAuthFacePictureRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.handleSuspiciousAuthFacePicture.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Jednoznačný identifikátor klienta.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeNumber" type="xsd:string">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identifikátor operátora, který prováděl ověření fotografie.</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleSuspiciousAuthFacePictureResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.handleSuspiciousAuthFacePicture.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="ApproveDevicePairingRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.approveDevicePairing.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="idInstallation" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApproveDevicePairingResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.approveDevicePairing.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="approvalResult" type="Result"/>
                <xsd:element name="nextActivity" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleNotifyClientRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.handleNotifyClient.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="appUserName" type="xsd:string"/>
                <xsd:element name="businessProcessEvent" type="BusinessProcessEvent"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleNotifyClientResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.handleNotifyClient.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="ApproveDeviceAccessRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.approveDeviceAccess.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="installationID" type="xsd:string"/>
                <xsd:element name="sessionID" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApproveDeviceAccessResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.handleNotifyClient.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="approvalResult" type="Result"/>
                <xsd:element name="nextActivity" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleVerifyClientRequest">
        <xsd:annotation>
            <xsd:documentation>Request operace AntiphishingWS.handleVerifyClient.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="appUserName" type="xsd:string"/>
                <xsd:element name="businessProcessEvent" type="BusinessProcessEvent"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandleVerifyClientResponse">
        <xsd:annotation>
            <xsd:documentation>Response operace AntiphishingWS.handleVerifyClient.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexType/>
    </xsd:element>

    <xsd:element name="ApproveCardAuthorizationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="idMessageLog" type="xsd:long"/>
                <xsd:element name="idAccMove" type="xsd:long"/>
                <xsd:element name="idCard" type="xsd:long"/>
                <xsd:element name="transaction" type="Transaction"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApproveCardAuthorizationResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="scoring" type="Scoring"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="Transaction">
        <xsd:sequence>
            <xsd:element name="transactionDate" type="xsd:dateTime"/>
            <xsd:element name="amountTransaction" type="xsd:decimal"/>
            <xsd:element name="currencyTransaction" type="xsd:string"/>
            <xsd:element name="amountCurAcc" type="xsd:decimal" minOccurs="0"/>
            <xsd:element name="currencyAccount" type="xsd:string" minOccurs="0"/>
            <xsd:element name="advice" type="xsd:boolean"/>
            <xsd:element name="checkBalance" type="xsd:boolean" />
            <xsd:element name="merchantNameAndPlace" type="xsd:string" minOccurs="0"/>
            <xsd:element name="merchantType" type="xsd:string" minOccurs="0"/>
            <xsd:element name="pointOfServiceEntryMode" type="xsd:string" minOccurs="0"/>
            <xsd:element name="transactionType" type="xsd:string" minOccurs="0"/>
            <xsd:element name="safeEntryMode" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="contactless" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="pinIndicator" type="xsd:int" minOccurs="0"/>
            <xsd:element name="ecommerce" type="xsd:boolean"/>
            <xsd:element name="verifiedBy3DS" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="cardTokenId" type="xsd:long" minOccurs="0"/>
            <xsd:element name="walletCode" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Scoring">
        <xsd:sequence>
            <xsd:element name="decisions" type="CardDecision" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="score" type="xsd:decimal" minOccurs="0"/>
            <xsd:element name="nextActivity" type="NextActivity" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="scoringFlags" type="ScoringFlag" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CardDecision">
        <xsd:sequence>
            <xsd:element name="result" type="Result"/>
            <xsd:element name="reason" type="xsd:string" minOccurs="0"/>
            <xsd:element name="approvalSubject" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ScoringFlag">
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="BusinessProcessEvent">
        <xsd:annotation>
            <xsd:documentation>Enumerace definující podmnožinu hodnot z MDM číselníku BusinessProcessEvent.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ANTIPHISHING_HACKER_LOGGEDIN_KNOWN_IP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úspěšné přihlášení útočníka do IB z evidované IP adresy.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_HACKER_NOT_LOGGEDIN_VALID_USERNAME_KNOWN_IP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Neúspěšné přihlášení útočníka s platným userName do IB z evidované IP adresy</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_MANUAL_BLOCKING_BY_CLIENT_CALL">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obsluha rozhodla o blokaci přístupu klienta do banky na základě volání klienta s podezřením na útok hackera (phishing)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_MANUAL_BLOCKING_BY_ALERT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obsluha manuálně rozhodla o blokaci přístupu klienta automatickém upozornění na útok hackera (phishing).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_CLIENT_LOGGEDIN_ASSURANCE_NOTIFICATION">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Podezřelé příhlášení, u kterého má být přes schválení zaslána varovná/ujišťovací zpráva.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_HACKER_LOGIN_REJECTED_KNOWN_IP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Blokace přihlášení útočníka (tvářící se jako nesprávné přihlašovací údaje), s platným userName do IB z evidované IP adresy.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_PAIRING_DEVICE_REJECTED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obchodní proces zamítnutí párování zařízení z důvodu antiphishingových kontrol.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_PAIRING_DEVICE_APPROVED_VERIFICATION">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obchodní proces schválené párování zařízení - verifikace klienta.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_MANUAL_UNBLOCKING_CORRECT_CARD_TRN">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obsluha manuálně rozhodla o odblokaci. Karetní transakce vyhodnocena jako korektní.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_MANUAL_UNBLOCKING_SUSPICIOUS_CARD_TRN">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obsluha manuálně rozhodla o odblokaci. Karetní transakce vyhodnocena jako podezřelá.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_MANUAL_UNBLOCKING_VERIFICATION_CALL">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obsluha manuálně rozhodla o odblokaci. U karetní transakce verifikačním hovorem s klientem.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANTIPHISHING_VOICEBOT_BLOCKING_BY_CLIENT_CALL">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Blokace voicebotem na základě volání klienta s podezřením na útok (phishing).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="TransactionType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CCNCLCZ">
                <xsd:annotation>
                    <xsd:documentation>Storno platby</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNCLO">
                <xsd:annotation>
                    <xsd:documentation>Převod mezi účty klienta odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNCLI">
                <xsd:annotation>
                    <xsd:documentation>Převod mezi účty klienta příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNINTO">
                <xsd:annotation>
                    <xsd:documentation>Platba v rámci banky odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNINTI">
                <xsd:annotation>
                    <xsd:documentation>Platba  v rámci banky příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNOUTO">
                <xsd:annotation>
                    <xsd:documentation>Platba mimo banku odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNOUTI">
                <xsd:annotation>
                    <xsd:documentation>Platba mimo banku příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNIMMO">
                <xsd:annotation>
                    <xsd:documentation>Tuzemská odchozi okamžitá platba(do jiné banky v ČR)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNIMMI">
                <xsd:annotation>
                    <xsd:documentation>Tuzemská prichozi okamžitá platba (z jiné banky v ČR)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DCOLL">
                <xsd:annotation>
                    <xsd:documentation>Inkasní platba</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DCOLLBM">
                <xsd:annotation>
                    <xsd:documentation>Inkasní platba - Bill Manager</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DSIPO">
                <xsd:annotation>
                    <xsd:documentation>SIPO</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DREPTRN">
                <xsd:annotation>
                    <xsd:documentation>Trvalý příkaz</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DSAVING">
                <xsd:annotation>
                    <xsd:documentation>Pravidelné spoření - Forced Savings</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNCLO">
                <xsd:annotation>
                    <xsd:documentation>Konverzní převod mezi účty klienta odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNCLI">
                <xsd:annotation>
                    <xsd:documentation>Konverzní převod mezi účty klienta příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNINTO">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba v rámci banky odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNINTI">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba v rámci banky příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNCZO">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba v rámci ČR odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNCZI">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba v rámci ČR příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNEEAO">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba v rámci EHP mimo ČR odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNEEAI">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba v rámci EHP mimo ČR příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNOUTO">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba mimo EHP odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNOUTI">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní platba mimo EHP příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNEURO">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní Euro platba odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNEURI">
                <xsd:annotation>
                    <xsd:documentation>Konverzní/nekonverzní Euro platba příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPAYCUR">
                <xsd:annotation>
                    <xsd:documentation>platba kartou u obchodníka v měně účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPAYOTH">
                <xsd:annotation>
                    <xsd:documentation>platba kartou u obchodníka v jiné měně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPAYCURCB">
                <xsd:annotation>
                    <xsd:documentation>Platba kartou včetně Cash Back v měně účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPAYOTHCB">
                <xsd:annotation>
                    <xsd:documentation>Platba kartou včetně Cash Back v jiné měně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KREFCUR">
                <xsd:annotation>
                    <xsd:documentation>refundace platby kartou v měně účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KREFOTH">
                <xsd:annotation>
                    <xsd:documentation>refundace platby kartou v jiné měně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KATMCUR">
                <xsd:annotation>
                    <xsd:documentation>výběr hotovosti z ATM v měně účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KATMOTH">
                <xsd:annotation>
                    <xsd:documentation>výběr hotovosti z ATM v jiné měně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KATMCODE">
                <xsd:annotation>
                    <xsd:documentation>výběr hotovosti výběrovým kódem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KCASHCUR">
                <xsd:annotation>
                    <xsd:documentation>výplata hotovosti v měně účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KCASHOTH">
                <xsd:annotation>
                    <xsd:documentation>výplata hotovosti v jiné měně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KDEPCUR">
                <xsd:annotation>
                    <xsd:documentation>vklad hotovosti v ATM v měně účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KDEPOTH">
                <xsd:annotation>
                    <xsd:documentation>vklad hotovosti v ATM v jiné měně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KBLOCK">
                <xsd:annotation>
                    <xsd:documentation>karetní blokace</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BBLOCK">
                <xsd:annotation>
                    <xsd:documentation>bankovní blokace</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CPOSINT">
                <xsd:annotation>
                    <xsd:documentation>kreditní úrok</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CNEGINT">
                <xsd:annotation>
                    <xsd:documentation>debetní úrok</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CWITHTAX">
                <xsd:annotation>
                    <xsd:documentation>srážková daň</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CPRINCBACK">
                <xsd:annotation>
                    <xsd:documentation>Vrácení úroků</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CRETD">
                <xsd:annotation>
                    <xsd:documentation>Vrácená platba tuzemská</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CRETI">
                <xsd:annotation>
                    <xsd:documentation>Vrácená platba zahraniční</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CFIXINT">
                <xsd:annotation>
                    <xsd:documentation>Opravné zúčtování</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCNCL">
                <xsd:annotation>
                    <xsd:documentation>Storno platby</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCNCLSG">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku v rámci SG</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COTHTRN">
                <xsd:annotation>
                    <xsd:documentation>manuálně zadaný název poplatku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COTHNTRN">
                <xsd:annotation>
                    <xsd:documentation>manuálně zadaný název poplatku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CTHIRD">
                <xsd:annotation>
                    <xsd:documentation>poplatky třetích stran (zahraniční platby)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COURRBI">
                <xsd:annotation>
                    <xsd:documentation>OUR poplatky RBI (zahraniční platby)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CPINEL">
                <xsd:annotation>
                    <xsd:documentation>Zaslání PIN elektronicky (IB)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREPPIN">
                <xsd:annotation>
                    <xsd:documentation>Znovu zaslání PIN poštou</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREPPINEL">
                <xsd:annotation>
                    <xsd:documentation>Znovu zaslání PIN elektronicky (IB)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CPINCHNG">
                <xsd:annotation>
                    <xsd:documentation>Změna PIN v bankomatu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CPWDMAIL">
                <xsd:annotation>
                    <xsd:documentation>opakované zaslání hesla</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CBACHRG">
                <xsd:annotation>
                    <xsd:documentation>Měsíční poplatek za tarif</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CBACHRGSTORNO">
                <xsd:annotation>
                    <xsd:documentation>Storno poplatku za tarif kvůli nepoužívání</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CSTATMAIL">
                <xsd:annotation>
                    <xsd:documentation>Zaslání výpisu poštou</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CSTATEL">
                <xsd:annotation>
                    <xsd:documentation>Zaslání elektronického výpisu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CLMTCHNG">
                <xsd:annotation>
                    <xsd:documentation>Změna nastavení transakčních limitů na kartě</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDMNG">
                <xsd:annotation>
                    <xsd:documentation>Měsíční vedení karty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDYR">
                <xsd:annotation>
                    <xsd:documentation>Roční vedení karty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDDBL">
                <xsd:annotation>
                    <xsd:documentation>Odblokování karty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCLAIM">
                <xsd:annotation>
                    <xsd:documentation>Neoprávněná reklamace</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COWD">
                <xsd:annotation>
                    <xsd:documentation>Zaslání upomínky při přečerpání účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CSMS">
                <xsd:annotation>
                    <xsd:documentation>Zaslání notifikačních SMS</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CTRMINS">
                <xsd:annotation>
                    <xsd:documentation>Nastavení trvalého příkazu, inkasa nebo SIPO</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CTRMUPD">
                <xsd:annotation>
                    <xsd:documentation>Změna trvalého příkazu, inkasa nebo SIPO</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDVDK">
                <xsd:annotation>
                    <xsd:documentation>Vydání platební karty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDVPN">
                <xsd:annotation>
                    <xsd:documentation>Poskytnutí další debetní karty / platební nálepky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDVVK">
                <xsd:annotation>
                    <xsd:documentation>Vydání výběrového kódu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDVNK">
                <xsd:annotation>
                    <xsd:documentation>Vydání náhradní karty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CDOCEL">
                <xsd:annotation>
                    <xsd:documentation>Zaslání potvrzení na žádost klienta elektronicky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CDOCPOST">
                <xsd:annotation>
                    <xsd:documentation>Zaslání potvrzení na žádost klienta poštou</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CACCBLK">
                <xsd:annotation>
                    <xsd:documentation>Blokace účtu na žádost klienta</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CACCDBLK">
                <xsd:annotation>
                    <xsd:documentation>Deblokace účtu na žádost klienta</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OTRMTRN">
                <xsd:annotation>
                    <xsd:documentation>Převod konečného zůstatku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ORNDUP">
                <xsd:annotation>
                    <xsd:documentation>Haléřové vyrovnání</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ODBTCLR">
                <xsd:annotation>
                    <xsd:documentation>Úhrada debetního zůstatku na účtu klienta z vnitřního účtu banky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCNCLREF">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FREETRIAL">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku 3 měsíce na zkoušku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="I2ABPRCTRNSFR">
                <xsd:annotation>
                    <xsd:documentation>Investice do Air Bank - převod</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ACCTRN">
                <xsd:annotation>
                    <xsd:documentation>převod v rámci účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOADRAWDOWNFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za čerpání hypotéky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAEXCEEDINGLTVFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za čerpání hypotéky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAUNDRAWNAMOUNTFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za čerpání hypotéky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAUTIL">
                <xsd:annotation>
                    <xsd:documentation>Načerpání půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPAYSUMFULL">
                <xsd:annotation>
                    <xsd:documentation>Splátka půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPAYSUMPART">
                <xsd:annotation>
                    <xsd:documentation>Částečná splátka půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAEXPAY">
                <xsd:annotation>
                    <xsd:documentation>Mimořádná splátka půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAEXINSTFINAL">
                <xsd:annotation>
                    <xsd:documentation>Předčasné splacení půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAZKABLD">
                <xsd:annotation>
                    <xsd:documentation>Blokace Sazka</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAZKABLUND">
                <xsd:annotation>
                    <xsd:documentation>Blokace Sazka (nepoužitá)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAZKAPAYD">
                <xsd:annotation>
                    <xsd:documentation>Výběr Sazka kódem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAZKABLF">
                <xsd:annotation>
                    <xsd:documentation>Blokace Sazka - cizoměnová</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAZKABLUNF">
                <xsd:annotation>
                    <xsd:documentation>Blokace Sazka - cizoměnová (nepoužitá)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAZKAPAYF">
                <xsd:annotation>
                    <xsd:documentation>Výběr Sazka kódem - cizoměnový</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SPLITPAYMENTFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za rozložení platby</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TDINTRETURN">
                <xsd:annotation>
                    <xsd:documentation>Vrácení úroku z terminovaného vkladu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TDINTTRNSFR">
                <xsd:annotation>
                    <xsd:documentation>Výnos z terminovaného vkladu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTRNIMMHOLD">
                <xsd:annotation>
                    <xsd:documentation>Blokace - tuzemská okamžitá platba (do jiné banky v ČR)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INNOFFSET">
                <xsd:annotation>
                    <xsd:documentation>Příchozí nekonverzní automatický zápočet pohled. mezi účty klienta - deposita</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INCOFFSET">
                <xsd:annotation>
                    <xsd:documentation>Příchozí konverzní automatický zápočet pohled. mezi účty klienta - deposita</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OUTNOFFSET">
                <xsd:annotation>
                    <xsd:documentation>Odchozí nekonverzní automatický zápočet pohled. mezi účty klienta - deposita</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OUTCOFFSET">
                <xsd:annotation>
                    <xsd:documentation>Odchozí konverzní automatický zápočet pohled. mezi účty klienta - deposita</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INNOFFSETLOA">
                <xsd:annotation>
                    <xsd:documentation>Příchozí nekonverzní automatický zápočet pohled. mezi účty klienta - půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INCOFFSETLOA">
                <xsd:annotation>
                    <xsd:documentation>Příchozí konverzní automatický zápočet pohled. mezi účty klienta - půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OUTNOFFSETLOA">
                <xsd:annotation>
                    <xsd:documentation>Odchozí nekonverzní automatický zápočet pohled. mezi účty klienta - půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OUTCOFFSETLOA">
                <xsd:annotation>
                    <xsd:documentation>Odchozí konverzní automatický zápočet pohled. mezi účty klienta - půjčky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BUSINTPAYFULL">
                <xsd:annotation>
                    <xsd:documentation>Odchozí nekonverzní splátka obch. úroku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PENINTPAYFULL">
                <xsd:annotation>
                    <xsd:documentation>Odchozí nekonverzní splátka úroku z prodlení</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BUSINTPAYPART">
                <xsd:annotation>
                    <xsd:documentation>Odchozí nekonverzní částečná splátka obch. úroku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PENINTPAYPART">
                <xsd:annotation>
                    <xsd:documentation>Odchozí nekonverzní částečná splátka úroku z prodlení</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREFIN">
                <xsd:annotation>
                    <xsd:documentation>Refinancování úvěru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RESTRAINT">
                <xsd:annotation>
                    <xsd:documentation>Výplata exekuce</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RESTRAINTCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata exekuce konverzni</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LIVINGWAGE">
                <xsd:annotation>
                    <xsd:documentation>Výplata životního minima</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LIVINGWAGECO">
                <xsd:annotation>
                    <xsd:documentation>Výplata životního minima konverzni</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCE">
                <xsd:annotation>
                    <xsd:documentation>Pojistné</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCECO">
                <xsd:annotation>
                    <xsd:documentation>Pojistné konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEREF">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek pojistného</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEREFCO">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek pojistného konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPIP">
                <xsd:annotation>
                    <xsd:documentation>Pojistné</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPIPCO">
                <xsd:annotation>
                    <xsd:documentation>Pojistné konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPIPREF">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek pojistného</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPIPREFCO">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek pojistného konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPPI">
                <xsd:annotation>
                    <xsd:documentation>Pojistné PPI</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPPICO">
                <xsd:annotation>
                    <xsd:documentation>Pojistné PPI konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPPIREF">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek pojistného PPI</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INSURANCEPPIREFCO">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek pojistného PPI konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNSEPO">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční platba - SEPA odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNSEPI">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční platba - SEPA příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNPSDO">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční platba - PSD odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNPSDI">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční platba - PSD příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNNPSDO">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční platba - nonPSD odchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ITRNNPSDI">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční platba - nonPSD příchozí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAUTIL">
                <xsd:annotation>
                    <xsd:documentation>Načerpání hypotéky (na BÚ pro splácení půjčky/hypo)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAREFIN">
                <xsd:annotation>
                    <xsd:documentation>Doplacení závazku při refinanc. hypotéky provádíme-li za klienta (do jiné banky v ČR)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAPAYSUMFULL">
                <xsd:annotation>
                    <xsd:documentation>Splátka hypotéky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAPAYSUMPART">
                <xsd:annotation>
                    <xsd:documentation>Splátka hypotéky - částečná (při nedost. prostředků)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAEXINSTLFINAL">
                <xsd:annotation>
                    <xsd:documentation>Předčasné úplné splacení hypotéky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAEXPREPAY">
                <xsd:annotation>
                    <xsd:documentation>Úložka do OTB (mimořádná platba navíc)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAPREPAY">
                <xsd:annotation>
                    <xsd:documentation>Úložka do OTB - pravidelná (pravidelná mimořádná platba navíc)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOARETURNPREPAY">
                <xsd:annotation>
                    <xsd:documentation>Čerpání z OTB (na BÚ pro splácení půjčky/hypo)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAPREPAYTOPRC">
                <xsd:annotation>
                    <xsd:documentation>Převod peněz z Chytré rezervy na jistinu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPENALTYRECALC">
                <xsd:annotation>
                    <xsd:documentation>Úrok z prodlení - doúčtování (ze servis. BÚ na vnitř. účet)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPENALTYRETURN">
                <xsd:annotation>
                    <xsd:documentation>Úrok z prodlení - vratka (z vnitř. účtu na  servis. BÚ)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPENINTRECALC">
                <xsd:annotation>
                    <xsd:documentation>Obchodní úrok - doúčtování (ze servis. BÚ na vnitř. účet)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPENINTRETURN">
                <xsd:annotation>
                    <xsd:documentation>Obchodní úrok - vratka (z vnitř. účtu na  servis. BÚ)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ZKNCHRG">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za zápis do KN</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAREDEM">
                <xsd:annotation>
                    <xsd:documentation>Zesplatnění hypotéky - plná úhrada</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOAREDEMPART">
                <xsd:annotation>
                    <xsd:documentation>Zesplatnění hypotéky - část. úhrada (při nedost. prostředků)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREDEM">
                <xsd:annotation>
                    <xsd:documentation>Zesplatnění úvěru - plná úhrada</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREDEMPART">
                <xsd:annotation>
                    <xsd:documentation>Zesplatnění úvěru - část. úhrada (při nedost. prostředků)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPENINTRECALCPART">
                <xsd:annotation>
                    <xsd:documentation>Doúčtování úroku z prodlení - část. úhrada (při nedost. prostředků)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAPENALTYRECALCPART">
                <xsd:annotation>
                    <xsd:documentation>Doúčtování obchodního úroku - část. úhrada (při nedost. prostředků)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CMLHEDGECOST">
                <xsd:annotation>
                    <xsd:documentation>Poplatek - náklady vynaložené za garanci sazby</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREFINDEADI">
                <xsd:annotation>
                    <xsd:documentation>Refinancování úvěru dědictví</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREFINDEADO">
                <xsd:annotation>
                    <xsd:documentation>Refinancování úvěru dědictví</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREFININTI">
                <xsd:annotation>
                    <xsd:documentation>Doplacení interního závazku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LOAREFININTO">
                <xsd:annotation>
                    <xsd:documentation>Doplacení interního závazku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDVSTIC">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za vydání platební nálepky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDVNN">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za vydání náhradní platební nálepky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDHTVDK">
                <xsd:annotation>
                    <xsd:documentation>Vydání podpisové karty - z účtu v CZ</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDHTVDKCO">
                <xsd:annotation>
                    <xsd:documentation>Vydání podpisové karty - z účtu v CM</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDHTVNK">
                <xsd:annotation>
                    <xsd:documentation>Vydání náhradní podpisové karty - z účtu v CZ</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDHTVNKCO">
                <xsd:annotation>
                    <xsd:documentation>Vydání náhradní podpisové karty - z účtu v CM</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDHTVOK">
                <xsd:annotation>
                    <xsd:documentation>Vydání obnovené podpisové karty - z účtu v CZ</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCARDHTVOKCO">
                <xsd:annotation>
                    <xsd:documentation>Vydání obnovené podpisové karty - z účtu v CM</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CFOREIGNCORRESPCHRG">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za zahraniční korespondenci</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TOPUPPHONECREDIT">
                <xsd:annotation>
                    <xsd:documentation>Dobití kreditu na předplacené SIM kartě</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRVINSURPAY">
                <xsd:annotation>
                    <xsd:documentation>Úhrada cestovního pojištění z CZ účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRVINSURPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Úhrada cestovního pojištění z CM účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRVINSURREFUND">
                <xsd:annotation>
                    <xsd:documentation>Vratka přeplatku cestovního pojištění na CZ účet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRVINSURREFUNDCO">
                <xsd:annotation>
                    <xsd:documentation>Vratka přeplatku cestovního pojištění na CM účet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WAGEPAY">
                <xsd:annotation>
                    <xsd:documentation>Výplata mzdy zaměstnanců</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WAGEPAYACC">
                <xsd:annotation>
                    <xsd:documentation>Výplata mzdy zaměstnanců bezhotovostní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WAGEPAYACCCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata mzdy zaměstnanců bezhotovostní s konverzí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WAGEPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata mzdy zaměstnanců konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRVINSURCANCEL">
                <xsd:annotation>
                    <xsd:documentation>Vratka cestovního pojištění na CZ účet při stornu cesty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRVINSURCANCELCO">
                <xsd:annotation>
                    <xsd:documentation>Vratka cestovního pojištění na CM účet při stornu cesty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MYAIRPAY">
                <xsd:annotation>
                    <xsd:documentation>Platba z aplikace My Air z klientského účtu na transportní účet partnera</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MYAIRPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Platba z aplikace My Air z klientského účtu na transportní účet partnera-konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MYAIRPAYRETURN">
                <xsd:annotation>
                    <xsd:documentation>Vratka platby z aplikace My Air z transportního účtu partnera na klientský účet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MYAIRPAYRETURNCO">
                <xsd:annotation>
                    <xsd:documentation>Vratka platby z aplikace My Air z transportního účtu partnera na klientský účet-konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHBACKPAY">
                <xsd:annotation>
                    <xsd:documentation>Výplata peněz ušetřených díky slevám</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHBACKPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata peněz ušetřených díky slevám-konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHBACKPAYO2">
                <xsd:annotation>
                    <xsd:documentation>Odměna za spolupráci s O2</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHBACKPAYRETURN">
                <xsd:annotation>
                    <xsd:documentation>Vrácení peněz získaných díky slevám</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHBACKPAYRETURNCO">
                <xsd:annotation>
                    <xsd:documentation>Vrácení peněz získaných díky slevám-konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MOBILEPAYREWARD">
                <xsd:annotation>
                    <xsd:documentation>Odměna za platby a výběry mobilem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MOBILEPAYREWARDCO">
                <xsd:annotation>
                    <xsd:documentation>Odměna za platby a výběry mobilem (konverzní)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="REWARD">
                <xsd:annotation>
                    <xsd:documentation>Odměna obecná</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="REWARDCO">
                <xsd:annotation>
                    <xsd:documentation>Odměna obecná (konverzní)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXTRAPAYOUT">
                <xsd:annotation>
                    <xsd:documentation>Výplata mimořádná</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXTRAPAYOUTCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata mimořádná (konverzní)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXTRAPAYOUTVCH">
                <xsd:annotation>
                    <xsd:documentation>Výplata mimořádná složenkou</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXTRAPAYOUTVCHCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata mimořádná složenkou(konverzní)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FIXDEPCAP">
                <xsd:annotation>
                    <xsd:documentation>Peníze získané úročením termínovaného vkladu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FIXDEPCLOSE">
                <xsd:annotation>
                    <xsd:documentation>Vložení peněz na termínovaný vklad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FIXDEPCREATE">
                <xsd:annotation>
                    <xsd:documentation>Vrácení peněz z termínovaného vkladu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MYAIRPARTNERPAY">
                <xsd:annotation>
                    <xsd:documentation>Převod z technického účtu partnera na účet partnera, vytváří se jednou denně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MYAIRPARTNERACCCHRG">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za platební styk a zúčtování na technickém účtu, vytváří se jednou měsíčně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LIVINGWAGEACC">
                <xsd:annotation>
                    <xsd:documentation>Výplata životního minima klientovi bezhotovostně</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LIVINGWAGEACCCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata životního minima klientovi bezhotovostně - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OVERPAYOUTCONSOLID">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek z odchozí konsolidace</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OVERPAYOUTCONSOLIDCO">
                <xsd:annotation>
                    <xsd:documentation>Přeplatek z odchozí konsolidace - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COMPLETECOMMITMENT">
                <xsd:annotation>
                    <xsd:documentation>Doplacení závazku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COMPLETECOMMITMENTCO">
                <xsd:annotation>
                    <xsd:documentation>Doplacení závazku - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PAYINHERITANCE">
                <xsd:annotation>
                    <xsd:documentation>Výplata dědictví</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PAYINHERITANCECO">
                <xsd:annotation>
                    <xsd:documentation>Výplata dědictví - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COURTFEE">
                <xsd:annotation>
                    <xsd:documentation>Soudní poplatky</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COURTFEECO">
                <xsd:annotation>
                    <xsd:documentation>Soudní poplatky - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COMPLAINT">
                <xsd:annotation>
                    <xsd:documentation>Reklamace</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COMPLAINTCO">
                <xsd:annotation>
                    <xsd:documentation>Reklamace - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RETIREMENT">
                <xsd:annotation>
                    <xsd:documentation>Vrácení důchodu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RETIREMENTCO">
                <xsd:annotation>
                    <xsd:documentation>Vrácení důchodu - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RETURNFEE">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RETURNFEECO">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku - konverní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BALANCETRANSFER">
                <xsd:annotation>
                    <xsd:documentation>Převod zůstatku</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BALANCETRANSFERCO">
                <xsd:annotation>
                    <xsd:documentation>Převod zůstatku - konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXTCHRGFRGPAY">
                <xsd:annotation>
                    <xsd:documentation>Poplatek partnera za zahraniční platbu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXTCHRGFRGPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Poplatek partnera za zahraniční platbu - konverní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OVERDRTOLOAN">
                <xsd:annotation>
                    <xsd:documentation>Vyvedení čerpání kontokorentu při exekuci na běžném účtu/ukončení běžného účtu/zesplatnění</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OVDRRETURN">
                <xsd:annotation>
                    <xsd:documentation>Vrácení čerpání kontokorentu na běžný účet při ukončení poslední exekuce</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ODTREDEM">
                <xsd:annotation>
                    <xsd:documentation>Splátka převedeného kontokorentu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ODTBUSINTPAY">
                <xsd:annotation>
                    <xsd:documentation>Obchodní úrok kontokorentu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ODTPENINTPAY">
                <xsd:annotation>
                    <xsd:documentation>Úrok z prodlení kontokorentu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OVDRINSTL">
                <xsd:annotation>
                    <xsd:documentation>Splátka kontokorentu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVBUY">
                <xsd:annotation>
                    <xsd:documentation>Nákup cenného papíru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVBUYCO">
                <xsd:annotation>
                    <xsd:documentation>Nákup cenného papíru konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVBUYFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za nákup cenného papíru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVBUYFEECO">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za nákup cenného papíru konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVBUYSTOCK">
                <xsd:annotation>
                    <xsd:documentation>Nákup akcií</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVMGMFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za správu portfolia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVMGMFEECO">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za správu portfolia konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVREALDATAFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za poskytnutí realtime dat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVSELLFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za prodej cenného papíru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVSELLFEECO">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za prodej cenného papíru konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVSELLSTOCK">
                <xsd:annotation>
                    <xsd:documentation>Prodej akcií</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVTRANSFERFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za převod cenného papíru na jiný majetkový účet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVTRANSFERFEECO">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za převod cenného papíru na jiný majetkový účet konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVCOUPONPAY">
                <xsd:annotation>
                    <xsd:documentation>Výplata kupónu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVCOUPONPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata kupónu konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVDIVIDENDPAY">
                <xsd:annotation>
                    <xsd:documentation>Výplata dividendy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVDIVIDENDPAYCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata dividendy konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVSELL">
                <xsd:annotation>
                    <xsd:documentation>Prodej cenného papíru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVSELLCO">
                <xsd:annotation>
                    <xsd:documentation>Prodej cenného papíru konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVMATURITY">
                <xsd:annotation>
                    <xsd:documentation>Výplata dluhopisu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVMATURITYCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata dluhopisu konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVREFUNDBUY">
                <xsd:annotation>
                    <xsd:documentation>Vrácení přeplatku z nákupu cenného papíru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVREFUNDBUYCO">
                <xsd:annotation>
                    <xsd:documentation>Vrácení přeplatku z nákupu cenného papíru konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVREFUNDBUYFEE">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku za nákup cenného papíru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVREFUNDBUYFEECO">
                <xsd:annotation>
                    <xsd:documentation>Vrácení poplatku za nákup cenného papíru konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLOOTHFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za změnu hypoteční smlouvy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVXSTMTFEE">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za výpis</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVXSTMTFEECO">
                <xsd:annotation>
                    <xsd:documentation>Poplatek za výpis s měnovou konverzí</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="POSTALORDER">
                <xsd:annotation>
                    <xsd:documentation>Výplata složenkou</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="POSTALORDERCO">
                <xsd:annotation>
                    <xsd:documentation>Výplata složenkou konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FEEPKG_CURACC">
                <xsd:annotation>
                    <xsd:documentation>Vedení všech dalších běžných účtů (až do celkového počtu deseti účtů)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FEEPKG_SMS">
                <xsd:annotation>
                    <xsd:documentation>Neomezená zasílání informačních SMS o pohybech na účtu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FEEPKG_ATM">
                <xsd:annotation>
                    <xsd:documentation>Výběry hotovosti ze všech bankomatů u nás i ve světě</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FEEPKG_TRAVELPROP">
                <xsd:annotation>
                    <xsd:documentation>Zahraniční karta</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CARDCOMPLAINT">
                <xsd:annotation>
                    <xsd:documentation>Karetní reklamace - proplacení reklamované platby / výběru hotovosti</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CARDCOMPLAINTCNCL">
                <xsd:annotation>
                    <xsd:documentation>Storno karetní reklamace</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EXEMPTEDINCOME">
                <xsd:annotation>
                    <xsd:documentation>Výplata nepostižitelného příjmu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVDIVIDENDTAX">
                <xsd:annotation>
                    <xsd:documentation>Srážková daň z dividendy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INVDIVIDENDTAXCO">
                <xsd:annotation>
                    <xsd:documentation>Srážková daň z dividendy konverzní</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TOPUPPHONECREDIT">
                <xsd:annotation>
                    <xsd:documentation>Dobití telefonního kreditu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHBACKBENEFIT">
                <xsd:annotation>
                    <xsd:documentation>Odměna za věrnost</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRAVELINSURANCEUNITYREWARD">
                <xsd:annotation>
                    <xsd:documentation>Unity sleva na cestovní pojištění</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UNITYREWARD">
                <xsd:annotation>
                    <xsd:documentation>Odměna Unity</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
