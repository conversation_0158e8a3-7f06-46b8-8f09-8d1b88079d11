<?xml version="1.0" encoding="UTF-8"?>

<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:jxb="https://jakarta.ee/xml/ns/jaxb"
            elementFormDefault="qualified"
            xmlns="http://homecredit.net/lap/ws/approvalprocess/common" jxb:version="3.0"
            targetNamespace="http://homecredit.net/lap/ws/approvalprocess/common">

    <xsd:complexType name="Application" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Information about application (set of applications), depends on application hierarchy.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of application.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="status" type="ApplicationStatus" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>application status</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Envelope" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Envelope (set of applications) relevant for approval process. Applications has its hierarchy and main application define envelope
                type.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="envelopeId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of main application.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="dateCreated" type="xsd:date" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of envelope creation.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="channel" type="Channel" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>channel</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="applicant" type="Person" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>applicant</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContractEnvelope">
        <xsd:annotation>
            <xsd:documentation>Contract Envelope.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="currentAccountApplication" type="CurrentAccountApplication" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="savingAccountApplication" type="SavingAccountApplication" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="cardApplication" type="CardApplication" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="loanApplication" type="LoanApplication" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="contractApplication" type="ContractApplication" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="codebtorApplication" type="Affidavit" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="mortgageRefApplication" type="MortgageRefApplication" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="mortgageApplication" type="MortgageApplication" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="insuranceApplication" type="InsuranceApplication" minOccurs="0" maxOccurs="2"/>
                    <xsd:element name="identificationProcess" type="ExternalIdentificationProcess"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="DisponentEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="disponentApplication" type="PersonRequestApplication" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="affidavitApplication" type="Affidavit" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="ExternalIdentificationProcess">
        <xsd:sequence>
            <xsd:element name="bankIdStateId" type="xsd:string"/>
            <xsd:element name="businessProcessEvent" type="xsd:string"/>
            <xsd:element name="businessProcessEventId" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CurrentAccountEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="cardApplication" type="CardApplication" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="currentAccountApplication" type="CurrentAccountApplication" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="SavingAccountEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="savingAccountApplication" type="SavingAccountApplication" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="TermDepositEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="termDepositApplication" type="TermDepositApplication" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="InvestmentCertificateEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="investmentCertificateApplication" type="InvestmentCertificateApplication"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="PersonalDataChangeEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="personalDataChangeApplication" type="PersonalDataChangeApplication" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="AuthResetEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="authResetApplication" type="AuthResetApplication" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="CardEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="affidavitApplication" type="Affidavit" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="cardApplication" type="CardApplication" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="LoanEnvelope">
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="currentAccountApplication" type="CurrentAccountApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>current acount applications</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="loanApplication" type="LoanApplication" minOccurs="1" maxOccurs="2">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>loan application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="insuranceApplication" type="InsuranceApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>insurance application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="ChangeLoanEnvelope">
        <xsd:annotation>
            <xsd:documentation>Change loan envelope</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="changeLoanParametersApplication" type="ChangeLoanParameters" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>change loan parameters</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="MortgageRefEnvelope">
        <xsd:annotation>
            <xsd:documentation>Mortgage refinancing envelope</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="mortgageRefApplication" type="MortgageRefApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>mortgage refinancing application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="codebtorApplication" type="Affidavit" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>list of codebtor applications</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="currentAccountApplication" type="CurrentAccountApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>list of current acount applications</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="insuranceApplication" type="InsuranceApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>insurance application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="MortgageEnvelope">
        <xsd:annotation>
            <xsd:documentation>Mortgage envelope</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="mortgageApplication" type="MortgageApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>mortgage application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="codebtorApplication" type="Affidavit" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>list of codebtor applications</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="currentAccountApplication" type="CurrentAccountApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>current acount application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="insuranceApplication" type="InsuranceApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>insurance application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="OverdraftEnvelope">
        <xsd:annotation>
            <xsd:documentation>Overdraft envelope</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Envelope">
                <xsd:sequence>
                    <xsd:element name="overdraftApplication" type="OverdraftApplication" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>overdraft application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="StockEtfEnvelope">
        <xsd:annotation>
            <xsd:documentation>Stock Etf envelope</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Envelope"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="ChannelType">
        <xsd:annotation>
            <xsd:documentation>Channel type for relevant process type.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ICC"/>
            <xsd:enumeration value="IB"/>
            <xsd:enumeration value="BRANCH"/>
            <xsd:enumeration value="POST"/>
            <xsd:enumeration value="ECC"/>
            <xsd:enumeration value="SPB"/>
            <xsd:enumeration value="MESSENGER"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="EmploymentRole">
        <xsd:annotation>
            <xsd:documentation>Role of employment, enumeration.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Y"/>
            <xsd:enumeration value="N"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="ContractApplication">
        <xsd:annotation>
            <xsd:documentation>Information about general contract application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="recommendatoryCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>code of recommendation also known as Member Get Member code.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="urlReferer" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>identifier of web, which links to Air/Bank web site.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="processCode" type="xsd:string">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Process code of full or short application.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="InvestmentCertificateApplication">
        <xsd:annotation>
            <xsd:documentation>Information about investment certificate application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="requiredDeposit" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Required deposit amount.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="LoanApplication">
        <xsd:annotation>
            <xsd:documentation>loan application</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="creditAmount" type="xsd:long" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>loan amount in relevant currency.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dateOpened" type="xsd:date" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>date of loan opening (transfer to client´s current account)</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="installmentCount" type="xsd:short" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>final count of installments.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="annuity" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>monthly installment amount.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="paymentFirstDate" type="xsd:date" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>date of first installment.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="yearInterestRate" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>effective year interest rate (%)</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="currentAccountNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>association for current account used for loan maintenance</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="loanRPSNMin" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>minimal RPSN for actual bonus level</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="loanRPSNMax" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>maximal RPSN for actual bonus level.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dateCreated" type="xsd:date" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>date of application creation.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="creditAmountPreScoring" type="xsd:long" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>credit amount set by applicant, before application was sent to approval process.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="applicationChange" type="ApplicationChange" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="applicationLoanType" type="ApplicationLoanType" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>type of loan application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="consolidatedLoans" type="ConsolidatedLoans" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>candidate obligations records for loan consolidation.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="creditOffer" type="CreditOffer" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>credit offer (proposal for consolidation loan)</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="originApplicationLoanType" type="ApplicationLoanType" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>type of original application in case of alternative application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="internalCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Názor prodejce na klienta</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="alternativeToId" type="xsd:long" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Id of original consolidation request.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="specificRequest" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="creditAmountDocumentsDisplayed" type="xsd:decimal" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="upsellAmount" type="xsd:decimal" minOccurs="0"/>
                    <xsd:element name="customerCareCode" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="processCode" type="ApplicationProcessCode" minOccurs="0"/>
                    <xsd:element name="splitPaymentTransactionKey" type="TransactionKey" minOccurs="0">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Key of transaction for split payment.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="ApplicationProcessCode">
        <xsd:annotation>
            <xsd:documentation>application process code</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="TOP_UP"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="Employment">
        <xsd:annotation>
            <xsd:documentation>employment (source of person's income)</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="netSalary" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>income (salary)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="economicalStatus" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>economicalStatus describes income type, CIF REGISTER (Reg_number = 1).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="industry" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>industry for employee or smeCustomer income type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="longTermEmployment" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>employment with term longer than 3 year.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employedFrom" type="xsd:date" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>start date of employment contract validity</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employmentType" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>describes limitation of contract (with defined end date or withnout)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="agreementUntil" type="xsd:date" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>if exist contract limitation, it describes end date of contract.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employer" type="Employer" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about employer</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employmentRole" type="EmploymentRole" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>role of employment</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employmentId" type="xsd:long" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="noTaxReturn" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Indicates that the applicant did not file a tax return, paid a flat tax</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PersonPreviousApplication">
        <xsd:annotation>
            <xsd:documentation>information about person's previous loan applications</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of previous application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="dateCreate" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of creation of application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="applicationType" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>type of application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="applicationStatus" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>status of application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="koCodes" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>hard check codes, delimited by "|"</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="rejectReason" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reject reason stored on application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="creditAmount" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>loan amount on previous application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanAnnuity" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>loan annuity (monthly installment) for previous application.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="paymentNumber" type="xsd:short" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of installments</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="changedAppDataKey" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>attribute name, that was changed</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="changedAppDataValue" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>origin value (from previous application)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="applicant" type="Person" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>applicant - main debtor</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="codebtors" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>list of co-debtors</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Channel">
        <xsd:annotation>
            <xsd:documentation>Information about channel (set of channels) for relevant business process time stamps.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="employeeCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of employee, who assist applicant during acquisition process.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="channelProcessTime" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>timestamp for current channel process type.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="fingerPrint" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>is defined as hash of next web browser parameters: plugins, supercookies, timezone, fonts, video.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cookies" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about cookies, relevant for channel internet banking.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ipAddress" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ip address of computer, where application was originated.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="branchOffice" type="BranchOffice" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="channelProcessType" type="ChannelProcessType" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="channelType" type="ChannelType" minOccurs="1" maxOccurs="1"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PersonFinancialData">
        <xsd:annotation>
            <xsd:documentation>Financial data related to person. Mandatory for request where is contained loan application.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="sameApplicantData" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AccountApplication" abstract="true">
        <xsd:annotation>
            <xsd:documentation>account application</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="currency" type="xsd:string" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>set currency for account</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="withMobilityRequest" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about mobility request existence for selected account. Relevant only for current account
                                        application.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="systemEnforcedRequest" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about enforced account (was not required by applicant, but necessary for loan). Relevant only for
                                        current account application.
                                    </jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="SavingAccountApplication">
        <xsd:annotation>
            <xsd:documentation>Information about general contract application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AccountApplication">
                <xsd:sequence>
                    <xsd:sequence>
                    </xsd:sequence>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="TermDepositApplication">
        <xsd:annotation>
            <xsd:documentation>Information about term deposit application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:sequence>
                        <xsd:element name="requiredDeposit" type="xsd:decimal">
                            <xsd:annotation>
                                <xsd:appinfo>
                                    <jxb:property>
                                        <jxb:javadoc>Deposit amount</jxb:javadoc>
                                    </jxb:property>
                                </xsd:appinfo>
                            </xsd:annotation>
                        </xsd:element>

                    </xsd:sequence>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="CurrentAccountApplication">
        <xsd:annotation>
            <xsd:documentation>Information about general contract application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AccountApplication">
                <xsd:sequence>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="PersonalDataChangeApplication">
        <xsd:annotation>
            <xsd:documentation>Information about personal data change application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:sequence>
                    </xsd:sequence>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="AuthResetApplication">
        <xsd:annotation>
            <xsd:documentation>Information about auth. reset application.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:sequence>
                    </xsd:sequence>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="Person">
        <xsd:annotation>
            <xsd:documentation>person information (set of persons related to application)</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="birthDate" type="xsd:date" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date of person birth.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="birthPlace" type="BirthPlace">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>place of person´s birth.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="citizenship" type="xsd:string" minOccurs="1" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>citizenship, alpha2 country code is expected, ISO 3166-1.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="gender" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>gender, CIF REGISTER (Reg_number = 12)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="name1" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>first name of person.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="name2" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>last name of person.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of person, defined in CIF system.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="birthNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>birth number is identifier of person used in czech republic. 9 or 10 digits number which is unique. Birth number is
                                optional due to foreigners.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="politicallyExposed" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Politically expose person.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="applicationDataClientAccepted" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Indicator that applicant confirmed new structure of application data.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contact" type="Contact" minOccurs="1" maxOccurs="unbounded"/>
            <xsd:element name="personFlag" type="PersonFlag" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="address" type="Address" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="document" type="Document" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="personPersonalData" type="PersonPersonalData" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="personType" type="PersonType" minOccurs="1" maxOccurs="1"/>
            <xsd:element name="personFinancialData" type="PersonFinancialData" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="employment" type="Employment" minOccurs="0" maxOccurs="2"/>
            <xsd:element name="relation" type="Relation" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="householdIncomeExpense" type="HouseholdIncomeExpense" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="shadowData" type="ShadowData" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="applicationDataRequiredType" type="ApplicationDataRequiredType" minOccurs="0" maxOccurs="1"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ApplicationDataRequiredType">
        <xsd:annotation>
            <xsd:documentation>required type of application data relevant for person</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="FULL"/>
            <xsd:enumeration value="LIMITED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="Address">
        <xsd:annotation>
            <xsd:documentation>Person address (set of addresses).</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="country" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>address country, alpha2 code is expected, defined by ISO 3166-1</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="streetOrLocality" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Street or Locality</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="streetNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Address street number</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="town" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Address town name</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="zipCode" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>address zip code.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="addressType" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>address type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ApplicationLoanType">
        <xsd:annotation>
            <xsd:documentation>type of application relevant for loan application</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CONSOLIDATION"/>
            <xsd:enumeration value="LOAN"/>
            <xsd:enumeration value="MORTGAGE_REF"/>
            <xsd:enumeration value="MORTGAGE"/>
            <xsd:enumeration value="OVERDRAFT"/>
            <xsd:enumeration value="SPLIT_PAYMENT"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="CardApplication">
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="requiredPersonRole" type="PersonType" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>person type, for who is debit card required, CMS REISTER (REG. 718).</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="coupleAccountApplicationId" type="xsd:long" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>identifier of associated account request</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="isVirtualCardRequired" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about requirement for virtual card</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="isIssueFeeRequired" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information about fee charge</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="coupleAccountNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="replacedCardNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>card number (PCI-DSS compliant) of replacing card.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="cuidApplicant" type="xsd:long" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>cuid of applicant</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="requiredCardDevice" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>Indicates, if we create physical card through gpe</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cardBusinessCategory" type="xsd:string">
                        <xsd:annotation>
                            <xsd:documentation>Card business category</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="PersonPersonalData">
        <xsd:annotation>
            <xsd:documentation>set of data collected through loan application.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="residenceType" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>type of person´s residence. Relevant only for foreign persons, CIF REGISTER (Reg_number = 741).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="childCount" type="xsd:short" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of person´s children</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="education" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>the highest education level, reached by person.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="married" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Boolean information about marital status.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="housingType" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>type of person´s housing, CIF REGISTER (Reg_number = 2).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="householdMemberType" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>person's household member type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Contact">
        <xsd:annotation>
            <xsd:documentation>contact (set of contacts) related to person.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="value" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>contact value</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="callingCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calling code, relevant for contact types using phone.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="trustLevel" type="xsd:int" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>trust level of contact value, CIF REGISTER (Reg_number = 732).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contactType" type="ContactType" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>contact type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ChannelProcessType">
        <xsd:annotation>
            <xsd:documentation>Timestamp for some business relevant cases in application life cycle.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="APPROVE_CLIENT"/>
            <xsd:enumeration value="APPROVE"/>
            <xsd:enumeration value="LEAD"/>
            <xsd:enumeration value="REQUEST"/>
            <xsd:enumeration value="FINISHED"/>
            <xsd:enumeration value="CURRENT"/>
            <xsd:enumeration value="PRESCORING"/>
            <xsd:enumeration value="SIGN"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="Employer">
        <xsd:annotation>
            <xsd:documentation>identification of employer</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="name" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>name of employer</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="registrationNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identification number for czech companies. (I&#268;)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="taxNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>tax identification number</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employerAddress" type="Address" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>employer's address</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employerContact" type="Contact" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>employer's contact</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="economicalSubjectStatus" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="pureBisnodeData" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Flag of employer data was automatically pre-filled and not manually changed by user.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="PersonType">
        <xsd:annotation>
            <xsd:documentation>type of person, related to data origin.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="APPLICANT"/>
            <xsd:enumeration value="DISPONENT"/>
            <xsd:enumeration value="CARD_HOLDER"/>
            <xsd:enumeration value="DISPONENT_AND_CARD_HOLDER"/>
            <xsd:enumeration value="CODEBTOR"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="BranchOffice">
        <xsd:annotation>
            <xsd:documentation>Branch office information.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="officeId" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of branch office</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ApplicationChange">
        <xsd:sequence>
            <xsd:element name="dateChange" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date and time of change</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="orderChange" type="xsd:short" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ordering number of change</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="changedAppDataKey" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>key of changed attribute</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="changedAppDataValue" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>previous value of changed attribute</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PersonRequestApplication">
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="flagClientIsLegalRepresentative" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>information if client is legal representative of person included in application.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>unique identifier of person, defined in CIF system</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="requiredPersonRole" type="PersonType" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>person type</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="cuidApplicant" type="xsd:long" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>cuid of applicant</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="Affidavit">
        <xsd:annotation>
            <xsd:documentation>Specific person flag (attributes with special meaning)</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of previous application</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="requiredPerson" type="Person" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>required person</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="requiredPersonRole" type="PersonType" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>person type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuidApplicant" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>cuid of applicant</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="status" type="AffidavitStatus" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>status</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PersonFlag">
        <xsd:annotation>
            <xsd:documentation>Specific person flag (attributes with special meaning), Puvodne REG_NUMBER = 887, nove cif_param.flag_type.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="personAmlStatus" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about actual anti money laundering (AML) person status</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="manualAmlCheckStatus" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about actual AML verification (manually by operator)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="relatedParty" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>person with some relationship to PPF group.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Document">
        <xsd:annotation>
            <xsd:documentation>Document information. For person is expected only document records from document group "IDENTIFICATION" or "FINACIAL".
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="number" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>number of document</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="country" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>country, where document was issued.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="isPrimary" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document flag defines if document is set as primary.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="issueDate" type="xsd:date" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date when document was issued.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="validTo" type="xsd:date" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date when document ends its validity.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="deliveryChannel" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>channel which was used for delivery to Air/Bank</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="documentType" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="documentGroup" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document group (FINANCIAL, IDENTIFICATION, ...)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="documentId" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikační číslo dokumentu - číslo dodaného dokladu v AMS</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="modifTime" type="xsd:dateTime" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="code" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Strojový kód identifikačního dokladu</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="identificationNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>IČO zaměstnavatele</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="incomeType" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ příjmu: MAIN, OTHER, ALL</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="netIncome" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Potvrzený příjem klient</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="taxNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Daňové identifikační číslo - DIČ</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="completionByOperator" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace operátora, nebo systému provádějícího kompletaci.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="processingDate" type="xsd:dateTime" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Datum a čas kompletace dokumentu.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="documentStatusOrigin" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj změny stavu dokumentu: MANUAL/OCR.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="faceMatchScore" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek porovnání fotografí, pokud se prováděl.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="source" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zdroj dokladu.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="newIdentification" type="NewIdentification" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Nová identifikace osoby.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="NewIdentification">
        <xsd:annotation>
            <xsd:documentation>
                Nová identifikace osoby.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="businessProcessEvent" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Operátorem zamýšlená úprava, může být jen
                                IDENTITY_CLIENT_CORRECTION
                                IDENTITY_LEGAL_CHANGE
                                Obecně se jedná o položky MDM číselníku IdentityProcessEvent.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="operatorEmployeeNumber" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identifikace operatora podle LDAP
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="identification" type="Identification">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Budoucí identifikace klienta podle specifikace v CIF.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Identification">
        <xsd:choice>
            <xsd:element name="simpleIdentification" type="SimpleIdentification"/>
            <xsd:element name="standardIdentification" type="StandardIdentification"/>
            <xsd:element name="fullIdentification" type="FullIdentification"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="SimpleIdentification">
        <xsd:annotation>
            <xsd:documentation>
                Jednoduchá identifikace
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="quintupleIdentity" type="QuintupleIdentity">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita pětice
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="StandardIdentification">
        <xsd:annotation>
            <xsd:documentation>
                Standardní identifikace
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="quintupleIdentity" type="QuintupleIdentity">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita pětice
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="aifoIdentity" type="AifoIdentity" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita AIFO
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="FullIdentification">
        <xsd:annotation>
            <xsd:documentation>
                Plná identifikace
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="quintupleIdentity" type="QuintupleIdentity">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita pětice
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="birthNumberIdentity" type="BirthNumberIdentity">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita českého rodného čísla
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="aifoIdentity" type="AifoIdentity" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Identita AIFO
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="QuintupleIdentity">
        <xsd:sequence>
            <xsd:element name="firstName" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Jméno
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="lastName" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Příjmení
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="birthDate" type="xsd:date">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Datum narození
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="birthPlace" type="BirthPlace">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Místo narození
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BirthPlace">
        <xsd:annotation>
            <xsd:documentation>Místo narození</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="discriminator" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Položky z BirthPlaceDiscriminator.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="stateToDisplay" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Stát narození k zobrazení
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="placeToDisplay" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Místo narození k zobrazení
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:choice minOccurs="0">
                <xsd:element name="ruianBirthPlace" type="RuianBirthPlace">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Místo narození podle RUIAN (implicitně CZ)
                                </jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="countryBirthPlace" type="CountryBirthPlace">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Místo narození určením země a textové specifikace přesného místa narození
                                </jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="outsideCountryBirthPlace" type="OutsideCountryBirthPlace">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>
                                    Místo narození určené popisem mimo určení země
                                </jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="RuianBirthPlace">
        <xsd:choice>
            <xsd:element name="townBirthPlace" type="TownBirthPlace"/>
            <xsd:element name="pragueMunicipalDistrictBirthPlace" type="PragueMunicipalDistrictBirthPlace"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="TownBirthPlace">
        <xsd:sequence>
            <xsd:element name="townCode" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Kód obce podle specifikace RUIAN
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PragueMunicipalDistrictBirthPlace">
        <xsd:sequence>
            <xsd:element name="pragueMunicipalDistrictCode" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Kód městského obvodu Prahy v RUIAN
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CountryBirthPlace">
        <xsd:sequence>
            <xsd:element name="alpha2Code" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Dvoupísmenná hodnota z číselníku Country (CIF Codelist items)
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="location" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Textová specifikace přesného místa narození pro danou zemi.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="OutsideCountryBirthPlace">
        <xsd:sequence>
            <xsd:element name="place" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Textová specifikace ne zcela přesného místa narození, například "na moři"
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AifoIdentity">
        <xsd:sequence>
            <xsd:element name="aifo" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Agendový identifikator fyzické osoby
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BirthNumberIdentity">
        <xsd:sequence>
            <xsd:element name="birthNumber" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                České rodné číslo
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ContactType">
        <xsd:annotation>
            <xsd:documentation>contact type</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PRIMARY_EMAIL"/>
            <xsd:enumeration value="PRIMARY_MOBILE"/>
            <xsd:enumeration value="PHONE"/>
            <xsd:enumeration value="ACCOUNT_EMAIL"/>
            <xsd:enumeration value="EMPLOYMENT_PHONE"/>
            <xsd:enumeration value="ACCOUNT_MOBILE"/>
            <xsd:enumeration value="EMAIL"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="DocumentType">
        <xsd:annotation>
            <xsd:documentation>document type, relevant for document group.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ACC_AGREEMENT"/>
            <xsd:enumeration value="ACC_CONFIRMATION"/>
            <xsd:enumeration value="ACC_STATEMENT"/>
            <xsd:enumeration value="APARTMENT_LEASE"/>
            <xsd:enumeration value="BILL_OF_EXCHANGE"/>
            <xsd:enumeration value="BIRTH"/>
            <xsd:enumeration value="BUSINESS_CERTIFICATE"/>
            <xsd:enumeration value="BUSINESS_LICENCE"/>
            <xsd:enumeration value="CONCESSION"/>
            <xsd:enumeration value="DRIVE"/>
            <xsd:enumeration value="EMPLOYMENT_CONTRACT"/>
            <xsd:enumeration value="ENERGY_BILL"/>
            <xsd:enumeration value="FOREIGN_ID_CARD"/>
            <xsd:enumeration value="GUN"/>
            <xsd:enumeration value="ID_CARD"/>
            <xsd:enumeration value="ID_CARD_SUBSTITUTED"/>
            <xsd:enumeration value="INCOME"/>
            <xsd:enumeration value="LAND_REGISTRY"/>
            <xsd:enumeration value="LEASE_AGREEMENT"/>
            <xsd:enumeration value="LICENSE_AGREEMENT"/>
            <xsd:enumeration value="LIVING_DECLARATION"/>
            <xsd:enumeration value="MARRIAGE_CERTIFICATE"/>
            <xsd:enumeration value="MOBILE_PHONE_BILL"/>
            <xsd:enumeration value="OTHER"/>
            <xsd:enumeration value="OTHER_ADDRESS"/>
            <xsd:enumeration value="OTHER_BUSINESS"/>
            <xsd:enumeration value="OTHER_ID"/>
            <xsd:enumeration value="OTHER_INCOME"/>
            <xsd:enumeration value="PASSPORT"/>
            <xsd:enumeration value="PAYSLIP"/>
            <xsd:enumeration value="PHONE_BILL"/>
            <xsd:enumeration value="PURCHASE_CONTRACT"/>
            <xsd:enumeration value="RETIREMENT"/>
            <xsd:enumeration value="RETIREMENT_CONFIRMATION"/>
            <xsd:enumeration value="SERVICE_RENT"/>
            <xsd:enumeration value="SHARE_STATEMENT"/>
            <xsd:enumeration value="SIPO"/>
            <xsd:enumeration value="SPOUSES_JOINT_ASSETS"/>
            <xsd:enumeration value="STAY_PERMIT"/>
            <xsd:enumeration value="STUDENT_CONFIRMATION"/>
            <xsd:enumeration value="TAXOFF_NODEBT"/>
            <xsd:enumeration value="TAX_RETURN"/>
            <xsd:enumeration value="TRADE_REGISTER"/>
            <xsd:enumeration value="LOAN_CONTRACT"/>
            <xsd:enumeration value="CC_STATEMENT"/>
            <xsd:enumeration value="OTHER_OBLIGATION_DOCUMENT"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="ChangeLoanParameters">
        <xsd:annotation>
            <xsd:documentation>application for loan parameter changes on active contract.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="loanNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>loan number - unique identifier of loan contract from contract management system.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="applicationLoanType" type="ApplicationLoanType" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>type of application relevant for loan application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="requiredAction" type="RequiredAction" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>type of application relevant for loan application</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="RefinancedLoans">
        <xsd:annotation>
            <xsd:documentation>candidate obligations records for loan consolidation.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="candidateObligationId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifikátor úvěru (pořadové číslo) pro rozlišení jednotlivých úvěrů.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="financialInstitution" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Finanční instituce (kód), která vede refinancovaný úvěr - hodnota z číselníku Master Data managementu (MDM) pro
                                FINANCIAL INSTITUTION. Hodnota vybraná klientem.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="pairedContracts" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor BRKI/HA uložený na žádosti</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="creditType" type="MortgageRefCreditType" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ závazku k refinancování hypotéka/stavební spoření.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="status" type="ConsolidatedLoanStatus" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Status refinancování závazku.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:choice minOccurs="1" maxOccurs="1">
                <xsd:element name="savingLoans" type="ConstructionSavingsLoan" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Úvěr ze stavebního spoření</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="mortgage" type="Mortgage" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Hypotéka</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ConsolidatedLoans">
        <xsd:annotation>
            <xsd:documentation>candidate obligations records for loan consolidation.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="candidateObligationId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of candidate obligation (defined by application management system)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="financialInstitution" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>responsible financial institution for candidate obligation</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="status" type="ConsolidatedLoanStatus" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>candidate obligation status</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="creditType" type="LoanCreditType" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Obligation's type: cash loan/credit card/overdraft</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:choice minOccurs="1" maxOccurs="1">
                <xsd:element name="cashLoan" type="CashLoan" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Information about cashLoan [in case it is chosen]</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="creditCard" type="CreditCard" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Information about credit card [in case it is chosen]</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="overDraft" type="OverDraft" minOccurs="1" maxOccurs="1">
                    <xsd:annotation>
                        <xsd:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Information about overDraft [in case it is chosen]</jxb:javadoc>
                            </jxb:property>
                        </xsd:appinfo>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
            <xsd:element name="processOrigin" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>process, when data was collected (calculated)expected value are from mdm register: PRESCORING, BRKI, UNDERWRITING,
                                POSTSCORING, MANUAL_PAIRING, MANUAL_PAIRING_BLAZE
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="calculation" type="ConsolidatedLoansCalculation" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>result of calculation for candidate obligation.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="pairedContracts" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>paired contracts</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Číslo úvěru z OBS.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="isInternal" type="xsd:boolean" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikace, zda se jedná o interní závazek.(Zda je z AirBank)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ConsolidatedLoanStatus">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ACTIVE"/>
            <xsd:enumeration value="REJECTED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="LoanCreditType">
        <xsd:annotation>
            <xsd:documentation>Credit type of candidate obligation / CC, cash loan, overdraft</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="CREDIT_CARD">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>credit card obligation type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CASHLOAN">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>cash loan obligation type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OVERDRAFT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>overdraft obligation type</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="MortgageRefCreditType">
        <xsd:annotation>
            <xsd:documentation>Typ produktu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="MORTGAGE">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Hypotéka</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CONSTRUCTION_SAVINGS_LOAN">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úvěr ze stavebního spoření</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="CandidateObligationCommon" abstract="true">
        <xsd:sequence>
            <xsd:element name="approvedAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>approved loan amount of candidate obligation.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="monthlyFeesAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>sum of monthly fees for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CashLoan">
        <xsd:annotation>
            <xsd:documentation>cashloan candidate obligation</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="CandidateObligationCommon">
                <xsd:sequence>
                    <xsd:element name="residualAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Actual amount to be paid</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="installmentCount" type="xsd:int" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>count of installments for candidate obligation loan.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="installmentAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>amount of installment for candidate obligation loan.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="instalmentDay" type="xsd:int" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Den splácení : vyplněno v případě procesu "změna dne splácení"</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dateOfActivation" type="xsd:date" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>date of activation for candidate obligation loan.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="currency" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="CreditCard">
        <xsd:annotation>
            <xsd:documentation>credit card candidate obligation</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="CandidateObligationCommon">
                <xsd:sequence>
                    <xsd:element name="actualBalance" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Actual amount to be paid</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dateOfActivation" type="xsd:date" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Credit card is valid from</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="OverDraft">
        <xsd:annotation>
            <xsd:documentation>overdraft candidate obligation</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="CandidateObligationCommon">
                <xsd:sequence>
                    <xsd:element name="actualBalance" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Actual amount to be paid</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dateOfActivation" type="xsd:date" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Over Draft is open from</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="ConsolidatedLoansCalculation">
        <xsd:annotation>
            <xsd:documentation>result of calculation for candidate obligation.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="calculatedOutstanding" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculated outstanding of candidate obligation loan. sum of unpaid principal, futures interests and futures fees.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="calculatedUnpaidPrincipal" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculated unpaid principal for candidate obligation is equal to credit amount on new Air/Bank loan (just for
                                candidate)
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="calculatedInstallmentCount" type="xsd:int" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculated count of installments for new Air/Bank loan associated with candidate obligation.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="estimatedRpsn" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>estimated year effective interest rate. Attribute is mandatory for installment.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="estimatedReserve" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>empiric estimation of amount for reserve (difference between our calculation and calculation by financial institution,
                                that provided loan). Attribute is mandatory for credit card and for overdraft.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CreditOffer">
        <xsd:annotation>
            <xsd:documentation>credit offer (proposal for consolidation loan)</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="productType" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>type of credit offer, expected value is "CONSOLIDATION"</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="instalmentAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>installment amount for consolidation proposal.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="repaymentPeriod" type="xsd:int" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>repayment period means count of installment for consolidation proposal.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="rpsn" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>year effective interest rate.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>loan amount for consolidation proposal</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="loanInterest" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>loan interest based on risk grade for consolidation proposal.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="received" type="xsd:dateTime" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date and time when consolidation proposal was made.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="calculatedReserve" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculated reserve for consolidation proposal.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="upsellAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>amount for upsell, loan amount above consolidation proposal.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="responseReason" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reason of calculation error.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="processOrigin" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>process, when data was collected (calculated)expected value are from mdm register: PRESCORING, BRKI, UNDERWRITING,
                                POSTSCORING, MANUAL_PAIRING, MANUAL_PAIRING_BLAZE
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <!-- Abstract response -->

    <xsd:complexType name="AbstractResponse" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Default application response.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:sequence>
                <xsd:element name="processStatus" type="ApprovalProcessStatus"/>
            </xsd:sequence>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ApprovalProcessStatus">
        <xsd:annotation>
            <xsd:documentation>This object is sent to the external system just after workflow invocation. Wokflow hasn't finished yet.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element maxOccurs="1" minOccurs="1" name="workflowId" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifier of the new contract. This identifier has been created in the LAP system and for it the workflow has been
                                executed. The attribute value is not specified in case of any error.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element maxOccurs="1" minOccurs="1" name="statusCode" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Status message string identifier. It is identifier of the shared status message code list sharing between the external
                                system and the LAP system. Every external systems need to have this code list. The LAP system is guarantee of this code list and
                                is responsible for its content. For example: "BAD_REQUEST".
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element maxOccurs="1" minOccurs="0" name="details" type="xsd:string">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Detailed information about some error</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>


    <!-- Fault elements -->
    <xsd:complexType name="CoreFault" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Base for all application faults. It contains message (as part of standard SOAP fault) and optional arguments.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="arguments" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Optional fault arguments (they can be different for each fault type).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BusinessFault">
        <xsd:annotation>
            <xsd:documentation>Base for all business faults.</xsd:documentation>
        </xsd:annotation>

        <xsd:complexContent>
            <xsd:extension base="CoreFault">
                <xsd:sequence>
                    <xsd:element name="code" minOccurs="1"
                                 maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Fault code (specific codes are described for each operation, that can generate business fault).
                                    </jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                        <xsd:simpleType>
                            <xsd:restriction base="xsd:string">
                                <xsd:minLength value="1"/>
                                <xsd:maxLength value="100"/>
                                <xsd:pattern value="[a-zA-Z0-9.]*"/>
                            </xsd:restriction>
                        </xsd:simpleType>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="SystemFault">
        <xsd:annotation>
            <xsd:documentation>Base for all system faults.</xsd:documentation>
        </xsd:annotation>

        <xsd:complexContent>
            <xsd:extension base="CoreFault">
                <xsd:sequence>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>


    <xsd:complexType name="ValidationResult">
        <xsd:annotation>
            <xsd:documentation>Single validation result.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Validation error code (specific codes must be specified for each operation).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="attributeName" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Attribute name, that caused the validation error (if there is no corresponding attribute, it is not present).
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="args" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Optional validation error arguments.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ValidationFault">
        <xsd:annotation>
            <xsd:documentation>Base for all validation faults. Each fault contains at least one validation result.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="CoreFault">
                <xsd:sequence>
                    <xsd:element name="validationResults" type="ValidationResult" minOccurs="1" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Validation results</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="Mortgage">
        <xsd:annotation>
            <xsd:documentation>Hypotéka</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="refixDate" type="xsd:date" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Date of re-fixing</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ConstructionSavingsLoan">
        <xsd:annotation>
            <xsd:documentation>Úvěr ze stavebního spoření</xsd:documentation>
        </xsd:annotation>
    </xsd:complexType>

    <xsd:complexType name="Relation">
        <xsd:annotation>
            <xsd:documentation>Vztah</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="role" type="RelationRole" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ vztahu.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cuid2" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Cuid spoludlužníka</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="HouseholdIncomeExpense">
        <xsd:annotation>
            <xsd:documentation>Reprezentuje aplikační data pro Výdaje a příjmy domácnosti.
                Objekt plněn pouze z AMS v rámci žádosti o úvěr (půjčka, konsolidace) a refinancování hypotéky.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="householdIncomeAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Čistý měsíční příjem manžela/manželky nebo partnera/partnerky</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="medicalTransportationFoodExpenseAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měsíční výdaje domácnosti za doktory, léky, cestovné a jídlo.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="accommodationExpenseAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Měsíční výdaje domácnosti za bydlení.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="installmentsExpenseAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výdaje domácnosti za splátky půjček a hypoték (včetně půjček od Air Bank)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="genericAmount1" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výdaj/Příjem domácnosti...</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="genericAmount2" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výdaj/Příjem domácnosti...</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="genericAmount3" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výdaj/Příjem domácnosti...</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="genericAmount4" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výdaj/Příjem domácnosti...</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ShadowData">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ doplňkových shadow dat.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="brkiUnregisteredInstallments" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Splátky úvěrů nad rámec BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="brkiUnregisteredPrincipal" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jistina nad rámec BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="brkiString" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Řetězec BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="AffidavitStatus">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ACTIVE"/>
            <xsd:enumeration value="REJECTED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="RelationRole">
        <xsd:annotation>
            <xsd:documentation>Typ vztahu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="HUSBAND_WIFE">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Manželé</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PARTNER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Partneři</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PARENT_CHILD">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rodič-dítě</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OTHER_RELATION">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jiný</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DECEASED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zesnulá osoba, u které je schvalovaná osoba dědicem</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="RelationStatus">
        <xsd:annotation>
            <xsd:documentation>Stav daného vztahu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ACTIVE"/>
            <xsd:enumeration value="NOT_ACTIVE"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="MortgageRefApplication">
        <xsd:annotation>
            <xsd:documentation>Žádost o refinancování hypotéky.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AbstractMortgageApplication">
                <xsd:sequence>
                    <xsd:element name="refinancedLoans" type="RefinancedLoans" minOccurs="1" maxOccurs="unbounded">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>List závazků k refinancování. V tomto případě - hypotéka/úvěr ze stavebního spoření.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="MortgageApplication">
        <xsd:annotation>
            <xsd:documentation>Žádost o hypotéku.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="AbstractMortgageApplication"/>
        </xsd:complexContent>
    </xsd:complexType>


    <xsd:complexType name="AbstractMortgageApplication" abstract="true">
        <xsd:annotation>
            <xsd:documentation>Abstraktní předek žádosti o hypotéku, nebo její refinancování.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="creditAmountPrescoring" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Požadovaná celková částka úvěru před scoringem</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="upsellAmount" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Hodnota Upsellu</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="realPropertyValue" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Hodnota odhadu zastavované nemovitosti</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="specificRequestCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kód pro specifický požadavek. Hodnoty MDM číselníku "HYPO_SPECIAL_CASES", např. USUAL("Poznámka k převodu"),
                                        SPECIFIC_REQUEST("Specifický požadavek")
                                    </jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="applicationDataRecovery" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Příznak „data oživena“</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="creditAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Požadovaná částka úvěru</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="mortgageApplicationParametrization" type="MortgageApplicationParametrization" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Parametrizace žádosti o hypotéku, nebo její refinancování.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="internalCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Názor prodejce na klienta.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="unpaidPrincipal" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Nesplacený zůstatek jistiny. V AMS "MAIN_PURPUSE_AMOUNT" - Částka hlavního účelu (převedení/kupní cena).
                                    </jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="creditAmountUW" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Částka úvěru platná k datu ukončení manuální aktivity</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="maximalApprovedAmountUW" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Shadow limit</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="MortgageApplicationParametrization">
        <xsd:annotation>
            <xsd:documentation>Parametrizace žádosti o hypotéku, nebo její refinancování.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="paymentNum" type="xsd:int" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet splátek</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="fixDuration" type="xsd:int" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Doba fixace</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="rateCap" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Maximální garantovaná sazba</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="interestMargin" type="xsd:decimal" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Marže klienta</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="annuity" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výše měsíční splátky – dohodnutá</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="minimalAnnuity" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Minimální výše měsíční splátky</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="rateCapAnnuity" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Minimální splátka při maximální garantované sazbě</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="minimalPaymentNum" type="xsd:int" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Počet splátek pro minimální splátku</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="maxRepaymentPeriod" type="xsd:int" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Maximální délka splácení.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="defaultFixDuration" type="xsd:int" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Defaultní doba fixace z AMS</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="rateType" type="RateType" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ úrokové sazby</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="OverdraftApplication">
        <xsd:annotation>
            <xsd:documentation>Žádost o kontokorent.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="creditAmount" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Požadovaná výše kontokorentu v dané měně.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="creditAmountPreScoring" type="xsd:decimal" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Požadovaná výše kontokorentu v dané měně před vstupem do scoringu pro úvěrový produkt.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="specificRequestCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Kód pro specifický požadavek.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="internalCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>Názor prodejce na klienta</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dateCreated" type="xsd:date" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                            <xsd:appinfo>
                                <jxb:property>
                                    <jxb:javadoc>date of application creation.</jxb:javadoc>
                                </jxb:property>
                            </xsd:appinfo>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="InsuranceApplication">
        <xsd:annotation>
            <xsd:documentation>Žádost o pojištění.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="Application">
                <xsd:sequence>
                    <xsd:element name="type" type="InsuranceApplicationType"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:simpleType name="InsuranceApplicationType">
        <xsd:annotation>
            <xsd:documentation>Rozlišení typu schvalovaného pojištění</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PAYMENT_PROTECTION_INSURANCE"/>
            <xsd:enumeration value="BILL_PROTECTION_INSURANCE"/>
            <xsd:enumeration value="PERSONAL_ITEMS_PROTECTION"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="RateType">
        <xsd:annotation>
            <xsd:documentation>Typ úrokové sazby.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="FIX"/>
            <xsd:enumeration value="FLOAT"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="Completion">
        <xsd:annotation>
            <xsd:documentation>Kompletace.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor žádosti v AMS.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="envelopeId" type="xsd:long" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor obálky v AMS.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contractId" type="xsd:long" minOccurs="0" maxOccurs="1"/>
            <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jedinečný identifikátor klienta</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="startCompletionSubjectType" type="CompletionSubjectType" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ předmětu kompletace, ze které byla kompletace spuštěna.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="startCompletionSubjectId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Id předmětu kompletace, ze kterého byla kompletace spuštěna.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="completionSubjects" type="CompletionSubject" minOccurs="1" maxOccurs="unbounded"/>
            <xsd:element name="isStartedManually" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Příznak, zda byla komletace spuštěna automaticky systémem, nebo manuálně operátorem.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CompletionSubject">
        <xsd:annotation>
            <xsd:documentation>Předmět kompletace.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="applicationId" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor žádosti v AMS.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="id" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor předmětu kompletace.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="type" type="CompletionSubjectType" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ předmětu kompletace.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="approvalRequired" type="xsd:boolean" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Příznak approvalRequired říká, jaké vyjádření se očekává jako výstup ze schvalovacího procesu (ke kterému předmětu
                                kompletace)
                                TRUE - Vyjádření vždy
                                FALSE - Vyjádření jen v případě zamítnutí
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="uwHandoverReason" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                If underwritting check is needed, reason for handover is filled. Value is code from MDM register.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="uwHandoverReasonDescription" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                Detail description why underwritting check is needed.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="CompletionSubjectType">
        <xsd:annotation>
            <xsd:documentation>Typ předmětu kompletace.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="RS">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rámcová smlouva</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_BU">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Běžný účet</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_HYSA">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Založení produktu HYSA</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_DK">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debetní karta</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_DK_HOLDER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debetní karta pro jiného držitele</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_CHILD_DK_HOLDER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debetní karta pro jiného detskeho držitele</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_VK">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Založení produktu VK (Virtuální karta)</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DECLARATE_HOLDER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prohlášení držitele</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DECLARATE_CHILD_HOLDER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prohlášení detskeho držitele</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANNOU_DISP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prohlášení disponenta</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANNOU_CHILD_DISP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Prohlášení detskeho disponenta</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RHY_VERIFY_CODEBTOR">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Ověření spoludlužníka na příslibu</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ADD_DISP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Založení disponenta k bankovním službám</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ADD_CHILD_DISP">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Založení detskeho disponenta k bankovním službám</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_LOAN">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>CashLoan</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_REFINANCING">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Konsolidace</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_OVERDRAFT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Kontokorent</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RHY_ACCESSN_TO_DEBT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Přistoupení k dluhu spoludlužníkem</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RHY_CODEBT_AMENDMNT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Dodatek k přistoupení spoludlužníka</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RHY_LOAN_COMMITMENT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úvěrový příslib</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RHY_LOAN_AGREEMENT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Úvěrová smlouva</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RHY_DEBTOR_AMENDMNT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Dodatek úvěrové smlouvy hlavního dlužníka</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PP_INSURANCE_CREATE">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zalozeni pojisteni schopnosti splacet</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PP_INSURANCE_CANCEL">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zruseni pojisteni schopnosti splacet</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PPI_CREATION_CODEBTOR">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zalozeni PPI - dodatek k pristoupeni</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_CHILD_SAVING_ACCOUNT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zalozeni detskeho sporiciho uctu</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CREATE_SPLIT_PAYMENT">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zalozeni platby k rozlozeni</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="Codebtor">
        <xsd:annotation>
            <xsd:documentation>Spoludlužník</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long" minOccurs="1" maxOccurs="1">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jedinečný identifikátor klienta pro spoludlužníka.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="TransactionKey">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ pro identifikaci realizované transakce v OBS</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="idRealizedTransaction" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID of realized transaction.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="idBankAccount" type="xsd:long">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>ID of bank account.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="Decision">
        <xsd:annotation>
            <xsd:documentation>Komplexní typ obsahující informace o rozhodnutí pro daný ApprovalSubject.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="result" type="xsd:string"/>
            <xsd:element name="reason" type="xsd:string" minOccurs="0"/>
            <xsd:element name="reasonClient" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AllowedDistribution">
        <xsd:sequence>
            <xsd:element name="allowedDocumentDeliveryWay" type="xsd:string"/>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="documentCount" type="xsd:int"/>
            <xsd:element name="documentGroup" type="xsd:string"/>
            <xsd:element name="documentGroupCount" type="xsd:int"/>
            <xsd:element name="documentGroupDetail" type="xsd:string"/>
            <xsd:element name="documentGroupOptional" type="xsd:boolean"/>
            <xsd:element name="documentType" type="xsd:string"/>
            <xsd:element name="loanBinFrom" type="xsd:int"/>
            <xsd:element name="operation" type="xsd:string"/>
            <xsd:element name="productType" type="xsd:string"/>
            <xsd:element name="resultNumber" type="xsd:long"/>
            <xsd:element name="workflowId" type="xsd:long"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="RequiredAction">
        <xsd:annotation>
            <xsd:documentation>type of application relevant for loan application</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="RETURN_PAYMENT"/>
            <xsd:enumeration value="CHANGE_TERM">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Změna dne splátky.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CHANGE_ANNUITY">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Změna výše splátky.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PAYMENT_HOLIDAY">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Odložení splátek.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApplicationStatus">
        <xsd:annotation>
            <xsd:documentation>AMS application status.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="DEMO">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Special status until first updateXXX WS method. It means, that the application was not actually created and is
                                internally
                                used just as a cache. A loan application in this status does not block creation of another loan application.
                            </jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LEAD">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Special status just for new contract application before invoking first scoring.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UNFINISHED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was created and there was no scoring executed.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="APPROVED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was approved (either after scoring invocation or because there was no scoring).</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="REJECTED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was rejected after scoring.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CANCELLED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application was cancelled by user or it expired.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VERIFY">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is being scored and it is in VERIFY workflow.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MANUALVERIFY">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is being scored and it is in MANUAL VERIFY workflow.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VIP_PAUSED">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is being scored and it is in VIP PAUSED workflow.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WAITING">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is being scored and it is in WAITING workflow.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MANUALPAIRING">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is being scored and it is in MANUAL PAIRING workflow.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WAIT_FOR_OFFER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is waiting for segmentation offer, client will be contacted.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ALTERNATIVE_OFFER">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>There is alternative possible and we are waiting for the client to choose.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UNDERWRITING"/>
            <xsd:enumeration value="COMPLETION">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Application is finished by client and bank employees assembled all documentation.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessMode">
        <xsd:annotation>
            <xsd:documentation>Typ schvalovacího procesu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="AD-HOC_MANUAL">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Na manuální vyžádání.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AUTOMATIC">
                <xsd:annotation>
                    <xsd:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Automatický.</jxb:javadoc>
                        </jxb:property>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessLoanPhase">
        <xsd:annotation>
            <xsd:documentation>Typ úvěrového scoringu - návaznost na initalWfCode LAPu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="LOAN"/>
            <xsd:enumeration value="UNDERWRITING"/>
            <xsd:enumeration value="WARNING"/>
            <xsd:enumeration value="VERIFICATION"/>
            <xsd:enumeration value="MANUAL_PAIRING"/>
            <xsd:enumeration value="MANUAL_CHECKS"/>
            <xsd:enumeration value="PRESCORING"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessGeneralContractPhase">
        <xsd:annotation>
            <xsd:documentation>Typ depositniho scoringu - návaznost na initalWfCode LAPu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NEW_GC"/>
            <xsd:enumeration value="WARNING"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessNewProductPhase">
        <xsd:annotation>
            <xsd:documentation>Typ depositniho scoringu - návaznost na initalWfCode LAPu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NEW_PROD"/>
            <xsd:enumeration value="WARNING"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessNewProductSyncPhase">
        <xsd:annotation>
            <xsd:documentation>
                Typ depositniho scoringu - návaznost na initalWfCode LAPu (synchronní workflow)
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NEW_PROD_SYNC"/>
            <xsd:enumeration value="WARNING"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessMortgageRefPhase">
        <xsd:annotation>
            <xsd:documentation>Typ úvěrového scoringu - návaznost na initalWfCode LAPu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="UNDERWRITING"/>
            <xsd:enumeration value="WARNING"/>
            <xsd:enumeration value="MANUAL_CHECKS"/>
            <xsd:enumeration value="MORTGAGE"/>
            <xsd:enumeration value="MANUAL_PAIRING"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="ApprovalProcessMortgagePhase">
        <xsd:annotation>
            <xsd:documentation>Typ úvěrového scoringu - návaznost na initalWfCode LAPu</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="MORTGAGE"/>
            <xsd:enumeration value="UNDERWRITING"/>
            <xsd:enumeration value="WARNING"/>
            <xsd:enumeration value="MANUAL_CHECKS"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:element name="coreFaultElement" type="CoreFault"/>
</xsd:schema>
