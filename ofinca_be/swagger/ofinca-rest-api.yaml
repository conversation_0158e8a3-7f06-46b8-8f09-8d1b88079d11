openapi: 3.0.1
info:
  title: OFINCA Rest API Documentation
  description: Api Documentation
  termsOfService: urn:tos
  contact:
    name: Air Bank
    url: https://www.airbank.cz
  version: 1.0.0
servers:
  - url: https://ofinca.{environment}.np.ab
    variables:
      environment:
        default: te00
        enum:
         - te00
         - de11
  - url: https://localhost:8000
tags:
  - name: ofinca-authorization-controller
    description: OFINCA Authorization Controller
  - name: ofinca-semaphore-offer-controller
    description: OFINCA Semaphore Offers Controller

security:
  - bearerAuth: []
paths:
  # OFINCA authentication
  /api/auth/authorize:
    post:
      tags:
        - ofinca-authentication-controller
      operationId: authorize
      description: Performs authorization using SSO Token
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizationRequestTO'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizationResponseTO'


  # OFINCA Semaphore Offers
  /api/semaphore-offers/getSemaphoreOffers:
    post:
      tags:
        - ofinca-semaphore-offer-controller
      operationId: getSemaphoreOffers
      description: Retrieves semaphore offers from CML.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSemaphoreOffersRequestTO'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSemaphoreOffersResponseTO'

  /api/semaphore-offers/setSemaphoreOfferReaction:
    post:
      tags:
        - ofinca-semaphore-offer-controller
      operationId: setSemaphoreOfferReaction
      description: Set semaphore offer reaction.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetSemaphoreOfferReactionRequestTO'
        required: true
      responses:
        204:
          description: OK

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    AuthorizationRequestTO:
      title: AuthorizationRequestTO
      type: object
      required:
        - ssoToken
      properties:
        ssoToken:
          type: string
          minLength: 1
    AuthorizationResponseTO:
      title: AuthorizationResponseTO
      type: object
      required:
        - jwt
      properties:
        jwt:
          type: string
          minLength: 1
    GetSemaphoreOffersRequestTO:
      title: GetSemaphoreOffersRequestTO
      type: object
      required:
        - cuid
        - sourceSystem
      properties:
        cuid:
          type: integer
          format: int64
        sourceSystem:
          $ref: "#/components/schemas/SourceSystemETO"
    SourceSystemETO:
      title: SourceSystemETO
      type: string
      enum:
        - CAIR
        - WDE
    GetSemaphoreOffersResponseTO:
      title: GetSemaphoreOffersResponseTO
      type: object
      required:
        - offerGroups
      properties:
        offerGroups:
          type: array
          items:
            $ref: "#/components/schemas/SemaphoreOfferDisplayGroupTO"

    SemaphoreOfferDisplayGroupTO:
      title: SemaphoreOfferDisplayGroupTO
      type: object
      required:
        - groupType
        - displayText
        - offers
      properties:
        groupType:
          $ref: "#/components/schemas/SemaphoreOfferDisplayGroupTypeETO"
        displayText:
          type: string
          example: "Nabídka služeb pro vás"
        offers:
          type: array
          items:
            $ref: "#/components/schemas/SemaphoreOfferTO"

    SemaphoreOfferDisplayGroupTypeETO:
      title: SemaphoreOfferDisplayGroupTypeETO
      type: string
      enum:
        - SALES
        - EDU
        - TOP

    SemaphoreOfferTO:
      title: SemaphoreOfferTO
      type: object
      required:
        - id
        - title
        - offerType
      properties:
        id:
          type: string
          minLength: 1
          example: "CML.12345SEMOF"
        title:
          type: string
          minLength: 1
          example: "Půjčku si sjednáte přímo v aplikaci"
        message:
          type: string
          example: "Zvládnete to během chvíle. A půjčit vám můžeme až 890 00 Kč s výhodným úrokem."
        image:
          type: string
          example: "https://mb-test.airbank.cz/de05/mobile/pujcka.jpg"
        detailUrl:
          type: string
          format: url
          example: "https://www.airbank.cz/produkty/pujcka"
        detailText:
          type: string
          example: "Více informací"
        offerType:
          $ref: "#/components/schemas/SemaphoreOfferTypeETO"

    SemaphoreOfferTypeETO:
      title: OfferTypeETO
      type: string
      enum:
        - COMMON
        - DEFAULT

    SetSemaphoreOfferReactionRequestTO:
      title: SetSemaphoreOfferReactionRequestTO
      type: object
      required:
        - semaphoreOfferId
        - reactionChange
        - cuid
      properties:
        semaphoreOfferId:
          type: string
          minLength: 1
          description: "Semaphore offer ID"
        reactionChange:
          $ref: '#/components/schemas/ReactionChangeMeaningETO'
        cuid:
          type: integer
          format: int64
    ReactionChangeMeaningETO:
      type: string
      enum:
        - LIKE
        - DISLIKE

    OfincaErrorTO:
      title: OfincaErrorTO
      type: object
      required:
        - code
        - message
      properties:
        code:
          $ref: "#/components/schemas/OfincaErrorCodeETO"
        message:
          type: string

    OfincaErrorCodeETO:
      type: string
      enum:
        - SSO_ATTRIBUTES_EMPTY
        - SSO_EMPLOYEE_NUMBER_NOT_PRESENT
        - SSO_ERROR
        - SSO_INSUFFICIENT_ROLES
        - SSO_USER_DETAILS_NOT_FOUND
        - UNAUTHORIZED_EMPLOYEE