topics:
  - name: cz.airbank.ams.travelinsurance.application.status.v1
    description:
      brief: "Topic se změnami stavu žádostí o cestovní po<PERSON>štění."
      url: "https://wiki.airbank.cz/x/dMsHGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: TravelInsuranceApplicationStatus
        artifactId: cz.airbank.ams.travelinsurance.application.TravelInsuranceApplicationStatus
        groupId: default
        description: Schema for travel insurance application status
        version: 1
        schemaRef: schemas/travel-insurance-application-status.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 consumes events for further processing in CRM"
      write:
        - principal: "User:AMS_KAFKA_USER"
          name: ams
          transactionalId: ams
          description:
            brief: "AMS produce event when travel insurance application moves to some new states"

