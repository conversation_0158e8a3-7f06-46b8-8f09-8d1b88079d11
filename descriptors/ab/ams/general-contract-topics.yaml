topics:
  - name: cz.airbank.ams.generalcontract.application.change.v1
    description:
      brief: "Topic se změnami stavu žádostí o rámcovou smlouvu. aktuáně obsahuje pouze žádosti o FOP RS"
      url: "https://wiki.airbank.cz/x/KwVrG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: GeneralContractApplicationChangeEvent
        artifactId: cz.airbank.ams.generalcontract.application.GeneralContractApplicationChangeEvent
        groupId: default
        description: Schema for general contract change events
        version: 1
        schemaRef: schemas/general-contract-change-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 consumes events for further processing in CRM"
      write:
        - principal: "User:AMS_KAFKA_USER"
          name: asm
          transactionalId: asm
          description:
            brief: "ASM produce event when general contract application moves to some new states"
  - name: cz.airbank.ams.generalcontract.application.change.v2
    description:
      brief: "Topic se změnami stavu žádostí o rámcovou smlouvu. aktuáně obsahuje pouze žádosti o FOP RS"
      url: "https://wiki.airbank.cz/x/KwVrG"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: GeneralContractApplicationChangeEvent_v2
        artifactId: cz.airbank.ams.generalcontract.application.v2.GeneralContractApplicationChangeEvent
        groupId: default
        description: Schema for general contract change events
        version: 1
        schemaRef: schemas/general-contract-change-event-v2.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 consumes events for further processing in CRM"
      write:
        - principal: "User:AMS_KAFKA_USER"
          name: asm
          transactionalId: asm
          description:
            brief: "ASM produce event when general contract application moves to some new states"
