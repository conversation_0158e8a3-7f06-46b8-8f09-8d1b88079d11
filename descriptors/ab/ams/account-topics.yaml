topics:
  - name: cz.airbank.ams.account.application.status.v1
    description:
      brief: "Topic se změnami stavu žádostí o účet."
      url: "https://wiki.airbank.cz/x/_M0HGQ"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: AccountApplicationStatus
        artifactId: cz.airbank.ams.account.application.AccountApplicationStatus
        groupId: default
        description: Schema for account application status
        version: 1
        schemaRef: schemas/account-application-status.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "SAS360 consumes events for further processing in CRM"
      write:
        - principal: "User:AMS_KAFKA_USER"
          name: ams
          transactionalId: ams
          description:
            brief: "AMS produce event when account application moves to some new states"
        - principal: "User:AMS_KAFKA_USER"
          name: asm
          transactionalId: asm
          description:
            brief: "ASM produce event when account application moves to some new states"

