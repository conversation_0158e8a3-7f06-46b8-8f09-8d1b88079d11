{"type": "record", "name": "MarketingConsentsChangeEvent", "namespace": "cz.airbank.cml.marketing.consents", "fields": [{"name": "cuid", "type": "long"}, {"name": "channels", "type": {"type": "array", "items": {"type": "record", "name": "ChannelConsent", "fields": [{"name": "channel", "type": "string"}, {"name": "status", "type": "string"}]}}}, {"name": "products", "type": {"type": "array", "items": {"type": "record", "name": "ProductConsent", "fields": [{"name": "product", "type": "string"}, {"name": "status", "type": "string"}]}}}]}