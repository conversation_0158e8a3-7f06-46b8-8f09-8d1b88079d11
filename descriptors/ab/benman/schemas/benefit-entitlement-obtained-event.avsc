{"type": "record", "name": "BenefitEntitlementObtainedEvent", "namespace": "cz.airbank.benman.benefit", "fields": [{"name": "generalContractNumber", "type": "string"}, {"name": "benefitGeneralContractId", "type": "long"}, {"name": "benefitType", "type": "string"}, {"name": "parameter", "type": "string"}, {"name": "entitledFrom", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "entitledTo", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "evaluatedMonth", "type": {"type": "string", "logicalType": "iso-local-date"}}, {"name": "evaluatedOn", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}