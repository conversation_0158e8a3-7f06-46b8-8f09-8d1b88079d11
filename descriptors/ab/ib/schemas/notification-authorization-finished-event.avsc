{"type": "record", "namespace": "cz.airbank.frontend.event.notification", "name": "NotificationAuthorizationFinishedEvent", "doc": "Avro message for notification about authorization finished from ib-ws to frontends.", "fields": [{"name": "authId", "type": "string", "doc": "Authorization id from case."}, {"name": "authState", "type": {"type": "enum", "name": "AuthorizationState", "symbols": ["IN_PROGRESS", "AUTHORIZED", "FAILED", "TIMED_OUT"]}}]}