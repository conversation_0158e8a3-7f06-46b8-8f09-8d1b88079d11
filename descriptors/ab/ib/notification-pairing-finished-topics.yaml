topics:
  - name: cz.airbank.frontend.event.notification.pairing.finished.v1
    description:
      brief: "Topic s eventy pro notifikaci dokoncene<PERSON> parovani."
      url: "https://wiki.airbank.cz/display/KPA/PAPUCA"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "600000"
      "delete.retention.ms": "500000"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
      "segment.ms": "600000"
      "segment.bytes": "********"
    keyType: unknown
    schema:
      - name: NotificationPairingFinishedEvent
        artifactId: cz.airbank.frontend.event.notification.NotificationPairingFinishedEvent
        groupId: default
        description: Schema for notification pairing finished
        version: 1
        schemaRef: schemas/notification-pairing-finished-event.avsc
    acl:
      read:
        - principal: "User:IB_KAFKA_USER"
          name: ib
          group: ib-frontend
          generateDlt: true
          description:
            brief: "IB consume notification about pairing finished."
      write:
        - principal: "User:IB_WS_KAFKA_USER"
          name: ib-ws
          description:
            brief: "IB WS send notification about pairing finished."
