topics:
  - name: cz.airbank.frontend.event.notification.authorization.finished.v1
    description:
      brief: "Topic s eventy pro notifikaci dokoncene autorizace."
      url: "https://wiki.airbank.cz/display/KPA/PAPUCA"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "600000"
      "delete.retention.ms": "500000"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
      "segment.ms": "600000"
      "segment.bytes": "********"
    keyType: unknown
    schema:
      - name: NotificationAuthorizationFinishedEvent
        artifactId: cz.airbank.frontend.event.notification.NotificationAuthorizationFinishedEvent
        groupId: default
        description: Schema for notification authorization finished
        version: 1
        schemaRef: schemas/notification-authorization-finished-event.avsc
    acl:
      read:
        - principal: "User:IB_KAFKA_USER"
          name: ib
          group: ib-frontend
          generateDlt: true
          description:
            brief: "IB consume notification about authorization finished."
      write:
        - principal: "User:IB_WS_KAFKA_USER"
          name: ib-ws
          description:
            brief: "IB WS send notification about authorization finished."
