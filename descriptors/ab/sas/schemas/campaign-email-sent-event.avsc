{"type": "record", "name": "SentCampaignEmailEvent", "namespace": "cz.airbank.sas.campaign.email", "fields": [{"name": "cuid", "type": "long"}, {"name": "relatedCuid", "type": ["null", "long"], "default": null}, {"name": "personFirstName", "type": ["null", "string"], "default": null}, {"name": "personLastName", "type": ["null", "string"], "default": null}, {"name": "personEmail", "type": ["null", "string"], "default": null}, {"name": "creator", "type": "string"}, {"name": "externalId", "type": "string"}, {"name": "communicationKind", "type": "string"}, {"name": "products", "type": ["null", {"type": "array", "items": "string"}], "default": null}, {"name": "campaignCode", "type": "string"}, {"name": "campaignName", "type": "string"}, {"name": "communicationCode", "type": "string"}, {"name": "taskVersionId", "type": "string"}, {"name": "communicationName", "type": "string"}, {"name": "businessSummaryCauseCode", "type": ["null", "string"], "default": null}, {"name": "emailSubject", "type": "string"}, {"name": "emailURL", "type": "string"}, {"name": "timeSent", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "created", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}