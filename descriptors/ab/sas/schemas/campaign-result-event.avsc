{"type": "record", "name": "SAS360MessageResultEvent", "namespace": "cz.airbank.sas.campaign", "fields": [{"name": "externalId", "type": "string"}, {"name": "entityId", "type": ["null", "string"], "default": null}, {"name": "messageType", "type": "string"}, {"name": "status", "type": "string"}, {"name": "detail", "type": ["null", "string"], "default": null}, {"name": "createdAt", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "eventDateTime", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}