{"type": "record", "name": "ConditionFulfilledEvent", "namespace": "cz.airbank.ctc.condition.v2", "fields": [{"name": "conditionType", "type": "string"}, {"name": "conditionFulfillmentDate", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "generalContractNumber", "type": "string"}, {"name": "generalContractOwnerCuid", "type": "long"}, {"name": "evaluatedMonth", "type": {"type": "string", "logicalType": "iso-local-date"}}]}