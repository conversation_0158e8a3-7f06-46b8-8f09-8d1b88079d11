topics:
  - name: cz.airbank.rmd.devicepairing.success.v1
    description:
      brief: Topic o uspešnem párování zařízení
      url: "https://wiki.airbank.cz/display/SA/SucessfullDevicePairing+topic"
    partitions: 10
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: long
    schema:
      - name: SuccessfulDevicePairingEvent
        artifactId: cz.airbank.rmd.devicepairing.SuccessfulDevicePairingEvent
        groupId: default
        description: Schema for successful device pairing events
        version: 1
        schemaRef: schemas/successful-pairing-event.avsc
    acl:
      read:
        - principal: "User:SAS_KAFKA_USER"
          name: sas
          group: sas-agent-kafka
          generateDlt: true
          description:
            brief: "inbound events"
      write:
        - principal: "User:RMD_KAFKA_USER"
          name: rmd
          description:
            brief: "outbound events"
