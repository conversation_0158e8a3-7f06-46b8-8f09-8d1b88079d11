{"type": "record", "name": "SuccessfulDevicePairingEvent", "namespace": "cz.airbank.rmd.devicepairing", "fields": [{"name": "cuid", "type": "long"}, {"name": "cuid<PERSON>uth", "type": ["null", "long"], "default": null}, {"name": "installationId", "type": "string"}, {"name": "firstPairing", "type": ["null", "boolean"], "default": null}, {"name": "deviceName", "type": "string"}, {"name": "deviceType", "type": ["null", "string"], "default": null}, {"name": "deviceStatus", "type": ["null", {"type": "enum", "name": "DeviceStatus", "symbols": ["ACTIVE", "BLOCKED", "EXPIRED", "REGISTERED", "TERMINATED"]}], "default": null}, {"name": "activeOperations", "type": ["null", "boolean"], "default": null}]}