topics:
  - name: cz.cvak.pcm.notification.sticker.terminate.v1
    description:
      brief: "Topic for cvak sticker notification events"
      url: "https://wiki.airbank.cz/display/SA/stickerTerminated+topic"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: Schema for cvak sticker terminated notification event
        artifactId: cz.cvak.pcm.notification.sticker.terminate.v1.CvakStickerTerminatedEvent
        groupId: default
        description: Schema for cvak sticker terminated notification event
        version: 1
        schemaRef: schema/terminate-sticker-notification-event-v1.avsc
    acl:
      read:
        - principal: "User:CVAK_TRANSACTIONS_KAFKA_USER"
          name: transactions
          group: transactions
          generateDlt: true
          description:
            brief: "read sticker notification event"
      write:
        - principal: "User:CVAK_PCM_KAFKA_USER"
          name: pcm
          description:
            brief: "write sticker notification event"