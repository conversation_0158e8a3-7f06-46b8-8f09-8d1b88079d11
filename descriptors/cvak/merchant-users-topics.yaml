topics:
  - name: cz.cvak.pcm.notification.merchantuser.terminate.v1
    description:
      brief: "Topic for cvak merchant users notification events"
      url: "https://wiki.airbank.cz/display/SA/merchantUserTerminated+topic"
    partitions: 6
    replicationFactor: 3
    config:
      "retention.ms": "**********"
      "cleanup.policy": "delete"
      "min.insync.replicas": "2"
    keyType: unknown
    schema:
      - name: Schema for cvak merchant user terminated notification event
        artifactId: cz.cvak.pcm.notification.merchantuser.terminate.v1.CvakMerchantUserTerminatedEvent
        groupId: default
        description: Schema for cvak merchant user terminated notification event
        version: 1
        schemaRef: schema/terminate-merchant-user-notification-event-v1.avsc
    acl:
      read:
        - principal: "User:CVAK_TRANSACTIONS_KAFKA_USER"
          name: transactions
          group: transactions
          generateDlt: true
          description:
            brief: "read merchant user notification event"
      write:
        - principal: "User:CVAK_PCM_KAFKA_USER"
          name: pcm
          description:
            brief: "write merchant user notification event"