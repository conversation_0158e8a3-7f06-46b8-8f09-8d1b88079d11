package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.ibpromoad.v3.SendIbPromoAdEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.spring.boot.kafka.client.AbstractKafkaProducer;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SendIbPromoAdEventKafkaProducer extends AbstractKafkaProducer<Long, SendIbPromoAdEvent> {

    public SendIbPromoAdEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig.getKafka().getTopics().get(AgentType.IB_PROMO_AD), applicationConfig.getKafka().getTimeout());
    }
}
