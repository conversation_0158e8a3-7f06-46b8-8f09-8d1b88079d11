package cz.airbank.sas_agent_chstream.cache;

import cz.airbank.sas_agent_chstream.client.ci360.CI360Api;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * Original SAS code
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskCache {

    private static HashMap<String, TaskCacheEntry> cache = new HashMap<>();    //Task CP cache !!!!!!!!!!
    private static HashMap<String, Object> locks = new HashMap<>();        //used to synchronizace processing each taskVersionId (one thread in one moment)
    private final DataSource dataSource;
    private final ApplicationConfig applicationConfig;
    private final CI360Api ci360Api;

    private static void addCPEntryToBatch(PreparedStatement st, String taskVersionId, String cpName, String cpValue) throws SQLException {
        st.setString(1, taskVersionId);
        st.setString(2, cpName);
        if (cpValue == null) {
            st.setNull(3, Types.VARCHAR);
        } else {
            st.setString(3, cpValue);
        }
        st.addBatch();
    }

    public TaskCacheEntry getTask(String taskVersionId, String taskId) {
        LocalDateTime start = LocalDateTime.now();
        //add taskVersionId to locks array if missing, used to synchronize processing of taskVersionId (one taskVersionId just in one thread in one moment)
        synchronized (locks) {
            if (locks.get(taskVersionId) == null) {
                locks.put(taskVersionId, new Object());
            }
        }

        synchronized (locks.get(taskVersionId)) {
            //get task from cache in memory
            TaskCacheEntry te = cache.get(taskVersionId);

            //task is in application cache
            if (te != null) {
                log.debug("TaskVersionId={} loaded from application cache, duration[ms]: {}", taskVersionId,
                          Duration.between(start, LocalDateTime.now()).toMillis());
                return te;
            }

            //if not exist load from Task cache db table
            try {
                te = loadCacheTaskCp(taskVersionId, taskId);
                if (te != null) {
                    log.debug("TaskVersionId={} loaded from TaskCache table cie_contact_history_stream_task_cp_cache, duration[ms]: {}", taskVersionId,
                              Duration.between(start, LocalDateTime.now()).toMillis());
                    addTaskCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                log.error("Error when loading CP from cie_contact_history_stream_task_cp_cache", e);
            }

            //if not exist read from CI 360 API
            try {
                te = getCI360ApiTaskCp(taskId, taskVersionId);

                if (te != null) {
                    log.debug("TaskVersionId={} loaded from CI360 API, and saved to TaskCache table, duration[ms]: {}", taskVersionId,
                              Duration.between(start, LocalDateTime.now()).toMillis());
                    saveTaskCpToCache(te);
                    addTaskCacheEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                log.error("Exception()", e);
            }
            log.debug("Failed to load task cache for taskVersionId {}", taskVersionId);
            return null;        //error
        }
    }

    public TaskCacheEntry getCI360ApiTaskCp(String taskId, String taskVersionId) {
        TaskCacheEntry te = new TaskCacheEntry();
        te.taskVersionId = taskVersionId;
        te.taskId = taskId;

        //get cp from ci360 api
        List<String> requiredCP = te.getCacheVariableList();
        HashMap<String, String> taskCP = ci360Api.getTaskCustomProperties(taskId, requiredCP);

        //assign to CP
        te.tsk_comm_chan_code.setValueWithDefault(taskCP.get("tsk_comm_chan_code"));
        te.tsk_comm_camp_name.setValueWithDefault(taskCP.get("tsk_comm_camp_name"));
        te.tsk_camp_type.setValueWithDefault(taskCP.get("tsk_camp_type"));
        te.tsk_camp_subtype.setValueWithDefault(taskCP.get("tsk_camp_subtype"));
        te.tsk_camp_product.setValueWithDefault(taskCP.get("tsk_camp_product"));
        te.tsk_camp_buss_cause_cd.setValueWithDefault(taskCP.get("tsk_camp_buss_cause_cd"));
        te.tsk_camp_comm_type.setValueWithDefault(taskCP.get("tsk_camp_comm_type"));
        te.tsk_cp_type.setValueWithDefault(taskCP.get("tsk_cp_type"));
        te.tsk_cp_product.setValueWithDefault(taskCP.get("tsk_cp_product"));
        te.taskName.setValueWithDefault(taskCP.get("taskName"));

        return te;
    }

    private void addTaskCacheEntryToCache(TaskCacheEntry te) {
        cache.put(te.taskVersionId, te);
    }

    public TaskCacheEntry loadCacheTaskCp(String taskVersionId, String taskId) throws SQLException, InterruptedException, ClassNotFoundException {
        return loadDatabaseTaskCp(applicationConfig.getDatabaseName() + ".cie_contact_history_stream_task_cp_cache", taskVersionId, taskId);
    }

    private TaskCacheEntry loadDatabaseTaskCp(String dbTable, String taskVersionId, String taskId) throws SQLException, InterruptedException, ClassNotFoundException {
        //save message to DB table queue
        String sql = "SELECT  attribute_nm,attribute_val  FROM " + dbTable + " WHERE task_version_id=?";

        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql)
        ) {
            /*input parameter, batch size*/
            st.setString(1, taskVersionId);

            ResultSet rs = st.executeQuery();

            if (rs.next() != false) {    //test if rs is not empty
                TaskCacheEntry te = new TaskCacheEntry();
                te.taskVersionId = taskVersionId;
                te.taskId = taskId;

                do {    //for each custom property
                    String name = rs.getString("attribute_nm");
                    String value = rs.getString("attribute_val");

                    if (name.equalsIgnoreCase(te.tsk_comm_chan_code.name))
                        te.tsk_comm_chan_code.value = value;
                    if (name.equalsIgnoreCase(te.tsk_comm_camp_name.name))
                        te.tsk_comm_camp_name.value = value;
                    if (name.equalsIgnoreCase(te.tsk_camp_type.name))
                        te.tsk_camp_type.value = value;
                    if (name.equalsIgnoreCase(te.tsk_camp_subtype.name))
                        te.tsk_camp_subtype.value = value;
                    if (name.equalsIgnoreCase(te.tsk_camp_product.name))
                        te.tsk_camp_product.value = value;
                    if (name.equalsIgnoreCase(te.tsk_camp_buss_cause_cd.name))
                        te.tsk_camp_buss_cause_cd.value = value;
                    if (name.equalsIgnoreCase(te.tsk_camp_comm_type.name))
                        te.tsk_camp_comm_type.value = value;
                    if (name.equalsIgnoreCase(te.tsk_cp_type.name))
                        te.tsk_cp_type.value = value;
                    if (name.equalsIgnoreCase(te.tsk_cp_product.name))
                        te.tsk_cp_product.value = value;
                    if (name.equalsIgnoreCase(te.taskName.name))
                        te.taskName.value = value;

                    if (te.tsk_comm_chan_code.hasValue() &&
                            te.tsk_comm_camp_name.hasValue() &&
                            te.tsk_camp_type.hasValue() &&
                            te.tsk_camp_subtype.hasValue() &&
                            te.tsk_camp_product.hasValue() &&
                            te.tsk_camp_buss_cause_cd.hasValue() &&
                            te.tsk_camp_comm_type.hasValue() &&
                            te.tsk_cp_type.hasValue() &&
                            te.tsk_cp_product.hasValue() &&
                            te.taskName.hasValue()
                    ) {
                        //all task custom properties loaded, no need to continue in iteration via remaining rows in table
                        return te;
                    }
                } while (rs.next());
            }
        } catch (SQLException e) {
            log.error("Exception()", e);
        }
        return null;
    }

    public void saveTaskCpToCache(TaskCacheEntry te) throws SQLException, InterruptedException, ClassNotFoundException {
        String sql = "INSERT INTO " + applicationConfig.getDatabaseName() + ".cie_contact_history_stream_task_cp_cache  (task_version_id,attribute_nm,attribute_val)    VALUES(?,?,?)";
        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql)
        ) {
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_comm_chan_code.name, te.tsk_comm_chan_code.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_comm_camp_name.name, te.tsk_comm_camp_name.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_type.name, te.tsk_camp_type.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_subtype.name, te.tsk_camp_subtype.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_product.name, te.tsk_camp_product.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_buss_cause_cd.name, te.tsk_camp_buss_cause_cd.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_camp_comm_type.name, te.tsk_camp_comm_type.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_cp_type.name, te.tsk_cp_type.value);
            addCPEntryToBatch(st, te.taskVersionId, te.tsk_cp_product.name, te.tsk_cp_product.value);
            addCPEntryToBatch(st, te.taskVersionId, te.taskName.name, te.taskName.value);

            st.executeBatch();
        } catch (SQLException e) {
            log.error("Exception()", e);
        }
    }
}
