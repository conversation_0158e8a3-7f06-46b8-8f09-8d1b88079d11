package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.email.SentCampaignEmailEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.spring.boot.kafka.client.AbstractKafkaProducer;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SentCampaignEmailEventKafkaProducer extends AbstractKafkaProducer<Long, SentCampaignEmailEvent> {

    public SentCampaignEmailEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig.getKafka().getTopics().get(AgentType.CH_STREAM), applicationConfig.getKafka().getTimeout());
    }
}
