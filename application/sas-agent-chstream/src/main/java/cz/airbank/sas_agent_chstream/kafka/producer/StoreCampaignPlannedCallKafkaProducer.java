package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.planned.call.CampaignPlannedCallEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.spring.boot.kafka.client.AbstractKafkaProducer;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class StoreCampaignPlannedCallKafkaProducer extends AbstractKafkaProducer<Long, CampaignPlannedCallEvent> {

    public StoreCampaignPlannedCallKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig.getKafka().getTopics().get(AgentType.CAMPAIGN_PLANNED_CALL), applicationConfig.getKafka().getTimeout());
    }
}
