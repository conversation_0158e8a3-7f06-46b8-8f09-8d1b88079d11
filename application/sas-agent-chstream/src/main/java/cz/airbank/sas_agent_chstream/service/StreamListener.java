package cz.airbank.sas_agent_chstream.service;

import com.sas.mkt.agent.sdk.CI360Agent;
import com.sas.mkt.agent.sdk.CI360AgentException;
import com.sas.mkt.agent.sdk.CI360StreamInterface;
import com.sas.mkt.agent.sdk.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@RequiredArgsConstructor
public class StreamListener implements CI360StreamInterface {

    private static final AtomicBoolean alreadySeenStreamClosedCall = new AtomicBoolean(false);
    @Setter
    private static boolean exiting = false;
    private final ReceiverService receiverService;
    private final CI360Agent agent;
    private final AtomicInteger activeTasks = new AtomicInteger(0);

    @Override
    public boolean processEvent(String event) {
        if (exiting) {
            log.info("Agent is exiting, event will not be processed: {}", event);
            return false;
        }
        activeTasks.incrementAndGet();
        try {
            receiverService.processEvent(event);
        } finally {
            activeTasks.decrementAndGet();
        }
        return true;
    }

    @Override
    public void streamClosed(ErrorCode errorCode, String message) {
        if (exiting) {
            log.info("Agent is exiting, stream closed");
            return;
        }

        log.info("Stream closed {}: {}", errorCode, message);
        if ((message != null) && (
                message.contains("MKTCMN74224") ||   // incorrect JWT (bad format)
                        message.contains("MKTCMN74248") ||   // tenant missing (unknown tenant.  maybe using wrong stack)
                        message.contains("MKTCMN74261") ||   // invalid JWT (doesn't match any access points)
                        message.contains("MKTCMN74265") ||   // agent out of date (version of API not supported by extapigw
                        message.contains("MKTCMN74282")      // tenant is not licensed
        )) {
            System.exit(-1);
        }
        if (alreadySeenStreamClosedCall.compareAndSet(false, true)) {
            log.debug("Passed compareAndSet test");
            final CI360StreamInterface ci360StreamInterface = this;
            Thread startThread = new Thread(() -> {
                try {
                    Thread.sleep(15000);
                } catch (InterruptedException e) {

                }
                alreadySeenStreamClosedCall.set(false);
                try {
                    agent.startStream(ci360StreamInterface, true);
                } catch (CI360AgentException e) {
                    log.error("ERROR {}: {}", e.getErrorCode(), e.getMessage());
                }
            });
            startThread.setName("AgentRestart-");
            startThread.start();
        }
    }

    public void awaitShutdown() throws InterruptedException {
        while (activeTasks.get() > 0) {
            log.info("Waiting for all workers to finish. Currently active {} threads", activeTasks.get());
            Thread.sleep(100);
        }
        log.info("All workers finished. Shutdown.");
    }
}
