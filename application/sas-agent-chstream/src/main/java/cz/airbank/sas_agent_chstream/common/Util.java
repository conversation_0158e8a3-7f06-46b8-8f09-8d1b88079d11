package cz.airbank.sas_agent_chstream.common;

import lombok.experimental.UtilityClass;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Original SAS code
 */
@UtilityClass
public class Util {

    public static String jsonReadString(JSONObject payload, String key) {
        String res = "";
        if (payload.has(key)) res = payload.getString(key);
        return res;
    }

    public static String base64Encode(String input) {
        String encodedString = Base64.getEncoder().encodeToString(input.getBytes());
        return encodedString;
    }

    public static String base64Decode(String input) {
        byte[] decodedBytes = Base64.getDecoder().decode(input);
        String decodedString = new String(decodedBytes);
        return decodedString;
    }

    public static JSONObject loadJSONFile(String file) throws Exception {

        JSONObject data = null;

        try (FileInputStream fis = new FileInputStream(file);
             InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8);
             BufferedReader reader = new BufferedReader(isr)
        ) {

            String strMapp = "";

            String str;
            while ((str = reader.readLine()) != null) {
                strMapp += str;
            }
            data = new JSONObject(strMapp);

        } catch (Exception e) {
            throw e;
        }

        return data;
    }
}

