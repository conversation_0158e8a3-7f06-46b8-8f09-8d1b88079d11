package cz.airbank.sas_agent_chstream.mapper;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public abstract class BaseMapper {

    private static final ZoneId ZONE_ID = ZoneId.of("Europe/Prague");
    private static final long EPOCH_1960 = *********; // number of seconds between 1.1.1960 and 1.1.1970. SAS uses 1960 as epoch

    protected static String getLocalDateTimeInIsoOffset(LocalDateTime dt) {
        ZonedDateTime ldtZoned = dt.atZone(ZONE_ID);
        return DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(ldtZoned);
    }

    protected static String getLocalDateTimeInIsoOffset(String timestamp) {
        try {
            long timestampLong = Long.parseLong(timestamp);
            long timestampLong1960 = timestampLong - EPOCH_1960;
            Instant instant = Instant.ofEpochSecond(timestampLong1960);
            ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, ZONE_ID);
            return zonedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        } catch (Exception e) {
            return null;
        }
    }

    protected static String getLocalDate(String timestamp) {
        try {
            long timestampLong = Long.parseLong(timestamp);
            long timestampLong1960 = timestampLong - EPOCH_1960;
            LocalDate localDate = Instant.ofEpochSecond(timestampLong1960).atZone(ZONE_ID).toLocalDate();
            return localDate.format(DateTimeFormatter.ISO_DATE);
        } catch (Exception e) {
            return null;
        }
    }

    protected static String getValue(String value1, String value2) {
        if (value1 != null && !value1.isBlank()) {
            return value1;
        }
        return value2;
    }

    protected static Long parseLongOrNull(String value) {
        try {
            return Long.parseLong(value);
        } catch (Exception e) {
            return null;
        }
    }

    protected static Integer parseIntOrNull(String value) {
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return null;
        }
    }

    protected static Double parseDoubleOrNull(String value) {
        try {
            return Double.parseDouble(value);
        } catch (Exception e) {
            return null;
        }
    }

    protected static Boolean parseBooleanOrNull(String value) {
        if ("1".equals(value)) {
            return true;
        }
        try {
            return Boolean.parseBoolean(value);
        } catch (Exception e) {
            return null;
        }
    }

    protected static Boolean parseBoolean(String value) {
        if ("1".equals(value)) {
            return true;
        }
        try {
            return Boolean.parseBoolean(value);
        } catch (Exception e) {
            return false;
        }
    }
}
