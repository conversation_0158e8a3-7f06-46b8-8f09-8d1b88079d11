package cz.airbank.sas_agent_chstream.validator;

import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.Schema.Field;
import org.apache.avro.Schema.Type;
import org.apache.avro.reflect.ReflectData;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AvroValidator {

    public static void validateNonNullableFields(Object record) {
        List<String> missingFields = new ArrayList<>();
        Schema schema = ReflectData.get().getSchema(record.getClass());

        for (Field avroField : schema.getFields()) {
            if (!isNullable(avroField)) {
                java.lang.reflect.Field javaField = getJavaField(record.getClass(), avroField.name());
                if (javaField != null) {
                    javaField.setAccessible(true);
                    try {
                        Object fieldValue = javaField.get(record);
                        if (fieldValue == null) {
                            missingFields.add(avroField.name());
                        }
                    } catch (IllegalAccessException ignored) {
                        // the field was set to accessible, if we catch this exception just skip the field
                    }
                }
            }
        }

        if (!missingFields.isEmpty()) {
            String missingFieldsString = String.join(",", missingFields);
            log.error("ErrorCode:CH_02 - Validation error - missing mandatory attributes in payload: {}", missingFieldsString);
            throw new CHStreamAgentException("ErrorCode:CH_02 - Validation error - missing mandatory attributes in payload: " + missingFieldsString);
        } else {
            log.debug("Validation successful");
        }
    }

    private static boolean isNullable(Field avroField) {
        Schema fieldSchema = avroField.schema();
        if (fieldSchema.getType() == Type.UNION) {
            for (Schema unionSchema : fieldSchema.getTypes()) {
                if (unionSchema.getType() == Type.NULL) {
                    return true;
                }
            }
        }
        return false;
    }

    private static java.lang.reflect.Field getJavaField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return null;
        }
    }
}
