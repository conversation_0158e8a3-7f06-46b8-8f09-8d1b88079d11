package cz.airbank.sas_agent_kafka.util;

import lombok.experimental.UtilityClass;
import org.apache.avro.Conversions;
import org.apache.avro.LogicalType;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericEnumSymbol;
import org.apache.avro.generic.GenericRecord;
import org.apache.avro.util.Utf8;

import java.nio.ByteBuffer;

@UtilityClass
public class AvroUtil {

    /**
     * Retrieves the value of a specified field from an Avro GenericRecord and converts it to a string
     * if it is of type {@link GenericEnumSymbol} or {@link org.apache.avro.util.Utf8}. This method
     * normalizes the data types retrieved from Avro records, ensuring that they are
     * returned in a consistent format for further MVEL evaluation and Jackson conversion to JSON.
     *
     * @param record The {@link GenericRecord} from which to retrieve the value.
     * @param avroFieldName The name of the field whose value is to be retrieved from the record.
     * @return The value of the specified field, converted to a {@link String} if it is an instance of
     *         {@link GenericEnumSymbol} or {@link org.apache.avro.util.Utf8}; returns the raw object
     *         otherwise. If the field does not exist or the value is null, this method returns {@code null}.
     */
    public static Object getAvroObject(GenericRecord record, String avroFieldName) {
        Object value = record.get(avroFieldName);
        if (value instanceof GenericEnumSymbol<?>) {
            value = value.toString();
        } else if (value instanceof Utf8) {
            value = value.toString();
        } else if (value instanceof ByteBuffer) {
            Schema fieldSchema = record.getSchema().getField(avroFieldName).schema();
            LogicalType logicalType = fieldSchema.getLogicalType();
            if (logicalType != null && "decimal".equals(logicalType.getName())) {
                Conversions.DecimalConversion decimalConversion = new Conversions.DecimalConversion();
                value = decimalConversion.fromBytes((ByteBuffer) value, fieldSchema, logicalType);
            }
        }
        return value;
    }

    public static String getAvroString(GenericRecord record, String avroFieldName) {
        Object value = getAvroObject(record, avroFieldName);
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    public static boolean getAvroBoolean(GenericRecord record, String avroFieldName) {
        Object value = getAvroObject(record, avroFieldName);
        if (value == null) {
            return false;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return false;
    }
}
