package cz.airbank.sas_agent_kafka;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ConfigurationPropertiesScan
@EnableScheduling
public class AirbankSenderAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(AirbankSenderAgentApplication.class, args);
    }
}
