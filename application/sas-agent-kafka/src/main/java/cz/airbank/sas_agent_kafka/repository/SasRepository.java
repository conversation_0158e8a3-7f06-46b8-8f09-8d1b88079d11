package cz.airbank.sas_agent_kafka.repository;

import cz.airbank.sas_agent_kafka.config.ApplicationConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;

@Component
@Slf4j
@RequiredArgsConstructor
public class SasRepository {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;

    public String getSourceChannelCd(String externalId) {
        String sql = "SELECT CONTACT_CHANNEL_CD FROM " + applicationConfig.getDatabaseName() + ".CIE_CONTACT_HISTORY_STREAM WHERE LEAD_ID = ?";

        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql)
        ) {
            st.setString(1, externalId);

            ResultSet rs = st.executeQuery();

            if (rs.next()) {
                return rs.getString("CONTACT_CHANNEL_CD");
            }
        } catch (SQLException e) {
            log.error("Cannot retrieve CONTACT_CHANNEL_CD from DB", e);
        }
        return null;
    }

    public void insertRow(String externalId, String sourceChannelCd, String status, String detail, String eventDateTime) {
        String sql = "INSERT INTO  " + applicationConfig.getDatabaseName() + ".CIE_INT_RESPONSE_BY_LEAD_ID (LEAD_ID, SOURCE_CHANNEL_CD, SOURCE_RESPONSE_CD, EXTERNAL_INFO_1_ID, RESPONSE_DTTM) VALUES (?, ?, ?, ?, ?) ";
        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql)
        ) {
            st.setString(1, externalId);
            st.setString(2, sourceChannelCd);
            st.setString(3, status);
            st.setString(4, detail);

            OffsetDateTime offsetDateTime = OffsetDateTime.parse(eventDateTime);
            Timestamp sqlTimestamp = Timestamp.valueOf(offsetDateTime.toLocalDateTime());
            st.setTimestamp(5, sqlTimestamp);

            st.execute();
        } catch (SQLException | DateTimeParseException e) {
            log.error("Not possible to write payload to DB", e);
        }
        log.debug("Message with leadId {} inserted into database", externalId);
    }
}
