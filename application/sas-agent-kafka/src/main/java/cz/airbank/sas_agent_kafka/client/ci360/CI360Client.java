package cz.airbank.sas_agent_kafka.client.ci360;

import com.google.common.base.Joiner;

import com.fasterxml.jackson.databind.ObjectMapper;

import io.github.resilience4j.retry.annotation.Retry;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import jakarta.annotation.PostConstruct;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;

import cz.airbank.sas_agent_kafka.client.ci360.request.EventRequest;
import cz.airbank.sas_agent_kafka.config.ApplicationConfig;
import cz.airbank.sas_agent_kafka.exception.SasCI360ProxyException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.PROXY_AUTHORIZATION;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * The {@code CI360Client} class is responsible for communicating with the CI360 system by sending event data over HTTP. It constructs HTTP requests based on
 * the provided event data and configuration settings, then sends these requests to the configured CI360 endpoint.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CI360Client {

    private final ApplicationConfig applicationConfig;
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;
    private final MeterRegistry meterRegistry;

    private Counter ci360PassedRequests;
    private Counter ci360FailedRequests;
    private Timer ci360Latency;

    @PostConstruct
    void initializeMetrics() {
        ci360PassedRequests = Counter.builder("ci_360_passed_requests")
                .tag("version", "v1")
                .description("Count of successful requests to CI360")
                .register(meterRegistry);
        ci360FailedRequests = Counter.builder("ci_360_failed_requests")
                .tag("version", "v1")
                .description("Count of unsuccessful requests to CI360")
                .register(meterRegistry);
        ci360Latency = Timer.builder("ci_360_latency")
                .tag("version", "v1")
                .description("Time of request to CI360")
                .register(meterRegistry);
    }

    /**
     * Sends an event request to the CI360 system using the configured CI360 API endpoint and authentication token. The method serializes the event request
     * object to JSON, constructs an HTTP POST request with appropriate headers, and sends it to CI360.
     *
     * @param request The event request to be sent to CI360. This object contains all necessary information about the event, including the application ID, event
     * name, and any other relevant fields.
     * @return The body of the HTTP response from CI360 as a String. This may contain information about the success or failure of the event processing by CI360.
     * @throws cz.airbank.sas_agent_kafka.exception.SasCI360ProxyException If there is an IOException or InterruptedException during the HTTP request execution,
     * indicating a failure to communicate with the CI360 system.
     */
    @Retry(name = "call360", fallbackMethod = "call360fallback")
    public String sendMessageToCI360(EventRequest request) {
        try {
            String json = objectMapper.writeValueAsString(request);
            log.info("Sending event to CI360: {}", json);

            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(applicationConfig.getCi360().getUrl()))
                .header(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                .header(AUTHORIZATION, "Bearer " + applicationConfig.getCi360().getToken())
                .timeout(Duration.ofSeconds(applicationConfig.getHttpClient().getTimeout()))
                .POST(BodyPublishers.ofString(json));
            if (isProxyUsed()) {
                requestBuilder.header(PROXY_AUTHORIZATION, "Basic " + basicAuthorization());
            }
            Timer.Sample sample = Timer.start(meterRegistry);
            HttpResponse<String> response = httpClient.send(requestBuilder.build(), BodyHandlers.ofString());
            sample.stop(ci360Latency);
            log.info("CI360 response: code: {}, body: {}", response.statusCode(), response.body());

            if (response.statusCode() != HttpStatus.CREATED.value()) {
                ci360FailedRequests.increment();
                log.warn("Failed to create event in CI360 with request {}", json);
                throw new SasCI360ProxyException("Failed to create event in CI360 with request " + json);
            }
            ci360PassedRequests.increment();
            return response.body();
        } catch (IOException | InterruptedException e) {
            ci360FailedRequests.increment();
            throw new SasCI360ProxyException(e);
        }
    }

    public String call360fallback(EventRequest request, Throwable throwable) {
        log.error("Retries exhausted for creating request in CI360 {}. Exception: {}", request, throwable);
        throw new SasCI360ProxyException("Retries exhausted for creating request in CI360.", throwable);
    }

    private String basicAuthorization() {
        return new String(encodeBasicAuthorization(), StandardCharsets.UTF_8);
    }

    private byte[] encodeBasicAuthorization() {
        return Base64.getEncoder()
            .encode(Joiner.on(':')
                        .join(applicationConfig.getCi360().getProxy().getUsername(),
                              applicationConfig.getCi360().getProxy().getPassword())
                        .getBytes(StandardCharsets.UTF_8));
    }

    private boolean isProxyUsed() {
        return applicationConfig.getCi360().getProxy() != null
               && applicationConfig.getCi360().getProxy().getUsername() != null
               && !applicationConfig.getCi360().getProxy().getUsername().isEmpty()
               && applicationConfig.getCi360().getProxy().getPassword() != null;
    }
}
