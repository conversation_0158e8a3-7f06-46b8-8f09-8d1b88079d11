package cz.airbank.sas_agent_kafka.client.ci360.request;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents an event request to be sent to the CI360 system. This class encapsulates all the necessary
 * information for an event, including its name, the application ID it's associated with, and any additional
 * fields that should be included in the request.
 * The additional fields are dynamic and can vary between different events. They are handled through a map
 * that allows for flexible inclusion of any number of fields without needing to modify the class structure.
 */
@Data
@Builder
public class EventRequest {

    /**
     * The application ID identifies the sender or the source application within the CI360 system.
     */
    private String applicationId;
    /**
     * The name of the event. This identifies the type of event being sent to CI360.
     */
    private String eventName;
    /**
     * A map of additional fields to be included in the event. These fields are dynamic and can vary between
     * different events. The map's contents are directly serialized into the JSON payload of the request, with
     * each entry becoming a separate field in the JSON object.
     * This field is ignored during normal serialization and instead is accessed through the {@link #getFields()}
     * method to enable dynamic property inclusion in the JSON output.
     */
    @JsonIgnore
    private Map<String, Object> fields;

    /**
     * Gets a copy of the additional fields map. This method is annotated with {@link JsonAnyGetter} to indicate
     * that the returned map's contents should be included as direct properties of the object during JSON serialization.
     * This approach allows for the dynamic inclusion of properties in the serialization output, making the class
     * flexible for use with various event types and their unique data requirements.
     *
     * @return A copy of the fields map, ensuring the original map's immutability.
     */
    @JsonAnyGetter
    public Map<String, Object> getFields() {
        return new HashMap<>(fields);
    }
}
