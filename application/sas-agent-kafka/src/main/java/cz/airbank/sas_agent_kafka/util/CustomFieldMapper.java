package cz.airbank.sas_agent_kafka.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomFieldMapper {

    private static final DecimalFormat AMOUNT_FORMAT;
    private static final DecimalFormat AMOUNT_FORMAT_NO_DECIMALS;

    static {
        AMOUNT_FORMAT = new DecimalFormat("###,##0.00");
        AMOUNT_FORMAT_NO_DECIMALS = new DecimalFormat("###,##0");
        DecimalFormatSymbols customSymbol = new DecimalFormatSymbols();
        customSymbol.setGroupingSeparator(' ');
        AMOUNT_FORMAT.setDecimalFormatSymbols(customSymbol);
        AMOUNT_FORMAT_NO_DECIMALS.setDecimalFormatSymbols(customSymbol);
    }

    public static String mapField(String fieldName, Object value) {
        if (value == null) {
            return null;
        }

        return switch (fieldName) {
            case "SplitPaymentTransaction_FAmount" -> formatAmount(value);
            default -> null;
        };
    }

    private static String formatAmount(Object value) {
        try {
            BigDecimal amount = convertToBigDecimal(value);
            if (amount.stripTrailingZeros().scale() <= 0) {
                return AMOUNT_FORMAT_NO_DECIMALS.format(amount);
            }
            return AMOUNT_FORMAT.format(amount);
        } catch (Exception e) {
            log.warn("Could not format amount value: {}", value, e);
            return value.toString();
        }
    }

    private static BigDecimal convertToBigDecimal(Object value) {
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            Number numberValue = (Number) value;
            return BigDecimal.valueOf((numberValue.doubleValue()));
        } else if (value instanceof String) {
            return new BigDecimal((String) value);
        }
        throw new IllegalArgumentException("Unsupported type for amount conversion: " + value.getClass());
    }
}
