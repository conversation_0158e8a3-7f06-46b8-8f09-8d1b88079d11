<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 http://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>distribution</id>
    <formats>
        <format>tar</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <files>
        <file>
            <source>${project.build.directory}/${release.name}.jar</source>
            <outputDirectory>app</outputDirectory>
            <filtered>false</filtered>
            <destName>app.jar</destName>
        </file>
    </files>
</assembly>