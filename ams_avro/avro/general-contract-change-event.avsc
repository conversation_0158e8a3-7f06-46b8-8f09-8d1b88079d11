{"type": "record", "name": "GeneralContractApplicationChangeEvent", "namespace": "cz.airbank.ams.generalcontract.application", "fields": [{"name": "applicationId", "type": "string"}, {"name": "originateSystem", "type": "string"}, {"name": "cuid<PERSON>ntitled", "type": "long"}, {"name": "generalContractType", "type": {"type": "enum", "name": "GeneralContractType", "symbols": ["RETAIL", "ENTREPRENEUR", "LEGAL_ENTITY"]}}, {"name": "applicationRequestStatus", "type": {"type": "enum", "name": "ApplicationRequestStatus", "symbols": ["APPROVED", "UNFINISHED", "CANCELED"]}}]}