{"type": "record", "name": "PensionApplicationStatusChangeEvent", "namespace": "cz.airbank.ams.pensionstatus.application", "fields": [{"name": "envelopeId", "type": ["null", "long"]}, {"name": "applicationId", "type": ["null", "string"]}, {"name": "originateSystem", "type": "string"}, {"name": "cuid", "type": "long"}, {"name": "generalContractNumber", "type": "string"}, {"name": "GeneralContractType", "type": {"type": "enum", "name": "GeneralContractType", "symbols": ["RETAIL", "ENTREPRENEUR", "LEGAL_ENTITY"]}}, {"name": "applicationRequestStatus", "type": {"type": "enum", "name": "ApplicationRequestStatus", "symbols": ["DEMO", "CANCELLED", "UNFINISHED", "APPROVED", "REJECTED", "COMPLETION"]}}]}