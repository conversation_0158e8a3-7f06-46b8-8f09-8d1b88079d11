<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:ai="http://airbank.cz/aigw/ws/chatws"
            targetNamespace="http://airbank.cz/aigw/ws/chatws"
            elementFormDefault="qualified">

    <xsd:element name="GetGreetingMessagesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="context" type="ai:ContextTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetGreetingMessagesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="messages" type="ai:MessageTO" maxOccurs="unbounded"/>
                <xsd:element name="actions" type="ai:MessageTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetConfigRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="platform" type="xsd:string" minOccurs="0" />
                <xsd:element name="platformVersion" type="xsd:string" minOccurs="0" />
                <xsd:element name="sdkVersion" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetConfigResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="configuration" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSessionRequest">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitSessionResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitIdentifiedSessionRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="channel" type="xsd:string"/>
                <xsd:element name="context" type="ai:ContextTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="InitIdentifiedSessionResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
                <xsd:element name="messages" type="ai:MessageTO" maxOccurs="unbounded"/>
                <xsd:element name="actions" type="ai:ActionTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ProcessMessageRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
                <xsd:element name="inputType" type="xsd:string"/>
                <xsd:element name="text" type="xsd:string"/>
                <xsd:element name="context" type="ai:ContextTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ProcessMessageResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="messages" type="ai:MessageTO" maxOccurs="unbounded"/>
                <xsd:element name="intents" type="ai:IntentTO" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="actions" type="ai:ActionTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="KeepSessionRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="KeepSessionResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandoverToHumanRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
                <xsd:element name="message" type="xsd:string" minOccurs="0"/>
                <xsd:element name="phoneNumber" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="HandoverToHumanResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="CloseSessionRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="CloseSessionResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="MessageTO">
        <xsd:annotation>
            <xsd:documentation>
                Basic message form

                type - message's kind which describes content - TEXT, OPTIONS, IMAGE, VIDEO
                text - message content, only TEXT type
                source - url for VIDEO/IMAGE type
                title - button's title, only OPTIONS type
                options - label's of buttons, only OPTIONS type
                style - text style
                icon - identifier of icon
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="type" type="xsd:string"/>
            <xsd:element name="text" type="xsd:string" minOccurs="0"/>
            <xsd:element name="source" type="xsd:string" minOccurs="0"/>
            <xsd:element name="title" type="xsd:string" minOccurs="0"/>
            <xsd:element name="options" type="ai:OptionTO" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="style" type="xsd:string" minOccurs="0"/>
            <xsd:element name="icon" type="xsd:string" minOccurs="0"/>
            <xsd:element name="audioText" type="ai:AudioTextTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContextTO">
        <xsd:annotation>
            <xsd:documentation>
                Addai context
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="name" type="xsd:string"/>
            <xsd:element name="value" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="OptionTO">
        <xsd:annotation>
            <xsd:documentation>
                Option button

                label - optional enhanced text of button
                text - basic label of button
                icon - identifier of icon
                link - address (maybe deeplink) to open after click
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="label" type="xsd:string" minOccurs="0"/>
            <xsd:element name="text" type="xsd:string"/>
            <xsd:element name="icon" type="xsd:string" minOccurs="0"/>
            <xsd:element name="link" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IntentTO">
        <xsd:annotation>
            <xsd:documentation>
                Analyzed intent with score

                intent - identification of intent
                score - score of intent
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="intent" type="xsd:string"/>
            <xsd:element name="score" type="xsd:float"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ActionTO">
        <xsd:annotation>
            <xsd:documentation>
                Analyzed action with parameter

                type - kind of action
                parameter - optional settings
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="type" type="xsd:string"/>
            <xsd:element name="parameter" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AudioTextTO">
        <xsd:annotation>
            <xsd:documentation>
                Formatted text for services purposes

                text - formatted text for read by services
                ssml - formatted text with marks
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="text" type="xsd:string" minOccurs="0"/>
            <xsd:element name="ssml" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

</xsd:schema>