#!/bin/bash
echo

app_name="HCI WTS RECEIVER AGENT"
running=0

#test if start-wts.pid exists and PID  is running
if test -f "./start-wts.pid";
then
	if ps -p `cat ./start-wts.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-wts.xml --spring.profiles.active=wts 2>&1 &  echo $! > start-wts.pid
fi

echo
