#!/bin/bash
echo

app_name="HCI ID LMA RECEIVER AGENT"
running=0

#test if start-idlma.pid exists and PID  is running
if test -f "./start-idlma.pid";
then
	if ps -p `cat ./start-idlma.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-idlma.xml --spring.profiles.active=idlma 2>&1 &  echo $! > start-idlma.pid
fi

echo
