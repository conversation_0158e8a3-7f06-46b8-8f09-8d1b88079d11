{"type": "record", "name": "SendMaPromoAdEvent", "namespace": "cz.airbank.sas.campaign.mapromoad.v4", "fields": [{"name": "cuid", "type": "long"}, {"name": "relatedCuid", "type": ["null", "long"], "default": null}, {"name": "creator", "type": "string"}, {"name": "externalId", "type": "string"}, {"name": "priority", "type": "int"}, {"name": "displayGroup", "type": "string"}, {"name": "communicationKind", "type": "string"}, {"name": "products", "type": ["null", {"type": "array", "items": "string"}], "default": null}, {"name": "taskVersionId", "type": "string"}, {"name": "campaignCode", "type": "string"}, {"name": "campaignName", "type": "string"}, {"name": "communicationCode", "type": "string"}, {"name": "communicationName", "type": "string"}, {"name": "businessSummaryCauseCode", "type": ["null", "string"], "default": null}, {"name": "dismissAfterGoAheadClick", "type": ["null", "boolean"], "default": null}, {"name": "useDetail", "type": "boolean"}, {"name": "place", "type": "string"}, {"name": "subject", "type": "string"}, {"name": "body", "type": "string"}, {"name": "image", "type": "string"}, {"name": "detailTitle", "type": ["null", "string"], "default": null}, {"name": "detailBody", "type": ["null", "string"], "default": null}, {"name": "detailImage", "type": ["null", "string"], "default": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "primaryClickMeaning", "type": "string"}, {"name": "primaryVisualization", "type": "string"}, {"name": "primaryUrl", "type": ["null", "string"], "default": null}, {"name": "primaryDeepLink", "type": ["null", "string"], "default": null}, {"name": "secondaryLabel", "type": ["null", "string"], "default": null}, {"name": "secondaryClickMeaning", "type": ["null", "string"], "default": null}, {"name": "secondaryVisualization", "type": ["null", "string"], "default": null}, {"name": "secondaryUrl", "type": ["null", "string"], "default": null}, {"name": "detailVisualization", "type": ["null", "string"], "default": null}, {"name": "detail<PERSON><PERSON><PERSON>", "type": ["null", "string"], "default": null}, {"name": "validFromAbs", "type": ["null", {"type": "string", "logicalType": "iso-zoned-date-time"}], "default": null}, {"name": "validFromRel", "type": ["null", "int"], "default": null}, {"name": "validToAbs", "type": ["null", {"type": "string", "logicalType": "iso-local-date"}], "default": null}, {"name": "validToRel", "type": ["null", "int"], "default": null}, {"name": "showGuaranteedDays", "type": ["null", "int"], "default": null}, {"name": "showMaxCountAfterGuaranteedDays", "type": ["null", "int"], "default": null}, {"name": "created", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}, {"name": "urlParameter1", "type": ["null", "string"], "default": null}, {"name": "secondaryDeepLink", "type": ["null", "string"], "default": null}, {"name": "businessService", "type": ["null", "string"], "default": null}, {"name": "label", "type": ["null", "string"], "default": null}, {"name": "colour", "type": ["null", "string"], "default": null}]}