<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/cml/ws/batchOperations"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://airbank.cz/cml/ws/batchOperations">
    <wsdl:types>
        <xsd:schema
                attributeFormDefault="unqualified" elementFormDefault="qualified"
                targetNamespace="http://airbank.cz/cml/ws/batchOperations">
            <xsd:include schemaLocation="CmlBatchOperationsWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="SetupCampaignEmailContactsRequest">
        <wsdl:part element="tns:SetupCampaignEmailContactsRequest" name="SetupCampaignEmailContactsRequest"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignEmailContactsResponse">
        <wsdl:part element="tns:SetupCampaignEmailContactsResponse" name="SetupCampaignEmailContactsResponse"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignEmailContactsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="SetupCampaignEmailContactsFault"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignSmsContactsRequest">
        <wsdl:part element="tns:SetupCampaignSmsContactsRequest" name="SetupCampaignSmsContactsRequest"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignSmsContactsResponse">
        <wsdl:part element="tns:SetupCampaignSmsContactsResponse" name="SetupCampaignSmsContactsResponse"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignSmsContactsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="SetupCampaignSmsContactsFault"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignPlannedCallsRequest">
        <wsdl:part element="tns:SetupCampaignPlannedCallsRequest" name="SetupCampaignPlannedCallsRequest"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignPlannedCallsResponse">
        <wsdl:part element="tns:SetupCampaignPlannedCallsResponse" name="SetupCampaignPlannedCallsResponse"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignPlannedCallsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="SetupCampaignPlannedCallsFault"/>
    </wsdl:message>
    <wsdl:message name="RevokeCampaignPlannedCallsRequest">
        <wsdl:part element="tns:RevokeCampaignPlannedCallsRequest" name="RevokeCampaignPlannedCallsRequest"/>
    </wsdl:message>
    <wsdl:message name="RevokeCampaignPlannedCallsResponse">
        <wsdl:part element="tns:RevokeCampaignPlannedCallsResponse" name="RevokeCampaignPlannedCallsResponse"/>
    </wsdl:message>
    <wsdl:message name="RevokeCampaignPlannedCallsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="RevokeCampaignPlannedCallsFault"/>
    </wsdl:message>
    <wsdl:message name="SetupSemaphoreOffersRequest">
        <wsdl:part element="tns:SetupSemaphoreOffersRequest" name="SetupSemaphoreOffersRequest"/>
    </wsdl:message>
    <wsdl:message name="SetupSemaphoreOffersResponse">
        <wsdl:part element="tns:SetupSemaphoreOffersResponse" name="SetupSemaphoreOffersResponse"/>
    </wsdl:message>
    <wsdl:message name="SetupSemaphoreOffersFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="SetupSemaphoreOffersFault"/>
    </wsdl:message>
    <wsdl:message name="RevokeSemaphoreOffersRequest">
        <wsdl:part element="tns:RevokeSemaphoreOffersRequest" name="RevokeSemaphoreOffersRequest"/>
    </wsdl:message>
    <wsdl:message name="RevokeSemaphoreOffersResponse">
        <wsdl:part element="tns:RevokeSemaphoreOffersResponse" name="RevokeSemaphoreOffersResponse"/>
    </wsdl:message>
    <wsdl:message name="RevokeSemaphoreOffersFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="RevokeSemaphoreOffersFault"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignIbContactsRequest">
        <wsdl:part element="tns:SetupCampaignIbContactsRequest" name="SetupCampaignIbContactsRequest"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignIbContactsResponse">
        <wsdl:part element="tns:SetupCampaignIbContactsResponse" name="SetupCampaignIbContactsResponse"/>
    </wsdl:message>
    <wsdl:message name="SetupCampaignIbContactsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="SetupCampaignIbContactsFault"/>
    </wsdl:message>
    <wsdl:message name="RevokeCampaignIbContactsRequest">
        <wsdl:part element="tns:RevokeCampaignIbContactsRequest" name="RevokeCampaignIbContactsRequest"/>
    </wsdl:message>
    <wsdl:message name="RevokeCampaignIbContactsResponse">
        <wsdl:part element="tns:RevokeCampaignIbContactsResponse" name="RevokeCampaignIbContactsResponse"/>
    </wsdl:message>
    <wsdl:message name="RevokeCampaignIbContactsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="RevokeCampaignIbContactsFault"/>
    </wsdl:message>
    <wsdl:message name="SetupPromoAdsRequest">
        <wsdl:part element="tns:SetupPromoAdsRequest" name="SetupPromoAdsRequest"/>
    </wsdl:message>
    <wsdl:message name="SetupPromoAdsResponse">
        <wsdl:part element="tns:SetupPromoAdsResponse" name="SetupPromoAdsResponse"/>
    </wsdl:message>
    <wsdl:message name="SetupPromoAdsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="SetupPromoAdsFault"/>
    </wsdl:message>
    <wsdl:message name="StopPromoAdsRequest">
        <wsdl:part element="tns:StopPromoAdsRequest" name="StopPromoAdsRequest"/>
    </wsdl:message>
    <wsdl:message name="StopPromoAdsResponse">
        <wsdl:part element="tns:StopPromoAdsResponse" name="StopPromoAdsResponse"/>
    </wsdl:message>
    <wsdl:message name="StopPromoAdsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="StopPromoAdsFault"/>
    </wsdl:message>
    <wsdl:portType name="CmlBatchOperationsWS">
        <wsdl:operation name="SetupCampaignEmailContacts">
            <wsdl:documentation>
                Register campaign emails from SAS

                Error codes:
                - CommunicationBatch.NotValidProducts - given products not valid
                - Contact.ExpirationInPast - expiration is in the past
            </wsdl:documentation>
            <wsdl:input message="tns:SetupCampaignEmailContactsRequest" name="SetupCampaignEmailContactsRequest"/>
            <wsdl:output message="tns:SetupCampaignEmailContactsResponse" name="SetupCampaignEmailContactsResponse"/>
            <wsdl:fault message="tns:SetupCampaignEmailContactsFaultMessage" name="SetupCampaignEmailContactsFault"/>
        </wsdl:operation>
        <wsdl:operation name="SetupCampaignSmsContacts">
            <wsdl:documentation>
                Setup campaign SMSs from SAS

                Error codes:
                - Contact.ExpirationInPast - expiration is in the past
            </wsdl:documentation>
            <wsdl:input message="tns:SetupCampaignSmsContactsRequest" name="SetupCampaignSmsContactsRequest"/>
            <wsdl:output message="tns:SetupCampaignSmsContactsResponse" name="SetupCampaignSmsContactsResponse"/>
            <wsdl:fault message="tns:SetupCampaignSmsContactsFaultMessage" name="SetupCampaignSmsContactsFault"/>
        </wsdl:operation>
        <wsdl:operation name="SetupCampaignPlannedCalls">
            <wsdl:documentation>
                Setup campaign planned calls from SAS

                Response:
                - communicationBatchId
                - plannedCallStatuses
                - - plannedCallId
                - - externalId
                - - status

                Response fault codes:
                - CodeList.DepartmentQueueCode.DoesNotExist
                - CommunicationBatch.NotValidProducts - given products not valid
                - PlannedCall.ExpirationInPast - expiration is in the past
                - PlannedCall.ScheduleInPast - schedule is in the past
            </wsdl:documentation>
            <wsdl:input message="tns:SetupCampaignPlannedCallsRequest" name="SetupCampaignPlannedCallsRequest"/>
            <wsdl:output message="tns:SetupCampaignPlannedCallsResponse" name="SetupCampaignPlannedCallsResponse"/>
            <wsdl:fault message="tns:SetupCampaignPlannedCallsFaultMessage" name="SetupCampaignPlannedCallsFault"/>
        </wsdl:operation>
        <wsdl:operation name="RevokeCampaignPlannedCalls">
            <wsdl:documentation>
                Revoke campaign planned calls from SAS

                Following attribute is always mandatory:
                - plannedContactId
                Response:

                Response fault codes:
                - PlannedCall.DoesNotExist - planned contact was not found
            </wsdl:documentation>
            <wsdl:input message="tns:RevokeCampaignPlannedCallsRequest" name="RevokeCampaignPlannedCallsRequest"/>
            <wsdl:output message="tns:RevokeCampaignPlannedCallsResponse" name="RevokeCampaignPlannedCallsResponse"/>
            <wsdl:fault message="tns:RevokeCampaignPlannedCallsFaultMessage" name="RevokeCampaignPlannedCallsFault"/>
        </wsdl:operation>
        <wsdl:operation name="SetupSemaphoreOffers">
            <wsdl:documentation>
                Setup semaphore offers from SAS

                Error codes:
                - CommunicationBatch.NotValidProducts - given products not valid
                - SemaphoreOffer.NotValidCommunicationKind - not alloved communication kind type
                - SemaphoreOffer.ExpirationInPast - given expiration is scheduled to past
                - SemaphoreOffer.IncorrectCreator
            </wsdl:documentation>
            <wsdl:input message="tns:SetupSemaphoreOffersRequest" name="SetupSemaphoreOffersRequest"/>
            <wsdl:output message="tns:SetupSemaphoreOffersResponse" name="SetupSemaphoreOffersResponse"/>
            <wsdl:fault message="tns:SetupSemaphoreOffersFaultMessage" name="SetupSemaphoreOffersFault"/>
        </wsdl:operation>
        <wsdl:operation name="RevokeSemaphoreOffers">
            <wsdl:documentation>
                Revoke semaphore offers

                Following attributes are always mandatory:
                - semaphoreOfferId

                Error codes:
                - SemaphoreOffer.DoesNotExist - specified semaphore offer not found
            </wsdl:documentation>
            <wsdl:input message="tns:RevokeSemaphoreOffersRequest" name="RevokeSemaphoreOffersRequest"/>
            <wsdl:output message="tns:RevokeSemaphoreOffersResponse" name="RevokeSemaphoreOffersResponse"/>
            <wsdl:fault message="tns:RevokeSemaphoreOffersFaultMessage" name="RevokeSemaphoreOffersFault"/>
        </wsdl:operation>
        <wsdl:operation name="SetupCampaignIbContacts">
            <wsdl:documentation>
                Setup campaign IB messages from SAS

                Response:
                - communicationBatchId
                - ibContact - list of the IB contacts

                Response fault codes:
                - CommunicationBatch.NotValidProducts - given products not valid
                - Contact.UrlNotValid - URL link code is not valid
                - Contact.UrlTextMissing - When URL code is defined URL text is mandatory
                - Contact.UrlAndSurveyTogether - For IB contact is possible to set separate only URL or feedback
            </wsdl:documentation>
            <wsdl:input message="tns:SetupCampaignIbContactsRequest" name="SetupCampaignIbContactsRequest"/>
            <wsdl:output message="tns:SetupCampaignIbContactsResponse" name="SetupCampaignIbContactsResponse"/>
            <wsdl:fault message="tns:SetupCampaignIbContactsFaultMessage" name="SetupCampaignIbContactsFault"/>
        </wsdl:operation>
        <wsdl:operation name="RevokeCampaignIbContacts">
            <wsdl:documentation>
                Service to revoke IB messages from SAS

                Response:
                - ibContact - list of the IB contacts

                Response fault codes:
                - Contact.DoesNotExist - ibContact not found
            </wsdl:documentation>
            <wsdl:input message="tns:RevokeCampaignIbContactsRequest" name="RevokeCampaignIbContactsRequest"/>
            <wsdl:output message="tns:RevokeCampaignIbContactsResponse" name="RevokeCampaignIbContactsResponse"/>
            <wsdl:fault message="tns:RevokeCampaignIbContactsFaultMessage" name="RevokeCampaignIbContactsFault"/>
        </wsdl:operation>
        <wsdl:operation name="SetupPromoAds">
            <wsdl:documentation>
                Setup promo ads from SAS

                Response:
                - communicationBatchId
                - promoAd

                Response fault codes:
                - CommunicationBatch.NotValidProducts - given products not valid
                - PromoAds.NotValidShowInterval - show interval is in the past
            </wsdl:documentation>
            <wsdl:input message="tns:SetupPromoAdsRequest" name="SetupPromoAdsRequest"/>
            <wsdl:output message="tns:SetupPromoAdsResponse" name="SetupPromoAdsResponse"/>
            <wsdl:fault message="tns:SetupPromoAdsFaultMessage" name="SetupPromoAdsFault"/>
        </wsdl:operation>
        <wsdl:operation name="StopPromoAds">
            <wsdl:documentation>
                Stop promo ads from SAS

                Following attributes are always mandatory:
                - promoAdId

                Response fault codes:
                - PromoAd.DoesNotExist
            </wsdl:documentation>
            <wsdl:input message="tns:StopPromoAdsRequest" name="StopPromoAdsRequest"/>
            <wsdl:output message="tns:StopPromoAdsResponse" name="StopPromoAdsResponse"/>
            <wsdl:fault message="tns:StopPromoAdsFaultMessage" name="StopPromoAdsFault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CmlBatchOperationsWSSoap11" type="tns:CmlBatchOperationsWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="SetupCampaignEmailContacts">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetupCampaignEmailContactsFault">
                <soap:fault name="SetupCampaignEmailContactsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="SetupCampaignSmsContacts">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetupCampaignSmsContactsFault">
                <soap:fault name="SetupCampaignSmsContactsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="SetupCampaignPlannedCalls">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetupCampaignPlannedCallsFault">
                <soap:fault name="SetupCampaignPlannedCallsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="RevokeCampaignPlannedCalls">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RevokeCampaignPlannedCallsFault">
                <soap:fault name="RevokeCampaignPlannedCallsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="SetupSemaphoreOffers">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetupSemaphoreOffersFault">
                <soap:fault name="SetupSemaphoreOffersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="RevokeSemaphoreOffers">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RevokeSemaphoreOffersFault">
                <soap:fault name="RevokeSemaphoreOffersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="SetupCampaignIbContacts">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetupCampaignIbContactsFault">
                <soap:fault name="SetupCampaignIbContactsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="RevokeCampaignIbContacts">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RevokeCampaignIbContactsFault">
                <soap:fault name="RevokeCampaignIbContactsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="SetupPromoAds">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetupPromoAdsFault">
                <soap:fault name="SetupPromoAdsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="StopPromoAds">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StopPromoAdsFault">
                <soap:fault name="StopPromoAdsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CmlBatchOperationsWSService">
        <wsdl:port binding="tns:CmlBatchOperationsWSSoap11" name="CmlBatchOperationsWSSoap11">
            <soap:address location="http://localhost:8087/ws/CmlBatchOperationsWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>