<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  xmlns:tns="http://airbank.cz/cml/ws/chatws"
                  targetNamespace="http://airbank.cz/cml/ws/chatws">
    <wsdl:types>
        <xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://airbank.cz/cml/ws/chatws">
            <xsd:include schemaLocation="CmlChatWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="StoreInteractionRequest">
        <wsdl:part element="tns:StoreInteractionRequest" name="StoreInteractionRequest"/>
    </wsdl:message>
    <wsdl:message name="StoreInteractionResponse">
        <wsdl:part element="tns:StoreInteractionResponse" name="StoreInteractionResponse"/>
    </wsdl:message>
    <wsdl:message name="StoreInteractionFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="StoreInteractionFault"/>
    </wsdl:message>

    <wsdl:message name="StoreInteractionFeedbackRequest">
        <wsdl:part element="tns:StoreInteractionFeedbackRequest" name="StoreInteractionFeedbackRequest"/>
    </wsdl:message>
    <wsdl:message name="StoreInteractionFeedbackResponse">
        <wsdl:part element="tns:StoreInteractionFeedbackResponse" name="StoreInteractionFeedbackResponse"/>
    </wsdl:message>
    <wsdl:message name="StoreInteractionFeedbackFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="StoreInteractionFeedbackFault"/>
    </wsdl:message>


    <wsdl:portType name="CmlChatWS">
        <wsdl:operation name="StoreInteraction">
            <wsdl:documentation>
                Operation processes questions from web
            </wsdl:documentation>
            <wsdl:input message="tns:StoreInteractionRequest" name="StoreInteractionRequest"/>
            <wsdl:output message="tns:StoreInteractionResponse" name="StoreInteractionResponse"/>
            <wsdl:fault message="tns:StoreInteractionFaultMessage" name="StoreInteractionFaultMessage"/>
        </wsdl:operation>
        <wsdl:operation name="StoreInteractionFeedback">
            <wsdl:documentation>
                Operation processes questions from web
            </wsdl:documentation>
            <wsdl:input message="tns:StoreInteractionFeedbackRequest" name="StoreInteractionFeedbackRequest"/>
            <wsdl:output message="tns:StoreInteractionFeedbackResponse" name="StoreInteractionFeedbackResponse"/>
            <wsdl:fault message="tns:StoreInteractionFeedbackFaultMessage" name="StoreInteractionFeedbackFaultMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CmlChatWSSoap11" type="tns:CmlChatWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="StoreInteraction">
            <soap:operation soapAction=""/>
            <wsdl:input name="StoreInteractionRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="StoreInteractionResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StoreInteractionFaultMessage">
                <soap:fault name="StoreInteractionFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="StoreInteractionFeedback">
            <soap:operation soapAction=""/>
            <wsdl:input name="StoreInteractionFeedbackRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="StoreInteractionFeedbackResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StoreInteractionFeedbackFaultMessage">
                <soap:fault name="StoreInteractionFeedbackFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CmlChatWSService">
        <wsdl:port binding="tns:CmlChatWSSoap11" name="CmlChatWSSoap11">
            <soap:address location="http://localhost:8087/ws/CmlChatWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>