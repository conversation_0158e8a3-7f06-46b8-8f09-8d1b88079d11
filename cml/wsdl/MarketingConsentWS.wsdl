<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  xmlns:tns="http://homecredit.net/cml/ws/marketingconsent"
                  targetNamespace="http://homecredit.net/cml/ws/marketingconsent">
    <wsdl:types>
        <xsd:schema
                attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://homecredit.net/cml/ws/marketingconsent">
            <xsd:include schemaLocation="MarketingConsentWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="GetCrmConsentRequest">
        <wsdl:part element="tns:GetCrmConsentRequest" name="GetCrmConsentRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="SetCrmConsentMarkRequest">
        <wsdl:part element="tns:SetCrmConsentMarkRequest" name="SetCrmConsentMarkRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="SetCrmConsentMarkResponse">
        <wsdl:part element="tns:SetCrmConsentMarkResponse" name="SetCrmConsentMarkResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetCrmConsentResponse">
        <wsdl:part element="tns:GetCrmConsentResponse" name="GetCrmConsentResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetCrmConsentsWithdrawalHistoryRequest">
        <wsdl:part element="tns:GetCrmConsentsWithdrawalHistoryRequest" name="GetCrmConsentsWithdrawalHistoryRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetCrmConsentsWithdrawalHistoryResponse">
        <wsdl:part element="tns:GetCrmConsentsWithdrawalHistoryResponse" name="GetCrmConsentsWithdrawalHistoryResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ValidatePlannedCallContactConsentRequest">
        <wsdl:part element="tns:ValidatePlannedCallContactConsentRequest" name="ValidatePlannedCallContactConsentRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ValidatePlannedCallContactConsentResponse">
        <wsdl:part element="tns:ValidatePlannedCallContactConsentResponse" name="ValidatePlannedCallContactConsentResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ValidateCRMConsentsRequest">
        <wsdl:part element="tns:ValidateCRMConsentsRequest" name="ValidateCRMConsentsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ValidateCRMConsentsResponse">
        <wsdl:part element="tns:ValidateCRMConsentsResponse" name="ValidateCRMConsentsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ValidateCRMConsentsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="ValidateCRMConsentsFaultMessage">
        </wsdl:part>
    </wsdl:message>
    <wsdl:portType name="MarketingConsentWS">
        <wsdl:operation name="GetCrmConsent">
            <wsdl:input message="tns:GetCrmConsentRequest" name="GetCrmConsentRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetCrmConsentResponse" name="GetCrmConsentResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetCrmConsentMark">
            <wsdl:input message="tns:SetCrmConsentMarkRequest" name="SetCrmConsentMarkRequest">
            </wsdl:input>
            <wsdl:output message="tns:SetCrmConsentMarkResponse" name="SetCrmConsentMarkResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCrmConsentsWithdrawalHistory">
            <wsdl:input message="tns:GetCrmConsentsWithdrawalHistoryRequest" name="GetCrmConsentsWithdrawalHistoryRequest">
            </wsdl:input>
            <wsdl:output message="tns:GetCrmConsentsWithdrawalHistoryResponse" name="GetCrmConsentsWithdrawalHistoryResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ValidatePlannedCallContactConsent">
            <wsdl:input message="tns:ValidatePlannedCallContactConsentRequest" name="ValidatePlannedCallContactConsentRequest">
            </wsdl:input>
            <wsdl:output message="tns:ValidatePlannedCallContactConsentResponse" name="ValidatePlannedCallContactConsentResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ValidateCRMConsents">
            <wsdl:input message="tns:ValidateCRMConsentsRequest" name="ValidateCRMConsentsRequest">
            </wsdl:input>
            <wsdl:output message="tns:ValidateCRMConsentsResponse" name="ValidateCRMConsentsResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ValidateCRMConsentsFaultMessage" name="ValidateCRMConsentsFaultMessage">
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="MarketingConsentWSSoap11" type="tns:MarketingConsentWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetCrmConsent">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetCrmConsentRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetCrmConsentResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetCrmConsentMark">
            <soap:operation soapAction=""/>
            <wsdl:input name="SetCrmConsentMarkRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="SetCrmConsentMarkResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCrmConsentsWithdrawalHistory">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetCrmConsentsWithdrawalHistoryRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetCrmConsentsWithdrawalHistoryResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ValidatePlannedCallContactConsent">
            <soap:operation soapAction=""/>
            <wsdl:input name="ValidatePlannedCallContactConsentRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ValidatePlannedCallContactConsentResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ValidateCRMConsents">
            <soap:operation soapAction=""/>
            <wsdl:input name="ValidateCRMConsentsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ValidateCRMConsentsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ValidateCRMConsentsFaultMessage">
                <soap:fault name="ValidateCRMConsentsFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="MarketingConsentWSService">
        <wsdl:port binding="tns:MarketingConsentWSSoap11" name="MarketingConsentWSSoap11">
            <soap:address location="https://TO-BE-CHANGED/ws/MarketingConsentWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>