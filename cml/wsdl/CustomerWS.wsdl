<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://homecredit.net/cml/ws/customer" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="CustomerWS"
                  targetNamespace="http://homecredit.net/cml/ws/customer">
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                    targetNamespace="http://homecredit.net/cml/ws/customer">
            <xsd:include schemaLocation="CustomerWS.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="AnonymizePersonRequest">
        <wsdl:part element="tns:AnonymizePersonRequest" name="parameters"/>
    </wsdl:message>
    <wsdl:message name="AnonymizePersonResponse">
        <wsdl:part element="tns:AnonymizePersonResponse" name="parameters"/>
    </wsdl:message>
    <wsdl:message name="GetCustomerCommunicationCharacteristicRequest">
        <wsdl:part element="tns:GetCustomerCommunicationCharacteristicRequest" name="GetCustomerCommunicationCharacteristicRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCustomerCommunicationCharacteristicResponse">
        <wsdl:part element="tns:GetCustomerCommunicationCharacteristicResponse" name="GetCustomerCommunicationCharacteristicResponse"/>
    </wsdl:message>
    <wsdl:message name="SetCustomerCommunicationCharacteristicRequest">
        <wsdl:part element="tns:SetCustomerCommunicationCharacteristicRequest" name="SetCustomerCommunicationCharacteristicRequest"/>
    </wsdl:message>
    <wsdl:message name="SetCustomerCommunicationCharacteristicResponse">
        <wsdl:part element="tns:SetCustomerCommunicationCharacteristicResponse" name="SetCustomerCommunicationCharacteristicResponse"/>
    </wsdl:message>

    <wsdl:portType name="CustomerWS">
        <wsdl:operation name="AnonymizePerson">
            <wsdl:input message="tns:AnonymizePersonRequest"/>
            <wsdl:output message="tns:AnonymizePersonResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetCustomerCommunicationCharacteristic">
            <wsdl:input message="tns:GetCustomerCommunicationCharacteristicRequest" name="GetCustomerCommunicationCharacteristicRequest"/>
            <wsdl:output message="tns:GetCustomerCommunicationCharacteristicResponse" name="GetCustomerCommunicationCharacteristicResponse"/>
        </wsdl:operation>
        <wsdl:operation name="SetCustomerCommunicationCharacteristic">
            <wsdl:input message="tns:SetCustomerCommunicationCharacteristicRequest" name="SetCustomerCommunicationCharacteristicRequest"/>
            <wsdl:output message="tns:SetCustomerCommunicationCharacteristicResponse" name="SetCustomerCommunicationCharacteristicResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CustomerWSSOAP" type="tns:CustomerWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="AnonymizePerson">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetCustomerCommunicationCharacteristic">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="SetCustomerCommunicationCharacteristic">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="CustomerWS">
        <wsdl:port binding="tns:CustomerWSSOAP" name="CustomerWSSOAP">
            <soap:address location="https://TO-BE-CHANGED/ws/CustomerWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
