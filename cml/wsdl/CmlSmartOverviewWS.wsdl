<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  xmlns:tns="http://airbank.cz/cml/ws/smartoverview"
                  targetNamespace="http://airbank.cz/cml/ws/smartoverview">
    <wsdl:types>
        <xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://airbank.cz/cml/ws/smartoverview">
            <xsd:include schemaLocation="CmlSmartOverviewWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="GetActivePartBusinessServicesRequest">
        <wsdl:part element="tns:GetActivePartBusinessServicesRequest" name="GetActivePartBusinessServicesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetActivePartBusinessServicesResponse">
        <wsdl:part element="tns:GetActivePartBusinessServicesResponse" name="GetActivePartBusinessServicesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetActivePartBusinessServicesFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="GetActivePartBusinessServicesFault"/>
    </wsdl:message>

    <wsdl:message name="GetOfferingPartBusinessServicesRequest">
        <wsdl:part element="tns:GetOfferingPartBusinessServicesRequest" name="GetOfferingPartBusinessServicesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetOfferingPartBusinessServicesResponse">
        <wsdl:part element="tns:GetOfferingPartBusinessServicesResponse" name="GetOfferingPartBusinessServicesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetOfferingPartBusinessServicesFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="GetOfferingPartBusinessServicesFault"/>
    </wsdl:message>

    <wsdl:message name="ProcessCustomerBusinessServiceAppliedRequest">
        <wsdl:part element="tns:ProcessCustomerBusinessServiceAppliedRequest" name="ProcessCustomerBusinessServiceAppliedRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessCustomerBusinessServiceAppliedResponse">
        <wsdl:part element="tns:ProcessCustomerBusinessServiceAppliedResponse" name="ProcessCustomerBusinessServiceAppliedResponse"/>
    </wsdl:message>
    <wsdl:message name="ProcessCustomerBusinessServiceAppliedFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="ProcessCustomerBusinessServiceAppliedFaultMessage"/>
    </wsdl:message>

    <wsdl:portType name="CmlSmartOverviewWS">
        <wsdl:operation name="GetActivePartBusinessServices">
            <wsdl:documentation>
                Operation for obtaining the business services of the active part of the Smart Overview
            </wsdl:documentation>
            <wsdl:input message="tns:GetActivePartBusinessServicesRequest" name="GetActivePartBusinessServicesRequest"/>
            <wsdl:output message="tns:GetActivePartBusinessServicesResponse" name="GetActivePartBusinessServicesResponse"/>
            <wsdl:fault message="tns:GetActivePartBusinessServicesFaultMessage" name="GetActivePartBusinessServicesFaultMessage"/>
        </wsdl:operation>
        <wsdl:operation name="GetOfferingPartBusinessServices">
            <wsdl:documentation>
                Operation for obtaining the business services of the active part of the Smart Overview
            </wsdl:documentation>
            <wsdl:input message="tns:GetOfferingPartBusinessServicesRequest" name="GetOfferingPartBusinessServicesRequest"/>
            <wsdl:output message="tns:GetOfferingPartBusinessServicesResponse" name="GetOfferingPartBusinessServicesResponse"/>
            <wsdl:fault message="tns:GetOfferingPartBusinessServicesFaultMessage" name="GetOfferingPartBusinessServicesFaultMessage"/>
        </wsdl:operation>
        <wsdl:operation name="ProcessCustomerBusinessServiceApplied">
            <wsdl:documentation>
                See documentation https://wiki.airbank.cz/x/iCizF
            </wsdl:documentation>
            <wsdl:input message="tns:ProcessCustomerBusinessServiceAppliedRequest" name="ProcessCustomerBusinessServiceAppliedRequest"/>
            <wsdl:output message="tns:ProcessCustomerBusinessServiceAppliedResponse" name="ProcessCustomerBusinessServiceAppliedResponse"/>
            <wsdl:fault message="tns:ProcessCustomerBusinessServiceAppliedFaultMessage" name="ProcessCustomerBusinessServiceAppliedFaultMessage"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CmlSmartOverviewWSSoap11" type="tns:CmlSmartOverviewWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetActivePartBusinessServices">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetActivePartBusinessServicesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetActivePartBusinessServicesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetActivePartBusinessServicesFaultMessage">
                <soap:fault name="GetActivePartBusinessServicesFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="GetOfferingPartBusinessServices">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetOfferingPartBusinessServicesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetOfferingPartBusinessServicesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetOfferingPartBusinessServicesFaultMessage">
                <soap:fault name="GetOfferingPartBusinessServicesFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="ProcessCustomerBusinessServiceApplied">
            <soap:operation soapAction=""/>
            <wsdl:input name="ProcessCustomerBusinessServiceAppliedRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ProcessCustomerBusinessServiceAppliedResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ProcessCustomerBusinessServiceAppliedFaultMessage">
                <soap:fault name="ProcessCustomerBusinessServiceAppliedFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CmlSmartOverviewWService">
        <wsdl:port binding="tns:CmlSmartOverviewWSSoap11" name="CmlSmartOverviewWSSoap11">
            <soap:address location="http://localhost:8000/ws/CmlSmartOverviewWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>