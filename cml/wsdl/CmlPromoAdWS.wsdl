<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/cml/ws/promoAd"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  targetNamespace="http://airbank.cz/cml/ws/promoAd">
    <wsdl:types>
        <xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://airbank.cz/cml/ws/promoAd">
            <xsd:include schemaLocation="CmlPromoAdWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="GetPromoAdsRequest">
        <wsdl:part element="tns:GetPromoAdsRequest" name="GetPromoAdsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPromoAdsResponse">
        <wsdl:part element="tns:GetPromoAdsResponse" name="GetPromoAdsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPromoAdsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="GetPromoAdsFault"/>
    </wsdl:message>

    <wsdl:message name="GetPromoAdDetailRequest">
        <wsdl:part element="tns:GetPromoAdDetailRequest" name="GetPromoAdDetailRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPromoAdDetailResponse">
        <wsdl:part element="tns:GetPromoAdDetailResponse" name="GetPromoAdDetailResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPromoAdDetailFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="GetPromoAdDetailFault"/>
    </wsdl:message>

    <wsdl:message name="RegisterPromoAdClickRequest">
        <wsdl:part element="tns:RegisterPromoAdClickRequest" name="RegisterPromoAdClickRequest"/>
    </wsdl:message>
    <wsdl:message name="RegisterPromoAdClickResponse">
        <wsdl:part element="tns:RegisterPromoAdClickResponse" name="RegisterPromoAdClickResponse"/>
    </wsdl:message>
    <wsdl:message name="RegisterPromoAdClickFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="RegisterPromoAdClickFault"/>
    </wsdl:message>

    <wsdl:message name="GetMaPromoAdsRequest">
        <wsdl:part element="tns:GetMaPromoAdsRequest" name="GetMaPromoAdsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetMaPromoAdsResponse">
        <wsdl:part element="tns:GetMaPromoAdsResponse" name="GetMaPromoAdsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetMaPromoAdsFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="GetMaPromoAdsFault"/>
    </wsdl:message>

    <wsdl:portType name="CmlPromoAdWS">
        <wsdl:operation name="GetPromoAds">
            <wsdl:documentation>
                Get promo ads

                Following attributes are always mandatory:
                - cuid
                - presentation
            </wsdl:documentation>
            <wsdl:input message="tns:GetPromoAdsRequest" name="GetPromoAdsRequest"/>
            <wsdl:output message="tns:GetPromoAdsResponse" name="GetPromoAdsResponse"/>
            <wsdl:fault message="tns:GetPromoAdsFaultMessage" name="GetPromoAdsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetPromoAdDetail">
            <wsdl:documentation>
                Get promo ad detail

                Following attributes are always mandatory:
                - promoAdId
                - presentation

                Error codes:
                - PromoAd.DoesNotExist
            </wsdl:documentation>
            <wsdl:input message="tns:GetPromoAdDetailRequest" name="GetPromoAdDetailRequest"/>
            <wsdl:output message="tns:GetPromoAdDetailResponse" name="GetPromoAdDetailResponse"/>
            <wsdl:fault message="tns:GetPromoAdDetailFaultMessage" name="GetPromoAdDetailFault"/>
        </wsdl:operation>

        <wsdl:operation name="RegisterPromoAdClick">
            <wsdl:documentation>
                Registers click on link in promo ad (IB internal or external)

                Following attributes are always mandatory:
                - promoAdId
                - clickMeaning

                Error codes:
                - PromoAd.DoesNotExist
            </wsdl:documentation>
            <wsdl:input message="tns:RegisterPromoAdClickRequest" name="RegisterPromoAdClickRequest"/>
            <wsdl:output message="tns:RegisterPromoAdClickResponse" name="RegisterPromoAdClickResponse"/>
            <wsdl:fault message="tns:RegisterPromoAdClickFaultMessage" name="RegisterPromoAdClickFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetMaPromoAds">
            <wsdl:documentation>
                Get promo ads for mobile application (My Air)

                Following attributes are always mandatory:
                - cuid
                - place
            </wsdl:documentation>
            <wsdl:input message="tns:GetMaPromoAdsRequest" name="GetMaPromoAdsRequest"/>
            <wsdl:output message="tns:GetMaPromoAdsResponse" name="GetMaPromoAdsResponse"/>
            <wsdl:fault message="tns:GetMaPromoAdsFaultMessage" name="GetMaPromoAdsFault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CmlPromoAdWSSoap11" type="tns:CmlPromoAdWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="GetPromoAds">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPromoAdsFault">
                <soap:fault name="GetPromoAdsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetPromoAdDetail">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPromoAdDetailFault">
                <soap:fault name="GetPromoAdDetailFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="RegisterPromoAdClick">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RegisterPromoAdClickFault">
                <soap:fault name="RegisterPromoAdClickFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetMaPromoAds">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetMaPromoAdsFault">
                <soap:fault name="GetMaPromoAdsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CmlPromoAdWSService">
        <wsdl:port binding="tns:CmlPromoAdWSSoap11" name="CmlPromoAdWSSoap11">
            <soap:address location="http://localhost:8087/ws/CmlPromoAdWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>