<?xml version="1.0" encoding="utf-8" ?>
<xsd:schema xmlns:con="http://airbank.cz/cml/ws/contact"
            xmlns:comm="http://homecredit.net/cml/ws/common/dto"
            xmlns:res="http://homecredit.net/cml/ws/result/dto"
            xmlns:creator="http://airbank.cz/cml/ws/creator"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://airbank.cz/cml/ws/contact">
    <xsd:import namespace="http://homecredit.net/cml/ws/common/dto" schemaLocation="../xsd/Common.xsd"/>
    <xsd:import namespace="http://homecredit.net/cml/ws/result/dto" schemaLocation="../xsd/Result.xsd"/>
    <xsd:import namespace="http://airbank.cz/cml/ws/creator" schemaLocation="../xsd/Creator.xsd"/>
    <xsd:include schemaLocation="../xsd/ContactShared.xsd"/>
    <xsd:include schemaLocation="../xsd/OriginContext.xsd"/>

    <xsd:complexType name="BasePersonTO">
        <xsd:annotation>
            <xsd:documentation>
                Identifier of bank or non-bank client

                cuid - id of bank client
                phoneNumber - non-bank client identified by phone number
            </xsd:documentation>
        </xsd:annotation>
        <xsd:choice>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="phoneNumber" type="comm:PhoneNumberTO"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="PersonTO">
        <xsd:annotation>
            <xsd:documentation>
                Identifier of bank or non-bank client

                cuid - id of bank client
                email - non-bank client identified by email
                phoneNumber - non-bank client identified by phone number
            </xsd:documentation>
        </xsd:annotation>
        <xsd:choice>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="email" type="xsd:string"/>
            <xsd:element name="phoneNumber" type="comm:PhoneTO"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="EmployeeTO">
        <xsd:annotation>
            <xsd:documentation>
                bank operator
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string"/>
            <xsd:element name="employeeDepartment" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ContactStatusTO">
        <xsd:annotation>
            <xsd:documentation>
                Status of contact

                ESTABLISHED - relevant to FROM_BANK call contact connected to client
                RECEIVED - relevant to all TO_BANK unfinished contacts
                FINISHED - relevant to all FROM_BANK contact except call (offline contacts) and to all TO_BANK finished contacts
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ESTABLISHED"/>
            <xsd:enumeration value="RECEIVED"/>
            <xsd:enumeration value="FINISHED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="CommonCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Common call data

                callID - identifier of call record in ReDat
                bankPhone - phone number of bank
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="callID" type="xsd:string" minOccurs="0"/>
            <xsd:element name="sessionId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="bankPhone" type="comm:PhoneNumberTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PersonPhoneNotMandatoryCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Call data with person phone not mandatory field
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="commonCallData" type="con:CommonCallTO"/>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PersonPhoneMandatoryCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Call data with person phone mandatory field
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="commonCallData" type="con:CommonCallTO"/>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="FailoverCommonCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Failover common call data
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="callID" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        identifier of call record in ReDat
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="bankPhone" type="comm:PhoneNumberTO">
                <xsd:annotation>
                    <xsd:documentation>
                        phone number of bank
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="FailoverToBankCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Failover TO_BANK call contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:FailoverCommonCallTO">
                <xsd:sequence>
                    <xsd:element name="personPhone" type="comm:PhoneNumberTO" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                phone number of person
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="FailoverFromBankCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Failover FROM_BANK call contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:FailoverCommonCallTO">
                <xsd:sequence>
                    <xsd:element name="personPhone" type="comm:PhoneNumberTO">
                        <xsd:annotation>
                            <xsd:documentation>
                                phone number of person
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="sessionId" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:group name="IbToBankType">
        <xsd:annotation>
            <xsd:documentation>
                customerFeedback : Customer contact feedback
                agendaMessage : agenda message
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="branch" type="xsd:boolean"/>
            <xsd:element name="branchEmployeeNumber" type="xsd:string" minOccurs="0"/>
            <xsd:element name="branchPosId" type="xsd:string" minOccurs="0"/>
            <xsd:choice minOccurs="0">
                <xsd:element name="customerFeedback" type="con:IBContactCustomerFeedbackTO"/>
                <xsd:element name="agendaMessage" type="con:AgendaMessageTO"/>
            </xsd:choice>
        </xsd:sequence>
    </xsd:group>

    <xsd:complexType name="IBContactReceiveTO">
        <xsd:annotation>
            <xsd:documentation>
                Incoming IB contact

                cuid - unique identification of the customer
                branch - flag: was message sent from branch?
                branchEmployeeNumber - identification of branch employee
                branchPosId - identification of branch POS
                sentFromDevice - mobile phone ID
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="commonData" type="con:IBContactCommonTO"/>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="sentFromDevice" type="xsd:string" minOccurs="0"/>
            <xsd:element name="externalRelation" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:group ref="con:IbToBankType"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IBContactCustomerFeedbackTO">
        <xsd:annotation>
            <xsd:documentation>
                Customer contact feedback

                satisfaction - customer feedback from IB message (Nr.: 1-5)
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="satisfaction" type="xsd:int" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AgendaMessageTO">
        <xsd:annotation>
            <xsd:documentation>
                Agenda message
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="domain" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        Agenda area to which the message is sent (MDM registry)
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IBContactFromBankTO">
        <xsd:annotation>
            <xsd:documentation>
                Outgoing IB contact
                display: display type of ib contact
                commonData: ib commont data
                expiration: validity of ib contact. If ib contact is unread and expiration is not valid than contact is not valid
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="commonData" type="con:IBContactCommonTO"/>
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IBContactCommonTO">
        <xsd:annotation>
            <xsd:documentation>
                General shared data for IB contact

                subject - subject of the contact
                text - text (body) of the contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="subject" type="xsd:string"/>
            <xsd:element name="text" type="xsd:string" minOccurs="0"/>
            <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="NotificationTO">
        <xsd:annotation>
            <xsd:documentation>
                General shared data for notification
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="failover" type="xsd:boolean"/>
            <xsd:element name="name" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IbNotificationTO">
        <xsd:complexContent>
            <xsd:extension base="con:NotificationTO">
                <xsd:sequence>
                    <xsd:element name="subject" type="xsd:string"/>
                    <xsd:element name="body" type="xsd:string"/>
                    <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="bodyMB" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="display" type="xsd:string"/>
                    <xsd:element name="button" type="con:IbButtonTO" minOccurs="0"/>
                    <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>


    <xsd:complexType name="IbButtonTO">
        <xsd:annotation>
            <xsd:documentation>
                IB button attributes - clickUrl, clickText
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="clickUrl" type="xsd:string"/>
            <xsd:element name="clickText" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:complexType name="SmsContactTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for SMS contact

                personPhone - person's phone number
                text - text of the contact
                expiration - expiration date of the contact
                messageId - identifier of sms contact in messageServer
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="text" type="xsd:string"/>
            <xsd:element name="messageId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="EmailContactTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for email contact

                messageId : ID of the contact (used for example in message server)
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:BaseEmailContactTO">
                <xsd:sequence>
                    <xsd:element name="messageId" type="xsd:string"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:group name="BaseEmailAttributes">
        <xsd:annotation>
            <xsd:documentation>
                Base email attributes

                bankEmail - email address of the bank
                personEmail - email address of the person
                emailReplyTo - address that should be used to reply to the message
                subject - subject of the contact
                body - text (body) of the contact
                expiration - expiration date of the contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="bankEmail" type="xsd:string"/>
            <xsd:element name="personEmail" type="xsd:string"/>
            <xsd:element name="emailReplyTo" type="xsd:string" minOccurs="0"/>
            <xsd:element name="subject" type="xsd:string" minOccurs="0"/>
            <xsd:element name="body" type="xsd:string" minOccurs="0"/>
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
        </xsd:sequence>
    </xsd:group>

    <xsd:complexType name="CampaignEmailTO">
        <xsd:sequence>
            <xsd:element name="personEmail" type="xsd:string"/>
            <xsd:element name="bankEmail" type="xsd:string" minOccurs="0"/>
            <xsd:element name="subject" type="xsd:string" minOccurs="0"/>
            <xsd:element name="emailURL" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="BaseEmailContactTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for email contact

                includes base email attributes and followed elements:
                attachment : attachments associated with contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:group ref="con:BaseEmailAttributes"/>
            <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PrintedContactTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for printed contact

                direction - direction of the contact
                realized - date and time of sending or receiving written messages
                subject - subject of the contact
                body - text (body) of the contact
                attachment - list with attachments
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="direction" type="con:Direction"/>
            <xsd:element name="realized" type="xsd:dateTime"/>
            <xsd:element name="subject" type="xsd:string"/>
            <xsd:element name="body" type="xsd:string"/>
            <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="VisitContentTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for contact of channel VISIT

                branchDepartmentDn - Dn according to LDAP department of the branch
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="branchDepartmentDn" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="WebChatContentTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for contact of channel WEB_CHAT

                subject - subject of chat conversation with customer
                body - text of chat conversation with customer
                personPhone - person's phone number
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="subject" type="xsd:string"/>
            <xsd:element name="body" type="xsd:string"/>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="MAChatContentTO">
        <xsd:annotation>
            <xsd:documentation>
                Specific data for contact of channel MA_CHAT

                subject - subject of chat conversation with customer
                body - text of chat conversation with customer
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="subject" type="xsd:string"/>
            <xsd:element name="body" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="Direction">
        <xsd:annotation>
            <xsd:documentation>
                Direction of the message.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="TO_BANK"/>
            <xsd:enumeration value="FROM_BANK"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="Display">
        <xsd:annotation>
            <xsd:documentation>
                The way to view message in IB
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="MAILBOX"/>
            <xsd:enumeration value="LIGHTBOX"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="AttachmentTO">
        <xsd:annotation>
            <xsd:documentation>
                Attachment of the contact.

                uidDocument - unique identification of the message from DMS
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="uidDocument" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CauseTO">
        <xsd:annotation>
            <xsd:documentation>
                Cause is used in business summary.

                code - code of the reason
                comment - description of the reason
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string"/>
            <xsd:element name="comment" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CauseExtendedTO">
        <xsd:complexContent>
            <xsd:extension base="con:CauseTO">
                <xsd:sequence>
                    <xsd:element name="plannedRealization" type="xsd:date" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                plannedRealization - date of planned realization
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="isClientPrimary" type="xsd:boolean" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                isClientPrimary - mark of the primary reason for the client's contact
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="OperatorBusinessSummaryOutputTO">
        <xsd:complexContent>
            <xsd:extension base="con:OperatorBusinessSummaryCommonTO">
                <xsd:sequence>
                    <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                            <xsd:documentation>
                                Number that identifies an employer
                            </xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="OperatorBusinessSummaryInputTO">
        <xsd:annotation>
            <xsd:documentation>
                Operator business summary.

                employeeNumber - number that identifies an employer
                employeeDepartment - identifier of employee department
                plannedRealization - date and time of planned realization
                emotion - emotion of the customer
                cause - cause of the business summary
            </xsd:documentation>
        </xsd:annotation>

        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string"/>
            <xsd:element name="employeeDepartment" type="xsd:string"/>
            <xsd:element name="emotion" type="xsd:string" minOccurs="0"/>
            <xsd:element name="cause" type="con:CauseExtendedTO" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType abstract="true" name="OperatorBusinessSummaryCommonTO">
        <xsd:annotation>
            <xsd:documentation>
                General shared data for business summary

                emotion - emotion of the customer
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="emotion" type="xsd:string" minOccurs="0"/>
            <xsd:element name="cause" type="con:CauseTO" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="InformationClassificationTO">
        <xsd:annotation>
            <xsd:documentation>
                Classification of contact

                classifier : who classify contact
                businessSummary : business summary of contact
                contentlessClassification : code of contentless contact (e.g. SPAM, UNDELIVERED). Defined in MDM registry

            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="classifier" type="con:RealisatorTO"/>
            <xsd:choice>
                <xsd:element name="businessSummary" type="con:OperatorBusinessSummaryOutputTO"/>
                <xsd:element name="contentlessClassification" type="xsd:string"/>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IBContentTO">
        <xsd:annotation>
            <xsd:documentation>
                IB content for getContactDetail

                subject - subject of the contact
                body - text (body) of the contact
                display : display type of contact
                bodyMobileBanking : shorten version of the text for mobile banking application
                readInDeviceID : ID of the device, where the contact was read by client
                read : contact was read by client
                url : URL from Code list
                urlText : text of the URL
                urlRegistered : date, when the URL was used by client
                deleted : contact was deleted by client
                revoked : contact was revoked by external system (e.g. SAS)
                expiration : contact expiration date
                expired : time when the contact has expired
                survey : definition of the survey
                surveyResult : associated result answer
                surveyRegistered : date when the survey was answered
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="subject" type="xsd:string"/>
            <xsd:element name="body" type="xsd:string" minOccurs="0"/>
            <xsd:element name="display" type="con:Display" minOccurs="0"/>
            <xsd:element name="bodyMobileBanking" type="xsd:string" minOccurs="0"/>
            <xsd:element name="readInDeviceID" type="xsd:string" minOccurs="0"/>
            <xsd:element name="read" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="url" type="xsd:string" minOccurs="0"/>
            <xsd:element name="urlText" type="xsd:string" minOccurs="0"/>
            <xsd:element name="urlRegistered" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="deleted" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="revoked" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="expired" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="survey" type="comm:Survey" minOccurs="0"/>
            <xsd:element name="surveyResult" type="res:IbSurveyResultTO" minOccurs="0"/>
            <xsd:element name="surveyRegistered" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="legalSegment" type="xsd:string" minOccurs="0"/>
            <xsd:group ref="con:IbToBankType"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="EmailContentTO">
        <xsd:annotation>
            <xsd:documentation>
                Email content for getContactDetail

                messageId : ID of the contact (used for example in message server)
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:group ref="con:BaseEmailAttributes"/>
            <xsd:element name="businessSummaryFailed" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="messageId" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="SmsContentTO">
        <xsd:annotation>
            <xsd:documentation>
                Sms content for getContactDetail
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="body" type="xsd:string"/>
            <xsd:element name="messageId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="businessSummaryFailed" type="xsd:boolean" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CallContentTO">
        <xsd:annotation>
            <xsd:documentation>
                call content for getContactDetail
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:PersonPhoneNotMandatoryCallTO">
                <xsd:sequence>
                    <xsd:element name="subject" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="body" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="PrintedContentTO">
        <xsd:annotation>
            <xsd:documentation>
                Print content for getContactDetail

                subject - subject of the contact
                body - text (body) of the contact
                attachment - list with attachments
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="subject" type="xsd:string"/>
            <xsd:element name="body" type="xsd:string"/>
            <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactDetailTO">
        <xsd:annotation>
            <xsd:documentation>
                Contact detail information

                attachment : attachments associated to contact
                contactFinish : information related to finishing contact
                informationClassification : Classification of contact
                externalRelation : relation of contact to external entities
                status : status of contatc
                name : name of contact (operator's note)
                subject: subject of contact
                body : body of contact
                informationClassificationMissing : true - contact is not classified yet, but classification is expected
                communicationBatch : communication batch
                processed : date of the first contact finishing
                origin : type of contact origin. it's relevant only for FROM_BANK contacts

                channel specific data
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:ContactBaseTO">
                <xsd:sequence>
                    <xsd:element name="attachment" type="con:AttachmentTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="contactFinish" type="con:ContactFinishTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="informationClassification" type="con:InformationClassificationTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="externalRelation" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="plannedCall" type="con:PlannedCallWithChangeTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="semaphoreOffer" type="con:SemaphoreOfferDetailWithChangeTO" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="status" type="xsd:string"/>
                    <xsd:element name="name" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="informationClassificationMissing" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="communicationBatch" type="comm:CommunicationBatchBaseDetailTO" minOccurs="0"/>
                    <xsd:element name="processed" type="xsd:dateTime" minOccurs="0"/>
                    <xsd:element name="origin" type="con:OriginExtendedTO" minOccurs="0"/>
                    <xsd:choice minOccurs="0">
                        <xsd:element name="ibContent" type="con:IBContentTO"/>
                        <xsd:element name="emailContent" type="con:EmailContentTO"/>
                        <xsd:element name="smsContent" type="con:SmsContentTO"/>
                        <xsd:element name="callContent" type="con:CallContentTO"/>
                        <xsd:element name="printedContent" type="con:PrintedContentTO"/>
                        <xsd:element name="visitContent" type="con:VisitContentTO"/>
                        <xsd:element name="webChatContent" type="con:WebChatContentTO"/>
                        <xsd:element name="maChatContent" type="con:MAChatContentTO"/>
                    </xsd:choice>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="RealisatorTO">
        <xsd:annotation>
            <xsd:documentation>
                Who create contact

                realisatorType - OPERATOR, AUTOMAT
                operattor - infor about operator
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="realisatorType" type="comm:CreatorDiscriminator"/>
            <xsd:element name="operator" type="con:EmployeeTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:complexType name="ContactBaseTO">
        <xsd:annotation>
            <xsd:documentation>
                Base contact data. Common data for getContactHistory and getContactDetail

                contactId - id of contact
                contactHolder - who is owner of contact
                channel - contact is related to specified channel
                direction - FROM_BANK or TO_BANK contact
                realizationDate - date of contact realization
                finishDate - date of contact processing, closing contact by operator
                processingWay - type of contact processing (code from MDM register "processingWay")
                realisator - who create contact
                processedStatus - contact status
                failover : true - contact was created in failover mode, false - contact was created in standard mode
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="contactId" type="comm:ContactIdTO"/>
            <xsd:element name="contactHolder" type="con:PersonTO"/>
            <xsd:element name="channel" type="con:Channel"/>
            <xsd:element name="direction" type="con:Direction"/>
            <xsd:element name="realizationDate" type="xsd:dateTime"/>
            <xsd:element name="finishDate" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="processingWay" type="xsd:string" minOccurs="0"/>
            <xsd:element name="realisator" type="con:RealisatorTO" minOccurs="0"/>
            <xsd:element name="processedStatus" type="con:ContactStatusTO"/>
            <xsd:element name="failover" type="xsd:boolean" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactItemTO">
        <xsd:annotation>
            <xsd:documentation>
                extented contact data to ContactBaseTO. It's used for getContactHistory output.

                origin - type of contact origin. it's relevant only for FROM_BANK contacts
                subject - short text of contact description (e.g. subject for email, ib, etc.)
                informationClassification - list of all unique codes (from all operators) or contentless information code (like SPAM etc)
                isRead : contact is or is not read by client. This element is related only to IB channel contacts
                isAlert : contact is or is not lightbox. This attribute is related only to IB channel contacts
                hasAttachments : contact has or has not any attachment
            </xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
            <xsd:extension base="con:ContactBaseTO">
                <xsd:sequence>
                    <xsd:element name="origin" type="con:OriginTO" minOccurs="0"/>
                    <xsd:element name="subject" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="informationClassification" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    <xsd:element name="isRead" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="isAlert" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="legalSegment" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="hasAttachments" type="xsd:boolean" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="DateIntervalTO">
        <xsd:annotation>
            <xsd:documentation>
                Date interval

                from - date from
                to - date to
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="from" type="xsd:date" minOccurs="0"/>
            <xsd:element name="to" type="xsd:date" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="InformationClassificationFilterTO">
        <xsd:annotation>
            <xsd:documentation>
                Support for filtering contacts by contact classification

                businessSummaryCauseCode: return only contacts with specified codes
                contentlessClassificationCode: return only contats with specifed contentless code
                withoutContentlessClassification: true - return only contacts not marked as contentless, false - ignore this element
            </xsd:documentation>
        </xsd:annotation>
        <xsd:choice>
            <xsd:element name="businessSummaryCauseCode" type="xsd:string" maxOccurs="unbounded"/>
            <xsd:element name="contentlessClassificationCode" type="xsd:string"/>
            <xsd:element name="withoutContentlessClassification" type="xsd:boolean"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="IbBoxFilterTO">
        <xsd:annotation>
            <xsd:documentation>
                specific filter related to ib contacts

                onlyUnread : true - return only unread contacts. false - ignore this element
                isAlert : true - return only LIGHTBOX contacts, false - return only MAILBOX contacts
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="onlyUnread" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="isAlert" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="evaluateLegalSegment" type="xsd:boolean" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CallBaseDataTO">
        <xsd:sequence>
            <xsd:element name="callId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="bankPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="callContact" type="con:CallContactTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactFilterTO">
        <xsd:annotation>
            <xsd:documentation>
                Filter for getContactHistory

                At least one of followed elements must be used:
                contactHolder : return only contacts related to specified contact holder (client or non-client)
                externalRelation : return only contacts associated to specified external entity
                contactId : return only contacts with specified contactId
                assistantEmployeeNumber : return contacts associated with specified branch assistant

                optional elements:
                channel : specify list of contact channels. Operator IN
                realizationDate : date interval of contact realization
                direction : return only TO_BANK or FROM_BANK contacts
                informationClassification : get only contacts with specified classification
                originWay : list of origing types (operator IN)
                isProcessed : true - return only processed contacts (contacts in status FINISHED)
                false - return only unprocessed contacts {contacts in other status then FINISHED)
                realizatorDepartmentNumber : return only contacts relized by specified department
                text: search specified substring in subject or text body of contacts
                hasAttachment: true - return only contacts with attachments; false - return only contacts without attachments
                ibBoxFilter: if specified return only filtered IB contacts
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="contactHolder" type="con:PersonTO" minOccurs="0"/>
            <xsd:element name="externalRelation" type="comm:ExternalRelationBaseTO" minOccurs="0"/>
            <xsd:element name="contactId" type="comm:ContactIdTO" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="assistantEmployeeNumber" type="xsd:string" minOccurs="0"/>

            <xsd:element name="channel" type="con:Channel" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="realizationDate" type="con:DateIntervalTO" minOccurs="0"/>
            <xsd:element name="direction" type="con:Direction" minOccurs="0"/>
            <xsd:element name="informationClassification" type="con:InformationClassificationFilterTO" minOccurs="0"/>
            <xsd:element name="originWay" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="isProcessed" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="realizatorDepartmentNumber" type="xsd:string" minOccurs="0"/>
            <xsd:element name="text" type="xsd:string" minOccurs="0"/>
            <xsd:element name="hasAttachments" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="ibBoxFilter" type="con:IbBoxFilterTO" minOccurs="0"/>
            <xsd:element name="evaluateLegalSegment" type="xsd:boolean" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactHistoryOutputSettingTO">
        <xsd:annotation>
            <xsd:documentation>
                set output specifics for getContactHistory

                needContactRelations : true - include contact relations (element "relation") in output
                maxCount : specify max number of returned contacts
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="needContactRelations" type="xsd:boolean" minOccurs="0"/>
            <xsd:element name="maxCount" type="xsd:long" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactRelationTO">
        <xsd:annotation>
            <xsd:documentation>
                Contact relation

                contactId - ID of the contact
                parentContactId - parent ID of the contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="contactId" type="xsd:string"/>
            <xsd:element name="parentContactId" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactsHistoryTO">
        <xsd:annotation>
            <xsd:documentation>
                Contact history
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="contact" type="con:ContactItemTO" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="relation" type="con:ContactRelationTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContactTO">
        <xsd:annotation>
            <xsd:documentation>
                Type of the contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:choice>
            <xsd:element name="emailContact" type="con:EmailContactTO"/>
            <xsd:element name="ibContact" type="con:IBContactFromBankTO"/>
            <xsd:element name="smsContact" type="con:SmsContactTO"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="NotificationChoiceTO">
        <xsd:annotation>
            <xsd:documentation>
                Type of the notification
            </xsd:documentation>
        </xsd:annotation>
        <xsd:choice>
            <xsd:element name="emailContact" type="con:EmailContactTO"/>
            <xsd:element name="ibNotification" type="con:IbNotificationTO"/>
            <xsd:element name="smsContact" type="con:SmsContactTO"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="ContactFinishTO">
        <xsd:annotation>
            <xsd:documentation>
                Element with information about finishing contact

                employeeNumber - employee number
                employeeDepartment - employee department
                created - contact finish creation timestamp
                processingWay - how was the contact finished
                businessSummaryFailed - flag if the CSP was working or not
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="creator" type="comm:CreatorDiscriminator"/>
            <xsd:element name="created" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0"/>
            <xsd:element name="employeeDepartment" type="xsd:string" minOccurs="0"/>
            <xsd:element name="processingWay" type="xsd:string"/>
            <xsd:element name="businessSummaryFailed" type="xsd:boolean"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="RegisterContactFinishTO">
        <xsd:annotation>
            <xsd:documentation>
                Element with information about finishing contact

                employeeNumber - employee number
                employeeDepartment - employee department
                created - contact finish creation timestamp
                processingWay - how was the contact finished
                businessSummaryFailed - flag if the CSP was working or not
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="creator" type="creator:CreatorTO"/>
            <xsd:element name="created" type="xsd:dateTime" minOccurs="0"/>
            <xsd:element name="processingWay" type="xsd:string"/>
            <xsd:element name="businessSummaryFailed" type="xsd:boolean"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="UnestablishedFromBankCallTO">
        <xsd:annotation>
            <xsd:documentation>
                Element with information about unestablished call

                dialed - Date when an unestablished call was dialed that was mistakenly processed as a established call
                whyPersonWasCalled - Information about why we called the client
                incomingCallHint - Optional hint for possible paired incoming call with this unanswered call
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="dialed" type="xsd:dateTime"/>
            <xsd:element name="whyPersonWasCalled" type="xsd:string" minOccurs="0"/>
            <xsd:element name="incomingCallHint" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="OriginTO">
        <xsd:annotation>
            <xsd:documentation>
                Context of contact origin
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="way" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        Origin of the contact
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="OriginExtendedTO">
        <xsd:complexContent>
            <xsd:extension base="con:OriginTO">
                <xsd:sequence>
                    <xsd:element name="originContext" type="con:OriginContextExtendedTO"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="OriginContextExtendedTO">
        <xsd:complexContent>
            <xsd:extension base="con:OriginContext"/>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="DefaultOperatorBusinessSummaryTO">
        <xsd:annotation>
            <xsd:documentation>
                Default business summary for concept
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        Number that identifies an employer
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employeeDepartment" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        Identifier of employee department
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="cause" type="con:CauseTO" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="UnansweredCallTO">
        <xsd:sequence>
            <xsd:element name="contactId" type="comm:ContactIdTO"/>
            <xsd:element name="person" type="con:BasePersonTO"/>
            <xsd:element name="bankPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="callId" type="xsd:string"/>
            <xsd:element name="dialed" type="xsd:dateTime"/>
            <xsd:element name="whyPersonWasCalled" type="xsd:string" minOccurs="0"/>
            <xsd:element name="incomingCallHint" type="xsd:string" minOccurs="0"/>
            <xsd:element name="originContext" type="con:OriginContext"/>
            <xsd:element name="contactFinish" type="con:ContactFinishTO"/>
            <xsd:element name="contentlessClassification" type="xsd:string"/>
            <xsd:element name="externalRelation" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ConceptCallTO">
        <xsd:annotation>
            <xsd:documentation>
                CONCEPT_CALL - corresponds to variant click-to-call
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="conceptCotactId" type="comm:ContactIdTO"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="NewCallTO">
        <xsd:annotation>
            <xsd:documentation>
                NEW_CALL - corresponds to variant Reply
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="predecessorContactId" type="comm:ContactIdTO" minOccurs="0"/>
            <xsd:element name="plannedContactId" type="xsd:string" minOccurs="0"/>
            <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Unique identification of the customer
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="origin" type="con:OriginExtendedTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CallContactTO">
        <xsd:choice>
            <xsd:element name="conceptCall" type="con:ConceptCallTO"/>
            <xsd:element name="newCall" type="con:NewCallTO"/>
        </xsd:choice>

    </xsd:complexType>

    <!-- getContactDetail - collections -->
    <xsd:complexType name="PlannedCallWithChangeTO">
        <xsd:annotation>
            <xsd:documentation>
                Element with planned call detail
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="plannedCallId" type="xsd:string"/>
            <xsd:element name="subject" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        subject of plannedCall
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="originContext" type="con:OriginContext"/>
            <xsd:element name="change" type="con:StatusChangeOverviewTO" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="StatusChangeOverviewTO">
        <xsd:annotation>
            <xsd:documentation>
                Element with basic status change information about the call contact
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="operatorType" type="comm:CreatorDiscriminator"/>
            <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        employeeNumber - number that identifies an employee
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="employeeDepartment" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        employeeDepartment - number that identifies an employee department
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="created" type="xsd:dateTime">
                <xsd:annotation>
                    <xsd:documentation>
                        created - status change creation timestamp
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="meaning" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        meaning - meaning type of the status change
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="reason" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        reason of plannedContact change
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="result" type="res:ResultTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="SemaphoreOfferDetailWithChangeTO">
        <xsd:sequence>
            <xsd:element name="communicationName" type="xsd:string"/>
            <xsd:element name="communicationKind" type="xsd:string"/>
            <xsd:element name="change" type="con:SemaphoreOfferChangeOverviewTO" maxOccurs="unbounded"/>
            <xsd:element name="result" type="res:ResultTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="SemaphoreOfferChangeOverviewTO">
        <xsd:sequence>
            <xsd:element name="created" type="xsd:dateTime"/>
            <xsd:element name="creator" type="comm:CreatorDiscriminator"/>
            <xsd:element name="meaning" type="xsd:string"/>
            <xsd:element name="employeeNumber" type="xsd:string" minOccurs="0"/>
            <xsd:element name="employeeDepartmentId" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="PredecessorTO">
        <xsd:annotation>
            <xsd:documentation>
                Type of the contact predecessor
            </xsd:documentation>
        </xsd:annotation>
        <xsd:choice>
            <xsd:element name="standardPredecessor" type="con:StandardPredecessorTO"/>
            <xsd:element name="failoverPredecessor" type="con:FailoverPredecessorTO"/>
        </xsd:choice>
    </xsd:complexType>

    <xsd:complexType name="StandardPredecessorTO">
        <xsd:sequence>
            <xsd:element name="contactId" type="comm:ContactIdTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="FailoverPredecessorTO">
        <xsd:sequence>
            <xsd:element name="person" type="con:PersonTO"/>
            <xsd:element name="originContext" type="con:OriginContextExtendedTO"/>
            <xsd:element name="channel" type="con:AlertChannelTO"/>
            <xsd:element name="realized" type="xsd:dateTime"/>
            <xsd:element name="operator" type="con:EmployeeTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="AlertSmsContactTO">
        <xsd:sequence>
            <xsd:element name="messageId" type="xsd:string"/>
            <xsd:element name="body" type="xsd:string"/>
            <xsd:element name="personPhone" type="comm:PhoneNumberTO"/>
            <xsd:element name="sent" type="xsd:dateTime"/>
            <xsd:element name="expiration" type="xsd:dateTime" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="ReceiveEmailRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer (In this case these are supposed cuids, which were found by emailTo attribute as primary email
                            in CIF)
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="emailContact" type="con:EmailContactTO"/>
                <xsd:element name="inReplyTo" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Message ID of the original message
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveEmailResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="predecessorContactId" type="comm:ContactIdTO" minOccurs="0"/>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AssignContactToCustomerRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AssignContactToCustomerResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="UnassignContactFromCustomerRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="UnassignContactFromCustomerResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RevokeConceptRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="conceptContactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RevokeConceptResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="resultCode" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Revoke concept result codes {OK, PREDECESSOR_WITH_REPLY}
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterContactFinishRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="contentlessClassification" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Contentless classification type
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contactFinish" type="con:RegisterContactFinishTO"/>
                <xsd:element name="unestablishedFromBankCall" type="con:UnestablishedFromBankCallTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterContactFinishResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterAutomaticReplyRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="predecessorContactId" type="comm:ContactIdTO"/>
                <xsd:element name="contentlessClassification" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Contentless classification type
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="sent" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Time of message was sent
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="originContext" type="con:OriginContext" minOccurs="0"/>
                <xsd:choice>
                    <xsd:element name="emailContact" type="con:EmailContactTO"/>
                    <xsd:element name="ibContact" type="con:IBContactFromBankTO"/>
                    <xsd:element name="smsContact" type="con:SmsContactTO"/>
                </xsd:choice>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterAutomaticReplyResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="CreateConceptRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="predecessorContactId" type="comm:ContactIdTO" minOccurs="0"/>
                <xsd:element name="channel" type="con:Channel"/>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="origin" type="con:OriginExtendedTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="CreateConceptResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="OverrideContactRelationsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="predecessorContactId" type="comm:ContactIdTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="OverrideContactRelationsResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ChangeConcept2sentMessageRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="conceptContactId" type="comm:ContactIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Message identification in CML.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="businessSummaryFailed" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>
                            Business summary failed flag
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="sent" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Time of contact was sent
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeNumber" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Number that identifies an employer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeDepartment" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Number that identifies an employer department
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contact" type="con:ContactTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ChangeConcept2sentMessageResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactBusinessSummaryRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="employeeNumber" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Number that identifies an employer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactBusinessSummaryResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="operatorBusinessSummary" type="con:OperatorBusinessSummaryOutputTO" minOccurs="0"/>
                <xsd:element name="emotionRequired" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>
                            Flag if emotion is required
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactHistoryRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="filter" type="con:ContactFilterTO"/>
                <xsd:element name="outputSetting" type="con:ContactHistoryOutputSettingTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactHistoryResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactsHistory" type="con:ContactsHistoryTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddContactBusinessSummaryRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="operatorBusinessSummary" type="con:OperatorBusinessSummaryInputTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddContactBusinessSummaryResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactDetailRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetContactDetailResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactDetail" type="con:ContactDetailTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterNotificationRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contact" type="con:NotificationChoiceTO"/>
                <xsd:element name="sent" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Datetime of message sent
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="originContext" type="con:OriginContextTO"/>
                <xsd:element name="businessSummaryCauseCode" type="xsd:string" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>
                            Business summary cause code
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contactExternalRelationList" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="operator" type="con:EmployeeTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterNotificationResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterCampaignEmailRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="emailContact" type="con:CampaignEmailTO"/>
                <xsd:element name="sent" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Datetime of message sent
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="originContext" type="con:OriginContextTO"/>
                <xsd:element name="businessSummaryCauseCode" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                    <xsd:annotation>
                        <xsd:documentation>
                            Business summary cause code
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="contactExternalRelationList" type="comm:ExternalRelationBaseTO" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="realTimeCampaign" type="comm:CommunicationBatchCommonTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterCampaignEmailResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveIBMessageRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="predecessorContactId" type="comm:ContactIdTO" minOccurs="0"/>
                <xsd:element name="contactData" type="con:IBContactReceiveTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveIBMessageResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RevokeIBMessageRequest">
        <xsd:complexType>
            <xsd:annotation>
                <xsd:documentation>
                    contactId - Unique identifier of contact cross systems
                </xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RevokeIBMessageResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetMessageReadRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:choice>
                    <xsd:element name="contactId" type="comm:ContactIdTO"/>
                    <xsd:element name="contactIdCryptogram" type="comm:ContactIdTO"/>
                </xsd:choice>
                <xsd:element name="readInDevice" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>readInDevice - hash of the device, where the contact was read by client</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetMessageReadResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterUrlClickRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:choice>
                    <xsd:element name="contactId" type="comm:ContactIdTO"/>
                    <xsd:element name="contactIdCryptogram" type="comm:ContactIdTO"/>
                </xsd:choice>
                <xsd:element name="url" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>url - link, which was clicked by client</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterUrlClickResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveCallRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="realized" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Time of call establishment.
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="callData" type="con:PersonPhoneNotMandatoryCallTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveCallResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveWebChatRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="phoneNumber" type="comm:PhoneNumberTO"/>
                <xsd:element name="sessionId" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            ID of web session
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="message" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Message which should be saved on planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="lastTraceId" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Last traceID for session
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveWebChatResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveMAChatRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="scheduleCallback" type="xsd:boolean">
                    <xsd:annotation>
                        <xsd:documentation>
                            Flag to recognize if is necessary to create planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="sessionId" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Identification of Addai's session
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="lastTraceId" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Last identification of interaction with session
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="message" type="xsd:string" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Message which should be saved on planned call
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ReceiveMAChatResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterEstablishedFromBankCallRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="con:CallBaseDataTO">
                    <xsd:sequence>
                        <xsd:element name="established" type="xsd:dateTime"/>
                        <xsd:element name="sessionId" type="xsd:string" minOccurs="0">
                            <xsd:annotation>
                                <xsd:documentation>
                                    Identification of Addai's session
                                </xsd:documentation>
                            </xsd:annotation>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterEstablishedFromBankCallResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterUnestablishedFromBankCallRequest">
        <xsd:complexType>
            <xsd:complexContent>
                <xsd:extension base="con:CallBaseDataTO">
                    <xsd:sequence>
                        <xsd:element name="dialed" type="xsd:dateTime"/>
                        <xsd:element name="creator" type="creator:CreatorTO"/>
                        <xsd:element name="contentlessClassification" type="xsd:string"/>
                        <xsd:element name="whyPersonWasCalled" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="incomingCallHint" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:extension>
            </xsd:complexContent>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterUnestablishedFromBankCallResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="unestablishedContactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterFromBankFailoverContactRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:choice>
                    <xsd:element name="emailContact" type="con:BaseEmailContactTO"/>
                    <xsd:element name="smsContact" type="con:SmsContactTO"/>
                    <xsd:element name="callContact" type="con:FailoverFromBankCallTO"/>
                </xsd:choice>
                <xsd:element name="realized" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Time when email was sent
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="creator" type="creator:CreatorTO" minOccurs="0"/>
                <xsd:element name="originContext" type="con:OriginContextTO"/>
                <xsd:element name="externalRelation" type="comm:ExternalRelationBaseTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterFromBankFailoverContactResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Id of created contact
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterToBankFailoverContactRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:choice>
                    <xsd:element name="emailContact" type="con:BaseEmailContactTO"/>
                    <xsd:element name="callContact" type="con:FailoverToBankCallTO"/>
                </xsd:choice>
                <xsd:element name="received" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            Time when email was received
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="externalRelation" type="comm:ExternalRelationBaseTO" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="defaultOperatorBusinessSummary" type="con:DefaultOperatorBusinessSummaryTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterToBankFailoverContactResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Id of created contact
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterPrintedMessageRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="printedMessage" type="con:PrintedContactTO"/>
                <xsd:element name="originContext" type="con:OriginContext" minOccurs="0"/>
                <xsd:element name="contactExternalRelationList" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterPrintedMessageResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Id of created contact
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterAssistanceRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="created" type="xsd:dateTime">
                    <xsd:annotation>
                        <xsd:documentation>
                            created - status change creation timestamp
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="cuid" type="xsd:long">
                    <xsd:annotation>
                        <xsd:documentation>
                            Unique identification of the customer
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeNumber" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            employeeNumber - number that identifies an employee
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="employeeDepartment" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            employeeDepartment - number that identifies an employee department
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="branchDepartmentDn" type="xsd:string">
                    <xsd:annotation>
                        <xsd:documentation>
                            Dn according to LDAP department with given department office
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="externalRelation" type="comm:ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterAssistanceResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO">
                    <xsd:annotation>
                        <xsd:documentation>
                            Id of created contact
                        </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddExternalRelationsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="externalRelations" type="comm:ExternalRelationTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddExternalRelationsResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetExternalRelationsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetExternalRelationsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="externalRelation" type="comm:ExternalRelationBaseTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RemoveExternalRelationsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="xsd:string"/>
                <xsd:element name="externalRelation " type="comm:ExternalRelationBaseTO"  maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RemoveExternalRelationsResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddDefaultBusinessSummaryRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="conceptContactId" type="comm:ContactIdTO"/>
                <xsd:element name="predecessorContactId" type="comm:ContactIdTO" minOccurs="0"/>
                <xsd:element name="defaultOperatorBusinessSummary" type="con:DefaultOperatorBusinessSummaryTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddDefaultBusinessSummaryResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetUnansweredCallsRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="person" type="con:BasePersonTO" minOccurs="0"/>
                <xsd:element name="dialedFrom" type="xsd:date"/>
                <xsd:element name="externalRelation" type="comm:ExternalRelationBaseTO" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetUnansweredCallsResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="unansweredCall" type="con:UnansweredCallTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterContactAlertRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="predecessor" type="con:PredecessorTO"/>
                <xsd:element name="alertSmsContact" type="con:AlertSmsContactTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RegisterContactAlertResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetUnansweredCallDetailRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="GetUnansweredCallDetailResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="unansweredCall" type="con:UnansweredCallTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetWebCampaignAnswerRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cryptogram" type="xsd:string"/>
                <xsd:element name="sent" type="xsd:dateTime"/>
                <xsd:element name="questionResult" type="con:QuestionResultTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SetWebCampaignAnswerResponse">
        <xsd:complexType/>
    </xsd:element>
    <xsd:element name="UpdateRealtimeEmailContactRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="comm:ContactIdTO"/>
                <xsd:element name="body" type="xsd:string"/>
                <xsd:element name="file" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="UpdateRealtimeEmailContactResponse">
        <xsd:complexType>
            <xsd:sequence/>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="QuestionResultTO">
        <xsd:sequence>
            <xsd:element name="questionId" type="xsd:string"/>
            <xsd:element name="text" type="xsd:string" minOccurs="0"/>
            <xsd:element name="result" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="AddVoiceChatRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="sessionId" type="xsd:string"/>
                <xsd:element name="lastTraceId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddVoiceChatResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StoreCallTranscriptRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="callId" type="xsd:string"/>
                <xsd:element name="transcript" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="StoreCallTranscriptResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="AddCallConnectionIdRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contactId" type="xsd:string"/>
                <xsd:element name="connectionId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AddCallConnectionIdResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

</xsd:schema>