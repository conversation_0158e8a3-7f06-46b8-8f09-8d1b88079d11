<?xml version="1.0"?>
<!-- $Id: 29c17aa1fb0049e9b0af82e25384f62a5c18c109 $ -->
<xsd:schema targetNamespace="http://airbank.cz/cml/ws/contact"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <xsd:complexType name="OriginContextTO">
        <xsd:annotation>
            <xsd:documentation>
                Context of contact origin
            </xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="businessProcess" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>
                        Business process context
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="businessIdentification" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        Business process identification, e.g. application ID
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="businessProcessEvent" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>
                        A business process event that triggered outgoing contact
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

</xsd:schema>
