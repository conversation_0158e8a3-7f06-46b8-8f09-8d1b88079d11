<?xml version="1.0" encoding="utf-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:creator="http://airbank.cz/cml/ws/creator"
            targetNamespace="http://airbank.cz/cml/ws/creator">
    <xsd:complexType name="Creator<PERSON>">
        <xsd:sequence>
            <xsd:element name="discriminator" type="creator:CreatorDiscriminator"/>
            <xsd:element name="employee" type="creator:EmployeeTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>
    
    <xsd:complexType name="EmployeeTO">
        <xsd:sequence>
            <xsd:element name="employeeNumber" type="xsd:string"/>
            <xsd:element name="employeeDepartment" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="CreatorDiscriminator">
        <xsd:annotation>
            <xsd:documentation>Who created the record
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OPERATOR"/>
            <xsd:enumeration value="AUTOMAT"/>
            <xsd:enumeration value="ANETA"/>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>