#!/bin/bash

# Path to the configuration file
CONFIG_FILE="./agents.config"

# Function to run a script based on a condition
run_script() {
    key=$1
    value=$(echo $2 | tr -d '[:space:]' | tr -d '"' | tr -d "'") # Trim whitespace and quotes
    script="stop-${key,,}.sh" # Convert the key to lowercase and prepend 'stop-'

    if [[ "$value" == "true" ]]; then
        echo "Agent "$key" enabled. Running $script..."
        sh "$script" # Run the script
    else
        echo "Agent "$key" disabled. Skipping $script..."
    fi
}

# Read the configuration file
while IFS=':' read -r key value || [[ -n "$key" ]]; do
    run_script "$key" "$value"
done < "$CONFIG_FILE"