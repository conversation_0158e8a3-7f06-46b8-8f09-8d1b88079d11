<?xml version="1.0" encoding="UTF-8"?>
<xs:schema
        xmlns:base="http://airbank.cz/ams/ws/application/shared"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        elementFormDefault="qualified"
        xmlns="http://airbank.cz/ams/ws/backoffice/application"
        targetNamespace="http://airbank.cz/ams/ws/backoffice/application">


    <xs:import namespace="http://airbank.cz/ams/ws/application/shared" schemaLocation="../xsd/AMSApplication.xsd"/>

    <xs:complexType name="RequestForSmeGC">
        <xs:complexContent>
            <xs:extension base="base:ApplicationDetail">
                <xs:sequence>
                    <xs:element name="contractNumber" type="xs:string"/>
                    <xs:element name="processType" type="ProcessType"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="ProcessType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="STANDARD"/>
            <xs:enumeration value="SHORT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="RequestForSmeAccount">
        <xs:complexContent>
            <xs:extension base="base:RequestForProduct">
                <xs:sequence>
                    <xs:element name="accountType" type="AccountType"/>
                    <xs:element name="primary" type="xs:boolean"/>
                    <xs:element name="currency" type="xs:string"/>
                    <xs:element name="name" type="xs:string"/>
                    <xs:element name="obsId" type="xs:long"/>
                    <xs:element name="accountNumber" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="AccountType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CURRENT"/>
            <xs:enumeration value="SAVING"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="RequestForSmeCard">
        <xs:complexContent>
            <xs:extension base="base:RequestForProduct">
                <xs:sequence>
                    <xs:element name="accountApplication" type="RequestForSmeAccount"/>
                    <xs:element name="cardDesignId" type="xs:string"/>
                    <xs:element name="cardDesignName" type="xs:string"/>
                    <xs:element name="cmsId" type="xs:long"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequestForSmeContractParticipant">
        <xs:complexContent>
            <xs:extension base="base:RequestForProduct">
                <xs:sequence>
                    <xs:element name="participantType" type="ParticipantType"/>
                    <xs:element name="customer" type="base:Customer"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="ParticipantType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FORMER_PERSON"/>
            <xs:enumeration value="BUSINESS_ENTITY"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="GetApplicationsDetailRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="1" name="applicationId" type="base:ApplicationId"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetApplicationsDetailResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="applicationDetail" type="base:ApplicationDetail"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
