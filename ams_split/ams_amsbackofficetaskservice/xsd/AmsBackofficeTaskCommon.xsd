<?xml version="1.0" encoding="UTF-8"?>
<xs:schema
        elementFormDefault="qualified"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:bc="http://airbank.cz/ams/ws/backoffice/common"
        xmlns="http://airbank.cz/ams/ws/backoffice/task/common"
        targetNamespace="http://airbank.cz/ams/ws/backoffice/task/common">

    <xs:import namespace="http://airbank.cz/ams/ws/backoffice/common" schemaLocation="BackofficeApplication.xsd"/>

    <xs:simpleType name="FindTasksSortByEto">
        <xs:annotation>
            <xs:documentation>Sorted by attribute</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="LOAN_AMOUNT"/>
            <xs:enumeration value="DATE_CREATE"/>
            <xs:enumeration value="DATE_VALID_FROM"/>
            <xs:enumeration value="DATE_MODIFY"/>
            <xs:enumeration value="DATE_SCORING"/>
            <xs:enumeration value="DATE_EXPIRATION"/>
            <xs:enumeration value="DATE_REFIX"/>
            <xs:enumeration value="DATE_UTILIZATION"/>
            <xs:enumeration value="LOCKED_FOR"/>
            <xs:enumeration value="ACTIVITY_TYPE"/>
            <xs:enumeration value="APPLICATION_TYPE"/>
            <xs:enumeration value="CUID"/>
            <xs:enumeration value="STATUS"/>
            <xs:enumeration value="ID_APPLICATION"/>
            <xs:enumeration value="ID_ENVELOPE"/>
            <xs:enumeration value="APPLICATION_STATUS"/>
            <xs:enumeration value="BUSINESS_OWNER"/>
            <xs:enumeration value="WITH_MORTGAGE"/>
            <xs:enumeration value="ACTIVE_MOST_PRIOR_EVENT"/>
            <xs:enumeration value="NAME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ManualTaskResultTo">
        <xs:annotation>
            <xs:documentation>Result of manual task.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="OK"/>
            <xs:enumeration value="NOT_OK"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ParamTo">
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Param name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="value" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Param value</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FindTasksFilterSortTo">
        <xs:sequence>
            <xs:element name="sortBy" type="FindTasksSortByEto">
                <xs:annotation>
                    <xs:documentation>Sorted by attribute</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sortAscending" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Sorted ascend/descend</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FindTasksFilterTo">
        <xs:annotation>
            <xs:documentation>Filter of manual task queue</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="sort" type="FindTasksFilterSortTo" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Sorting</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pageSize" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Paging: requested count of full details</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxIdCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Paging: requested count of IDs of record</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lockedFor" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationTypes" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Types of application - eg. Cash loan, consolidation, mortgage refinancing...</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="priorityEventTypes" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Types of priority event</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmountFrom" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmountTo" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationStatus" type="bc:ApplicationStatusTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationStage" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Application stage: APPLICATION, OFFER_ACCEPTANCE, DOCUMENT_DELIVERY, ...</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="approveStatus" type="bc:ApproveStatusTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="activities" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Current activity performed - can be more than one, if so, multiple values are
                        treated as OR relation.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Status of task</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="businessOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Business owner</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="uwOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>UW owner</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="uwOwnerOrCollateralUwOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>UW owner or collateral UW owner</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="businessOwnerOrUwOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Business owner or UW owner or collateral UW owner</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestForAssignment" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>True if requested assignment to UW</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateCreateFrom" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateCreateTo" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateUtilizationFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateUtilizationTo" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateRefixFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateRefixTo" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeMainType" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeOtherType" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="coDebtorExists" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelFinish" type="bc:ChannelTypeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Channel of role=FINISHED</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="withMortgage" type="xs:boolean" minOccurs="0" maxOccurs="1">
            </xs:element>
            <xs:element name="queueType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Task queue type (category).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="trancheOrder" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>we want a specific tranche order</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="excludeFirstTranche" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>or we want all but the first</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="TaskWarningTypeTo">
        <xs:restriction base="xs:string">
            <xs:enumeration value="AML_CHECK"/>
            <xs:enumeration value="EMBOSS_NAME_CHANGED"/>
            <xs:enumeration value="CUSTOMER_NAME_NOT_CONSISTENT_BETWEEN_AMS_AND_CIF"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ManualTaskBaseTo">
        <xs:annotation>
            <xs:documentation>Manual task - basic data</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="taskId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Manual task ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Application envelope ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Application ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="validFromTime" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Validity of manual task (Task could be postponed).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="doneTime" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>When status = DONE.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="createTime" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of manual task creation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyTime" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of last change of manual task.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyOperator" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Operator of last change of manual task.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lockedForOperator" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Operator who are currently assign to this task.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Status of manual task: ACTIVE, VERIFY, DONE</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="activity" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of manual task activity.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="postponeReasonCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Postpone reason code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="postponeNote" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Postpone note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="priority" type="xs:int" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Priority of manual task. Has its meaning for loans. For hyref, default value is used.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="queueType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Task queue type (category).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="order" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Order of task related to application .</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resultProposition" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Task result proposition</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taskWarnings" type="TaskWarningTypeTo" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Task warnings</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="trancheOrder" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Mortgage trache order</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="trancheId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Mortgage trache id</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ManualTaskTo" abstract="true">
        <xs:annotation>
            <xs:documentation>
                Abstract entity which define common properties for task which are shown as queue or dashboard.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ManualTaskBaseTo">
                <xs:sequence>
                    <xs:element name="applicationStatus" type="bc:ApplicationStatusTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Application status: APPROVED, MANUALVERIFY, WAITING, ...</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationStage" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Application stage: APPLICATION, OFFER_ACCEPTANCE, DOCUMENT_DELIVERY, ...</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="businessOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Business owner</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="uwOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>UW owner</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="collateralUwOwner" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Collateral UW owner</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="approveStatus" type="bc:ApproveStatusTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Application approve status</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicantCuid" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Applicant's cuid (global data in CIF).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicantId" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Applicant's ID (data on application).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstName" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Applicant's first name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastName" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Applicant's last name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="birthNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Applicant's birth number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeMainAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Amount of main income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeMainType" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of main income - CIF codeList.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeOtherAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Amount of other income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeOtherType" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of other income - CIF codeList.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="personType" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Person type: client/card holder/co-debtor/...</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="xs:decimal" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Loan amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelFinish" type="bc:ChannelTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Channel of role=FINISHED</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="channelRequest" type="bc:ChannelTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Channel of role=REQUEST.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationType" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Application type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="expirationDate" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Expiration date of envelope.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scoringDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Loan scoring date.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="activeMostPriorEvent" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Most priority active event</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="MortgageRefTaskTo">
        <xs:annotation>
            <xs:documentation>Manual task - with data from application for mortgage refinancing.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ManualTaskTo">
                <xs:sequence>
                    <xs:element name="dateRefix" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date of refix.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="dateUtilization" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date of utilization.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coDebtorExists" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Exists co-debtor on application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="withMortgageCommitment" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Contains at least one mortgage commitment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="publishedMortgageDocumentation" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Published mortgage documentation</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="drawdownPreconditionsStatus" type="bc:DrawdownPreconditionsStatusTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is incomplete pre drawn condition exist</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isLoanCommitmentApproved" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>RHY_LOAN_COMMITMENT has status APPROVED</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="LoanTaskTo">
        <xs:annotation>
            <xs:documentation>Manual task - with data from application for loan (consolidation/cash loan).</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ManualTaskTo">
                <xs:sequence>
                    <xs:element name="uwEntry" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date when enter to UW process</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="eventType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Priority event type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ManualNoteTo">
        <xs:annotation>
            <xs:documentation>Manual note - note for an application</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="noteId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Manual note ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Application envelope ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Application ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="createTime" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of manual note creation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyTime" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of last change of manual note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyOperator" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Operator of manual note creation or last modification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Text of note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deleted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Record is deleted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="privateNote" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>the note is marked as private</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="task" type="ManualTaskBaseTo" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Task related with this note</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Hibernate version of manual note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="noteType" type="NoteTypeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of note (USER, SYSTEM)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="checkListId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Check list ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="NoteTypeTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="USER"/>
            <xs:enumeration value="SYSTEM"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="VerifyPersonTo">
        <xs:annotation>
            <xs:documentation>Person data for verification activity</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Person's CUID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mainIncomeTaskType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>To create verify task of main income</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="otherIncomeTaskType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>To create verify task of other income</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskCode" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Activity contains risk code</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CheckListTypeTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="UW"/>
            <xs:enumeration value="HS"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CheckListTO">
        <xs:sequence>
            <xs:element name="id" minOccurs="0" maxOccurs="1" type="xs:long"/>
            <xs:element name="code" minOccurs="1" maxOccurs="1" type="xs:string"/>
            <xs:element name="order" minOccurs="1" maxOccurs="1" type="xs:int"/>
            <xs:element name="type" minOccurs="1" maxOccurs="1" type="CheckListTypeTO"/>
            <xs:element name="section" minOccurs="1" maxOccurs="1" type="xs:string"/>
            <xs:element name="stateOk" minOccurs="0" maxOccurs="1" type="xs:boolean"/>
            <xs:element name="stateHS" minOccurs="0" maxOccurs="1" type="xs:int">
                <xs:annotation>
                    <xs:documentation>0 : doklad dodán, 1 : Kontrola OK, 2 : Zpracován, 3: Kontrola NOK, 4 : Nevyžadujeme</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="warning" minOccurs="0" maxOccurs="1" type="xs:boolean"/>
            <xs:element name="applicationId" minOccurs="0" maxOccurs="1" type="xs:long"/>
            <xs:element name="envelopeId" minOccurs="0" maxOccurs="1" type="xs:long"/>
            <xs:element name="applicantId" minOccurs="0" maxOccurs="1" type="xs:long"/>
            <xs:element name="notes" type="ManualNoteTo" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ManualTaskPairingTo">
        <xs:complexContent>
            <xs:extension base="ManualTaskBaseTo">
                <xs:sequence>
                    <xs:element name="delayCount" type="xs:int" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Number of delayed calls</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="callResult" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>
                                The result of all the phone calls. Filled by MANUAL_TASK_CALL_RESULT codelist defined by MDM.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pairingResult" type="xs:string" minOccurs="0" maxOccurs="1" >
                        <xs:annotation>
                            <xs:documentation>The result of the manual pairing. Null when the activity was not finished yet.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ManualTaskWarningTo">
        <xs:complexContent>
            <xs:extension base="ManualTaskBaseTo">
                <xs:sequence>
                    <xs:element name="delayCount" type="xs:int" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Number of delayed calls</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="warningResult" type="xs:string" minOccurs="0" maxOccurs="1" >
                        <xs:annotation>
                            <xs:documentation>The result of the manual pairing. Null when the activity was not finished yet.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ManualTaskVerificationTo">
        <xs:annotation>
            <xs:documentation>Manual task for manual verification and result</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ManualTaskBaseTo">
                <xs:sequence>
                    <xs:element name="delayCount" type="xs:int" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Number of delayed calls</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeType" type="IncomeTypeTo" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of income</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="verificationLevel" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Level of verification</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employer" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Match target employer</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employment" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Confirmation of employment</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contractType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of contract</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contractValidTo" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Validity date of contract</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contractProlonged" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is contract prolonged</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employedFrom" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Employed from</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="netIncome" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Amount of net income</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="inProbationPeriod" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is in probation period?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="inTermination" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is in termination?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maternityLeave" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is in maternity leave?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="parentalLeave" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is in parental leave?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="longSick" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is in long sick?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="inExecution" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is in execution?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="verifiedIc" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is verified IC?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="paymentMethod" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Payment method</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="riskCase" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Result of calling</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="notDisclosed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Information isn't disclosed</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="VerificationPhoneNumberTo">
        <xs:annotation>
            <xs:documentation>Phone number for verification.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="bc:StandardPhoneTo">
                <xs:sequence>
                    <xs:element name="phoneId" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of phone number instance.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Description of phone number</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="verified" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is phone number verified?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognized" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is phone number recognized?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fromApplication" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is phone number from application?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="IncomeTypeTo">
        <xs:annotation>
            <xs:documentation>Type of income.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAIN_INCOME"/>
            <xs:enumeration value="OTHER_INCOME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ApplicantRoleTo">
        <xs:annotation>
            <xs:documentation>Applicant role in application.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HD">
                <xs:annotation>
                    <xs:documentation>Main debtor</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SD">
                <xs:annotation>
                    <xs:documentation>Co-debtor</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ManualPairingResultTo">
        <xs:sequence>
            <xs:element name="candidateObligationId" minOccurs="1" maxOccurs="1" type="xs:long">
                <xs:annotation>
                    <xs:documentation>The AMS obligation ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" minOccurs="0" maxOccurs="1" type="xs:string">
                <xs:annotation>
                    <xs:documentation>When the obligation was rejected, a code of the reject reason is sent here.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedObligationId" minOccurs="0" maxOccurs="1" type="xs:string">
                <xs:annotation>
                    <xs:documentation>When the obligation was manually paired, the ID of the obligation in register is sent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedObligationRegister" minOccurs="0" maxOccurs="1" type="xs:string">
                <xs:annotation>
                    <xs:documentation>When the obligation was manually paired, the register is sent.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
