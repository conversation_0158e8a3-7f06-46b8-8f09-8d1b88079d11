<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application"
                  targetNamespace="http://airbank.cz/ams/ws/application">
    <wsdl:types>
        <xs:schema xmlns="http://airbank.cz/ams/ws/application" targetNamespace="http://airbank.cz/ams/ws/application">
            <xs:include schemaLocation="AMSApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="FindApplicationsRequest">
        <wsdl:part name="FindApplicationsRequest" element="FindApplicationsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="FindApplicationsResponse">
        <wsdl:part name="FindApplicationsResponse" element="FindApplicationsResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="FindProductApplicationsResponse">
        <wsdl:part name="FindProductApplicationsResponse" element="FindProductApplicationsResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="InvalidateConsentRequest">
        <wsdl:part name="InvalidateConsentRequest" element="InvalidateConsentRequest">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="FindProductApplicationsRequest">
        <wsdl:part name="FindProductApplicationsRequest" element="FindProductApplicationsRequest">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsRequest">
        <wsdl:part name="FindGCApplicationsRequest" element="FindGCApplicationsRequest"/>
    </wsdl:message>

    <wsdl:message name="InvalidateConsentResponse">
        <wsdl:part name="InvalidateConsentResponse" element="InvalidateConsentResponse">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetApplicationsDetailResponse">
        <wsdl:part name="GetApplicationsDetailResponse" element="GetApplicationsDetailResponse">
        </wsdl:part>
    </wsdl:message>

    <wsdl:message name="GetApplicationsDetailRequest">
        <wsdl:part name="GetApplicationsDetailRequest" element="GetApplicationsDetailRequest"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsResponse">
        <wsdl:part name="FindGCApplicationsResponse" element="FindGCApplicationsResponse">
        </wsdl:part>
    </wsdl:message>
    
    <wsdl:message name="GetApplicationByCompletionIdRequest">
        <wsdl:part name="GetApplicationByCompletionIdRequest" element="GetApplicationByCompletionIdRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationByCompletionIdResponse">
        <wsdl:part name="GetApplicationByCompletionIdResponse" element="GetApplicationByCompletionIdResponse"/>
    </wsdl:message>

    <wsdl:message name="GetPersonalDataChangesRequest">
        <wsdl:part name="GetPersonalDataChangesRequest" element="GetPersonalDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataChangesResponse">
        <wsdl:part name="GetPersonalDataChangesResponse" element="GetPersonalDataChangesResponse"/>
    </wsdl:message>

    <wsdl:message name="SetBranchCodeRequest">
        <wsdl:part name="SetBranchCodeRequest" element="SetBranchCodeRequest"/>
    </wsdl:message>
    <wsdl:message name="SetBranchCodeResponse">
        <wsdl:part name="SetBranchCodeResponse" element="SetBranchCodeResponse"/>
    </wsdl:message>

    <wsdl:message name="FindGCApplicationsForCairActivationRequest">
        <wsdl:part name="FindGCApplicationsForCairActivationRequest" element="FindGCApplicationsForCairActivationRequest"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairActivationResponse">
        <wsdl:part name="FindGCApplicationsForCairActivationResponse" element="FindGCApplicationsForCairActivationResponse"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairActivationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FindGCApplicationsForCairActivationFault"/>
    </wsdl:message>

    <wsdl:message name="FindGCApplicationsForCairBySessionUuidRequest">
        <wsdl:part name="FindGCApplicationsForCairBySessionUuidRequest" element="FindGCApplicationsForCairBySessionUuidRequest"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairBySessionUuidResponse">
        <wsdl:part name="FindGCApplicationsForCairBySessionUuidResponse" element="FindGCApplicationsForCairBySessionUuidResponse"/>
    </wsdl:message>
    <wsdl:message name="FindGCApplicationsForCairBySessionUuidFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FindGCApplicationsForCairBySessionUuidFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationDetailByFilterRequest">
        <wsdl:part name="GetApplicationDetailByFilterRequest" element="GetApplicationDetailByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationDetailByFilterResponse">
        <wsdl:part name="GetApplicationDetailByFilterResponse" element="GetApplicationDetailByFilterResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationDetailByFilterFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationDetailByFilterFault"/>
    </wsdl:message>

    <wsdl:portType name="Application">
        <wsdl:operation name="GetApplicationsDetail">
            <wsdl:input name="GetApplicationsDetailRequest" message="GetApplicationsDetailRequest">
            </wsdl:input>
            <wsdl:output name="GetApplicationsDetailResponse" message="GetApplicationsDetailResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplications">
            <wsdl:input name="FindGCApplicationsRequest" message="FindGCApplicationsRequest">
            </wsdl:input>
            <wsdl:output name="FindGCApplicationsResponse" message="FindGCApplicationsResponse">
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InvalidateConsent">
            <wsdl:input name="InvalidateConsentRequest" message="InvalidateConsentRequest">
            </wsdl:input>
            <wsdl:output name="InvalidateConsentResponse" message="InvalidateConsentResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindProductApplications">
            <wsdl:input name="FindProductApplicationsRequest" message="FindProductApplicationsRequest">
            </wsdl:input>
            <wsdl:output name="FindProductApplicationsResponse" message="FindProductApplicationsResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindApplications">
            <wsdl:input name="FindApplicationsRequest" message="FindApplicationsRequest">
            </wsdl:input>
            <wsdl:output name="FindApplicationsResponse" message="FindApplicationsResponse">
            </wsdl:output>
        </wsdl:operation>
        
        <wsdl:operation name="GetApplicationByCompletionId">
            <wsdl:input name="GetApplicationByCompletionIdRequest" message="GetApplicationByCompletionIdRequest">
            </wsdl:input>
            <wsdl:output name="GetApplicationByCompletionIdResponse" message="GetApplicationByCompletionIdResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalDataChanges">
            <wsdl:input name="GetPersonalDataChangesRequest" message="GetPersonalDataChangesRequest">
            </wsdl:input>
            <wsdl:output name="GetPersonalDataChangesResponse" message="GetPersonalDataChangesResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="SetBranchCode">
            <wsdl:input name="SetBranchCodeRequest" message="SetBranchCodeRequest">
            </wsdl:input>
            <wsdl:output name="SetBranchCodeResponse" message="SetBranchCodeResponse">
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairActivation">
            <wsdl:input name="FindGCApplicationsForCairActivationRequest" message="FindGCApplicationsForCairActivationRequest"/>
            <wsdl:output name="FindGCApplicationsForCairActivationResponse" message="FindGCApplicationsForCairActivationResponse"/>
            <wsdl:fault name="FindGCApplicationsForCairActivationFault" message="FindGCApplicationsForCairActivationFault"/>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairBySessionUuid">
            <wsdl:input name="FindGCApplicationsForCairBySessionUuidRequest" message="FindGCApplicationsForCairBySessionUuidRequest"/>
            <wsdl:output name="FindGCApplicationsForCairBySessionUuidResponse" message="FindGCApplicationsForCairBySessionUuidResponse"/>
            <wsdl:fault name="FindGCApplicationsForCairBySessionUuidFault" message="FindGCApplicationsForCairBySessionUuidFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationDetailByFilter">
            <wsdl:input name="GetApplicationDetailByFilterRequest" message="GetApplicationDetailByFilterRequest"/>
            <wsdl:output name="GetApplicationDetailByFilterResponse" message="GetApplicationDetailByFilterResponse"/>
            <wsdl:fault name="GetApplicationDetailByFilterFault" message="GetApplicationDetailByFilterFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="ApplicationSoap11" type="Application">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="GetApplicationsDetail">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetApplicationsDetailRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetApplicationsDetailResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplications">
            <soap:operation soapAction=""/>
            <wsdl:input name="FindGCApplicationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="FindGCApplicationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="InvalidateConsent">
            <soap:operation soapAction=""/>
            <wsdl:input name="InvalidateConsentRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="InvalidateConsentResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindProductApplications">
            <soap:operation soapAction=""/>
            <wsdl:input name="FindProductApplicationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="FindProductApplicationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindApplications">
            <soap:operation soapAction=""/>
            <wsdl:input name="FindApplicationsRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="FindApplicationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationByCompletionId">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetApplicationByCompletionIdRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetApplicationByCompletionIdResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalDataChanges">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetPersonalDataChangesRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetPersonalDataChangesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="SetBranchCode">
            <soap:operation soapAction=""/>
            <wsdl:input name="SetBranchCodeRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="SetBranchCodeResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairActivation">
            <soap:operation soapAction=""/>
            <wsdl:input name="FindGCApplicationsForCairActivationRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="FindGCApplicationsForCairActivationResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FindGCApplicationsForCairActivationFault">
                <soap:fault name="FindGCApplicationsForCairActivationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="FindGCApplicationsForCairBySessionUuid">
            <soap:operation soapAction=""/>
            <wsdl:input name="FindGCApplicationsForCairBySessionUuidRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="FindGCApplicationsForCairBySessionUuidResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FindGCApplicationsForCairBySessionUuidFault">
                <soap:fault name="FindGCApplicationsForCairBySessionUuidFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationDetailByFilter">
            <soap:operation soapAction=""/>
            <wsdl:input name="GetApplicationDetailByFilterRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="GetApplicationDetailByFilterResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationDetailByFilterFault">
                <soap:fault name="GetApplicationDetailByFilterFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="ApplicationService">
        <wsdl:port name="ApplicationSoap11" binding="ApplicationSoap11">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>
