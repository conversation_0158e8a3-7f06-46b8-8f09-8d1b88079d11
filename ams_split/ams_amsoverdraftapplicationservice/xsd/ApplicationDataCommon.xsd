<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/common/applicationdata"
           targetNamespace="http://airbank.cz/ams/ws/application/common/applicationdata">

    <xs:annotation>
        <xs:documentation>Common types for application data for Browser and mobile</xs:documentation>
    </xs:annotation>

    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/authorization/" schemaLocation="Authorization.xsd"/>

    <xs:complexType name="BaseResponseCommon">
        <xs:annotation>
            <xs:documentation>Parent for ResponseCommon objects containing SpecificResponseCommon data.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="loanCommon:BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="progressRatio" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>indicates progress ratio over application data section</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="BaseResponseCommonWithCoDebtor">
        <xs:annotation>
            <xs:documentation>BaseResponseCommon extended for CoDebtor info.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="coDebtorData" type="loanCommon:CoDebtorDataTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Co-debtor data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitApplicationDataSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing applicationDataSummaryTask or applicationDataRecapitulationTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="loanCommon:BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="applicationDataRequiredType" type="appCommon:ApplicationDataRequiredTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of application data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="personalData" type="PersonalDataSummaryTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Init data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomes" type="IncomeDataSummaryTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Init data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedIncomeConfirmationRequested" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates that unconfirmed recognised income for the client has been identified.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedIncomes" type="RecognisedIncomeTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Last recognised incomes.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="clientOtherData" type="appCommon:ClientOtherDataTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Rest (in respect to client basic data) of main client data (only for co-debtor).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="clientContactData" type="appCommon:ClientContactDataTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Client contact information (only for co-debtor).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="householdIncomeExpense" type="HouseholdIncomeExpenseDataSummaryTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Household income/expense data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="householdMemberType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Information about applicant's household member type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationDataClientAccepted" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicator that applicant confirmed new structure of application data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="GetApplicationDataSummaryRequestCommon">
        <xs:annotation>
            <xs:documentation>Common request for get application data</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Envelope id.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="finalizationId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the application finalization process. Set only in finalization.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="GetApplicationDataSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when application data in branch or ICC.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationDataRequiredType" type="appCommon:ApplicationDataRequiredTypeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of application data.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="personalData" type="PersonalDataSummaryTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Init data.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomes" type="IncomeDataSummaryTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Init data.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroup" type="appCommon:DocumentGroupTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Collection of document groups</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="householdIncomeExpense" type="HouseholdIncomeExpenseDataSummaryTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Household income/expense data.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="RecognisedIncomeTypeTO">
        <xs:annotation>
            <xs:documentation>
                Values of CIF's EconomicalStatusType.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:long">
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="RecognisedEmployerConfirmationTO">
        <xs:sequence>
            <xs:element name="employerId" type="xs:long"/>
            <xs:element name="status" type="RecognisedStatusTO"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RecognisedIncomeConfirmationTO">
        <xs:sequence>
            <xs:element name="calculationId" type="xs:long"/>
            <xs:element name="status" type="RecognisedStatusTO"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RecognisedIncomeTO">
        <xs:sequence>
            <xs:element name="status" type="RecognisedStatusTO"/>
            <xs:element name="incomeType" type="RecognisedIncomeTypeTO"/>
            <xs:element name="account" type="loanCommon:AccountTO"/>
            <xs:element name="accountName" type="xs:string" minOccurs="0"/>
            <xs:element name="amount" type="xs:decimal"/>
            <xs:element name="amountCurrency" type="xs:string"/>
            <xs:element name="calculationId" type="xs:long"/>
            <xs:element name="transactionId" type="loanCommon:TransactionIdTO" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="differentFromPrevious" type="xs:boolean" minOccurs="0">
                <xs:annotation><xs:documentation>flag if amount is different from incomeAmount selected in previous application</xs:documentation></xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="RecognisedStatusTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RECOGNISED"/>
            <xs:enumeration value="VERIFIED_BY_CLIENT"/>
            <xs:enumeration value="REJECTED_BY_CLIENT"/>
            <xs:enumeration value="IGNORED_BY_CLIENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="PersonalDataSummaryTO">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing personalParametersTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="loanCommon:BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="relationToDebtor" type="RelationToDebtorTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Co-debtors relation to debtor.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="citizenships" type="xs:string" minOccurs="1" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Applicants citizenships.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="gender" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Gender of client used to show correct text in IB (mainly for walkin).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfDependants" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Number of dependants.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="married" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is client married.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="housing" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Housing code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="residence" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Residence code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="education" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Education code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="typeOfMajorIncome" type="EconomicalStatusTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of major income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="otherContactPhone" type="appCommon:PhoneNumberTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Other contact phone.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="primaryDocumentType" type="IdentificationDocumentTypeTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Primary identification document code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="readOnly" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates that screen should be read only - i.e. no data could be changed on it.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coDebtorData" type="loanCommon:CoDebtorDataTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Co-debtor data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>application id needed for additional spending collection in IB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>cuid needed for additional spending collection in IB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationData" type="loanCommon:ObligationTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of Obligations to consolidate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitIncomeExpenseTO">
        <xs:annotation>
            <xs:documentation>Expense or Income data holder with amount and visibility for client.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="amount" type="appCommon:MonetaryAmountTO" minOccurs="0"/>
            <xs:element name="visible" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IncomeExpenseTO">
        <xs:annotation>
            <xs:documentation>Expense or Income data holder with label and amount.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="amount" type="appCommon:MonetaryAmountTO"/>
            <xs:element name="label" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IncomeDataSummaryTO">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing income.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="loanCommon:BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="incomeCounter" type="appCommon:IncomeCounterTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates which income in income order is the data for.
                                Has to be provided in init response and update request.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="currentIncomeEconomicalStatus" type="EconomicalStatusTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of current income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="employmentType" type="EmploymentTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Employment type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="company" type="CompanyTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Data about the client's company.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="incomeTimeType" type="appCommon:IncomeTimeTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of income time.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date when the client started business in the company / employment has begun / other income started.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="income" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Income from the company.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="agreementUntil" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date when the agreement ends.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="InitBasicParametersResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing basicParameters screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="salutation" type="appCommon:SalutationTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of salutation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="title" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of title.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of first name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstNameConfirmed" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Confirmation that first name  correct.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of last name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastNameConfirmed" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Confirmation that last name  correct.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="titleAfter" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of title after name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="email" type="appCommon:EmailTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of email.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="emailDiacriticsConfirmedByClient" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Confirmation that email with diacritic is correct.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="emailDuplicityConfirmedByClient" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Confirmation that duplicity email is correct.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="phone" type="appCommon:PhoneNumberTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of phone.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="citizenships" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Init value of citizenships.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="personalId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of czech personal identification number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="gender" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of gender.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="dateOfBirth" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of birth date.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="placeOfBirth" type="appCommon:BirthPlaceTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of birth place.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateBasicParametersRequestCommon">
        <xs:annotation>
            <xs:documentation>Request data when updating basic parameters.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="salutation" type="appCommon:SalutationTO"/>
            <xs:element name="title" type="xs:string" minOccurs="0"/>
            <xs:element name="firstName" type="xs:string"/>
            <xs:element name="firstNameConfirmed" type="xs:boolean"/>
            <xs:element name="lastName" type="xs:string"/>
            <xs:element name="lastNameConfirmed" type="xs:boolean"/>
            <xs:element name="titleAfter" type="xs:string" minOccurs="0"/>
            <xs:element name="email" type="appCommon:EmailTO"/>
            <xs:element name="emailDiacriticsConfirmedByClient" type="xs:boolean"/>
            <xs:element name="emailDuplicityConfirmedByClient" type="xs:boolean"/>
            <xs:element name="phone" type="appCommon:PhoneNumberTO"/>
            <xs:element name="citizenships" type="xs:string" maxOccurs="unbounded"/>
            <xs:element name="personalId" type="xs:string" minOccurs="0"/>
            <xs:element name="gender" type="xs:string"/>
            <xs:element name="dateOfBirth" type="xs:date"/>
            <xs:element name="placeOfBirth" type="appCommon:BirthPlaceTO"/>

        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="CompanyTO">
        <xs:annotation>
            <xs:documentation>Company data. Company can be in used in role of employer or a subject owned by an entrepreneur.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="industry" type="IndustryTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of industry of the company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Name of the company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="identificationNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Identification number of the company. "IC"</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taxNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Tax number of the company. "DIC"</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="phone" type="appCommon:PhoneNumberTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Contact phone number to the company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="phoneUnknown" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates that contact phone number to the company is known.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="address" type="appCommon:AddressTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Residence address of the company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="active" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether the company is active in Bisnode or not.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="HouseholdIncomeExpenseDataSummaryTO">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing applicationDataSummaryTask and household income/expense screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="loanCommon:BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="otherHouseholdMembersIncome" type="InitIncomeExpenseTO" minOccurs="0"/>
                    <xs:element name="medicalFoodTransportationExpense" type="InitIncomeExpenseTO" minOccurs="0"/>
                    <xs:element name="accommodationExpense" type="InitIncomeExpenseTO" minOccurs="0"/>
                    <xs:element name="coDebtorData" type="loanCommon:CoDebtorDataTO" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="amountsProvided" type="loanCommon:AmountsProvidedTO" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="loanPaymentSumAllMembers" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Monthly household installments expenses (loans and mortgages).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitRegisterAgreementsResponseCommon">
        <xs:annotation>
            <xs:documentation>Init of registerAgreementTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="loanCommon:BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="primaryMobile" type="appCommon:PhoneNumberTO" minOccurs="0" maxOccurs="1"><xs:annotation>
                            <xs:documentation>Primary mobile phone number of the walkin.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coDebtorData" type="loanCommon:CoDebtorDataTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Co-debtor data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateAffidavitRequestCommon">
        <xs:annotation>
            <xs:documentation>Request data when updating affidavit.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="consent" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Affidavit consent.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitDocumentAndAddressesResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing documentAndAddresses screen.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="citizenships" type="xs:string" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Init value of citizenships.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentTypeList" type="appCommon:DocumentTypeTO" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of document types.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentType" type="appCommon:DocumentTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of document type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of document number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="permanentAddressSameAsApplicant" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Init value of flag indicates that co-debtor address is the same as main debtor address.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="permanentAddress" type="appCommon:AddressTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of permanent address.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mailingAddressDifferent" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Init value of flag indicates that mailing address is not the same as permanent.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of mailing address.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mainApplicantPermanentAddress" type="appCommon:AddressTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Init value of main debtor permanent address.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateDocumentAndAddressesRequestCommon">
        <xs:annotation>
            <xs:documentation>Request data when updating document and addresses.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="documentType" type="appCommon:DocumentTypeTO"/>
            <xs:element name="documentNumber" type="xs:string"/>
            <xs:element name="permanentAddressSameAsApplicant" type="xs:boolean"/>
            <xs:element name="permanentAddress" type="appCommon:AddressTO"/>
            <xs:element name="mailingAddressDifferent" type="xs:boolean"/>
            <xs:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>


    <xs:simpleType name="RelationToDebtorTO">
        <xs:annotation>
            <xs:documentation>Co-debtors relation to debtor.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="EconomicalStatusTO">
        <xs:annotation>
            <xs:documentation>Type of income. Employer(1), Business(3), Freelancer(7) and Other sources of income</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:int">

        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="EmploymentTypeTO">
        <xs:annotation>
            <xs:documentation>Employment type. Permanent or Time limited.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:int">

        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="IdentificationDocumentTypeTO">
        <xs:annotation>
            <xs:documentation>Type of identification document. ID_CARD, PASSPORT, ...</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="IndustryTO">
        <xs:annotation>
            <xs:documentation>Type of industry of an employer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ProfessionTO">
        <xs:annotation>
            <xs:documentation>Employment profession of a client.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
