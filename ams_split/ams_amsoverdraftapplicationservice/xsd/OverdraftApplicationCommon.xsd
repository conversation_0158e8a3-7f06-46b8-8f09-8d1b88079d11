<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/common/overdraft"
           targetNamespace="http://airbank.cz/ams/ws/application/common/overdraft">

    <xs:annotation>
        <xs:documentation>Common types for overdraft application</xs:documentation>
    </xs:annotation>

    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>

    <xs:complexType name="InitOverdraftAcceptedResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing any of
                overdraft accepted/acceptedWithLower/acceptedWithGreater screen.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="approvedAmount" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Max overdraft amount approved by scoring</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="overdraftAmount" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Current overdraft amount requested by client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestedAmount" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Original overdraft amount requested by client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="daysToCancelRequest" type="xs:int" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Time in days until the offer is expired.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="approveResult" type="appCommon:ApproveResultTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Result of LAP processing of the loan application.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productMin" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Overdraft minimal amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                <xs:annotation>
                    <xs:documentation>Code of process.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="promoFlag" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>True if promo is used for this application.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestFreeReserve" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>True if Overdraft without interest to a certain amount</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestFreeReserveAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Overdraft Amount without interest</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitOverdraftSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing any of
                overdraft accepted/acceptedWithLower/acceptedWithGreater screen.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="account" type="appCommon:AccountTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Account to which overdraft belongs</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="overdraftRepaymentPeriod" type="xs:int" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>How often the client needs to be in positive balance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRate" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Product interest rate</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="annualPercentageRate" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Annual Percentage Rate = RPSN </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="htmlPreAgreementDocumentId" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of HTML version of preagreement document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pdfPreAgreementDocumentId" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of PDF version of preagreement document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="promoFlag" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>True if promo is used for this application.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                <xs:annotation>
                    <xs:documentation>Code of process.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestFreeReserve" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>True if Overdraft without interest to a certain amount</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestFreeReserveAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Overdraft Amount without interest</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitCampaignPromoResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing of campaign promo screen.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                <xs:annotation>
                    <xs:documentation>Code of process.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OverdraftInterestRate">
        <xs:annotation>
            <xs:documentation>Interest rate</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="interestRate" type="xs:decimal" />
            <xs:element name="validFrom" type="xs:date" />
            <xs:element name="validTo" type="xs:date" />
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="OverdraftAcceptedTypeTO">
        <xs:annotation>
            <xs:documentation>Type of overdraft accepting.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACCEPTED"/>
            <xs:enumeration value="ACCEPTED_MORE"/>
            <xs:enumeration value="ACCEPTED_LESS"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>