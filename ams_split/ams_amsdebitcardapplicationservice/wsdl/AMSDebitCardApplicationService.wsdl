<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/debitcard"
                  targetNamespace="http://airbank.cz/ams/ws/application/debitcard">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/debitcard">
            <xs:include schemaLocation="AMSDebitCardApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- CardParameters task -->
    <wsdl:message name="InitSelectAccountAndHolderRequest">
        <wsdl:part element="InitSelectAccountAndHolderRequest" name="InitSelectAccountAndHolderRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSelectAccountAndHolderResponse">
        <wsdl:part element="InitSelectAccountAndHolderResponse" name="InitSelectAccountAndHolderResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSelectAccountAndHolderFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSelectAccountAndHolderFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSelectAccountAndHolderRequest">
        <wsdl:part element="UpdateSelectAccountAndHolderRequest" name="UpdateSelectAccountAndHolderRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSelectAccountAndHolderResponse">
        <wsdl:part element="UpdateSelectAccountAndHolderResponse" name="UpdateSelectAccountAndHolderResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSelectAccountAndHolderFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSelectAccountAndHolderFault"/>
    </wsdl:message>

    <!-- NewHolderBasicParameters task -->
    <wsdl:message name="InitNewHolderBasicParametersRequest">
        <wsdl:part element="InitNewHolderBasicParametersRequest" name="InitNewHolderBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitNewHolderBasicParametersResponse">
        <wsdl:part element="InitNewHolderBasicParametersResponse" name="InitNewHolderBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitNewHolderBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitNewHolderBasicParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateNewHolderBasicParametersRequest">
        <wsdl:part element="UpdateNewHolderBasicParametersRequest" name="UpdateNewHolderBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateNewHolderBasicParametersResponse">
        <wsdl:part element="UpdateNewHolderBasicParametersResponse" name="UpdateNewHolderBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateNewHolderBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateNewHolderBasicParametersFault"/>
    </wsdl:message>

    <!-- NewHolderPersonalParameters task -->
    <wsdl:message name="InitNewHolderPersonalParametersRequest">
        <wsdl:part element="InitNewHolderPersonalParametersRequest" name="InitNewHolderPersonalParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitNewHolderPersonalParametersResponse">
        <wsdl:part element="InitNewHolderPersonalParametersResponse" name="InitNewHolderPersonalParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitNewHolderPersonalParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitNewHolderPersonalParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateNewHolderPersonalParametersRequest">
        <wsdl:part element="UpdateNewHolderPersonalParametersRequest" name="UpdateNewHolderPersonalParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateNewHolderPersonalParametersResponse">
        <wsdl:part element="UpdateNewHolderPersonalParametersResponse" name="UpdateNewHolderPersonalParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateNewHolderPersonalParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateNewHolderPersonalParametersFault"/>
    </wsdl:message>

    <!-- NewHolderContactVerification task -->
    <wsdl:message name="InitNewHolderContactVerificationRequest">
        <wsdl:part element="InitNewHolderContactVerificationRequest" name="InitNewHolderContactVerificationRequest"/>
    </wsdl:message>
    <wsdl:message name="InitNewHolderContactVerificationResponse">
        <wsdl:part element="InitNewHolderContactVerificationResponse" name="InitNewHolderContactVerificationResponse"/>
    </wsdl:message>
    <wsdl:message name="InitNewHolderContactVerificationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitNewHolderContactVerificationFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateNewHolderContactVerificationRequest">
        <wsdl:part element="UpdateNewHolderContactVerificationRequest" name="UpdateNewHolderContactVerificationRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateNewHolderContactVerificationResponse">
        <wsdl:part element="UpdateNewHolderContactVerificationResponse" name="UpdateNewHolderContactVerificationResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateNewHolderContactVerificationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateNewHolderContactVerificationFault"/>
    </wsdl:message>

    <!-- Application already card holder step init. This step does not have update-->
    <wsdl:message name="InitAlreadyHolderRequest">
        <wsdl:part element="InitAlreadyHolderRequest" name="InitAlreadyHolderRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAlreadyHolderResponse">
        <wsdl:part element="InitAlreadyHolderResponse" name="InitAlreadyHolderResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAlreadyHolderFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAlreadyHolderFault"/>
    </wsdl:message>

    <!-- CardParameters task -->
    <wsdl:message name="InitCardParametersRequest">
        <wsdl:part element="InitCardParametersRequest" name="InitCardParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitCardParametersResponse">
        <wsdl:part element="InitCardParametersResponse" name="InitCardParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitCardParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitCardParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCardParametersRequest">
        <wsdl:part element="UpdateCardParametersRequest" name="UpdateCardParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCardParametersResponse">
        <wsdl:part element="UpdateCardParametersResponse" name="UpdateCardParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCardParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCardParametersFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="DebitCardApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>
                Starts a new application for new debit card of an existing customer.
                Uses cuid and idProfile from tracking context to identify customer and profile.

                Generated business faults:
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous debit card application in the VERIFY status
                - Application.DebitCard.CountLimitReached - The customer has already as many debit cards as possible
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSelectAccountAndHolder">
            <wsdl:documentation>Operation used to initialize the selectAccountAndHolderTask.</wsdl:documentation>
            <wsdl:input message="InitSelectAccountAndHolderRequest"/>
            <wsdl:output message="InitSelectAccountAndHolderResponse"/>
            <wsdl:fault name="InitSelectAccountAndHolderFault" message="InitSelectAccountAndHolderFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSelectAccountAndHolder">
            <wsdl:documentation>
                Operation used to update data for the selectAccountAndHolderTask. It uses to select account and card holder for new debit card.

                For all transitions except for back, following attributes are required:
                - selectedAccount

                Generated business faults:
                - Application.DebitCard.CountLimitReached - The customer has already as many debit cards as possible
            </wsdl:documentation>
            <wsdl:input message="UpdateSelectAccountAndHolderRequest"/>
            <wsdl:output message="UpdateSelectAccountAndHolderResponse"/>
            <wsdl:fault name="UpdateSelectAccountAndHolderFault" message="UpdateSelectAccountAndHolderFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitNewHolderBasicParameters">
            <wsdl:documentation>Operation used to initialize the newHolderBasicParametersTask.</wsdl:documentation>
            <wsdl:input message="InitNewHolderBasicParametersRequest"/>
            <wsdl:output message="InitNewHolderBasicParametersResponse"/>
            <wsdl:fault name="InitNewHolderBasicParametersFault" message="InitNewHolderBasicParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateNewHolderBasicParameters">
            <wsdl:documentation>Operation used to update data for the newHolderBasicParametersTask.
                For all transitions following attributes are required:
                - salutation
                - firstName
                - lastName
                - phone
                - email

                Generated validation errors:
                - Application.EmailDiacriticsNotConfirmed - The email address contains diacritics and it is not confirmed by the user
                - Application.FirstNameIsNotUsual - The first name provided has not been found within usual names and it is not confirmed by the client
                - Application.LastNameIsNotUsual - The last name provided has not been found within usual names and it is not confirmed by the client
                - validation errors resulting from CIF validations of firstName, lastName, phone and email
            </wsdl:documentation>
            <wsdl:input message="UpdateNewHolderBasicParametersRequest"/>
            <wsdl:output message="UpdateNewHolderBasicParametersResponse"/>
            <wsdl:fault name="UpdateNewHolderBasicParametersFault" message="UpdateNewHolderBasicParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitNewHolderPersonalParameters">
            <wsdl:documentation>Operation used to initialize the personalParametersTask.</wsdl:documentation>
            <wsdl:input message="InitNewHolderPersonalParametersRequest"/>
            <wsdl:output message="InitNewHolderPersonalParametersResponse"/>
            <wsdl:fault name="InitNewHolderPersonalParametersFault" message="InitNewHolderPersonalParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateNewHolderPersonalParameters">
            <wsdl:documentation>Operation used to update data for the newHolderPersonalParametersTask.
                For all transitions except for back, following attributes are required:
                - citizenship
                - selectedDocumentType
                - documentNumber
                - personalId or gender (one of these two must be filled)
                - dateOfBirth
                - placeOfBirth
                - documentIssuedBy
                - documentIssuedDate
                - permanentAddress

                Generated validation faults:
                - Application.Contract.SalutationPersonalIdConflict - Gender information from salutation and from personal id number do not conform
                - Application.Contract.SalutationGenderConflict - Gender information from salutation and selected gender do not conform
                - Application.ApplicantIsNotOldEnough - Applicant is not old enough
                - Application.AddressNotInCz - Specified address is not in CZ
                - Application.AddressNotConfirmed - The other address does not conform to registers and it is not confirmed by the user.
                - Application.DocumentIssueDateIsInFuture - The document issue date is in future
                - validation codes coming from validations in CIF
            </wsdl:documentation>
            <wsdl:input message="UpdateNewHolderPersonalParametersRequest"/>
            <wsdl:output message="UpdateNewHolderPersonalParametersResponse"/>
            <wsdl:fault name="UpdateNewHolderPersonalParametersFault" message="UpdateNewHolderPersonalParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitNewHolderContactVerification">
            <wsdl:documentation>Operation used to initialize the newHolderContactVerificationTask.</wsdl:documentation>
            <wsdl:input message="InitNewHolderContactVerificationRequest"/>
            <wsdl:output message="InitNewHolderContactVerificationResponse"/>
            <wsdl:fault name="InitNewHolderContactVerificationFault" message="InitNewHolderContactVerificationFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateNewHolderContactVerification">
            <wsdl:documentation>Operation used to update data for the NewHolderContactVerificationTask.
                For all transitions except for back, following attributes are required:

                Generated validation faults:
                - Application.DuplicatePhone - The phone provided is already used by some other active user
                - Application.DuplicateEmail - The email provided is already used by some other active user
                - Application.EmailDiacriticsNotConfirmed - The email address contains diacritics and it is not confirmed by the user
                - validation errors resulting from CIF validations of phone and email
            </wsdl:documentation>
            <wsdl:input message="UpdateNewHolderContactVerificationRequest"/>
            <wsdl:output message="UpdateNewHolderContactVerificationResponse"/>
            <wsdl:fault name="UpdateNewHolderContactVerificationFault" message="UpdateNewHolderContactVerificationFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAlreadyHolder">
            <wsdl:documentation>Init of final step the application.</wsdl:documentation>
            <wsdl:input message="InitAlreadyHolderRequest"/>
            <wsdl:output message="InitAlreadyHolderResponse"/>
            <wsdl:fault name="InitAlreadyHolderFault" message="InitAlreadyHolderFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitCardParameters">
            <wsdl:documentation>Operation used to initialize the cardParametersTask.</wsdl:documentation>
            <wsdl:input message="InitCardParametersRequest"/>
            <wsdl:output message="InitCardParametersResponse"/>
            <wsdl:fault name="InitCardParametersFault" message="InitCardParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateCardParameters">
            <wsdl:documentation>
                Operation used to update data for the cardParametersTask.

                For all transitions except for back, following attributes of cardParams are required:
                - addressSelected
                - showPin
                - sendPin
                - selectedCardDesignType
                - embossedName
                - embossedNameChanged

                Generated business faults:
                - Application.Account.NoWayHowToSendPin - User has not selected way how he wants to to get pin

                Generated validation faults:
                - Application.AddressNotInCz - Specified address is not in CZ
                - Application.AddressNotConfirmed - The other address does not conform to registers and it is not confirmed by the user.
            </wsdl:documentation>
            <wsdl:input message="UpdateCardParametersRequest"/>
            <wsdl:output message="UpdateCardParametersResponse"/>
            <wsdl:fault name="UpdateCardParametersFault" message="UpdateCardParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="DebitCardApplicationBinding" type="DebitCardApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSelectAccountAndHolder">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSelectAccountAndHolderFault">
                <soap:fault name="InitSelectAccountAndHolderFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSelectAccountAndHolder">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSelectAccountAndHolderFault">
                <soap:fault name="UpdateSelectAccountAndHolderFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitNewHolderBasicParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitNewHolderBasicParametersFault">
                <soap:fault name="InitNewHolderBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateNewHolderBasicParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateNewHolderBasicParametersFault">
                <soap:fault name="UpdateNewHolderBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitNewHolderPersonalParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitNewHolderPersonalParametersFault">
                <soap:fault name="InitNewHolderPersonalParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateNewHolderPersonalParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateNewHolderPersonalParametersFault">
                <soap:fault name="UpdateNewHolderPersonalParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitNewHolderContactVerification">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitNewHolderContactVerificationFault">
                <soap:fault name="InitNewHolderContactVerificationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateNewHolderContactVerification">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateNewHolderContactVerificationFault">
                <soap:fault name="UpdateNewHolderContactVerificationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitAlreadyHolder">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAlreadyHolderFault">
                <soap:fault name="InitAlreadyHolderFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitCardParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitCardParametersFault">
                <soap:fault name="InitCardParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCardParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCardParametersFault">
                <soap:fault name="UpdateCardParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="DebitCardApplicationService">
        <wsdl:documentation>
            Service providing operations related to debit card application of an existing customer.

            The application has the following task IDs:
            - selectAccountAndHolderTask - applicant selects account and card holder for new debit card
            - newHolderBasicParametersTask - basic information about new card holder
            - newHolderPersonalParametersTask - detail personal information about new card holder
            - newHolderContactVerificationTask - new card holder contact information verification
            - cardParametersTask - detail parameters about new debit card, such as card design, address, pin
            - alreadyHolderTask - just init, final step of the flow if holder is already exists
            - generateDocumentationTask - application was approved, documentation could be generated
            - waitForWarningResultTask - AMS is waiting for the warning processing

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="DebitCardApplicationPort" binding="DebitCardApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/debitcard"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>