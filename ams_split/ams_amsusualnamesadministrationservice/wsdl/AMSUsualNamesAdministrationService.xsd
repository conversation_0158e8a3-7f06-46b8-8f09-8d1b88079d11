<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/administration/usualnames"
           targetNamespace="http://airbank.cz/ams/ws/administration/usualnames">

    <xs:simpleType name="NameTypeTO">
        <xs:annotation>
            <xs:documentation>Role of usual name to be checked.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FIRST_NAME"/>
            <xs:enumeration value="LAST_NAME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="CheckUsualNameRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="name" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Name to check</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="NameTypeTO">
                    <xs:annotation>
                        <xs:documentation>Type of the name - first or last</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CheckUsualNameResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="usual" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>true when the name is usual, false otherwise</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InsertUsualNameRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="name" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Name to insert</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="NameTypeTO">
                    <xs:annotation>
                        <xs:documentation>Type of the name - first or last</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InsertUsualNameResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="inserted" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>false when the name already existed and was not inserted, true if it was created</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="RemoveUsualNameRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="name" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Name to remove</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="NameTypeTO">
                    <xs:annotation>
                        <xs:documentation>Type of the name - first or last</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="RemoveUsualNameResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="removed" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>false when the name did not existed, true if it was removed</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFrequentCzechFirstNamesRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="minFrequency" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Minimal frequency of name to return. If not set all names with any frequency are returned.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetFrequentCzechFirstNamesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="frequentName" type="FrequentNameTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Frequent names</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="FrequentNameTO">
        <xs:annotation>
            <xs:documentation>Data type for frequent name</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Name (first or last name)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="frequency" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Frequency</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
