<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns="http://airbank.cz/ams/ws/administration/usualnames"
                  targetNamespace="http://airbank.cz/ams/ws/administration/usualnames">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/administration/usualnames">
            <xs:include schemaLocation="AMSUsualNamesAdministrationService.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="CheckUsualNameRequest">
        <wsdl:part element="CheckUsualNameRequest" name="CheckUsualNameRequest"/>
    </wsdl:message>
    <wsdl:message name="CheckUsualNameResponse">
        <wsdl:part element="CheckUsualNameResponse" name="CheckUsualNameResponse"/>
    </wsdl:message>
    <wsdl:message name="InsertUsualNameRequest">
        <wsdl:part element="InsertUsualNameRequest" name="InsertUsualNameRequest"/>
    </wsdl:message>
    <wsdl:message name="InsertUsualNameResponse">
        <wsdl:part element="InsertUsualNameResponse" name="InsertUsualNameResponse"/>
    </wsdl:message>
    <wsdl:message name="RemoveUsualNameRequest">
        <wsdl:part element="RemoveUsualNameRequest" name="RemoveUsualNameRequest"/>
    </wsdl:message>
    <wsdl:message name="RemoveUsualNameResponse">
        <wsdl:part element="RemoveUsualNameResponse" name="RemoveUsualNameResponse"/>
    </wsdl:message>
    <wsdl:message name="GetFrequentCzechFirstNamesRequest">
        <wsdl:part element="GetFrequentCzechFirstNamesRequest" name="GetFrequentCzechFirstNamesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetFrequentCzechFirstNamesResponse">
        <wsdl:part element="GetFrequentCzechFirstNamesResponse" name="GetFrequentCzechFirstNamesResponse"/>
    </wsdl:message>

    <wsdl:portType name="UsualNamesAdministration">
        <wsdl:operation name="CheckUsualName">
            <wsdl:documentation>
                Checks, whether the name is found in usual names registry
            </wsdl:documentation>
            <wsdl:input message="CheckUsualNameRequest"/>
            <wsdl:output message="CheckUsualNameResponse"/>
        </wsdl:operation>
        <wsdl:operation name="InsertUsualName">
            <wsdl:documentation>
                Inserts usual name into registry.
            </wsdl:documentation>
            <wsdl:input message="InsertUsualNameRequest"/>
            <wsdl:output message="InsertUsualNameResponse"/>
        </wsdl:operation>
        <wsdl:operation name="RemoveUsualName">
            <wsdl:documentation>
                Removes usual name from registry.
            </wsdl:documentation>
            <wsdl:input message="RemoveUsualNameRequest"/>
            <wsdl:output message="RemoveUsualNameResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetFrequentCzechFirstNames">
            <wsdl:documentation>
                Gets frequent czech first names.
            </wsdl:documentation>
            <wsdl:input message="GetFrequentCzechFirstNamesRequest"/>
            <wsdl:output message="GetFrequentCzechFirstNamesResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="UsualNamesAdministrationBinding" type="UsualNamesAdministration">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="CheckUsualName">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InsertUsualName">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="RemoveUsualName">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetFrequentCzechFirstNames">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="UsualNamesAdministrationService">
        <wsdl:documentation>
            Service for AMS administration tasks.
        </wsdl:documentation>
        <wsdl:port name="UsualNamesAdministrationPort" binding="UsualNamesAdministrationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/administration/usualnames"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
