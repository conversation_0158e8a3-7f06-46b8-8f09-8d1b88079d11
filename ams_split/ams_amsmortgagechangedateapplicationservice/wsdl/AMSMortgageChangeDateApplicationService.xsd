<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/mortgageChangeDate"
           targetNamespace="http://airbank.cz/ams/ws/application/mortgageChangeDate">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeStartRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitChangeDateRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitChangeDateResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing ChangeDateTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeInitResponse">
                    <xs:sequence>
                        <xs:element name="possibleInstallmentDays" type="xs:int" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible payment days as input for combo.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedInstallmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Payment day from application. Null, if not set by user previously.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateChangeDateRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="instalmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Selected installment day.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateChangeDateResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating ChangeDateTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeUpdateChangeResponse">
                    <xs:sequence>
                        <xs:element name="possibleInstallmentDays" type="xs:int" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible payment days as input for combo.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedInstallmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Payment day from application. Null, if not set by user previously.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of summary page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing SummaryTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeInitResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of summary page for change loan date application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeUpdateSummaryRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating SummaryTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeUpdateSummaryResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>