<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/investmentcontract"
                  targetNamespace="http://airbank.cz/ams/ws/application/investmentcontract">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/investmentcontract">
            <xs:include schemaLocation="AMSInvestmentContractApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Note task -->
    <wsdl:message name="InitNoteRequest">
        <wsdl:part element="InitNoteRequest" name="InitNoteRequest"/>
    </wsdl:message>
    <wsdl:message name="InitNoteResponse">
        <wsdl:part element="InitNoteResponse" name="InitNoteResponse"/>
    </wsdl:message>
    <wsdl:message name="InitNoteFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitNoteFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateNoteRequest">
        <wsdl:part element="UpdateNoteRequest" name="UpdateNoteRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateNoteResponse">
        <wsdl:part element="UpdateNoteResponse" name="UpdateNoteResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateNoteFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateNoteFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:message name="TerminateInvestmentContractRequest">
        <wsdl:part element="TerminateInvestmentContractRequest" name="TerminateInvestmentContractRequest"/>
    </wsdl:message>
    <wsdl:message name="TerminateInvestmentContractResponse">
        <wsdl:part element="TerminateInvestmentContractResponse" name="TerminateInvestmentContractResponse"/>
    </wsdl:message>
    <wsdl:message name="TerminateInvestmentContractFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="TerminateInvestmentContractFault"/>
    </wsdl:message>

    <wsdl:message name="GenerateInvestmentMeetingRecordRequest">
        <wsdl:part element="GenerateInvestmentMeetingRecordRequest" name="GenerateInvestmentMeetingRecordRequest"/>
    </wsdl:message>
    <wsdl:message name="GenerateInvestmentMeetingRecordResponse">
        <wsdl:part element="GenerateInvestmentMeetingRecordResponse" name="GenerateInvestmentMeetingRecordResponse"/>
    </wsdl:message>
    <wsdl:message name="GenerateInvestmentMeetingRecordFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GenerateInvestmentMeetingRecordFault"/>
    </wsdl:message>

    <wsdl:portType name="InvestmentContractApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for investment contract.

            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>
        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitNote">
            <wsdl:documentation>Operation used to initialize the noteTask.</wsdl:documentation>
            <wsdl:input message="InitNoteRequest"/>
            <wsdl:output message="InitNoteResponse"/>
            <wsdl:fault name="InitNoteFault" message="InitNoteFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateNote">
            <wsdl:documentation>Operation used to update data for the noteTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateNoteRequest"/>
            <wsdl:output message="UpdateNoteResponse"/>
            <wsdl:fault name="UpdateNoteFault" message="UpdateNoteFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.

                Generated business faults:
                - Common.ProcessEnded - Underlaying activiti process ended
            </wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="TerminateInvestmentContract">
            <wsdl:documentation>Terminate existing investment contract product.</wsdl:documentation>
            <wsdl:input message="TerminateInvestmentContractRequest"/>
            <wsdl:output message="TerminateInvestmentContractResponse"/>
            <wsdl:fault name="TerminateInvestmentContractFault" message="TerminateInvestmentContractFault"/>
        </wsdl:operation>

        <wsdl:operation name="GenerateInvestmentMeetingRecord">
            <wsdl:documentation>Generate investment meeting record.</wsdl:documentation>
            <wsdl:input message="GenerateInvestmentMeetingRecordRequest"/>
            <wsdl:output message="GenerateInvestmentMeetingRecordResponse"/>
            <wsdl:fault name="GenerateInvestmentMeetingRecordFault" message="GenerateInvestmentMeetingRecordFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="InvestmentContractApplicationBinding" type="InvestmentContractApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitNote">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitNoteFault">
                <soap:fault name="InitNoteFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateNote">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateNoteFault">
                <soap:fault name="UpdateNoteFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="TerminateInvestmentContract">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="TerminateInvestmentContractFault">
                <soap:fault name="TerminateInvestmentContractFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GenerateInvestmentMeetingRecord">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GenerateInvestmentMeetingRecordFault">
                <soap:fault name="GenerateInvestmentMeetingRecordFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="InvestmentContractApplicationService">
        <wsdl:documentation>Service providing operations related to investment contract application.

            The application has no task IDs:
            - noteTask - page with information note
            - getCurrentTask - returns current task (step) of investment contract application process
            - cancel - cancels whole investment contract application

            Service terminateInvestmentContract can generate the following business faults:
            - Investment.AssetAccount.CannotBeTerminated - asset account cannot be terminated
            - Investment.InvestmentAccount.CannotBeTerminated - investment account cannot be terminated
        </wsdl:documentation>

        <wsdl:port name="InvestmentContractApplicationPort" binding="InvestmentContractApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/investmentContract"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
