<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/scoring/verification"
                  targetNamespace="http://airbank.cz/ams/ws/scoring/verification">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/scoring/verification">
            <xs:include schemaLocation="AMSScoringVerificationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="DocumentVerificationRequest">
        <wsdl:part element="DocumentVerificationRequest" name="DocumentVerificationRequest"/>
    </wsdl:message>
    <wsdl:message name="DocumentVerificationResponse">
        <wsdl:part element="DocumentVerificationResponse" name="DocumentVerificationResponse"/>
    </wsdl:message>
    <wsdl:message name="DocumentVerificationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="DocumentVerificationFault"/>
    </wsdl:message>
    <wsdl:message name="ProcessApplicationScoringResultRequest">
        <wsdl:part element="ProcessApplicationScoringResultRequest" name="ProcessApplicationScoringResultRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessApplicationScoringResultResponse">
        <wsdl:part element="ProcessApplicationScoringResultResponse" name="ProcessApplicationScoringResultResponse"/>
    </wsdl:message>
    <wsdl:message name="ProcessApplicationScoringResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ProcessApplicationScoringResultFault"/>
    </wsdl:message>
    <wsdl:message name="ProcessScoringNotificationRequest">
        <wsdl:part element="ProcessScoringNotificationRequest" name="ProcessScoringNotificationRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessScoringNotificationResponse">
        <wsdl:part element="ProcessScoringNotificationResponse" name="ProcessScoringNotificationResponse"/>
    </wsdl:message>
    <wsdl:message name="ProcessScoringNotificationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ProcessScoringNotificationFault"/>
    </wsdl:message>
    <wsdl:message name="ProcessCompletionPreDrawnCheckResultResponse">
        <wsdl:part element="ProcessCompletionPreDrawnCheckResultResponse" name="ProcessCompletionPreDrawnCheckResultResponse"/>
    </wsdl:message>
    <wsdl:message name="ProcessCompletionPreDrawnCheckResultRequest">
        <wsdl:part element="ProcessCompletionPreDrawnCheckResultRequest" name="ProcessCompletionPreDrawnCheckResultRequest"/>
    </wsdl:message>
    <wsdl:message name="ProcessCompletionPreDrawnCheckResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ProcessCompletionPreDrawnCheckResultFault"/>
    </wsdl:message>

    <wsdl:portType name="ScoringVerification">
        <wsdl:operation name="DocumentVerification">
            <wsdl:documentation>
                Return list of failed process jobs
            </wsdl:documentation>
            <wsdl:input message="DocumentVerificationRequest"/>
            <wsdl:output message="DocumentVerificationResponse"/>
            <wsdl:fault name="DocumentVerificationFault" message="DocumentVerificationFault"/>
        </wsdl:operation>
        <wsdl:operation name="ProcessApplicationScoringResult">
            <wsdl:input message="ProcessApplicationScoringResultRequest"/>
            <wsdl:output message="ProcessApplicationScoringResultResponse"/>
            <wsdl:fault name="ProcessApplicationScoringResultFault" message="ProcessApplicationScoringResultFault"/>
        </wsdl:operation>
        <wsdl:operation name="ProcessScoringNotification">
            <wsdl:input message="ProcessScoringNotificationRequest"/>
            <wsdl:output message="ProcessScoringNotificationResponse"/>
            <wsdl:fault name="ProcessScoringNotificationFault" message="ProcessScoringNotificationFault"/>
        </wsdl:operation>
        <wsdl:operation name="ProcessCompletionPreDrawnCheckResult">
            <wsdl:input message="ProcessCompletionPreDrawnCheckResultRequest"/>
            <wsdl:output message="ProcessCompletionPreDrawnCheckResultResponse"/>
            <wsdl:fault name="ProcessCompletionPreDrawnCheckResultFault" message="ProcessCompletionPreDrawnCheckResultFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ScoringVerificationBinding" type="ScoringVerification">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="DocumentVerification">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="DocumentVerificationFault">
                <soap:fault name="DocumentVerificationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="ProcessApplicationScoringResult">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ProcessApplicationScoringResultFault">
                <soap:fault name="ProcessApplicationScoringResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="ProcessScoringNotification">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ProcessScoringNotificationFault">
                <soap:fault name="ProcessScoringNotificationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="ProcessCompletionPreDrawnCheckResult">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ProcessCompletionPreDrawnCheckResultFault">
                <soap:fault name="ProcessCompletionPreDrawnCheckResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="ScoringVerificationService">
        <wsdl:documentation>
            Service to get result from LAP about scoring verification document in completion.
        </wsdl:documentation>
        <wsdl:port name="ScoringVerificationPort" binding="ScoringVerificationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ws/ams/scoring/verification"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
