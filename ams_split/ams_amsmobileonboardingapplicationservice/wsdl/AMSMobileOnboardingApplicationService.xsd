<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/onboarding"
           targetNamespace="http://airbank.cz/ams/ws/application/onboarding">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="applicantRef" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Id applicant reference (id of registration envelope)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="smeRequest" type="xs:string"  minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>request sme</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                    <xs:sequence>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cuid" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Optional cuid for BankId onboarding</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitIdCardUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitIdCardUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="frontSideUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>UUID of front side of ID card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="backSideUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>UUID of back side of ID card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="withoutActivationPaymentNotification" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether activation payment notification should be displayed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateIdCardUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="frontSideUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>UUID of front side of ID card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="backSideUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>UUID of back side of ID card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateIdCardUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSecondIdDocumentUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSecondIdDocumentUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="documentUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>UUID of uploaded document.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documentType" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Selected document type.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="possibleDocumentTypes" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible document types.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSecondIdDocumentUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="documentUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>UUID of uploaded document.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documentType" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Selected document type.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSecondIdDocumentUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitClientBasicDataRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitClientBasicDataResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="appCommon:OnboardingClientBasicDataTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Client basic data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="birthNumberLocked" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether it is possible to change birth number or not (if birthNumberLocked is true then change is not possible).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="oCRFrontSideDone" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Indicates whatever the front side of identification document was recognized</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="oCRBackSideDone" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Indicates whatever the back side of identification document was recognized</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateClientBasicDataRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="data" type="appCommon:OnboardingClientBasicDataTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Client basic data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateClientBasicDataResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="cuid" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>ID of created customer.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLegalMandatoryDataRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLegalMandatoryDataResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="relationshipData" type="appCommon:RelationshipTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Data about client relationship to the bank</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="taxResidenceCountryCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    country code of person's tax residence (optional)
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="taxId" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    person's tax identifier if any (optional)
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLegalMandatoryDataRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="relationshipData" type="appCommon:RelationshipTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Data about client relationship to the bank</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="taxResidenceCountryCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    country code of person's tax residence (optional)
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="taxId" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    person's tax identifier if any (optional)
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="smeRequest" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>
                                    sme request
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLegalMandatoryDataResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of application envelope.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractResumeApplicationResponse">
                    <xs:sequence>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetProgressRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="applicantRef" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id reference of applicant (envelope id of register application).</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetProgressResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeVersion" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Current version of the application envelope.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Id of the envelope containing application.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationStatus" type="appCommon:ApplicationStatusTO" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Application status.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationTaskId" type="appCommon:TaskIdTO" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Id of the current task of application phase.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="finalizationTaskId" type="appCommon:TaskIdTO" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Id of the current task of finalization phase.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="finalizationProcessId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Id of finalization process.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="requiredDocumentGroup" type="appCommon:RequiredDocumentGroupInfo" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Required document groups with fulfillment state.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="finished" type="xs:boolean" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Is process finished and contract is activated.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="politicallyExposedApplicant" type="xs:boolean" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Flag whether applicant is politically exposed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="registrationType" type="RegistrationEnvelopeType" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Envelope type of registration</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitScoringApprovedRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitScoringApprovedResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="RestartScoringRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="RestartScoringResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitContractDocumentsOverviewRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest" />
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitContractDocumentsOverviewResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse" >
                    <xs:sequence>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateContractDocumentsOverviewRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateContractDocumentsOverviewResponse">
    <xs:complexType>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractUpdateResponse" />
        </xs:complexContent>
    </xs:complexType>
    </xs:element>

    <xs:element name="EnqueueForOCRRequest">
        <xs:annotation>
            <xs:documentation>Start OCR process on uploded document.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="uuid" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Id of document to OCR.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="EnqueueForOCRResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="RegistrationEnvelopeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MOBILE_REG_BY_BANKID"/>
            <xs:enumeration value="MOBILE_REGISTRATION"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
