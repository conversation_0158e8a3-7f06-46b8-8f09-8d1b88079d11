<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/terminateaccount"
           targetNamespace="http://airbank.cz/ams/ws/application/terminateaccount">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="accountId" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Account identification in OBS</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitTransferToAccountRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitTransferToAccountResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="targetAccount" type="appCommon:BankAccountNumberTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Account which is terminated.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="terminatedAccountType" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Type of terminated account, one of [CURRENT/SAVINGS/PROTECTED].</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="lastActiveAccountWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account is a last active account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="primaryAccountWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account is a primary account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountHasInsuranceWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account has insurance on it.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountHasPipInsuranceWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account has PIP insurance on it.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountHasDebtWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account has debt on it.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountHasLoanWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account has loan on it.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountHasMortgageWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates the terminated account has mortgage on it.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="clientHasOrganizerWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that client has activated organizer.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="clientHasInvestmentWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that client has active investment contract.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="clientHasStockInvestmentWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that client has active stock investment contract.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="registredAccountPayment2ContactWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that is registred in payment 2 contract.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountHasTermDepositWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that account is connected to term deposit.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="overdraftInfo" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                                <xs:annotation>
                                    <xs:documentation>Information about overdraft to terminated account</xs:documentation>
                                </xs:annotation>
                                <xs:sequence>
                                    <xs:element name="utilized" type="xs:boolean" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>True if this overdraft is utilized and cannot be terminated.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="amountToRepay" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Current amount to repay.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateTransferToAccountRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <!--
                                                <xs:element name="accountId" type="xs:long" minOccurs="1" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>Account identification in OBS</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                        -->
                        <xs:element name="targetAccount" type="appCommon:BankAccountNumberTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Target account</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="warningsDisplayed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that one or more warnings have been displayed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="warningsConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates all warnings (if any) have been confirmed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="emptyTransferAccountWarningExists" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that empty transfer account warning should be displayed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="emptyTransferAccountWarningConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag that indicates that empty transfer account warning has been confirmed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="conversionTransferWarning" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>indicates foreign account cancellation warning is confirmed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateTransferToAccountResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>