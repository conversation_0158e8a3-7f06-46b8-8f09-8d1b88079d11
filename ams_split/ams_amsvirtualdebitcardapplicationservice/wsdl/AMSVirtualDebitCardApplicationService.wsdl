<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/ams/ws/application/virtualdebitcard"
                  targetNamespace="http://airbank.cz/ams/ws/application/virtualdebitcard">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/virtualdebitcard">
            <xs:include schemaLocation="AMSVirtualDebitCardApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- VirtualDebitCard task -->
    <wsdl:message name="InitVirtualDebitCardRequest">
        <wsdl:part element="InitVirtualDebitCardRequest" name="InitVirtualDebitCardRequest"/>
    </wsdl:message>
    <wsdl:message name="InitVirtualDebitCardResponse">
        <wsdl:part element="InitVirtualDebitCardResponse" name="InitVirtualDebitCardResponse"/>
    </wsdl:message>
    <wsdl:message name="InitVirtualDebitCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitVirtualDebitCardFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateVirtualDebitCardRequest">
        <wsdl:part element="UpdateVirtualDebitCardRequest" name="UpdateVirtualDebitCardRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateVirtualDebitCardResponse">
        <wsdl:part element="UpdateVirtualDebitCardResponse" name="UpdateVirtualDebitCardResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateVirtualDebitCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateVirtualDebitCardFault"/>
    </wsdl:message>

    <!-- VirtualDebitCardSummary task -->
    <wsdl:message name="InitVirtualDebitCardSummaryRequest">
        <wsdl:part element="InitVirtualDebitCardSummaryRequest" name="InitVirtualDebitCardSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitVirtualDebitCardSummaryResponse">
        <wsdl:part element="InitVirtualDebitCardSummaryResponse" name="InitVirtualDebitCardSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitVirtualDebitCardSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitVirtualDebitCardSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateVirtualDebitCardSummaryRequest">
        <wsdl:part element="UpdateVirtualDebitCardSummaryRequest" name="UpdateVirtualDebitCardSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateVirtualDebitCardSummaryResponse">
        <wsdl:part element="UpdateVirtualDebitCardSummaryResponse" name="UpdateVirtualDebitCardSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateVirtualDebitCardSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateVirtualDebitCardSummaryFault"/>
    </wsdl:message>

    <!-- Application cancel request -->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="VirtualDebitCardApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>
                Starts a new application for virtual debit card of an existing customer.
                Uses cuid and idProfile from tracking context to identify customer and profile.

                Generated business faults:
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous debit card application in the VERIFY status
                - Application.VirtualCard.BlockedCardFound - There is a debit card that is actually blocked
                - Application.VirtualCard.ActiveVirtualCardFound - There is a virtual card that is actually active
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitVirtualDebitCard">
            <wsdl:documentation>Operation used to initialize the virtualDebitCardTask.</wsdl:documentation>
            <wsdl:input message="InitVirtualDebitCardRequest"/>
            <wsdl:output message="InitVirtualDebitCardResponse"/>
            <wsdl:fault name="InitVirtualDebitCardFault" message="InitVirtualDebitCardFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateVirtualDebitCard">
            <wsdl:documentation>
                Operation used to update data for the virtualDebitCardTask. It uses to select account for new virtual debit card.

                For all transitions except for back, following attributes are required:
                - selectedAccount
            </wsdl:documentation>
            <wsdl:input message="UpdateVirtualDebitCardRequest"/>
            <wsdl:output message="UpdateVirtualDebitCardResponse"/>
            <wsdl:fault name="UpdateVirtualDebitCardFault" message="UpdateVirtualDebitCardFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitVirtualDebitCardSummary">
            <wsdl:documentation>Operation used to initialize the virtualDebitCardSummaryTask.</wsdl:documentation>
            <wsdl:input message="InitVirtualDebitCardSummaryRequest"/>
            <wsdl:output message="InitVirtualDebitCardSummaryResponse"/>
            <wsdl:fault name="InitVirtualDebitCardSummaryFault" message="InitVirtualDebitCardSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateVirtualDebitCardSummary">
            <wsdl:documentation>
                Operation used to update data for the virtualDebitCardSummaryTask.
                It uses to summarize information about the new virtual debit card.
            </wsdl:documentation>
            <wsdl:input message="UpdateVirtualDebitCardSummaryRequest"/>
            <wsdl:output message="UpdateVirtualDebitCardSummaryResponse"/>
            <wsdl:fault name="UpdateVirtualDebitCardSummaryFault" message="UpdateVirtualDebitCardSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="VirtualDebitCardApplicationBinding" type="VirtualDebitCardApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitVirtualDebitCard">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitVirtualDebitCardFault">
                <soap:fault name="InitVirtualDebitCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateVirtualDebitCard">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateVirtualDebitCardFault">
                <soap:fault name="UpdateVirtualDebitCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitVirtualDebitCardSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitVirtualDebitCardSummaryFault">
                <soap:fault name="InitVirtualDebitCardSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateVirtualDebitCardSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateVirtualDebitCardSummaryFault">
                <soap:fault name="UpdateVirtualDebitCardSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="VirtualDebitCardApplicationService">
        <wsdl:documentation>
            Service providing operations related to virtual debit card application of an existing customer.

            The application has the following task IDs:
            - virtualDebitCardTask - applicant selects account for his new virtual debit card
            - virtualDebitCardSummaryTask - summary information about new virtual debit card
            - generateDocumentationTask - application was approved, documentation could be generated
            - waitForWarningResultTask - AMS is waiting for the warning processing

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="VirtualDebitCardApplicationPort" binding="VirtualDebitCardApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/virtualdebitcard"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>