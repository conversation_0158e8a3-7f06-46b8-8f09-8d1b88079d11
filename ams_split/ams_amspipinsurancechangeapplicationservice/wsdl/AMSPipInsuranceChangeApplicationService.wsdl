<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/insurance/pipchange"
                  targetNamespace="http://airbank.cz/ams/ws/application/insurance/pipchange">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/insurance/pipchange">
            <xs:include schemaLocation="AMSPipInsuranceChangeApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Settings task -->
    <wsdl:message name="InitSettingsRequest">
        <wsdl:part element="InitSettingsRequest" name="InitSettingsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSettingsResponse">
        <wsdl:part element="InitSettingsResponse" name="InitSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSettingsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSettingsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSettingsRequest">
        <wsdl:part element="UpdateSettingsRequest" name="UpdateSettingsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSettingsResponse">
        <wsdl:part element="UpdateSettingsResponse" name="UpdateSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSettingsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSettingsFault"/>
    </wsdl:message>

    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <wsdl:portType name="PipInsuranceApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for PIP. Uses cuid and idProfile from tracking context to identify customer and profile.</wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>
        <wsdl:operation name="InitSettings">
            <wsdl:documentation>Operation used to initialize the settingsTask.</wsdl:documentation>
            <wsdl:input message="InitSettingsRequest"/>
            <wsdl:output message="InitSettingsResponse"/>
            <wsdl:fault name="InitSettingsFault" message="InitSettingsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSettings">
            <wsdl:documentation>Operation used to update data for the settingsTask.</wsdl:documentation>
            <wsdl:input message="UpdateSettingsRequest"/>
            <wsdl:output message="UpdateSettingsResponse"/>
            <wsdl:fault name="UpdateSettingsFault" message="UpdateSettingsFault"/>
        </wsdl:operation>
        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="PipInsuranceApplicationBinding" type="PipInsuranceApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="InitSettings">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSettingsFault">
                <soap:fault name="InitSettingsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateSettings">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSettingsFault">
                <soap:fault name="UpdateSettingsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="PipInsuranceApplicationService">
        <wsdl:port name="PipInsuranceApplicationPort" binding="PipInsuranceApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/pipinsurance"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>