<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/applicationManagement"
           targetNamespace="http://airbank.cz/ams/ws/application/applicationManagement">

    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>


    <xs:element name="CancelApplicationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id (in the AMS DB) of the application to cancel.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="completionId" type="xs:long" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of the contract of the application to cancel.</xs:documentation>
                    </xs:annotation>
                </xs:element>

                <xs:element name="cancelReason" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Reason for cancelling the application. There are no constraints, because this is generated internally
                            in Blaze.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cancelReasonClient" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Reason for cancelling the application as communicated to client. There are no constraints, because
                            this is generated internally in Blaze.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelApplicationResponse">
        <xs:complexType>

        </xs:complexType>
    </xs:element>

    <xs:element name="GetModesForProductsRequest">
        <xs:complexType/>
    </xs:element>

    <xs:element name="GetModesForProductsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="productTypeMode" type="appCommon:ProductTypeModeItemTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Product type and its corresponding mode .</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ContractBeingClosedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="generalContractCompletionId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>GC completion ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="closeReason" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Close/cancel reason for AMS.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="generalContractType" type="appCommon:GeneralContractType">
                    <xs:annotation>
                        <xs:documentation>General contract type</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="ownerCuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Owner CUID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ContractBeingClosedResponse">
        <xs:complexType/>
    </xs:element>

</xs:schema>