<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/applicationManagement"
                  targetNamespace="http://airbank.cz/ams/ws/application/applicationManagement">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/applicationManagement">
            <xs:include schemaLocation="AMSApplicationManagementService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="CancelApplicationRequest">
        <wsdl:part element="CancelApplicationRequest" name="CancelApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelApplicationResponse">
        <wsdl:part element="CancelApplicationResponse" name="CancelApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="GetModesForProductsRequest">
        <wsdl:part element="GetModesForProductsRequest" name="GetModesForProductsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetModesForProductsResponse">
        <wsdl:part element="GetModesForProductsResponse" name="GetModesForProductsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetModesForProductsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetModesForProductsFault"/>
    </wsdl:message>

    <wsdl:message name="ContractBeingClosedRequest">
        <wsdl:part element="ContractBeingClosedRequest" name="ContractBeingClosedRequest"/>
    </wsdl:message>
    <wsdl:message name="ContractBeingClosedResponse">
        <wsdl:part element="ContractBeingClosedResponse" name="ContractBeingClosedResponse"/>
    </wsdl:message>
    <wsdl:message name="ContractBeingClosedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ContractBeingClosedsFault"/>
    </wsdl:message>

    <wsdl:portType name="ApplicationManagement">
        <wsdl:operation name="CancelApplication">
            <wsdl:documentation>
                Cancels application identified by id or contract id.

                Generated business faults:
                - Application.Management.ExactlyOneIdIsNeeded - Exactly one of the id attributes must be filled
                - Application.NotFound - Application has not been found based on the id provided
            </wsdl:documentation>
            <wsdl:input message="CancelApplicationRequest"/>
            <wsdl:output message="CancelApplicationResponse"/>
            <wsdl:fault name="CancelApplicationFault" message="CancelApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetModesForProducts">
            <wsdl:documentation>
                Returns list of products and it's modes (PRODUCTION, PILOT, NONE).
            </wsdl:documentation>
            <wsdl:input message="GetModesForProductsRequest"/>
            <wsdl:output message="GetModesForProductsResponse"/>
            <wsdl:fault name="GetModesForProductsFault" message="GetModesForProductsFault"/>
        </wsdl:operation>

        <wsdl:operation name="ContractBeingClosed">
            <wsdl:documentation>
                Returns list of products and it's modes (PRODUCTION, PILOT, NONE).
            </wsdl:documentation>
            <wsdl:input message="ContractBeingClosedRequest"/>
            <wsdl:output message="ContractBeingClosedResponse"/>
            <wsdl:fault name="ContractBeingClosedFault" message="ContractBeingClosedFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="ApplicationManagementBinding" type="ApplicationManagement">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="CancelApplication">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelApplicationFault">
                <soap:fault name="CancelApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="GetModesForProducts">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetModesForProductsFault">
                <soap:fault name="GetModesForProductsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="ContractBeingClosed">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ContractBeingClosedFault">
                <soap:fault name="ContractBeingClosedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ApplicationManagementService">
        <wsdl:documentation>
            Offers common operations over applications.
        </wsdl:documentation>
        <wsdl:port name="ApplicationManagementPort" binding="ApplicationManagementBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/applicationManagement"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>