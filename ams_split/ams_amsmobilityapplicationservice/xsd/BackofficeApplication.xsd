<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
        xmlns:Q1="http://airbank.cz/ams/ws/backoffice/common"
        xmlns="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://airbank.cz/ams/ws/backoffice/common"
        elementFormDefault="qualified" version="1.0">


    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>


    <!--

    Searching applications

     -->

    <complexType name="FindApplicationsResultTO">
        <annotation>
            <documentation>Some selected data from application, used to show list of found applications.</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>PK in database</documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long"/>
            <element name="alternativeId" type="long" minOccurs="0"/>
            <element name="deleted" type="boolean"/>
            <element name="type" type="appCommon:ApplicationTypeTO"/>
            <element name="status" type="Q1:ApplicationStatusTO"/>

            <element name="applicationCreatedDate" type="dateTime"/>
            <element name="applicationCreatedChannel" type="Q1:ChannelTypeTO"/>
            <element name="applicationFinishedDate" type="dateTime" minOccurs="0"/>
            <element name="applicationFinishedChannel" type="Q1:ChannelTypeTO" minOccurs="0"/>
            <element name="applicationSentToScoringDate" type="dateTime" minOccurs="0"/>
            <element name="applicationSentToScoringChannel" type="Q1:ChannelTypeTO" minOccurs="0"/>

            <element name="cuid" type="long" minOccurs="0"/>
            <element name="firstName" type="string" minOccurs="0"/>
            <element name="lastName" type="string" minOccurs="0"/>
            <element name="birthNumber" type="string" minOccurs="0"/>
            <element name="citizenships" type="string" minOccurs="0" maxOccurs="unbounded"/>
            <element name="gender" type="string" minOccurs="0"/>
            <element name="businessOwner" type="string" minOccurs="0"/>
            <element name="modifOperator" type="string" minOccurs="0"/>
            <element name="applicationCreatedBranchId" type="string" minOccurs="0"/>
            <element name="uwOwner" type="string" minOccurs="0"/>
            <element name="collateralUwOwner" type="string" minOccurs="0"/>
            <element name="birthDate" type="date" minOccurs="0"/>
            <element name="signOperator" type="string" minOccurs="0"/>
            <element name="signedInBranch" type="string" minOccurs="0"/>

            <element name="loanAmount" type="decimal" minOccurs="0"/>
            <element name="utilizationDate" type="date" minOccurs="0"/>
            <element name="requestedUtilizationDate" type="date" minOccurs="0"/>
            <element name="withMortgage" type="boolean" minOccurs="0"/>
            <element name="applicationStage" type="string" minOccurs="1" maxOccurs="1"/>

            <element name="taskType" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="taskStatus" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="economicalStatusMain" type="int" minOccurs="0" maxOccurs="1"/>
            <element name="economicalStatusOther" type="int" minOccurs="0" maxOccurs="1"/>
            <element name="enterIntoUwDate" type="date" minOccurs="0" maxOccurs="1"/>
            <element name="enterIntoCompletionDate" type="dateTime" minOccurs="0" maxOccurs="1"/>
            <element name="applicationSignedChannel" type="Q1:DistributionChannelTypeTo"/>
            <element name="expirationDate" type="dateTime" minOccurs="0" maxOccurs="1"/>
            <element name="taskLockedForOperator" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="activeMostPriorEvent" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="actualQueue" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="taskDelayExpirationDate" type="dateTime" minOccurs="0" maxOccurs="1"/>
            <element name="postponedTaskReasonCode" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="postponedTaskNote" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="rejectReasonClient" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="completionStatus" type="Q1:CompletionScopeStatusTo" minOccurs="0" maxOccurs="1"/>
            <element name="rejectReason" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="numberOfRequiredDocuments" type="int" minOccurs="0" maxOccurs="1"/>
            <element name="numberOfDeliveredDocuments" type="int" minOccurs="0" maxOccurs="1"/>
            <element name="hardChecks" type="Q1:HardCheckTO" minOccurs="0" maxOccurs="unbounded"/>
            <element name="isLoanCommitmentApproved" type="boolean" minOccurs="0" maxOccurs="1"/>
            <element name="signDate" type="dateTime" minOccurs="0"/>
            <element name="loanAmountPreScoring" type="decimal" minOccurs="0" />
            <element name="identificationNumber" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="taxIdentificationNumber" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="businessName" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="businessStartDate" type="date" minOccurs="0" maxOccurs="1"/>
            <element name="legalForm" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>
            <element name="legalSegment" type="appCommon:LegalSegmentTO" minOccurs="1" maxOccurs="1"/>
        </sequence>
    </complexType>

    <complexType name="FindApplicationsFilterTO">
        <annotation>
            <documentation>Filter for application search.</documentation>
        </annotation>
        <sequence>
            <element name="offset" type="int" minOccurs="0"/>
            <element name="itemCount" type="int" minOccurs="0"/>
            <element name="sortByList" type="Q1:SortByTypeTO" minOccurs="0" maxOccurs="unbounded"/>
            <element name="maxIdCount" type="int" minOccurs="0"/>
            <element name="cuid" type="long" minOccurs="0"/>
            <element name="applicantAmsId" type="long" minOccurs="0"/>
            <element name="envelopeId" type="long" minOccurs="0"/>
            <element name="applicationId" type="long" minOccurs="0"/>
            <element name="applicationType" type="appCommon:ApplicationTypeTO" minOccurs="0" maxOccurs="unbounded"/>
            <element name="firstName" type="string" minOccurs="0"/>
            <element name="lastName" type="string" minOccurs="0"/>
            <element name="birthDate" type="date" minOccurs="0"/>
            <element name="birthNumber" type="string" minOccurs="0"/>
            <element name="primaryPhone" type="string" minOccurs="0"/>
            <element name="primaryEmail" type="string" minOccurs="0"/>
            <element name="gcId" type="long" minOccurs="0"/>
            <element name="citizenship" type="string" minOccurs="0"/>
            <element name="gender" type="string" minOccurs="0"/>
            <element name="businessOwner" type="string" minOccurs="0"/>
            <element name="modifOperator" type="string" minOccurs="0"/>
            <element name="applicationCreatedBranchId" type="string" minOccurs="0"/>
            <element name="uwOwner" type="string" minOccurs="0"/>
            <element name="collateralUwOwner" type="string" minOccurs="0"/>
            <element name="uwOwnerOrCollateralUwOwner" type="string" minOccurs="0"/>
            <element name="onlyLeads" type="boolean"/>
            <element name="atLeastOneCommitmentWithNoQuantification" type="boolean"/>
            <element name="atLeastOneCommitmentWithQuantification" type="boolean"/>
            <element name="reportType" type="string" minOccurs="0"/>
            <element name="onlyLive" type="boolean"/>
            <element name="onlyInProcess" type="boolean"/>
            <element name="mortgageStages" type="string" minOccurs="0" maxOccurs="unbounded"/>
            <element name="withoutActiveTaskTypes" type="boolean"/>
            <element name="applicationCreatedDate" type="date" minOccurs="0"/>
            <element name="applicationSentToScoringDate" type="date" minOccurs="0"/>
            <element name="applicationStatuses" type="Q1:ApplicationStatusTO" minOccurs="0" maxOccurs="unbounded"/>
            <element name="actualQueues" type="Q1:QueueTypeTO" minOccurs="0" maxOccurs="unbounded"/>
            <element name="activeTaskTypes" type="string" minOccurs="0" maxOccurs="unbounded"/>
            <element name="taskStatus" type="string" minOccurs="0"/>
            <element name="actualEvent" type="string" minOccurs="0"/>
            <element name="taskLockedForOperator" type="string" minOccurs="0"/>
            <element name="taskLockedForOperatorAlsoHistorical" type="boolean" minOccurs="1"/>
            <element name="applicationSignedDate" type="date" minOccurs="0"/>
            <element name="applicationSignedChannel" type="Q1:DistributionChannelTypeTo" minOccurs="0"/>
            <element name="applicationFinishedDate" type="date" minOccurs="0"/>
            <element name="applicationFinishedChannel" type="Q1:ChannelTypeTO" minOccurs="0"/>
            <element name="applicationCreatedChannels" type="Q1:ChannelTypeTO" minOccurs="0" maxOccurs="unbounded"/>
            <element name="enterIntoUwDate" type="date" minOccurs="0"/>
            <element name="envelopeExpirationDaysCount" type="int" minOccurs="0" maxOccurs="1"/>
            <element name="completionExpirationDaysCount" type="int" minOccurs="0" maxOccurs="1"/>
            <element name="signChannelModified" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="filterByQueue" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="signed" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="completionTypes" type="Q1:RestrictedContractType" minOccurs="0" maxOccurs="unbounded"/>
            <element name="completionScopeStatuses" type="Q1:CompletionScopeStatusTo" minOccurs="0" maxOccurs="unbounded"/>
            <element name="existsBranchCompletion" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="existsDeliveredDocumentInNonFinalState" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="notExistsCheckDocumentsBranchTask" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="notExistsUnderWritingTask" type="boolean" minOccurs="0" maxOccurs="1" />
            <element name="signedInBranch" type="string" minOccurs="0"/>
            <element name="isFastTrack" type="boolean" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>If true, filter returns applications, that are fasttrack (internalCode=5), if false, filer returns all applications, that are not in fastrack (internalCode != 5). Null means ignore this filter.</documentation>
                </annotation>
            </element>
            <element name="includeKOCodes" type="boolean" minOccurs="0" maxOccurs="1"/>
            <element name="applicationSentToScoring" type="boolean" minOccurs="0" maxOccurs="1"/>
            <element name="identificationNumber" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="taxIdentificationNumber" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="businessName" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="businessStartDate" type="date" minOccurs="0" maxOccurs="1"/>
            <element name="legalForm" type="string" minOccurs="0" maxOccurs="1"/>
            <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>
            <element name="legalSegment" type="appCommon:LegalSegmentTO" minOccurs="0" maxOccurs="1"/>
        </sequence>
    </complexType>

    <simpleType name="RestrictedContractType">
        <annotation>
            <documentation>Minimum list of possible contract types to limit applications' filter overview</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CREATE_LOAN"/>
            <enumeration value="CREATE_BU_LOAN"/>
            <enumeration value="CREATE_REFINANCING"/>
        </restriction>
    </simpleType>

    <complexType name="SortByTypeTO">
        <annotation>
            <documentation>Sort by enumeration type when searching application results.</documentation>
        </annotation>
        <sequence>
            <element name="sortType" type="Q1:ApplicationSortTypeTO" minOccurs="0"/>
            <element name="order" type="Q1:SortOrderTO" minOccurs="0"/>
        </sequence>
    </complexType>

    <complexType name="SortByTO">
        <annotation>
            <documentation>Sort by string when searching results.</documentation>
        </annotation>
        <sequence>
            <element name="sortType" type="string" minOccurs="0"/>
            <element name="order" type="Q1:SortOrderTO" minOccurs="0"/>
        </sequence>
    </complexType>

    <simpleType name="SortOrderTO">
        <annotation>
            <documentation>Order of sorting when searching results.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ASC"/>
            <enumeration value="DESC"/>
        </restriction>
    </simpleType>

    <simpleType name="ApplicationSortTypeTO">
        <annotation>
            <documentation>Column of sorting when searching results.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ID"/>
            <enumeration value="ENVELOPE_ID"/>
            <enumeration value="ACTUAL_TASK_TYPE"/>
            <enumeration value="ACTUAL_TASK_STATUS"/>
            <enumeration value="INCOME_MAIN_AND_ADDITIONAL"/>
            <enumeration value="TYPE"/>
            <enumeration value="STATUS"/>
            <enumeration value="APPLICATION_CREATED_DATE"/>
            <enumeration value="APPLICATION_FINISHED_DATE"/>
            <enumeration value="APPLICATION_SENT_TO_SCORING_DATE"/>
            <enumeration value="NAME"/>
            <enumeration value="CUID"/>
            <enumeration value="BIRTH_NUMBER"/>
            <enumeration value="BIRTH_DATE"/>
            <enumeration value="CITIZENSHIP">
                <annotation>
                    <documentation>Not used.</documentation>
                </annotation>
            </enumeration>
            <enumeration value="GENDER"/>
            <enumeration value="ALTERNATIVE_ID"/>
            <enumeration value="LOAN_AMOUNT"/>
            <enumeration value="BUSINESS_OWNER"/>
            <enumeration value="WITH_MORTGAGE"/>
            <enumeration value="UTILIZATION_DATE"/>
            <enumeration value="REQUESTED_UTILIZATION_DATE"/>
            <enumeration value="ENTER_INTO_UW_DATE"/>
            <enumeration value="ENTER_INTO_COMPLETION_DATE"/>
            <enumeration value="APPLICATION_SIGNED_CHANNEL"/>
            <enumeration value="EXPIRATION_DATE"/>
            <enumeration value="TASK_LOCKED_FOR_USER"/>
            <enumeration value="ACTIVE_MOST_PRIOR_EVENT"/>
            <enumeration value="ACTUAL_QUEUE"/>
            <enumeration value="TASK_DELAY_EXPIRATION_DATE"/>
        </restriction>
    </simpleType>

    <complexType name="TransferObjectWithIdentity" abstract="true">
        <annotation>
            <documentation>Parent of all objects with DB ID.</documentation>
        </annotation>
        <sequence>
            <element name="id" type="long" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>PK in database, can be NULL (new unsaved object)</documentation>
                </annotation>
            </element>
            <element name="createdTime" type="dateTime" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Time of the object creation in DB</documentation>
                </annotation>
            </element>
            <element name="version" type="long" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Version of the object in DB</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="AuditedTransferObjectWithIdentity" abstract="true">
        <annotation>
            <documentation>Parent object with audit data..</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="modifyOperatorId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Cuid of operator who modify this record.</documentation>
                        </annotation>
                    </element>
                    <element name="modifyManagerId" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Id of operator who modify this record.</documentation>
                        </annotation>
                    </element>
                    <element name="modifyTime" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Time of the record update.</documentation>
                        </annotation>
                    </element>
                    <element name="deleted" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Flag indicating whether is the record deleted or not.</documentation>
                        </annotation>
                    </element>
                    <element name="auditData" type="Q1:AuditDataTO">
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <xs:complexType name="StandardPhoneTo" abstract="true">
        <xs:annotation>
            <xs:documentation>Standard phone number</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="callingCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Calling code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="nationalNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>National phone number</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FilterPagination" abstract="true">
        <xs:annotation>
            <xs:documentation>Parent object with data for pagination</xs:documentation>
        </xs:annotation>
        <sequence>
            <xs:element name="itemCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Paging: requested count of full records</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="offset" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Paging: requested number of record in ordered records</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sortByList" type="Q1:SortByTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of sort by attributes</xs:documentation>
                </xs:annotation>
            </xs:element>
        </sequence>
    </xs:complexType>

    <xs:complexType name="FilterDeliveredDocument" abstract="true">
        <xs:annotation>
            <xs:documentation>Parent object with data for pagination and specific sorting attributes</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <extension base="Q1:FilterPagination">
                <sequence>
                </sequence>
            </extension>
        </xs:complexContent>

    </xs:complexType>

    <!--

    Application detail

     -->

    <complexType name="ApplicationBaseTO">
        <annotation>
            <documentation>Application detail with attributes for AMS GUI</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="type" type="appCommon:ApplicationTypeTO" minOccurs="0">
                        <annotation>
                            <documentation>
                                Application type.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="status" type="Q1:ApplicationStatusTO">
                        <annotation>
                            <documentation>
                                Application status.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="approveStatus" type="Q1:ApproveStatusTO" minOccurs="0">
                        <annotation>
                            <documentation>
                                LAP approval status type.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="distributionChannelType" type="Q1:DistributionChannelTypeTo">
                        <annotation>
                            <documentation>
                                Application distribution channel
                            </documentation>
                        </annotation>
                    </element>
                    <element name="distributionPlace" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                delivery place - optional - name of branch if distributionChannelType is BRANCH
                            </documentation>
                        </annotation>
                    </element>
                    <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ApplicationSimpleTO">
        <annotation>
            <documentation>Application detail with attributes for AMS GUI</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationBaseTO">
                <sequence>
                    <element name="generalContractCompletionId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                ID of general contract generated by OBS. Set during application finalization.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="completionId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                contract completion id in OBS
                            </documentation>
                        </annotation>
                    </element>
                    <element name="rejectReason" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Internal code of reject reason.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="rejectReasonClient" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Code of reject reason for customer.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="applicantId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                Applicant ID.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="envelopeId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                ID of application envelope.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="addressType" type="Q1:AddressRoleTO">
                        <annotation>
                            <documentation>
                                Role of customer/employment address
                            </documentation>
                        </annotation>
                    </element>
                    <element name="recognisedIncomeCalculationExtIds" type="xs:long" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>
                                Recognised Income Calculation ID
                            </documentation>
                        </annotation>
                    </element>
                    <element name="expirationDate" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Expiration date of application
                            </documentation>
                        </annotation>
                    </element>
                    <element name="applicationCreatedDate" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Create date of application
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ApplicationTO">
        <annotation>
            <documentation>Application detail with attributes for AMS GUI</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationSimpleTO">
                <sequence>
                    <element name="applicant" type="Q1:ApplicantTO" minOccurs="0">
                        <annotation>
                            <documentation>
                                Applicant data.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="applicantEntrepreneur" type="Q1:EntrepreneurApplicantTO" minOccurs="0">
                        <annotation>
                            <documentation>
                                Entrepreneur Applicant data.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ApplicantTO">
        <annotation>
            <documentation>Applicant data</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="username" type="string"/>
                    <element name="birthNumber" type="string"/>
                    <element name="firstName" type="string"/>
                    <element name="lastName" type="string"/>
                    <element name="salutation" type="string" minOccurs="0"/>
                    <element name="honourBefore" type="string" minOccurs="0"/>
                    <element name="honourAfter" type="string" minOccurs="0"/>
                    <element name="gender" type="string"/>
                    <element name="citizenships" type="string" maxOccurs="unbounded"/>
                    <element name="birthDate" type="date"/>
                    <element name="birthPlace" type="Q1:BirthPlaceTO"/>
                    <element name="relatedParty" type="boolean" minOccurs="0"/>
                    <element name="politicallyExposed" type="boolean" minOccurs="0"/>
                    <element name="cuid" type="long" minOccurs="0"/>

                    <element name="anonymizationStatus" type="Q1:AnonymizationStatusTO" minOccurs="0"/>
                    <element name="anonymizationDate" type="date" minOccurs="0"/>
                    <element name="amlStatus" type="Q1:AmlStatusTO" minOccurs="0"/>
                    <element name="manualAmlCheck" type="boolean" minOccurs="0"/>

                    <element name="personalData" type="Q1:PersonalDataTO"/>
                    <element name="financialData" type="Q1:FinancialDataTO"/>
                    <element name="householdIncomeExpenseData" type="Q1:HouseholdIncomeExpenseDataTO"/>
                    <element name="undesirability" type="Q1:UndesirabilityTO" minOccurs="0" maxOccurs="1"/>
                    <element name="addresses" type="Q1:AddressTO" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="contacts" type="Q1:ContactTO" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="employments" type="Q1:EmploymentTO" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="relationToMainApplicant" type="string" minOccurs="0"/>
                    <element name="embossedName" type="string" minOccurs="0"/>
                    <element name="documentNumber" type="string" minOccurs="0"/>
                    <element name="documentType" type="string" minOccurs="0"/>
                    <xs:element name="applicationDataRequiredType" type="appCommon:ApplicationDataRequiredTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of application data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <element name="documentValidTo" type="date" minOccurs="0"/>
                    <element name="identificationResult" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Result of customer identification in CIF.</documentation>
                        </annotation>
                    </element>
                    <element name="relationToBankExists" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Flag indicating whether customer relation to bank was created for customer who had no relation before.</documentation>
                        </annotation>
                    </element>
                    <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>
                    <element name="applicationDataClientAccepted" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Indicator that applicant confirmed new structure of application data.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="EntrepreneurApplicantTO">
        <annotation>
            <documentation>Entrepreneur Applicant data</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicantTO">
                <sequence>
                    <element name="identificationNumber" type="string"/>
                    <element name="taxIdentificationNumber" type="string" minOccurs="0"/>
                    <element name="businessName" type="string"/>
                    <element name="businessStartDate" type="date"/>
                    <element name="businessEndDate" type="date" minOccurs="0"/>
                    <element name="legalForm" type="string" minOccurs="0"/>
                    <element name="registrationPlace" type="string" minOccurs="0"/>
                    <element name="registrationFileNumber" type="string" minOccurs="0"/>
                    <element name="dunsNumber" type="string" minOccurs="0"/>
                    <element name="leiCode" type="string" minOccurs="0"/>
                    <element name="esaCode" type="string" minOccurs="0"/>
                    <element name="primaryNace" type="string" minOccurs="0"/>
                    <element name="secondaryNaces" type="string" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="EventTO">
        <annotation>
            <documentation>Event on application, envelope, ...</documentation>
        </annotation>
        <sequence>
            <element name="time" type="dateTime" minOccurs="0"/>
            <element name="code" type="string" />
            <element name="referenceId" type="long" />
            <element name="referenceType" type="string" />
            <element name="operator" type="string" />
            <element name="system" type="string" />
            <element name="description" type="string" />
        </sequence>
    </complexType>

    <complexType name="AddressTO">
        <annotation>
            <documentation>Data about applicant or employment address</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="countryAlpha2Code" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Two-letter code of country.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="town" type="string" minOccurs="0"/>
                    <element name="streetOrLocality" type="string" minOccurs="0"/>
                    <element name="house" type="string" minOccurs="0"/>
                    <element name="zip" type="string" minOccurs="0"/>

                    <element name="role" type="Q1:AddressRoleTO"/>
                    <element name="residenceFrom" type="dateTime" minOccurs="0"/>
                    <element name="confirmed" type="boolean" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AddressHistoryTO">
        <annotation>
            <documentation>Historical data about applicant or employment address</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="countryAlpha2Code" type="string">
                        <annotation>
                            <documentation>
                                Two-letter code of country.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="town" type="string" minOccurs="0"/>
                    <element name="streetOrLocality" type="string" minOccurs="0"/>
                    <element name="houseNum" type="string" minOccurs="0"/>
                    <element name="zip" type="string" minOccurs="0"/>

                    <element name="role" type="Q1:AddressRoleTO"/>
                    <element name="residenceFrom" type="dateTime" minOccurs="0"/>
                    <element name="confirmed" type="boolean" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>


    <complexType name="ContactTO">
        <annotation>
            <documentation>Contact or email information</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="role" type="Q1:ContactRoleTO"/>
                    <element name="value" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Phone number without prefix or email address
                            </documentation>
                        </annotation>
                    </element>
                    <element name="callingCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Filled when this is a phone contact.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ContactHistoryTO">
        <annotation>
            <documentation>Historical data - Contact or email information</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="role" type="Q1:ContactRoleTO"/>
                    <element name="value" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Phone number without prefix or email address
                            </documentation>
                        </annotation>
                    </element>
                    <element name="callingCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                Filled when this is a phone contact.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>


    <complexType name="EmploymentTO">
        <annotation>
            <documentation>Information about customer employment</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="mainEmployment" type="boolean"/>
                    <element name="acceptableIncome" type="decimal" minOccurs="0"/>
                    <element name="netIncome" type="decimal" minOccurs="0"/>
                    <element name="taxBaseIncome" type="decimal" minOccurs="0"/>
                    <element name="incomeUnknown" type="boolean" minOccurs="0"/>
                    <element name="noTaxReturn" type="boolean" minOccurs="0"/>
                    <element name="economicalStatus" type="int" minOccurs="0"/>
                    <element name="employmentType" type="int" minOccurs="0"/>
                    <element name="employedFrom" type="date" minOccurs="0"/>
                    <xs:element name="incomeTimeType" type="appCommon:IncomeTimeTypeTO" minOccurs="0"/>
                    <element name="agreementUntil" type="date" minOccurs="0"/>
                    <element name="industry" type="string" minOccurs="0"/>
                    <element name="profession" type="string" minOccurs="0"/>
                    <element name="name" type="string" minOccurs="0"/>
                    <element name="idNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                ICO
                            </documentation>
                        </annotation>
                    </element>
                    <element name="taxNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                DIC
                            </documentation>
                        </annotation>
                    </element>
                    <element name="phoneUnknown" type="boolean" minOccurs="0"/>
                    <element name="addresses" type="Q1:AddressTO" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="contacts" type="Q1:ContactTO" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="UndesirabilityTO">
        <annotation>
            <documentation>Data about applicant undesirability</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="undesirableFrom" type="dateTime"/>
                    <element name="undesirableTo" type="dateTime" minOccurs="0"/>
                    <element name="insertReason" type="string" minOccurs="0"/>
                    <element name="note" type="string" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="PersonalDataTO">
        <annotation>
            <documentation>Data about applicant undesirability</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="maritalStatus" type="int" minOccurs="0"/>
                    <element name="married" type="xs:boolean" minOccurs="0"/>
                    <element name="residenceType" type="int" minOccurs="0"/>
                    <element name="residenceTo" type="dateTime" minOccurs="0"/>
                    <element name="education" type="int" minOccurs="0"/>
                    <element name="housingType" type="int" minOccurs="0"/>
                    <element name="dependentPersons" type="int" minOccurs="0"/>
                    <element name="secondIdentificationDoc" type="string" minOccurs="0"/>
                    <element name="householdMemberType" type="xs:string" minOccurs="0">
                        <annotation>
                            <documentation>Applicant's household member type</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="HouseholdIncomeExpenseDataTO">
        <annotation>
            <documentation>Customer incomes and expenses.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="otherHouseholdMembersIncome" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="medicalFoodTransportationExpense" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="accommodationExpense" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="otherHouseholdMembersInstallmentsExpense" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="dynamicExpense1" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="dynamicExpense2" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="dynamicExpense3" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                    <element name="dynamicExpense4" type="Q1:IncomeExpenseTO" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="IncomeExpenseTO">
        <annotation>
            <documentation>Expense or Income data holder with label and amount.</documentation>
        </annotation>
        <sequence>
            <element name="amount" type="decimal"/>
            <element name="label" type="xs:string"/>
        </sequence>
    </complexType>

    <complexType name="FinancialDataTO">
        <annotation>
            <documentation>Customer financial data</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="loanPaymentsSum" type="decimal"/>
                    <element name="creditsSum" type="decimal"/>
                    <element name="correctLoanPaymentsWithCreditsSum" type="decimal"/>
                    <element name="loanPaymentSumAllMembers" type="xs:decimal">
                        <annotation>
                            <documentation>Monthly household installments expenses (loans and mortgages)</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ApplicationEnvelopeTO">
        <annotation>
            <documentation>Data of application envelope</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="businessOwner" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Owner of this business case.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="modifOperator" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Branch operator, creator of the application.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="uwOwner" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Owner of this uw case.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="requestForAssignmentUw" type="boolean" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                True if it was requested to do manual assignment to UW
                            </documentation>
                        </annotation>
                    </element>
                    <element name="collateralUwOwner" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Owner of this collateral uw case.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="uploadDocumentsPostpone" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Date of upload documents postpone
                            </documentation>
                        </annotation>
                    </element>
                    <element name="applications" type="Q1:ApplicationSimpleTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>
                                Applications from envelope.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="channels" type="Q1:ChannelTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>
                                Channels where the client performed application operations.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="lapJob" type="Q1:LapJobTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                last score lap job
                            </documentation>
                        </annotation>
                    </element>
                    <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>                    
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="GetEnvelopeIdsTO">
        <annotation>
            <documentation>Return type for GetEnvelopeIds</documentation>
        </annotation>
        <sequence>
            <element name="applicationId" type="long"/>
            <element name="envelopeId" type="long" minOccurs="0">
                <annotation><documentation>null when the application ID was not found</documentation></annotation>
            </element>
            <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>            
        </sequence>
    </complexType>

    <complexType name="LapJobTO">
        <annotation>
            <documentation>Lap job data</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="status" type="Q1:LapJobStatusTO"/>
                    <element name="wfCode" type="string" minOccurs="0"/>
                    <element name="directive" type="string" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="LapJobStatusTO">
        <annotation>
            <documentation>Type of the lap job status</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="DELIVERED"/>
            <enumeration value="TIMEOUT"/>
            <enumeration value="WAITING"/>
            <enumeration value="FAIL_TO_FINISH"/>
            <enumeration value="CANCELLED"/>
            <enumeration value="EXPIRED"/>
        </restriction>
    </simpleType>

    <complexType name="AuditDataTO">
        <annotation>
            <documentation>Audit additional data of some entity</documentation>
        </annotation>
        <sequence>
            <element name="authorRole" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        Author's role of change.
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MortgageCommitmentAuditedChangeTO">
        <annotation>
            <documentation>Data of mortgage commitment change</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:MortgageCommitmentTO">
                <sequence>
                    <element name="recordType" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                "Com" (Commitment) or "App" (Application).
                            </documentation>
                        </annotation>
                    </element>
                    <element name="loanAmount" type="decimal" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Amount of loan.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="upsell" type="decimal" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Amount of upsell (negative number for "downsell").
                            </documentation>
                        </annotation>
                    </element>
                    <element name="upsellReasonCode" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Reason of upsell.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="downsellReasonCode" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Reason of downsell.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="FinancialDataAuditedChangeTO">
        <annotation>
            <documentation>Financial data history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="dependentPersons" type="int" minOccurs="0" maxOccurs="1"/>
                    <element name="loanPaymentSum" type="decimal" minOccurs="0" maxOccurs="1"/>
                    <element name="creditsSum" type="decimal" minOccurs="0" maxOccurs="1"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>


    <complexType name="PersonalFinancialDataAuditedChangeTO">
        <annotation>
            <documentation>Financial data history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="loanPaymentSum" type="decimal" minOccurs="0" maxOccurs="1"/>
                    <element name="creditsSum" type="decimal" minOccurs="0" maxOccurs="1"/>
                    <element name="loanPaymentSumAllMembers" type="xs:decimal">
                        <annotation>
                            <documentation>Monthly household installments expenses (loans and mortgages)</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="HouseholdIncomeExpenseAuditedChangeTO">
        <annotation>
            <documentation>Household income/expense history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="householdIncomeExpense" type="Q1:HouseholdIncomeExpenseDataTO" minOccurs="1" maxOccurs="1"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="PersonalDataAuditedChangeTO">
        <annotation>
            <documentation>Financial data history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="maritalStatus" type="int" minOccurs="0"/>
                    <element name="married" type="xs:boolean" minOccurs="0"/>
                    <element name="residenceType" type="int" minOccurs="0"/>
                    <element name="residenceTo" type="date" minOccurs="0"/>
                    <element name="education" type="int" minOccurs="0"/>
                    <element name="housingType" type="int" minOccurs="0"/>
                    <element name="dependentPersons" type="int" minOccurs="0"/>
                    <element name="householdMemberType" type="xs:string" minOccurs="0">
                        <annotation>
                            <documentation>Applicant's household member type</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="EmploymentDataAuditedChangeTO">
        <annotation>
            <documentation>Employment data history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="employmentChanges" type="Q1:EmploymentTO" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="addressChanges" type="Q1:AddressHistoryTO" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="contactChanges" type="Q1:ContactHistoryTO" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="EmploymentLastClientDataAuditedChangeTO">
        <annotation>
            <documentation>Employment data history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="employmentChanges" type="Q1:EmploymentTO" minOccurs="0" maxOccurs="1"/>
                    <element name="addressChanges" type="Q1:AddressHistoryTO" minOccurs="0" maxOccurs="1"/>
                    <element name="contactChanges" type="Q1:ContactHistoryTO" minOccurs="0" maxOccurs="1"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="EnvelopeHistoryTO">
        <annotation>
            <documentation>Envelope data history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="attributeValue" type="string" minOccurs="0" maxOccurs="1"/>
                    <element name="attributeName" type="string" minOccurs="0" maxOccurs="1"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ChannelTO">
        <annotation>
            <documentation>Channel data</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:TransferObjectWithIdentity">
                <sequence>
                    <element name="channelType" type="Q1:ChannelTypeTO"/>
                    <element name="channelRole" type="Q1:ChannelRoleTO"/>
                    <element name="branchId" type="string" minOccurs="0"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="ChannelTypeTO">
        <annotation>
            <documentation>Type of the channel where the customer performed the operation.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IB"/>
            <enumeration value="ECC"/>
            <enumeration value="ICC"/>
            <enumeration value="BRANCH"/>
            <enumeration value="EXT"/>
            <enumeration value="SPB"/>
            <enumeration value="POST"/>
            <enumeration value="MESSENGER"/>
            <enumeration value="BO"/>
        </restriction>
    </simpleType>

    <simpleType name="ChannelRoleTO">
        <annotation>
            <documentation>Role of the channel where the customer performed the operation.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="LEAD"/>
            <enumeration value="REQUEST"/>
            <enumeration value="APPROVE"/>
            <enumeration value="APPROVE_CLIENT"/>
            <enumeration value="FINISHED"/>
            <enumeration value="CURRENT"/>
            <enumeration value="PRESCORING"/>
            <enumeration value="SIGN"/>
        </restriction>
    </simpleType>

    <simpleType name="ApplicationStatusTO">
        <annotation>
            <documentation>Application status.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="DEMO"/>
            <enumeration value="LEAD"/>
            <enumeration value="UNFINISHED"/>
            <enumeration value="COMPLETION"/>
            <enumeration value="APPROVED"/>
            <enumeration value="REJECTED"/>
            <enumeration value="CANCELLED"/>
            <enumeration value="VERIFY"/>
            <enumeration value="MANUALVERIFY"/>
            <enumeration value="VIP_PAUSED"/>
            <enumeration value="WAITING"/>
            <enumeration value="MANUALPAIRING"/>
            <enumeration value="WAIT_FOR_OFFER"/>
            <enumeration value="ALTERNATIVE_OFFER"/>
            <enumeration value="UNDERWRITING"/>
        </restriction>
    </simpleType>

    <simpleType name="QueueTypeTO">
        <annotation>
            <documentation>Queueu types.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CREDIT_EXPERT_HIGH_CE"/>
            <enumeration value="CREDIT_EXPERT"/>
            <enumeration value="CREDIT_EXPERT_LOW_CE"/>
            <enumeration value="CREDIT_EXPERT_CONS_HIGH_CE"/>
            <enumeration value="CREDIT_EXPERT_CONS"/>
            <enumeration value="CREDIT_EXPERT_CONS_LOW_CE"/>
            <enumeration value="VIP"/>
            <enumeration value="EXPERT_CONSOLIDATION"/>
            <enumeration value="EXPERT_INCOME_HIGH_CE"/>
            <enumeration value="EXPERT_INCOME"/>
            <enumeration value="EXPERT_INCOME_LOW_CE"/>
            <enumeration value="EXPERT_INCOME_CONS_HIGH_CE"/>
            <enumeration value="EXPERT_INCOME_CONS"/>
            <enumeration value="EXPERT_INCOME_CONS_LOW_CE"/>
            <enumeration value="UW_COMPLETION"/>
            <enumeration value="RISK_CASE"/>
            <enumeration value="SPECIFIC_CASE_HIGH_CE"/>
            <enumeration value="SPECIFIC_CASE"/>
            <enumeration value="SPECIFIC_CASE_LOW_CE"/>
            <enumeration value="SPECIFIC_CASE_CONS_HIGH_CE"/>
            <enumeration value="SPECIFIC_CASE_CONS"/>
            <enumeration value="SPECIFIC_CASE_CONS_LOW_CE"/>
            <enumeration value="PILOT"/>
            <enumeration value="WARNING"/>
            <enumeration value="MANUAL_PAIRING"/>
            <enumeration value="VERIFICATION_EMPLOYER"/>
            <enumeration value="VERIFICATION_ENTREPRENEUR"/>
            <enumeration value="VERIFICATION_FOREIGN_EMPLOYER"/>
            <enumeration value="PERSONAL_DATA_CHANGE"/>
            <enumeration value="COMPLETION_LOAN_WALKIN"/>
            <enumeration value="COMPLETION_LOAN_EXISTING"/>
            <enumeration value="COMPLETION_CONS_INTERNAL"/>
            <enumeration value="COMPLETION_CONS_EXTERNAL_WALKIN"/>
            <enumeration value="COMPLETION_CONS_EXTERNAL_EXISTING"/>
            <enumeration value="COMPLETION_OVERDRAFT_WALKIN"/>
            <enumeration value="COMPLETION_OVERDRAFT_EXISTING"/>
            <enumeration value="COMPLETION_FAST_TRACK"/>
            <enumeration value="COMPLETION_GC_IB_POST_COURIER"/>
            <enumeration value="COMPLETION_GC_BRANCH"/>
            <enumeration value="COMPLETION_GC_MOBILE_APP"/>
            <enumeration value="COMPLETION_DISPONENT"/>
            <enumeration value="COMPLETION_CARDHOLDER"/>
            <enumeration value="COMPLETION_STOCK_ETF"/>
            <enumeration value="MORTGAGE"/>
            <enumeration value="FAIL_TO_FINISH"/>
            <enumeration value="DOCUMENTS_GC"/>
            <enumeration value="SME_COMPLETION_CARD_HOLDER"/>
            <enumeration value="SME_COMPLETION_DEPOSIT"/>
            <enumeration value="SME_COMPLETION_DISPONENT"/>
            <enumeration value="SME_COMPLETION_GC_IB_POST"/>
            <enumeration value="SME_COMPLETION_GC_BRANCH"/>
            <enumeration value="SME_DOCUMENTS_GC"/>
            <enumeration value="SME_FAIL_TO_FINISH"/>
            <enumeration value="SME_WARNING"/>
        </restriction>
    </simpleType>

    <simpleType name="ApproveStatusTO">
        <annotation>
            <documentation>LAP approval status type.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="APPROVE"/>
            <enumeration value="REJECT"/>
            <enumeration value="VERIFY"/>
            <enumeration value="MANUALVERIFY"/>
            <enumeration value="FAIL_TO_FINISH"/>
            <enumeration value="CANCEL"/>
            <enumeration value="BRKINOTFOUND"/>
            <enumeration value="MANUAL_PAIRING"/>
            <enumeration value="FI_CANCEL"/>
            <enumeration value="CLIENT_CANCEL"/>
            <enumeration value="WAITING"/>
            <enumeration value="VIP_PAUSED"/>
            <enumeration value="UNDERWRITING"/>
            <enumeration value="UW_WITH_VERIFICATION"/>
        </restriction>
    </simpleType>



    <simpleType name="AddressRoleTO">
        <annotation>
            <documentation>Role of customer/employment address.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PERMANENT"/>
            <enumeration value="FOREIGN_PERMANENT"/>
            <enumeration value="REPORTS"/>
            <enumeration value="CARD"/>
            <enumeration value="PIN"/>
            <enumeration value="EMPLOYMENT"/>
            <enumeration value="REGISTERED"/>
            <enumeration value="BUSINESS"/>
            <enumeration value="CORRESPONDENCE"/>
            <enumeration value="OFFICE"/>
            <enumeration value="HEADQUARTERS"/>
        </restriction>
    </simpleType>

    <simpleType name="ContactRoleTO">
        <annotation>
            <documentation>Role of customer/employment contact.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PRIMARY_EMAIL"/>
            <enumeration value="NOTIFICATION_EMAIL"/>
            <enumeration value="PRIMARY_MOBILE"/>
            <enumeration value="PHONE"/>
            <enumeration value="EMPLOYMENT_PHONE"/>
            <enumeration value="ENTREPRENEUR_DATA_BOX"/>
        </restriction>
    </simpleType>

    <simpleType name="AnonymizationStatusTO">
        <annotation>
            <documentation>Customer anonymization status.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ANONYMIZED"/>
            <enumeration value="HIDDEN"/>
            <enumeration value="NONE"/>
            <enumeration value="EMPLOYEE"/>
        </restriction>
    </simpleType>

    <simpleType name="AmlStatusTO">
        <annotation>
            <documentation>AML status of customer.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ACCEPTED"/>
            <enumeration value="DECLINED"/>
            <enumeration value="RISKFUL"/>
        </restriction>
    </simpleType>

    <simpleType name="DistributionChannelTypeTo">
        <annotation>
            <documentation>Application distribution channel</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IB"/>
            <enumeration value="BRANCH"/>
            <enumeration value="POST"/>
            <enumeration value="MESSENGER"/>
            <enumeration value="ICC"/>
            <enumeration value="SPB"/>
        </restriction>
    </simpleType>

    <simpleType name="CompletionScopeStatusTo">
        <annotation>
            <documentation>Completion status</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="APPROVED" />
            <enumeration value="CLIENT_CANCEL" />
            <enumeration value="FI_CANCEL" />
            <enumeration value="NOT_STARTED" />
            <enumeration value="RECEIVED" />
            <enumeration value="REJECTED" />
            <enumeration value="REOPENED" />
            <enumeration value="SIGNED" />
            <enumeration value="SUSPENDED" />
            <enumeration value="UNDERWRITING" />
            <enumeration value="WARNING" />
            <enumeration value="COMPLETION" />
        </restriction>
    </simpleType>

    <complexType name="FindApplicantsFilterTO">
        <annotation>
            <documentation>Filter for applicants search</documentation>
        </annotation>
        <sequence>
            <element name="firstName" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>First name.</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Last name.</documentation>
                </annotation>
            </element>
            <element name="birthNumber" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Birth number.</documentation>
                </annotation>
            </element>
            <element name="birthDate" type="date" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Birth date.</documentation>
                </annotation>
            </element>
            <element name="phoneNumber" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Phone number.</documentation>
                </annotation>
            </element>
            <element name="email" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Email.</documentation>
                </annotation>
            </element>
            <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>
        </sequence>
    </complexType>

    <complexType name="FindApplicantsResultTO">
        <annotation>
            <documentation>Result of applicants search.</documentation>
        </annotation>
        <sequence>
            <element name="applicantId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Applicant id.</documentation>
                </annotation>
            </element>
            <element name="firstName" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>First name.</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Last name.</documentation>
                </annotation>
            </element>
            <element name="birthNumber" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Birth number.</documentation>
                </annotation>
            </element>
            <element name="birthDate" type="date" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Birth date.</documentation>
                </annotation>
            </element>
            <element name="birthPlace" type="Q1:BirthPlaceTO" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Birth place.</documentation>
                </annotation>
            </element>
            <element name="citizenships" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>Citizenships.</documentation>
                </annotation>
            </element>
            <element name="phoneNumber" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Phone number.</documentation>
                </annotation>
            </element>
            <element name="email" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Email.</documentation>
                </annotation>
            </element>
            <element name="gender" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Gender.</documentation>
                </annotation>
            </element>
            <element name="identificationResult" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Result of customer identification in CIF.</documentation>
                </annotation>
            </element>
            <element name="relationToBankExists" type="boolean" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Flag indicating whether customer relation to bank was created for customer who had no relation before.</documentation>
                </annotation>
            </element>
            <element name="segment" type="appCommon:SegmentTO" minOccurs="0" maxOccurs="1"/>
        </sequence>
    </complexType>

    <simpleType name="ProcessTypeETO">
        <annotation>
            <documentation>Type of application process.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="STANDARD" />
            <enumeration value="SHORT" />
        </restriction>
    </simpleType>

    <complexType name="GeneralContractApplicationTO">
        <annotation>
            <documentation>General contract application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="leadPickerFirstName" type="string">
                        <annotation>
                            <documentation>Reseller first name</documentation>
                        </annotation>
                    </element>
                    <element name="leadPickerLastName" type="string">
                        <annotation>
                            <documentation>Reseller last name</documentation>
                        </annotation>
                    </element>
                    <element name="processType" type="Q1:ProcessTypeETO" minOccurs="0">
                        <annotation>
                            <documentation>Type of application process.</documentation>
                        </annotation>
                    </element>
                    <element name="identificationProcess" minOccurs="0" type="Q1:IdentificationProcessBankIdInfo">
                        <annotation>
                            <documentation>
                                Identification process info
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="InvestmentContractApplicationTO">
        <annotation>
            <documentation>Investment contract application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="investmentAccount" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Number of investment account</documentation>
                        </annotation>
                    </element>
                    <element name="assetAccount" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Number of asset account</documentation>
                        </annotation>
                    </element>
                    <element name="investmentContractNumber" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Number of investment contract</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="PensionApplicationTO">
        <annotation>
            <documentation>Pension application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="contractNumber" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Number of pension contract</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AccountApplicationTO">
        <annotation>
            <documentation>Account application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="accountType" type="Q1:AccountTypeTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>accountType</documentation>
                        </annotation>
                    </element>
                    <element name="currency" type="string">
                        <annotation>
                            <documentation>currency</documentation>
                        </annotation>
                    </element>
                    <element name="isPrimary" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>isPrimary</documentation>
                        </annotation>
                    </element>
                    <element name="loanService" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanService</documentation>
                        </annotation>
                    </element>
                    <element name="forced" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>forced</documentation>
                        </annotation>
                    </element>
                    <element name="name" type="string">
                        <annotation>
                            <documentation>name</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="AccountTypeTO">
        <annotation>
            <documentation>Account type.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CURRENT"/>
            <enumeration value="SAVINGS"/>
            <enumeration value="PROTECTED"/>
            <enumeration value="CHILD_SAVING"/>
        </restriction>
    </simpleType>

    <complexType name="CardHolderApplicationTO">
        <annotation>
            <documentation>Card holder application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CardApplicationTO">
        <annotation>
            <documentation>Card application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="accountId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>accountId</documentation>
                        </annotation>
                    </element>
                    <element name="accountNumber" type="string">
                        <annotation>
                            <documentation>accountNumber</documentation>
                        </annotation>
                    </element>
                    <element name="showPin" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>showPin</documentation>
                        </annotation>
                    </element>
                    <element name="sendPin" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>sendPin</documentation>
                        </annotation>
                    </element>
                    <element name="embossedNameChangedByUser" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>embossedNameChangedByUser</documentation>
                        </annotation>
                    </element>
                    <element name="cardDesignId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>cardDesignId</documentation>
                        </annotation>
                    </element>
                    <element name="cardDesignName" type="string">
                        <annotation>
                            <documentation>cardDesignName</documentation>
                        </annotation>
                    </element>
                    <element name="isVirtual" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>isVirtual</documentation>
                        </annotation>
                    </element>
                    <element name="replaceCardId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>replaceCardId</documentation>
                        </annotation>
                    </element>
                    <element name="replaceCardNumber" type="string">
                        <annotation>
                            <documentation>replaceCardNumber</documentation>
                        </annotation>
                    </element>
                    <element name="transferPin" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>transferPin</documentation>
                        </annotation>
                    </element>
                    <element name="wrongPersonalization" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>wrongPersonalization</documentation>
                        </annotation>
                    </element>
                    <element name="accountApplication" type="Q1:AccountApplicationTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>accountApplication</documentation>
                        </annotation>
                    </element>
                    <element name="requiredCardDevice" type="boolean" minOccurs="0">
                        <annotation>
                            <documentation>Indicates, if we create physical card through gpe</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DisponentApplicationTO">
        <annotation>
            <documentation>Disponent Application</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="DisponentAuthorizationApplicationTO">
        <annotation>
            <documentation>Disponent Authorization Application</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CashLoanApplicationTO">
        <annotation>
            <documentation>Cash loan application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:LoanApplicationTO">
                <sequence>
                    <element name="loanAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanAmount</documentation>
                        </annotation>
                    </element>
                    <element name="loanAmountSimulation" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanAmountSimulation</documentation>
                        </annotation>
                    </element>
                    <element name="instalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>instalment</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>repaymentPeriod</documentation>
                        </annotation>
                    </element>
                    <element name="idProductVariant" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>idProductVariant</documentation>
                        </annotation>
                    </element>
                    <element name="productVariant" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>productVariant</documentation>
                        </annotation>
                    </element>
                    <element name="evalMaxRepaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>evalMaxRepaymentPeriod</documentation>
                        </annotation>
                    </element>
                    <element name="evalMinRepaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>evalMinRepaymentPeriod</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ConsolidationApplicationTO">
        <annotation>
            <documentation>Cash loan application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:LoanApplicationTO">
                <sequence>
                    <element name="loanAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanAmount</documentation>
                        </annotation>
                    </element>
                    <element name="loanAmountSim" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>original amount requested by user</documentation>
                        </annotation>
                    </element>
                    <element name="instalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>instalment</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>repaymentPeriod</documentation>
                        </annotation>
                    </element>
                    <element name="idProductVariant" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>idProductVariant</documentation>
                        </annotation>
                    </element>
                    <element name="productVariant" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>productVariant</documentation>
                        </annotation>
                    </element>
                    <element name="evalMaxRepaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>evalMaxRepaymentPeriod</documentation>
                        </annotation>
                    </element>
                    <element name="evalMinRepaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>evalMinRepaymentPeriod</documentation>
                        </annotation>
                    </element>
                    <element name="currentRole" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Offer role  PRESCORE, BRKI, EXPERT_CONSOLIDATION, POSTSCORE</documentation>
                        </annotation>
                    </element>
                    <element name="loanCommitment" type="Q1:LoanCommitmentTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>Commitment that is refinanced from other financial institution during consolidation</documentation>
                        </annotation>
                    </element>
                    <element name="consolidationOffers" type="Q1:LoanConsolidationOfferTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="usedUpsell" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Upsell that client took</documentation>
                        </annotation>
                    </element>
                    <element name="usedUpsellSimulation" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Upsell that client took before scoring</documentation>
                        </annotation>
                    </element>
                    <xs:element name="requiredDocumentsVerified" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Date and time all required documents was verified</documentation>
                        </annotation>
                    </xs:element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>
    
    <xs:complexType name="IdentificationProcessBankIdInfo">
        <xs:sequence>
            <xs:element type="string" name="bankIdStateId">
                <annotation>
                    <documentation>BankId identifier</documentation>
                </annotation>
            </xs:element>
            <xs:element type="string" name="businessProcessEvent">
            </xs:element>
            <xs:element type="long" name="businessProcessEventId" minOccurs="0">
                <annotation>
                    <documentation>Id of identification process</documentation>
                </annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    
    <complexType name="LoanApplicationTO">
        <annotation>
            <documentation>Loan application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AbstractLoanApplicationTO">
                <sequence>
                    <element name="loanVariants" type="Q1:LoanVariantTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>loanVariants</documentation>
                        </annotation>
                    </element>
                    <element name="business" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>business</documentation>
                        </annotation>
                    </element>
                    <element name="chargeAdministration" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>chargeAdministration</documentation>
                        </annotation>
                    </element>
                    <element name="chargeAssoc" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>chargeAssoc</documentation>
                        </annotation>
                    </element>
                    <element name="chargeLatePay" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>chargeLatePay</documentation>
                        </annotation>
                    </element>
                    <element name="chargeProviding" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>chargeProviding</documentation>
                        </annotation>
                    </element>
                    <element name="firstInstalmentDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>firstInstalmentDate</documentation>
                        </annotation>
                    </element>
                    <element name="utilization" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>utilization</documentation>
                        </annotation>
                    </element>
                    <element name="idAccount" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                idAccount
                            </documentation>
                        </annotation>
                    </element>
                    <element name="accountNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                accountNumber
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocHtml" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                idPrecontractDocHtml
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocPdf" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                idPrecontractDocPdf
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idTargetAccount" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                idTargetAccount
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idTargetEnvelope" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                idTargetEnvelope
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idOriginApplication" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                idOriginApplication
                            </documentation>
                        </annotation>
                    </element>
                    <element name="payDay" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Day of month, when the instalments are paid.</documentation>
                        </annotation>
                    </element>
                    <element name="internalCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                internalCode
                            </documentation>
                        </annotation>
                    </element>
                    <element name="loanVariantCount" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanVariantCount</documentation>
                        </annotation>
                    </element>
                    <element name="loanSustainDocLimit" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanSustainDocLimit</documentation>
                        </annotation>
                    </element>
                    <element name="loanByIcc" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanByIcc</documentation>
                        </annotation>
                    </element>
                    <element name="loanApplicationType" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                NORMAL,REFINANCE,REFINANCING_R4,CONSOLIDATION,MORTGAGE_REFINANCE,OVERDRAFT
                            </documentation>
                        </annotation>
                    </element>
                    <element name="product" type="Q1:LoanProductTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanVariants</documentation>
                        </annotation>
                    </element>
                    <element name="segmentationOfferAccepted" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>segmentationOfferAccepted</documentation>
                        </annotation>
                    </element>
                    <element name="alternativeTo" type="Q1:LoanApplicationTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>alternativeTo</documentation>
                        </annotation>
                    </element>
                    <element name="alternativeAccepted" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>alternativeAccepted</documentation>
                        </annotation>
                    </element>
                    <element name="sameApplicationData" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>sameApplicationData</documentation>
                        </annotation>
                    </element>
                    <element name="applicationDataRecovery" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>applicationDataRecovery</documentation>
                        </annotation>
                    </element>
                    <element name="alternative" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>alternative</documentation>
                        </annotation>
                    </element>
                    <element name="contactedBy" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Operator who contacted client during underwriting. Null when the client was not contacted.</documentation>
                        </annotation>
                    </element>
                    <element name="contactedTime" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Time when client was contacted during underwriting. Null when the client was not contacted.</documentation>
                        </annotation>
                    </element>
                    <element name="requestType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Request type (currently there are only 2 possible values - comment, specific case)</documentation>
                        </annotation>
                    </element>
                    <element name="requestDescription" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Request description.</documentation>
                        </annotation>
                    </element>
                    <element name="deceasedCuid" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>CIF ID of decedent</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LoanCommitmentEstimateTO">
        <annotation>
            <documentation>Loan consolidation offer</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="estimatedTotalAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="estimatedInstAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="estimatedRpsn" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="estimatedUnpaidPrincipal" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="estimatedRemainingInstallments" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="estimatedReserveAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="role" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LoanConsolidationOfferTO">
        <annotation>
            <documentation>Loan consolidation offer</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="instalmentAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="chargeAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriodBonus" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="totalAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="rpsn" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="unpaidPrincipal" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="riskGrade" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="loanInterest" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="loanInterestBonus" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="upsell" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="reservedAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="rpsnBonus" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="totalAmountBonus" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="responseReason" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="creditType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="role" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LoanCommitmentTO">
        <annotation>
            <documentation>Loan commitment for consolidation</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="origInstalmentAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="origTotalAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="origLoanDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="origNumberOfInstalments" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="finInstCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="finInstBankCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="finInstChargeAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="finInstBufferUsed" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="origMaxCredit" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element><element name="instalmentDay" type="int" minOccurs="0" maxOccurs="1">
                    <annotation>
                        <documentation></documentation>
                    </annotation>
                </element>
                    <element name="rejected" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="rejectReason" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="rejectReasonClient" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="role" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="manualCheck" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="pairedContracts" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="creditBureau" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="responseReason" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="commitmentType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanVariants</documentation>
                        </annotation>
                    </element>
                    <element name="commitmentEstimates" type="Q1:LoanCommitmentEstimateTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>loanVariants</documentation>
                        </annotation>
                    </element>
                    <element name="origLoanId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Original loan id</documentation>
                        </annotation>
                    </element>
                    <element name="origLoanName" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Original loan name</documentation>
                        </annotation>
                    </element>
                    <element name="origLoanNumber" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Original loan number</documentation>
                        </annotation>
                    </element>
                    <element name="internal" type="boolean" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>Is internal or external commitment</documentation>
                        </annotation>
                    </element>
                    <element name="consolidable" type="boolean" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>Is the commitment consolidable</documentation>
                        </annotation>
                    </element>
                    <element name="originalUnpaidPrincipal" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Upsell that client took</documentation>
                        </annotation>
                    </element>
                    <element name="originalApplicationType" type="appCommon:ApplicationTypeTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Upsell that client took</documentation>
                        </annotation>
                    </element>
                    <xs:element name="selectedInApplication" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>This obligation is selected for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectableInApplication" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>This obligation is selectable for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="notSelectableInApplicationReason" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Reason why this obligation is not selectable for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LoanVariantTO">
        <annotation>
            <documentation>LoanVariant</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="loanApplication" type="Q1:ApplicationTO">
                        <annotation>
                            <documentation>loanApplication</documentation>
                        </annotation>
                    </element>
                    <element name="lastInstalment" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>lastInstalment</documentation>
                        </annotation>
                    </element>
                    <element name="loanInterest" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>loanInterest</documentation>
                        </annotation>
                    </element>
                    <element name="numberOfInstalments" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                numberOfInstalments
                            </documentation>
                        </annotation>
                    </element>
                    <element name="orderNumber" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                orderNumber
                            </documentation>
                        </annotation>
                    </element>
                    <element name="rpsn" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>rpsn</documentation>
                        </annotation>
                    </element>
                    <element name="rpsnMaximum" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>rpsnMaximum</documentation>
                        </annotation>
                    </element>
                    <element name="rpsnMinimum" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>rpsnMinimum</documentation>
                        </annotation>
                    </element>
                    <element name="totalAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>totalAmount</documentation>
                        </annotation>
                    </element>
                    <element name="totalAmountMin" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>totalAmountMin</documentation>
                        </annotation>
                    </element>
                    <element name="totalAmountMax" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>totalAmountMax</documentation>
                        </annotation>
                    </element>
                    <element name="bonus" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>bonus</documentation>
                        </annotation>
                    </element>
                    <element name="numberOfRemainingInstallments" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                numberOfRemainingInstallments
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="LoanProductTO">
        <annotation>
            <documentation>LoanProduct</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="loanApplication" type="Q1:ApplicationTO">
                        <annotation>
                            <documentation>loanApplication</documentation>
                        </annotation>
                    </element>
                    <element name="creditMinimum" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>creditMinimum</documentation>
                        </annotation>
                    </element>
                    <element name="creditMaximum" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>creditMaximum</documentation>
                        </annotation>
                    </element>
                    <element name="minimumInstalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>minimumInstalment</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriodMinimum" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                repaymentPeriodMinimum
                            </documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriodMaximum" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                repaymentPeriodMaximum
                            </documentation>
                        </annotation>
                    </element>
                    <element name="clientSubType" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                STD,REF,REF_R4,CON,MORTGAGE_LOAN_REFINANCE,AB,PPF,ALL
                            </documentation>
                        </annotation>
                    </element>
                    <element name="loanApplicationType" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                NORMAL,REFINANCE,REFINANCING_R4,CONSOLIDATION,MORTGAGE_REFINANCE,OVERDRAFT
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AbstractLoanApplicationTO">
        <annotation>
            <documentation>AbstractLoan application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="loanName" type="string">
                        <annotation>
                            <documentation>loanName</documentation>
                        </annotation>
                    </element>
                    <element name="requireApplicationData" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                requireApplicationData
                            </documentation>
                        </annotation>
                    </element>
                    <element name="secondScoringStart" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                secondScoringStart
                            </documentation>
                        </annotation>
                    </element>
                    <element name="secondScoringEnd" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                secondScoringEnd
                            </documentation>
                        </annotation>
                    </element>
                    <element name="insuranceOfferChecked" type="boolean" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                Insurance offer checked.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>


    <complexType name="CoDebtorApplicationTO">
        <annotation>
            <documentation>Codebtor application (used for Mortgage refinance).</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="relationToMainApplicant" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Relation to main applicant from MDM register RelationshipRole.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="CompletionTypeTO">
        <annotation>
            <documentation>
                Completion type with document variant
            </documentation>
        </annotation>
        <sequence>
            <element name="code" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        Type of completion RHY_DEBTOR_AMENDMNT, RHY_DEBTOR_AMDM_INF, RHY_CODEBT_AMENDMNT,...
                    </documentation>
                </annotation>
            </element>
            <element name="variant" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Document variant 1, 2, 3...</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="AbstractMortgageApplicationTO" abstract="true">
        <annotation>
            <documentation>Mortgage refinance application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AbstractLoanApplicationTO">
                <sequence>
                    <element name="payDay" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Day of month, when the instalments are paid.</documentation>
                        </annotation>
                    </element>
                    <element name="firstInstalmentDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date, when the first instalment is paid.</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date, when the mortgage will be completely repaid.</documentation>
                        </annotation>
                    </element>
                    <element name="internalCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Internal client evaluation code created by branch officer.</documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocHtml" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Document identifier of precontract document in HTML.</documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocPdf" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Document identifier of precontract document in PDF.</documentation>
                        </annotation>
                    </element>
                    <element name="loanAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Requested mortgage amount.</documentation>
                        </annotation>
                    </element>
                    <element name="instalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Monthly instalment amount.</documentation>
                        </annotation>
                    </element>
                    <element name="minInstalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Minimum monthly instalment (when using the longest possible repayment period).</documentation>
                        </annotation>
                    </element>
                    <element name="maxInstalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Maximum monthly instalment amount (only valid and not null for variable rate type).</documentation>
                        </annotation>
                    </element>
                    <element name="term" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Repayment period (in years).</documentation>
                        </annotation>
                    </element>
                    <element name="productVariant" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Code of the OBS product variant.</documentation>
                        </annotation>
                    </element>
                    <element name="realEstatePriceEstimate" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Estimated real estate price.</documentation>
                        </annotation>
                    </element>
                    <element name="realEstatePriceOngoing" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Calculated real estate price.</documentation>
                        </annotation>
                    </element>
                    <element name="loanAmountOngoing" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Calculated loan amount of mortgage.</documentation>
                        </annotation>
                    </element>
                    <element name="requestDescription" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Description of the application and its transfer written by operator at the branch.</documentation>
                        </annotation>
                    </element>
                    <element name="requestType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Request type (currently only comment or specific case).</documentation>
                        </annotation>
                    </element>
                    <element name="interestRateType" type="Q1:MortgageInterestRateTypeTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Mortgage interest rate type (enumeration of FIX, FLOAT).</documentation>
                        </annotation>
                    </element>
                    <element name="fixationPeriod" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Length of fixation period (years).</documentation>
                        </annotation>
                    </element>
                    <element name="interestRate" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Interest rate.</documentation>
                        </annotation>
                    </element>
                    <element name="maxInterestRateRisk" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Interest rate.</documentation>
                        </annotation>
                    </element>
                    <xs:element name="otbUsageInterestRateDivergence" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Interest rate divergence in case with OTB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <element name="maxInterestRate" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Maximum interest rate (only valid and not null for variable rate type).</documentation>
                        </annotation>
                    </element>
                    <element name="upsellReasonCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Code of the reason to request higher amount than the original amount.</documentation>
                        </annotation>
                    </element>
                    <element name="downsellReasonCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Code of the reason to request lower amount than the original amount.</documentation>
                        </annotation>
                    </element>
                    <element name="scoreExpire" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Scoring expiration date (i.e. validity of the offer).</documentation>
                        </annotation>
                    </element>
                    <element name="upsellAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Requested mortgage upsell amount of commitments, always positive if specified.</documentation>
                        </annotation>
                    </element>
                    <element name="downsellAmount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Requested mortgage downsell amount of commitments, always positive if specified.</documentation>
                        </annotation>
                    </element>
                    <element name="maxTerm" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Max. repayment period (in years).</documentation>
                        </annotation>
                    </element>
                    <element name="maxTermRisk" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Maximal repayment period (in years) set by operator, could be different from OBS calculated maxTerm</documentation>
                        </annotation>
                    </element>
                    <element name="pribor" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Repayment period (in years).</documentation>
                        </annotation>
                    </element>
                    <element name="capInterestRate" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Cap. interest rate.</documentation>
                        </annotation>
                    </element>
                    <element name="minCapInstalment" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Minimal cap. instalment.</documentation>
                        </annotation>
                    </element>
                    <element name="riskGrade" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Risk Grade.</documentation>
                        </annotation>
                    </element>
                    <element name="interestMargin" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Interest margin.</documentation>
                        </annotation>
                    </element>
                    <element name="utilizationDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation></documentation>
                        </annotation>
                    </element>
                    <element name="requestedUtilizationDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Start of the mortgage refinancing requested by the client.</documentation>
                        </annotation>
                    </element>
                    <element name="finalUtilizationDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date when mortgage switch to annuity repayment.</documentation>
                        </annotation>
                    </element>
                    <element name="applicationDataRecovery" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Application data recovery (oživení aplikačních dat) flag</documentation>
                        </annotation>
                    </element>
                    <element name="globalLimitHyref" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Schválená částka úvěru</documentation>
                        </annotation>
                    </element>
                    <element name="stage" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Fáze zpracování žádosti</documentation>
                        </annotation>
                    </element>
                    <element name="collateralLock" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Collateral Lock</documentation>
                        </annotation>
                    </element>
                    <element name="sameApplicationData" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>sameApplicationData</documentation>
                        </annotation>
                    </element>
                    <element name="distributionChannelType2nd" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>distributionChannelType2nd</documentation>
                        </annotation>
                    </element>
                    <element name="obsMortgageId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>obsMortgageId</documentation>
                        </annotation>
                    </element>
                    <element name="rateReferenceDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>rateReferenceDate</documentation>
                        </annotation>
                    </element>
                    <element name="purpose1Type" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Type of mortgage purpose 1</documentation>
                        </annotation>
                    </element>
                    <element name="purpose2Type" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Type of mortgage purpose 2</documentation>
                        </annotation>
                    </element>
                    <element name="purpose1Amount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Částka účelu 1</documentation>
                        </annotation>
                    </element>
                    <element name="purpose2Amount" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Částka účelu 2</documentation>
                        </annotation>
                    </element>
                    <element name="flagForRent" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>It is for rent indicator</documentation>
                        </annotation>
                    </element>
                    <element name="ltv" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Current LTV</documentation>
                        </annotation>
                    </element>
                    <element name="maxLtv" type="int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Maximal LTV</documentation>
                        </annotation>
                    </element>
                    <element name="ltvOngoing" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Current LTV</documentation>
                        </annotation>
                    </element>
                    <element name="maxLtvOngoing" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Maximal LTV</documentation>
                        </annotation>
                    </element>
                    <element name="feeLtv" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Fee to exceed max LTV ongoing.</documentation>
                        </annotation>
                    </element>
                    <element name="insurancePrice" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Price of insurance</documentation>
                        </annotation>
                    </element>
                    <element name="insuranceInterestRateDivergence" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Divergence of interest rate for insurance</documentation>
                        </annotation>
                    </element>
                    <element name="mortgageCashbackValue" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Mortgage cashback.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="MortgageRefinanceApplicationTO">
        <annotation>
            <documentation>Mortgage refinance application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AbstractMortgageApplicationTO">
                <sequence>
                    <element name="mortgageLoanCommitments" type="Q1:MortgageCommitmentTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>All commitments to refinance.</documentation>
                        </annotation>
                    </element>
                    <element name="processLater" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Flag indicating, that the application cannot be processed right now, because the refixation date is too far in
                                future, and the customer wants us to call him later.
                            </documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="MortgageApplicationTO">
        <annotation>
            <documentation>Mortgage application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AbstractMortgageApplicationTO">
                <sequence>
                    <element name="purchasePrice" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Property price.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="MortgageCommitmentTO">
        <annotation>
            <documentation>Mortgage commitment.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="commitmentType" type="Q1:MortgageCommitmentTypeTO" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>Applicant id.</documentation>
                        </annotation>
                    </element>
                    <element name="finInstitutionCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Code of the financial instution of the commitment (enumeration).</documentation>
                        </annotation>
                    </element>
                    <element name="pairContract" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Informace o zpárovaných kontraktech z dat BRKI/HC.</documentation>
                        </annotation>
                    </element>
                    <element name="quantification" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Quantification of debt / částka k doplacení uvedená na vyčíslení.</documentation>
                        </annotation>
                    </element>
                    <element name="quantificationDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date for required payment of quantification / Datum požadovaného uhrazení z vyčíslení.</documentation>
                        </annotation>
                    </element>
                    <xs:element name="originalContractNumber" type="xs:string"  minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Original contract number</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="originalContractSigningDate" type="xs:dateTime"  minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date of signature of original contract</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="ClientTypeTO">
        <annotation>
            <documentation>Client type [WALK_IN, EXISTING].</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="WALK_IN"/>
            <enumeration value="EXISTING"/>
        </restriction>
    </simpleType>

    <simpleType name="MortgageInterestRateTypeTO">
        <annotation>
            <documentation>Mortgage interest rate type.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="FIX"/>
            <enumeration value="FLOAT"/>
        </restriction>
    </simpleType>

    <simpleType name="MortgageCommitmentTypeTO">
        <annotation>
            <documentation>Mortgage commitment type.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="MORTGAGE_LOAN"/>
            <enumeration value="CONSTRUCTION_SAVINGS_LOAN"/>
        </restriction>
    </simpleType>

    <complexType name="ApplicationCheckTO">
        <annotation>
            <documentation>Application check.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <xs:sequence>
                    <xs:element name="applicationId" type="long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="trancheId" type="long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of mortgage tranche.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Cuid of applicant (null if check belongs to whole application).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Check type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="status" type="Q1:ApplicationCheckStatusTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Check status.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="note" type="string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Check note.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="riskReason" type="string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Risk reason</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </extension>
        </complexContent>
    </complexType>

    <simpleType name="ApplicationCheckStatusTO">
        <annotation>
            <documentation>Check status.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PASSED"/>
            <enumeration value="FAILED"/>
            <enumeration value="UNKNOWN"/>
            <enumeration value="NOT_CLIENT"/>
            <enumeration value="ERROR"/>
        </restriction>
    </simpleType>

    <complexType name="ScoringProgressResultTO">
        <annotation>
            <documentation>Scoring progress result.</documentation>
        </annotation>
        <sequence>
            <element name="done" type="boolean" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Scoring is finished.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="WorkflowIdTO">
        <annotation>
            <documentation>Workflow ID.</documentation>
        </annotation>
        <sequence>
            <element name="wfId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Workflow ID.</documentation>
                </annotation>
            </element>
            <element name="wfCode" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Workflow code.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PropertyTO">
        <annotation>
            <documentation>Property of envelope.</documentation>
        </annotation>
        <sequence>
            <element name="code" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Property code.</documentation>
                </annotation>
            </element>
            <element name="value" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Property value.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <!-- SCORING DATA BEGIN -->

    <complexType name="DecisionTO">
        <annotation>
            <documentation>
                Odpovídá FP LAP: productResults[]
                K dané žádosti (application) a výsledku (result) daného workflow může existovat více productType. Toto je způsobeno tím, že v případě úvěrových
                žádostí lze zapsat výsledek pro jiný druh úvěrové žádosti, než byla samotná žádost, která se považuje za tzv. alternativní žádost (=nabídku),
                která v daný okamžik fyzicky neexistuje.
                V zásadě by měl být ProductResult jenom vyjádřením se = rozhodnutí k různým žádostem v obálce a neměla by přibývat další úroveň členění dle
                productType. Proto se alternativa eviduje jako specifický druh žádosti (předmětu schvalování).
            </documentation>
        </annotation>
        <sequence>
            <element name="approvalSubjectOrigin" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        AMS pro žádost
                        LAP pro alternativu
                        COMPLETION pro dokument.
                    </documentation>
                </annotation>
            </element>
            <element name="approvalSubjectType" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>O jaký typ jde.</documentation>
                </annotation>
            </element>
            <element name="approvalSubjectId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Pro dokument jde o document.id
                        Pro alternativu se ukladá odkaz na amsApplcation.id pro kterou alternativa vznikla.
                        Pro ams žádost se ukládá amsApplication.id
                    </documentation>
                </annotation>
            </element>
            <element name="result" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Původně: productSystemDecision
                        Výsledek scoringu pro danou žádost.
                    </documentation>
                </annotation>
            </element>
            <element name="comment" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="reason" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="reasonClient" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="partLoan" type="Q1:DecisionPartLoanTO" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Rozšiřující údaje k rozhodnutí o schvalovaném předmětu, který je úvrového typu.</documentation>
                </annotation>
            </element>
            <element name="partMortgage" type="Q1:DecisionPartMortgageTO" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Rozšiřující údaje k rozhodnutí o schvalovaném předmětu, který je úvrového typu.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
    <complexType name="DecisionPartLoanTO">
        <annotation>
            <documentation>
                Rozšíření rozhodnutí. Rozhodnutí se může týkat i alternativy a je dáno scoringem a předmětem schvalování.
                Zapisuje se jen pro úvěrový scoring.
            </documentation>
        </annotation>
        <sequence>
            <element name="consolidationMode" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>
                        Režim konsolidace. Jen pro typ předmětu schvalování CONSOLIDATION.
                        Ve vektoru se jedná o FP productresults[].productScoreResult.
                        Očekávané hodnoty 0+, 0-
                    </documentation>
                </annotation>
            </element>
            <element name="creditAmount" type="decimal" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        Výše úvěru pro daný scoring a předmět schvalování (žádost nebo alternativa).
                        FP: outputData.changeProductParameters.creditAmount
                    </documentation>
                </annotation>
            </element>
            <element name="installmentCount" type="long" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>
                        Počet splátek pro daný scoring a typ produktu. FP: outputData.changeProductParameters.paymentSum
                    </documentation>
                </annotation>
            </element>
            <element name="interestRate" type="decimal" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>FP: outputdata.changeProductParameters.yearInterestRate</documentation>
                </annotation>
            </element>
            <element name="interestRateBonus" type="decimal" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        bonusová sazba
                        FP: outputdata.changeProductParameters.bonusYearInterestRate
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DecisionPartMortgageTO">
        <sequence>
            <element name="maximalApprovedAmount" type="decimal" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        maximální schválená částka hypotéky
                        FP: mortgageResult[].maximalApprovedAmount
                    </documentation>
                </annotation>
            </element>
            <element name="mls" type="decimal" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        mls na žádost o hypotéku
                    </documentation>
                </annotation>
            </element>
            <element name="riskGrade" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>
                        riskGrade na žádosti o hypotéku
                    </documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="PersonScoreModifyTO">
        <annotation>
            <documentation>Person score - changeable attributes.</documentation>
        </annotation>
        <sequence>
            <element name="cuid" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Identifikace osoby z obálky.</documentation>
                </annotation>
            </element>
            <element name="riskGrade" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="loanBinAllocation" type="decimal" minOccurs="0" maxOccurs="1"/>
        </sequence>
    </complexType>

    <complexType name="MlsTO">
        <sequence>
            <element name="productType" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>CASHLOAN, CONSOLIDATION, MORTGAGE_REF</documentation>
                </annotation>
            </element>
            <element name="evaluationArea" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Oblast dat z níž je MLS vypočten: RISK, BRKI, HC</documentation>
                </annotation>
            </element>
            <element name="amount" type="decimal" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Hodnota MLS.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="MlsChangeTO">
        <sequence>
            <element name="cuid" type="long" minOccurs="0" maxOccurs="1"/>
            <element name="mls" type="Q1:MlsTO"/>
        </sequence>
    </complexType>

    <!-- SCORING DATA END -->

    <simpleType name="CancelActionTypeTO">
        <annotation>
            <documentation>Type of cancel action.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CANCEL_BY_CLIENT"/>
            <enumeration value="CANCEL_BY_BANK"/>
        </restriction>
    </simpleType>

    <!--Priority event-->

    <complexType name="PriorityEventTo">
        <annotation>
            <documentation>Priority event to indicate event on application.</documentation>
        </annotation>
        <sequence>
            <element name="eventId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Priority event ID.</documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Application envelope ID.</documentation>
                </annotation>
            </element>
            <element name="priority" type="int" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Priority of event.</documentation>
                </annotation>
            </element>
            <element name="eventType" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Type of event.</documentation>
                </annotation>
            </element>
            <element name="createTime" type="dateTime" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Date of priority event creation.</documentation>
                </annotation>
            </element>
            <element name="validToTime" type="dateTime" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>When event was disabled.</documentation>
                </annotation>
            </element>
            <element name="modifyTime" type="dateTime" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Date of last change of priority event.</documentation>
                </annotation>
            </element>
            <element name="reporter" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Operator who create this priority event.</documentation>
                </annotation>
            </element>
            <element name="closedBy" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Operator of last change of priority event(Who disable it).</documentation>
                </annotation>
            </element>
            <element name="branchOffice" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Branch office of origin (of urgency).</documentation>
                </annotation>
            </element>
            <element name="reasonCode" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Reason of urgency.</documentation>
                </annotation>
            </element>
            <element name="note" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Note.</documentation>
                </annotation>
            </element>
            <element name="manualTaskId" type="long" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Manual task Id related to this event (case of assing task by team leader).</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="QueuePriorityTO">
        <annotation>
            <documentation>Priority set on queue by teamleader</documentation>
        </annotation>
        <sequence>
            <element name="priority" type="int" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Priority of queue</documentation>
                </annotation>
            </element>
            <element name="queue" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Queue</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="QueuePriorityResultTO">
        <annotation>
            <documentation>Priority set on queue by teamleader</documentation>
        </annotation>
        <sequence>
            <element name="queuePriorityList" type="Q1:QueuePriorityTO" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>List of common queue priorities of all operators to show</documentation>
                </annotation>
            </element>
            <element name="deniedOperators" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>List of denied operators. Denied operator is a operator who has least one queue different than others.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="OperatorTO">
        <annotation>
            <documentation>Information about operator</documentation>
        </annotation>
        <sequence>
            <element name="operatorId" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Id of operator</documentation>
                </annotation>
            </element>
            <element name="firstname" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Operator's first name</documentation>
                </annotation>
            </element>
            <element name="surname" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Operator's lastname</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="TaskActionTO">
        <annotation>
            <documentation>Information about manual task allowed actions</documentation>
        </annotation>
        <sequence>
            <element name="taskId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Id of manual task</documentation>
                </annotation>
            </element>
            <element name="alerts" type="string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>Alerts of manual task actions</documentation>
                </annotation>
            </element>
            <element name="defaultAction" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Default action of manual task.</documentation>
                </annotation>
            </element>
            <element name="actions" type="Q1:ActionTO" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>Allowed actions for manual task</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="ActionTO">
        <annotation>
            <documentation>Information about manual task allowed actions</documentation>
        </annotation>
        <sequence>
            <element name="action" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Name of the action</documentation>
                </annotation>
            </element>
            <element name="allowed" type="boolean" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Is action allowed?</documentation>
                </annotation>
            </element>
            <element name="resultReason" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Reason why is not allowed?</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DocumentSnapshotTO">
        <annotation>
            <documentation>Document snapshot DMS object - tracking document change history</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="envelopeId" minOccurs="1" maxOccurs="1" type="long"/>
                    <element name="dmsId" minOccurs="1" maxOccurs="1" type="string"/>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="AbstractCancelOrRejectApplicationRequest">
        <sequence>
            <element name="applicationId" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Application ID</documentation>
                </annotation>
            </element>
            <element name="reasonCode" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Reason code</documentation>
                </annotation>
            </element>
            <element name="reasonCodeClient" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Reason code for client</documentation>
                </annotation>
            </element>
            <element name="reasonDescription" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Reason description</documentation>
                </annotation>
            </element>
            <element name="wholeEnvelope" type="boolean" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Cancel all application in envelope</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
    <complexType name="QueueStatisticsTO">
        <sequence>
            <element name="totalEnvelopesCount" maxOccurs="1" minOccurs="1" type="int"/>
            <element name="activeEnvelopesCount" maxOccurs="1" minOccurs="1" type="int"/>
            <element name="assignedEnvelopesCount" maxOccurs="1" minOccurs="1" type="int"/>
        </sequence>
    </complexType>

    <simpleType name="ContractTypeTO">
        <restriction base="string">
            <enumeration value="RS">
                <annotation>
                    <documentation>Rámcová smlouva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PRICE_CLIENT">
                <annotation>
                    <documentation>Změna cenového programu na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PRICE_BANK">
                <annotation>
                    <documentation>Změna cenového programu Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_BU">
                <annotation>
                    <documentation>Založení produktu BU</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_BU_CLIENT">
                <annotation>
                    <documentation>Zrušení produktu BU na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AVOIDANCE_BU_CLIENT">
                <annotation>
                    <documentation>Odstoupení od produktu BU na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_BU_BANK">
                <annotation>
                    <documentation>Zrušení BU Bankou - bude?</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_HYSA">
                <annotation>
                    <documentation>Založení produktu HYSA</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HYSA_CLIENT">
                <annotation>
                    <documentation>Zrušení produktu HYSA na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AVOIDANCE_HYSA_CLIENT">
                <annotation>
                    <documentation>Odstoupení od produktu HYSA na žádost klienta - bude?</documentation>
                </annotation>
            </enumeration>
            <enumeration value="AVOIDANCE_HYSA_BANK">
                <annotation>
                    <documentation>Zrušení HYSA Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_DK">
                <annotation>
                    <documentation>Založení produktu DK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_DK_INFO">
                <annotation>
                    <documentation>Založení produktu DK na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_DK_HOLDER">
                <annotation>
                    <documentation>Založení produktu DK pro jiného Držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_CHILD_DK_HOLDER">
                <annotation>
                    <documentation>Založení produktu DK pro detskeho Držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DECLARATE_HOLDER">
                <annotation>
                    <documentation>Prohlášení Držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="DECLARATE_CHILD_HOLDER">
                <annotation>
                    <documentation>Prohlášení detskeho Držitele</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DK_CLIENT">
                <annotation>
                    <documentation>Zrušení  DK na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DK_INFO">
                <annotation>
                    <documentation>Zrušení DK na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ADD_DISP">
                <annotation>
                    <documentation>Zřízení disponenta k bankovním službám</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ADD_CHILD_DISP">
                <annotation>
                    <documentation>Zřízení dětského disponenta k bankovním službám</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ANNOU_DISP">
                <annotation>
                    <documentation>Prohlášení Disponenta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="ANNOU_CHILD_DISP">
                <annotation>
                    <documentation>Prohlášení dětského Disponenta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DISP_OWNER">
                <annotation>
                    <documentation>Zrušení disponenta majitelem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DISP_DISP">
                <annotation>
                    <documentation>Zrušení disponenta disponentem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_DISP_BANK">
                <annotation>
                    <documentation>Zrušení disponenta Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HOLDER_OWNER">
                <annotation>
                    <documentation>Zrušení držitele majitelem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HOLDER_HOLDER">
                <annotation>
                    <documentation>Zrušení držitele držitelem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HOLDER_BANK">
                <annotation>
                    <documentation>Zrušení držitele Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_VK">
                <annotation>
                    <documentation>Založení produktu VK</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_VK_INFO">
                <annotation>
                    <documentation>Založení produktu VK na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_VK_CLIENT">
                <annotation>
                    <documentation>Zrušení produktu VK na žádost klienta</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_VK_BANK">
                <annotation>
                    <documentation>Zrušení VK Bankou</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_HYSA_BANK"></enumeration>
            <enumeration value="CANCEL_VK_CLIENT">
                <annotation>
                    <documentation>Zrušení VK klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_MOBILITY">
                <annotation>
                    <documentation>mobilita</documentation>
                </annotation>
            </enumeration>
            <enumeration value="SJM">
                <annotation>
                    <documentation>společné jmění manželů</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_LOAN">
                <annotation>
                    <documentation>žádost o hotovostní úvěr</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CANCEL_LOAN">
                <annotation>
                    <documentation>Odstoupení od hotovostního úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_BU_LOAN">
                <annotation>
                    <documentation>žádost o hotovostního úvěru s bežným účtem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="PARTIAL_REPAYMENT">
                <annotation>
                    <documentation>žádost o částečné splacení úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="EARLY_REPAYMENT">
                <annotation>
                    <documentation>žádost o předčasné splacení úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CREATE_REFINANCING">
                <annotation>
                    <documentation>Refinancování úvěru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESIGN_ACC_CLIENT">
                <annotation>
                    <documentation>odstoupení od účtu klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RESIGN_LOAN_CLIENT">
                <annotation>
                    <documentation>odstoupení od úvěru klientem</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANGE_INST_DATE">
                <annotation>
                    <documentation>zmena parametru data splatky uveru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="CHANGE_INST_AMOUNT">
                <annotation>
                    <documentation>zmena parametru vyse splatky uveru</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RENEWAL_DK">
                <annotation>
                    <documentation>Automatická obnova debetní karty</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LOAN_COMMITMENT">
                <annotation>
                    <documentation>Úvěrový příslib RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LOAN_AGREEMENT">
                <annotation>
                    <documentation>Úvěrová smlouva RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_ACCESSN_TO_DEBT">
                <annotation>
                    <documentation>Přistoupení k dluhu spoludlužníkem RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_MORTGAGE_DEED">
                <annotation>
                    <documentation>Zástavní smlouva RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_LAND_REG_INSERT">
                <annotation>
                    <documentation>Návrh na vklad na katastr RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_DEBTOR_AMENDMNT">
                <annotation>
                    <documentation>Dodatek úvěrové smlouvy HD RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CODEBT_AMENDMNT">
                <annotation>
                    <documentation>Dodatek k přistoupení SD RefinHypo</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_DEBTOR_AMDM_INF">
                <annotation>
                    <documentation>Dodatku úvěrové smlouvy HD RefinHypo na vědomí</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_CH_INST_AMOUNT">
                <annotation>
                    <documentation>Změna délky a výšky měsíční splátky HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_CH_INST_DATE">
                <annotation>
                    <documentation>Změna data splátky HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_EXTRAINSTALL">
                <annotation>
                    <documentation>Mimořádná splátka HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYPO_ADD_DISBURSMNT">
                <annotation>
                    <documentation>Vrácení přeplatku HYPO</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CNCL_AGREEMENT">
                <annotation>
                    <documentation>Storno/zamitnuti uverove smlouvy</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHY_CNCL_COMMITMENT">
                <annotation>
                    <documentation>Storno/zamitnuti uveroveho prislibu</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHYREQQUANTIFY">
                <annotation>
                    <documentation>Žádost o vyčíslení původního závazku</documentation>
                </annotation>
            </enumeration>
            <enumeration value="RHYANNOTHELIEN">
                <annotation>
                    <documentation>Oznámení o vzniku zástavního práva</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYNEW_DEBTOR_AMENDMNT">
                <annotation>
                    <documentation>Dodatek úvěrové smlouvy HD HyNew</documentation>
                </annotation>
            </enumeration>
            <enumeration value="HYNEW_CODEBT_AMENDMNT">
                <annotation>
                    <documentation>Dodatek k přistoupení SD HyNew</documentation>
                </annotation>
            </enumeration>
            <enumeration value="REFIN_NOTICE_LETTER">
                <annotation>
                    <documentation>Konsolidace - výpovědní dopis</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <complexType name="DeliveredDocumentTO">
        <xs:complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="deliveryDate" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>when the document was delivered</documentation>
                        </annotation>
                    </element>
                    <element name="documentType" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>document type in CIF</documentation>
                        </annotation>
                    </element>
                    <element name="documentName" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document description</documentation>
                        </annotation>
                    </element>
                    <element name="issueDate" type="xs:date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date of document issue</documentation>
                        </annotation>
                    </element>
                    <element name="idExt" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Vazba na IDProof v RDR</documentation>
                        </annotation>
                    </element>
                    <element name="status" type="Q1:DocumentStatusTO" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>status of document</documentation>
                        </annotation>
                    </element>
                    <element name="statusOrigin" type="Q1:DocumentStatusOriginTO" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>origin of document status</documentation>
                        </annotation>
                    </element>
                    <element name="migrationFlag" type="Q1:DocumentMigrationFlagTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>migration flag of document</documentation>
                        </annotation>
                    </element>
                    <element name="archiveStatus" type="Q1:ArchiveStatusTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>status of document</documentation>
                        </annotation>
                    </element>
                    <element name="operatorNote" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>comment by operator - call center / ICC / ECC etc</documentation>
                        </annotation>
                    </element>
                    <element name="branchOfficerNote" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>comment pobocnika</documentation>
                        </annotation>
                    </element>
                    <element name="internalNote" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>comment internal</documentation>
                        </annotation>
                    </element>
                    <element name="deliveryChannel" type="Q1:DeliveryChannelTO" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>delivery channel by which the document was delivered</documentation>
                        </annotation>
                    </element>
                    <element name="documentAttachments" type="Q1:DocumentAttachmentTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>document group relation</documentation>
                        </annotation>
                    </element>
                    <element name="rejectReason" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document rejection reason in MDM</documentation>
                        </annotation>
                    </element>
                    <element name="cancelReason" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document cancel reason in MDM</documentation>
                        </annotation>
                    </element>
                    <element name="barCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>search code</documentation>
                        </annotation>
                    </element>
                    <element name="uploaded" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is uploaded?</documentation>
                        </annotation>
                    </element>
                    <element name="onlyElectronicVersion" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is only electronic version?</documentation>
                        </annotation>
                    </element>
                    <element name="isVirtual" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is virtual version of document?</documentation>
                        </annotation>
                    </element>
                    <element name="notFound" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>not found?</documentation>
                        </annotation>
                    </element>
                    <element name="discarded" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is discarded?</documentation>
                        </annotation>
                    </element>
                    <element name="discardedDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date of discarding. It is filled when we set discarded to true</documentation>
                        </annotation>
                    </element>
                    <element name="archiveNumber" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>archive number</documentation>
                        </annotation>
                    </element>
                    <element name="lentToOperatorId" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>LDAP id of person who was it lent to</documentation>
                        </annotation>
                    </element>
                    <element name="mergedTo" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Merged to document ID</documentation>
                        </annotation>
                    </element>
                    <element name="identityInfo" type="Q1:IdentityInfoTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Special attributes for identification documents</documentation>
                        </annotation>
                    </element>
                    <element name="incomeInfo" type="Q1:IncomeInfoTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Special attributes for income documents</documentation>
                        </annotation>
                    </element>
                    <element name="studentConfirmationInfo" type="Q1:StudentConfirmationInfoTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Special attributes for student confirmation documents</documentation>
                        </annotation>
                    </element>
                    <element name="customerInfo" type="Q1:ApplicantTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Only used in response. Information about client. Only id, cuid, first name, last name, honourBefore and honourAfter
                                are filled
                            </documentation>
                        </annotation>
                    </element>
                    <element name="lockedFor" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Document is locked for specified operator</documentation>
                        </annotation>
                    </element>
                    <element name="source" type="Q1:DocumentSourceTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>source of document (read-only field, value is set by backend)</documentation>
                        </annotation>
                    </element>
                    <element name="useAsLegalBindingDocument" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>use document as legal binding document</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </xs:complexContent>
    </complexType>

    <complexType name="DeliveredDocumentWithGroupTO">
        <sequence>
            <element name="groupFilter" type="Q1:GroupFilterTO" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>filter to identify document group</documentation>
                </annotation>
            </element>
            <element name="document" type="Q1:DeliveredDocumentTO" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>delivered document metadata</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <xs:simpleType name="DocumentStatusTO">
        <xs:annotation>
            <xs:documentation>stavy dokladů a zpracování</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="RECEIVED">
                <xs:annotation>
                    <xs:documentation>Přijatý (Neověřený)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VERIFIED_NOK">
                <xs:annotation>
                    <xs:documentation>Ověřený NOK : pokud je nějaký problém s přílohou dokladu (nekvalitní scan, chybí zadní strana, atd.), klient dohraje
                        chybějící obrázek
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VERIFIED_OK">
                <xs:annotation>
                    <xs:documentation>Zpracovaný (Ověřený OK) : doklad je v pořádku, proběhlo zavstupování a schválení dokladu</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="REJECTED">
                <xs:annotation>
                    <xs:documentation>Zamítnutý : Doklad byl zamítnutý, konečný stav, klient musí nahrát novou přílohu</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CANCELED">
                <xs:annotation>
                    <xs:documentation>Stornovaný</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="DocumentStatusOriginTO">
        <xs:annotation>
            <xs:documentation>Původ stavu dokladu</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="AISP">
                <xs:annotation>
                    <xs:documentation>Automatické zpracování na základě dodání dokladu přes AISP</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OCR">
                <xs:annotation>
                    <xs:documentation>Automatické zpracování s OCR</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MANUAL">
                <xs:annotation>
                    <xs:documentation>Ruční spuštění</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AIS_EOP">
                <xs:annotation>
                    <xs:documentation>Agendový Informační Systém Evidence Občanských Průkazů</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <simpleType name="DocumentSourceTO">
        <restriction base="string">
            <enumeration value="CLIENT"/>
            <enumeration value="AISP"/>
            <enumeration value="AB"/>
            <enumeration value="AIS_EOP">
                <annotation>
                    <documentation>Agendový Informační Systém Evidence Občanských Průkazů</documentation>
                </annotation>
            </enumeration>
        </restriction>
    </simpleType>

    <xs:simpleType name="DocumentMigrationFlagTO">
        <xs:annotation>
            <xs:documentation>document flag related to migration of id a validTo</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="PROOF">
                <xs:annotation>
                    <xs:documentation>data mined from proof</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RISK_ESTIMATE">
                <xs:annotation>
                    <xs:documentation>data mined from application and the validTo is computed by risk algorithm</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MANUAL_PROCESS">
                <xs:annotation>
                    <xs:documentation>data mining unsuccessfull, so the data will be added by the manual process</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MANUAL_PROCESS_CANDIDATE">
                <xs:annotation>
                    <xs:documentation>candidate to get data by the manual process</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INACTIVE_CLIENT">
                <xs:annotation>
                    <xs:documentation>client has no relation to bank</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ArchiveStatusTO">
        <xs:annotation>
            <xs:documentation>stavy dokladů a zpracování</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="IN_REGISTRY">
                <xs:annotation>
                    <xs:documentation>Ve spisovne</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EXT_REGISTRY">
                <xs:annotation>
                    <xs:documentation>V externí spisovně (nepoužívá se)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BORROWED">
                <xs:annotation>
                    <xs:documentation>Zapůjčený</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <complexType name="DocumentAttachmentTO">
        <sequence>
            <element name="dmsId" type="string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>id of document in DMS</documentation>
                </annotation>
            </element>
            <element name="deleted" type="boolean" minOccurs="1" maxOccurs="1" default="false">
                <annotation>
                    <documentation>Is the document deleted? We want to display also deleted documents. The default value is false.</documentation>
                </annotation>
            </element>
            <element name="anonymized" type="boolean" minOccurs="0" maxOccurs="1" default="false">
                <annotation>
                    <documentation>Is the document anonymised? The default value is false.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="DeliveryChannelTO">
        <annotation>
            <documentation>
                IB - Internetové bankovnictví
                POST - Pošta
                MESSENGER - Kurýr
                BRANCH - Pobočka
                EMAIL Jiný e-mail
                EMAILPR - Primární e-mail Majitele RS
                EMAILDP - Primární e-mail Disponenta
                EMAILCH - Primární e-mail Držitele
                REGISTER - Registry
                SPB - smart phone banking - mobilni aplikace
            </documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="BRANCH"/>
            <enumeration value="EMAIL"/>
            <enumeration value="EMAILCH"/>
            <enumeration value="EMAILDP"/>
            <enumeration value="EMAILPR"/>
            <enumeration value="IB"/>
            <enumeration value="MESSENGER"/>
            <enumeration value="POST"/>
            <enumeration value="REGISTER"/>
            <enumeration value="SPB"/>
        </restriction>
    </simpleType>

    <simpleType name="ProcessVariableTypeTO">
        <restriction base="string">
            <enumeration value="BOOLEAN"/>
            <enumeration value="INTEGER"/>
            <enumeration value="LONG"/>
            <enumeration value="STRING"/>
        </restriction>
    </simpleType>

    <simpleType name="ProcessJobTypeTO">
        <restriction base="string">
            <enumeration value="MESSAGE"/>
            <enumeration value="TIMER"/>
        </restriction>
    </simpleType>

    <complexType name="GroupFilterTO">
        <sequence>
            <element name="cuid" type="long" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>customer cuid</documentation>
                </annotation>
            </element>
            <element name="envelopeId" type="long" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>AMS envelope ID</documentation>
                </annotation>
            </element>
            <element name="groupType" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>document group / type</documentation>
                </annotation>
            </element>
            <element name="groupRelation" type="string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>document group relation / detail</documentation>
                </annotation>
            </element>
            <element name="groupId" type="long" minOccurs="0">
                <annotation>
                    <documentation>document group ID</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="DeliveredDocumentFilterTO">
        <complexContent>
            <extension base="Q1:FilterDeliveredDocument">
                <sequence>
                    <element name="envelopeId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>AMS envelope ID</documentation>
                        </annotation>
                    </element>
                    <element name="id" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document ID in AMS</documentation>
                        </annotation>
                    </element>
                    <element name="documentType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document type in MDM - ID_CARD, GUN, PASSPORT, ...</documentation>
                        </annotation>
                    </element>
                    <element name="status" type="Q1:DocumentStatusTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>status of document</documentation>
                        </annotation>
                    </element>
                    <element name="statusOrigin" type="Q1:DocumentStatusOriginTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>origin of document status</documentation>
                        </annotation>
                    </element>
                    <element name="migrationFlag" type="Q1:DocumentMigrationFlagTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>migration flag of document</documentation>
                        </annotation>
                    </element>
                    <element name="archiveStatus" type="Q1:ArchiveStatusTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>status of document</documentation>
                        </annotation>
                    </element>
                    <element name="documentName" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document description</documentation>
                        </annotation>
                    </element>
                    <element name="deliveryChannel" type="Q1:DeliveryChannelTO" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>delivery channel by which the document was delivered</documentation>
                        </annotation>
                    </element>
                    <element name="cuid" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>customer cuid</documentation>
                        </annotation>
                    </element>
                    <element name="barCode" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>search code</documentation>
                        </annotation>
                    </element>
                    <element name="onlyElectronicVersion" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is only electronic version?</documentation>
                        </annotation>
                    </element>
                    <element name="notFound" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>not found?</documentation>
                        </annotation>
                    </element>
                    <element name="discarded" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is discarded?</documentation>
                        </annotation>
                    </element>
                    <element name="archiveNumber" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>archive number</documentation>
                        </annotation>
                    </element>
                    <element name="lentToOperatorId" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>id of person who was it lent</documentation>
                        </annotation>
                    </element>
                    <element name="documentNumber" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>identification number of document (identification card, driver's license, ...)</documentation>
                        </annotation>
                    </element>
                    <element name="country" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>country of person</documentation>
                        </annotation>
                    </element>
                    <element name="issuedDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>issue date of document</documentation>
                        </annotation>
                    </element>
                    <element name="fromCreatedDate" type="date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>all documents created after date</documentation>
                        </annotation>
                    </element>
                    <element name="includeStatusesFromDifferentEnvelopeSameCuid" type="Q1:DocumentStatusTO" minOccurs="0" maxOccurs="unbounded">
                        <annotation>
                            <documentation>
                                the list of statuses of documents from other envelope than the one set here in the filter by envelopeId and same cuid
                            </documentation>
                        </annotation>
                    </element>
                    <element name="includeCustomerInfo" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                If set to true, service returns also customer information (name,surname,titles) based on cuid
                            </documentation>
                        </annotation>
                    </element>
                    <element name="proofId" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                Proof ID
                            </documentation>
                        </annotation>
                    </element>
                    <element name="groupType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document group type</documentation>
                        </annotation>
                    </element>
                    <element name="groupRelation" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>document group relation</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <xs:complexType name="IdentityInfoTO">
        <xs:complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="documentNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Identity document ID filled by operator</documentation>
                        </annotation>
                    </element>
                    <element name="validTo" type="xs:date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Date of document validity</documentation>
                        </annotation>
                    </element>
                    <element name="country" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Country of origin (CIF codelist)</documentation>
                        </annotation>
                    </element>
                    <element name="maritalStatus" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Marital status (CIF codelist)</documentation>
                        </annotation>
                    </element>
                    <element name="code" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Code check / validity check</documentation>
                        </annotation>
                    </element>
                    <element name="invalidationDate" type="xs:date" minOccurs="0">
                        <annotation>
                            <documentation>Date of document invalidation</documentation>
                        </annotation>
                    </element>
                    <element name="facePictureDmsId" type="xs:string" minOccurs="0">
                        <annotation>
                            <documentation>DMS ID (uuid) of a face image if any was found on the document.</documentation>
                        </annotation>
                    </element>
                    <element name="issuer" type="xs:string" minOccurs="0">
                        <annotation>
                            <documentation>Issuer name.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="IncomeInfoTO">
        <xs:complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="inExecution" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is in confiscation</documentation>
                        </annotation>
                    </element>
                    <element name="inTermination" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is in termination</documentation>
                        </annotation>
                    </element>
                    <element name="inProbationPeriod" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>is in probation period</documentation>
                        </annotation>
                    </element>
                    <element name="last12MAvgNetIncome" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Průměrný čistý měsíční příjem za posledních 12 M.</documentation>
                        </annotation>
                    </element>
                    <element name="last1MGamblingAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Amount of transactions categorized as gambling for last month</documentation>
                        </annotation>
                    </element>
                    <element name="last3MGamblingAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Amount of transactions categorized as gambling for last three months</documentation>
                        </annotation>
                    </element>
                    <element name="lastUnemploymentBenefitDate" type="xs:date" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>date of last unemployment benefit</documentation>
                        </annotation>
                    </element>
                    <element name="incomePairedAutomatically" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>flag whether income was automatically paired to application data</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="StudentConfirmationInfoTO">
        <xs:complexContent>
            <extension base="Q1:AuditedTransferObjectWithIdentity">
                <sequence>
                    <element name="ic" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>identification number of university.</documentation>
                        </annotation>
                    </element>
                    <element name="faculty" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>faculty of university.</documentation>
                        </annotation>
                    </element>
                    <element name="grade" type="xs:int" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>current grade of study.</documentation>
                        </annotation>
                    </element>
                    <element name="isicNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>isic number</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HardCheckTO">
        <xs:sequence>
            <xs:element name="envelopePersonCuid" type="xs:long" minOccurs="1" maxOccurs="1"/>
            <xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="operation" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1"/>
            <xs:element name="completionSubjectId" type="xs:long" minOccurs="0" maxOccurs="1"/>
            <xs:element name="businessProcess" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="envelopeId" type="xs:long" minOccurs="0" maxOccurs="1"/>
            <xs:element name="documentId" type="xs:long" minOccurs="0" maxOccurs="1"/>
            <xs:element name="workflowId" type="xs:long" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DrawdownConditionTO">
        <xs:complexContent>
            <xs:extension base="Q1:AuditedTransferObjectWithIdentity">
                <xs:sequence>
                    <xs:element name="type" type="Q1:DrawdownConditionTypeTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of condition pre/post drawn.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="state" type="Q1:DrawdownConditionStateTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>State of condition</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="order" type="xs:int" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Order in GUI</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="code" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>MDM code of the condition (for free confition we have the hash of the name)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Name of the condition taken from MDM (for free confition it's defined by the AMG operator)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Operator note</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contractText" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Text from contract, that defined condition</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="conditionDays" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Definuje počet dní po načerpání, do kdy se má splnit daná podmínka</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="applicationId" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of application</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="trancheId" type="long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of mortgage tranche.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="purposeId" type="long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of mortgage purpose.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="drawdownType" type="Q1:DrawdownTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Drawdown type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <simpleType name="DrawdownConditionTypeTO">
        <restriction base="string">
            <enumeration value="DRAWDOWN_PRE_CONDITION" />
            <enumeration value="DRAWDOWN_POST_CONDITION" />
        </restriction>
    </simpleType>

    <simpleType name="DrawdownConditionStateTO">
        <annotation>
            <documentation>Condition status.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PASSED"/>
            <enumeration value="FAILED"/>
            <enumeration value="UNKNOWN"/>
        </restriction>
    </simpleType>

    <simpleType name="DrawdownTypeTO">
        <restriction base="string">
            <enumeration value="FIRST" />
            <enumeration value="FINAL" />
        </restriction>
    </simpleType>

    <complexType name="OverdraftApplicationTO">
        <annotation>
            <documentation>Overdraft application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AbstractLoanApplicationTO">
                <sequence>
                    <element name="overdraftAmount" type="decimal" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>overdraftAmount</documentation>
                        </annotation>
                    </element>
                    <element name="requestedOverdraftAmount" type="decimal" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>overdraftAmount</documentation>
                        </annotation>
                    </element>
                    <element name="idAccount" type="long" minOccurs="0">
                        <annotation>
                            <documentation>
                                idAccount
                            </documentation>
                        </annotation>
                    </element>
                    <element name="accountNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                accountNumber
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocHtml" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                idPrecontractDocHtml
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocPdf" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                idPrecontractDocPdf
                            </documentation>
                        </annotation>
                    </element>
                    <element name="internalCode" type="string" minOccurs="0">
                        <annotation>
                            <documentation>
                                internalCode
                            </documentation>
                        </annotation>
                    </element>
                    <element name="loanApplicationType" type="string" minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                NORMAL,REFINANCE,REFINANCING_R4,CONSOLIDATION,MORTGAGE_REFINANCE,OVERDRAFT
                            </documentation>
                        </annotation>
                    </element>
                    <element name="applicationDataRecovery" type="boolean" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>applicationDataRecovery</documentation>
                        </annotation>
                    </element>
                    <element name="contactedBy" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Operator who contacted client during underwriting. Null when the client was
                                not contacted.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="contactedTime" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Time when client was contacted during underwriting. Null when the client was
                                not contacted.
                            </documentation>
                        </annotation>
                    </element>
                    <element name="requestType" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Request type (currently there are only 2 possible values - comment, specific
                                case)
                            </documentation>
                        </annotation>
                    </element>
                    <element name="requestDescription" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Request description.</documentation>
                        </annotation>
                    </element>
                    <element name="annualPercentageRate" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>RPSN</documentation>
                        </annotation>
                    </element>
                    <element name="interestRate" type="decimal" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>overdraft interest rate</documentation>
                        </annotation>
                    </element>
                    <element name="overdraftId" type="long" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>overdraft id</documentation>
                        </annotation>
                    </element>

                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="SplitPaymentApplicationTO">
        <annotation>
            <documentation>Split payment application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AbstractLoanApplicationTO">
                <sequence>
                    <element name="idRealizedTransaction" type="long" minOccurs="0">
                        <annotation>
                            <documentation>ID platby k rozložení</documentation>
                        </annotation>
                    </element>
                    <element name="idBankAccount" type="long" minOccurs="0">
                        <annotation>
                            <documentation>ID účtu, ze kterého byla realizována platba k rozložení</documentation>
                        </annotation>
                    </element>
                    <element name="accountNumber" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Číslo účtu, ze kterého byla realizována platba k rozložení</documentation>
                        </annotation>
                    </element>
                    <element name="realizationDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Datum provedení platby</documentation>
                        </annotation>
                    </element>
                    <element name="splitPaymentAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Výše úvěru (rozložení platby)</documentation>
                        </annotation>
                    </element>
                    <element name="repaymentPeriod" type="int" minOccurs="0">
                        <annotation>
                            <documentation>Počet splátek</documentation>
                        </annotation>
                    </element>
                    <element name="utilizationDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Datum čerpání</documentation>
                        </annotation>
                    </element>
                    <element name="instalmentAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Výše splátky</documentation>
                        </annotation>
                    </element>
                    <element name="lastInstalmentAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Výše poslední splátky (může se lišit od standardní výše splátky)</documentation>
                        </annotation>
                    </element>
                    <element name="providingCharges" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Poplatek za poskytnutí služby</documentation>
                        </annotation>
                    </element>
                    <element name="interestRate" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Úroková sazba v %</documentation>
                        </annotation>
                    </element>
                    <element name="annualPercentageRate" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Roční procentní sazba nákladů v %</documentation>
                        </annotation>
                    </element>
                    <element name="totalAmount" type="decimal" minOccurs="0">
                        <annotation>
                            <documentation>Celkové náklady na úvěr (odpovídá součtu všech splátek a poplatku za poskytnutí služby)</documentation>
                        </annotation>
                    </element>
                    <element name="firstInstalmentDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Datum první splátky</documentation>
                        </annotation>
                    </element>
                    <element name="lastInstalmentDate" type="date" minOccurs="0">
                        <annotation>
                            <documentation>Datum poslední splátky</documentation>
                        </annotation>
                    </element>
                    <element name="instalmentDay" type="int" minOccurs="0">
                        <annotation>
                            <documentation>Den v měsíci pro splácení</documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocHtml" type="string" minOccurs="0">
                        <annotation>
                            <documentation>ID dokumentu Předsmluvní informace v PDF formátu</documentation>
                        </annotation>
                    </element>
                    <element name="idPrecontractDocPdf" type="string" minOccurs="0">
                        <annotation>
                            <documentation>ID dokumentu Předsmluvní informace v HTML formátu</documentation>
                        </annotation>
                    </element>
                    <element name="loanApplicationType" type="string">
                        <annotation>
                            <documentation>Typ úvěrové žádosti -  NORMAL,REFINANCE,REFINANCING_R4,CONSOLIDATION,MORTGAGE_REFINANCE,OVERDRAFT
                            </documentation>
                        </annotation>
                    </element>
                    <element name="idProductVariant" type="long" minOccurs="0">
                        <annotation>
                            <documentation>ID varianty produktu v OBS</documentation>
                        </annotation>
                    </element>
                    <element name="productVariant" type="string" minOccurs="0">
                        <annotation>
                            <documentation>Kód varianty produktu v OBS</documentation>
                        </annotation>
                    </element>
                    <element name="idSplitPayment" type="long" minOccurs="0">
                        <annotation>
                            <documentation>Vazba na vytvořený úvěr v OBS (BC_LOAN.IDLOAN)</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <xs:complexType name="UserFilterSettingsTO">
        <sequence>
            <element name="dashboardId" type="xs:string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Identification of dashboard</documentation>
                </annotation>
            </element>
            <element name="sortBy" type="Q1:SortByTO" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>Sort filter by field</documentation>
                </annotation>
            </element>
            <element name="filters" type="Q1:FilterSettingTO" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>Fields to filter</documentation>
                </annotation>
            </element>
        </sequence>
    </xs:complexType>

    <xs:complexType name="FilterSettingTO">
        <sequence>
            <element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
                <annotation>
                    <documentation>Identification name of filter</documentation>
                </annotation>
            </element>
            <element name="values" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>Values of filter presets.</documentation>
                </annotation>
            </element>
        </sequence>
    </xs:complexType>

    <simpleType name="DrawdownPreconditionsStatusTO">
        <annotation>
            <documentation>Status of Drawdown preconditions.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="ALL_UNKNOWN"/>
            <enumeration value="ONE_PASSED"/>
            <enumeration value="ONE_FAILED"/>
            <enumeration value="ALL_PASSED"/>
        </restriction>
    </simpleType>

    <simpleType name="PersonalDataChangeTypeTO">
        <annotation>
            <documentation>Field changed for PersonalDataChange</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="DOCUMENT_TYPE"/>
            <enumeration value="DOCUMENT_NUMBER"/>
            <enumeration value="DOCUMENT_VALID_TO"/>
            <enumeration value="EMBOSSED_NAME"/>
            <enumeration value="MAILING_ADDRESS"/>
            <enumeration value="PERMANENT_ADDRESS"/>
            <enumeration value="CITIZENSHIPS"/>
            <enumeration value="MANUAL"/>
            <enumeration value="MARITAL_STATUS"/>
            <enumeration value="GENDER"/>
            <enumeration value="HONOUR_AFTER_NAME"/>
            <enumeration value="SALUTATION"/>
            <enumeration value="LAST_NAME"/>
            <enumeration value="FIRST_NAME"/>
            <enumeration value="HONOUR_BEFORE_NAME"/>
        </restriction>
    </simpleType>

    <complexType name="MobileRegistrationApplicationTO">
        <annotation>
            <documentation>Mobile registration application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <sequence>
                    <element name="deviceWirelessName" type="string">
                        <annotation>
                            <documentation>Device wireless name.</documentation>
                        </annotation>
                    </element>
                    <element name="rotp" type="string" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>One-time registration password.</documentation>
                        </annotation>
                    </element>
                    <element name="lastSmsSentAt" type="dateTime" minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>Time when was the last registration SMS sent.</documentation>
                        </annotation>
                    </element>
                    <element name="smsesSentLastDay" type="int">
                        <annotation>
                            <documentation>Number of SMSes sent last day.</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="MobileOnboardingApplicationTO">
        <annotation>
            <documentation>Mobile onboarding application.</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:ApplicationTO">
                <xs:sequence>
                    <xs:element name="identificationProcess" minOccurs="0" type="Q1:IdentificationProcessBankIdInfo">
                        <xs:annotation>
                            <xs:documentation>
                                Identification process info
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="IdentityChangeVerificationTO">
        <sequence>
            <element name="identityVerification" type="Q1:IdentityVerificationTO" maxOccurs="unbounded">
                <annotation>
                    <documentation>Verification of customer's new identities.</documentation>
                </annotation>
            </element>
            <element name="verificationResult" type="string">
                <annotation>
                    <documentation>Aggregated verification result from MDM codelist IDENTITY_VERIFICATION_RESULT (https://wiki.airbank.cz/x/zYGBDw).</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="IdentityVerificationTO">
        <sequence>
            <element name="identity" type="Q1:IdentityTO">
                <annotation>
                    <documentation>Verified identity.</documentation>
                </annotation>
            </element>
            <element name="identityVerificationResult" type="string">
                <annotation>
                    <documentation>Verification result from MDM codelist IDENTITY_VERIFICATION_RESULT (https://wiki.airbank.cz/x/zYGBDw).</documentation>
                </annotation>
            </element>
            <element name="relatedCuid" type="long" minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>CUIDs of related customers (for some results).</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="IdentityTO">
        <sequence>
            <element name="id" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID of identity.</documentation>
                </annotation>
            </element>
            <choice>
                <element name="quintupleIdentity" type="Q1:QuintupleIdentityTO"/>
                <element name="birthNumberIdentity" type="Q1:BirthNumberIdentityTO"/>
                <element name="aifoIdentity" type="Q1:AifoIdentityTO"/>
            </choice>
            <element name="status" type="string">
                <annotation>
                    <documentation>Identity status from MDM codelist IDENTITY_STATUS (https://wiki.airbank.cz/x/CIJZDw).</documentation>
                </annotation>
            </element>
            <element name="toShare" type="boolean">
                <annotation>
                    <documentation>Flag indicating whether identity share is allowed.</documentation>
                </annotation>
            </element>
            <element name="compromised " type="boolean">
                <annotation>
                    <documentation>Flag indicating whether identity is compromised.</documentation>
                </annotation>
            </element>
            <element name="toDisplay" type="string" minOccurs="0">
                <annotation>
                    <documentation>Data to be displayed.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="QuintupleIdentityTO">
        <sequence>
            <element name="firstName" type="string">
                <annotation>
                    <documentation>First name.</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string">
                <annotation>
                    <documentation>Last name.</documentation>
                </annotation>
            </element>
            <element name="birthDate" type="date">
                <annotation>
                    <documentation>Birth date.</documentation>
                </annotation>
            </element>
            <element name="birthPlace" type="Q1:BirthPlaceTO">
                <annotation>
                    <documentation>Birth place.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="BirthNumberIdentityTO">
        <sequence>
            <element name="birthNumber" type="string">
                <annotation>
                    <documentation>Birth number.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="AifoIdentityTO">
        <sequence>
            <element name="aifo" type="string">
                <annotation>
                    <documentation>ID from register (in czech: agendový identifikator fyzické osoby).</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="BirthPlaceTO">
        <sequence>
            <element name="discriminator" type="string">
                <annotation>
                    <documentation>Birth place discriminator from MDM codelist BIRTH_PLACE_DISCRIMINATOR (https://wiki.airbank.cz/x/uYVZDw).</documentation>
                </annotation>
            </element>
            <choice minOccurs="0">
                <element name="ruianBirthPlace" type="Q1:RuianBirthPlaceTO"/>
                <element name="countryBirthPlace" type="Q1:CountryBirthPlaceTO"/>
                <element name="outsideCountryBirthPlace" type="Q1:OutsideCountryBirthPlaceTO"/>
            </choice>
        </sequence>
    </complexType>

    <complexType name="RuianBirthPlaceTO">
        <sequence>
            <choice>
                <element name="townCode" type="long">
                    <annotation>
                        <documentation>Code of town from RUIAN.</documentation>
                    </annotation>
                </element>
                <element name="pragueMunicipalDistrictCode" type="long">
                    <annotation>
                        <documentation>Code of Prague municipal district from RUIAN.</documentation>
                    </annotation>
                </element>
            </choice>
        </sequence>
    </complexType>

    <complexType name="CountryBirthPlaceTO">
        <sequence>
            <element name="countryAlpha2Code" type="string">
                <annotation>
                    <documentation>Two letter code of country.</documentation>
                </annotation>
            </element>
            <element name="location" type="string">
                <annotation>
                    <documentation>Text representation of exact location for given country.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="OutsideCountryBirthPlaceTO">
        <sequence>
            <element name="place" type="string">
                <annotation>
                    <documentation>Text representation of vague location (for example "On the sea").</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="IdentificationProcessBusinessResultTO">
        <annotation>
            <documentation>Identification process business result.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="IDENTIFIED"/>
            <enumeration value="CITIZEN_NOT_FOUND_IN_ROB"/>
            <enumeration value="CITIZEN_DUPLICITY_IN_ROB"/>
            <enumeration value="FOREIGNER_NOT_FOUND_IN_ROB"/>
            <enumeration value="FOREIGNER_DUPLICITY_IN_ROB"/>
            <enumeration value="NO_VERIFIED_IDENTIFICATION_DOCUMENT"/>
            <enumeration value="INVALID_IDENTIFICATION_DOCUMENT_NUMBER"/>
            <enumeration value="IDENTIFICATION_PROCESS_ALREADY_STARTED"/>
            <enumeration value="REGISTRY_PERSON_IDENTIFICATION_ERROR"/>
            <enumeration value="AIFO_VERIFICATON_OUT_OF_CORRECT"/>
        </restriction>
    </simpleType>

    <simpleType name="IdentificationProcessAlertTypeTO">
        <annotation>
            <documentation>Alerts types for identification processes filtering.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PENDING"/>
            <enumeration value="CLEARED"/>
        </restriction>
    </simpleType>

    <complexType name="IdentificationProcessTO">
        <sequence>
            <element name="id" type="long">
                <annotation>
                    <documentation>ID of identification process.</documentation>
                </annotation>
            </element>
            <element name="created" type="dateTime">
                <annotation>
                    <documentation>Created time.</documentation>
                </annotation>
            </element>
            <element name="batchId" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID of identification process batch (for identification process created by batch).</documentation>
                </annotation>
            </element>
            <element name="personalDataChangeLogId" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID of personal data change log record (when data change is triggered).</documentation>
                </annotation>
            </element>
            <element name="personalDataChangeLogCreated" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Timestamp of creating personal data change log.</documentation>
                </annotation>
            </element>
            <element name="personalDataChangeLogFlag" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Flag indicating whether personal data change log was successfully processed.</documentation>
                </annotation>
            </element>
            <element name="personalDataChangeLogStatus" type="Q1:PersonalDataChangeLogStatusTO" minOccurs="0">
                <annotation>
                    <documentation>Status of personal data change log.</documentation>
                </annotation>
            </element>
            <element name="cuid" type="long" minOccurs="0">
                <annotation>
                    <documentation>Customer ID.</documentation>
                </annotation>
            </element>
            <element name="firstName" type="string" minOccurs="0">
                <annotation>
                    <documentation>First name.</documentation>
                </annotation>
            </element>
            <element name="lastName" type="string" minOccurs="0">
                <annotation>
                    <documentation>Last name.</documentation>
                </annotation>
            </element>
            <element name="birthDate" type="date" minOccurs="0">
                <annotation>
                    <documentation>Birth date.</documentation>
                </annotation>
            </element>
            <element name="birthNumber" type="string" minOccurs="0">
                <annotation>
                    <documentation>Birth number.</documentation>
                </annotation>
            </element>
            <element name="essentialFullRobIdentificationResult" type="Q1:IdentifyPersonResultTO" minOccurs="0">
                <annotation>
                    <documentation>Result of essential full identification in ROB.</documentation>
                </annotation>
            </element>
            <element name="candidateRobIdentificationResult" type="Q1:IdentifyPersonResultTO" minOccurs="0">
                <annotation>
                    <documentation>Result of candidate identification in ROB.</documentation>
                </annotation>
            </element>
            <element name="candidateAiseoIdentificationResult" type="Q1:IdentifyPersonResultTO" minOccurs="0">
                <annotation>
                    <documentation>Result of candidate identification in AISEO.</documentation>
                </annotation>
            </element>
            <element name="checkFullRobIdentificationResult" type="Q1:IdentifyPersonResultTO" minOccurs="0">
                <annotation>
                    <documentation>Result of check full identification in ROB.</documentation>
                </annotation>
            </element>
            <element name="identityCardsEvaluationResult" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Result of identity cards evaluation.</documentation>
                </annotation>
            </element>
            <element name="status" type="Q1:IdentificationProcessStatusTO">
                <annotation>
                    <documentation>Identification process status.</documentation>
                </annotation>
            </element>
            <element name="businessResult" type="Q1:IdentificationProcessBusinessResultTO" minOccurs="0">
                <annotation>
                    <documentation>Business result.</documentation>
                </annotation>
            </element>
            <element name="cleared" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Time when identification process alert was marked as cleared by operator (null for not cleared yet).</documentation>
                </annotation>
            </element>
            <element name="clearedBy" type="string" minOccurs="0">
                <annotation>
                    <documentation>Employee number of operator who marked identification process alert as cleared (null for not cleared yet).</documentation>
                </annotation>
            </element>
            <element name="deliveredDocumentId" type="long" minOccurs="0">
                <annotation>
                    <documentation>ID of the delivered document.</documentation>
                </annotation>
            </element>
            <element name="deliveredDocumentType" type="string" minOccurs="0">
                <annotation>
                    <documentation>Type of the delivered document.</documentation>
                </annotation>
            </element>
            <element name="deliveredDocumentNumber" type="string" minOccurs="0">
                <annotation>
                    <documentation>Number of the delivered document.</documentation>
                </annotation>
            </element>
            <element name="businessProcessEvent" type="string">
                <annotation>
                    <documentation>Business process event.</documentation>
                </annotation>
            </element>
            <element name="error" type="string" minOccurs="0">
                <annotation>
                    <documentation>Information about unexpected error.</documentation>
                </annotation>
            </element>
            <element name="storeAifoRequested" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Timestamp of store AIFO request.</documentation>
                </annotation>
            </element>
            <element name="storeAifoFlag" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Flag indicating whether AIFO was successfully stored.</documentation>
                </annotation>
            </element>
            <element name="storeAifoResult" type="string" minOccurs="0">
                <annotation>
                    <documentation>Result of storing AIFO.</documentation>
                </annotation>
            </element>
            <element name="enableAutoUpdatesRequested" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Timestamp of enable auto updates request.</documentation>
                </annotation>
            </element>
            <element name="enableAutoUpdatesFlag" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Flag indicating whether auto updates was successfully enabled.</documentation>
                </annotation>
            </element>
            <element name="enableAutoUpdatesResult" type="string" minOccurs="0">
                <annotation>
                    <documentation>Result of enabling auto updates.</documentation>
                </annotation>
            </element>
            <element name="createPersonalDataProofRequested" type="dateTime" minOccurs="0">
                <annotation>
                    <documentation>Timestamp of create personal data proof request.</documentation>
                </annotation>
            </element>
            <element name="createPersonalDataProofFlag" type="boolean" minOccurs="0">
                <annotation>
                    <documentation>Flag indicating whether personal data proof was successfully created.</documentation>
                </annotation>
            </element>
            <element name="createPersonalDataProofResult" type="string" minOccurs="0">
                <annotation>
                    <documentation>Result of creating personal data proof.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <simpleType name="IdentifyPersonResultTO">
        <annotation>
            <documentation>Alerts types for identification processes filtering.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="FOUND"/>
            <enumeration value="NOT_FOUND"/>
            <enumeration value="MULTIMATCH"/>
            <enumeration value="ERROR"/>
            <enumeration value="EXTERNAL_SYSTEM_OVERLOAD_DETECTED"/>
        </restriction>
    </simpleType>

    <simpleType name="IdentificationProcessStatusTO">
        <annotation>
            <documentation>Status of identification process.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="CREATED"/>
            <enumeration value="IN_PROGRESS"/>
            <enumeration value="SUCCEEDED"/>
            <enumeration value="FAILED"/>
        </restriction>
    </simpleType>

    <simpleType name="PersonalDataChangeLogStatusTO">
        <annotation>
            <documentation>Status of personal data change log.</documentation>
        </annotation>
        <restriction base="string">
            <enumeration value="PENDING"/>
            <enumeration value="IN_PROGRESS"/>
            <enumeration value="PROCESSED"/>
            <enumeration value="FAILED"/>
            <enumeration value="REJECTED"/>
        </restriction>
    </simpleType>

    <complexType name="NewIdentificationTO">
        <sequence>
            <element name="businessProcessEvent" type="string" minOccurs="0">
                <annotation>
                    <documentation>Business process event of identification change.</documentation>
                </annotation>
            </element>
            <element name="employeeNumber" type="string" minOccurs="0">
                <annotation>
                    <documentation>Employee number of operator who made identification change.</documentation>
                </annotation>
            </element>
            <element name="identification" type="Q1:IdentificationTO" minOccurs="0">
                <annotation>
                    <documentation>New identification data.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="IdentificationTO">
        <sequence>
            <choice>
                <element name="simpleIdentification" type="Q1:SimpleIdentificationTO"/>
                <element name="standardIdentification" type="Q1:StandardIdentificationTO"/>
                <element name="fullIdentification" type="Q1:FullIdentificationTO"/>
            </choice>
        </sequence>
    </complexType>

    <complexType name="SimpleIdentificationTO">
        <sequence>
            <element name="quintupleIdentity" type="Q1:QuintupleIdentityTO">
                <annotation>
                    <documentation>Quintuple identity.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="StandardIdentificationTO">
        <sequence>
            <element name="quintupleIdentity" type="Q1:QuintupleIdentityTO">
                <annotation>
                    <documentation>Quintuple identity.</documentation>
                </annotation>
            </element>
            <element name="aifoIdentity" type="Q1:AifoIdentityTO">
                <annotation>
                    <documentation>AIFO identity.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="FullIdentificationTO">
        <sequence>
            <element name="quintupleIdentity" type="Q1:QuintupleIdentityTO">
                <annotation>
                    <documentation>Quintuple identity.</documentation>
                </annotation>
            </element>
            <element name="birthNumberIdentity" type="Q1:BirthNumberIdentityTO">
                <annotation>
                    <documentation>Birth number identity.</documentation>
                </annotation>
            </element>
            <element name="aifoIdentity" type="Q1:AifoIdentityTO" minOccurs="0">
                <annotation>
                    <documentation>AIFO identity.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="IdentificationValidationResultTO">
        <sequence>
            <element name="code" type="string">
                <annotation>
                    <documentation>Validation error code.</documentation>
                </annotation>
            </element>
            <element name="attributeName" type="string" minOccurs="0">
                <annotation>
                    <documentation>Validated attribute name.</documentation>
                </annotation>
            </element>
            <element name="message" type="string" minOccurs="0">
                <annotation>
                    <documentation>Error message.</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>
</schema>