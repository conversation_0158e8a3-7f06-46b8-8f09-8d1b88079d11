<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/insuranceChange"
                  targetNamespace="http://airbank.cz/ams/ws/application/insuranceChange">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/insuranceChange">
            <xs:include schemaLocation="AMSInsuranceChangeApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Settings task -->
    <wsdl:message name="InitSettingsRequest">
        <wsdl:part element="InitSettingsRequest" name="InitSettingsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSettingsResponse">
        <wsdl:part element="InitSettingsResponse" name="InitSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSettingsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSettingsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSettingsRequest">
        <wsdl:part element="UpdateSettingsRequest" name="UpdateSettingsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSettingsResponse">
        <wsdl:part element="UpdateSettingsResponse" name="UpdateSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSettingsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSettingsFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>
    
    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>
    
    <wsdl:portType name="InsuranceChangeApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for insurance change. Uses cuid and idProfile from tracking context to identify customer and profile.
                Generated business faults:
                - Application.Reject.CantApplyForInsuranceChangeProduct - User can't apply for loan product!
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous insurance change application in the VERIFY status
                - Application.UnsignedApplicationExists - Unsigned application for a specific product already exists
                - Application.SignedApplicationExists - Signed application for a specific product exists
                - CLIENT_TOO_OLD_FOR_INSURANCE - Client is too old for applying for new insurance
                - CLIENT_TOO_YOUNG_FOR_INSURANCE - Client is too young for applying for new insurance
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSettings">
            <wsdl:documentation>Operation used to initialize the settingsTask.</wsdl:documentation>
            <wsdl:input message="InitSettingsRequest"/>
            <wsdl:output message="InitSettingsResponse"/>
            <wsdl:fault name="InitSettingsFault" message="InitSettingsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSettings">
            <wsdl:documentation>Operation used to update data for the settingsTask.

                For next transition is required at least one insurance change.
                Generated business faults:
                - DUPLICATE_INSURANCE_TYPE - Multiple variants with same insurance type.
                - INSURANCE_VARIANT_NOT_ALLOWED - Forbidden insurance variant.
                - NO_SELECTED_INSURANCE_VARIANT - There is required at least one insurance variant.
            </wsdl:documentation>
            <wsdl:input message="UpdateSettingsRequest"/>
            <wsdl:output message="UpdateSettingsResponse"/>
            <wsdl:fault name="UpdateSettingsFault" message="UpdateSettingsFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>
    </wsdl:portType>
    
    <wsdl:binding name="InsuranceChangeApplicationBinding" type="InsuranceChangeApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSettings">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSettingsFault">
                <soap:fault name="InitSettingsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSettings">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSettingsFault">
                <soap:fault name="UpdateSettingsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    
    <wsdl:service name="InsuranceChangeApplicationService">
        <wsdl:documentation>Service is providing operations related to existing customer insurance change application.

            The application has the following task IDs:
            - settingsTask - page with insurance change settings (types of insurance and their coverage)

            Standalone calls:
            - cancel - cancels loan application

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGTH, INVALID_EXACT_LENGTH - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
            - DATE_RANGE_VALIDATOR - year is not in range 1900 - 2999
        </wsdl:documentation>
        <wsdl:port name="InsuranceChangeApplicationPort" binding="InsuranceChangeApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/insuranceChange"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>