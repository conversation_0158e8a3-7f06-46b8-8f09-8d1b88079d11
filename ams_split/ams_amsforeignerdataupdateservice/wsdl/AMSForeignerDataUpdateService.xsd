<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/process/foreignerdata/update"
           targetNamespace="http://airbank.cz/ams/ws/process/foreignerdata/update">

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="batch" type="Batch" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Person identification and Identity Card numbers.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="batchId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>ID of identification process batch.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Batch">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:documentation>unique identifier of person, defined in CIF system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="aifo" type="xs:string">
                <xs:annotation>
                    <xs:documentation>AIFO of customer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Id (in the AMS DB) of the application envelope.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
