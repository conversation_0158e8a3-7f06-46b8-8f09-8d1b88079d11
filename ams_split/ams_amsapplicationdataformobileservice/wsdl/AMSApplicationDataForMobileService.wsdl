<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/common/applicationdata/mobile"
                  targetNamespace="http://airbank.cz/ams/ws/application/common/applicationdata/mobile">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/common/applicationdata/mobile">
            <xs:include schemaLocation="AMSApplicationDataForMobileService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <!-- ApplicationDataSummary task -->
    <wsdl:message name="InitApplicationDataSummaryRequest">
        <wsdl:part element="InitApplicationDataSummaryRequest" name="InitApplicationDataSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitApplicationDataSummaryResponse">
        <wsdl:part element="InitApplicationDataSummaryResponse" name="InitApplicationDataSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitApplicationDataSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitApplicationDataSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateApplicationDataSummaryRequest">
        <wsdl:part element="UpdateApplicationDataSummaryRequest" name="UpdateApplicationDataSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateApplicationDataSummaryResponse">
        <wsdl:part element="UpdateApplicationDataSummaryResponse" name="UpdateApplicationDataSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateApplicationDataSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateApplicationDataSummaryFault"/>
    </wsdl:message>

    <!-- Education task -->
    <wsdl:message name="InitEducationRequest">
        <wsdl:part element="InitEducationRequest" name="InitEducationRequest"/>
    </wsdl:message>
    <wsdl:message name="InitEducationResponse">
        <wsdl:part element="InitEducationResponse" name="InitEducationResponse"/>
    </wsdl:message>
    <wsdl:message name="InitEducationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitEducationFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateEducationRequest">
        <wsdl:part element="UpdateEducationRequest" name="UpdateEducationRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateEducationResponse">
        <wsdl:part element="UpdateEducationResponse" name="UpdateEducationResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateEducationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateEducationFault"/>
    </wsdl:message>

    <!-- MaritalStatus task -->
    <wsdl:message name="InitMaritalStatusRequest">
        <wsdl:part element="InitMaritalStatusRequest" name="InitMaritalStatusRequest"/>
    </wsdl:message>
    <wsdl:message name="InitMaritalStatusResponse">
        <wsdl:part element="InitMaritalStatusResponse" name="InitMaritalStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="InitMaritalStatusFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitMaritalStatusFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateMaritalStatusRequest">
        <wsdl:part element="UpdateMaritalStatusRequest" name="UpdateMaritalStatusRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateMaritalStatusResponse">
        <wsdl:part element="UpdateMaritalStatusResponse" name="UpdateMaritalStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateMaritalStatusFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateMaritalStatusFault"/>
    </wsdl:message>

    <!-- RelationToDebtor task -->
    <wsdl:message name="InitRelationToDebtorRequest">
        <wsdl:part element="InitRelationToDebtorRequest" name="InitRelationToDebtorRequest"/>
    </wsdl:message>
    <wsdl:message name="InitRelationToDebtorResponse">
        <wsdl:part element="InitRelationToDebtorResponse" name="InitRelationToDebtorResponse"/>
    </wsdl:message>
    <wsdl:message name="InitRelationToDebtorFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitRelationToDebtorFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateRelationToDebtorRequest">
        <wsdl:part element="UpdateRelationToDebtorRequest" name="UpdateRelationToDebtorRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateRelationToDebtorResponse">
        <wsdl:part element="UpdateRelationToDebtorResponse" name="UpdateRelationToDebtorResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateRelationToDebtorFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateRelationToDebtorFault"/>
    </wsdl:message>

    <!-- Housing task -->
    <wsdl:message name="InitHousingRequest">
        <wsdl:part element="InitHousingRequest" name="InitHousingRequest"/>
    </wsdl:message>
    <wsdl:message name="InitHousingResponse">
        <wsdl:part element="InitHousingResponse" name="InitHousingResponse"/>
    </wsdl:message>
    <wsdl:message name="InitHousingFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitHousingFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateHousingRequest">
        <wsdl:part element="UpdateHousingRequest" name="UpdateHousingRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateHousingResponse">
        <wsdl:part element="UpdateHousingResponse" name="UpdateHousingResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateHousingFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateHousingFault"/>
    </wsdl:message>

    <!-- Dependants task -->
    <wsdl:message name="InitDependantsRequest">
        <wsdl:part element="InitDependantsRequest" name="InitDependantsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDependantsResponse">
        <wsdl:part element="InitDependantsResponse" name="InitDependantsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDependantsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDependantsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDependantsRequest">
        <wsdl:part element="UpdateDependantsRequest" name="UpdateDependantsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDependantsResponse">
        <wsdl:part element="UpdateDependantsResponse" name="UpdateDependantsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDependantsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDependantsFault"/>
    </wsdl:message>

    <!-- Residence task -->
    <wsdl:message name="InitResidenceRequest">
        <wsdl:part element="InitResidenceRequest" name="InitResidenceRequest"/>
    </wsdl:message>
    <wsdl:message name="InitResidenceResponse">
        <wsdl:part element="InitResidenceResponse" name="InitResidenceResponse"/>
    </wsdl:message>
    <wsdl:message name="InitResidenceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitResidenceFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateResidenceRequest">
        <wsdl:part element="UpdateResidenceRequest" name="UpdateResidenceRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateResidenceResponse">
        <wsdl:part element="UpdateResidenceResponse" name="UpdateResidenceResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateResidenceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateResidenceFault"/>
    </wsdl:message>

    <!-- Income type task -->
    <wsdl:message name="InitIncomeTypeRequest">
        <wsdl:part element="InitIncomeTypeRequest" name="InitIncomeTypeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitIncomeTypeResponse">
        <wsdl:part element="InitIncomeTypeResponse" name="InitIncomeTypeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitIncomeTypeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitIncomeTypeFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateIncomeTypeRequest">
        <wsdl:part element="UpdateIncomeTypeRequest" name="UpdateIncomeTypeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateIncomeTypeResponse">
        <wsdl:part element="UpdateIncomeTypeResponse" name="UpdateIncomeTypeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateIncomeTypeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateIncomeTypeFault"/>
    </wsdl:message>

    <!-- Confirm recognised income task -->
    <wsdl:message name="InitConfirmRecognisedIncomeRequest">
        <wsdl:part element="InitConfirmRecognisedIncomeRequest" name="InitConfirmRecognisedIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitConfirmRecognisedIncomeResponse">
        <wsdl:part element="InitConfirmRecognisedIncomeResponse" name="InitConfirmRecognisedIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitConfirmRecognisedIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitConfirmRecognisedIncomeFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateConfirmRecognisedIncomeRequest">
        <wsdl:part element="UpdateConfirmRecognisedIncomeRequest" name="UpdateConfirmRecognisedIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateConfirmRecognisedIncomeResponse">
        <wsdl:part element="UpdateConfirmRecognisedIncomeResponse" name="UpdateConfirmRecognisedIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateConfirmRecognisedIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateConfirmRecognisedIncomeFault"/>
    </wsdl:message>

    <!-- Confirm recognised income for summary task -->
    <wsdl:message name="InitConfirmRecognisedIncomeSummaryRequest">
        <wsdl:part element="InitConfirmRecognisedIncomeSummaryRequest" name="InitConfirmRecognisedIncomeSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitConfirmRecognisedIncomeSummaryResponse">
        <wsdl:part element="InitConfirmRecognisedIncomeSummaryResponse" name="InitConfirmRecognisedIncomeSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitConfirmRecognisedIncomeSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitConfirmRecognisedIncomeSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateConfirmRecognisedIncomeSummaryRequest">
        <wsdl:part element="UpdateConfirmRecognisedIncomeSummaryRequest" name="UpdateConfirmRecognisedIncomeSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateConfirmRecognisedIncomeSummaryResponse">
        <wsdl:part element="UpdateConfirmRecognisedIncomeSummaryResponse" name="UpdateConfirmRecognisedIncomeSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateConfirmRecognisedIncomeSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateConfirmRecognisedIncomeSummaryFault"/>
    </wsdl:message>

    <!-- Employee net income task -->
    <wsdl:message name="InitEmployeeNetIncomeRequest">
        <wsdl:part element="InitEmployeeNetIncomeRequest" name="InitEmployeeNetIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitEmployeeNetIncomeResponse">
        <wsdl:part element="InitEmployeeNetIncomeResponse" name="InitEmployeeNetIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitEmployeeNetIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitEmployeeNetIncomeFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateEmployeeNetIncomeRequest">
        <wsdl:part element="UpdateEmployeeNetIncomeRequest" name="UpdateEmployeeNetIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateEmployeeNetIncomeResponse">
        <wsdl:part element="UpdateEmployeeNetIncomeResponse" name="UpdateEmployeeNetIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateEmployeeNetIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateEmployeeNetIncomeFault"/>
    </wsdl:message>

    <!-- RecognisedEmployer task -->
    <wsdl:message name="InitRecognisedEmployerRequest">
        <wsdl:part element="InitRecognisedEmployerRequest" name="InitRecognisedEmployerRequest"/>
    </wsdl:message>
    <wsdl:message name="InitRecognisedEmployerResponse">
        <wsdl:part element="InitRecognisedEmployerResponse" name="InitRecognisedEmployerResponse"/>
    </wsdl:message>
    <wsdl:message name="InitRecognisedEmployerFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitRecognisedEmployerFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateRecognisedEmployerRequest">
        <wsdl:part element="UpdateRecognisedEmployerRequest" name="UpdateRecognisedEmployerRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateRecognisedEmployerResponse">
        <wsdl:part element="UpdateRecognisedEmployerResponse" name="UpdateRecognisedEmployerResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateRecognisedEmployerFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateRecognisedEmployerFault"/>
    </wsdl:message>

    <!-- Employer info task -->
    <wsdl:message name="InitEmployerInfoRequest">
        <wsdl:part element="InitEmployerInfoRequest" name="InitEmployerInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitEmployerInfoResponse">
        <wsdl:part element="InitEmployerInfoResponse" name="InitEmployerInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitEmployerInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitEmployerInfoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateEmployerInfoRequest">
        <wsdl:part element="UpdateEmployerInfoRequest" name="UpdateEmployerInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateEmployerInfoResponse">
        <wsdl:part element="UpdateEmployerInfoResponse" name="UpdateEmployerInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateEmployerInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateEmployerInfoFault"/>
    </wsdl:message>

    <!-- Employment additional info task -->
    <wsdl:message name="InitEmploymentAdditionalInfoRequest">
        <wsdl:part element="InitEmploymentAdditionalInfoRequest" name="InitEmploymentAdditionalInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitEmploymentAdditionalInfoResponse">
        <wsdl:part element="InitEmploymentAdditionalInfoResponse" name="InitEmploymentAdditionalInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitEmploymentAdditionalInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitEmploymentAdditionalInfoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateEmploymentAdditionalInfoRequest">
        <wsdl:part element="UpdateEmploymentAdditionalInfoRequest" name="UpdateEmploymentAdditionalInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateEmploymentAdditionalInfoResponse">
        <wsdl:part element="UpdateEmploymentAdditionalInfoResponse" name="UpdateEmploymentAdditionalInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateEmploymentAdditionalInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateEmploymentAdditionalInfoFault"/>
    </wsdl:message>

    <!-- Company income task -->
    <wsdl:message name="InitCompanyIncomeRequest">
        <wsdl:part element="InitCompanyIncomeRequest" name="InitCompanyIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitCompanyIncomeResponse">
        <wsdl:part element="InitCompanyIncomeResponse" name="InitCompanyIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitCompanyIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitCompanyIncomeFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCompanyIncomeRequest">
        <wsdl:part element="UpdateCompanyIncomeRequest" name="UpdateCompanyIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCompanyIncomeResponse">
        <wsdl:part element="UpdateCompanyIncomeResponse" name="UpdateCompanyIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCompanyIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCompanyIncomeFault"/>
    </wsdl:message>

    <!-- Company info task -->
    <wsdl:message name="InitCompanyInfoRequest">
        <wsdl:part element="InitCompanyInfoRequest" name="InitCompanyInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitCompanyInfoResponse">
        <wsdl:part element="InitCompanyInfoResponse" name="InitCompanyInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitCompanyInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitCompanyInfoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCompanyInfoRequest">
        <wsdl:part element="UpdateCompanyInfoRequest" name="UpdateCompanyInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCompanyInfoResponse">
        <wsdl:part element="UpdateCompanyInfoResponse" name="UpdateCompanyInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCompanyInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCompanyInfoFault"/>
    </wsdl:message>

    <!-- Company additional info task -->
    <wsdl:message name="InitCompanyAdditionalInfoRequest">
        <wsdl:part element="InitCompanyAdditionalInfoRequest" name="InitCompanyAdditionalInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitCompanyAdditionalInfoResponse">
        <wsdl:part element="InitCompanyAdditionalInfoResponse" name="InitCompanyAdditionalInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitCompanyAdditionalInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitCompanyAdditionalInfoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCompanyAdditionalInfoRequest">
        <wsdl:part element="UpdateCompanyAdditionalInfoRequest" name="UpdateCompanyAdditionalInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCompanyAdditionalInfoResponse">
        <wsdl:part element="UpdateCompanyAdditionalInfoResponse" name="UpdateCompanyAdditionalInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCompanyAdditionalInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCompanyAdditionalInfoFault"/>
    </wsdl:message>

    <!-- Other income task -->
    <wsdl:message name="InitOtherIncomeRequest">
        <wsdl:part element="InitOtherIncomeRequest" name="InitOtherIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOtherIncomeResponse">
        <wsdl:part element="InitOtherIncomeResponse" name="InitOtherIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOtherIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOtherIncomeFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOtherIncomeRequest">
        <wsdl:part element="UpdateOtherIncomeRequest" name="UpdateOtherIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherIncomeResponse">
        <wsdl:part element="UpdateOtherIncomeResponse" name="UpdateOtherIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOtherIncomeFault"/>
    </wsdl:message>

    <!-- Other income additional info task -->
    <wsdl:message name="InitOtherIncomeAdditionalInfoRequest">
        <wsdl:part element="InitOtherIncomeAdditionalInfoRequest" name="InitOtherIncomeAdditionalInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOtherIncomeAdditionalInfoResponse">
        <wsdl:part element="InitOtherIncomeAdditionalInfoResponse" name="InitOtherIncomeAdditionalInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOtherIncomeAdditionalInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOtherIncomeAdditionalInfoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOtherIncomeAdditionalInfoRequest">
        <wsdl:part element="UpdateOtherIncomeAdditionalInfoRequest" name="UpdateOtherIncomeAdditionalInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherIncomeAdditionalInfoResponse">
        <wsdl:part element="UpdateOtherIncomeAdditionalInfoResponse" name="UpdateOtherIncomeAdditionalInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherIncomeAdditionalInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOtherIncomeAdditionalInfoFault"/>
    </wsdl:message>

    <!-- Other income exists task -->
    <wsdl:message name="InitOtherIncomeExistsRequest">
        <wsdl:part element="InitOtherIncomeExistsRequest" name="InitOtherIncomeExistsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOtherIncomeExistsResponse">
        <wsdl:part element="InitOtherIncomeExistsResponse" name="InitOtherIncomeExistsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOtherIncomeExistsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOtherIncomeExistsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOtherIncomeExistsRequest">
        <wsdl:part element="UpdateOtherIncomeExistsRequest" name="UpdateOtherIncomeExistsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherIncomeExistsResponse">
        <wsdl:part element="UpdateOtherIncomeExistsResponse" name="UpdateOtherIncomeExistsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherIncomeExistsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOtherIncomeExistsFault"/>
    </wsdl:message>

    <!-- Other household members income task -->
    <wsdl:message name="InitOtherHouseholdMembersIncomeRequest">
        <wsdl:part element="InitOtherHouseholdMembersIncomeRequest" name="InitOtherHouseholdMembersIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOtherHouseholdMembersIncomeResponse">
        <wsdl:part element="InitOtherHouseholdMembersIncomeResponse" name="InitOtherHouseholdMembersIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOtherHouseholdMembersIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOtherHouseholdMembersIncomeFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOtherHouseholdMembersIncomeRequest">
        <wsdl:part element="UpdateOtherHouseholdMembersIncomeRequest" name="UpdateOtherHouseholdMembersIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherHouseholdMembersIncomeResponse">
        <wsdl:part element="UpdateOtherHouseholdMembersIncomeResponse" name="UpdateOtherHouseholdMembersIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtherHouseholdMembersIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOtherHouseholdMembersIncomeFault"/>
    </wsdl:message>

    <!-- Other household expenses task -->
    <wsdl:message name="InitHouseholdExpensesRequest">
        <wsdl:part element="InitHouseholdExpensesRequest" name="InitHouseholdExpensesRequest"/>
    </wsdl:message>
    <wsdl:message name="InitHouseholdExpensesResponse">
        <wsdl:part element="InitHouseholdExpensesResponse" name="InitHouseholdExpensesResponse"/>
    </wsdl:message>
    <wsdl:message name="InitHouseholdExpensesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitHouseholdExpensesFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateHouseholdExpensesRequest">
        <wsdl:part element="UpdateHouseholdExpensesRequest" name="UpdateHouseholdExpensesRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateHouseholdExpensesResponse">
        <wsdl:part element="UpdateHouseholdExpensesResponse" name="UpdateHouseholdExpensesResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateHouseholdExpensesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateHouseholdExpensesFault"/>
    </wsdl:message>

    <!-- Affidavit task -->
    <wsdl:message name="InitAffidavitRequest">
        <wsdl:part element="InitAffidavitRequest" name="InitAffidavitRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAffidavitResponse">
        <wsdl:part element="InitAffidavitResponse" name="InitAffidavitResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAffidavitFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAffidavitFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateAffidavitRequest">
        <wsdl:part element="UpdateAffidavitRequest" name="UpdateAffidavitRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateAffidavitResponse">
        <wsdl:part element="UpdateAffidavitResponse" name="UpdateAffidavitResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateAffidavitFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateAffidavitFault"/>
    </wsdl:message>

    <!-- Basic parameters task -->
    <wsdl:message name="InitBasicParametersRequest">
        <wsdl:part element="InitBasicParametersRequest" name="InitBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBasicParametersResponse">
        <wsdl:part element="InitBasicParametersResponse" name="InitBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBasicParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateBasicParametersRequest">
        <wsdl:part element="UpdateBasicParametersRequest" name="UpdateBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBasicParametersResponse">
        <wsdl:part element="UpdateBasicParametersResponse" name="UpdateBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBasicParametersFault"/>
    </wsdl:message>

    <!-- Document and addresses task -->
    <wsdl:message name="InitDocumentAndAddressesRequest">
        <wsdl:part element="InitDocumentAndAddressesRequest" name="InitDocumentAndAddressesRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDocumentAndAddressesResponse">
        <wsdl:part element="InitDocumentAndAddressesResponse" name="InitDocumentAndAddressesResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDocumentAndAddressesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDocumentAndAddressesFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDocumentAndAddressesRequest">
        <wsdl:part element="UpdateDocumentAndAddressesRequest" name="UpdateDocumentAndAddressesRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDocumentAndAddressesResponse">
        <wsdl:part element="UpdateDocumentAndAddressesResponse" name="UpdateDocumentAndAddressesResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDocumentAndAddressesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDocumentAndAddressesFault"/>
    </wsdl:message>


    <wsdl:portType name="ApplicationData">
        <wsdl:operation name="InitApplicationDataSummary">
            <wsdl:documentation>Operation used to initialize the applicationDataSummaryTask.</wsdl:documentation>
            <wsdl:input message="InitApplicationDataSummaryRequest"/>
            <wsdl:output message="InitApplicationDataSummaryResponse"/>
            <wsdl:fault name="InitApplicationDataSummaryFault" message="InitApplicationDataSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateApplicationDataSummary">
            <wsdl:documentation>Operation used to update data for the applicationDataSummaryTask.</wsdl:documentation>
            <wsdl:input message="UpdateApplicationDataSummaryRequest"/>
            <wsdl:output message="UpdateApplicationDataSummaryResponse"/>
            <wsdl:fault name="UpdateApplicationDataSummaryFault" message="UpdateApplicationDataSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitEducation">
            <wsdl:documentation>Operation used to initialize the educationTask.</wsdl:documentation>
            <wsdl:input message="InitEducationRequest"/>
            <wsdl:output message="InitEducationResponse"/>
            <wsdl:fault name="InitEducationFault" message="InitEducationFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateEducation">
            <wsdl:documentation>Operation used to update data for the educationTask.</wsdl:documentation>
            <wsdl:input message="UpdateEducationRequest"/>
            <wsdl:output message="UpdateEducationResponse"/>
            <wsdl:fault name="UpdateEducationFault" message="UpdateEducationFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitMaritalStatus">
            <wsdl:documentation>Operation used to initialize the maritalStatusTask.</wsdl:documentation>
            <wsdl:input message="InitMaritalStatusRequest"/>
            <wsdl:output message="InitMaritalStatusResponse"/>
            <wsdl:fault name="InitMaritalStatusFault" message="InitMaritalStatusFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateMaritalStatus">
            <wsdl:documentation>Operation used to update data for the maritalStatusTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateMaritalStatusRequest"/>
            <wsdl:output message="UpdateMaritalStatusResponse"/>
            <wsdl:fault name="UpdateMaritalStatusFault" message="UpdateMaritalStatusFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitRelationToDebtor">
            <wsdl:documentation>Operation used to initialize the relationToDebtorTask.</wsdl:documentation>
            <wsdl:input message="InitRelationToDebtorRequest"/>
            <wsdl:output message="InitRelationToDebtorResponse"/>
            <wsdl:fault name="InitRelationToDebtorFault" message="InitRelationToDebtorFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateRelationToDebtor">
            <wsdl:documentation>Operation used to update data for the relationToDebtorTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateRelationToDebtorRequest"/>
            <wsdl:output message="UpdateRelationToDebtorResponse"/>
            <wsdl:fault name="UpdateRelationToDebtorFault" message="UpdateRelationToDebtorFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitHousing">
            <wsdl:documentation>Operation used to initialize the housingTask.</wsdl:documentation>
            <wsdl:input message="InitHousingRequest"/>
            <wsdl:output message="InitHousingResponse"/>
            <wsdl:fault name="InitHousingFault" message="InitHousingFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateHousing">
            <wsdl:documentation>Operation used to update data for the housingTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateHousingRequest"/>
            <wsdl:output message="UpdateHousingResponse"/>
            <wsdl:fault name="UpdateHousingFault" message="UpdateHousingFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDependants">
            <wsdl:documentation>Operation used to initialize the dependantsTask.</wsdl:documentation>
            <wsdl:input message="InitDependantsRequest"/>
            <wsdl:output message="InitDependantsResponse"/>
            <wsdl:fault name="InitDependantsFault" message="InitDependantsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDependants">
            <wsdl:documentation>Operation used to update data for the dependantsTask.</wsdl:documentation>
            <wsdl:input message="UpdateDependantsRequest"/>
            <wsdl:output message="UpdateDependantsResponse"/>
            <wsdl:fault name="UpdateDependantsFault" message="UpdateDependantsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitResidence">
            <wsdl:documentation>Operation used to initialize the residenceTask.</wsdl:documentation>
            <wsdl:input message="InitResidenceRequest"/>
            <wsdl:output message="InitResidenceResponse"/>
            <wsdl:fault name="InitResidenceFault" message="InitResidenceFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateResidence">
            <wsdl:documentation>Operation used to update data for the residenceTask.</wsdl:documentation>
            <wsdl:input message="UpdateResidenceRequest"/>
            <wsdl:output message="UpdateResidenceResponse"/>
            <wsdl:fault name="UpdateResidenceFault" message="UpdateResidenceFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitIncomeType">
            <wsdl:documentation>Operation used to initialize the incomeTypeTask.</wsdl:documentation>
            <wsdl:input message="InitIncomeTypeRequest"/>
            <wsdl:output message="InitIncomeTypeResponse"/>
            <wsdl:fault name="InitIncomeTypeFault" message="InitIncomeTypeFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateIncomeType">
            <wsdl:documentation>Operation used to update data for the incomeTypeTask.</wsdl:documentation>
            <wsdl:input message="UpdateIncomeTypeRequest"/>
            <wsdl:output message="UpdateIncomeTypeResponse"/>
            <wsdl:fault name="UpdateIncomeTypeFault" message="UpdateIncomeTypeFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitConfirmRecognisedIncome">
            <wsdl:documentation>Operation used to initialize the confirmRecognisedIncomeTask.</wsdl:documentation>
            <wsdl:input message="InitConfirmRecognisedIncomeRequest"/>
            <wsdl:output message="InitConfirmRecognisedIncomeResponse"/>
            <wsdl:fault name="InitConfirmRecognisedIncomeFault" message="InitConfirmRecognisedIncomeFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateConfirmRecognisedIncome">
            <wsdl:documentation>Operation used to update data for the confirmRecognisedIncomeTask.</wsdl:documentation>
            <wsdl:input message="UpdateConfirmRecognisedIncomeRequest"/>
            <wsdl:output message="UpdateConfirmRecognisedIncomeResponse"/>
            <wsdl:fault name="UpdateConfirmRecognisedIncomeFault" message="UpdateConfirmRecognisedIncomeFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitConfirmRecognisedIncomeSummary">
            <wsdl:documentation>Operation used to initialize the confirmRecognisedIncomeSummaryTask.</wsdl:documentation>
            <wsdl:input message="InitConfirmRecognisedIncomeSummaryRequest"/>
            <wsdl:output message="InitConfirmRecognisedIncomeSummaryResponse"/>
            <wsdl:fault name="InitConfirmRecognisedIncomeSummaryFault" message="InitConfirmRecognisedIncomeSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateConfirmRecognisedIncomeSummary">
            <wsdl:documentation>Operation used to update data for the confirmRecognisedIncomeSummaryTask.</wsdl:documentation>
            <wsdl:input message="UpdateConfirmRecognisedIncomeSummaryRequest"/>
            <wsdl:output message="UpdateConfirmRecognisedIncomeSummaryResponse"/>
            <wsdl:fault name="UpdateConfirmRecognisedIncomeSummaryFault" message="UpdateConfirmRecognisedIncomeSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitEmployeeNetIncome">
            <wsdl:documentation>Operation used to initialize the employeeNetIncomeTask.</wsdl:documentation>
            <wsdl:input message="InitEmployeeNetIncomeRequest"/>
            <wsdl:output message="InitEmployeeNetIncomeResponse"/>
            <wsdl:fault name="InitEmployeeNetIncomeFault" message="InitEmployeeNetIncomeFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateEmployeeNetIncome">
            <wsdl:documentation>Operation used to update data for the employeeNetIncomeTask.</wsdl:documentation>
            <wsdl:input message="UpdateEmployeeNetIncomeRequest"/>
            <wsdl:output message="UpdateEmployeeNetIncomeResponse"/>
            <wsdl:fault name="UpdateEmployeeNetIncomeFault" message="UpdateEmployeeNetIncomeFault"/>
        </wsdl:operation>


        <wsdl:operation name="InitRecognisedEmployer">
            <wsdl:documentation>Operation used to initialize the recognizedEmployerTask.</wsdl:documentation>
            <wsdl:input message="InitRecognisedEmployerRequest"/>
            <wsdl:output message="InitRecognisedEmployerResponse"/>
            <wsdl:fault name="InitRecognisedEmployerFault" message="InitRecognisedEmployerFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateRecognisedEmployer">
            <wsdl:documentation>Operation used to update data for the recognizedEmployerTask.</wsdl:documentation>
            <wsdl:input message="UpdateRecognisedEmployerRequest"/>
            <wsdl:output message="UpdateRecognisedEmployerResponse"/>
            <wsdl:fault name="UpdateRecognisedEmployerFault" message="UpdateRecognisedEmployerFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitEmployerInfo">
            <wsdl:documentation>Operation used to initialize the employerInfoTask.</wsdl:documentation>
            <wsdl:input message="InitEmployerInfoRequest"/>
            <wsdl:output message="InitEmployerInfoResponse"/>
            <wsdl:fault name="InitEmployerInfoFault" message="InitEmployerInfoFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateEmployerInfo">
            <wsdl:documentation>Operation used to update data for the employerInfoTask.</wsdl:documentation>
            <wsdl:input message="UpdateEmployerInfoRequest"/>
            <wsdl:output message="UpdateEmployerInfoResponse"/>
            <wsdl:fault name="UpdateEmployerInfoFault" message="UpdateEmployerInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitEmploymentAdditionalInfo">
            <wsdl:documentation>Operation used to initialize the employmentAdditionalInfoTask.</wsdl:documentation>
            <wsdl:input message="InitEmploymentAdditionalInfoRequest"/>
            <wsdl:output message="InitEmploymentAdditionalInfoResponse"/>
            <wsdl:fault name="InitEmploymentAdditionalInfoFault" message="InitEmploymentAdditionalInfoFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateEmploymentAdditionalInfo">
            <wsdl:documentation>Operation used to update data for the employmentAdditionalInfoTask.</wsdl:documentation>
            <wsdl:input message="UpdateEmploymentAdditionalInfoRequest"/>
            <wsdl:output message="UpdateEmploymentAdditionalInfoResponse"/>
            <wsdl:fault name="UpdateEmploymentAdditionalInfoFault" message="UpdateEmploymentAdditionalInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitCompanyIncome">
            <wsdl:documentation>Operation used to initialize the companyIncomeTask.</wsdl:documentation>
            <wsdl:input message="InitCompanyIncomeRequest"/>
            <wsdl:output message="InitCompanyIncomeResponse"/>
            <wsdl:fault name="InitCompanyIncomeFault" message="InitCompanyIncomeFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateCompanyIncome">
            <wsdl:documentation>Operation used to update data for the companyIncomeTask.</wsdl:documentation>
            <wsdl:input message="UpdateCompanyIncomeRequest"/>
            <wsdl:output message="UpdateCompanyIncomeResponse"/>
            <wsdl:fault name="UpdateCompanyIncomeFault" message="UpdateCompanyIncomeFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitCompanyInfo">
            <wsdl:documentation>Operation used to initialize the companyInfoTask.</wsdl:documentation>
            <wsdl:input message="InitCompanyInfoRequest"/>
            <wsdl:output message="InitCompanyInfoResponse"/>
            <wsdl:fault name="InitCompanyInfoFault" message="InitCompanyInfoFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateCompanyInfo">
            <wsdl:documentation>Operation used to update data for the companyInfoTask.</wsdl:documentation>
            <wsdl:input message="UpdateCompanyInfoRequest"/>
            <wsdl:output message="UpdateCompanyInfoResponse"/>
            <wsdl:fault name="UpdateCompanyInfoFault" message="UpdateCompanyInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitCompanyAdditionalInfo">
            <wsdl:documentation>Operation used to initialize the companyAdditionalInfoTask.</wsdl:documentation>
            <wsdl:input message="InitCompanyAdditionalInfoRequest"/>
            <wsdl:output message="InitCompanyAdditionalInfoResponse"/>
            <wsdl:fault name="InitCompanyAdditionalInfoFault" message="InitCompanyAdditionalInfoFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateCompanyAdditionalInfo">
            <wsdl:documentation>Operation used to update data for the companyAdditionalInfoTask.</wsdl:documentation>
            <wsdl:input message="UpdateCompanyAdditionalInfoRequest"/>
            <wsdl:output message="UpdateCompanyAdditionalInfoResponse"/>
            <wsdl:fault name="UpdateCompanyAdditionalInfoFault" message="UpdateCompanyAdditionalInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOtherIncome">
            <wsdl:documentation>Operation used to initialize the otherIncomeTask.</wsdl:documentation>
            <wsdl:input message="InitOtherIncomeRequest"/>
            <wsdl:output message="InitOtherIncomeResponse"/>
            <wsdl:fault name="InitOtherIncomeFault" message="InitOtherIncomeFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOtherIncome">
            <wsdl:documentation>Operation used to update data for the otherIncomeTask.</wsdl:documentation>
            <wsdl:input message="UpdateOtherIncomeRequest"/>
            <wsdl:output message="UpdateOtherIncomeResponse"/>
            <wsdl:fault name="UpdateOtherIncomeFault" message="UpdateOtherIncomeFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOtherIncomeAdditionalInfo">
            <wsdl:documentation>Operation used to initialize the otherIncomeAdditionalInfoTask.</wsdl:documentation>
            <wsdl:input message="InitOtherIncomeAdditionalInfoRequest"/>
            <wsdl:output message="InitOtherIncomeAdditionalInfoResponse"/>
            <wsdl:fault name="InitOtherIncomeAdditionalInfoFault" message="InitOtherIncomeAdditionalInfoFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOtherIncomeAdditionalInfo">
            <wsdl:documentation>Operation used to update data for the otherIncomeAdditionalInfoTask.</wsdl:documentation>
            <wsdl:input message="UpdateOtherIncomeAdditionalInfoRequest"/>
            <wsdl:output message="UpdateOtherIncomeAdditionalInfoResponse"/>
            <wsdl:fault name="UpdateOtherIncomeAdditionalInfoFault" message="UpdateOtherIncomeAdditionalInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOtherIncomeExists">
            <wsdl:documentation>Operation used to initialize the otherIncomeExistsTask.</wsdl:documentation>
            <wsdl:input message="InitOtherIncomeExistsRequest"/>
            <wsdl:output message="InitOtherIncomeExistsResponse"/>
            <wsdl:fault name="InitOtherIncomeExistsFault" message="InitOtherIncomeExistsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOtherIncomeExists">
            <wsdl:documentation>Operation used to update data for the otherIncomeExistsTask.</wsdl:documentation>
            <wsdl:input message="UpdateOtherIncomeExistsRequest"/>
            <wsdl:output message="UpdateOtherIncomeExistsResponse"/>
            <wsdl:fault name="UpdateOtherIncomeExistsFault" message="UpdateOtherIncomeExistsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOtherHouseholdMembersIncome">
            <wsdl:documentation>Operation used to initialize the otherHouseholdMembersIncomeTask.</wsdl:documentation>
            <wsdl:input message="InitOtherHouseholdMembersIncomeRequest"/>
            <wsdl:output message="InitOtherHouseholdMembersIncomeResponse"/>
            <wsdl:fault name="InitOtherHouseholdMembersIncomeFault" message="InitOtherHouseholdMembersIncomeFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOtherHouseholdMembersIncome">
            <wsdl:documentation>Operation used to update data for the otherHouseholdMembersIncomeTask.</wsdl:documentation>
            <wsdl:input message="UpdateOtherHouseholdMembersIncomeRequest"/>
            <wsdl:output message="UpdateOtherHouseholdMembersIncomeResponse"/>
            <wsdl:fault name="UpdateOtherHouseholdMembersIncomeFault" message="UpdateOtherHouseholdMembersIncomeFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitHouseholdExpenses">
            <wsdl:documentation>Operation used to initialize the householdExpensesTask.</wsdl:documentation>
            <wsdl:input message="InitHouseholdExpensesRequest"/>
            <wsdl:output message="InitHouseholdExpensesResponse"/>
            <wsdl:fault name="InitHouseholdExpensesFault" message="InitHouseholdExpensesFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateHouseholdExpenses">
            <wsdl:documentation>Operation used to update data for the householdExpensesTask.</wsdl:documentation>
            <wsdl:input message="UpdateHouseholdExpensesRequest"/>
            <wsdl:output message="UpdateHouseholdExpensesResponse"/>
            <wsdl:fault name="UpdateHouseholdExpensesFault" message="UpdateHouseholdExpensesFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAffidavit">
            <wsdl:documentation>Operation used to initialize the affidavitTask.</wsdl:documentation>
            <wsdl:input message="InitAffidavitRequest"/>
            <wsdl:output message="InitAffidavitResponse"/>
            <wsdl:fault name="InitAffidavitFault" message="InitAffidavitFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateAffidavit">
            <wsdl:documentation>Operation used to update data for the affidavitTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateAffidavitRequest"/>
            <wsdl:output message="UpdateAffidavitResponse"/>
            <wsdl:fault name="UpdateAffidavitFault" message="UpdateAffidavitFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitBasicParameters">
            <wsdl:documentation>Operation used to initialize the basicParametersTask.</wsdl:documentation>
            <wsdl:input message="InitBasicParametersRequest"/>
            <wsdl:output message="InitBasicParametersResponse"/>
            <wsdl:fault name="InitBasicParametersFault" message="InitBasicParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateBasicParameters">
            <wsdl:documentation>Operation used to update data for the basicParametersTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateBasicParametersRequest"/>
            <wsdl:output message="UpdateBasicParametersResponse"/>
            <wsdl:fault name="UpdateBasicParametersFault" message="UpdateBasicParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDocumentAndAddresses">
            <wsdl:documentation>Operation used to initialize the documentAndAddressesTask.</wsdl:documentation>
            <wsdl:input message="InitDocumentAndAddressesRequest"/>
            <wsdl:output message="InitDocumentAndAddressesResponse"/>
            <wsdl:fault name="InitDocumentAndAddressesFault" message="InitDocumentAndAddressesFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDocumentAndAddresses">
            <wsdl:documentation>Operation used to update data for the documentAndAddressesTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateDocumentAndAddressesRequest"/>
            <wsdl:output message="UpdateDocumentAndAddressesResponse"/>
            <wsdl:fault name="UpdateDocumentAndAddressesFault" message="UpdateDocumentAndAddressesFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="ApplicationDataBinding" type="ApplicationData">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="InitApplicationDataSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitApplicationDataSummaryFault">
                <soap:fault name="InitApplicationDataSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateApplicationDataSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateApplicationDataSummaryFault">
                <soap:fault name="UpdateApplicationDataSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitEducation">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitEducationFault">
                <soap:fault name="InitEducationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateEducation">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateEducationFault">
                <soap:fault name="UpdateEducationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitRelationToDebtor">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitRelationToDebtorFault">
                <soap:fault name="InitRelationToDebtorFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateRelationToDebtor">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateRelationToDebtorFault">
                <soap:fault name="UpdateRelationToDebtorFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitMaritalStatus">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitMaritalStatusFault">
                <soap:fault name="InitMaritalStatusFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateMaritalStatus">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateMaritalStatusFault">
                <soap:fault name="UpdateMaritalStatusFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitHousing">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitHousingFault">
                <soap:fault name="InitHousingFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateHousing">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateHousingFault">
                <soap:fault name="UpdateHousingFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDependants">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDependantsFault">
                <soap:fault name="InitDependantsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDependants">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDependantsFault">
                <soap:fault name="UpdateDependantsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitResidence">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitResidenceFault">
                <soap:fault name="InitResidenceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateResidence">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateResidenceFault">
                <soap:fault name="UpdateResidenceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitIncomeType">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitIncomeTypeFault">
                <soap:fault name="InitIncomeTypeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateIncomeType">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateIncomeTypeFault">
                <soap:fault name="UpdateIncomeTypeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitConfirmRecognisedIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitConfirmRecognisedIncomeFault">
                <soap:fault name="InitConfirmRecognisedIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateConfirmRecognisedIncome">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateConfirmRecognisedIncomeFault">
                <soap:fault name="UpdateConfirmRecognisedIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitConfirmRecognisedIncomeSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitConfirmRecognisedIncomeSummaryFault">
                <soap:fault name="InitConfirmRecognisedIncomeSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateConfirmRecognisedIncomeSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateConfirmRecognisedIncomeSummaryFault">
                <soap:fault name="UpdateConfirmRecognisedIncomeSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitEmployeeNetIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitEmployeeNetIncomeFault">
                <soap:fault name="InitEmployeeNetIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateEmployeeNetIncome">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateEmployeeNetIncomeFault">
                <soap:fault name="UpdateEmployeeNetIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitRecognisedEmployer">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitRecognisedEmployerFault">
                <soap:fault name="InitRecognisedEmployerFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateRecognisedEmployer">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateRecognisedEmployerFault">
                <soap:fault name="UpdateRecognisedEmployerFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitEmployerInfo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitEmployerInfoFault">
                <soap:fault name="InitEmployerInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateEmployerInfo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateEmployerInfoFault">
                <soap:fault name="UpdateEmployerInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitEmploymentAdditionalInfo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitEmploymentAdditionalInfoFault">
                <soap:fault name="InitEmploymentAdditionalInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateEmploymentAdditionalInfo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateEmploymentAdditionalInfoFault">
                <soap:fault name="UpdateEmploymentAdditionalInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitCompanyIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitCompanyIncomeFault">
                <soap:fault name="InitCompanyIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCompanyIncome">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCompanyIncomeFault">
                <soap:fault name="UpdateCompanyIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitCompanyInfo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitCompanyInfoFault">
                <soap:fault name="InitCompanyInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCompanyInfo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCompanyInfoFault">
                <soap:fault name="UpdateCompanyInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitCompanyAdditionalInfo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitCompanyAdditionalInfoFault">
                <soap:fault name="InitCompanyAdditionalInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCompanyAdditionalInfo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCompanyAdditionalInfoFault">
                <soap:fault name="UpdateCompanyAdditionalInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOtherIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOtherIncomeFault">
                <soap:fault name="InitOtherIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOtherIncome">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOtherIncomeFault">
                <soap:fault name="UpdateOtherIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOtherIncomeAdditionalInfo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOtherIncomeAdditionalInfoFault">
                <soap:fault name="InitOtherIncomeAdditionalInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOtherIncomeAdditionalInfo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOtherIncomeAdditionalInfoFault">
                <soap:fault name="UpdateOtherIncomeAdditionalInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOtherIncomeExists">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOtherIncomeExistsFault">
                <soap:fault name="InitOtherIncomeExistsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOtherIncomeExists">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOtherIncomeExistsFault">
                <soap:fault name="UpdateOtherIncomeExistsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOtherHouseholdMembersIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOtherHouseholdMembersIncomeFault">
                <soap:fault name="InitOtherHouseholdMembersIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOtherHouseholdMembersIncome">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOtherHouseholdMembersIncomeFault">
                <soap:fault name="UpdateOtherHouseholdMembersIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitHouseholdExpenses">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitHouseholdExpensesFault">
                <soap:fault name="InitHouseholdExpensesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateHouseholdExpenses">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateHouseholdExpensesFault">
                <soap:fault name="UpdateHouseholdExpensesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitAffidavit">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAffidavitFault">
                <soap:fault name="InitAffidavitFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateAffidavit">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateAffidavitFault">
                <soap:fault name="UpdateAffidavitFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitBasicParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBasicParametersFault">
                <soap:fault name="InitBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateBasicParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBasicParametersFault">
                <soap:fault name="UpdateBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDocumentAndAddresses">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDocumentAndAddressesFault">
                <soap:fault name="InitDocumentAndAddressesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDocumentAndAddresses">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDocumentAndAddressesFault">
                <soap:fault name="UpdateDocumentAndAddressesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ApplicationDataService">
        <wsdl:documentation>Service is providing common operations related to loans and application data.

            The application has the following task IDs:
            - personalParametersTask - page with family and education personal data
            - applicationDataSummaryTask - page with application data set in previous application
            - incomeEmployerTask - page with income data when client is employer
            - incomeCompanyTask - page with income data when client is business or freelancer
            - incomeOtherTask - page with income data when client other source of income than the previous two types
            - branchOfficerCodeTask - page shown only when branch officer is logged in, allows to insert branch officer code and password
            - registerAgreementTask - page with agreements to check registers
            - affidavitTask - inital page for co-debtor

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGTH, INVALID_EXACT_LENGTH - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
            - DATE_RANGE_VALIDATOR - year is not in range 1900 - 2999
        </wsdl:documentation>
        <wsdl:port name="ApplicationDatanPort" binding="ApplicationDataBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/common/applicationdata"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
