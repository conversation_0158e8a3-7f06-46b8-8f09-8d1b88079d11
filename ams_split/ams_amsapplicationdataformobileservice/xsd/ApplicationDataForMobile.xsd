<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           xmlns:applicationDataCommon="http://airbank.cz/ams/ws/application/common/applicationdata"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/common/applicationdata/mobile"
           targetNamespace="http://airbank.cz/ams/ws/application/common/applicationdata/mobile">

    <xs:annotation>
        <xs:documentation>Common types for application data for Mobile</xs:documentation>
    </xs:annotation>

    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/authorization/" schemaLocation="Authorization.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/applicationdata" schemaLocation="ApplicationDataCommon.xsd"/>

    <xs:complexType name="InitEducationResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing education.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="education" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Education code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateEducationRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing education.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="education" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Education code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitRelationToDebtorResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing relation to debtor.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="gender" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Gender of client used to show correct text in frontend.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="relationToDebtor" type="applicationDataCommon:RelationToDebtorTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Co-debtors relation to debtor.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="possibleRelationsToMainApplicant" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Possible relations to main applicant from MDM codelist RelationshipRole.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateRelationToDebtorRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing relation to debtor.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="relationToDebtor" type="applicationDataCommon:RelationToDebtorTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Co-debtors relation to debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="skipUpdate" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>True if it should move process only - backward compatibility.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitMaritalStatusResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing marital status.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="married" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is client married.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="relationToDebtor" type="applicationDataCommon:RelationToDebtorTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Co-debtors relation to debtor.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="gender" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Gender of client used to show correct text in IB (mainly for walkin).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="possibleRelationsToMainApplicant" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Possible relations to main applicant from MDM codelist RelationshipRole.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="householdMemberType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Information about applicant's household member type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="householdMemberTypeVisible" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>True if Household member type question should be visible (default true).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateMaritalStatusRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing marital status.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="married" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Is client married.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="relationToDebtor" type="applicationDataCommon:RelationToDebtorTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Co-debtors relation to debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="householdMemberType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Information about applicant's household member type.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitHousingResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing housing.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="housingType" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of  housing.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateHousingRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing housing.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="housingType" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of  housing.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitDependantsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing dependants.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="numberOfDependants" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of number of dependants.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateDependantsRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing dependants.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="numberOfDependants" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of number of dependants.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitResidenceResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing residence.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="residenceType" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Residence code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateResidenceRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing residence.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="residenceType" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Residence code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitIncomeTypeResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing income type.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="incomeType" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Income type code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateIncomeTypeRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing income type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="incomeType" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Income type code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitConfirmRecognisedIncomeResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing confirm recognised income.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="recognisedIncomes" type="applicationDataCommon:RecognisedIncomeTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Last recognised incomes.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateConfirmRecognisedIncomeRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing confirm recognised income.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="recognisedIncomesConfirmations" type="applicationDataCommon:RecognisedIncomeConfirmationTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Client changed statuses of recognised incomes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backwardCompatibility" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Temporary solution. Remove it in future release.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitEmployeeNetIncomeResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing employee net income.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="netIncome" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Net income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedIncomeUsed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates that recognised income for the client has been verified.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedIncomeTransactionId" type="loanCommon:TransactionIdTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Transactions from which the recognized income was calculated.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateEmployeeNetIncomeRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing employee net income.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="netIncome" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Net income.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lowNetIncomeConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Confirmation of low income waring from the employment</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitRecognisedEmployerResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing recognized employer.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="company" type="applicationDataCommon:CompanyTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Company.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedEmployerConfirmation" type="applicationDataCommon:RecognisedEmployerConfirmationTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Recognised employer status.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateRecognisedEmployerRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing recognized employer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="recognisedEmployerConfirmation" type="applicationDataCommon:RecognisedEmployerConfirmationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Recognised employer status.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="company" type="applicationDataCommon:CompanyTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Company.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitEmployerInfoResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing employer info.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="company" type="applicationDataCommon:CompanyTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Company.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pureBisnodeData" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is company pure from Bisnode.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateEmployerInfoRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing employer info.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="company" type="applicationDataCommon:CompanyTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pureBisnodeData" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Is company pure from Bisnode.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitEmploymentAdditionalInfoResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing employment additional info.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="InitIncomeAdditionalInfoResponseCommon">
                <xs:sequence>
                    <xs:element name="employmentType" type="applicationDataCommon:EmploymentTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of employment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="agreementUntil" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date when the agreement ends.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateEmploymentAdditionalInfoRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing employment additional info.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="UpdateIncomeAdditionalInfoRequestCommon">
                <xs:sequence>
                    <xs:element name="employmentType" type="applicationDataCommon:EmploymentTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of employment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="agreementUntil" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date when the agreement ends.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitCompanyIncomeResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing company income.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="income" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Income from the company.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="noTaxReturn" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates that the applicant doesn't file a tax return.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateCompanyIncomeRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing company income.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="income" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Income from the company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="noTaxReturn" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates that the applicant doesn't file a tax return.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lowIncomeConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Confirmation of low income warning</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitCompanyInfoResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing company info.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="company" type="applicationDataCommon:CompanyTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Company.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pureBisnodeData" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Is company pure from Bisnode.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateCompanyInfoRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing company info.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="company" type="applicationDataCommon:CompanyTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Company.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pureBisnodeData" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Is company pure from Bisnode.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitIncomeAdditionalInfoResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing income additional info (incomeTimeType and incomeFrom). Is used for employer, company and other.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="incomeTimeType" type="appCommon:IncomeTimeTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of income time.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="incomeFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Date when the employment has begun.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateIncomeAdditionalInfoRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing income additional info (incomeTimeType and incomeFrom). Is used for employer, company and other.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="incomeTimeType" type="appCommon:IncomeTimeTypeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of income time.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="incomeFrom" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date when the employment has begun.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitOtherIncomeResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing other income.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="IncomeStatusBaseResponseCommon">
                <xs:sequence>
                    <xs:element name="pureMonthlyIncome" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Amount of monthly income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedIncomeUsed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates that recognised income for the client has been verified.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="recognisedIncomeTransactionId" type="loanCommon:TransactionIdTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Transactions from which the recognized income was calculated.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateOtherIncomeRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing other income.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:sequence>
                <xs:element name="pureMonthlyIncome" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Amount of monthly income.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitOtherIncomeExistsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing other income exists.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="otherIncomeExists" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Other income exists.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateOtherIncomeExistsRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing other income exists.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="otherIncomeExists" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Other income exists.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitOtherHouseholdMembersIncomeResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing other household members income.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="otherHouseholdMembersIncome" type="applicationDataCommon:InitIncomeExpenseTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Other household members income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="householdMemberType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Information about applicant's household member type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="gender" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Gender of client used to show correct text in frontend.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateOtherHouseholdMembersIncomeRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing other household members income.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="otherHouseholdMembersIncome" type="applicationDataCommon:IncomeExpenseTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Other household members income.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitHouseholdExpensesResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing household expenses.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="medicalFoodTransportationExpense" type="applicationDataCommon:InitIncomeExpenseTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Other household members income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accommodationExpense" type="applicationDataCommon:InitIncomeExpenseTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Other household members income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanPaymentSumAllMembers" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Monthly household installments expenses (loans and mortgages).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="householdMemberType" type="xs:string" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Information about applicant's household member type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateHouseholdExpensesRequestCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing household expenses.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="medicalFoodTransportationExpense" type="applicationDataCommon:IncomeExpenseTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Other household members income.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="confirmedMedicalFoodTransportationExpenseWarning" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="accommodationExpense" type="applicationDataCommon:IncomeExpenseTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Other household members income.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="confirmedAccommodationExpenseWarning" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="loanPaymentSumAllMembers" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Monthly household installments expenses (loans and mortgages).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="confirmedLoanPaymentSumAllMembersWarning" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="IncomeStatusBaseResponseCommon" abstract="true">
        <xs:annotation>
            <xs:documentation>BaseResponseCommon extended for current income type (main/other).</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="applicationDataCommon:BaseResponseCommonWithCoDebtor">
                <xs:sequence>
                    <xs:element name="currentIncomeType" type="loanCommon:IncomeTypeTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of current income.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="currentEconomicalStatus" type="applicationDataCommon:EconomicalStatusTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Type of current economical status.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>
