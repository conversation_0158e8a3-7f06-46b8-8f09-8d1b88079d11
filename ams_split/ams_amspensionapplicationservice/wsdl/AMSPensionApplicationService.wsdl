<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/ams/ws/application/pension"
                  targetNamespace="http://airbank.cz/ams/ws/application/pension">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/pension">
            <xs:include schemaLocation="AMSPensionApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Initialization the modelation step -->
    <wsdl:message name="InitModelationRequest">
        <wsdl:part element="InitModelationRequest" name="InitModelationRequest"/>
    </wsdl:message>
    <wsdl:message name="InitModelationResponse">
        <wsdl:part element="InitModelationResponse" name="InitModelationResponse"/>
    </wsdl:message>
    <wsdl:message name="InitModelationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitModelationFault"/>
    </wsdl:message>

    <!-- Update the modelation step -->
    <wsdl:message name="UpdateModelationRequest">
        <wsdl:part element="UpdateModelationRequest" name="UpdateModelationRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateModelationResponse">
        <wsdl:part element="UpdateModelationResponse" name="UpdateModelationResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateModelationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateModelationFault"/>
    </wsdl:message>

    <!-- Initialization the questionnaire step -->
    <wsdl:message name="InitQuestionnaireRequest">
        <wsdl:part element="InitQuestionnaireRequest" name="InitQuestionnaireRequest"/>
    </wsdl:message>
    <wsdl:message name="InitQuestionnaireResponse">
        <wsdl:part element="InitQuestionnaireResponse" name="InitQuestionnaireResponse"/>
    </wsdl:message>
    <wsdl:message name="InitQuestionnaireFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitQuestionnaireFault"/>
    </wsdl:message>

    <!-- Update the questionnaire step -->
    <wsdl:message name="UpdateQuestionnaireRequest">
        <wsdl:part element="UpdateQuestionnaireRequest" name="UpdateQuestionnaireRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateQuestionnaireResponse">
        <wsdl:part element="UpdateQuestionnaireResponse" name="UpdateQuestionnaireResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateQuestionnaireFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateQuestionnaireFault"/>
    </wsdl:message>

    <!-- Initialization the strategy selection step -->
    <wsdl:message name="InitStrategySelectionRequest">
        <wsdl:part element="InitStrategySelectionRequest" name="InitStrategySelectionRequest"/>
    </wsdl:message>
    <wsdl:message name="InitStrategySelectionResponse">
        <wsdl:part element="InitStrategySelectionResponse" name="InitStrategySelectionResponse"/>
    </wsdl:message>
    <wsdl:message name="InitStrategySelectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitStrategySelectionFault"/>
    </wsdl:message>

    <!-- Update the strategy selection step -->
    <wsdl:message name="UpdateStrategySelectionRequest">
        <wsdl:part element="UpdateStrategySelectionRequest" name="UpdateStrategySelectionRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateStrategySelectionResponse">
        <wsdl:part element="UpdateStrategySelectionResponse" name="UpdateStrategySelectionResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateStrategySelectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateStrategySelectionFault"/>
    </wsdl:message>

    <!-- Initialization the beneficiary selection step -->
    <wsdl:message name="InitBeneficiarySelectionRequest">
        <wsdl:part element="InitBeneficiarySelectionRequest" name="InitBeneficiarySelectionRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBeneficiarySelectionResponse">
        <wsdl:part element="InitBeneficiarySelectionResponse" name="InitBeneficiarySelectionResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBeneficiarySelectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBeneficiarySelectionFault"/>
    </wsdl:message>

    <!-- Update the beneficiary selection step -->
    <wsdl:message name="UpdateBeneficiarySelectionRequest">
        <wsdl:part element="UpdateBeneficiarySelectionRequest" name="UpdateBeneficiarySelectionRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBeneficiarySelectionResponse">
        <wsdl:part element="UpdateBeneficiarySelectionResponse" name="UpdateBeneficiarySelectionResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBeneficiarySelectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBeneficiarySelectionFault"/>
    </wsdl:message>

    <!-- Initialization the source of income step -->
    <wsdl:message name="InitSourceOfIncomeRequest">
        <wsdl:part element="InitSourceOfIncomeRequest" name="InitSourceOfIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSourceOfIncomeResponse">
        <wsdl:part element="InitSourceOfIncomeResponse" name="InitSourceOfIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSourceOfIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSourceOfIncomeFault"/>
    </wsdl:message>

    <!-- Update the source of income step -->
    <wsdl:message name="UpdateSourceOfIncomeRequest">
        <wsdl:part element="UpdateSourceOfIncomeRequest" name="UpdateSourceOfIncomeRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSourceOfIncomeResponse">
        <wsdl:part element="UpdateSourceOfIncomeResponse" name="UpdateSourceOfIncomeResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSourceOfIncomeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSourceOfIncomeFault"/>
    </wsdl:message>

    <!-- Initialization the summary step -->
    <wsdl:message name="InitSummaryRequest">
        <wsdl:part element="InitSummaryRequest" name="InitSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryResponse">
        <wsdl:part element="InitSummaryResponse" name="InitSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSummaryFault"/>
    </wsdl:message>

    <!-- Update the summary step -->
    <wsdl:message name="UpdateSummaryRequest">
        <wsdl:part element="UpdateSummaryRequest" name="UpdateSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryResponse">
        <wsdl:part element="UpdateSummaryResponse" name="UpdateSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSummaryFault"/>
    </wsdl:message>

    <!-- Request to resume pension application-->
    <wsdl:message name="ResumeApplicationRequest">
        <wsdl:part element="ResumeApplicationRequest" name="ResumeApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationResponse">
        <wsdl:part element="ResumeApplicationResponse" name="ResumeApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ResumeApplicationFault"/>
    </wsdl:message>
    
    <!-- Application cancel request -->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <!-- Initialization the standing order -->
    <wsdl:message name="InitStandingOrderRequest">
        <wsdl:part element="InitStandingOrderRequest" name="InitStandingOrderRequest"/>
    </wsdl:message>
    <wsdl:message name="InitStandingOrderResponse">
        <wsdl:part element="InitStandingOrderResponse" name="InitStandingOrderResponse"/>
    </wsdl:message>
    <wsdl:message name="InitStandingOrderFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitStandingOrderFault"/>
    </wsdl:message>

    <wsdl:portType name="PensionApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>
                Starts a new application for pension of an existing customer.
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitModelation">
            <wsdl:documentation>Operation used to initialize the modelationTask.</wsdl:documentation>
            <wsdl:input message="InitModelationRequest"/>
            <wsdl:output message="InitModelationResponse"/>
            <wsdl:fault name="InitModelationFault" message="InitModelationFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateModelation">
            <wsdl:documentation>Operation used to update the modelationTask.</wsdl:documentation>
            <wsdl:input message="UpdateModelationRequest"/>
            <wsdl:output message="UpdateModelationResponse"/>
            <wsdl:fault name="UpdateModelationFault" message="UpdateModelationFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitQuestionnaire">
            <wsdl:documentation>Operation used to initialize the questionnaireTask.</wsdl:documentation>
            <wsdl:input message="InitQuestionnaireRequest"/>
            <wsdl:output message="InitQuestionnaireResponse"/>
            <wsdl:fault name="InitQuestionnaireFault" message="InitQuestionnaireFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateQuestionnaire">
            <wsdl:documentation>Operation used to update the questionnaireTask.</wsdl:documentation>
            <wsdl:input message="UpdateQuestionnaireRequest"/>
            <wsdl:output message="UpdateQuestionnaireResponse"/>
            <wsdl:fault name="UpdateQuestionnaireFault" message="UpdateQuestionnaireFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitStrategySelection">
            <wsdl:documentation>Operation used to initialize the strategySelectionTask.</wsdl:documentation>
            <wsdl:input message="InitStrategySelectionRequest"/>
            <wsdl:output message="InitStrategySelectionResponse"/>
            <wsdl:fault name="InitStrategySelectionFault" message="InitStrategySelectionFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateStrategySelection">
            <wsdl:documentation>Operation used to update the strategySelectionTask.</wsdl:documentation>
            <wsdl:input message="UpdateStrategySelectionRequest"/>
            <wsdl:output message="UpdateStrategySelectionResponse"/>
            <wsdl:fault name="UpdateStrategySelectionFault" message="UpdateStrategySelectionFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitBeneficiarySelection">
            <wsdl:documentation>Operation used to initialize the beneficiarySelectionTask.</wsdl:documentation>
            <wsdl:input message="InitBeneficiarySelectionRequest"/>
            <wsdl:output message="InitBeneficiarySelectionResponse"/>
            <wsdl:fault name="InitBeneficiarySelectionFault" message="InitBeneficiarySelectionFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateBeneficiarySelection">
            <wsdl:documentation>Operation used to update the beneficiarySelectionTask.</wsdl:documentation>
            <wsdl:input message="UpdateBeneficiarySelectionRequest"/>
            <wsdl:output message="UpdateBeneficiarySelectionResponse"/>
            <wsdl:fault name="UpdateBeneficiarySelectionFault" message="UpdateBeneficiarySelectionFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSourceOfIncome">
            <wsdl:documentation>Operation used to initialize the sourceOfIncomeTask.</wsdl:documentation>
            <wsdl:input message="InitSourceOfIncomeRequest"/>
            <wsdl:output message="InitSourceOfIncomeResponse"/>
            <wsdl:fault name="InitSourceOfIncomeFault" message="InitSourceOfIncomeFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSourceOfIncome">
            <wsdl:documentation>Operation used to update the sourceOfIncomeTask.</wsdl:documentation>
            <wsdl:input message="UpdateSourceOfIncomeRequest"/>
            <wsdl:output message="UpdateSourceOfIncomeResponse"/>
            <wsdl:fault name="UpdateSourceOfIncomeFault" message="UpdateSourceOfIncomeFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="InitSummary">
            <wsdl:documentation>Operation used to initialize the summaryTask.</wsdl:documentation>
            <wsdl:input message="InitSummaryRequest"/>
            <wsdl:output message="InitSummaryResponse"/>
            <wsdl:fault name="InitSummaryFault" message="InitSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSummary">
            <wsdl:documentation>Operation used to initialize the summaryTask.</wsdl:documentation>
            <wsdl:input message="UpdateSummaryRequest"/>
            <wsdl:output message="UpdateSummaryResponse"/>
            <wsdl:fault name="UpdateSummaryFault" message="UpdateSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <wsdl:documentation>Resumes process of pension application. Use it instead of GetCurrentTask because this will also update actual task and restart
                scoring, if previous one expired.
            </wsdl:documentation>
            <wsdl:input message="ResumeApplicationRequest"/>
            <wsdl:output message="ResumeApplicationResponse"/>
            <wsdl:fault name="ResumeApplicationFault" message="ResumeApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitStandingOrder">
            <wsdl:documentation>Operation used to initialize the standing order to pay pension savings.</wsdl:documentation>
            <wsdl:input message="InitStandingOrderRequest"/>
            <wsdl:output message="InitStandingOrderResponse"/>
            <wsdl:fault name="InitStandingOrderFault" message="InitStandingOrderFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="PensionApplicationBinding" type="PensionApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitModelation">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitModelationFault">
                <soap:fault name="InitModelationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateModelation">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateModelationFault">
                <soap:fault name="UpdateModelationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitQuestionnaire">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitQuestionnaireFault">
                <soap:fault name="InitQuestionnaireFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateQuestionnaire">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateQuestionnaireFault">
                <soap:fault name="UpdateQuestionnaireFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitStrategySelection">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitStrategySelectionFault">
                <soap:fault name="InitStrategySelectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateStrategySelection">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateStrategySelectionFault">
                <soap:fault name="UpdateStrategySelectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitBeneficiarySelection">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBeneficiarySelectionFault">
                <soap:fault name="InitBeneficiarySelectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateBeneficiarySelection">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBeneficiarySelectionFault">
                <soap:fault name="UpdateBeneficiarySelectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSourceOfIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSourceOfIncomeFault">
                <soap:fault name="InitSourceOfIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSourceOfIncome">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSourceOfIncomeFault">
                <soap:fault name="UpdateSourceOfIncomeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSummaryFault">
                <soap:fault name="InitSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSummaryFault">
                <soap:fault name="UpdateSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ResumeApplicationFault">
                <soap:fault name="ResumeApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitStandingOrder">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitStandingOrderFault">
                <soap:fault name="InitStandingOrderFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="PensionApplicationService">
        <wsdl:documentation>
            Service providing operations related to pension application of an existing customer.

            The application has the following task IDs:
            - modelationTask - Modelation of pension default strategy
            - questionnaireTask - Pension questionnaire to get appropriate invest strategy
            - strategySelectionTask - Select invest strategy
            - beneficiarySelectionTask - Set beneficiaries
            - summaryTask - Summary of all inserted data

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="PensionApplicationPort" binding="PensionApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/pension"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>