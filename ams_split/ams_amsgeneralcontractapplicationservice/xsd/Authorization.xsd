<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://airbank.cz/ams/ws/application/authorization/"
        xmlns:Q1="http://airbank.cz/ams/ws/application/authorization/"
        xmlns="http://www.w3.org/2001/XMLSchema"
        elementFormDefault="qualified" version="1.0">

    <complexType name="AuthType">
        <annotation>
            <documentation>
                Autorizační zá<PERSON>nam, který se použije ve všech metodách vyžadující autorizaci.
            </documentation>
        </annotation>
        <sequence>
            <element name="password" type="xsd:base64Binary" minOccurs="0"
                     maxOccurs="1">
                <annotation>
                    <documentation>statické heslo</documentation>
                </annotation>
            </element>
            <element name="authCodeType" minOccurs="0"
                     maxOccurs="1">
                <annotation>
                    <documentation>
                        <PERSON><PERSON> auto<PERSON><PERSON><PERSON><PERSON> kod<PERSON>, r<PERSON><PERSON><PERSON> o jaky typ se jedna SMS, callID, ...
                    </documentation>
                </annotation>
                <simpleType>
                    <restriction base="xsd:string">
                        <!-- Authentication by SMS and password -->
                        <enumeration value="PWD_OTP"/>
                        <!-- Authentication by SMS -->
                        <enumeration value="OTP"/>
                        <!-- Authentication password in IBS prolonged mode -->
                        <enumeration value="PWD"/>
                        <!-- Authentication by security questions -->
                        <enumeration value="QUESTION"/>
                        <!-- No authentication required -->
                        <enumeration value="NO_AUTH"/>
                        <!-- Authentication by e-sign -->
                        <enumeration value="SIGNPAD"/>
                        <!-- Authentication by general sign-->
                        <enumeration value="BLUE_SIGN"/>
                        <!-- Customer login authentication - special case -->
                        <enumeration value="PWD_PPF"/>
                        <!-- Authentication by customer agree and operator authentication -->
                        <enumeration value="CALL_ID"/>
                        <!-- Authentication by new password and password -->
                        <enumeration value="PWDCMD"/>
                        <enumeration value="PWD_SPB"/>

                    </restriction>
                </simpleType>
            </element>
            <element name="authCode" type="xsd:string" minOccurs="0"
                     maxOccurs="1">
                <annotation>
                    <documentation>
                        Jednorázový kód (Digipass, SMS, idHovoru, atd. ) v zavislosti na AuthCodeType
                    </documentation>
                </annotation>
            </element>
            <element name="secQuestion" type="Q1:ObsSecQuestionTO"
                     minOccurs="0" maxOccurs="unbounded">
                <annotation>
                    <documentation>bezpečnostní otázky</documentation>
                </annotation>
            </element>
            <element name="operationDesc" type="xsd:string"
                     minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>
                        unikátní řetězec identifikující operaci (nejlépe
                        serializovaná data z formuláře) Používá se ve
                        spojení s autorizací pomocí SMS
                    </documentation>
                </annotation>
            </element>
            <element name="authId" type="xsd:string" minOccurs="0" maxOccurs="1">
                <annotation>
                    <documentation>id of external authorization</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

    <complexType name="LoginAuthType">
        <annotation>
            <documentation>autentizační záznam pro login</documentation>
        </annotation>
        <complexContent>
            <extension base="Q1:AuthType">
                <sequence>
                    <element name="userName" type="xsd:string"
                             minOccurs="1" maxOccurs="1">
                        <annotation>
                            <documentation>
                                uživatelské jméno
                            </documentation>
                        </annotation>
                    </element>
                    <element name="parameter" type="xsd:string"
                             minOccurs="0" maxOccurs="1">
                        <annotation>
                            <documentation>
                                třetí autentizační parametr (datum
                                narození) -- datum bude zadáno ve
                                fromátu yyyy-mmm-dd
                            </documentation>
                        </annotation>
                    </element>
                    <element name="ipAddress" type="xsd:string" maxOccurs="1" minOccurs="0">
                        <annotation>
                            <documentation>ip adresa prihlasovaneho uzivatele</documentation>
                        </annotation>
                    </element>
                </sequence>
            </extension>
        </complexContent>
    </complexType>

    <complexType name="ObsSecQuestionTO">
        <annotation>
            <documentation>Security question record.</documentation>
        </annotation>
        <sequence>
            <element name="code" type="string" maxOccurs="1" minOccurs="1">
                <annotation>
                    <documentation>kód otázky</documentation>
                </annotation>
            </element>
            <element name="text" type="string" maxOccurs="1" minOccurs="1">
                <annotation>
                    <documentation>text bezpečnostní otázky nebo odpověď</documentation>
                </annotation>
            </element>
        </sequence>
    </complexType>

</schema>
