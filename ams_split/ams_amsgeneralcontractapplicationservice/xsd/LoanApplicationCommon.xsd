<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:obsAuth="http://airbank.cz/ams/ws/application/authorization/"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/common/loan"
           targetNamespace="http://airbank.cz/ams/ws/application/common/loan">

    <xs:annotation>
        <xs:documentation>Common types for loan application</xs:documentation>
    </xs:annotation>

    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/authorization/" schemaLocation="Authorization.xsd"/>


    <xs:complexType name="BaseResponseCommon">
        <xs:annotation>
            <xs:documentation>Parent for ResponseCommon objects containing SpecificResponseCommon data.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanApplicationType" type="LoanApplicationTypeTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Selected loan application type code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitConsolidationDashboardForWalkinResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing consolidationDashboardForWalkinTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minCredit" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minimal loan credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxCredit" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Maximal loan credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateConsolidationDashboardForWalkinRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of first page for consolidation application for walkin.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Loan amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitLoanParametersResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing loanParametersTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="possibleLoanApplicationTypes" type="LoanApplicationTypeTO" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of codes of possible loan types.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="possibleConsolidationObligationTypes" type="xs:string" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of codes of possible obligation types. It is enumeration with possible values CREDIT_CARD, CASHLOAN or
                                OVERDRAFT.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxLoanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximum loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="availableLoanBin" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Available loan bin amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="productMax" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Product max amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="displayLoanBin" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag for display logic.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="displayProductMax" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag for display logic.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationData" type="LoanObligationTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of Obligations (CashLoans, CreditCards and Overdrafts) to consolidate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="readOnly" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Indicates that screen should be read only - i.e. no data could be changed on it.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="changeLoanTypeForbidden" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Indicates that it is forbidden to change type of loan application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="deceasedCustomers" type="appCommon:PersonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of customers that are in inheritance relation with applicant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="deceasedCuid" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>ID of selected customer who is in inheritance relation with applicant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Type of special case.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Note to special case.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO" minOccurs="0"/>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Used upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountToRepay" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Loan amount without upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minInstalment" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimum instalment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxInstalment" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximum instalment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalment" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Instalment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="standardLoanVariant" type="LoanVariantTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Standard loan variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusLoanVariant" type="LoanVariantTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Bonus loan variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountMin" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Min amount of upsell for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountMax" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Max amount of upsell for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="customerCareCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Customer care code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Code of process.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitLoanCalculationResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing loanCalculationTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="minCredit" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minimum loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxCredit" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Maximum loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Loan amount. Credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minInstallment" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minumum installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxInstallment" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Maximum installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installment" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="repaymentPeriod" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Interest rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="annualPercentageRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Annual percentage rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="totalLoanCost" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Total loan cost.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxInterestRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>MAximum interest rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxAnnualPercentageRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Maximum annual percentage rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO"/>
                    <xs:element name="standardLoanVariantRepaymentPeriod" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Repayment period for standard variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxTotalLoanCost" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximal total loan cost.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="displayLoanBinAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Available loan bin amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitLoanDataReuseResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing loanParametersTask screen and previous loan usable for data reuse has been found.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="previousLoanApplicationDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>If previousLoanApplicationFound attribute is true then the attribute indicates creation date of the loan
                                application found.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="previousLoanApplicationType" type="LoanApplicationTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>If previousLoanApplicationFound attribute is true then the attribute indicate previous loan application type.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateLoanParametersRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of first page for loan application.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="selectedLoanApplicationType" type="LoanApplicationTypeTO">
                <xs:annotation>
                    <xs:documentation>Selected loan application type code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="consolidationData" type="LoanObligationTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of Obligations (CashLoans, CreditCards and Overdrafts) to consolidate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deceasedCuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>ID of selected customer who is in inheritance relation with applicant.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Type of special case.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Note to special case.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Used upsell.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Repayment period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceOfferChecked" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Instalment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="customerCareCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Customer care code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="skipPreScoring" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Indicates if consolidation TOP-UP prescoring has to be skipped. True used only for consolidation TOP-UP applications started in old version of MA without prescoring implementation.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UpdateLoanParametersResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when updating loanParametersTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationData" type="LoanObligationTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of Obligations (CashLoans, CreditCards and Overdrafts) to consolidate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountCorrected" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Flag that loan amount has been corrected.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateLoanCalculationRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of first page for loan application for mobile.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Loan amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Loan instalment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceOfferChecked" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="skipPreScoring" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Indicates if cash-loan prescoring has to be skipped. True used only for cashLoan applications started in old version of MA without prescoring implementation.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UpdateLoanCalculationResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when updating loanCalculationTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="InitLoanCalculationResponseCommon"/>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AmountsProvidedTO">
        <xs:sequence>
            <xs:element name="knownIncomes" type="KnownIncomeTO" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="loanPaymentsSum" type="appCommon:MonetaryAmountTO" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="KnownIncomeTO">
        <xs:sequence>
            <xs:element name="amount" type="appCommon:MonetaryAmountTO"/>
            <xs:element name="independentSource" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitCoDebtorOfferResponseCommon">
        <xs:annotation>
            <xs:documentation>Init of coDebtorOfferTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="coDebtorData" type="CoDebtorDataTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of co-debtors.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coDebtorsMaxLimit" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximum number of co-debtors.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="progressRatio" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>indicates progress ratio over application data section</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="InitLoanWaitingResponseCommon">
        <xs:annotation>
            <xs:documentation>Init of loanWaitingTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="approveResult" type="appCommon:ApproveResultTO">
                        <xs:annotation>
                            <xs:documentation>Result of LAP processing of the loan application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="rejectReasonCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Result of approve request of the loan application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="economicalStatusCode" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Main economical status code.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitAlternativeToCashLoanResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing alternativeToCashLoanTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="cashLoanStatus" type="AlternativeToCashLoanStatus">
                        <xs:annotation>
                            <xs:documentation>Status of cash loan.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cashLoanMaxAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Cash loan maximum amount offered to the client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationMaxAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Consolidation maximum amount offered to the client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validity" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Number of days for which the alternative offer is valid.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="commitments" type="Commitments" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Data about current commitments.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationOffers" type="ConsolidationOffers" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Data about consolidation offer.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cashLoanOffers" type="CashLoanOffers" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Data about cash loan offer.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="consolidationOfferSelectable" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Consolidation offer is selectable by client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="Commitments">
        <xs:annotation>
            <xs:documentation>Data about commitments.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="unpaidAmountSum" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Sum of unpaid commitments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="count" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Count of unpaid commitments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentSum" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Sum of instalment of unpaid commitments.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ConsolidationOffers">
        <xs:annotation>
            <xs:documentation>Data about consolidation offer.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AlternativeOffers">
                <xs:sequence>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Used upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanCount" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Count of loans after consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="CashLoanOffers">
        <xs:annotation>
            <xs:documentation>Data about cash loan offer.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AlternativeOffers">
                <xs:sequence>
                    <xs:element name="loanAmountByClient" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Loan amount requested by client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AlternativeOffers">
        <xs:annotation>
            <xs:documentation>Data about alternative offer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanAmountApproved" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Approved loan amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalment" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Instalment of variant.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="standardOffer" type="AlternativeOffer">
                <xs:annotation>
                    <xs:documentation>Data about standard variant of variant offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bonusOffer" type="AlternativeOffer">
                <xs:annotation>
                    <xs:documentation>Data about bonus variant of variant offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AlternativeOffer">
        <xs:annotation>
            <xs:documentation>Data about alternative offer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="interestRate" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Interest rate of offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="apr" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Annual percentage rate of offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repaymentPeriod" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Repayment period of offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalCost" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Total cost of offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitAlternativeToConsolidationResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing alternativeToConsolidationTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="cashLoanMaxAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Cash loan maximum amount offered to the client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scoringResult" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Scoring result of offered cash loan application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validity" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Number of days for which the alternative offer is valid.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitMortgageAcceptedResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing mortgageAcceptedTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="MortgageAcceptedResponseCommon">
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="MortgageAcceptedResponseCommon">
        <xs:annotation>
            <xs:documentation>Mortgage accepted page. Used in case of tweaking refinance.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="approveResult" type="appCommon:ApproveResultTO">
                        <xs:annotation>
                            <xs:documentation>Result of LAP processing of the loan application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="rejectReasonCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Result of approve request of the loan application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numOfExpirationDay" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Validity of the offer in days.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestedAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Requested loan amount by the client.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitScoringResultAndLoanSettingsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned for accepted loan and loan settings.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="installmentDayMaximum" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximum day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentDay" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentDayMinimum" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimum day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfMonths" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of months in which loan will be paid. Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minInstallmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minimum installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maximumInstallmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minimum installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestedAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Amount requested by client</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="validity" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of days offer is valid for</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountMaximum" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Maximum loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Loan amount. Credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountMinimum" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minimum loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstPayment" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of payment of first installment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusVariants" type="LoanVariantTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of loan variants.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="defaultVariantIndex" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Index of default variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isFastApplicationProcess4Mobile" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Fast/slow loan variant</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="alternativeToConsolidation" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Alternative consolidation offer exists</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="newServiceAccount" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Flag showing if exist application for new service account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="serviceAccountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Service account id.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="serviceAccountName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Service account name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="serviceAccountNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Service account number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="availableAccounts" type="appCommon:AccountTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of available account, that can be used as service. Filled just in case no account is selected.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO"/>
                    <xs:element name="loanAmountEditable" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether loan amount is editable or not.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="predefinedUpsellOffer" type="PredefinedUpsellOfferTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Predefined upsell offer - filled if scoring approved higher amount (loanAmountMaximum > requestedLoanAmount)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="expirationDate" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date when the application automatically expires if not finished by client</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateScoringResultAndLoanSettingsRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of first page for loan application for mobile.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="installmentDay" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Day of payment in each month.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Installment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Loan amount. Credit.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="serviceAccountId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Service account id..</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="serviceAccountName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Name of the service account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceOfferChecked" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="predefinedUpsellOfferAccepted" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Client accepts predefined upsell offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UpdateScoringResultAndLoanSettingsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when updating loanCalculationTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="InitScoringResultAndLoanSettingsResponseCommon">
                <xs:sequence>
                    <xs:element name="htmlBinDocumentId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of HTML version of preagreement document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pdfBinDocumentId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of PDF version of preagreement document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitRepaymentWayResponseCommon">
        <xs:annotation>
            <xs:documentation>Init of repaymentWayTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="newServiceAccount" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Flag showing if exist application for new service account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="serviceAccountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Service account id.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="serviceAccountName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Name of the service account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="serviceAccountNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Service account number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="availableAccounts" type="appCommon:AccountTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of available account, that can be used as service. Filled just in case no account is selected.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scoreResultType" type="LoanProductScoreResultTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Product score result. Set in case of application is refinance or consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="financialInstitutionCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Financial institution code. Set in case of application is refinance or consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="defaultDistributionChannel" type="appCommon:DistributionChannelTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Distribution channel that is by default shown at first tab.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestedDocuments" type="RequestedDocumentsTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Map of distribution channels and required documents for each channel.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coDebtorRequestedDocuments" type="CoDebtorRequestedDocumentsTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Map of distribution channels and required documents for each channel for co-debtor.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateRepaymentWayRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of repaymentWayTask screen.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="serviceAccountId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Service account id..</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="serviceAccountName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Name of the service account.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitConsolidationAcceptedAndSettingsResponseCommon">
        <xs:annotation>
            <xs:documentation>Init of consolidationAcceptedAndSettingsTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="obligationCheckResult" type="ObligationCheckResultTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Obligation check results.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Refinance addition amount. Addition credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountByClient" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Refinance addition amount. Addition credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountMin" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Refinance addition amount. Addition credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountMax" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Refinance addition amount. Addition credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfMonths" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Number of months in which loan will be paid. Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfMonthsMin" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Number of months in which loan will be paid. Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfMonthsMax" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Number of months in which loan will be paid. Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="newServiceAccount" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Flag showing if exist application for new service account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedAccountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of the selected account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedAccountName" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Name of the selected account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedAccountNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of selected account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="availableAccounts" type="appCommon:AccountTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of available account, that can be used as service. Filled just in case no account is selected.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentDay" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentDayMinimum" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Minimum day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentDayMaximum" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Maximum day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstPayment" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of payment of first installment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO"/>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Refinance addition amount. Addition credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Instalment amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentAmountMin" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimal instalment amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentAmountMax" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximal instalment amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Current interest rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="apr" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Current annual percentage rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="normalVariant" type="LoanVariantTO">
                        <xs:annotation>
                            <xs:documentation>Normal loan variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusVariant" type="LoanVariantTO">
                        <xs:annotation>
                            <xs:documentation>Bonus loan variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountEditable" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether loan amount is editable or not.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Code of process.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="predefinedUpsellOffer" type="PredefinedUpsellOfferTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Predefined upsell offer - filled if scoring approved higher amount (loanAmountMaximum > requestedLoanAmount)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateConsolidationAcceptedAndSettingsRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of consolidationAcceptedAndSettingsTask screen.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Refinance addition amount. Addition credit.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfMonths" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Number of months in which loan will be paid. Repayment period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="selectedAccountId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Selected account id..</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentDay" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Day of payment in each month.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceOfferChecked" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Instalment amount</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="predefinedUpsellOfferAccepted" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Client accepts predefined upsell offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UpdateConsolidationAcceptedAndSettingsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when updating consolidationAcceptedAndSettings screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Current upsell amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountMin" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Minimum amount of upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmountMax" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Maximum amount of upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Installment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentAmountMin" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Minimum amount of instalment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentAmountMax" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Maximum amount of instalment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="sumObligationsAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Sum of amount of selected obligations.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Normal variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="normalVariant" type="LoanVariantTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Normal variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusVariant" type="LoanVariantTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="htmlBinDocumentId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of HTML version of preagreement document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pdfBinDocumentId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of PDF version of preagreement document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="predefinedUpsellOffer" type="PredefinedUpsellOfferTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Predefined upsell offer - filled if scoring approved higher amount (loanAmountMaximum > requestedLoanAmount)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitLoanSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing loanSummaryTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="applicationId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of loan application.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Name of the loan.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Loan amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Month payment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Upsell amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="totalConsolidationAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Total consolidation loan amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scoreResultType" type="LoanProductScoreResultTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Refinance scoring result</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="obligations" type="ObligationCheckResultTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Obligations</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="installmentDay" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstPayment" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of payment of first installment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="interestRate" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Interest rate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateBonus" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Interest rate for bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minRPSN" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Min RPSN.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minRPSNBonus" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Min RPSN for bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxRPSN" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Max RPSN.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxRPSNBonus" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Max RPSN for bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxTotal" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Max total amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxTotalBonus" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Max total amount for bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minTotal" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Min total amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minTotalBonus" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Min total amount for bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Bonus amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="bonusAmountBonus" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Bonus amount for bonus variant.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfMonths" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of months in which loan will be paid. Standard repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numberOfMonthsBonus" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of months in which loan will be paid. Bonus repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastInstallmentDay" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of payment of last installment. Standard payment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastInstallmentDayBonus" type="xs:date" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Date of payment of last installment. Bonus payment.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="fee1" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Fee 1.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fee1Bonus" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Fee 1 bonus.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fee2" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Fee 2.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fee2Bonus" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Fee 2 bonus.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of service account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Name of service account. Send in case we don't have id = client is walk-in.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="hasBonus" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Has bonus.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="numOfExpirationDay" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Validity of the offer in days.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="htmlBinDocumentId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of HTML version of preagreement document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="pdfBinDocumentId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of PDF version of preagreement document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insurancePrice" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Price of accepted insurance.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="UpdateLoanSummaryRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of loanSummaryTask screen.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Requested loan name.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitLoanCancelledResponseCommon">
        <xs:annotation>
            <xs:documentation>Init of loanCancelledTask screen.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="rejectReasonClient" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Reject reason client.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UpdateLoanSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Update response of loanSummaryTask screen.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="insuranceOfferEligible" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitMortgageParametersResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing mortgageParametersTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="maxNumberOfConstructionSavingsLoanObligations" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Maximum number of all obligations.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxNumberOfMortgageObligations" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Maximum number of mortgage obligations.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxNumberOfAllObligations" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Maximum number of all obligations.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxUpsellAmount" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Maximum amount of upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="obligations" type="MortgageObligationTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>List of Obligations (Mortgage Loan, Construction Savings) to refinance.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedFixationPeriod" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Selected fixation period (5, 7, 10, ... yers).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="possibleObligationTypes" type="MortgageObligationTypeTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Possible obligation type for mortgage refinance.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="amountFromPublicWeb" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Mortgage refinance amount from public web.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Total requested amount including upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Requested upsell amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="realEstatePriceEstimate" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Estimated price of real estate.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Request description.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="requestType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Type of request description.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellReason" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Reason for upsell.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateMortgageParametersRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of first page for mortgage refinance application.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="selectedLoanApplicationType" type="LoanApplicationTypeTO">
                <xs:annotation>
                    <xs:documentation>Selected loan application type code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="selectedFixationPeriod" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Selected fixation period (5, 7, 10, ... yers).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="obligations" type="MortgageObligationTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of Obligations (Mortgage Loan, Construction Savings) to refinance.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Total requested amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan upsell amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="realEstatePriceEstimate" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Estimated price of real estate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestDescription" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Request description.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Type of request description.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="upsellReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason for upsell.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="downsellConfirmed" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Downsell confirmed by client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceRemovalConfirmed" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Confirmed removing payment protection insurance</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageSettingsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing mortgageSettingsTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="repaymentLength" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Repayment length in years</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fixationPeriodLength" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Length of selected fixation period 3/5</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateType" type="MortgageInterestRateTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Selected interest rate type FIX/FLOAT</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentDayMaximum" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximum day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentDay" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentDayMinimum" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimum day of payment in each month.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Mortgage loan amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Loan upsell amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Value of instalment amount for current year</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="standardSchedule" type="MortgageLoanOfferTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Standard schedule</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="scheduleWithOTB" type="MortgageLoanOfferTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Schedule with OTB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minTerm" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimal repayment length in years</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="maxTerm" type="xs:int" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Maximal repayment length in years</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO"/>
                    <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateMortgageSettingsRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of mortgage settings page.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="fixationPeriodLength" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Length of selected fixation period 3/5</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRateType" type="MortgageInterestRateTypeTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Selected interest rate type FIX/FLOAT</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentDay" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Day of the month installments</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repaymentLength" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Repayment length in years</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceOfferChecked" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="insuranceRemovalConfirmed" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Confirmed removing payment protection insurance</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitMortgageSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing mortgageSummaryTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="refinanceAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Amount to refinance obligation loans</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Amount to finance other housing-related needs</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="upsellReasonCode" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Upsell reason.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="downsellAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Amount to finance other housing-related needs</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of service account.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Name of service account. Send in case we don't have id = client is walk-in.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="finInstitutions" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Financial institution which are commitments to refinance</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentDay" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Day of the month installments</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="firstInstalmentDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>Date of the first installment</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="lastInstalmentDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>Date of the last installment</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Mortgage loan amount</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRate" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Amount of interest rate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateWithOtb" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Amount of interest rate with OTB</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="capInterestRate" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Amount of max interest rate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateType" type="MortgageInterestRateTypeTO">
                        <xs:annotation>
                            <xs:documentation>Interest rate type FIX/FLOAT</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="fixationPeriodLength" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Length of fixation period 3/5</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Amount of monthly instalment</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="capInstalmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Amount of max monthly instalment for cap interest rate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minInstalmentAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Minimal amount of monthly instalment</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minCapInstalmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Minimal amount of monthly instalment for cap interest rate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="numOfExpirationDays" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Number of days to expire offer</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="coDebtors" type="CoDebtorDataTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>All co-debtors data</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferEligible" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer is eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferChecked" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceOfferRejectReason" type="InsuranceOfferRejectReasonTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Reason why insurance offer is not eligible.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO"/>
                    <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateMortgageSummaryRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of mortgage summary page.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="insuranceRemovalConfirmed" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Confirmed removing payment protection insurance</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitMortgageAcceptedSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing mortgageSummaryTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="MortgageAcceptedResponseCommon">
                <xs:sequence>
                    <xs:element name="summaryData" type="InitMortgageSummaryResponseCommon">
                        <xs:annotation>
                            <xs:documentation>Mortgage summary data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="settingsData" type="MortgageSettingsResponseCommon">
                        <xs:annotation>
                            <xs:documentation>Mortgage settings data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="repaymentWayData" type="InitRepaymentWayResponseCommon">
                        <xs:annotation>
                            <xs:documentation>Mortgage settings data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insuranceInterestRateDivergence" type="xs:decimal" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Amount of insurance interest rate divergence</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateMortgageAcceptedSummaryRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of mortgage settings page.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="repaymentWayData" type="UpdateRepaymentWayRequestCommon">
                <xs:annotation>
                    <xs:documentation>Repayment way data</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="settingsData" type="UpdateMortgageSettingsRequestCommon">
                <xs:annotation>
                    <xs:documentation>Mortgage settings data</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UpdateMortgageAcceptedSummaryResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when updating loanSettingsTask screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="MortgageSettingsResponseCommon">
                <xs:sequence>

                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="OfferedRepaymentSetTO">
        <xs:annotation>
            <xs:documentation>Values ​​interest rate for current fixation period.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="fixationPeriod" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Current fixation period</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRateType" type="MortgageInterestRateTypeTO">
                <xs:annotation>
                    <xs:documentation>Type of interest rate</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageLoanOfferTO">
        <xs:annotation>
            <xs:documentation>Mortgage offer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="interestRate" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Value of interest rate for current length of repayment period</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="apr" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>APR</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="marketingInterestRate" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfInstalments" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastInstalmentDate" type="xs:date">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mortgageCashbackValue" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageLoanObligationTO">
        <xs:annotation>
            <xs:documentation>Mortgage loan obligation</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="MortgageObligationTO">
                <xs:sequence>
                    <xs:element name="refinanceDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>When to transfer mortgage (date of refixation).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ConstructionSavingsLoanObligationTO">
        <xs:annotation>
            <xs:documentation>Construction savings loan.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="MortgageObligationTO">
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InternalCashLoanObligationTO">
        <xs:annotation>
            <xs:documentation>Cash loan.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="LoanObligationTO">
                <xs:sequence>
                    <xs:element name="residualAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Amount to be repaid</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="origNumber" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Internal loan number.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="origName" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Internal loan name.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="origLoanId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Internal loan id.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="isConsolidable" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Can consolidate internal cash loan?</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="originalUnpaidPrincipal" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Unpaid principal of original internal cashloan/consolidation application</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="originalApplicationType" type="appCommon:ApplicationTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>The type of original internal cashloan/consolidation application</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="startDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>Obligation 'valid from' date.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="paymentHolidayActivated" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Original loan has payment holiday activated.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ExternalLoanObligationTO" abstract="true">
        <xs:annotation>
            <xs:documentation>External loan obligations.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="LoanObligationTO">
                <xs:sequence>
                    <xs:element name="startDate" type="xs:date">
                        <xs:annotation>
                            <xs:documentation>Obligation 'valid from' date.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CashLoanObligationTO">
        <xs:annotation>
            <xs:documentation>Cash loan.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ExternalLoanObligationTO">
                <xs:sequence>
                    <xs:element name="numberOfInstalments" type="xs:int">
                        <xs:annotation>
                            <xs:documentation>Repayment period.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="unpaidPrincipal" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Unpaid amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="CreditCardObligationTO">
        <xs:annotation>
            <xs:documentation>Credit card.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ExternalLoanObligationTO">
                <xs:sequence>
                    <xs:element name="refinanceMaxLoanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Original maximal credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="usedRecently" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Credit card used in last 6 months.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="OverdraftObligationTO">
        <xs:annotation>
            <xs:documentation>Account overdraft.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ExternalLoanObligationTO">
                <xs:sequence>
                    <xs:element name="refinanceMaxLoanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Original maximal credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="usedRecently" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Overdraft used in last 6 months.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ObligationTO" abstract="true">
        <xs:annotation>
            <xs:documentation>Shared parent for CashLoan, CreditCard and OverDraft.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanCommitmentId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Id in database, in case it's already saved.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="chargeAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Charge obligation amount (monthly fee).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="financialInstitution" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Issuing financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="obligationOrder" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Obligation order, it's set by IB to identify obligation update</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanObligationTO" abstract="true">
        <xs:annotation>
            <xs:documentation>Shared parent for loan obligation.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="ObligationTO">
                <xs:sequence>
                    <xs:element name="totalAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Total obligation amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="instalmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Instalment amount.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedInApplication" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>This obligation is selected for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectableInApplication" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>This obligation is selectable for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="notSelectableInApplicationReason" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Reason why this obligation is not selectable for consolidation.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="MortgageObligationTO">
        <xs:annotation>
            <xs:documentation>Shared parent for Mortgage Loan and Construction Savings loan.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="LoanObligationTO">
                <xs:sequence>
                    <xs:element name="refinanceMaxLoanAmount" type="appCommon:MonetaryAmountTO">
                        <xs:annotation>
                            <xs:documentation>Original maximal credit.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InterestRateTO">
        <xs:annotation>
            <xs:documentation>Shared parent for Mortgage Loan and Construction Savings loan.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="interestRate" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Interest rate for fixation period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fixationPeriod" type="xs:int">
                <xs:annotation>
                    <xs:documentation>Fixation period (5, 7, 10, ... yers).</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ObligationCheckResultTO">
        <xs:annotation>
            <xs:documentation>Obligation check result.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Reason of rejection.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installment" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Monthly installment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentPayment" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Monthly fee payment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Total amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unpaidAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Unpaid amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Max amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfInstallments" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Number of installments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rpsn" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="startDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Obligation 'valid from' date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="obligationType" type="ObligationTypeTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Obligation type [LOAN, CARD, OVERDRAFT].</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Original amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="ObligationCheckResultStatusTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Status of obligation check result.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="financialInstitution" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Issuing financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="internal" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Is internal or external commitment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="origLoanName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Name of original loan.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRate" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Interest rate of internal cash loan obligation - for TOP_UP only.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentProtectionInsurance" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Is internal cash loan obligation insured - for TOP_UP only.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanVariantTO">
        <xs:annotation>
            <xs:documentation>Loan variant.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="interestRate" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Interest rate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rpsn" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Total amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bonus" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Bonus.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfInstallments" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Number of installments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="orderNumber" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Order.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageTO">
        <xs:sequence>
            <xs:element name="installmentDay" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Installment day.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Installment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="otbInstallmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Amount saved to open-to-buy.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="nextInstallmentDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Next instalment date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastInstallmentDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Next instalment date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interestRate" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Interest rate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unpaidPrincipal" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Unpaid principal.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="otbAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Amount of money saved on open-to-buy account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="annualPercentageRate" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Annual percentage rate.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanTO">
        <xs:sequence>
            <xs:element name="installmentDay" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Installment day.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Installment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="nextInstallmentDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Next instalment date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfInstallments" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Number of remaining installments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanInterest" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan interest.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rpsn" type="xs:float" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalLoanCost" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Installment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unpaidPrincipal" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Installment amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bonusDetail" type="LoanBonusTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Detail of loan bonus.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bonusExisted" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Indicates if bonus existed.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanBonusTO">
        <xs:sequence>
            <xs:element name="bonus" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan bonus amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Bonus repayment period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="numberOfInstallments" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Number of remaining installments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanInterest" type="xs:float" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan bonus interest.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rpsn" type="xs:float" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>RPSN.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalLoanCost" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Total loan bonus cost.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="orderNumber" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Order.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RequestedDocumentsTO">
        <xs:annotation>
            <xs:documentation>Document group for distribution channel.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="distributionChannel" type="appCommon:DistributionChannelTypeTO">
                <xs:annotation>
                    <xs:documentation>Type of distribution channel.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroup" type="appCommon:DocumentGroupTO" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Document groups.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CoDebtorRequestedDocumentsTO">
        <xs:annotation>
            <xs:documentation>Document group for distribution channel.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="coDebtor" type="CoDebtorDataTO">
                <xs:annotation>
                    <xs:documentation>Data of the co-debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requestedDocuments" type="RequestedDocumentsTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Map of distribution channels and required documents for each channel.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CoDebtorDataTO">
        <xs:annotation>
            <xs:documentation>Co-debtor data.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="firstName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Co-debtors first name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Co-debtors last name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="title" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Co-debtors title.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="titleAfter" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Co-debtors title after name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthday" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Co-debtors birthday.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicantId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Co-debtors applicant id.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Co-debtors customer id.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="isFinished" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Marks that co-debtor is finished</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BaseMortgageChangeStartRequest">
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractStartRequest">
                <xs:sequence>
                    <xs:element name="mortgageId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Id of mortgage to change.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="BaseMortgageChangeUpdateChangeResponse">
        <xs:annotation>
            <xs:documentation>Shared response parent for all updateChange calls for mortgage.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractUpdateResponse">
                <xs:sequence>
                    <xs:element name="originalMortgage" type="MortgageTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Original mortgage details.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="updatedMortgage" type="MortgageTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Updated mortgage details.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accepted" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Change accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>If change has not been accepted, reason why.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="premium" type="InitChangePremiumTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Initialization data of change premium.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="BaseMortgageChangeInitResponse">
        <xs:annotation>
            <xs:documentation>Shared response parent for all initSummary calls for mortgage.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractInitResponse">
                <xs:sequence>
                    <xs:element name="originalMortgage" type="MortgageTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Original mortgage details.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="updatedMortgage" type="MortgageTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Updated mortgage details.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="premium" type="InitChangePremiumTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Initialization data of change premium.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="BaseMortgageChangeUpdateSummaryRequest">
        <xs:annotation>
            <xs:documentation>Shared request for update of summary page for change mortgage applications.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractUpdateRequest">
                <xs:sequence>
                    <xs:element name="authentication" type="obsAuth:AuthType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Solved OBS authentication.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="BaseMortgageChangeUpdateSummaryResponse">
        <xs:annotation>
            <xs:documentation>Shared response for update of summary page for change mortgage applications.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractUpdateResponse">
                <xs:sequence>
                    <xs:element name="accepted" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Change accepted.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="rejectReason" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>If change has not been accepted, reason why.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitInsuranceBenefitsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing insurance benefits screen.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BaseResponseCommon">
                <xs:sequence>
                    <xs:element name="insuranceCalculation" type="InsuranceCalculationTO"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="InitMortgageInsuranceBenefitsResponseCommon">
        <xs:annotation>
            <xs:documentation>Response data returned when initializing insurance benefits screen of mortgage.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="InitInsuranceBenefitsResponseCommon">
                <xs:sequence>
                    <xs:element name="interestRateWithoutInsurance" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Interest rate for uninsured mortgage.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="interestRateWithInsurance" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Interest rate for insured mortgage.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateInsuranceBenefitsRequestCommon">
        <xs:annotation>
            <xs:documentation>Update of insurance benefits page.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="insuranceOfferChecked" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether insurance offer was accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="LoanApplicationTypeTO">
        <xs:annotation>
            <xs:documentation>Type of loan application.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NORMAL"/>
            <xs:enumeration value="REFINANCE"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="MORTGAGE_REFINANCE"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="SPLIT_PAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ObligationCheckResultStatusTO">
        <xs:annotation>
            <xs:documentation>Status of obligation check result [APPROVE, REJECTED].</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ObligationTypeTO">
        <xs:annotation>
            <xs:documentation>>Obligation type [LOAN, CARD, OVERDRAFT].</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="MortgageObligationTypeTO">
        <xs:annotation>
            <xs:documentation>Mortgage obligation type [MORTGAGE_LOAN, CONSTRUCTION_SAVINGS_LOAN].</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="MortgageInterestRateTypeTO">
        <xs:annotation>
            <xs:documentation>Mortgage interest rate type [FIX, FLOAT].</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FIX"/>
            <xs:enumeration value="FLOAT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="LoanProductScoreResultTO">
        <xs:annotation>
            <xs:documentation>Result of scoring of refinance application. Possible values are [0+,0-]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="InternalCodeTO">
        <xs:annotation>
            <xs:documentation>Internal code data.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Internal code name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="value" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Internal code value</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CoDebtorDocumentsDataTO">
        <xs:annotation>
            <xs:documentation>Co-debtor data wirh required documents.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="coDebtor" type="CoDebtorDataTO">
                <xs:annotation>
                    <xs:documentation>Data of the co-debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="identificationDocuments" type="appCommon:DocumentGroupTO" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Required documents to proof client identity.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageApplicationStageTO">
        <xs:annotation>
            <xs:documentation>Mortgage application stage object.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="mortgageApplicationType" type="LoanApplicationTypeTO">
                <xs:annotation>
                    <xs:documentation>Selected mortgage application type code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mainApplicationId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Application ID of main application in evelope.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Envelope ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="stage" type="StageCodeTO">
                <xs:annotation>
                    <xs:documentation>Current application stage.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mortgageId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan ID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mortgageAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Mortgage amount..</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mortgageName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Mortgage name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mainCompletionId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Id of completion signed after application is requested.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="created" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>Date of application creation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="commitmentCompletionApproved" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Indicates, whether the completion of the commitment (úvěrový příslib) was finished (and approved).</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="StageCodeTO">
        <xs:annotation>
            <xs:documentation>Mortgage stage code enum.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPLICATION"/>
            <xs:enumeration value="OFFER_ACCEPTANCE"/>
            <xs:enumeration value="DOCUMENT_DELIVERY"/>
            <xs:enumeration value="CONTRACT_SIGNOFF"/>
            <xs:enumeration value="QUANTIFICATION_DELIVERY"/>
            <xs:enumeration value="MORTGAGE_DRAW"/>
            <xs:enumeration value="CANCELLED"/>
        </xs:restriction>
    </xs:simpleType>


    <xs:complexType name="LeadBasketTO">
        <xs:annotation>
            <xs:documentation>Carries information about product selection client made outside of our application.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanOrder" type="LoanOrderTO" minOccurs="0"/>
            <xs:element name="partnerId" type="xs:string" minOccurs="0"/>
            <xs:element name="referrer" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanOrderTO">
        <xs:annotation>
            <xs:documentation>Order for loan in lead basket</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Loan application type: NORMAL, CONSOLIDATION, MORTGAGE, MORTGAGE_REFINANCE</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="collateralAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="installment" type="xs:decimal" minOccurs="0"/>
            <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="fixationPeriod" type="xs:int" minOccurs="0"/>
            <xs:element name="currentLiabilities" type="CurrentLiabilityTO" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="interestRateType" type="MortgageInterestRateTypeTO" minOccurs="0"/>
            <xs:element name="insuranceOfferChecked" type="xs:boolean" minOccurs="0"/>
            <xs:element name="upsellAmount" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CurrentLiabilityTO">
        <xs:annotation>
            <xs:documentation>Data about current loan.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Liability type: LOAN, CARD, OVERDRAFT</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="maxAmount" type="xs:decimal" minOccurs="0"/>
            <xs:element name="payment" type="xs:decimal" minOccurs="0"/>
            <xs:element name="paymentNumber" type="xs:int" minOccurs="0"/>
            <xs:element name="fee" type="xs:decimal" minOccurs="0"/>
            <xs:element name="startDate" type="xs:date" minOccurs="0"/>
            <xs:element name="instCode" type="xs:string" minOccurs="0"/>
            <xs:element name="loanId" type="xs:long" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="TransactionIdTO">
        <xs:sequence>
            <xs:element name="accountId" type="xs:long"/>
            <xs:element name="transactionId" type="xs:long"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountTO" abstract="true">
        <xs:sequence>
            <xs:element name="accountName" type="xs:string"/>
            <xs:element name="accountType" type="AccountTypeTO"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AccountTypeTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DOMESTIC"/>
            <xs:enumeration value="FOREIGN_IBAN"/>
            <xs:enumeration value="FOREIGN_NO_IBAN"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AccountDomesticTO">
        <xs:complexContent>
            <xs:extension base="AccountTO">
                <xs:sequence>
                    <xs:element name="accountNumberPrefix" type="xs:string" minOccurs="0"/>
                    <xs:element name="accountNumber" type="xs:string"/>
                    <xs:element name="bankCode" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AccountForeignIbanTO">
        <xs:complexContent>
            <xs:extension base="AccountTO">
                <xs:sequence>
                    <xs:element name="iban" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AccountForeignNoIbanTO">
        <xs:complexContent>
            <xs:extension base="AccountTO">
                <xs:sequence>
                    <xs:element name="accountNumber" type="xs:string"/>
                    <xs:element name="bankCode" type="xs:string"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:simpleType name="IncomeTypeTO">
        <xs:annotation>
            <xs:documentation>Type of income (MAIN, OTHER).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAIN"/>
            <xs:enumeration value="OTHER"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="InsuranceOfferRejectReasonTO">
        <xs:annotation>
            <xs:documentation>Reject reason of insurance offer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAX_AGE_REACHED"/>
            <xs:enumeration value="DISABILITY"/>
            <xs:enumeration value="MATERNITY_LEAVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="InsuranceCalculationTO">
        <xs:annotation>
            <xs:documentation>Insurance calculation for loan</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="price" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Price for client for insurance.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AlternativeToCashLoanStatus">
        <xs:annotation>
            <xs:documentation>Status of cash loan with altertive.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HIGHER_ACCEPTED"/>
            <xs:enumeration value="ACCEPTED"/>
            <xs:enumeration value="LOWER_ACCEPTED"/>
            <xs:enumeration value="REJECTED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="InitChangePremiumTO">
        <xs:annotation>
            <xs:documentation>Initial premium change data.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="canChange" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Can client change premium.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalPrice" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Price for client of insurance of original loan.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="updatedPrice" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Price for client of insurance of updated loan.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="offerPrice" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Price for client of insurance for current instalment. Has the same value as updatedPrice but offerPrice is filled when
                        updatedLoanInsured=false too.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="originalLoanInsured" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Original loan is insured.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="updatedLoanInsured" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Updated loan is insure.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="agreementAccepted" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Is agreement accepted.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="meetingRecordDocumentPdfUuid" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>UUID of meeting record in PDF format.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="meetingRecordDocumentHtmlUuid" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>UUID of meeting record in HTML format.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PredefinedUpsellOfferTO">
        <xs:annotation>
            <xs:documentation>Predefined upsell offer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="offerAccepted" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Client accepted predefined upsell offer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="upsellAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Predefined upsell amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="installmentAmount" type="appCommon:MonetaryAmountTO">
                <xs:annotation>
                    <xs:documentation>Installment amount for predefined upsell amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
