<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/common/aisp"
                  targetNamespace="http://airbank.cz/ams/ws/application/common/aisp">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/common/aisp">
            <xs:include schemaLocation="AMSAispService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="InitDeliverDocumentViaAISPBeforeScoringRequest">
        <wsdl:part element="InitDeliverDocumentViaAISPBeforeScoringRequest" name="InitDeliverDocumentViaAISPBeforeScoringRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDeliverDocumentViaAISPBeforeScoringResponse">
        <wsdl:part element="InitDeliverDocumentViaAISPBeforeScoringResponse" name="InitDeliverDocumentViaAISPBeforeScoringResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDeliverDocumentViaAISPBeforeScoringFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDeliverDocumentViaAISPBeforeScoringFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDeliverDocumentViaAISPBeforeScoringRequest">
        <wsdl:part element="UpdateDeliverDocumentViaAISPBeforeScoringRequest" name="UpdateDeliverDocumentViaAISPBeforeScoringRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeliverDocumentViaAISPBeforeScoringResponse">
        <wsdl:part element="UpdateDeliverDocumentViaAISPBeforeScoringResponse" name="UpdateDeliverDocumentViaAISPBeforeScoringResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeliverDocumentViaAISPBeforeScoringFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDeliverDocumentViaAISPBeforeScoringFault"/>
    </wsdl:message>

    <wsdl:message name="InitDeliverDocumentViaAISPResultRequest">
        <wsdl:part element="InitDeliverDocumentViaAISPResultRequest" name="InitDeliverDocumentViaAISPResultRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDeliverDocumentViaAISPResultResponse">
        <wsdl:part element="InitDeliverDocumentViaAISPResultResponse" name="InitDeliverDocumentViaAISPResultResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDeliverDocumentViaAISPResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDeliverDocumentViaAISPResultFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDeliverDocumentViaAISPResultRequest">
        <wsdl:part element="UpdateDeliverDocumentViaAISPResultRequest" name="UpdateDeliverDocumentViaAISPResultRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeliverDocumentViaAISPResultResponse">
        <wsdl:part element="UpdateDeliverDocumentViaAISPResultResponse" name="UpdateDeliverDocumentViaAISPResultResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeliverDocumentViaAISPResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDeliverDocumentViaAISPResultFault"/>
    </wsdl:message>

    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="AISP">
        <wsdl:operation name="InitDeliverDocumentViaAISPBeforeScoring">
            <wsdl:input message="InitDeliverDocumentViaAISPBeforeScoringRequest"/>
            <wsdl:output message="InitDeliverDocumentViaAISPBeforeScoringResponse"/>
            <wsdl:fault name="InitDeliverDocumentViaAISPBeforeScoringFault" message="InitDeliverDocumentViaAISPBeforeScoringFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDeliverDocumentViaAISPBeforeScoring">
            <wsdl:input message="UpdateDeliverDocumentViaAISPBeforeScoringRequest"/>
            <wsdl:output message="UpdateDeliverDocumentViaAISPBeforeScoringResponse"/>
            <wsdl:fault name="UpdateDeliverDocumentViaAISPBeforeScoringFault" message="UpdateDeliverDocumentViaAISPBeforeScoringFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDeliverDocumentViaAISPResult">
            <wsdl:input message="InitDeliverDocumentViaAISPResultRequest"/>
            <wsdl:output message="InitDeliverDocumentViaAISPResultResponse"/>
            <wsdl:fault name="InitDeliverDocumentViaAISPResultFault" message="InitDeliverDocumentViaAISPResultFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDeliverDocumentViaAISPResult">
            <wsdl:input message="UpdateDeliverDocumentViaAISPResultRequest"/>
            <wsdl:output message="UpdateDeliverDocumentViaAISPResultResponse"/>
            <wsdl:fault name="UpdateDeliverDocumentViaAISPResultFault" message="UpdateDeliverDocumentViaAISPResultFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="AISPBinding" type="AISP">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="InitDeliverDocumentViaAISPBeforeScoring">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDeliverDocumentViaAISPBeforeScoringFault">
                <soap:fault name="InitDeliverDocumentViaAISPBeforeScoringFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDeliverDocumentViaAISPBeforeScoring">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDeliverDocumentViaAISPBeforeScoringFault">
                <soap:fault name="UpdateDeliverDocumentViaAISPBeforeScoringFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDeliverDocumentViaAISPResult">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDeliverDocumentViaAISPResultFault">
                <soap:fault name="InitDeliverDocumentViaAISPResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDeliverDocumentViaAISPResult">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDeliverDocumentViaAISPResultFault">
                <soap:fault name="UpdateDeliverDocumentViaAISPResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="AISPService">
        <wsdl:port name="AISPPort" binding="AISPBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/common/aisp"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>