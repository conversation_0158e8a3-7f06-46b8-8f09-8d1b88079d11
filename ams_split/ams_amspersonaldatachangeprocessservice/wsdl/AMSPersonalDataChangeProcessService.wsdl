<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/process/personalDataChange"
                  targetNamespace="http://airbank.cz/ams/ws/process/personalDataChange">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/process/personalDataChange">
            <xs:include schemaLocation="AMSPersonalDataChangeProcessService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <wsdl:portType name="PersonalDataChangeProcess">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a personal data change process for a batch of updates.

                Can generate validation faults with the following validation result codes:
                - IS_REQUIRED - attribute it null, but it should be non-null
                - INVALID_LENGHT - attribute length is outside allowed limits
                - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
                contains invalid characters)
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="PersonalDataChangeProcessBinding" type="PersonalDataChangeProcess">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="PersonalDataChangeProcessService">
        <wsdl:documentation>Service providing operations related to personal data change process for a batch of updates.</wsdl:documentation>
        <wsdl:port name="PersonalDataChangeProcessPort" binding="PersonalDataChangeProcessBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/process/personalDataChange"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>