<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/childsavingaccount"
           targetNamespace="http://airbank.cz/ams/ws/application/childsavingaccount">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element type="xs:long" name="childCuid"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>


    <xs:element name="InitAccountParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAccountParametersResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing accountParametersTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="currentAccountsOwnerFlag" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Indicates, whether the customer owns a current account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documentToExpireFlag" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Indicates, that the customer has a document, that is expired, or will expire soon.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="possibleCurrencies" type="appCommon:CurrencyCodeTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    List of 3-letter currency ISO codes,
                                    in which user can create saving
                                    account.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountParams" type="appCommon:AccountParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Account attributes</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="showMainAccount" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether to show the main account checkbox.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mobilitySelected" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a mobility has been chosen for the account being created.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="child" type="Child">
                            <xs:annotation>
                                <xs:documentation>Child info</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAccountParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="accountParams" type="appCommon:AccountParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Account attributes</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mobilitySelected" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a mobility has been chosen for the account being created.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAccountParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
    
    <xs:complexType name="Child">
        <xs:sequence>
            <xs:element name="firstname" type="xs:string"/>
            <xs:element name="lastname" type="xs:string"/>
            <xs:element name="birthdate" type="xs:date"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>