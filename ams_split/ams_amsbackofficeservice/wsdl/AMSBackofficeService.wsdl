<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/backoffice"
                  targetNamespace="http://airbank.cz/ams/ws/backoffice">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/backoffice">
            <xs:include schemaLocation="AMSBackofficeService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="GetApplicationDetailRequest">
        <wsdl:part element="GetApplicationDetailRequest" name="GetApplicationDetailRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationDetailResponse">
        <wsdl:part element="GetApplicationDetailResponse" name="GetApplicationDetailResponse"/>
    </wsdl:message>

    <wsdl:message name="FindApplicationsByFilterRequest">
        <wsdl:part element="FindApplicationsByFilterRequest" name="FindApplicationsByFilterRequest"/>
    </wsdl:message>
    <wsdl:message name="FindApplicationsByFilterResponse">
        <wsdl:part element="FindApplicationsByFilterResponse" name="FindApplicationsByFilterResponse"/>
    </wsdl:message>

    <wsdl:message name="FindApplicationsByIdsRequest">
        <wsdl:part element="FindApplicationsByIdsRequest" name="FindApplicationsByIdsRequest"/>
    </wsdl:message>
    <wsdl:message name="FindApplicationsByIdsResponse">
        <wsdl:part element="FindApplicationsByIdsResponse" name="FindApplicationsByIdsResponse"/>
    </wsdl:message>

    <wsdl:message name="GetPreviousLoanApplicationsRequest">
        <wsdl:part element="GetPreviousLoanApplicationsRequest" name="GetPreviousLoanApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPreviousLoanApplicationsResponse">
        <wsdl:part element="GetPreviousLoanApplicationsResponse" name="GetPreviousLoanApplicationsResponse"/>
    </wsdl:message>

    <wsdl:message name="FindEventsRequest">
        <wsdl:part element="FindEventsRequest" name="FindEventsRequest"/>
    </wsdl:message>
    <wsdl:message name="FindEventsResponse">
        <wsdl:part element="FindEventsResponse" name="FindEventsResponse"/>
    </wsdl:message>

    <wsdl:message name="FindApplicantsRequest">
        <wsdl:part element="FindApplicantsRequest" name="FindApplicantsRequest"/>
    </wsdl:message>
    <wsdl:message name="FindApplicantsResponse">
        <wsdl:part element="FindApplicantsResponse" name="FindApplicantsResponse"/>
    </wsdl:message>

    <wsdl:message name="GetEnvelopeRequest">
        <wsdl:part element="GetEnvelopeRequest" name="GetEnvelopeRequest"/>
    </wsdl:message>
    <wsdl:message name="GetEnvelopeResponse">
        <wsdl:part element="GetEnvelopeResponse" name="GetEnvelopeResponse"/>
    </wsdl:message>

    <wsdl:message name="GetEnvelopeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetEnvelopeFault"/>
    </wsdl:message>

    <wsdl:message name="GetEnvelopeIdsRequest">
        <wsdl:part element="GetEnvelopeIdsRequest" name="GetEnvelopeIdsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetEnvelopeIdsResponse">
        <wsdl:part element="GetEnvelopeIdsResponse" name="GetEnvelopeIdsResponse"/>
    </wsdl:message>

    <wsdl:message name="GetEnvelopePropertyRequest">
        <wsdl:part element="GetEnvelopePropertyRequest" name="GetEnvelopePropertyRequest"/>
    </wsdl:message>
    <wsdl:message name="GetEnvelopePropertyResponse">
        <wsdl:part element="GetEnvelopePropertyResponse" name="GetEnvelopePropertyResponse"/>
    </wsdl:message>
    <wsdl:message name="GetEnvelopePropertyFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetEnvelopePropertyFault"/>
    </wsdl:message>

    <wsdl:message name="GetMortgageCommitmentChangesRequest">
        <wsdl:part element="GetMortgageCommitmentChangesRequest" name="GetMortgageCommitmentChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageCommitmentChangesResponse">
        <wsdl:part element="GetMortgageCommitmentChangesResponse" name="GetMortgageCommitmentChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageCommitmentChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetMortgageCommitmentChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetMortgageChangesRequest">
        <wsdl:part element="GetMortgageChangesRequest" name="GetMortgageChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageChangesResponse">
        <wsdl:part element="GetMortgageChangesResponse" name="GetMortgageChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetMortgageChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetLoanChangesRequest">
        <wsdl:part element="GetLoanChangesRequest" name="GetLoanChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetLoanChangesResponse">
        <wsdl:part element="GetLoanChangesResponse" name="GetLoanChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetLoanChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetLoanChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationFinancialDataChangesRequest">
        <wsdl:part element="GetApplicationFinancialDataChangesRequest" name="GetApplicationFinancialDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationFinancialDataChangesResponse">
        <wsdl:part element="GetApplicationFinancialDataChangesResponse" name="GetApplicationFinancialDataChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationFinancialDataChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationFinancialDataChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetPersonalDataChangesRequest">
        <wsdl:part element="GetPersonalDataChangesRequest" name="GetPersonalDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataChangesResponse">
        <wsdl:part element="GetPersonalDataChangesResponse" name="GetPersonalDataChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetPersonalDataChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetPersonalFinancialDataChangesRequest">
        <wsdl:part element="GetPersonalFinancialDataChangesRequest" name="GetPersonalFinancialDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalFinancialDataChangesResponse">
        <wsdl:part element="GetPersonalFinancialDataChangesResponse" name="GetPersonalFinancialDataChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalFinancialDataChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetPersonalFinancialDataChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetEnvelopeHistoryRequest">
        <wsdl:part element="GetEnvelopeHistoryRequest" name="GetEnvelopeHistoryRequest"/>
    </wsdl:message>
    <wsdl:message name="GetEnvelopeHistoryResponse">
        <wsdl:part element="GetEnvelopeHistoryResponse" name="GetEnvelopeHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="GetEnvelopeHistoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetEnvelopeHistoryFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationBaseHistoryRequest">
        <wsdl:part element="GetApplicationBaseHistoryRequest" name="GetApplicationBaseHistoryRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationBaseHistoryResponse">
        <wsdl:part element="GetApplicationBaseHistoryResponse" name="GetApplicationBaseHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationBaseHistoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationBaseHistoryFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicantEmploymentDataChangesRequest">
        <wsdl:part element="GetApplicantEmploymentDataChangesRequest" name="GetApplicantEmploymentDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicantEmploymentDataChangesResponse">
        <wsdl:part element="GetApplicantEmploymentDataChangesResponse" name="GetApplicantEmploymentDataChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicantEmploymentDataChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicantEmploymentDataChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetAllEmploymentDataChangesRequest">
        <wsdl:part element="GetAllEmploymentDataChangesRequest" name="GetAllEmploymentDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetAllEmploymentDataChangesResponse">
        <wsdl:part element="GetAllEmploymentDataChangesResponse" name="GetAllEmploymentDataChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetAllEmploymentDataChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetAllEmploymentDataChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetEmploymentLastClientDataChangesRequest">
        <wsdl:part element="GetEmploymentLastClientDataChangesRequest" name="GetEmploymentLastClientDataChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetEmploymentLastClientDataChangesResponse">
        <wsdl:part element="GetEmploymentLastClientDataChangesResponse" name="GetEmploymentLastClientDataChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetEmploymentLastClientDataChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetEmploymentLastClientDataChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetAddressChangesRequest">
        <wsdl:part element="GetAddressChangesRequest" name="GetAddressChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetAddressChangesResponse">
        <wsdl:part element="GetAddressChangesResponse" name="GetAddressChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetAddressChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetAddressChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetContactChangesRequest">
        <wsdl:part element="GetContactChangesRequest" name="GetContactChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetContactChangesResponse">
        <wsdl:part element="GetContactChangesResponse" name="GetContactChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetContactChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetContactChangesFault"/>
    </wsdl:message>

    <wsdl:message name="CancelApplicationRequest">
        <wsdl:part element="CancelApplicationRequest" name="CancelApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelApplicationResponse">
        <wsdl:part element="CancelApplicationResponse" name="CancelApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="RejectApplicationRequest">
        <wsdl:part element="RejectApplicationRequest" name="RejectApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="RejectApplicationResponse">
        <wsdl:part element="RejectApplicationResponse" name="RejectApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="RejectApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="RejectApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="ChangeApplicationRequest">
        <wsdl:part element="ChangeApplicationRequest" name="ChangeApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ChangeApplicationResponse">
        <wsdl:part element="ChangeApplicationResponse" name="ChangeApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ChangeApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ChangeApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="GenerateDocSupplementRequest">
        <wsdl:part element="GenerateDocSupplementRequest" name="GenerateDocSupplementRequest"/>
    </wsdl:message>
    <wsdl:message name="GenerateDocSupplementResponse">
        <wsdl:part element="GenerateDocSupplementResponse" name="GenerateDocSupplementResponse"/>
    </wsdl:message>
    <wsdl:message name="GenerateDocSupplementFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GenerateDocSupplementFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationChecksRequest">
        <wsdl:part element="GetApplicationChecksRequest" name="GetApplicationChecksRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationChecksResponse">
        <wsdl:part element="GetApplicationChecksResponse" name="GetApplicationChecksResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationChecksFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationChecksFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationCheckHistoryRequest">
        <wsdl:part element="GetApplicationCheckHistoryRequest" name="GetApplicationCheckHistoryRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationCheckHistoryResponse">
        <wsdl:part element="GetApplicationCheckHistoryResponse" name="GetApplicationCheckHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationCheckHistoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationCheckHistoryFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicationCheckByIdRequest">
        <wsdl:part element="GetApplicationCheckByIdRequest" name="GetApplicationCheckByIdRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationCheckByIdResponse">
        <wsdl:part element="GetApplicationCheckByIdResponse" name="GetApplicationCheckByIdResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicationCheckByIdFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicationCheckByIdFault"/>
    </wsdl:message>

    <wsdl:message name="SetApplicationCheckRequest">
        <wsdl:part element="SetApplicationCheckRequest" name="SetApplicationCheckRequest"/>
    </wsdl:message>
    <wsdl:message name="SetApplicationCheckResponse">
        <wsdl:part element="SetApplicationCheckResponse" name="SetApplicationCheckResponse"/>
    </wsdl:message>
    <wsdl:message name="SetApplicationCheckFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SetApplicationCheckFault"/>
    </wsdl:message>

    <wsdl:message name="RegenerateApplicationCheckRequest">
        <wsdl:part element="RegenerateApplicationCheckRequest" name="RegenerateApplicationCheckRequest"/>
    </wsdl:message>
    <wsdl:message name="RegenerateApplicationCheckResponse">
        <wsdl:part element="RegenerateApplicationCheckResponse" name="RegenerateApplicationCheckResponse"/>
    </wsdl:message>
    <wsdl:message name="RegenerateApplicationCheckFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="RegenerateApplicationCheckFault"/>
    </wsdl:message>

    <wsdl:message name="GetApplicantRequest">
        <wsdl:part element="GetApplicantRequest" name="GetApplicantRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicantResponse">
        <wsdl:part element="GetApplicantResponse" name="GetApplicantResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicantFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicantFault"/>
    </wsdl:message>

    <wsdl:message name="StartScoringRequest">
        <wsdl:part element="StartScoringRequest" name="StartScoringRequest"/>
    </wsdl:message>
    <wsdl:message name="StartScoringResponse">
        <wsdl:part element="StartScoringResponse" name="StartScoringResponse"/>
    </wsdl:message>
    <wsdl:message name="StartScoringFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartScoringFault"/>
    </wsdl:message>

    <wsdl:message name="GetScoringProgressResultRequest">
        <wsdl:part element="GetScoringProgressResultRequest" name="GetScoringProgressResultRequest"/>
    </wsdl:message>
    <wsdl:message name="GetScoringProgressResultResponse">
        <wsdl:part element="GetScoringProgressResultResponse" name="GetScoringProgressResultResponse"/>
    </wsdl:message>
    <wsdl:message name="GetScoringProgressResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetScoringProgressResultFault"/>
    </wsdl:message>

    <wsdl:message name="IsScoringInProgressRequest">
        <wsdl:part element="IsScoringInProgressRequest" name="IsScoringInProgressRequest"/>
    </wsdl:message>
    <wsdl:message name="IsScoringInProgressResponse">
        <wsdl:part element="IsScoringInProgressResponse" name="IsScoringInProgressResponse"/>
    </wsdl:message>
    <wsdl:message name="IsScoringInProgressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="IsScoringInProgressFault"/>
    </wsdl:message>

    <wsdl:message name="GetLastScoringInProgressRequest">
        <wsdl:part element="GetLastScoringInProgressRequest" name="GetLastScoringInProgressRequest"/>
    </wsdl:message>
    <wsdl:message name="GetLastScoringInProgressResponse">
        <wsdl:part element="GetLastScoringInProgressResponse" name="GetLastScoringInProgressResponse"/>
    </wsdl:message>
    <wsdl:message name="GetLastScoringInProgressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetLastScoringInProgressFault"/>
    </wsdl:message>

    <wsdl:message name="MortgageCalculationRequest">
        <wsdl:part element="MortgageCalculationRequest" name="MortgageCalculationRequest"/>
    </wsdl:message>
    <wsdl:message name="MortgageCalculationResponse">
        <wsdl:part element="MortgageCalculationResponse" name="MortgageCalculationResponse"/>
    </wsdl:message>
    <wsdl:message name="MortgageCalculationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="MortgageCalculationFault"/>
    </wsdl:message>

    <wsdl:message name="CreatePriorityEventRequest">
        <wsdl:part element="CreatePriorityEventRequest" name="CreatePriorityEventRequest"/>
    </wsdl:message>
    <wsdl:message name="CreatePriorityEventResponse">
        <wsdl:part element="CreatePriorityEventResponse" name="CreatePriorityEventResponse"/>
    </wsdl:message>
    <wsdl:message name="CreatePriorityEventFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CreatePriorityEventFault"/>
    </wsdl:message>

    <wsdl:message name="GetPriorityEventsRequest">
        <wsdl:part element="GetPriorityEventsRequest" name="GetPriorityEventsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPriorityEventsResponse">
        <wsdl:part element="GetPriorityEventsResponse" name="GetPriorityEventsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPriorityEventsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetPriorityEventsFault"/>
    </wsdl:message>

    <wsdl:message name="DisablePriorityEventsRequest">
        <wsdl:part element="DisablePriorityEventsRequest" name="DisablePriorityEventsRequest"/>
    </wsdl:message>
    <wsdl:message name="DisablePriorityEventsResponse">
        <wsdl:part element="DisablePriorityEventsResponse" name="DisablePriorityEventsResponse"/>
    </wsdl:message>
    <wsdl:message name="DisablePriorityEventsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="DisablePriorityEventsFault"/>
    </wsdl:message>

    <wsdl:message name="ClientContactedRequest">
        <wsdl:part element="ClientContactedRequest" name="ClientContactedRequest"/>
    </wsdl:message>
    <wsdl:message name="ClientContactedResponse">
        <wsdl:part element="ClientContactedResponse" name="ClientContactedResponse"/>
    </wsdl:message>
    <wsdl:message name="ClientContactedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ClientContactedFault"/>
    </wsdl:message>

    <wsdl:message name="GetQueuePriorityResultRequest">
        <wsdl:part element="GetQueuePriorityResultRequest" name="GetQueuePriorityResultRequest"/>
    </wsdl:message>
    <wsdl:message name="GetQueuePriorityResultResponse">
        <wsdl:part element="GetQueuePriorityResultResponse" name="GetQueuePriorityResultResponse"/>
    </wsdl:message>
    <wsdl:message name="GetQueuePriorityResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetQueuePriorityResultFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateQueuePriorityListRequest">
        <wsdl:part element="UpdateQueuePriorityListRequest" name="UpdateQueuePriorityListRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateQueuePriorityListResponse">
        <wsdl:part element="UpdateQueuePriorityListResponse" name="UpdateQueuePriorityListResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateQueuePriorityListFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateQueuePriorityListFault"/>
    </wsdl:message>

    <wsdl:message name="GetOperatorsFromTeamRequest">
        <wsdl:part element="GetOperatorsFromTeamRequest" name="GetOperatorsFromTeamRequest"/>
    </wsdl:message>
    <wsdl:message name="GetOperatorsFromTeamResponse">
        <wsdl:part element="GetOperatorsFromTeamResponse" name="GetOperatorsFromTeamResponse"/>
    </wsdl:message>
    <wsdl:message name="GetOperatorsFromTeamFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetOperatorsFromTeamFault"/>
    </wsdl:message>

    <wsdl:message name="GetRolesForOperatorRequest">
        <wsdl:part element="GetRolesForOperatorRequest" name="GetRolesForOperatorRequest"/>
    </wsdl:message>
    <wsdl:message name="GetRolesForOperatorResponse">
        <wsdl:part element="GetRolesForOperatorResponse" name="GetRolesForOperatorResponse"/>
    </wsdl:message>
    <wsdl:message name="GetRolesForOperatorFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetRolesForOperatorFault"/>
    </wsdl:message>

    <wsdl:message name="GetAllowedActionsForApplicationRequest">
        <wsdl:part element="GetAllowedActionsForApplicationRequest" name="GetAllowedActionsForApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="GetAllowedActionsForApplicationResponse">
        <wsdl:part element="GetAllowedActionsForApplicationResponse" name="GetAllowedActionsForApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="GetAllowedActionsForApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetAllowedActionsForApplicationFault"/>
    </wsdl:message>

    <wsdl:message name="CalculateCashLoanParamsRequest">
        <wsdl:part element="CalculateCashLoanParamsRequest" name="CalculateCashLoanParamsRequest"/>
    </wsdl:message>
    <wsdl:message name="CalculateCashLoanParamsResponse">
        <wsdl:part element="CalculateCashLoanParamsResponse" name="CalculateCashLoanParamsResponse"/>
    </wsdl:message>
    <wsdl:message name="CalculateCashLoanParamsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CalculateCashLoanParamsFault"/>
    </wsdl:message>

    <wsdl:message name="SaveDmsDocumentRequest">
        <wsdl:part element="SaveDmsDocumentRequest" name="SaveDmsDocumentRequest"/>
    </wsdl:message>
    <wsdl:message name="SaveDmsDocumentResponse">
        <wsdl:part element="SaveDmsDocumentResponse" name="SaveDmsDocumentResponse"/>
    </wsdl:message>
    <wsdl:message name="SaveDmsDocumentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SaveDmsDocumentFault"/>
    </wsdl:message>

    <wsdl:message name="GetActualDmsDocumentRequest">
        <wsdl:part element="GetActualDmsDocumentRequest" name="GetActualDmsDocumentRequest"/>
    </wsdl:message>
    <wsdl:message name="GetActualDmsDocumentResponse">
        <wsdl:part element="GetActualDmsDocumentResponse" name="GetActualDmsDocumentResponse"/>
    </wsdl:message>
    <wsdl:message name="GetActualDmsDocumentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetActualDmsDocumentFault"/>
    </wsdl:message>

    <wsdl:message name="GetDmsDocumentWithHistoryRequest">
        <wsdl:part element="GetDmsDocumentWithHistoryRequest" name="GetDmsDocumentWithHistoryRequest"/>
    </wsdl:message>
    <wsdl:message name="GetDmsDocumentWithHistoryResponse">
        <wsdl:part element="GetDmsDocumentWithHistoryResponse" name="GetDmsDocumentWithHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="GetDmsDocumentWithHistoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetDmsDocumentWithHistoryFault"/>
    </wsdl:message>

    <wsdl:message name="GetQueueStatisticsRequest">
        <wsdl:part element="GetQueueStatisticsRequest" name="GetQueueStatisticsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetQueueStatisticsResponse">
        <wsdl:part element="GetQueueStatisticsResponse" name="GetQueueStatisticsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetQueueStatisticsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetQueueStatisticsFault"/>
    </wsdl:message>
    
    <wsdl:message name="GetMortgageDocumentationToSynchronizeRequest">
        <wsdl:part element="GetMortgageDocumentationToSynchronizeRequest" name="GetMortgageDocumentationToSynchronizeRequest"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageDocumentationToSynchronizeResponse">
        <wsdl:part element="GetMortgageDocumentationToSynchronizeResponse" name="GetMortgageDocumentationToSynchronizeResponse"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageDocumentationToSynchronizeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetMortgageDocumentationToSynchronizeFault"/>
    </wsdl:message>
    
    <wsdl:message name="SynchronizeMortgageDocumentationRequest">
        <wsdl:part element="SynchronizeMortgageDocumentationRequest" name="SynchronizeMortgageDocumentationRequest"/>
    </wsdl:message>
    <wsdl:message name="SynchronizeMortgageDocumentationResponse">
        <wsdl:part element="SynchronizeMortgageDocumentationResponse" name="SynchronizeMortgageDocumentationResponse"/>
    </wsdl:message>
    <wsdl:message name="SynchronizeMortgageDocumentationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SynchronizeMortgageDocumentationFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCustomerRequest">
        <wsdl:part element="UpdateCustomerRequest" name="UpdateCustomerRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCustomerResponse">
        <wsdl:part element="UpdateCustomerResponse" name="UpdateCustomerResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCustomerFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCustomerFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSmeCustomerCorrespondenceAddressRequest">
        <wsdl:part element="UpdateSmeCustomerCorrespondenceAddressRequest" name="UpdateSmeCustomerCorrespondenceAddressRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSmeCustomerCorrespondenceAddressResponse">
        <wsdl:part element="UpdateSmeCustomerCorrespondenceAddressResponse" name="UpdateSmeCustomerCorrespondenceAddressResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSmeCustomerCorrespondenceAddressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSmeCustomerCorrespondenceAddressFault"/>
    </wsdl:message>

    <wsdl:message name="DeleteCustomerAddressRequest">
        <wsdl:part element="DeleteCustomerAddressRequest" name="DeleteCustomerAddressRequest"/>
    </wsdl:message>
    <wsdl:message name="DeleteCustomerAddressResponse">
        <wsdl:part element="DeleteCustomerAddressResponse" name="DeleteCustomerAddressResponse"/>
    </wsdl:message>
    <wsdl:message name="DeleteCustomerAddressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="DeleteCustomerAddressFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOriginalMortgageContractDataRequest">
        <wsdl:part element="UpdateOriginalMortgageContractDataRequest" name="UpdateOriginalMortgageContractDataRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOriginalMortgageContractDataResponse">
        <wsdl:part element="UpdateOriginalMortgageContractDataResponse" name="UpdateOriginalMortgageContractDataResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOriginalMortgageContractDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOriginalMortgageContractDataFault"/>
    </wsdl:message>

    <wsdl:message name="GetDrawdownConditionsRequest">
        <wsdl:part element="GetDrawdownConditionsRequest" name="GetDrawdownConditionsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetDrawdownConditionsResponse">
        <wsdl:part element="GetDrawdownConditionsResponse" name="GetDrawdownConditionsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetDrawdownConditionsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetDrawdownConditionsFault"/>
    </wsdl:message>

    <wsdl:message name="GetDrawdownConditionHistoryRequest">
        <wsdl:part element="GetDrawdownConditionHistoryRequest" name="GetDrawdownConditionHistoryRequest"/>
    </wsdl:message>
    <wsdl:message name="GetDrawdownConditionHistoryResponse">
        <wsdl:part element="GetDrawdownConditionHistoryResponse" name="GetDrawdownConditionHistoryResponse"/>
    </wsdl:message>
    <wsdl:message name="GetDrawdownConditionHistoryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetDrawdownConditionHistoryFault"/>
    </wsdl:message>

    <wsdl:message name="CreateOrUpdateDrawdownConditionRequest">
        <wsdl:part element="CreateOrUpdateDrawdownConditionRequest" name="CreateOrUpdateDrawdownConditionRequest"/>
    </wsdl:message>
    <wsdl:message name="CreateOrUpdateDrawdownConditionResponse">
        <wsdl:part element="CreateOrUpdateDrawdownConditionResponse" name="CreateOrUpdateDrawdownConditionResponse"/>
    </wsdl:message>
    <wsdl:message name="CreateOrUpdateDrawdownConditionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CreateOrUpdateDrawdownConditionFault"/>
    </wsdl:message>

    <wsdl:message name="ChangeStateDrawdownConditionRequest">
        <wsdl:part element="ChangeStateDrawdownConditionRequest" name="ChangeStateDrawdownConditionRequest"/>
    </wsdl:message>
    <wsdl:message name="ChangeStateDrawdownConditionResponse">
        <wsdl:part element="ChangeStateDrawdownConditionResponse" name="ChangeStateDrawdownConditionResponse"/>
    </wsdl:message>
    <wsdl:message name="ChangeStateDrawdownConditionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ChangeStateDrawdownConditionFault"/>
    </wsdl:message>

    <wsdl:message name="GetOverdraftChangesRequest">
        <wsdl:part element="GetOverdraftChangesRequest" name="GetOverdraftChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetOverdraftChangesResponse">
        <wsdl:part element="GetOverdraftChangesResponse" name="GetOverdraftChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetOverdraftChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetOverdraftChangesFault"/>
    </wsdl:message>

    <wsdl:message name="GetPersonalDataChangeApplicationHistoryChangesRequest">
        <wsdl:part element="GetPersonalDataChangeApplicationHistoryChangesRequest" name="GetPersonalDataChangeApplicationHistoryChangesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataChangeApplicationHistoryChangesResponse">
        <wsdl:part element="GetPersonalDataChangeApplicationHistoryChangesResponse" name="GetPersonalDataChangeApplicationHistoryChangesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetPersonalDataChangeApplicationHistoryChangesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetPersonalDataChangeApplicationHistoryChangesFault"/>
    </wsdl:message>

    <wsdl:message name="SendManualNotificationRequest">
        <wsdl:part element="SendManualNotificationRequest" name="SendManualNotificationRequest"/>
    </wsdl:message>
    <wsdl:message name="SendManualNotificationResponse">
        <wsdl:part element="SendManualNotificationResponse" name="SendManualNotificationResponse"/>
    </wsdl:message>
    <wsdl:message name="SendManualNotificationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SendManualNotificationFault"/>
    </wsdl:message>

    <wsdl:message name="GetUserSettingsRequest">
        <wsdl:part element="GetUserSettingsRequest" name="GetUserSettingsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetUserSettingsResponse">
        <wsdl:part element="GetUserSettingsResponse" name="GetUserSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetUserSettingsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetUserSettingsFault"/>
    </wsdl:message>

    <wsdl:message name="SetUserSettingsRequest">
        <wsdl:part element="SetUserSettingsRequest" name="SetUserSettingsRequest"/>
    </wsdl:message>
    <wsdl:message name="SetUserSettingsResponse">
        <wsdl:part element="SetUserSettingsResponse" name="SetUserSettingsResponse"/>
    </wsdl:message>
    <wsdl:message name="SetUserSettingsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SetUserSettingsFault"/>
    </wsdl:message>

    <wsdl:message name="GeneratePPInsuranceSupplementsRequest">
        <wsdl:part element="GeneratePPInsuranceSupplementsRequest" name="GeneratePPInsuranceSupplementsRequest"/>
    </wsdl:message>
    <wsdl:message name="GeneratePPInsuranceSupplementsResponse">
        <wsdl:part element="GeneratePPInsuranceSupplementsResponse" name="GeneratePPInsuranceSupplementsResponse"/>
    </wsdl:message>
    <wsdl:message name="GeneratePPInsuranceSupplementsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GeneratePPInsuranceSupplementsFault"/>
    </wsdl:message>

    <wsdl:message name="FindIdentificationProcessesRequest">
        <wsdl:part element="FindIdentificationProcessesRequest" name="FindIdentificationProcessesRequest"/>
    </wsdl:message>
    <wsdl:message name="FindIdentificationProcessesResponse">
        <wsdl:part element="FindIdentificationProcessesResponse" name="FindIdentificationProcessesResponse"/>
    </wsdl:message>
    <wsdl:message name="FindIdentificationProcessesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="FindIdentificationProcessesFault"/>
    </wsdl:message>

    <wsdl:message name="GetIdentificationProcessesRequest">
        <wsdl:part element="GetIdentificationProcessesRequest" name="GetIdentificationProcessesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetIdentificationProcessesResponse">
        <wsdl:part element="GetIdentificationProcessesResponse" name="GetIdentificationProcessesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetIdentificationProcessesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetIdentificationProcessesFault"/>
    </wsdl:message>

    <wsdl:message name="ClearIdentificationProcessAlertRequest">
        <wsdl:part element="ClearIdentificationProcessAlertRequest" name="ClearIdentificationProcessAlertRequest"/>
    </wsdl:message>
    <wsdl:message name="ClearIdentificationProcessAlertResponse">
        <wsdl:part element="ClearIdentificationProcessAlertResponse" name="ClearIdentificationProcessAlertResponse"/>
    </wsdl:message>
    <wsdl:message name="ClearIdentificationProcessAlertFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ClearIdentificationProcessAlertFault"/>
    </wsdl:message>

    <wsdl:message name="GetRolesAndRightsRequest">
        <wsdl:part element="GetRolesAndRightsRequest" name="GetRolesAndRightsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetRolesAndRightsResponse">
        <wsdl:part element="GetRolesAndRightsResponse" name="GetRolesAndRightsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetRolesAndRightsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetRolesAndRightsFault"/>
    </wsdl:message>

    <wsdl:portType name="Backoffice">

        <wsdl:operation name="GetApplicationDetail">
            <wsdl:documentation>
                Returns application detail.
            </wsdl:documentation>
            <wsdl:input message="GetApplicationDetailRequest"/>
            <wsdl:output message="GetApplicationDetailResponse"/>
        </wsdl:operation>
        <wsdl:operation name="FindApplicationsByFilter">
            <wsdl:documentation>
                Returns application detail.
            </wsdl:documentation>
            <wsdl:input message="FindApplicationsByFilterRequest"/>
            <wsdl:output message="FindApplicationsByFilterResponse"/>
        </wsdl:operation>
        <wsdl:operation name="FindApplicationsByIds">
            <wsdl:documentation>
                Returns application detail.
            </wsdl:documentation>
            <wsdl:input message="FindApplicationsByIdsRequest"/>
            <wsdl:output message="FindApplicationsByIdsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetPreviousLoanApplications">
            <wsdl:documentation>
                Returns application history.
            </wsdl:documentation>
            <wsdl:input message="GetPreviousLoanApplicationsRequest"/>
            <wsdl:output message="GetPreviousLoanApplicationsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="FindEvents">
            <wsdl:documentation>
                Returns events.
            </wsdl:documentation>
            <wsdl:input message="FindEventsRequest"/>
            <wsdl:output message="FindEventsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="FindApplicants">
            <wsdl:documentation>
                Finds applicants by filter.
            </wsdl:documentation>
            <wsdl:input message="FindApplicantsRequest"/>
            <wsdl:output message="FindApplicantsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetEnvelope">
            <wsdl:documentation>
                Returns application envelope by id.

                Generated business faults:
                -Application.EnvelopeNotFound - when there was no envelope found based on the envelope ID
                -...
            </wsdl:documentation>
            <wsdl:input message="GetEnvelopeRequest"/>
            <wsdl:output message="GetEnvelopeResponse"/>
            <wsdl:fault name="GetEnvelopeFault" message="GetEnvelopeFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetEnvelopeIds">
            <wsdl:documentation>
                Returns envelope Ids by their application Ids.
            </wsdl:documentation>
            <wsdl:input message="GetEnvelopeIdsRequest"/>
            <wsdl:output message="GetEnvelopeIdsResponse"/>
        </wsdl:operation>

        <wsdl:operation name="GetEnvelopeProperty">
            <wsdl:documentation>
                Returns requested property of envelope (from BPM process).

                Generated business faults:
                -Application.EnvelopeNotFound - when there was no envelope found based on the envelope ID
                -...
            </wsdl:documentation>
            <wsdl:input message="GetEnvelopePropertyRequest"/>
            <wsdl:output message="GetEnvelopePropertyResponse"/>
            <wsdl:fault name="GetEnvelopePropertyFault" message="GetEnvelopePropertyFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetMortgageCommitmentChanges">
            <wsdl:documentation>
                Returns genesis of given mortgage commitment.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetMortgageCommitmentChangesRequest"/>
            <wsdl:output message="GetMortgageCommitmentChangesResponse"/>
            <wsdl:fault name="GetMortgageCommitmentChangesFault" message="GetMortgageCommitmentChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationFinancialDataChanges">
            <wsdl:input message="GetApplicationFinancialDataChangesRequest"/>
            <wsdl:output message="GetApplicationFinancialDataChangesResponse"/>
            <wsdl:fault name="GetApplicationFinancialDataChangesFault" message="GetApplicationFinancialDataChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalDataChanges">
            <wsdl:input message="GetPersonalDataChangesRequest"/>
            <wsdl:output message="GetPersonalDataChangesResponse"/>
            <wsdl:fault name="GetPersonalDataChangesFault" message="GetPersonalDataChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalFinancialDataChanges">
            <wsdl:input message="GetPersonalFinancialDataChangesRequest"/>
            <wsdl:output message="GetPersonalFinancialDataChangesResponse"/>
            <wsdl:fault name="GetPersonalFinancialDataChangesFault" message="GetPersonalFinancialDataChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicantEmploymentDataChanges">
            <wsdl:input message="GetApplicantEmploymentDataChangesRequest"/>
            <wsdl:output message="GetApplicantEmploymentDataChangesResponse"/>
            <wsdl:fault name="GetApplicantEmploymentDataChangesFault" message="GetApplicantEmploymentDataChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetAllEmploymentDataChanges">
            <wsdl:input message="GetAllEmploymentDataChangesRequest"/>
            <wsdl:output message="GetAllEmploymentDataChangesResponse"/>
            <wsdl:fault name="GetAllEmploymentDataChangesFault" message="GetAllEmploymentDataChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetEmploymentLastClientDataChanges">
            <wsdl:input message="GetEmploymentLastClientDataChangesRequest"/>
            <wsdl:output message="GetEmploymentLastClientDataChangesResponse"/>
            <wsdl:fault name="GetEmploymentLastClientDataChangesFault" message="GetEmploymentLastClientDataChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetAddressChanges">
            <wsdl:input message="GetAddressChangesRequest"/>
            <wsdl:output message="GetAddressChangesResponse"/>
            <wsdl:fault name="GetAddressChangesFault" message="GetAddressChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetContactChanges">
            <wsdl:input message="GetContactChangesRequest"/>
            <wsdl:output message="GetContactChangesResponse"/>
            <wsdl:fault name="GetContactChangesFault" message="GetContactChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetMortgageChanges">
            <wsdl:documentation>
                Returns genesis of given mortgage refinance application.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetMortgageChangesRequest"/>
            <wsdl:output message="GetMortgageChangesResponse"/>
            <wsdl:fault name="GetMortgageChangesFault" message="GetMortgageChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetLoanChanges">
            <wsdl:documentation>
                Returns genesis of given loan application.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetLoanChangesRequest"/>
            <wsdl:output message="GetLoanChangesResponse"/>
            <wsdl:fault name="GetLoanChangesFault" message="GetLoanChangesFault"/>
        </wsdl:operation>


        <wsdl:operation name="GetEnvelopeHistory">
            <wsdl:input message="GetEnvelopeHistoryRequest"/>
            <wsdl:output message="GetEnvelopeHistoryResponse"/>
            <wsdl:fault name="GetEnvelopeHistoryFault" message="GetEnvelopeHistoryFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationBaseHistory">
            <wsdl:input message="GetApplicationBaseHistoryRequest"/>
            <wsdl:output message="GetApplicationBaseHistoryResponse"/>
            <wsdl:fault name="GetApplicationBaseHistoryFault" message="GetApplicationBaseHistoryFault"/>
        </wsdl:operation>

        <wsdl:operation name="CancelApplication">
            <wsdl:documentation>
                Cancel an application by bank or by client request.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="CancelApplicationRequest"/>
            <wsdl:output message="CancelApplicationResponse"/>
            <wsdl:fault name="CancelApplicationFault" message="CancelApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="RejectApplication">
            <wsdl:documentation>
                Reject application.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="RejectApplicationRequest"/>
            <wsdl:output message="RejectApplicationResponse"/>
            <wsdl:fault name="RejectApplicationFault" message="RejectApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="ChangeApplication">
            <wsdl:documentation>
                Change data of application.

                Generated business faults:
                - DATA_SOURCE_CHANGED - when trying to update data from RDR to DB or vice versa
                - Common.DataRequired - mandatory field is not filled
                - Application.DuplicateValue - when trying to insert new value, that already exists (ie. required document, allowed channel)
                - Backoffice.ApplicationWithoutScoringResult - application doesn't have scoring result, so it's not possible to add ie. required document or
                allowed channel
                - Object.ConcurrentModification - version of updated object is higher than in data updating it
            </wsdl:documentation>
            <wsdl:input message="ChangeApplicationRequest"/>
            <wsdl:output message="ChangeApplicationResponse"/>
            <wsdl:fault name="ChangeApplicationFault" message="ChangeApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="GenerateDocSupplement">
            <wsdl:documentation>
                Generate supplements of contract.

                Generated business faults:
                - Application.Loan.Mortgage.SupplementGenerationNotAllowed - when the application is not in correct stage (only stages 5 and 6 are allowed)
            </wsdl:documentation>
            <wsdl:input message="GenerateDocSupplementRequest"/>
            <wsdl:output message="GenerateDocSupplementResponse"/>
            <wsdl:fault name="GenerateDocSupplementFault" message="GenerateDocSupplementFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationChecks">
            <wsdl:documentation>
                Returns checks for given application id.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetApplicationChecksRequest"/>
            <wsdl:output message="GetApplicationChecksResponse"/>
            <wsdl:fault name="GetApplicationChecksFault" message="GetApplicationChecksFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationCheckHistory">
            <wsdl:documentation>
                Returns check history for given application id and check code (and cuid).

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetApplicationCheckHistoryRequest"/>
            <wsdl:output message="GetApplicationCheckHistoryResponse"/>
            <wsdl:fault name="GetApplicationCheckHistoryFault" message="GetApplicationCheckHistoryFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationCheckById">
            <wsdl:documentation>
                Returns check by id.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetApplicationCheckByIdRequest"/>
            <wsdl:output message="GetApplicationCheckByIdResponse"/>
            <wsdl:fault name="GetApplicationCheckByIdFault" message="GetApplicationCheckByIdFault"/>
        </wsdl:operation>

        <wsdl:operation name="SetApplicationCheck">
            <wsdl:documentation>
                Sets value of application check.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="SetApplicationCheckRequest"/>
            <wsdl:output message="SetApplicationCheckResponse"/>
            <wsdl:fault name="SetApplicationCheckFault" message="SetApplicationCheckFault"/>
        </wsdl:operation>

        <wsdl:operation name="RegenerateApplicationCheck">
            <wsdl:documentation>
                Regenerate value of application check.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="RegenerateApplicationCheckRequest"/>
            <wsdl:output message="RegenerateApplicationCheckResponse"/>
            <wsdl:fault name="RegenerateApplicationCheckFault" message="RegenerateApplicationCheckFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicant">
            <wsdl:documentation>
                Finds applicant by id.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetApplicantRequest"/>
            <wsdl:output message="GetApplicantResponse"/>
            <wsdl:fault name="GetApplicantFault" message="GetApplicantFault"/>
        </wsdl:operation>

        <wsdl:operation name="StartScoring">
            <wsdl:documentation>
                Starts scoring.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="StartScoringRequest"/>
            <wsdl:output message="StartScoringResponse"/>
            <wsdl:fault name="StartScoringFault" message="StartScoringFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetScoringProgressResult">
            <wsdl:documentation>
                Returns scoring progress result by workflow ID.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetScoringProgressResultRequest"/>
            <wsdl:output message="GetScoringProgressResultResponse"/>
            <wsdl:fault name="GetScoringProgressResultFault" message="GetScoringProgressResultFault"/>
        </wsdl:operation>

        <wsdl:operation name="IsScoringInProgress">
            <wsdl:documentation>
                Check whether there is some scoring in progress for given application or not.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="IsScoringInProgressRequest"/>
            <wsdl:output message="IsScoringInProgressResponse"/>
            <wsdl:fault name="IsScoringInProgressFault" message="IsScoringInProgressFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetLastScoringInProgress">
            <wsdl:input message="GetLastScoringInProgressRequest"/>
            <wsdl:output message="GetLastScoringInProgressResponse"/>
            <wsdl:fault name="GetLastScoringInProgressFault" message="GetLastScoringInProgressFault"/>
        </wsdl:operation>

        <wsdl:operation name="MortgageCalculation">
            <wsdl:documentation>
                TODO
            </wsdl:documentation>
            <wsdl:input message="MortgageCalculationRequest"/>
            <wsdl:output message="MortgageCalculationResponse"/>
            <wsdl:fault name="MortgageCalculationFault" message="MortgageCalculationFault"/>
        </wsdl:operation>

        <wsdl:operation name="CreatePriorityEvent">
            <wsdl:documentation>
                Create priority event (urgency case).

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="CreatePriorityEventRequest"/>
            <wsdl:output message="CreatePriorityEventResponse"/>
            <wsdl:fault name="CreatePriorityEventFault" message="CreatePriorityEventFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetPriorityEvents">
            <wsdl:documentation>
                Return list of priority events.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetPriorityEventsRequest"/>
            <wsdl:output message="GetPriorityEventsResponse"/>
            <wsdl:fault name="GetPriorityEventsFault" message="GetPriorityEventsFault"/>
        </wsdl:operation>

        <wsdl:operation name="DisablePriorityEvents">
            <wsdl:documentation>
                Disable priority event. Operator can disable priority event.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="DisablePriorityEventsRequest"/>
            <wsdl:output message="DisablePriorityEventsResponse"/>
            <wsdl:fault name="DisablePriorityEventsFault" message="DisablePriorityEventsFault"/>
        </wsdl:operation>

        <wsdl:operation name="ClientContacted">
            <wsdl:documentation>
                Client was contacted by operator.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="ClientContactedRequest"/>
            <wsdl:output message="ClientContactedResponse"/>
            <wsdl:fault name="ClientContactedFault" message="ClientContactedFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetQueuePriorityResult">
            <wsdl:documentation>
                Get queue priority list. Operator can show list of queue priority.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetQueuePriorityResultRequest"/>
            <wsdl:output message="GetQueuePriorityResultResponse"/>
            <wsdl:fault name="GetQueuePriorityResultFault" message="GetQueuePriorityResultFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateQueuePriorityList">
            <wsdl:documentation>
                Update queue priority list. Operator update list of queue priority.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="UpdateQueuePriorityListRequest"/>
            <wsdl:output message="UpdateQueuePriorityListResponse"/>
            <wsdl:fault name="UpdateQueuePriorityListFault" message="UpdateQueuePriorityListFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetOperatorsFromTeam">
            <wsdl:documentation>
                Return list of operators from one team.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetOperatorsFromTeamRequest" />
            <wsdl:output message="GetOperatorsFromTeamResponse"/>
            <wsdl:fault name="GetOperatorsFromTeamFault" message="GetOperatorsFromTeamFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetRolesForOperator">
            <wsdl:documentation>
                Return list of roles by operator id.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetRolesForOperatorRequest" />
            <wsdl:output message="GetRolesForOperatorResponse"/>
            <wsdl:fault name="GetRolesForOperatorFault" message="GetRolesForOperatorFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetAllowedActionsForApplication">
            <wsdl:documentation>
                Return list of allowed actions for application in AMG

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetAllowedActionsForApplicationRequest"/>
            <wsdl:output message="GetAllowedActionsForApplicationResponse"/>
            <wsdl:fault name="GetAllowedActionsForApplicationFault" message="GetAllowedActionsForApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="CalculateCashLoanParams">
            <wsdl:documentation>
                Run OBS calculator for cash loan. Used in AMG in summarization section to toggle cash loan parameters.

                Generated business faults:
                - none.
            </wsdl:documentation>
            <wsdl:input message="CalculateCashLoanParamsRequest"/>
            <wsdl:output message="CalculateCashLoanParamsResponse"/>
            <wsdl:fault name="CalculateCashLoanParamsFault" message="CalculateCashLoanParamsFault"/>
        </wsdl:operation>

        <wsdl:operation name="SaveDmsDocument">
            <wsdl:documentation>
                save snapshot of DMS document for historization purposes
            </wsdl:documentation>
            <wsdl:input message="SaveDmsDocumentRequest"/>
            <wsdl:output message="SaveDmsDocumentResponse"/>
            <wsdl:fault name="SaveDmsDocumentFault" message="SaveDmsDocumentFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetActualDmsDocument">
            <wsdl:documentation>
                get actual snapshot of DMS document for historization purposes
            </wsdl:documentation>
            <wsdl:input message="GetActualDmsDocumentRequest"/>
            <wsdl:output message="GetActualDmsDocumentResponse"/>
            <wsdl:fault name="GetActualDmsDocumentFault" message="GetActualDmsDocumentFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetDmsDocumentWithHistory">
            <wsdl:documentation>
                get snapshot of DMS document and list of historical changes for the same document
            </wsdl:documentation>
            <wsdl:input message="GetDmsDocumentWithHistoryRequest"/>
            <wsdl:output message="GetDmsDocumentWithHistoryResponse"/>
            <wsdl:fault name="GetDmsDocumentWithHistoryFault" message="GetDmsDocumentWithHistoryFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetQueueStatistics">
            <wsdl:documentation>
                Requests statistics by queue type.

                Generated business faults:
                -...
            </wsdl:documentation>
            <wsdl:input message="GetQueueStatisticsRequest"/>
            <wsdl:output message="GetQueueStatisticsResponse"/>
            <wsdl:fault name="GetQueueStatisticsFault" message="GetQueueStatisticsFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="GetMortgageDocumentationToSynchronize">
            <wsdl:documentation>
                get mortgage documentation to be synchronized later by update operation
            </wsdl:documentation>
            <wsdl:input message="GetMortgageDocumentationToSynchronizeRequest"/>
            <wsdl:output message="GetMortgageDocumentationToSynchronizeResponse"/>
            <wsdl:fault name="GetMortgageDocumentationToSynchronizeFault" message="GetMortgageDocumentationToSynchronizeFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="SynchronizeMortgageDocumentation">
            <wsdl:documentation>
                synchronize mortgage documentation between AMS/OBS/Elbos
            </wsdl:documentation>
            <wsdl:input message="SynchronizeMortgageDocumentationRequest"/>
            <wsdl:output message="SynchronizeMortgageDocumentationResponse"/>
            <wsdl:fault name="SynchronizeMortgageDocumentationFault" message="SynchronizeMortgageDocumentationFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateCustomer">
            <wsdl:documentation>
                Update customer in CIF and if application id is provided update also this application.

                Generated business faults:
                - validation errors on fields:
                    - client.applicationType (APPLICATION_TYPE_NOT_SUPPORTED)
                    - cuid (Application.ApplicantCifUpdateError), client.cuid (CUID_DOESNT_MATCH)
                    - data.firstName
                    - data.lastName
                    - data.birthNumber, clientOtherData.personalId
                    - data.email
                    - data.phone
                    - debitCardParams.embossedName
                    - data.maritalStatus
                    - data.permanentAddress
                    - data.mailingAddress
                    - data.foreignPermanentAddress
            </wsdl:documentation>
            <wsdl:input message="UpdateCustomerRequest"/>
            <wsdl:output message="UpdateCustomerResponse"/>
            <wsdl:fault name="UpdateCustomerFault" message="UpdateCustomerFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSmeCustomerCorrespondenceAddress">
            <wsdl:input message="UpdateSmeCustomerCorrespondenceAddressRequest"/>
            <wsdl:output message="UpdateSmeCustomerCorrespondenceAddressResponse"/>
            <wsdl:fault name="UpdateSmeCustomerCorrespondenceAddressFault" message="UpdateSmeCustomerCorrespondenceAddressFault"/>
        </wsdl:operation>

        <wsdl:operation name="DeleteCustomerAddress">
            <wsdl:documentation>
                Deletes customers address in CIF and if application id is provided updates also data in AMS application.

                Generated business faults:
                - validation errors on fields:
                - cuid (Application.ApplicantCifUpdateError), client.cuid (CUID_DOESNT_MATCH)
            </wsdl:documentation>
            <wsdl:input message="DeleteCustomerAddressRequest"/>
            <wsdl:output message="DeleteCustomerAddressResponse"/>
            <wsdl:fault name="DeleteCustomerAddressFault" message="DeleteCustomerAddressFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateOriginalMortgageContractData">
            <wsdl:documentation>
                Updates original contract data for mortgage application

                Generated faults:
                - Application.NotFound
                - Application.Loan.Mortgage.CommitmentNotFound
                - Application.Loan.Mortgage.MultipleActiveCommitments
            </wsdl:documentation>
            <wsdl:input message="UpdateOriginalMortgageContractDataRequest"/>
            <wsdl:output message="UpdateOriginalMortgageContractDataResponse"/>
            <wsdl:fault name="UpdateOriginalMortgageContractDataFault" message="UpdateOriginalMortgageContractDataFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetDrawdownConditions">
            <wsdl:documentation>
                Return list of drawdown conditions

                Generated faults:
                - Application.NotFound
            </wsdl:documentation>
            <wsdl:input message="GetDrawdownConditionsRequest"/>
            <wsdl:output message="GetDrawdownConditionsResponse"/>
            <wsdl:fault name="GetDrawdownConditionsFault" message="GetDrawdownConditionsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetDrawdownConditionHistory">
            <wsdl:documentation>
                Return list of drawdown condition history

                Generated faults:
                - DrawdownCondition.NotFound
            </wsdl:documentation>
            <wsdl:input message="GetDrawdownConditionHistoryRequest"/>
            <wsdl:output message="GetDrawdownConditionHistoryResponse"/>
            <wsdl:fault name="GetDrawdownConditionHistoryFault" message="GetDrawdownConditionHistoryFault"/>
        </wsdl:operation>

        <wsdl:operation name="CreateOrUpdateDrawdownCondition">
            <wsdl:documentation>
                Create or update drawdown condition for application

                Generated faults:
                - DrawdownCondition.NotFound
            </wsdl:documentation>
            <wsdl:input message="CreateOrUpdateDrawdownConditionRequest"/>
            <wsdl:output message="CreateOrUpdateDrawdownConditionResponse"/>
            <wsdl:fault name="CreateOrUpdateDrawdownConditionFault" message="CreateOrUpdateDrawdownConditionFault"/>
        </wsdl:operation>

        <wsdl:operation name="ChangeStateDrawdownCondition">
            <wsdl:documentation>
                Change state of drawdown condition

                Generated faults:
                - DrawdownCondition.NotFound
            </wsdl:documentation>
            <wsdl:input message="ChangeStateDrawdownConditionRequest"/>
            <wsdl:output message="ChangeStateDrawdownConditionResponse"/>
            <wsdl:fault name="ChangeStateDrawdownConditionFault" message="ChangeStateDrawdownConditionFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetOverdraftChanges">
            <wsdl:documentation>
                Retruns overdraft changes
            </wsdl:documentation>
            <wsdl:input message="GetOverdraftChangesRequest"/>
            <wsdl:output message="GetOverdraftChangesResponse"/>
            <wsdl:fault name="GetOverdraftChangesFault" message="GetOverdraftChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalDataChangeApplicationHistoryChanges">
            <wsdl:documentation>
                Return personal's data change history, for current state and when something changed then changed values
            </wsdl:documentation>
            <wsdl:input message="GetPersonalDataChangeApplicationHistoryChangesRequest"/>
            <wsdl:output message="GetPersonalDataChangeApplicationHistoryChangesResponse"/>
            <wsdl:fault name="GetPersonalDataChangeApplicationHistoryChangesFault" message="GetPersonalDataChangeApplicationHistoryChangesFault"/>
        </wsdl:operation>

        <wsdl:operation name="SendManualNotification">
            <wsdl:documentation>
                Return user's settings of filters, dashboards, etc.
            </wsdl:documentation>
            <wsdl:input message="SendManualNotificationRequest"/>
            <wsdl:output message="SendManualNotificationResponse"/>
            <wsdl:fault name="SendManualNotificationFault" message="SendManualNotificationFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetUserSettings">
            <wsdl:documentation>
                Return user's settings of filters, dashboards, etc.
            </wsdl:documentation>
            <wsdl:input message="GetUserSettingsRequest"/>
            <wsdl:output message="GetUserSettingsResponse"/>
            <wsdl:fault name="GetUserSettingsFault" message="GetUserSettingsFault"/>
        </wsdl:operation>

        <wsdl:operation name="SetUserSettings">
            <wsdl:documentation>
                Set user's settings of filters, dashboards, etc.
            </wsdl:documentation>
            <wsdl:input message="SetUserSettingsRequest"/>
            <wsdl:output message="SetUserSettingsResponse"/>
            <wsdl:fault name="SetUserSettingsFault" message="SetUserSettingsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GeneratePPInsuranceSupplements">
            <wsdl:documentation>
                Generates PPI supplements
            </wsdl:documentation>
            <wsdl:input message="GeneratePPInsuranceSupplementsRequest"/>
            <wsdl:output message="GeneratePPInsuranceSupplementsResponse"/>
            <wsdl:fault name="GeneratePPInsuranceSupplementsFault" message="GeneratePPInsuranceSupplementsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetRolesAndRights">
            <wsdl:documentation>
                Return all roles with rights definition
            </wsdl:documentation>
            <wsdl:input message="GetRolesAndRightsRequest"/>
            <wsdl:output message="GetRolesAndRightsResponse"/>
            <wsdl:fault name="GetRolesAndRightsFault" message="GetRolesAndRightsFault"/>
        </wsdl:operation>

        <wsdl:operation name="FindIdentificationProcesses">
            <wsdl:documentation>
                Finds identification processes by filter.
            </wsdl:documentation>
            <wsdl:input message="FindIdentificationProcessesRequest"/>
            <wsdl:output message="FindIdentificationProcessesResponse"/>
            <wsdl:fault name="FindIdentificationProcessesFault" message="FindIdentificationProcessesFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetIdentificationProcesses">
            <wsdl:documentation>
                Returns identification processes by ids.
            </wsdl:documentation>
            <wsdl:input message="GetIdentificationProcessesRequest"/>
            <wsdl:output message="GetIdentificationProcessesResponse"/>
            <wsdl:fault name="GetIdentificationProcessesFault" message="GetIdentificationProcessesFault"/>
        </wsdl:operation>

        <wsdl:operation name="ClearIdentificationProcessAlert">
            <wsdl:documentation>
                Clears alert on identification process.
            </wsdl:documentation>
            <wsdl:input message="ClearIdentificationProcessAlertRequest"/>
            <wsdl:output message="ClearIdentificationProcessAlertResponse"/>
            <wsdl:fault name="ClearIdentificationProcessAlertFault" message="ClearIdentificationProcessAlertFault"/>
        </wsdl:operation>

    </wsdl:portType>


    <wsdl:binding name="BackofficeBinding" type="Backoffice">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="GetApplicationDetail">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindApplicationsByFilter">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindApplicationsByIds">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetPreviousLoanApplications">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindEvents">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="FindApplicants">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetEnvelope">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetEnvelopeFault">
                <soap:fault name="GetEnvelopeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetEnvelopeIds">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="GetEnvelopeProperty">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetEnvelopePropertyFault">
                <soap:fault name="GetEnvelopePropertyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetMortgageCommitmentChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetMortgageCommitmentChangesFault">
                <soap:fault name="GetMortgageCommitmentChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationFinancialDataChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationFinancialDataChangesFault">
                <soap:fault name="GetApplicationFinancialDataChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalDataChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPersonalDataChangesFault">
                <soap:fault name="GetPersonalDataChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalFinancialDataChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPersonalFinancialDataChangesFault">
                <soap:fault name="GetPersonalFinancialDataChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicantEmploymentDataChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicantEmploymentDataChangesFault">
                <soap:fault name="GetApplicantEmploymentDataChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetAllEmploymentDataChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetAllEmploymentDataChangesFault">
                <soap:fault name="GetAllEmploymentDataChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetEmploymentLastClientDataChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetEmploymentLastClientDataChangesFault">
                <soap:fault name="GetEmploymentLastClientDataChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetAddressChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetAddressChangesFault">
                <soap:fault name="GetAddressChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetContactChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetContactChangesFault">
                <soap:fault name="GetContactChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetMortgageChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetMortgageChangesFault">
                <soap:fault name="GetMortgageChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetLoanChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetLoanChangesFault">
                <soap:fault name="GetLoanChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetEnvelopeHistory">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetEnvelopeHistoryFault">
                <soap:fault name="GetEnvelopeHistoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationBaseHistory">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationBaseHistoryFault">
                <soap:fault name="GetApplicationBaseHistoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CancelApplication">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelApplicationFault">
                <soap:fault name="CancelApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="RejectApplication">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RejectApplicationFault">
                <soap:fault name="RejectApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ChangeApplication">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ChangeApplicationFault">
                <soap:fault name="ChangeApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GenerateDocSupplement">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GenerateDocSupplementFault">
                <soap:fault name="GenerateDocSupplementFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationChecks">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationChecksFault">
                <soap:fault name="GetApplicationChecksFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationCheckHistory">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationCheckHistoryFault">
                <soap:fault name="GetApplicationCheckHistoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicationCheckById">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicationCheckByIdFault">
                <soap:fault name="GetApplicationCheckByIdFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="SetApplicationCheck">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetApplicationCheckFault">
                <soap:fault name="SetApplicationCheckFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="RegenerateApplicationCheck">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="RegenerateApplicationCheckFault">
                <soap:fault name="RegenerateApplicationCheckFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicant">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicantFault">
                <soap:fault name="GetApplicantFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="StartScoring">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartScoringFault">
                <soap:fault name="StartScoringFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetScoringProgressResult">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetScoringProgressResultFault">
                <soap:fault name="GetScoringProgressResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="IsScoringInProgress">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="IsScoringInProgressFault">
                <soap:fault name="IsScoringInProgressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetLastScoringInProgress">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetLastScoringInProgressFault">
                <soap:fault name="GetLastScoringInProgressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="MortgageCalculation">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="MortgageCalculationFault">
                <soap:fault name="MortgageCalculationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CreatePriorityEvent">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CreatePriorityEventFault">
                <soap:fault name="CreatePriorityEventFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetPriorityEvents">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPriorityEventsFault">
                <soap:fault name="GetPriorityEventsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="DisablePriorityEvents">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="DisablePriorityEventsFault">
                <soap:fault name="DisablePriorityEventsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ClientContacted">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ClientContactedFault">
                <soap:fault name="ClientContactedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetQueuePriorityResult">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetQueuePriorityResultFault">
                <soap:fault name="GetQueuePriorityResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateQueuePriorityList">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateQueuePriorityListFault">
                <soap:fault name="UpdateQueuePriorityListFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetOperatorsFromTeam">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetOperatorsFromTeamFault">
                <soap:fault name="GetOperatorsFromTeamFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetRolesForOperator">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetRolesForOperatorFault">
                <soap:fault name="GetRolesForOperatorFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetAllowedActionsForApplication">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetAllowedActionsForApplicationFault">
                <soap:fault name="GetAllowedActionsForApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CalculateCashLoanParams">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CalculateCashLoanParamsFault">
                <soap:fault name="CalculateCashLoanParamsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="SaveDmsDocument">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SaveDmsDocumentFault">
                <soap:fault name="SaveDmsDocumentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetActualDmsDocument">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetActualDmsDocumentFault">
                <soap:fault name="GetActualDmsDocumentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetDmsDocumentWithHistory">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetDmsDocumentWithHistoryFault">
                <soap:fault name="GetDmsDocumentWithHistoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetQueueStatistics">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetQueueStatisticsFault">
                <soap:fault name="GetQueueStatisticsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="GetMortgageDocumentationToSynchronize">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetMortgageDocumentationToSynchronizeFault">
                <soap:fault name="GetMortgageDocumentationToSynchronizeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="SynchronizeMortgageDocumentation">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SynchronizeMortgageDocumentationFault">
                <soap:fault name="SynchronizeMortgageDocumentationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCustomer">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCustomerFault">
                <soap:fault name="UpdateCustomerFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSmeCustomerCorrespondenceAddress">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSmeCustomerCorrespondenceAddressFault">
                <soap:fault name="UpdateSmeCustomerCorrespondenceAddressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="DeleteCustomerAddress">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="DeleteCustomerAddressFault">
                <soap:fault name="DeleteCustomerAddressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOriginalMortgageContractData">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOriginalMortgageContractDataFault">
                <soap:fault name="UpdateOriginalMortgageContractDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetDrawdownConditions">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetDrawdownConditionsFault">
                <soap:fault name="GetDrawdownConditionsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetDrawdownConditionHistory">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetDrawdownConditionHistoryFault">
                <soap:fault name="GetDrawdownConditionHistoryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CreateOrUpdateDrawdownCondition">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CreateOrUpdateDrawdownConditionFault">
                <soap:fault name="CreateOrUpdateDrawdownConditionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ChangeStateDrawdownCondition">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ChangeStateDrawdownConditionFault">
                <soap:fault name="ChangeStateDrawdownConditionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetOverdraftChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetOverdraftChangesFault">
                <soap:fault name="GetOverdraftChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetPersonalDataChangeApplicationHistoryChanges">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetPersonalDataChangeApplicationHistoryChangesFault">
                <soap:fault name="GetPersonalDataChangeApplicationHistoryChangesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="SendManualNotification">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SendManualNotificationFault">
                <soap:fault name="SendManualNotificationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetUserSettings">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetUserSettingsFault">
                <soap:fault name="GetUserSettingsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="SetUserSettings">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SetUserSettingsFault">
                <soap:fault name="SetUserSettingsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GeneratePPInsuranceSupplements">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GeneratePPInsuranceSupplementsFault">
                <soap:fault name="GeneratePPInsuranceSupplementsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetRolesAndRights">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetRolesAndRightsFault">
                <soap:fault name="GetRolesAndRightsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="FindIdentificationProcesses">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="FindIdentificationProcessesFault">
                <soap:fault name="FindIdentificationProcessesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetIdentificationProcesses">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetIdentificationProcessesFault">
                <soap:fault name="GetIdentificationProcessesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ClearIdentificationProcessAlert">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ClearIdentificationProcessAlertFault">
                <soap:fault name="ClearIdentificationProcessAlertFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="BackofficeService">
        <wsdl:documentation>
            Offers services for AMS GUI.
        </wsdl:documentation>
        <wsdl:port name="BackofficePort" binding="BackofficeBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ws/ams/backoffice"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
