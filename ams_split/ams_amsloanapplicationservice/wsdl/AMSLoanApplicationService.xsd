<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/loan"
           targetNamespace="http://airbank.cz/ams/ws/application/loan">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="loanOrder" type="loanCommon:LoanOrderTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Optional basket content</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="requiredADValidationVersion" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Required application data validation version.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Branch officer internal code.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="affiliateParams" type="appCommon:AffiliateParamsTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Affiliate parameters.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanParametersRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for loan application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanParametersResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing loanParametersTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitLoanParametersResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="previousLoanApplicationFound" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Indicates that previous loan application has been found that fulfills internal search criteria.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="loanReuseData" type="loanCommon:InitLoanDataReuseResponseCommon" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Data about available loan application suitable for application data reuse.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="insuranceOffer" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Indicate if insurance can be offered.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanParametersRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for loan application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateLoanParametersRequestCommon" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanParametersResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating loanParametersTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateLoanParametersResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitConsolidationDashboardForWalkinRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for consolidation application for walkin.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitConsolidationDashboardForWalkinResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing consolidationDashboardForWalkinTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitConsolidationDashboardForWalkinResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateConsolidationDashboardForWalkinRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for consolidation application for walkin.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateConsolidationDashboardForWalkinRequestCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateConsolidationDashboardForWalkinResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating consolidationDashboardForWalkinTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence/>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanCalculationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for loan application on mobile.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanCalculationResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing loanCalculationTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitLoanCalculationResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanCalculationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for loan application for mobile.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateLoanCalculationRequestCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanCalculationResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating loanCalculationTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateLoanCalculationResponseCommon" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNoConsolidableObligationFoundRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of noConsolidableObligationFoundTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNoConsolidableObligationFoundResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing noConsolidableObligationFoundTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNoConsolidableObligationFoundRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of noConsolidableObligationFoundTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNoConsolidableObligationFoundResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating noConsolidableObligationFoundTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitWaitForSegmentationOfferRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of page with info about segmentation offer.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitWaitForSegmentationOfferResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing InitWaitForSegmentationOfferTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateWaitForSegmentationOfferRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateWaitForSegmentationOfferResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAlternativeToCashLoanRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAlternativeToCashLoanResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitAlternativeToCashLoanResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAlternativeToCashLoanRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of page with loan accepted info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAlternativeToCashLoanResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:BaseResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAlternativeToConsolidationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAlternativeToConsolidationResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitAlternativeToConsolidationResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAlternativeToConsolidationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAlternativeToConsolidationResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data of page with loan alternative info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:BaseResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitScoringResultAndLoanSettingsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitScoringResultAndLoanSettingsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitScoringResultAndLoanSettingsResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateScoringResultAndLoanSettingsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateScoringResultAndLoanSettingsRequestCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateScoringResultAndLoanSettingsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateScoringResultAndLoanSettingsResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update response data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitConsolidationAcceptedAndSettingsRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of page consolidationAcceptedAndSettingsTask for loan application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitConsolidationAcceptedAndSettingsResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing consolidationAcceptedAndSettingsTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitConsolidationAcceptedAndSettingsResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data for this step.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateConsolidationAcceptedAndSettingsRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of page with repayment way and loan settings for loan application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateConsolidationAcceptedAndSettingsRequestCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update data for this step.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateConsolidationAcceptedAndSettingsResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating consolidationAcceptedAndSettingsTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:UpdateConsolidationAcceptedAndSettingsResponseCommon" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Update result data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                    <xs:sequence>
                        <xs:element name="incomeCounter" type="appCommon:IncomeCounterTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Current income counter value in the process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of the envelope containing application.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractResumeApplicationResponse">
                    <xs:sequence>
                        <xs:element name="loanApplicationType" type="loanCommon:LoanApplicationTypeTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Selected loan application type.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="SetSegmentationOfferDecisionRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="applicationId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of application</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="accepted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>True if client accepted segmentation offer</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Reason for rejected segmentation offer</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SetSegmentationOfferDecisionResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllBonusVariantsRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of box with loan bonus variants.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitRequest">
                    <xs:sequence>
                        <xs:element name="envelopeId" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of the application envelope.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="finalizationId" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of finalization.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllBonusVariantsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="bonusVariants" type="loanCommon:LoanVariantTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Normal + all bonus variants.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanCancelledRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of page with loan cancellation info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanCancelledResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data for initialization of page with loan cancellation info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitLoanCancelledResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanCancelledRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of page with loan cancellation info.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanCancelledResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data for loan cancellation update.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>