<?xml version="1.0" encoding="UTF-8"?>
<xs:schema elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://airbank.cz/ams/ws/identification/common"
           targetNamespace="http://airbank.cz/ams/ws/identification/common">

    <xs:complexType name="CustomerIdentificationData">
        <xs:annotation>
            <xs:documentation>Entry data for customer identification process.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Customer's first name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="surname" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Customer's surname.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:documentation>ID of customer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthDate" type="xs:date">
                <xs:annotation>
                    <xs:documentation>Customer's birth date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="birthNumber" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Customer's birth number.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>