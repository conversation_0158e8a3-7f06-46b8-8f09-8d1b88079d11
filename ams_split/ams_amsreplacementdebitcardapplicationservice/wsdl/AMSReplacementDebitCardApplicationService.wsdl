<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault" xmlns="http://airbank.cz/ams/ws/application/replacementdebitcard"
                  targetNamespace="http://airbank.cz/ams/ws/application/replacementdebitcard">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/replacementdebitcard">
            <xs:include schemaLocation="AMSReplacementDebitCardApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- ReplacementDebitCardTask task -->
    <wsdl:message name="InitReplacementDebitCardRequest">
        <wsdl:part element="InitReplacementDebitCardRequest" name="InitReplacementDebitCardRequest"/>
    </wsdl:message>
    <wsdl:message name="InitReplacementDebitCardResponse">
        <wsdl:part element="InitReplacementDebitCardResponse" name="InitReplacementDebitCardResponse"/>
    </wsdl:message>
    <wsdl:message name="InitReplacementDebitCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitReplacementDebitCardFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateReplacementDebitCardRequest">
        <wsdl:part element="UpdateReplacementDebitCardRequest" name="UpdateReplacementDebitCardRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateReplacementDebitCardResponse">
        <wsdl:part element="UpdateReplacementDebitCardResponse" name="UpdateReplacementDebitCardResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateReplacementDebitCardFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateReplacementDebitCardFault"/>
    </wsdl:message>

    <!-- CardParameters task -->
    <wsdl:message name="InitCardParametersRequest">
        <wsdl:part element="InitCardParametersRequest" name="InitCardParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitCardParametersResponse">
        <wsdl:part element="InitCardParametersResponse" name="InitCardParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitCardParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitCardParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateCardParametersRequest">
        <wsdl:part element="UpdateCardParametersRequest" name="UpdateCardParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateCardParametersResponse">
        <wsdl:part element="UpdateCardParametersResponse" name="UpdateCardParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateCardParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateCardParametersFault"/>
    </wsdl:message>

    <!-- Application cancel request -->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="ReplacementDebitCardApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>
                Starts a new application for replacement debit card of an existing customer.
                Uses cuid and idProfile from tracking context to identify customer and profile.

                Generated business faults:
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous debit card application in the VERIFY status
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitReplacementDebitCard">
            <wsdl:documentation>Operation used to initialize the replacementDebitCardTask.</wsdl:documentation>
            <wsdl:input message="InitReplacementDebitCardRequest"/>
            <wsdl:output message="InitReplacementDebitCardResponse"/>
            <wsdl:fault name="InitReplacementDebitCardFault" message="InitReplacementDebitCardFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateReplacementDebitCard">
            <wsdl:documentation>
                Operation used to update data for the replacementDebitCardTask.

                For all transitions except for back, following attributes of cardParams are required:
                - selectedAccount
                - selectedCard
            </wsdl:documentation>
            <wsdl:input message="UpdateReplacementDebitCardRequest"/>
            <wsdl:output message="UpdateReplacementDebitCardResponse"/>
            <wsdl:fault name="UpdateReplacementDebitCardFault" message="UpdateReplacementDebitCardFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitCardParameters">
            <wsdl:documentation>Operation used to initialize the cardParametersTask.</wsdl:documentation>
            <wsdl:input message="InitCardParametersRequest"/>
            <wsdl:output message="InitCardParametersResponse"/>
            <wsdl:fault name="InitCardParametersFault" message="InitCardParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateCardParameters">
            <wsdl:documentation>
                Operation used to update data for the cardParametersTask.

                For all transitions except for back, following attributes of cardParams are required:
                - addressSelected
                - showPin
                - sendPin
                - selectedCardDesignType
                - embossedName
                - embossedNameChanged

                Generated business faults:
                - Application.Account.NoWayHowToSendPin - User has not selected way how he wants to to get pin

                Generated validation faults:
                - Application.AddressNotInCz - Specified address is not in CZ
                - Application.AddressNotConfirmed - The other address does not conform to registers and it is not confirmed by the user.
            </wsdl:documentation>
            <wsdl:input message="UpdateCardParametersRequest"/>
            <wsdl:output message="UpdateCardParametersResponse"/>
            <wsdl:fault name="UpdateCardParametersFault" message="UpdateCardParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ReplacementDebitCardApplicationBinding" type="ReplacementDebitCardApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitReplacementDebitCard">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitReplacementDebitCardFault">
                <soap:fault name="InitReplacementDebitCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateReplacementDebitCard">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateReplacementDebitCardFault">
                <soap:fault name="UpdateReplacementDebitCardFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitCardParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitCardParametersFault">
                <soap:fault name="InitCardParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateCardParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateCardParametersFault">
                <soap:fault name="UpdateCardParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="ReplacementDebitCardApplicationService">
        <wsdl:documentation>Service providing operations related to replacement debit card application of an existing customer.

            The application has the following task IDs:
            - replacementDebitCardTask - corresponds to the initial screen, where user specifies account attributes
            - cardParametersTask - corresponds to the screen where user specifies debet card attributes
            - generateDocumentationTask - application was approved, documentation could be generated

            - waitForWarningResultTask - AMS is waiting for the warning processing

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently
            modified by a different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that
            the value contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="ReplacementDebitCardApplicationPort" binding="ReplacementDebitCardApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/replacementdebitcard"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>