<xs:schema elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://airbank.cz/ams/ws/application/constant"
           targetNamespace="http://airbank.cz/ams/ws/application/constant">

    <xs:simpleType name="IdentityVerificationMethodTO">
        <xs:annotation>
            <xs:documentation>Identity verification method.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="IDENTIFICATION_DOCUMENT">
                <xs:annotation>
                    <xs:documentation>Identification document</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BANK_ID">
                <xs:annotation>
                    <xs:documentation>Bank ID</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ClientDataCollectionResult">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OK"/>
            <xs:enumeration value="ERROR"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="CardBusinessCategoryTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="STANDARD"/>
            <xs:enumeration value="TRAVEL"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
