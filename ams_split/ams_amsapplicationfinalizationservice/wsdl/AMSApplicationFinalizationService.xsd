<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           xmlns:aisp="http://airbank.cz/ams/ws/application/common/aisp"
           xmlns:overdraftCommon="http://airbank.cz/ams/ws/application/common/overdraft"
           xmlns:bc="http://airbank.cz/ams/ws/backoffice/common"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           elementFormDefault="qualified"
           jxb:version="2.1"
           xmlns="http://airbank.cz/ams/ws/application/finalization"
           targetNamespace="http://airbank.cz/ams/ws/application/finalization">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/overdraft" schemaLocation="../xsd/OverdraftApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/aisp" schemaLocation="AMSAispService.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/backoffice/common" schemaLocation="../xsd/BackofficeApplication.xsd"/>


    <xs:complexType name="AbstractInitFinalizationRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic init request parameters for the application finalization.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractEmptyInitRequest">
                <xs:sequence>
                    <xs:element name="finalizationId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Id of the application finalization process.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractUpdateSignRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic update request parameters for the application finalization.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractUpdateFinalizationRequest">
                <xs:sequence>
                    <xs:element name="deliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Selected delivery channel (filled in only when using a different channel)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="authId" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Authorization id that enables access to a document</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="contractType" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Contract completion type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractUpdateFinalizationRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic update request parameters for the application finalization.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractEmptyUpdateRequest">
                <xs:sequence>
                    <xs:element name="finalizationId" type="xs:long">
                        <xs:annotation>
                            <xs:documentation>Id of the application finalization process.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractFinalizationUpdateResponse" abstract="true">
        <xs:annotation/>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                <xs:sequence>
                    <xs:element name="parentEnvelopeId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Cascading Application Envelope ID.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="parentApplicationType" type="appCommon:ApplicationTypeTO" minOccurs="0">
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractInitSignResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic response for all signature inits</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="appCommon:AbstractEmptyInitResponse">
                <xs:sequence>
                    <xs:element name="signatureType" type="appCommon:SignatureTypeTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Possible signature types or null when signatureTypeError is set.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="signatureTypeError" type="xs:string" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>
                                Signature types are received using OBS method GetAuthTypes, which can return following errors:
                                CLERR_OWNER_SIGN_NEEDED, CLERR_AFF_TYPE_USER, CLERR_GENERAL_CONTRACT_NOT_COMPLETED.
                                In that case, signatureType is empty and error is filled here.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="deliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Possible delivery channel</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedDeliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Delivery channel, that was already selected as part of some previous application finalization run. If it is null,
                                then there was no previous delivery channel selected.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="completionId" type="xs:long" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Contract completion ID (from OBS)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="showMobilityWarning" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Indicates, whether the mobility warning should be shown (application contains mobility and user is not at branch)
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountHasInsuranceWarning" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Indicates that warning about insurance termination warning when closing account should be shown.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="insufficientDebitTravelInsuranceWarning" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Indicates that client does not have enough money in his account for Travel insurance. Warning should be displayed.

                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="internalCodes" type="loanCommon:InternalCodeTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Possible internal codes</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                        <xs:annotation>
                            <xs:documentation>Code of process.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="showRefinanceNoticeLetterWarning" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>Indicates, whether specific warning before signing of first refinance notice letter should be shown (can be true only for consolidation application).
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:choice>
                            <xs:sequence>
                                <xs:element name="applicationId" type="xs:long">
                                    <xs:annotation>
                                        <xs:documentation>Id of the application in AMS</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="completionId" type="xs:long" minOccurs="0">
                                    <xs:annotation>
                                        <xs:documentation>Id of the contract completion (in OBS) to finalize</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                            <xs:element name="envelopeId" type="xs:long">
                                <xs:annotation>
                                    <xs:documentation>Id of the application envelope (its main application will be finalized)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:choice>
                        <xs:element name="startDocumentsWithAISP" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>If true, indicates that the client (MA/IB) supports starting document upload with AISP screen first.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyStartResponse">
                    <xs:sequence>
                        <xs:element name="finalizationId" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Id of the finalization process. It must be used in all subsequent calls.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="applicationTypes" type="appCommon:ApplicationTypeTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Application types of apps from the envelope related to finalizationId</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="disponentOrCardHolderBirthDate" type="xs:date" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Disponent/Card holder date of birth.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="disponentOrCardHolderFirstName" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Disponent/Card holder first name.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="disponentOrCardHolderLastName" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Disponent/Card holder last name.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="disponentOrCardHolderCuid" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Disponent/Card holder cuid.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="leadProductContext" type="appCommon:LeadProductContextType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Product context of Lead page.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="applicationFinalizationSessionUuid" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Unique UUID generated at start of general contract application finalization.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>


    <!-- signOverInternet task -->
    <xs:element name="InitSignOverInternetRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignOverInternetResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignOverInternetRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateSignRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignOverInternetResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractSignUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>


    <!-- signOverMobile task -->
    <xs:element name="InitSignOverMobileRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignOverMobileResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                    <xs:sequence>
                        <xs:element name="applicationType" type="ApplicationType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Application type related to finalization</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="binaryDocument" type="BinaryDocument" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Binary document(s)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignOverMobileRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateSignRequest">
                    <xs:sequence>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Branch officer internal code.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignOverMobileResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractSignUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="AbstractSignUpdateResponse">
        <xs:complexContent>
            <xs:extension base="AbstractFinalizationUpdateResponse">
                <xs:sequence>
                    <xs:element name="productResult" type="ProductResult" minOccurs="0"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ProductResult">
        <xs:sequence>
            <xs:element name="resultingProductType" type="ResultingProductType">
                <xs:annotation>
                    <xs:documentation>Result product type of this finalization.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resultingProductId" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Id of the finalized product.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productFeature" type="ProductFeature" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Other product features.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ResultingProductType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INS_PP">
                <xs:annotation>
                    <xs:documentation>Premium insurance (aka "Pojisteni pravidelnych vydaju").</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INS_CP">
                <xs:annotation>
                    <xs:documentation>Travel insurance.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACCOUNT">
                <xs:annotation>
                    <xs:documentation>Current or saving account.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DISPONENT">
                <xs:annotation>
                    <xs:documentation>Disponent.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CARD">
                <xs:annotation>
                    <xs:documentation>Tangible card.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VIRTULIZED_CARD">
                <xs:annotation>
                    <xs:documentation>Virtualized card.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ProductFeature">
        <xs:sequence>
            <xs:element name="type" type="ProductFeatureType">
                <xs:annotation>
                    <xs:documentation>TODO</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="value" type="xs:string">
                <xs:annotation>
                    <xs:documentation>TODO</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ProductFeatureType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACCESS_RIGHT_ACCOUNT_ID">
                <xs:annotation>
                    <xs:documentation>TODO</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>



    <!-- signAtBranch task -->
    <xs:element name="InitSignAtBranchRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="signatureType" type="appCommon:SignatureTypeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Selected signature type (it must be either SIGN_PAD or BLUE_SIGN)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
<!--                        TODO why is internalCode here and also in UpdateSignpadConfirmRequest? -->
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Internal code</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>


    <!-- signpad task -->
    <xs:element name="InitSignpadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignpadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="signedDocumentId" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>OBS ID of the document that was signed on signpad</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractSignUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- signpad confirm task -->
    <xs:element name="InitSignpadConfirmRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignpadConfirmResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                    <xs:sequence>
                        <xs:element name="signedDocumentId" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>OBS ID of the document that was signed on signpad</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadConfirmRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateSignRequest">
                    <xs:sequence>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Internal code</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadConfirmResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadConfirmInternalCodeOnlyRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Internal code</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadConfirmInternalCodeOnlyResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- blueSign task -->
    <xs:element name="InitBlueSignRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitBlueSignResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateBlueSignRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateSignRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateBlueSignResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractSignUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchByPwdRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchByPwdResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchByPwdRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateSignRequest">
                    <xs:sequence>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Branch officer internal code.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchByPwdResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractSignUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchBySmsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchBySmsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitSignResponse">
                    <xs:sequence>
                        <xs:element name="secondApplicantCuid" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Customer ID of second applicant (disponent/cardholder/co-debtor).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchBySmsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateSignRequest">
                    <xs:sequence>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Branch officer internal code.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="secondApplicantCuid" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Customer ID of second applicant (disponent/cardholder/co-debtor).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchBySmsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractSignUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- signDifferently task -->
    <xs:element name="InitSignDifferentlyRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignDifferentlyResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="documentGroup" type="appCommon:DocumentGroupTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Collection of document groups</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="completionId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Id of the contract completion (in OBS)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="deliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible delivery channels (regardless of the currently selected delivery way)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedDeliveryWay" type="appCommon:DistributionChannelTypeTO">
                            <xs:annotation>
                                <xs:documentation>Selected delivery channel</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="channelAlreadySelected" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Idicate that channel is already selected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignDifferentlyRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="deliveryWay" type="appCommon:DistributionChannelTypeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Selected delivery channel (valid only when the delivery channel is modified)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignDifferentlyResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                    <xs:sequence>
                        <xs:element name="expirationDate" type="xs:dateTime" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Document expiration date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDocumentUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDocumentUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="documentGroup" type="appCommon:DocumentGroupTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Collection of document groups</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="completionId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Contract completion ID, which we are uploading documents for</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="contractType" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Contract completion type, which we are uploading documents for</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="envelopeId" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Envelope ID, which we are uploading documents for</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cuid" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Cuid of applicant, which we are uploading documents for</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="coDebtorData" type="loanCommon:CoDebtorDataTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Co-debtor data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="applicationCreateDate" type="xs:dateTime">
                            <xs:annotation>
                                <xs:documentation>Date of application creation.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="identificationDocumentValidTo" type="xs:date" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Primary identification document expiration date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mainIncomeDocumentDeliveryStatus" type="DocumentDeliveryStatusTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Status of main income document delivery.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="internalCodes" type="loanCommon:InternalCodeTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Possible internal codes</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="DocumentDeliveryStatusTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FAILED">
                <xs:annotation>
                    <xs:documentation>Delivery failed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SUCCESSFUL">
                <xs:annotation>
                    <xs:documentation>Document was delivered successfully and no manual processing is necessary</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MANUAL_PROCESSING">
                <xs:annotation>
                    <xs:documentation>Document was delivered but needs to be manually processed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="UpdateDocumentUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="uploadDocumentsPostpone" type="xs:dateTime" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Upload documents postponed to date</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="identificationDocumentValidTo" type="xs:date" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Primary identification document expiration date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documents" type="bc:DeliveredDocumentWithGroupTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>list of delivered documents to create/update with related document groups</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Internal code</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDocumentUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDeclarationRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDeclarationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="completionId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Id of the contract completion (in OBS) to finalize</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="envelopeId" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Id of the application envelope (its main application will be finalized)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="disponentOrCardHolderCompletionId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Completion ID of disponent authorization application or card holder (if there is such application in envelope).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="activationType" type="ActivationType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Type of General Contract activation.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeclarationRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="accDeclaration" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>If true acc declaration will be generated.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="activationType" type="ActivationType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Type of General Contract activation.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeclarationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNoDocumentUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="finalizationId" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Id of the finalization process. It must be used in all subsequent calls.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNoDocumentUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="applicationType" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>Type of current application to finalization</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="applicationId" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Id of the application in AMS</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="completionId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Id of the contract completion (in OBS) to finalize</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="envelopeId" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>Id of the application envelope (its main application will be finalized)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNoDocumentUploadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNoDocumentUploadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCompletionIdForEnvelopeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Application envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCompletionIdForEnvelopeResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="completionId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Contract completion ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:element name="HasRequiredDocumentsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="applicationId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>IDs of application to return the documents for.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractType" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Type of signed contract.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="HasRequiredDocumentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="hasRequiredDocuments" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Flag whether there are required documents.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:element name="GetMobilitySubApplicationCompletionIdRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="completionId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Contract completion id from application to which we want to find mobility sub-application.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetMobilitySubApplicationCompletionIdResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="mobilityCompletionId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Completion id from mobility sub-application.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="CompletionStateTO">
        <xs:annotation>
            <xs:documentation>
                APPROVED, // RVII-1194
                REJECTED,
                CLIENT_CANCEL,
                FI_CANCEL,
                REOPENED,
                SIGNED,
                SUSPENDED, //RVIII-726
                UNDERWRITING,
                RECEIVED,   //RVIII-1503
                WARNING
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="REJECTED"/>
            <xs:enumeration value="CLIENT_CANCEL"/>
            <xs:enumeration value="FI_CANCEL"/>
            <xs:enumeration value="REOPENED"/>
            <xs:enumeration value="SIGNED"/>
            <xs:enumeration value="SUSPENDED"/>
            <xs:enumeration value="UNDERWRITING"/>
            <xs:enumeration value="RECEIVED"/>
            <xs:enumeration value="WARNING"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="CompletionStateChangeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>mandatory / ID of envelope to which belong all changes</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="completionStateChange" type="CompletionStateChangeTO">
                    <xs:annotation>
                        <xs:documentation>List of changed completions with minimum of one element</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="CompletionStateChangeTO">
        <xs:sequence>
            <xs:element name="completionId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Completion Id</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sequenceNumber" type="xs:int"/>
            <xs:element name="completionState" type="CompletionStateTO"/>
            <xs:element name="decisionReason" type="xs:string" minOccurs="0"/>
            <xs:element name="decisionClient" type="xs:string" minOccurs="0"/>
            <xs:element name="signatureDate" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="signatureChannel" type="SignatureChannelTypeTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Channel of signature</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="signatureType" type="SignatureTypeTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Type of signature</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="completionType" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Completion type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long"/>
            <xs:element name="expirationDate" type="xs:dateTime" minOccurs="0"/>
            <xs:element name="signOperator" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Operator who signed the completion state change</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="signIdBranch" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Id of branch where signed</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="SignatureTypeTO">
        <xs:annotation>
            <xs:documentation>Type of signature</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="BLUE_SIGN">
                <xs:annotation>
                    <xs:documentation>Real blue signature</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SIGN_PAD">
                <xs:annotation>
                    <xs:documentation>Signature on a signpad</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SignatureChannelTypeTO">
        <xs:annotation>
            <xs:documentation>Channel of signature</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="IB">
                <xs:annotation>
                    <xs:documentation>Internet banking</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TB">
                <xs:annotation>
                    <xs:documentation>Telephone banking</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BRANCH">
                <xs:annotation>
                    <xs:documentation>Branch</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="POST">
                <xs:annotation>
                    <xs:documentation>Post</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MESSENGER">
                <xs:annotation>
                    <xs:documentation>Courier</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SPB">
                <xs:annotation>
                    <xs:documentation>Smart phone banking</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="CompletionStateChangeResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreatePriorityEventRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of customer</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Id of application envelope</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="evenType" type="PriorityEventTypeTO">
                    <xs:annotation>
                        <xs:documentation>Type of event</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="newNoteContent" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Content of newly created note in AMG</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="PriorityEventTypeTO">
        <xs:annotation>
            <xs:documentation>Type of priority event</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACCOUNT_ACTIVATION">
                <xs:annotation>
                    <xs:documentation>Service account has been activated.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DOCUMENT_UPLOAD">
                <xs:annotation>
                    <xs:documentation>Document was upload.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FAST_TRACK">
                <xs:annotation>
                    <xs:documentation>Loan in 3 hours.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="CreatePriorityEventResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetInProgressApplicationsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Cuid of applicant, which we are uploading documents for</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="profileId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Profile ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="relationsToContract" type="xs:string" maxOccurs="unbounded"/>
                <xs:element name="includeMortgages" type="xs:boolean" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="InProgressApplication">
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Application ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Envelope ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="completionId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Contract completion ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationStatus">
                <xs:annotation>
                    <xs:documentation>Application status. One of DEMO, LEAD, UNFINISHED, APPROVED, REJECTED, CANCELLED, VERIFY, MANUALVERIFY, VIP_PAUSED,
                        WAITING, MANUALPAIRING.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="64"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="processStatus" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Process status</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationType">
                <xs:annotation>
                    <xs:documentation>Application type. One of CASH_LOAN, CONSOLIDATION or MORTGAGE_LOAN_REF.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="64"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="signChannelType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Sign channel type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Loan amount asked by client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accountId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Account id connected with application .</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastModified" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>Date and time of last application modification</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="createdAt" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Date and time of application creation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="signingEnabled" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Date and time of application creation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cancelEnabled" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether this contract could be canceled.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="continueEnabled" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether it is possible to continue application process.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="uploadRequired" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether it is required to upload documents to this contract.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="signableNonContractualDocument" type="DocumentContractTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of signable non-contractual documents.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="processCode" type="appCommon:ProcessCodeTO">
                <xs:annotation>
                    <xs:documentation>Code of process.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expirationDate" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Date when the application automatically expires if not finished by client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mortgageStage" type="loanCommon:StageCodeTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Mortgage application stage.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Product name defined by customer.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="GetInProgressApplicationsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="application" type="InProgressApplication" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>All in progress applications</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>


    <xs:element name="InitLoanSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitLoanSummaryResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="loanCommon:InitLoanSummaryResponseCommon">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateLoanSummaryResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>


    <xs:element name="InitOverdraftSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftSummaryResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="overdraftCommon:InitOverdraftSummaryResponseCommon">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftSummaryResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitChangeMobilityDatesRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitChangeMobilityDatesResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="establishmentDate" type="xs:date" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Establishment date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="revocationDate" type="xs:date">
                            <xs:annotation>
                                <xs:documentation>Revocation date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="minEstablishmentDate" type="xs:date">
                            <xs:annotation>
                                <xs:documentation>Minimal revocation date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="minRevocationDate" type="xs:date">
                            <xs:annotation>
                                <xs:documentation>Minimal establishment date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="establishmentDateExpired" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Establishment date is expired.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="revocationDateExpired" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Revocation date is expired.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateChangeMobilityDatesRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="establishmentDate" type="xs:date" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Establishment date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="revocationDate" type="xs:date">
                            <xs:annotation>
                                <xs:documentation>Revocation date.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateChangeMobilityDatesResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetMortgageApplicationsStagesRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="generalContractCompletionId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>ID of general contract completion.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetMortgageApplicationsStagesResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="appStageData" type="loanCommon:MortgageApplicationStageTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Tuples of application ID and mortgage application stage.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ValidateDeliveryDocumentsDateRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope ID, which we are validating documents delivery date for</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deliveryDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Documents delivery date</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ValidateDeliveryDocumentsDateResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="NewContractActivatedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="generalContractType" type="appCommon:GeneralContractType">
                    <xs:annotation>
                        <xs:documentation>Client general contract type.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="generalContractCompletionId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Client general contract completion ID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="applicationCreatedDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date of application creation.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractActivationDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date of application activation.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of customer.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of envelope</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="activationReason" type="ActivationReason">
                    <xs:annotation>
                        <xs:documentation>Reason of activation.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="identificationPayment" type="IdentificationPaymentTO" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Identification Payment.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="NewContractActivatedResponse">
        <xs:complexType/>
    </xs:element>

    <xs:complexType name="IdentificationPaymentTO">
        <xs:annotation>
            <xs:documentation>Identification Payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identification Payment Id.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="debtorAccountName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Debtor Account Name.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="messageForReceiver" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Message For Receiver.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="verifiedBy" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identification of operator who accepted Identification Payment.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ActivationReason">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SIGNED_AT_MESSENGER" />
            <xs:enumeration value="SIGNED_AT_BRANCH" />
            <xs:enumeration value="LOGIN_AT_BRANCH" />
            <xs:enumeration value="REMOTE_IDENTIFICATION" />
            <xs:enumeration value="BANKID_IDENTIFICATION" />
            <xs:enumeration value="O2" />
            <xs:enumeration value="EXISTING_CONTRACT_IDENTIFICATION" />
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="GetDocumentContractsInEnvelopeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of envelope.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDocumentContractsInEnvelopeResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documents" type="DocumentContractTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Uuid of the document being created.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="DocumentContractTO">
        <xs:annotation>
            <xs:documentation>Binary document of contract.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="uuid" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Id of document binnary</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="DocumentContractStatusTO">
                <xs:annotation>
                    <xs:documentation>Status of document creation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Application id</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="completionId" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Completion id</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="completionType" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Completion type</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="DocumentContractStatusTO">
        <xs:annotation>
            <xs:documentation>Document contract status.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="IN_PROCESS"/>
            <xs:enumeration value="IN_FINALIZATION"/>
            <xs:enumeration value="FINISHED"/>
            <xs:enumeration value="ERROR"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="InitDeliverMainIncomeDocumentViaAISPRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDeliverMainIncomeDocumentViaAISPResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="otherIncomeDocumentRequired" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>If true, a document for other income is required</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="bank" type="aisp:BankTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Banks supporting document delivery via AISP</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeliverMainIncomeDocumentViaAISPRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:choice>
                            <xs:element name="accountId" type="xs:string" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Permanently paired account identifier in PAPUCA</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="pairingHash" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Pairing identifier of temporary paired account in PAPUCA.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Code of an already paired bank whose accounts will be checked.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="permanentAccountPairing" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag whether account was paired permanently in PAPUCA.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="reuseForOtherIncome" type="xs:boolean" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Indicates that the main income document (bank) should be used for other income too.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:choice>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeliverMainIncomeDocumentViaAISPResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAispIncomeDocumentResultRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAispIncomeDocumentResultResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="documentDeliveryStatus" type="DocumentDeliveryStatusTO">
                            <xs:annotation>
                                <xs:documentation>>Status of income document delivery.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAispIncomeDocumentResultRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAispIncomeDocumentResultResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="finalizationId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Finalization process ID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="taskId" type="appCommon:TaskIdTO">
                    <xs:annotation>
                        <xs:documentation>
                            Id of the current task.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="ReasonTO">
        <xs:annotation>
            <xs:documentation>Reason for task being finished.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="REJECTED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="GetDocumentsUploadStatusRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="completionId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Completion ID (any from envelope) to get all document in envelope.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Envelope ID to get all document in envelope.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDocumentsUploadStatusResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="status" type="DocumentUploadStatusTO">
                    <xs:annotation>
                        <xs:documentation>Collection of document groups of all application in envelope</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitActivationPaymentRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitActivationPaymentResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateActivationPaymentRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest">
                    <xs:sequence>
                        <xs:element name="activationPaymentConfirmedByClient" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Client confirms activation payment.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateActivationPaymentResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitVictoryScreenRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitVictoryScreenResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="activationType" type="ActivationType" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Activation type from GC application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="applicationTypes" type="appCommon:ApplicationTypeTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Application types of apps from the envelope related to finalizationId</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateVictoryScreenRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateVictoryScreenResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPairingRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractInitFinalizationRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPairingResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="deviceName" type="xs:string"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePairingRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="AbstractUpdateFinalizationRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePairingResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="DocumentUploadStatusTO">
        <xs:annotation>
            <xs:documentation>Type of upload document status.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NOT_REQUIRED"/>
            <xs:enumeration value="NONE"/>
            <xs:enumeration value="ANY"/>
            <xs:enumeration value="ALL"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="BinaryDocument">
        <xs:sequence>
            <xs:element name="uuid" type="xs:string"/>
            <xs:element name="documentName" type="xs:string" minOccurs="0"/>
            <xs:element name="fileName" type="xs:string" minOccurs="0"/>
            <xs:element name="fileNameExtension" type="xs:string" minOccurs="0"/>
            <xs:element name="contentType" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApplicationType">
        <xs:annotation>
            <xs:documentation>Type of application.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="W_8_BEN"/>
            <xs:enumeration value="STOCK_ETF"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="InitRejectedRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitRequest">
                    <xs:sequence>
                        <xs:element name="applicationId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Application ID to get rejected status of application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="completionId" type="xs:long" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Completion ID to get rejected status of application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
    <xs:element name="InitRejectedResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractEmptyInitResponse">
                    <xs:sequence>
                        <xs:element name="rejectReason" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>Reason for rejecting the application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="rejectReasonClient" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>Reason for rejecting the application as communicated to client.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="ActivationType">
        <xs:annotation>
            <xs:documentation>Type of General Contract activation.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVATION_PAYMENT"/>
            <xs:enumeration value="BRANCH"/>
            <xs:enumeration value="CONTRACT_ACTIVATED"/>
            <xs:enumeration value="MANUAL_COMPLETION"/>
            <xs:enumeration value="EXISTING_CONTRACT_IDENTIFICATION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="GeneralContractActivationAllowedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of the envelope containing application.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GeneralContractActivationAllowedResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="allowed" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Is allowed to activate contract</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="activationReason" type="appCommon:ContractActivationAllowanceReasonTO" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Contract activation allowance reason.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>