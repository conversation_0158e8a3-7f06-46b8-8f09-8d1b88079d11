<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/mortgageOtbPayback"
           targetNamespace="http://airbank.cz/ams/ws/application/mortgageOtbPayback">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeStartRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOtbPaybackRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for mortgage loan OTB payback application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOtbPaybackResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing OtbPaybackTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeInitResponse">
                    <xs:sequence>
                        <xs:element name="otbPaybackAmount" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="otbPaybackAmountMin" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Minimal OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="otbPaybackAmountMax" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Maximal OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOtbPaybackRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for mortgage loan OTB payback application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="otbPaybackAmount" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOtbPaybackResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating OtbPaybackTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeUpdateChangeResponse">
                    <xs:sequence>
                        <xs:element name="otbPaybackAmount" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="otbPaybackAmountMin" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Minimal OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="otbPaybackAmountMax" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Maximal OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of summary page for mortgage loan OTB payback application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing SummaryTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeInitResponse">
                    <xs:sequence>
                        <xs:element name="otbPaybackAmount" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>OTB payback amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of summary page for mortgage loan OTB payback application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeUpdateSummaryRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating SummaryTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="loanCommon:BaseMortgageChangeUpdateSummaryResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>