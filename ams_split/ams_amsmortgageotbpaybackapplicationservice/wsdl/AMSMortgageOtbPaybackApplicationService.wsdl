<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/mortgageOtbPayback"
                  targetNamespace="http://airbank.cz/ams/ws/application/mortgageOtbPayback">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/mortgageOtbPayback">
            <xs:include schemaLocation="AMSMortgageOtbPaybackApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <!-- Start application -->
    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- OtbPayback Task -->
    <wsdl:message name="InitOtbPaybackRequest">
        <wsdl:part element="InitOtbPaybackRequest" name="InitOtbPaybackRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOtbPaybackResponse">
        <wsdl:part element="InitOtbPaybackResponse" name="InitOtbPaybackResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOtbPaybackFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOtbPaybackFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOtbPaybackRequest">
        <wsdl:part element="UpdateOtbPaybackRequest" name="UpdateOtbPaybackRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtbPaybackResponse">
        <wsdl:part element="UpdateOtbPaybackResponse" name="UpdateOtbPaybackResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOtbPaybackFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOtbPaybackFault"/>
    </wsdl:message>

    <!-- Summary Task -->
    <wsdl:message name="InitSummaryRequest">
        <wsdl:part element="InitSummaryRequest" name="InitSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryResponse">
        <wsdl:part element="InitSummaryResponse" name="InitSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSummaryRequest">
        <wsdl:part element="UpdateSummaryRequest" name="UpdateSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryResponse">
        <wsdl:part element="UpdateSummaryResponse" name="UpdateSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSummaryFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <!-- Application cancel request -->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <wsdl:portType name="mortgageOtbPaybackApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application to get payback from OTB. Uses cuid and idProfile from tracking context to identify customer and
                profile.
                Mandatory parameters:
                - mortgageId

                Validation errors:

                Generated business faults:
                - Application.MortgageLoanChange.InvalidMortgageLoanId - Invalid mortgage id
                - Application.MortgageLoanChange.ZeroAmountOtb - There is no money on open-to-buy account
                - Application.Reject.CantApplyForLoanProduct - User can't apply for loan product!
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous loan application in the VERIFY status
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitOtbPayback">
            <wsdl:documentation>Operation used to initialize the OtbPaybackTask.</wsdl:documentation>
            <wsdl:input message="InitOtbPaybackRequest"/>
            <wsdl:output message="InitOtbPaybackResponse"/>
            <wsdl:fault name="InitOtbPaybackFault" message="InitOtbPaybackFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateOtbPayback">
            <wsdl:documentation>Operation used to update data for the OtbPaybackTask.

                For all transitions except for back, following attributes are required:
                - otbPaybackAmount

                Generated business faults:
                - Application.MortgageLoanChange.InvalidMortgageLoanId - Invalid mortgage id
                - BELOW_MIN_AMOUNT - OTB payback amount is lower than minimal amount
                - OVER_MAX_AMOUNT - OTB payback amount is higher than maximal amount
            </wsdl:documentation>
            <wsdl:input message="UpdateOtbPaybackRequest"/>
            <wsdl:output message="UpdateOtbPaybackResponse"/>
            <wsdl:fault name="UpdateOtbPaybackFault" message="UpdateOtbPaybackFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:documentation>Operation used to initialize the SummaryTask.</wsdl:documentation>
            <wsdl:input message="InitSummaryRequest"/>
            <wsdl:output message="InitSummaryResponse"/>
            <wsdl:fault name="InitSummaryFault" message="InitSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSummary">
            <wsdl:documentation>Operation used to update data for the SummaryTask.

                For all transitions except for back, following attributes are required:
                none

                Generated business faults:
                - Application.MortgageLoanChange.InvalidMortgageLoanId - Invalid mortgage id
            </wsdl:documentation>
            <wsdl:input message="UpdateSummaryRequest"/>
            <wsdl:output message="UpdateSummaryResponse"/>
            <wsdl:fault name="UpdateSummaryFault" message="UpdateSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="mortgageOtbPaybackApplicationBinding" type="mortgageOtbPaybackApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOtbPayback">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOtbPaybackFault">
                <soap:fault name="InitOtbPaybackFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOtbPayback">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOtbPaybackFault">
                <soap:fault name="UpdateOtbPaybackFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSummaryFault">
                <soap:fault name="InitSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSummaryFault">
                <soap:fault name="UpdateSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="mortgageOtbPaybackApplicationService">
        <wsdl:documentation>Service is providing operations related to existing customer mortgage loan OBT payback application.

            The application has the following task IDs:
            - otbPaybackTask - page where you can request payback from OTB
            - summaryTask - summary page for this application

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGTH, INVALID_EXACT_LENGTH - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
            - DATE_RANGE_VALIDATOR - year is not in range 1900 - 2999
        </wsdl:documentation>
        <wsdl:port name="mortgageOtbPaybackApplicationPort" binding="mortgageOtbPaybackApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/loanOtbPayback"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>