<xs:schema elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://airbank.cz/ams/ws/application/common"
           xmlns:bc="http://airbank.cz/ams/ws/backoffice/common"
           xmlns:appConstant="http://airbank.cz/ams/ws/application/constant"
           xmlns:faultCommon="http://airbank.cz/common/ws/fault"
           targetNamespace="http://airbank.cz/ams/ws/application/common">

    <xs:import namespace="http://airbank.cz/ams/ws/backoffice/common" schemaLocation="BackofficeApplication.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/constant" schemaLocation="ApplicationConstant.xsd"/>
    <xs:import namespace="http://airbank.cz/common/ws/fault" schemaLocation="commonSoapFault.xsd"/>

    <xs:complexType name="AbstractStartRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Application start request attributes (currently there are none).
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractEmptyStartResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Default application start response with no attributes.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="taskId" type="TaskIdTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the current task. It is used to decide about the processing in the client application.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="AbstractStartResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Default application start response attributes.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the envelope containing application. It must be used in all subsequent calls.</xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Current version of the application envelope. It must be used in a subsequent call.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taskId" type="TaskIdTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the current task. It is used to decide about the processing in the client application.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="AbstractEmptyInitRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic init method parameters for methods, that do not expect envelopeId and envelopeVersion.
                Should be used only as an exception in specific cases.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="AbstractInitRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic init method parameters.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractEmptyInitRequest">
                <xs:sequence>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of the envelope containing application (it is returned by the start operation).
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Current version of the application envelope as returned by previous operation. It is used to
                                validate, that the application is up-to-date.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractEmptyInitResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned by init method, that does not return envelopeVersion and possibleTransitions.
                Should be used only as an exception in specific cases.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="possibleTransitions" type="TransitionIdTO" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        List of all possible transitions from current
                        task. One of the transitions must be chosen,
                        when invoking
                        the following update method.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="AbstractInitResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned by any init method.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractEmptyInitResponse">
                <xs:sequence>
                    <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Current version of the application envelope. It is used to validate, that the application is
                                up-to-date.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="AbstractValidatedAttributeInitResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned by any init method.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractInitResponse">
                <xs:sequence>
                    <xs:element name="validatedAttribute" type="ValidatedAttributeTO" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="AbstractEmptyUpdateRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes used as a input for the update operation without envelope.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="selectedTransition" type="TransitionIdTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Id of the transition selected by the user (it must be one of the transitions returned by a previous init operation).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="AbstractUpdateRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes used as a input for the update operation.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractEmptyUpdateRequest">
                <xs:sequence>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Id of the envelope containing application.</xs:documentation>
                        </xs:annotation>
                    </xs:element>

                    <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Current version of the application envelope. It is used to validate, that the application envelope is
                                up-to-date.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractEmptyUpdateResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>
                Base for all responses to update without envelope
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="taskId" type="TaskIdTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Id of the current task (the list of all possible values is given in the description of eachservice).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="AbstractUpdateResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>
                Base for all responses to update
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractEmptyUpdateResponse">
                <xs:sequence>
                    <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Current version of the application envelope. It is used to validate, that the application envelope is
                                up-to-date.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractValidationFaultUpdateResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>
                Base for all responses to update with validation fault
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractUpdateResponse">
                <xs:sequence>
                    <xs:element name="validationFault" type="faultCommon:ValidationResultTO" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>
                                List of all validation faults.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractCancelRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes used to cancel an application.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the envelope containing application.</xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="cancelReason" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Reason for cancelling the application. There are no constraints, because this is generated internally
                        in Blaze.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="cancelReasonClient" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Reason for cancelling the application as communicated to client. There are no constraints, because
                        this is generated internally in Blaze.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractCancelResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned when canceling an application (currently there are none).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractRejectedResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned when rejecting an application.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AbstractInitResponse">
                <xs:sequence>
                    <xs:element name="rejectReason" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Reason for rejecting the application. There are no constraints, because
                                this is generated internally in Blaze.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="rejectReasonClient" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Reason for rejecting the application as communicated to client. There are
                                no constraints, because this is generated internally in Blaze.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="AbstractGetCurrentTaskRequest" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes used to get current task.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the envelope containing application.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractGetCurrentTaskResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned from all getCurrentTask methods.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Current version of the application envelope.</xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="taskId" type="TaskIdTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Id of the current task (the list of all possible values is given in the description of each service).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AbstractResumeApplicationResponse" abstract="true">
        <xs:annotation>
            <xs:documentation>Generic attributes returned from all resumeApplication methods.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the envelope containing application. It must be used in all subsequent calls.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="envelopeVersion" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Current version of the application envelope. It must be used in a subsequent call.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taskId" type="TaskIdTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Id of the current task. It is used to decide about the processing in the client application.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApplicationTypeTO">
        <xs:annotation>
            <xs:documentation>Type of application.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACCOUNT"/>
            <xs:enumeration value="CARD"/>
            <xs:enumeration value="CARD_HOLDER"/>
            <xs:enumeration value="CONTRACT"/>
            <xs:enumeration value="DISPONENT"/>
            <xs:enumeration value="DISPONENT_AUTH"/>
            <xs:enumeration value="CASH_LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="MORTGAGE_REF"/>
            <xs:enumeration value="MORTGAGE_CHNG"/>
            <xs:enumeration value="CO_DEBTOR"/>
            <xs:enumeration value="LOAN_CHQ"/>
            <xs:enumeration value="MOBILITY"/>
            <xs:enumeration value="TERM_DISPONENT"/>
            <xs:enumeration value="TARIFF"/>
            <xs:enumeration value="TERM_ACCOUNT"/>
            <xs:enumeration value="TERM_CARD"/>
            <xs:enumeration value="AUTH_RESET"/>
            <xs:enumeration value="INSURANCE"/>
            <xs:enumeration value="TERM_INSURANCE"/>
            <xs:enumeration value="INSURANCE_CHANGE"/>
            <xs:enumeration value="INS_TR_CREATE"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="TERM_OVERDRAFT"/>
            <xs:enumeration value="PERSONAL_DATA_CHANGE"/>
            <xs:enumeration value="INVESTMENT_CONTRACT"/>
            <xs:enumeration value="MOBILE_REGISTRATION"/>
            <xs:enumeration value="MOBILE_ONBOARDING"/>
            <xs:enumeration value="PENSION"/>
            <xs:enumeration value="PENSION_BENEFICIARY"/>
            <xs:enumeration value="PP_INSURANCE"/>
            <xs:enumeration value="TERM_PP_INSURANCE"/>
            <xs:enumeration value="SME_CONTRACT"/>
            <xs:enumeration value="SME_CONTRACT_PARTICIPANT"/>
            <xs:enumeration value="SME_CARD"/>
            <xs:enumeration value="SME_ACCOUNT"/>
            <xs:enumeration value="SPLIT_PAYMENT"/>
            <xs:enumeration value="SME_BUSINESS_DATA_CHANGE"/>
            <xs:enumeration value="STOCK_ETF"/>
            <xs:enumeration value="W_8_BEN"/>
            <xs:enumeration value="FIXED_DEPOSIT"/>
            <xs:enumeration value="TERM_FIXED_DEPOSIT"/>
            <xs:enumeration value="INVESTMENT_TO_AIRBANK"/>
            <xs:enumeration value="INVESTMENT_CERTIFICATE"/>
            <xs:enumeration value="PIP_INSURANCE"/>
            <xs:enumeration value="TERMINATE_PIP_INSURANCE"/>
            <xs:enumeration value="SME_DISPONENT"/>
            <xs:enumeration value="SME_DISPONENT_AUTHORIZATION"/>
            <xs:enumeration value="SME_TERM_DISPONENT"/>
            <xs:enumeration value="PIP_INSURANCE_CHANGE"/>
            <xs:enumeration value="SME_CARD_HOLDER"/>
            <xs:enumeration value="SME_CHANGE_ENTITLED_PERSON"/>
            <xs:enumeration value="SME_TERM_ACCOUNT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TaskIdTO">
        <xs:annotation>
            <xs:documentation>BPM task ID</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"></xs:minLength>
            <xs:maxLength value="100"></xs:maxLength>
            <xs:pattern value="[a-zA-Z0-9]*"></xs:pattern>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TransitionIdTO">
        <xs:annotation>
            <xs:documentation>BPM transition ID</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"></xs:minLength>
            <xs:maxLength value="100"></xs:maxLength>
            <xs:pattern value="[a-zA-Z0-9]*"></xs:pattern>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AddressTO">
        <xs:annotation>
            <xs:documentation>General address</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="countryCode" type="CountryCodeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        2-letter country code
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="town" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"></xs:minLength>
                        <xs:maxLength value="64"></xs:maxLength>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="streetOrLocality" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"></xs:minLength>
                        <xs:maxLength value="64"></xs:maxLength>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="house" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        House number
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"></xs:minLength>
                        <xs:maxLength value="50"></xs:maxLength>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="zip" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Address zip code
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"></xs:minLength>
                        <xs:maxLength value="10"></xs:maxLength>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="addressConfirmedByClient" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates that client confirmed that address is valid (in case the address or its certain attributes
                        are not found in address registers)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CmsPurposeAddressTO">
        <xs:annotation>
            <xs:documentation>Address for CMS purposes e.g. card delivery address</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="addressLine1" type="AddressLineTO">
                <xs:annotation>
                    <xs:documentation>The first line of address (usually contains street and number, name of box etc.)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="addressLine2" type="AddressLineTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Complementing address information</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="town" minOccurs="0">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:maxLength value="40" />
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="zipCode">
                <xs:annotation>
                    <xs:documentation>
                        Address zip code
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:maxLength value="12" />
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="countryAlpha2Code" type="CountryAlpha2CodeTO">
                <xs:annotation>
                    <xs:documentation>
                        2-letter country code
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="street" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        street name (if present already included in one of address lines)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="houseNumberAndSeq" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        contains house number and house sequence e.g. 2824/15 (if present already included in one of address lines)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="boxName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        name of mail box (if present already included in one of address lines)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AddressLineTO">
        <xs:restriction base="xs:string">
            <xs:maxLength value="40" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="CardDesignTO">
        <xs:annotation>
            <xs:documentation>Info about particular card design</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="designId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        ID of the card design
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Name of the card design
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="displayOrder" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="marketingName" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Used for contact(less) cards
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="technology" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Card type - chip, virt, ctls
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="CurrencyCodeTO">
        <xs:annotation>
            <xs:documentation>3-letter ISO currency code</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:length value="3"></xs:length>
            <xs:pattern value="[A-Z]*"></xs:pattern>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="BankAccountNumberTO">
        <xs:annotation>
            <xs:documentation>Bank account number</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="prefix" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="number" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="bankCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MobilityAccountTO">
        <xs:annotation>
            <xs:documentation>Data of the account which should be moved.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="BankAccountNumberTO">
                <xs:sequence>
                    <xs:element name="currency" type="CurrencyCodeTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>3-letter ISO code of the currency.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="currencyDifferenceConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Indicates that user confirmed difference between our current account currency
                                and currency of account in other bank.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="MobilityHolderTO">
        <xs:annotation>
            <xs:documentation>First step mobility holder (contains info about account, document and document type).
                Shall be common for mobility, current account and general contract applications</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="mobility" type="MobilityAccountTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Mobility data.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentTypeList" type="DocumentTypeListForCountryGroupTO" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of possible documents for proof of identity for particular group of countries.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>CIF primary id document type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>CIF primary id document number</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ExtendedMobilityNotificationsTO">
        <xs:annotation>
            <xs:documentation>Requests of notification made by the user.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="MobilityNotificationsTO">
                <xs:sequence>
                    <xs:element name="standingOrderMobilityType" type="StandingOrderMobilityTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>preference of client which standing order to move either ALL or SELECTED only - shall be listed offline at branch</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="stopPreviousBankAccountTransfersRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>stop performing transfers to previous bank account in the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="stopPreviousBankAccountStandingOrdersRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>stop sending standing order transfers to previous bank account in the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cooperationRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Request to provide some cooperation in orders transfer from other bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cooperationRequestType" type="CooperationRequestTypeTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>definition of how shall we cooperate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="provideIncomingTransfersListRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>provide list of incoming transfers and bills from previous account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="provideOutgoingTransfersListRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>provide list of outgoing transfers and bills from previous account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="balanceTransferRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Request to transfer final positive balance from the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="notifyOriginalBankAccountCancelRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>we shall deliver cancellation notification to the original bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="establishBankOperationsRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>we shall begin transfers, orders and bills for the newly established account to the establishedDate</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="MobilityNotificationsTO">
        <xs:annotation>
            <xs:documentation>Requests of notification made by the user - simplified version.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="revocationDate" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of revocation TP/inkas (orig. Bank)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="establishmentDate" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of establishment TP/inkas (AB)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="notificationDeliveryType" type="NotificationDeliveryTypeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>type of delivery - online or offline</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="overviewDeliveryRequest" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>send final list of standing orders and payment bills (inkaso)</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InitNotificationsResponse">
        <xs:complexContent>
            <xs:extension base="AbstractInitResponse">
                <xs:sequence>
                    <xs:element name="mobilityNotifications" type="MobilityNotificationsTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Notifications data.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minDateBeforeCancelOrders" type="xs:date" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>The minimum date before cancel standing orders</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="minDateBeforeEstablishOrders" type="xs:date" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>The minimum date before setting up standing orders</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="selectedCurrentAccountId" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Selected client's current account id (OBS id).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="mailingAddress" type="AddressTO" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Default mailing address where to send card.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="email" type="EmailTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Clients email</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="UpdateNotificationsRequest">
        <xs:complexContent>
            <xs:extension base="AbstractUpdateRequest">
                <xs:sequence>
                    <xs:sequence>
                        <xs:element name="mobility" type="MobilityNotificationsTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Mobility data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="standingOrderTypeAccepted" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The minimum date before cancel standing orders</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="activeCooperationRequestTypeAccepted" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The minimum date before setting up standing orders</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="establishBankOperationsAccepted" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client confirms that there is no need to establish new requests</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="StandingOrderMobilityTypeTO">
        <xs:annotation>
            <xs:documentation>Scope for standing orders mobility - either ALL or SELECTED</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ALL"/>
            <xs:enumeration value="SELECTED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="NotificationDeliveryTypeTO">
        <xs:annotation>
            <xs:documentation>possible notification type - online or by snail mail</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="EMAIL"/>
            <xs:enumeration value="POST"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="CooperationRequestTypeTO">
        <xs:annotation>
            <xs:documentation>types of possible mobility cooperation - passive or active cooperation -
                only provide some examples how to notify senders/recipients on bank account change
                or directly notify senders/receivers about bank account change</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="PASSIVE">
                <xs:annotation>
                    <xs:documentation>default selection - only provide some examples how to </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ACTIVE">
                <xs:annotation>
                    <xs:documentation>active cooperation is required - we directly notify recipients/senders on account change.
                        Not preferred by us. Warning shall be thrown</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>


    <xs:complexType name="AccountTO">
        <xs:annotation>
            <xs:documentation>Account data</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="accountId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Account id in database</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="number" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="currency" type="CurrencyCodeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>3-letter ISO code of the currency.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountExtTO">
        <xs:annotation>
            <xs:documentation>Account extended data</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AccountTO">
                <xs:sequence>
                    <xs:element name="balanceAccounting" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Accounting balance of bank account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="balanceAvailable" type="xs:decimal">
                        <xs:annotation>
                            <xs:documentation>Available balance of bank account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="accountType" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>Type code of bank account</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="CountryCodeTO">
        <xs:annotation>
            <xs:documentation>
                2-letter country code
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"></xs:minLength>
            <xs:maxLength value="2"></xs:maxLength>
            <xs:pattern value="[a-zA-Z]*"></xs:pattern>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="CountryAlpha2CodeTO">
        <xs:annotation>
            <xs:documentation>
                2-letter country code
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:length value="2" />
            <xs:pattern value="[a-zA-Z]*" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="PhoneNumberTO">
        <xs:annotation>
            <xs:documentation>Data type for general phone number</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="prefix" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Phone number prefix</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:length value="4"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="number" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Phone number</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:length value="9"/>
                        <xs:pattern value="[0-9]*"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="EmailTO">
        <xs:annotation>
            <xs:documentation>Email address</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="5"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="LoginTO">
        <xs:annotation>
            <xs:documentation>User login name</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="8"/>
        </xs:restriction>
    </xs:simpleType>


    <xs:simpleType name="AddressTypeTO">
        <xs:annotation>
            <xs:documentation>Address type. Following values are supported:
                - PERMANENT - Permanent address
                - REPORTS - Address to send statements
                - CARD - Address to send card
                - PIN - Address to send card PIN
                - EMPLOYMENT - Work address
                - REGISTERD - Employer address
                - FOREIGN_PERMANENT - Foreigner's permanent address in CZ
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"></xs:minLength>
            <xs:maxLength value="20"></xs:maxLength>
            <xs:pattern value="[A-Z]*"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="AddressKindTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CUSTOMER_ADDRESS" />
            <xs:enumeration value="CMS_PURPOSE_ADDRESS" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AccountParamsTO">
        <xs:annotation>
            <xs:documentation>Data type holding parameters of current or saving account</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="accountName" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Name of the saving account given by the user.</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"></xs:minLength>
                        <xs:maxLength value="25"></xs:maxLength>
                        <xs:pattern value="[\p{L}\d \-]*"></xs:pattern>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="currencyCode" type="CurrencyCodeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>3-letter ISO code of the selected currency for the account.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mainAccount" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether the account being created is the main account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanService" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether the current account is chosen as service account for a loan.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="forced" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Flag indicating that creation of the current account has been forced during application for a loan.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DebitCardParamsTO">
        <xs:annotation>
            <xs:documentation>Data type holding parameters of debit card application where card application is on single page
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="addressKind" type="AddressKindTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Indicates, whether the customer wants to send the card to customer address
                        (address type REPORTS will be used). Or custom address. Then cmsPurpose address will be used
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardDeliveryAddress" type="CmsPurposeAddressTO">
                <xs:annotation>
                    <xs:documentation>Custom card delivery address. Used when addressKind==CMS_PURPOSE_ADDRESS</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pinDeliveryAddress" type="CmsPurposeAddressTO">
                <xs:annotation>
                    <xs:documentation>Custom PIN delivery address. Used when addressKind==CMS_PURPOSE_ADDRESS</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="transferPin" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates, whether the original pin should be transfer to the new card.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="showPin" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates, whether the pin should be displayed in internet banking.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sendPin" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates, whether the pin should be send to the customer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="selectedCardDesignType" type="CardDesignTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Card design selected by the user</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="embossedName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Name to be embossed on the card.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="embossedNameChanged" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates, whether client changed default embossed name.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="virtual" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates, whether the card is virtual (pay code).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="createdVirtualizedCards" type="xs:long" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Count of created virtualized cards.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="offerVirtualizedCard" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Show virtualized card offer</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requiredCardDevice" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Indicates, if we create physical card through gpe</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="allowedVirtualizedCard" type="xs:boolean" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Is virtialized card allowed?</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Card name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="defaultVirtualizedCardName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Default card name for virtual card</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="defaultPlasticCardName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Default card name for plastic card</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardBusinessCategory" type="appConstant:CardBusinessCategoryTO" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Card business category</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CardTO">
        <xs:annotation>
            <xs:documentation></xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cardId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Card id in database</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Card number</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cardHolder" type="PersonTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Name of the card holder</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="embossedName" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Embossed name on the card.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="TariffTO">
        <xs:annotation>
            <xs:documentation>Tariff type. Possible values are: 'ALL_INCLUSIVE', 'PAY_AS_YOU_GO' and optionally 'INDIVIDUAL'.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="UsernameTO">
        <xs:annotation>
            <xs:documentation>Data type for username (login name).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="8"></xs:minLength>
            <xs:maxLength value="64"></xs:maxLength>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="PersonTO">
        <xs:annotation>
            <xs:documentation></xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="personId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Person id in database</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="title" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Person title before name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Person first name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Person last name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="titleAfter" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Person title after name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateOfBirth" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Birth date of the person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="completed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Is applicant's application completed?</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PersonDataTO">
        <xs:annotation>
            <xs:documentation>Basic data about client. Used for applicant or disponent.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="salutation" type="SalutationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client salutation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="title" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client title before name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client first name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstNameConfirmed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether client confirmed first name (in case it is not usual name
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client last name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastNameConfirmed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether client confirmed last name (in case it is not usual name
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="titleAfter" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client title after name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="gender" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Codelist of gender of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="maritalStatus" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Codelist of marital status of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ClientBasicDataTO">
        <xs:annotation>
            <xs:documentation>Basic data about client. Used for applicant or disponent.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="PersonDataTO">
                <xs:sequence>
                    <xs:element name="phone" type="PhoneNumberTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Clients phone number</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="email" type="EmailTO" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Clients email</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="emailDiacriticsConfirmedByClient" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>
                                Indicates that client confirmed that email address is valid even it contains diacritics
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="emailDomainConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>
                                Indicates that client confirmed that email address domain is valid even it is not present in common email domains list.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ClientOtherDataTO">
        <xs:annotation>
            <xs:documentation>Other data about client. Used for GC applicant, disponent or card holder.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="salutation" type="SalutationTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client salutation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="citizenships" type="xs:string" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Selected citizenship of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="selectedDocumentType" type="DocumentTypeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Selected document for proof of client identity.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Number of selected document for proof of client identity.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="personalId" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Czech personal identification number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateOfBirth" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of birth of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicantLegalRepresentative" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates that applicant is legal representative of teenage disponent or card holder.
                        Can't be true in case of general contract applicant
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="disponentOrCardHolderLegalRepresentative" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates that disponent or card holder is legal representative of teenage applicant.
                        Can't be true in case of general contract applicant
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="placeOfBirth" type="BirthPlaceTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Place of birth of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="gender" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Gender of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="politicallyExposed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Politically exposed person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="permanentAddressSameAsApplicant" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether co-debtor selected same address as main applicant.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="permanentAddress" type="AddressTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Permanent residence address.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mailingAddressDifferent" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether user selected different address than residence for mailing with bank.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mailingAddress" type="AddressTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Address where all documents for client should be sent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mainApplicantPermanentAddress" type="AddressTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Permanent address of main applicant.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="BirthPlaceTO">
        <xs:annotation>
            <xs:documentation>Customer's birth place.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="discriminator" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Birth place discriminator from MDM codelist BIRTH_PLACE_DISCRIMINATOR (https://wiki.airbank.cz/x/uYVZDw).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:choice minOccurs="0">
                <xs:element name="ruianBirthPlace" type="RuianBirthPlaceTO"/>
                <xs:element name="countryBirthPlace" type="CountryBirthPlaceTO"/>
                <xs:element name="outsideCountryBirthPlace" type="OutsideCountryBirthPlaceTO"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RuianBirthPlaceTO">
        <xs:sequence>
            <xs:choice>
                <xs:element name="townCode" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Code of town from RUIAN.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="pragueMunicipalDistrictCode" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Code of Prague municipal district from RUIAN.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CountryBirthPlaceTO">
        <xs:sequence>
            <xs:element name="countryAlpha2Code" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Two letter code of country.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="location" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Text representation of exact location for given country.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OutsideCountryBirthPlaceTO">
        <xs:sequence>
            <xs:element name="place" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Text representation of vague location (for example "On the sea").</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OnboardingClientBasicDataTO">
        <xs:annotation>
            <xs:documentation>Basic data about client used for onboarding.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="citizenships" type="xs:string" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Selected citizenship of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client first name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastName" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Client last name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="personalId" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Czech personal identification number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idCardNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Czech personal identification number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idCardValidUntil" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>ID card validity.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dateOfBirth" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Date of birth of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="placeOfBirth" type="BirthPlaceTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Place of birth of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="gender" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Gender of the client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="permanentAddress" type="AddressTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Permanent residence address.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mailingAddressDifferent" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether user selected different address than residence for mailing with bank.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mailingAddress" type="AddressTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Address where all documents for client should be sent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstNameConfirmedByClient" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether client confirmed first name (in case it is not usual name).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lastNameConfirmedByClient" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether client confirmed first name (in case it is not usual name).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="politicallyExposed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates whether client confirmed first name (in case it is not usual name).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="smeRequest" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>request sme</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ClientAdditionalDataTO">
        <xs:annotation>
            <xs:documentation>Additional data about client. Used for disponent or card holder.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="documentIssuedBy" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Authority which issued the ID document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentIssuedDate" type="xs:date" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Issue date of the ID document.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ClientContactDataTO">
        <xs:annotation>
            <xs:documentation>Confirmation of client contact data. Used for GC applicant, disponent or card holder.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="phone" type="PhoneNumberTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Phone using which user wants to sign documents and operations.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="phoneDuplicityConfirmedByClient" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates that client confirmed that it is OK that given phone number is being used by some other bank's client already
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="email" type="EmailTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Email for communication with the bank.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="emailDiacriticsConfirmedByClient" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates that client confirmed that email address is valid even it contains diacritics
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="emailDuplicityConfirmedByClient" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates that client confirmed that it is OK that given email address is being used by some other bank's client already
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Use newer `DocumentTypeListTO` type -->
    <xs:complexType name="DocumentTypeListForCountryGroupTO">
        <xs:annotation>
            <xs:documentation>Map holding list of accepted id documents for group of countries</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="typeList" type="DocumentTypeTO" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of accepted document types</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="countriesGroup" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>CZ, EU or OTHER</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="DocumentTypeTO">
        <xs:annotation>
            <xs:documentation>Data type holding document type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="DocumentTypeListTO">
        <xs:annotation>
            <xs:documentation>Map holding list of accepted id documents for group of countries</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="typeList" type="DocumentTypeDataTO" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>List of accepted document types</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="countriesGroup" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>CZ, EU or OTHER</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DocumentTypeDataTO">
        <xs:annotation>
            <xs:documentation>Data type holding document type with additional info.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Document type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="nonAdultOnly" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Type is available only for non adults</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Finalization common object - Start-->
    <xs:complexType name="ChannelSignatureTO">
        <xs:annotation>
            <xs:documentation>Record for a single channel, that can be used for contract signature</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="DistributionChannelTypeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Type of channel</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="signatureType" type="SignatureTypeTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Possible signature type</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ChannelDocumentGroupTO">
        <xs:annotation>
            <xs:documentation>Record for a single channel, that can be used to deliver documents</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="documentGroup" type="DocumentGroupTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Collection of document groups</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="type" type="DistributionChannelTypeTO" use="required">
            <xs:annotation>
                <xs:documentation>Type of channel</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="DocumentGroupTO">
        <xs:annotation>
            <xs:documentation>Document group</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Document group ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="DocumentGroupTypeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Document group type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requiredCount" type="xs:int" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Number of required documents from this group</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="detail" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Detailed information about this group. Possible values are PRIMARY, SECONDARY, MAIN_INCOME, OTHER_INCOME.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="document" minOccurs="0" maxOccurs="unbounded" type="DocumentTO">
                <xs:annotation>
                    <xs:documentation>Collection of all possible documents</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="templateUidSjmContract" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>UID of generated document for SJM type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="relation" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Document group relation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Document group product type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveryWay" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Document group delivery way</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Document group CUID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="optional" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag whether document group is optional</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DocumentTO">
        <xs:annotation>
            <xs:documentation>Record for a single document of a group</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Document type</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                        <xs:pattern value="[A-Z_]*"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="productType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Product type</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                        <xs:pattern value="[A-Z]*"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="loanbinFrom" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Minimum value of loan bin for this document</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Document count</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveredDocument" type="bc:DeliveredDocumentTO" minOccurs="0" maxOccurs="1"/>
            <xs:element name="financialInstitutionInstruction" type="FinancialInstitutionInstructionTO" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="allowUseAsLegalBindingDocument" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ContractDeliveryTO">
        <xs:annotation>
            <xs:documentation>Contains detailed information about contract delivery</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="channel" type="DistributionChannelTypeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Channel, that is used to deliver the contract to client</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="branch" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>If the contract is delivered to branch, this element contains the branch ID. TODO Add pattern</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="60"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="addressType" type="AddressTypeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>If the contract is delivered by post, this element contains the type of address used for delivery</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>

    </xs:complexType>

    <xs:complexType name="MonetaryAmountTO">
        <xs:annotation>
            <xs:documentation>Data about money amount.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="currencyCode" type="CurrencyCodeTO" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Currency of the money.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Money amount</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="DocumentGroupTypeTO">
        <xs:annotation>
            <xs:documentation>Document group type. Following values are supported:
                - PRIMARY - TODO
                - SECONDARY - TODO
                - OTHER - TODO
                - ADDRESS - TODO
                - BUSINESS - TODO
                - REMOTE_IDENTIFICATION - TODO
                - FINANCIAL - TODO
                - IDENTIFICATION - TODO
                - JOINT_ASSETS - TODO
                - MOBILE_PHONE - TODO
                - NODEBT - TODO
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="30"/>
            <xs:pattern value="[A-Z_]*"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="DistributionChannelTypeTO">
        <xs:annotation>
            <xs:documentation>Distribution channel. Following values are supported:
                - IB - Internet banking
                - BRANCH - Branch
                - POST - Post ("snail mail")
                - MESSENGER - Messneger (or courier)
                - ICC - (Internal) call center
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
            <xs:pattern value="[A-Z]*"></xs:pattern>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SignatureTypeTO">
        <xs:annotation>
            <xs:documentation>
                The way to sign a contract. Following values are supported:
                - PWD_OTP - Password + one-time password, i.e. SMS code
                - OTP - One-time password, i.e. SMS code
                - PWD - Password
                - QUESTION - Security question
                - NO_AUTH - No signature required
                - SIGNPAD - Signpad
                - BLUE_SIGN - Standard "paper signature" (using a blue pen)
                - PWD_PPF - TODO
                - CALL_ID - TODO
                - PWDCMD - TODO
                - PWD_SPB - Password in mobile application (smart-phone banking)?
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
            <xs:pattern value="[A-Z_]*"/>
        </xs:restriction>
    </xs:simpleType>
    <!--Finalization common object - End-->

    <xs:simpleType name="ApplicationStatusTO">
        <xs:annotation>
            <xs:documentation>
                Status of an application. Following values are supported:
                - DEMO
                - UNFINISHED
                - APPROVED
                - REJECTED
                - CANCELLED
                - VERIFY
                - MANUALVERIFY
                - VIP_PAUSED
                - WAITING
                - MANUALPAIRING
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
            <xs:pattern value="[A-Z_]*"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ApproveResultTO">
        <xs:annotation>
            <xs:documentation>
                Result of a LAP request. Following values are supported:
                APPROVED,
                LOAN_LOWER_APPROVED,
                DECLINED,
                CONTRACT_APP_DECLINED,
                PRODUCT_APP_DECLINED,
                MANUAL_VERFICATION,
                VERIFICATION,
                VIP_PAUSED,
                FAIL_TO_FINISH,
                UNKNOWN,
                WARNING,
                SUCCESS,
                BAD_REQUEST,
                SYSTEM_UNAVAILABLE,
                UNKNOWN_ERROR,
                CR_MVCR,
                CR_LOW_AGE,
                CR_ADD_OTHER,
                CR_STATUS_CC,
                CR_DPD,
                CR_INT_CRIT,
                CR_DPD_BRKI,
                CR_DPD_PPF,
                CR_LOW_AGECH,
                CR_EXE,
                CR_CONSENT_BRKI,
                CR_CONSENT_SOLUS,
                CR_CONSENT_PPF,
                CR_INT_CRIT_LB,
                CR_DPD_AB_LB,
                CR_DPD_PPF_LB
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
            <xs:pattern value="[A-Z_]*"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SalutationTO">
        <xs:annotation>
            <xs:documentation>
                Following values are supported:
                TODO
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
            <xs:pattern value="[A-Z_]*"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="IncomeCounterTO">
        <xs:annotation>
            <xs:documentation>Indicates which position of income in income orders. Starts from 0.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:int">
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="EshopProductTypeTO">
        <xs:annotation>
            <xs:documentation>Výčet produktů pro e-shop</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CURRENT_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>Běžný účet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SAVING_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>Spořící účet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEBIT_CARD">
                <xs:annotation>
                    <xs:documentation>Debetní karta</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CASH_LOAN">
                <xs:annotation>
                    <xs:documentation>Úvěr</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CONSOLIDATION">
                <xs:annotation>
                    <xs:documentation>Konsolidace úvěru</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MORTGAGE">
                <xs:annotation>
                    <xs:documentation>Hypotéka</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MORTGAGE_REFINANCE">
                <xs:annotation>
                    <xs:documentation>Refinancování hypotéky</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MOBILITY">
                <xs:annotation>
                    <xs:documentation>Mobitlita</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INSURANCE">
                <xs:annotation>
                    <xs:documentation>Pojištění</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INSURANCE_TRAVEL">
                <xs:annotation>
                    <xs:documentation>Cestovní pojištění</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ATM_WITHDRAWAL_PACKAGE">
                <xs:annotation>
                    <xs:documentation>Balíček neomezených výběrů</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="FeePackageTO">
        <xs:annotation>
            <xs:documentation>Fee package data for the application.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="active" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Is fee package active</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="price" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Price of the package</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Name of the package</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Fee package type</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FeePackageParamsTO">
        <xs:annotation>
            <xs:documentation>Fee package parameters.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Fee package type</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Name of the package</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="feeAmount" type="xs:decimal">
                <xs:annotation>
                    <xs:documentation>Price of the package</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="feeProductCount" type="xs:long">
                <xs:annotation>
                    <xs:documentation>Count of products before the package activation</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- this is a copy of OBS ContractTO.ProductType, if changed in OBS, add or modify here as well -->
    <xs:simpleType name="ProductTypeTO">
        <xs:annotation>
            <xs:documentation>výčet produktů</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="LOAN">
                <xs:annotation>
                    <xs:documentation>úvěr</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SAVING_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>spořící účet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEBIT_CARD">
                <xs:annotation>
                    <xs:documentation>debetní karta</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CURRENT_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>běžný účet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CREATE_MOBILITY">
                <xs:annotation>
                    <xs:documentation>mobilita</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CHNG_LOAN_PAYDAY">
                <xs:annotation>
                    <xs:documentation>změna data splátky</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CHNG_LOAN_INST_AMOUNT">
                <xs:annotation>
                    <xs:documentation>změna výše splátky</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SET_LOAN_PAYMENT_HOLIDAY">
                <xs:annotation>
                    <xs:documentation>splátkové prázdniny</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CHNG_LOAN_INST_COL">
                <xs:annotation>
                    <xs:documentation>změna výše splátky managerem vymahani</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EXTRA_INSTALMENT">
                <xs:annotation>
                    <xs:documentation>mimořádná splátka</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PAY_SOONER">
                <xs:annotation>
                    <xs:documentation>Předčasné splacení</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CREATE_REFINANCING">
                <xs:annotation>
                    <xs:documentation>Refinancovani uveru</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CONSOLIDATION">
                <xs:annotation>
                    <xs:documentation>Konsolidace uveru - pro sluzbu getAvailableLoanBin</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MORTGAGE_LOAN_EXTRA_INSTALMENT">
                <xs:annotation>
                    <xs:documentation>extra instalment on mortgage loan</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CHNG_MORTGAGE_LOAN_PAYDAY">
                <xs:annotation>
                    <xs:documentation>change of instalment day on mortgage loan</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CHNG_MORTGAGE_LOAN_INST_PREPAY_AMOUNT">
                <xs:annotation>
                    <xs:documentation>change of instalment or (and) prepay amount on mortgage loan</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CHNG_MORTGAGE_LOAN_INST_COL">
                <xs:annotation>
                    <xs:documentation>change of instalment amount on mortgage loan by collateral manager</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OTB_PAYBACK">
                <xs:annotation>
                    <xs:documentation>payback from open to buy account</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MORTGAGE_LOAN">
                <xs:annotation>
                    <xs:documentation>mortgage loan</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INSURANCE">
                <xs:annotation>
                    <xs:documentation>Pojištění</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INS_TRAVEL">
                <!-- TODO XR-2477 waitin for OBS -->
                <xs:annotation>
                    <xs:documentation>Cestovní pojištění</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OVERDRAFT">
                <xs:annotation>
                    <xs:documentation>kontokorent</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TERMINATE_OVERDRAFT">
                <xs:annotation>
                    <xs:documentation>ukonceni kontokorentu</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVESTMENT">
                <xs:annotation>
                    <xs:documentation>Investice</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PENSION">
                <xs:annotation>
                    <xs:documentation>Penze</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ProductModeTO">
        <xs:annotation>
            <xs:documentation>list of product modes</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NONE">
                <xs:annotation>
                    <xs:documentation>Not defined</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PRODUCTION">
                <xs:annotation>
                    <xs:documentation>Production mode</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PILOT">
                <xs:annotation>
                    <xs:documentation>Pilot mode</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ProductTypeModeItemTO">
        <xs:sequence>
            <xs:element name="productType" type="ProductTypeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Product type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="productMode" type="ProductModeTO" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Product mode.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RelationshipTO">
        <xs:annotation>
            <xs:documentation>Information about customer relation to the bank. Values from MDM codelists.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codeListType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Posible values are 'BUSINESS_RELATIONSHIP_PURPOSE', 'SOURCE_OF_INCOME', 'SOURCE_OF_SAVINGS'
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="codeListValue" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Codelist value
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RequiredDocumentGroupInfo">
        <xs:annotation>
            <xs:documentation>Group of required documents, grouped by channel, product type and document group detail if exists</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="groupType" type="GroupType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>type of document group</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="groupRelation" type="GroupRelation" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>information about association to employment (for which income type is document required)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fulfillmentStatus" type="FulfillmentStatus" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Fulfillment state of requirements.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="FinancialInstitutionInstructionTO">
        <xs:annotation>
            <xs:documentation></xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="financialInstitution" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instruction" type="xs:string">
                <xs:annotation>
                    <xs:documentation></xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="FulfillmentStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="UNDELIVERED"/>
            <xs:enumeration value="DELIVERED"/>
            <xs:enumeration value="REJECTED"/>
            <xs:enumeration value="PROCESSED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="GroupType">
        <xs:annotation>
            <xs:documentation>document group, enumeration.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDRESS"/>
            <xs:enumeration value="BUSINESS"/>
            <xs:enumeration value="FINANCIAL"/>
            <xs:enumeration value="IDENTIFICATION"/>
            <xs:enumeration value="JOINT_ASSETS"/>
            <xs:enumeration value="MOBILE_PHONE"/>
            <xs:enumeration value="NODEBT"/>
            <xs:enumeration value="OTHER"/>
            <xs:enumeration value="REMOTE_IDENTIFICATION"/>
            <xs:enumeration value="CO_DEBTORS"/>
            <xs:enumeration value="MORTGAGE_LOAN"/>
            <xs:enumeration value="MORTGAGE_LOAN_VALUATION"/>
            <xs:enumeration value="OBLIGATION"/>
            <xs:enumeration value="INSURANCE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="GroupRelation">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRIMARY"/>
            <xs:enumeration value="SECONDARY"/>
            <xs:enumeration value="TERTIARY"/>
            <xs:enumeration value="MAIN_INCOME"/>
            <xs:enumeration value="OTHER_INCOME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="MobilityPackageTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TRANSFER_PAYMENTS_AND_CANCEL_ACCOUNT"/>
            <xs:enumeration value="CANCEL_ACCOUNT"/>
            <xs:enumeration value="BY_CLIENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ApplicationDataRequiredTypeTO">
        <xs:annotation>
            <xs:documentation>Type of application data type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FULL"/>
            <xs:enumeration value="LIMITED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="IncomeTimeTypeTO">
        <xs:annotation><xs:documentation>Type of income time</xs:documentation></xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="SHORT">
                <xs:annotation><xs:documentation>Time of income is short (less that X years)</xs:documentation></xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LONG">
                <xs:annotation><xs:documentation>Time of income is long (more that X years)</xs:documentation></xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ProcessCodeTO">
        <xs:annotation>
            <xs:documentation>Code of process.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="POS_FINANCING"/>
            <xs:enumeration value="TOP_UP"/>
            <xs:enumeration value="CHILD_ACCOUNT_FEATURES_ACQUISITION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SegmentTO">
        <xs:annotation>
            <xs:documentation>Segment of application.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="RETAIL"/>
            <xs:enumeration value="SME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="LegalSegmentTO">
        <xs:annotation>
            <xs:documentation>Legal segment of the application.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CUSTOMER">
                <xs:annotation>
                    <xs:documentation>Fyzická osoba nepodnikající (FON)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ENTREPRENEUR">
                <xs:annotation>
                    <xs:documentation>Fyzická osoba podnikající (FOP)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LEGAL_ENTITY">
                <xs:annotation>
                    <xs:documentation>Právnická osoba (PO)</xs:documentation>
                </xs:annotation>
            </xs:enumeration><xs:enumeration value="OTHER">
                <xs:annotation>
                    <xs:documentation>Ostatní</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="QrCodeSourceTO">
        <xs:annotation>
            <xs:documentation>Source of QR code.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="O2"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ContractActivationAllowanceReasonTO">
        <xs:annotation>
            <xs:documentation>Contract activation allowance reason.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="O2"/>
            <xs:enumeration value="BANKID"/>
            <xs:enumeration value="ECI"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="GeneralContractType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RETAIL" />
            <xs:enumeration value="ENTREPRENEUR" />
            <xs:enumeration value="LEGAL_ENTITY" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AdvancedDeviceIdentificationTO">
        <xs:annotation>
            <xs:documentation>Info about device</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="androidID" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Android ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="androidGSFID" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Android google services framework ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="androidHWFingerPrint" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Android hardware fingerprint</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="iOSidentifierForVendor" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>iOS identifier for vendor</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="LeadProductContextType">
        <xs:annotation>
            <xs:documentation>Product context of Lead page (enum)</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CURRENT_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>Běžný účet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SAVING_ACCOUNT">
                <xs:annotation>
                    <xs:documentation>Spořící účet</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CASH_LOAN">
                <xs:annotation>
                    <xs:documentation>Úvěr</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CONSOLIDATION">
                <xs:annotation>
                    <xs:documentation>Konsolidace úvěru</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MORTGAGE">
                <xs:annotation>
                    <xs:documentation>Hypotéka</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MORTGAGE_REFINANCE">
                <xs:annotation>
                    <xs:documentation>Refinancování hypotéky</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AffiliateParamsTO">
        <xs:annotation>
            <xs:documentation>Affiliate parameters.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="product" type="AffiliateProductType"/>
            <xs:element name="provider" type="AffiliateProviderType"/>
            <xs:element name="affiliatePartner" type="xs:string"/>
            <xs:element name="transaction" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AffiliateProductType">
        <xs:annotation>
            <xs:documentation>Affiliate product.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CASH_LOAN">
                <xs:annotation>
                    <xs:documentation>Cash loan product.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CONSOLIDATION">
                <xs:annotation>
                    <xs:documentation>Consolidation product.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="AffiliateProviderType">
        <xs:annotation>
            <xs:documentation>Affiliate provider.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="AFFILBOX"/>
            <xs:enumeration value="TUNE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ValidatedAttributeTO">
        <xs:annotation>
            <xs:documentation>Definition of attribute which is subject of validation</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string"/>
            <xs:element name="constraint" type="AttributeConstraintTO" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AttributeConstraintTO">
        <xs:annotation>
            <xs:documentation>Constraint of attribute defined by name and it's allowed value</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="AttributeConstraintTypeTO"/>
            <xs:element name="restriction" type="ConstraintRestrictionTO" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="AttributeConstraintTypeTO">
        <xs:annotation>
            <xs:documentation>Type of attribute constraint.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="INVALID_DATE_RANGE">
                <xs:annotation>
                    <xs:documentation>Date is in invalid range</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INVALID_LENGTH">
                <xs:annotation>
                    <xs:documentation>String has invalid length</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="IS_REQUIRED">
                <xs:annotation>
                    <xs:documentation>Field is not null</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="WRONG_FORMAT">
                <xs:annotation>
                    <xs:documentation>String is in wrong format according to regular expression</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ConstraintRestrictionTO">
        <xs:annotation>
            <xs:documentation>Constraint of attribute defined by name and it's allowed value</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="type" type="ConstraintRestrictionTypeTO"/>
            <xs:element name="value" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ConstraintRestrictionTypeTO">
        <xs:annotation>
            <xs:documentation>Type of constraint restriction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="min">
                <xs:annotation>
                    <xs:documentation>Minimal allowed value - it can contain numeric value for numeric attributes or string in format YYYY-MM-DD for date
                        attributes
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="max">
                <xs:annotation>
                    <xs:documentation>Maximal allowed value - it can contain numeric value for numeric attributes or string in format YYYY-MM-DD for date
                        attributes
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="regexp">
                <xs:annotation>
                    <xs:documentation>Regular expression</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="sentenceRegexp">
                <xs:annotation>
                    <xs:documentation>Regular expression for password that can be defined as a sentence</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="minSentenceUniqueChars">
                <xs:annotation>
                    <xs:documentation>Minimum number of unique characters for password that is defined a sentence</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="BankInfo">
        <xs:sequence>
            <xs:element name="url" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Urls of banks from BankId</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bankCode" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Code of bank</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="title" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Name of bank</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="GeneralContractTO">
        <xs:annotation>
            <xs:documentation>Rámcová smlouva</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        id rámcové smlouvy
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contractNumber" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        číslo rámcové smlouvy
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="completionId" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        ID kompletace rámcové smlouvy
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="isOwner" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>
                        zda je v roli vlastníka
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ownerFirstName" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        jméno vlastníka rámcové smlouvy
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ownerSurname" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        příjmení vlastníka rámcové smlouvy
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status">
                <xs:annotation>
                    <xs:documentation>
                        status rámcové smlouvy
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="ACTIVE" />
                        <xs:enumeration value="PASIVE" />
                        <xs:enumeration value="TERMINATED" />
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="idProfile" type="xs:long">
                <xs:annotation>
                    <xs:documentation>
                        identifikace profilu
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="relationType" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Vztah ke smlouvě</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="generalContractType" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Typ rámcové smlouvy</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
