<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appr="http://airbank.cz/ams/ws/approvaldata/common"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           xmlns="http://airbank.cz/ams/ws/scoring/verification/common"
           targetNamespace="http://airbank.cz/ams/ws/scoring/verification/common" jxb:version="2.1"
           elementFormDefault="qualified">

    <xs:annotation>
        <xs:documentation>Common types of invoke approval interface.</xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/approvaldata/common" schemaLocation="../xsd/ApprovalData.xsd"/>

    <xs:complexType name="ApplicationScoringResultRequest">
        <xs:annotation>
            <xs:documentation>Information about results for approval process, which was requested by primary system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="idWorkflow" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of approval in LAP system.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalProcessType" type="ApprovalProcessType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Typ vykonaného schvalovacího procesu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="approvalProcessResult" type="ApprovalProcessResult" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výsledek schvalování.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="result" type="Result" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V RDR nejlépe odpovídá entitě Score. Tuto entitu má vytvářet z hlediska LAPu jen Blaze.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ProductResult">
        <xs:annotation>
            <xs:documentation>V RDR nejlépe odpovídá entitě Decision. Výsledek schvalování pro daný produkt - rámcová smlouva, úvěr, atd...</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>identifier of application</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="decisionReason" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Reason (aggregated) of decision (mostly with the highest priority)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="decisionReasonClient" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Reason of decision translated to reason communicated with client.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productSystemDecision" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>decision relevant for current result type.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about product type.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productScoreResult" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about scoring type (0+ or 0-), means if maximal annuity is enough for whole loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AllowedDistribution">
        <xs:annotation>
            <xs:documentation>Required documents for applications set.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Definition of product type group (DEPOSIT or LOAN)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanBinFrom" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Loan amount from which is document required.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of required documents of current document type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroupCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of required documents from document group.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroupDetail" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about association to employment (for which income type is document required)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="allowedDocumentDeliveryWay" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about channel, that is allowed as delivery channel for required document.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroup" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>document group</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>cuid related to document</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="Operation" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>operation with document</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="documentGroupOptional" type="xs:boolean">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>flag whether required document group is optional</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Warning">
        <xs:sequence>
            <xs:element name="warningCode" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>code of raised warning.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="warningDetail" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>detail information about warning, useful for back office operator.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of application due to was warning raised.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="Operation" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>operation with warning</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="Result">
        <xs:annotation>
            <xs:documentation>V RDR nejlépe odpovídá entitě Score. Tuto entitu má vytvářet z hlediska LAPu jen Blaze.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="allowedChannel" type="AllowedChannel" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>list of allowed distribution channels</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="allowedDistribution" type="AllowedDistribution" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>list of required documents</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productResult" type="ProductResult" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>list výsledků schvalování pro jednotlivé projekty</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationDecision" type="ApplicationDecision" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>list of application decisions</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="mortgageResult" type="MortgageResult" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozšířující atributy rozhodnutí pro hypotéku.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="moveToWf" type="MoveToWf" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>List přechodů na další workflow</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="outputData" type="OutputData" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Výstupní data</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanApplicationUpdate" type="LoanApplicationUpdate" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Aktualizovaná úvěrová data - jen pro konsolidaci</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="alert" type="Alert" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>kolekce alertů vyhozených Blazem</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="consolidatedLoanResult" type="ConsolidatedLoanResult" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>kolekce reprezentující vyjádření Blazu ke kandidátním závazkům (schválení/zamítnutí).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskInterestRate" type="RiskInterestRate" minOccurs="0" maxOccurs="2">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Reprezentuje nápočet riskových sazeb ve scoringu žádosti.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MortgageResult">
        <xs:annotation>
            <xs:documentation>Rozšířující atributy rozhodnutí pro hypotéku.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="maximalApprovedAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximální schválená částka hypotéky</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskGrade" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>riskGrade na žádosti o hypotéku</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedContract" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Identifikátor skutečně evidovaného závazku v BRKI.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxTerm" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Maximální term pro risk.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OutputData">
        <xs:annotation>
            <xs:documentation>Výstupní data</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="changeProductParameters" type="ChangeProductParameters" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>změny parametrů úvěru RISKem.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApprovalProcessType">
        <xs:annotation>
            <xs:documentation>Typ vykonaného shvalovacího procesu.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="NEW_GC"/>
            <xs:enumeration value="NEW_PROD"/>
            <xs:enumeration value="COMPL"/>
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="DOC_OFFICE"/>
            <xs:enumeration value="DOC_EVAL_COMPL"/>
            <xs:enumeration value="VERIFICATION"/>
            <xs:enumeration value="WARNING"/>
            <xs:enumeration value="CHANGE_LOAN"/>
            <xs:enumeration value="CHANGE_LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="MORTGAGE_REF"/>
            <xs:enumeration value="PREDRAWN_CHECK"/>
            <xs:enumeration value="UNDERWRITING"/>
            <xs:enumeration value="MANUAL_PAIRING"/>
            <xs:enumeration value="MANUAL_CHECKS"/>
            <xs:enumeration value="PRESCORING"/>
            <xs:enumeration value="STOCK_ETF"/>
            <xs:enumeration value="ONLINE_LOAN_BIN"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="AllowedChannel">
        <xs:annotation>
            <xs:documentation>Povolený kanál doručení</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="allowedDeliveryWay" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>allowed channel for documentation delivery to Air/Bank</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="allowedSignatureWay" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>allowed channel for signing contract/supplement.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cashLoanAmountTo" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>permission for channel, only if cash loan amount is below this value. Valid only for loan applications.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about product type.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>cuid related to channel</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="publishedDocumentType" type="PublishedDocumentType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>channel document type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation" type="Operation" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>operation with channel</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="Operation">
        <xs:annotation>
            <xs:documentation>Operation with document</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADD"/>
            <xs:enumeration value="REMOVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ApplicationDecision">
        <xs:annotation>
            <xs:documentation>
                Result of decision for particular product types (e.g. approved amount for loan, etc.)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of application to which decision is related</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="productType" type="ApplicationDecisionProductType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>product type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amount" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>approved amount</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="amountFastTrack" type="xs:decimal" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>approved amount for fast track process</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxTerm" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>maximum number of months for repayment</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="employeeType" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Employee Type</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApplicationDecisionProductType">
        <xs:annotation>
            <xs:documentation>product type for application decision</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CASH_LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="SPLITPAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ChangeProductParameters">
        <xs:annotation>
            <xs:documentation>loan application parameters changed by risk operator during approval process (client's application will be updated by this
                values)
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="creditAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>final loan amount (defined by risk operator)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="paymentNum" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of loan installments (defined by risk operator)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="campaingCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>marketing code for special offer (defined by risk operator)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="yearInterestRate" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>year interest rate (defined by risk operator)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="bonusYearInterestRate" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>bonus year interest rate (defined by risk operator)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ConsolidatedLoanResult">
        <xs:annotation>
            <xs:documentation>Reprezentuje vyjádření Blazu ke kandidátním závazkům (schválení/zamítnutí).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="candidateObligationId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of candidate obligation (defined in application management system)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="fiCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>code of financial institution</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditBureauName" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>code of credit bureau, where the obligation was found.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isInternal" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Zda se jedná o úvěr z AirBank.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedContracts" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>hash, made from obligation identifiers</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="manualCheck" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information if manual pairing was required (if TRUE then manual pairing is processed)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanRejected" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about candidate obligation (if TRUE then is rejected)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reason why candidate obligation was rejected.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejectReasonClient" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>reason (displayed to client), why candidate obligation was rejected.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="processOrigin" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>process, when data was collected (calculated)expected value are from mdm register: PRESCORING, BRKI, UNDERWRITING,
                                POSTSCORING, MANUAL_PAIRING, MANUAL_PAIRING_BLAZE
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>
                                OBS číslo úvěru pro interní závazek.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanApplicationUpdate">
        <xs:annotation>
            <xs:documentation>Aktualizovaná úvěrová data - jen pro konsolidaci</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="creditOffer" type="appr:CreditOffer" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>credit offer (proposal for consolidation loan)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedContract" type="PairedContract" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>list of paired contracts</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PairedContract">
        <xs:annotation>
            <xs:documentation>Paired contract</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="calculatedUnpaidPrincipal" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculated unpaid principal for candidate obligation.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="calculatedEIR" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculated effective year interest rate.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedContracts" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>hash, made from obligation identifiers.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="responseReason" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>calculation result.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>total amount for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="firstUtilizationDate" type="xs:date" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date when loan was activated.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="repaymentPeriod" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of installments for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>amount of installment for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="instalmentDay" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>day in month, when payment is required.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="currency" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>code of currency, that candidate obligation loan was provided.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="residualAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unpaid principal plus future interests.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="nrResidualInstalment" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>count of residual installments for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="unpaidPrincipal" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unpaid principal (value from registry)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="unpaidInterest" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>future interests (value from registry)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="candidateObligationId" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="financialInstitution" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>code of financial institution, that provided loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="received" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>date and time when the calculation was made.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="monthlyFeesAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>sum of monthly fees for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="calculatedInstAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>installment amount (calculated) for candidate obligation loan.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="creditType" type="appr:LoanCreditType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Credit type of candidate obligation / CC, cash loan, overdraft</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="processOrigin" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>process, when data was collected (calculated)expected value are from mdm register: PRESCORING, BRKI, UNDERWRITING,
                                POSTSCORING, MANUAL_PAIRING, MANUAL_PAIRING_BLAZE;
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="unpaidPrincipalModified" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unpaid principal (value from registry)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="PublishedDocumentType">
        <xs:annotation>
            <xs:documentation>channel document type</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="GENERAL_CONTRACT"/>
            <xs:enumeration value="CONTRACT_SUPPLEMENT"/>
            <xs:enumeration value="AFFIDAVIT"/>
            <xs:enumeration value="MORTGAGE_CONTRACT"/>
            <xs:enumeration value="MORTGAGE_APPLICATION"/>
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="SPLITPAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="NextManualActivity">
        <xs:annotation>
            <xs:documentation>Type of next manual activity</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="PARTIALLY_MORTGAGE"/>
            <xs:enumeration value="EXPERT_INCOME_MORTGAGE"/>
            <xs:enumeration value="VIP_MORTGAGE"/>
            <xs:enumeration value="STANDARD_MORTGAGE"/>
            <xs:enumeration value="MANUAL_PAIRING_MORTGAGE"/>
            <xs:enumeration value="SPECIFIC_MORTGAGE"/>
            <xs:enumeration value="CHECK_LAND_REGISTRY_MORTGAGE"/>
            <xs:enumeration value="CHECK_QUANTIFICATION_MORTGAGE"/>
            <xs:enumeration value="PRE_DRAWN_CHECK_MORTGAGE"/>
            <xs:enumeration value="FINAL_DRAWN_CHECK_MORTGAGE"/>
            <xs:enumeration value="CHANGE_PARAM_MORTGAGE"/>
            <xs:enumeration value="WARNING"/>
            <xs:enumeration value="CREDIT_EXPERT_HIGH_CE"/>
            <xs:enumeration value="CREDIT_EXPERT"/>
            <xs:enumeration value="CREDIT_EXPERT_LOW_CE"/>
            <xs:enumeration value="CREDIT_EXPERT_CONS_HIGH_CE"/>
            <xs:enumeration value="CREDIT_EXPERT_CONS"/>
            <xs:enumeration value="CREDIT_EXPERT_CONS_LOW_CE"/>
            <xs:enumeration value="EXPERT_INCOME_HIGH_CE"/>
            <xs:enumeration value="EXPERT_INCOME"/>
            <xs:enumeration value="EXPERT_INCOME_LOW_CE"/>
            <xs:enumeration value="EXPERT_INCOME_CONS_HIGH_CE"/>
            <xs:enumeration value="EXPERT_INCOME_CONS"/>
            <xs:enumeration value="EXPERT_INCOME_CONS_LOW_CE"/>
            <xs:enumeration value="EXPERT_CONSOLIDATION"/>
            <xs:enumeration value="VIP"/>
            <xs:enumeration value="PILOT"/>
            <xs:enumeration value="UW_COMPLETION"/>
            <xs:enumeration value="RISK_CASE"/>
            <xs:enumeration value="SPECIFIC_CASE_HIGH_CE"/>
            <xs:enumeration value="SPECIFIC_CASE"/>
            <xs:enumeration value="SPECIFIC_CASE_LOW_CE"/>
            <xs:enumeration value="SPECIFIC_CASE_CONS_HIGH_CE"/>
            <xs:enumeration value="SPECIFIC_CASE_CONS"/>
            <xs:enumeration value="SPECIFIC_CASE_CONS_LOW_CE"/>
            <xs:enumeration value="FAIL_TO_FINISH"/>
            <xs:enumeration value="MANUAL_PAIRING"/>
            <xs:enumeration value="VERIFICATION_EMPLOYER"/>
            <xs:enumeration value="VERIFICATION_ENTREPRENEUR"/>
            <xs:enumeration value="VERIFICATION_FOREIGN_EMPLOYER"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="CheckedIncomeType">
        <xs:annotation>
            <xs:documentation>Type of checked income</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAIN_INCOME"/>
            <xs:enumeration value="OTHER_INCOME"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="MoveToWf">
        <xs:annotation>
            <xs:documentation>Přechod na další workflow</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="nextManualActivity" type="NextManualActivity">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>následující manuální aktivita</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="operation" type="Operation">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>operace - přidání/odebrání</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="order" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>pořadí</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="checkedCuid" type="xs:long">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>klient pro verifikaci</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="checkedIncomeType" type="CheckedIncomeType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>příjem pro verifikaci</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="verificationLevel" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozsah ověření pro manuální verifikační aktivitu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ApprovalProcessResult">
        <xs:annotation>
            <xs:documentation>Výsledek schvalování.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="1" name="created" type="xs:dateTime">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>datum a čas vzniku výsledku schvalování v LAPu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="1" name="type" type="ApprovalProcessResultType">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Stav proběhlého schvalovacího procesu</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="reason" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozhodnutí - výčtový typ je definován jen pro type IN (FI_CANCEL, CLIENT_CANCEL)</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="reasonClient" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Rozhodnutí, které se zobrazí klientovi</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="comment" type="xs:string"/>
            <xs:element maxOccurs="1" minOccurs="0" name="userId" type="xs:string">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>V případě administrativního ukončení schvalování (např. storno) se eviduje, kdo takové ukončení schvalování vyvolal.
                            </jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isApplicationInterrupted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jedná se o přerušenou žádost.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="isFastApplicationProcess4Mobile" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Jedná se o zrychlené schvalování žádosti (pužití jen pro mobilní aplikaci).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ApprovalProcessResultType">
        <xs:annotation>
            <xs:documentation>Stav proběhlého schvalovacího procesu</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="SUCCESS"/>
            <xs:enumeration value="FAIL_TO_FINISH"/>
            <xs:enumeration value="CLIENT_CANCEL"/>
            <xs:enumeration value="FI_CANCEL"/>
            <xs:enumeration value="EXPIRE"/>
            <xs:enumeration value="TERMINATED_REJECT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Alert">
        <xs:sequence>
            <xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>information about raised alert in according to product type.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="applicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>unique identifier of application due to was alert raised.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="alertCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>code of alert, that was raised on application.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="alertDetail" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>detail of alert, that was raised on application.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RiskInterestRate">
        <xs:annotation>
            <xs:documentation>Reprezentuje nápočet riskových sazeb ve scoringu žádosti.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="applicationId" type="xs:long"/>
            <xs:element name="productType" type="LoanProductType"/>
            <xs:element name="fixedInterestRate" type="xs:boolean" default="false">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Defaultně bude nastaven na 0, jeho změnu bude řídit Blaze, podle toho zda mu byla doručena manuálně nastavená RIR z OT tabulky do UW scoringu.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="ranges" type="InterestRatesRange" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InterestRatesRange">
        <xs:annotation>
            <xs:documentation>Představuje jednotlivá pásma riskových sazeb a k nim náležící úrokové sazby.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="loanAmountFrom" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Spodní hranice pásma, pro kterou je úroková saba nastavena (Můžeme očekávat od Blaze, že bude zohledňovat produktové minimum?).</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanAmountTo" type="xs:int">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Horní hranice pásma, pro které bude úroková sazba nastavena. Hordní hranice pásma bude vždy o 1 Kč nižší než spodní hranice následujícího pásma.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskInterestRateStandard" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Standardní úroková sazba.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
            <xs:element name="riskInterestRateBonus" type="xs:decimal">
                <xs:annotation>
                    <xs:appinfo>
                        <jxb:property>
                            <jxb:javadoc>Bonusová úroková saba.</jxb:javadoc>
                        </jxb:property>
                    </xs:appinfo>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="LoanProductType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CASH_LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
