<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           xmlns="http://airbank.cz/ams/ws/backoffice/completion"
           targetNamespace="http://airbank.cz/ams/ws/backoffice/completion"
           jxb:version="2.1" elementFormDefault="qualified">

    <xs:complexType name="SignatureTO">
        <xs:sequence>
            <xs:element name="date" type="xs:dateTime"/>
            <xs:element name="channel" type="SignChannelTO"/>
            <xs:element name="place" type="xs:string"/>
            <xs:element name="posId" type="xs:string" minOccurs="0"/>
            <xs:element name="signatureType" type="ContractSignTypeTO" maxOccurs="unbounded"/>
            <xs:element name="createdBy" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="SignChannelTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IB"/>
            <xs:enumeration value="POST"/>
            <xs:enumeration value="MESSENGER"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ContractSignTypeTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SIGN_INK"/>
            <xs:enumeration value="SIGNPAD"/>
            <xs:enumeration value="SCANNED_SIGN"/>
            <xs:enumeration value="BRANCH_OTP"/>
            <xs:enumeration value="BRANCH_PWD"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
