<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/codelists"
           targetNamespace="http://airbank.cz/ams/ws/application/codelists">

<xs:simpleType name="AnonymizationStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ANONYMIZED"/>
            <xs:enumeration value="HIDDEN"/>
            <xs:enumeration value="NONE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DistributionalChannel">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BRANCH"/>
            <xs:enumeration value="IB"/>
            <xs:enumeration value="MESSENGER"/>
            <xs:enumeration value="POST"/>
            <xs:enumeration value="ICC"/>
            <xs:enumeration value="SPB"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ApplicationApprovalStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVE"/>
            <xs:enumeration value="APPROVE_INTCONS"/>
            <xs:enumeration value="REJECT"/>
            <xs:enumeration value="CANCEL"/>
            <xs:enumeration value="VERIFY"/>
            <xs:enumeration value="MANUALVERIFY"/>
            <xs:enumeration value="FAIL_TO_FINISH"/>
            <xs:enumeration value="BRKINOTFOUND"/>
            <xs:enumeration value="MANUALPAIRING"/>
            <xs:enumeration value="FI_CANCEL"/>
            <xs:enumeration value="CLIENT_CANCEL"/>
            <xs:enumeration value="WAITING"/>
            <xs:enumeration value="VIP_PAUSED"/>
            <xs:enumeration value="UNDERWRITING"/>
            <xs:enumeration value="UW_WITH_VERIFICATION"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Tariff">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ALL-INCLUSIVE"/>
            <xs:enumeration value="PAY-AS-YOU-GO"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ApplicationStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="CANCELLED"/>
            <xs:enumeration value="LEAD"/>
            <xs:enumeration value="REJECTED"/>
            <xs:enumeration value="UNFINISHED"/>
            <xs:enumeration value="VERIFY"/>
            <xs:enumeration value="MANUALVERIFY"/>
            <xs:enumeration value="WAITING"/>
            <xs:enumeration value="MANUALPAIRING"/>
            <xs:enumeration value="VIP_PAUSED"/>
            <xs:enumeration value="WAIT_FOR_OFFER"/>
            <xs:enumeration value="ALTERNATIVE_OFFER"/>
            <xs:enumeration value="UNDERWRITING"/>
            <xs:enumeration value="COMPLETION"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="LoanModificationType">
        <xs:annotation>
            <xs:documentation>typ modifikace uveru</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DATE_CHNG">
                <xs:annotation>
                    <xs:documentation>zmena data splatky</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PARAM_CHNG">
                <xs:annotation>
                    <xs:documentation>zmena parametru uveru</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PAYMENT_HOLIDAY">
                <xs:annotation>
                    <xs:documentation>odklad splatek</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="MortgageLoanModificationType">
        <xs:annotation>
            <xs:documentation>typ modifikace uveru</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MRT_DATE_CHNG">
                <xs:annotation>
                    <xs:documentation>zmena data splatky</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MRT_INSTALLMENT_CHNG">
                <xs:annotation>
                    <xs:documentation>zmena parametru uveru</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MRT_OTB_PAYBACK">
                <xs:annotation>
                    <xs:documentation>cerpani z OTB</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
