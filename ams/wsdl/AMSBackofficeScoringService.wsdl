<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/backoffice/scoring"
                  targetNamespace="http://airbank.cz/ams/ws/backoffice/scoring">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/backoffice/scoring">
            <xs:include schemaLocation="AMSBackofficeScoringService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="OverwriteScoringResultRequest">
        <wsdl:part element="OverwriteScoringResultRequest" name="OverwriteScoringResultRequest"/>
    </wsdl:message>
    <wsdl:message name="OverwriteScoringResultResponse">
        <wsdl:part element="OverwriteScoringResultResponse" name="OverwriteScoringResultResponse"/>
    </wsdl:message>
    <wsdl:message name="OverwriteScoringResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="OverwriteScoringResultFault"/>
    </wsdl:message>

    <wsdl:portType name="BackofficeScoringPort">
        <wsdl:operation name="OverwriteScoringResult">
            <wsdl:documentation>
                Calculates consolidation params
            </wsdl:documentation>
            <wsdl:input message="OverwriteScoringResultRequest"/>
            <wsdl:output message="OverwriteScoringResultResponse"/>
            <wsdl:fault name="OverwriteScoringResultFault" message="OverwriteScoringResultFault"/>
        </wsdl:operation>
    </wsdl:portType>


    <wsdl:binding name="BackofficeScoringBinding" type="BackofficeScoringPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="OverwriteScoringResult">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="OverwriteScoringResultFault">
                <soap:fault name="OverwriteScoringResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="BackofficeScoringService">
        <wsdl:documentation>
            Offers scoring services for AMS GUI.
        </wsdl:documentation>
        <wsdl:port name="BackofficeScoringPort" binding="BackofficeScoringBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ws/ams/backoffice/scoring"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
