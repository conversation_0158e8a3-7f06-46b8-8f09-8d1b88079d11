<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:pipCommon="http://airbank.cz/ams/ws/application/common/insurance/pip"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/insurance/pip"
           targetNamespace="http://airbank.cz/ams/ws/application/insurance/pip">

    <xs:annotation>
        <xs:documentation>
            Types of PIP insurance application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/insurance/pip" schemaLocation="../xsd/PipInsuranceApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSettingsRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for insurance application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSettingsResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing settingsTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="calculationResult " type="pipCommon:CalculationResultTO" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSettingsRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for insurance application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="productType" type="pipCommon:PipProductType"/>
                        <xs:element name="productVersion" type="xs:string"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSettingsResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating settingsTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="idContractDocPdf" type="xs:string"/>
                        <xs:element name="idContractDocHtml" type="xs:string"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
</xs:schema>