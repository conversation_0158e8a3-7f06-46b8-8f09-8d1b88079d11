<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/administration"
                  targetNamespace="http://airbank.cz/ams/ws/administration">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/administration">
            <xs:include schemaLocation="AMSAdministrationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="GetListOfFailedProcessJobsRequest">
        <wsdl:part element="GetListOfFailedProcessJobsRequest" name="GetListOfFailedProcessJobsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetListOfFailedProcessJobsResponse">
        <wsdl:part element="GetListOfFailedProcessJobsResponse" name="GetListOfFailedProcessJobsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetListOfFailedProcessJobsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetListOfFailedProcessJobsFault"/>
    </wsdl:message>
    
    <wsdl:message name="GetProcessJobDetailRequest">
        <wsdl:part element="GetProcessJobDetailRequest" name="GetListOfFailedProcessJobsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetProcessJobDetailResponse">
        <wsdl:part element="GetProcessJobDetailResponse" name="GetProcessJobDetailResponse"/>
    </wsdl:message>
    <wsdl:message name="GetProcessJobDetailFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetProcessJobDetailFault"/>
    </wsdl:message>
    
    <wsdl:message name="ExecuteProcessJobRequest">
        <wsdl:part element="ExecuteProcessJobRequest" name="ExecuteProcessJobRequest"/>
    </wsdl:message>
    <wsdl:message name="ExecuteProcessJobResponse">
        <wsdl:part element="ExecuteProcessJobResponse" name="ExecuteProcessJobResponse"/>
    </wsdl:message>
    <wsdl:message name="ExecuteProcessJobFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ExecuteProcessJobFault"/>
    </wsdl:message>
    
    <wsdl:message name="DeleteProcessJobRequest">
        <wsdl:part element="DeleteProcessJobRequest" name="DeleteProcessJobRequest"/>
    </wsdl:message>
    <wsdl:message name="DeleteProcessJobResponse">
        <wsdl:part element="DeleteProcessJobResponse" name="DeleteProcessJobResponse"/>
    </wsdl:message>
    <wsdl:message name="DeleteProcessJobFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="DeleteProcessJobFault"/>
    </wsdl:message>

    <wsdl:message name="InitForceResumeProcessInstanceRequest">
        <wsdl:part element="InitForceResumeProcessInstanceRequest" name="InitForceResumeProcessInstanceRequest"/>
    </wsdl:message>
    <wsdl:message name="InitForceResumeProcessInstanceResponse">
        <wsdl:part element="InitForceResumeProcessInstanceResponse" name="InitForceResumeProcessInstanceResponse"/>
    </wsdl:message>
    <wsdl:message name="InitForceResumeProcessInstanceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitForceResumeProcessInstanceFault"/>
    </wsdl:message>

    <wsdl:message name="ForceResumeProcessInstanceRequest">
        <wsdl:part element="ForceResumeProcessInstanceRequest" name="ForceResumeProcessInstanceRequest"/>
    </wsdl:message>
    <wsdl:message name="ForceResumeProcessInstanceResponse">
        <wsdl:part element="ForceResumeProcessInstanceResponse" name="ForceResumeProcessInstanceResponse"/>
    </wsdl:message>
    <wsdl:message name="ForceResumeProcessInstanceFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ForceResumeProcessInstanceFault"/>
    </wsdl:message>

    <wsdl:portType name="Administration">

        <wsdl:operation name="GetListOfFailedProcessJobs">
            <wsdl:documentation>
                Return list of failed process jobs
            </wsdl:documentation>
            <wsdl:input message="GetListOfFailedProcessJobsRequest"/>
            <wsdl:output message="GetListOfFailedProcessJobsResponse"/>
            <wsdl:fault name="GetListOfFailedProcessJobsFault" message="GetListOfFailedProcessJobsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetProcessJobDetail">
            <wsdl:documentation>
                Return detail of one specific process job
            </wsdl:documentation>
            <wsdl:input message="GetProcessJobDetailRequest"/>
            <wsdl:output message="GetProcessJobDetailResponse"/>
            <wsdl:fault name="GetProcessJobDetailFault" message="GetProcessJobDetailFault"/>
        </wsdl:operation>

        <wsdl:operation name="ExecuteProcessJob">
            <wsdl:documentation>
                Execute one specific failed process job
            </wsdl:documentation>
            <wsdl:input message="ExecuteProcessJobRequest"/>
            <wsdl:output message="ExecuteProcessJobResponse"/>
            <wsdl:fault name="ExecuteProcessJobFault" message="ExecuteProcessJobFault"/>
        </wsdl:operation>

        <wsdl:operation name="DeleteProcessJob">
            <wsdl:documentation>
                Delete on specific failed process job
            </wsdl:documentation>
            <wsdl:input message="DeleteProcessJobRequest"/>
            <wsdl:output message="DeleteProcessJobResponse"/>
            <wsdl:fault name="DeleteProcessJobFault" message="DeleteProcessJobFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="InitForceResumeProcessInstance">
            <wsdl:documentation>
                Initialize service to resume process instance
            </wsdl:documentation>
            <wsdl:input message="InitForceResumeProcessInstanceRequest"/>
            <wsdl:output message="InitForceResumeProcessInstanceResponse"/>
            <wsdl:fault name="InitForceResumeProcessInstanceFault" message="InitForceResumeProcessInstanceFault"/>
        </wsdl:operation>

        <wsdl:operation name="ForceResumeProcessInstance">
            <wsdl:documentation>
                Resume process instance in its flow
            </wsdl:documentation>
            <wsdl:input message="ForceResumeProcessInstanceRequest"/>
            <wsdl:output message="ForceResumeProcessInstanceResponse"/>
            <wsdl:fault name="ForceResumeProcessInstanceFault" message="ForceResumeProcessInstanceFault"/>
        </wsdl:operation>

    </wsdl:portType>

    <wsdl:binding name="AdministrationBinding" type="Administration">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="GetListOfFailedProcessJobs">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetListOfFailedProcessJobsFault">
                <soap:fault name="GetListOfFailedProcessJobsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetProcessJobDetail">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetProcessJobDetailFault">
                <soap:fault name="GetProcessJobDetailFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ExecuteProcessJob">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ExecuteProcessJobFault">
                <soap:fault name="ExecuteProcessJobFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="DeleteProcessJob">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="DeleteProcessJobFault">
                <soap:fault name="DeleteProcessJobFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitForceResumeProcessInstance">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitForceResumeProcessInstanceFault">
                <soap:fault name="InitForceResumeProcessInstanceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ForceResumeProcessInstance">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ForceResumeProcessInstanceFault">
                <soap:fault name="ForceResumeProcessInstanceFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="AdministrationService">
        <wsdl:documentation>
            Administration services for AMS GUI.
        </wsdl:documentation>
        <wsdl:port name="AdministrationPort" binding="AdministrationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ws/ams/administration"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
