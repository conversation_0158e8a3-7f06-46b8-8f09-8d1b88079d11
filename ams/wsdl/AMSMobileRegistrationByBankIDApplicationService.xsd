<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:appConstant="http://airbank.cz/ams/ws/application/constant"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/registrationbybankid"
           targetNamespace="http://airbank.cz/ams/ws/application/registrationbybankid">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/constant" schemaLocation="../xsd/ApplicationConstant.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSourceBankSelectionRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSourceBankSelectionResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="banksList" type="appCommon:BankInfo" maxOccurs="unbounded"/>
                        <xs:element name="stateId" type="xs:string"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSourceBankSelectionRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="clientDataCollectionResult" type="appConstant:ClientDataCollectionResult"/>
                        <xs:element name="stateId" type="xs:string"/>
                        <xs:element name="bankCode" type="xs:string"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSourceBankSelectionResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPersonalDataApprovalRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="InitPersonalDataApprovalResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="firstName" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer first name</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="lastName" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer last name</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="dateOfBirth" type="xs:date">
                            <xs:annotation>
                                <xs:documentation>customer date of birth</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="email" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer email</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="phoneNumber" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer phone number</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="address" type="appCommon:AddressTO"/>
                        <xs:element name="pep" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>political exposed person</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="vocative" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer name in vocative</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="salutation" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer salutation</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePersonalDataApprovalRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="email" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer email</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="phoneNumber" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>customer phone number</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="deviceWirelessName" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>device name</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="pep" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>political exposed person</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="address" type="appCommon:AddressTO">
                            <xs:annotation>
                                <xs:documentation>address</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="overrideAddressCheck" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>whether the address should be checked</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="overrideEmailCheck" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>whether the email should be checked</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="emailDomainConfirmed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether user confirmed unusual email domain warning or not.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="smeRequest" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>request sme</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element> 
    
    <xs:element name="UpdatePersonalDataApprovalResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPairingRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of pairing</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPairingResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing pairing.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="rotpPart1" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>Pairing data</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="nextSMSAfter" type="xs:dateTime">
                            <xs:annotation>
                                <xs:documentation>The earliest possible time to send another SMS with part of ROTP</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="phoneNumber" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>Phone number sms was sent</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePairingRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update pairing (merely request to finish)</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePairingResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response of update pairing (merely indicates that user was created)</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResendSMSRequest">
        <xs:annotation>
            <xs:documentation>Request to resend SMS with part of ROTP</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResendSMSResponse">
        <xs:annotation>
            <xs:documentation>Response carries next possible SMS resend or error</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="nextSMSAfter" type="xs:dateTime" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>The earliest possible time to send another SMS with part of ROTP</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
</xs:schema>
