<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:appConstant="http://airbank.cz/ams/ws/application/constant"
           xmlns="http://airbank.cz/ams/ws/application/debitcard"
           targetNamespace="http://airbank.cz/ams/ws/application/debitcard">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/constant" schemaLocation="../xsd/ApplicationConstant.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="cardBusinessCategory" type="appConstant:CardBusinessCategoryTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Card business category</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedCardHolder" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected card holder.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="requiredCardDevice" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Indicates, if we create physical card through gpe</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSelectAccountAndHolderRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSelectAccountAndHolderResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="accounts" type="appCommon:AccountTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of existing client accounts.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardHoldersAndDisponents" type="appCommon:PersonTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of existing card holders and disponents with active application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedCardHolder" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected card holder.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="isAffidavit" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is selected affidavit for new card holder.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="maxVirtualizedForContract" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Max virtualized cards for contract.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSelectAccountAndHolderRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedCardHolder" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected card holder.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSelectAccountAndHolderResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitCardParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitCardParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractValidatedAttributeInitResponse">
                    <xs:sequence>
                        <xs:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Default mailing address where to send card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardDesignOptions" type="appCommon:CardDesignTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of possible designs.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardParams" type="appCommon:DebitCardParamsTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Debit card params</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardHolderIsOwner" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is card holder same client as account owner?</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardHolderIsDisponent" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is card holder disponent for the account?</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="showWithdrawalsPackageOption" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether an option for withdrawal fee package should be visible.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="withdrawalsPackageSelected" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a withdrawal fee package was selected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="feePackagesConfirmed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether user confirmed activation of the fee packages.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="feePackageParams" type="appCommon:FeePackageParamsTO"  minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Fee packages parameters</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accounts" type="appCommon:AccountTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of existing client accounts. Only for MA.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account. Only for MA.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="maInstalled" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is exists any active MA device?</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="maxVirtualizedForContract" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Max virtualized cards for contract.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateCardParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="cardParams" type="appCommon:DebitCardParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Debit card params</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="withdrawalsPackageSelected" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether an unlimited withdrawal package was selected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="feePackagesConfirmed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether user confirmed activation of the fee packages.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateCardParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNewHolderBasicParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNewHolderBasicParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="clientBasicData" type="appCommon:ClientBasicDataTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client basic data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="alternativePhone" type="appCommon:PhoneNumberTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Clients alternative phone number</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNewHolderBasicParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="clientBasicData" type="appCommon:ClientBasicDataTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client basic data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="alternativePhone" type="appCommon:PhoneNumberTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Clients alternative phone number</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNewHolderBasicParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNewHolderPersonalParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNewHolderPersonalParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="clientOtherData" type="appCommon:ClientOtherDataTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Rest (in respect to client basic data) of main client data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documentTypeList" type="appCommon:DocumentTypeListTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of possible documents for proof of identity for particular group of countries.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="clientAdditionalData" type="appCommon:ClientAdditionalDataTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Additional client data required by card holder application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="identificationEnabled" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether identification process is enabled or not.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNewHolderPersonalParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="clientOtherData" type="appCommon:ClientOtherDataTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Rest (in respect to client basic data) of main client data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="clientAdditionalData" type="appCommon:ClientAdditionalDataTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Additional client data required by card holder application.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNewHolderPersonalParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNewHolderContactVerificationRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNewHolderContactVerificationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="haveDuplicateEmail" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>True when client's primary email is marked as duplicate.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="haveDuplicatePhone" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>True when client's primary phone is marked as duplicate.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="clientContactData" type="appCommon:ClientContactDataTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client contact information.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNewHolderContactVerificationRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="clientContactData" type="appCommon:ClientContactDataTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client contact information.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNewHolderContactVerificationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAlreadyHolderRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAlreadyHolderResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>
