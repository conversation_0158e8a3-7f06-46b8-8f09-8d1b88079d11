<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/loanChangePaymentHoliday"
                  targetNamespace="http://airbank.cz/ams/ws/application/loanChangePaymentHoliday">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/loanChangePaymentHoliday">
            <xs:include schemaLocation="AMSLoanChangePaymentHolidayApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <!-- Start application -->
    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- ChangePaymentHoliday Task -->
    <wsdl:message name="InitChangePaymentHolidayRequest">
        <wsdl:part element="InitChangePaymentHolidayRequest" name="InitChangePaymentHolidayRequest"/>
    </wsdl:message>
    <wsdl:message name="InitChangePaymentHolidayResponse">
        <wsdl:part element="InitChangePaymentHolidayResponse" name="InitChangePaymentHolidayResponse"/>
    </wsdl:message>
    <wsdl:message name="InitChangePaymentHolidayFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitChangePaymentHolidayFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateChangePaymentHolidayRequest">
        <wsdl:part element="UpdateChangePaymentHolidayRequest" name="UpdateChangePaymentHolidayRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateChangePaymentHolidayResponse">
        <wsdl:part element="UpdateChangePaymentHolidayResponse" name="UpdateChangePaymentHolidayResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateChangePaymentHolidayFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateChangePaymentHolidayFault"/>
    </wsdl:message>

    <!-- Summary Task -->
    <wsdl:message name="InitSummaryRequest">
        <wsdl:part element="InitSummaryRequest" name="InitSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryResponse">
        <wsdl:part element="InitSummaryResponse" name="InitSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSummaryRequest">
        <wsdl:part element="UpdateSummaryRequest" name="UpdateSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryResponse">
        <wsdl:part element="UpdateSummaryResponse" name="UpdateSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSummaryFault"/>
    </wsdl:message>

    <!-- Query for bonus steps -->
    <wsdl:message name="GetBonusStepsRequest">
        <wsdl:part element="GetBonusStepsRequest" name="GetBonusStepsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetBonusStepsResponse">
        <wsdl:part element="GetBonusStepsResponse" name="GetBonusStepsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetBonusStepsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetBonusStepsFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <!-- Application cancel request -->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <wsdl:portType name="LoanChangePaymentHolidayApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application to change loan installment date. Uses cuid and idProfile from tracking context to identify customer and
                profile.
                Mandatory parameters:
                - loanId

                Validation errors:

                Generated business faults:
                - Application.Reject.CantApplyForLoanProduct - User can't apply for loan product!
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous loan application in the VERIFY status
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitChangePaymentHoliday">
            <wsdl:documentation>Operation used to initialize the ChangePaymentHolidayTask.</wsdl:documentation>
            <wsdl:input message="InitChangePaymentHolidayRequest"/>
            <wsdl:output message="InitChangePaymentHolidayResponse"/>
            <wsdl:fault name="InitChangePaymentHolidayFault" message="InitChangePaymentHolidayFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateChangePaymentHoliday">
            <wsdl:documentation>Operation used to update data for the ChangePaymentHolidayTask.

                For all transitions except for back, following attributes are required:
                - installmentDay

                Generated business faults:
                - INSTALLMENT_DAY_NOT_UPDATED - client is trying to change loan date to the same day which is already set
            </wsdl:documentation>
            <wsdl:input message="UpdateChangePaymentHolidayRequest"/>
            <wsdl:output message="UpdateChangePaymentHolidayResponse"/>
            <wsdl:fault name="UpdateChangePaymentHolidayFault" message="UpdateChangePaymentHolidayFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:documentation>Operation used to initialize the SummaryTask.</wsdl:documentation>
            <wsdl:input message="InitSummaryRequest"/>
            <wsdl:output message="InitSummaryResponse"/>
            <wsdl:fault name="InitSummaryFault" message="InitSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSummary">
            <wsdl:documentation>Operation used to update data for the SummaryTask.

                For all transitions except for back, following attributes are required:
                none

                Generated business faults:
            </wsdl:documentation>
            <wsdl:input message="UpdateSummaryRequest"/>
            <wsdl:output message="UpdateSummaryResponse"/>
            <wsdl:fault name="UpdateSummaryFault" message="UpdateSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetBonusSteps">
            <wsdl:documentation>Returns bonus steps for application. It's resource expensive operation,
                which is done just occasionally so there is no reason to call it during each init and
                recalculate call.
            </wsdl:documentation>
            <wsdl:input message="GetBonusStepsRequest"/>
            <wsdl:output message="GetBonusStepsResponse"/>
            <wsdl:fault name="GetBonusStepsFault" message="GetBonusStepsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="LoanChangePaymentHolidayApplicationBinding" type="LoanChangePaymentHolidayApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitChangePaymentHoliday">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitChangePaymentHolidayFault">
                <soap:fault name="InitChangePaymentHolidayFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateChangePaymentHoliday">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateChangePaymentHolidayFault">
                <soap:fault name="UpdateChangePaymentHolidayFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSummaryFault">
                <soap:fault name="InitSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSummaryFault">
                <soap:fault name="UpdateSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetBonusSteps">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetBonusStepsFault">
                <soap:fault name="GetBonusStepsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="LoanChangePaymentHolidayApplicationService">
        <wsdl:documentation>Service is providing operations related to existing customer loan change date application.

            The application has the following task IDs:
            - ChangePaymentHolidayTask - page where you can change loan pay day
            - summaryTask - summary page for this application

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGTH, INVALID_EXACT_LENGTH - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
            - DATE_RANGE_VALIDATOR - year is not in range 1900 - 2999
        </wsdl:documentation>
        <wsdl:port name="LoanChangePaymentHolidayApplicationPort" binding="LoanChangePaymentHolidayApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/loanChangePaymentHoliday"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>