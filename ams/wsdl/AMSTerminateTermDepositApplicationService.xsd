<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/terminatetermdeposit"
           targetNamespace="http://airbank.cz/ams/ws/application/terminatetermdeposit">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="termDepositNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>TermDeposit identification.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitTerminateTermDepositRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitTerminateTermDepositResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="accounts" type="appCommon:AccountExtTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of existing client accounts.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccountId" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccountNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Bank account number for payment of the amount.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="depositAmount" type="appCommon:MonetaryAmountTO">
                            <xs:annotation>
                                <xs:documentation>Type of the deposit product.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateTerminateTermDepositRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="selectedAccountId" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedAccountNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Bank account number with bank code for payment of the principal.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="authId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Authorization id</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateTerminateTermDepositResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="TerminateFixedDepositRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Terminate fixed deposit by backend.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="fixedDepositNumber" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Number of fixed deposit product.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="terminationReason" type="TerminationReasonTypeTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Reason of the termination.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Note of the termination.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="TerminateFixedDepositResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="TerminationReasonTypeTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TERMINATION_BY_BANK"/>
            <xs:enumeration value="WITHDRAWAL_BY_BANK"/>
            <xs:enumeration value="TERMINATION_BY_CLIENT"/>
            <xs:enumeration value="WITHDRAWAL_BY_CLIENT"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>