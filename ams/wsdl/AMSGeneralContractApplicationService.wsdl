<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/generalcontract"
                  targetNamespace="http://airbank.cz/ams/ws/application/generalcontract">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/generalcontract">
            <xs:include schemaLocation="AMSGeneralContractApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Application identity verification method step init-->
    <wsdl:message name="InitIdentityVerificationMethodRequest">
        <wsdl:part element="InitIdentityVerificationMethodRequest" name="InitIdentityVerificationMethodRequest"/>
    </wsdl:message>
    <wsdl:message name="InitIdentityVerificationMethodResponse">
        <wsdl:part element="InitIdentityVerificationMethodResponse" name="InitIdentityVerificationMethodResponse"/>
    </wsdl:message>
    <wsdl:message name="InitIdentityVerificationMethodFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitIdentityVerificationMethodFault"/>
    </wsdl:message>

    <!-- Application identity verification method step update-->
    <wsdl:message name="UpdateIdentityVerificationMethodRequest">
        <wsdl:part element="UpdateIdentityVerificationMethodRequest" name="UpdateIdentityVerificationMethodRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateIdentityVerificationMethodResponse">
        <wsdl:part element="UpdateIdentityVerificationMethodResponse" name="UpdateIdentityVerificationMethodResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateIdentityVerificationMethodFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateIdentityVerificationMethodFault"/>
    </wsdl:message>
    
    <!-- Application basic step init-->
    <wsdl:message name="InitBasicParametersRequest">
        <wsdl:part element="InitBasicParametersRequest" name="InitBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBasicParametersResponse">
        <wsdl:part element="InitBasicParametersResponse" name="InitBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBasicParametersFault"/>
    </wsdl:message>

    <!-- Application basic step update-->
    <wsdl:message name="UpdateBasicParametersRequest">
        <wsdl:part element="UpdateBasicParametersRequest" name="UpdateBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBasicParametersResponse">
        <wsdl:part element="UpdateBasicParametersResponse" name="UpdateBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBasicParametersFault"/>
    </wsdl:message>

    <!-- Application address step init-->
    <wsdl:message name="InitAddressRequest">
        <wsdl:part element="InitAddressRequest" name="InitAddressRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAddressResponse">
        <wsdl:part element="InitAddressResponse" name="InitAddressResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAddressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAddressFault"/>
    </wsdl:message>

    <!-- Application address step update-->
    <wsdl:message name="UpdateAddressRequest">
        <wsdl:part element="UpdateAddressRequest" name="UpdateAddressRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateAddressResponse">
        <wsdl:part element="UpdateAddressResponse" name="UpdateAddressResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateAddressFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateAddressFault"/>
    </wsdl:message>

    <!-- Application source bank selection step init-->
    <wsdl:message name="InitSourceBankSelectionRequest">
        <wsdl:part element="InitSourceBankSelectionRequest" name="InitSourceBankSelectionRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSourceBankSelectionResponse">
        <wsdl:part element="InitSourceBankSelectionResponse" name="InitSourceBankSelectionResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSourceBankSelectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSourceBankSelectionFault"/>
    </wsdl:message>

    <!-- Application source bank selection step update-->
    <wsdl:message name="UpdateSourceBankSelectionRequest">
        <wsdl:part element="UpdateSourceBankSelectionRequest" name="UpdateSourceBankSelectionRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSourceBankSelectionResponse">
        <wsdl:part element="UpdateSourceBankSelectionResponse" name="UpdateSourceBankSelectionResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSourceBankSelectionFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSourceBankSelectionFault"/>
    </wsdl:message>

    <!-- Application personal data approval step init-->
    <wsdl:message name="InitPersonalDataApprovalRequest">
        <wsdl:part element="InitPersonalDataApprovalRequest" name="InitPersonalDataApprovalRequest"/>
    </wsdl:message>
    <wsdl:message name="InitPersonalDataApprovalResponse">
        <wsdl:part element="InitPersonalDataApprovalResponse" name="InitPersonalDataApprovalResponse"/>
    </wsdl:message>
    <wsdl:message name="InitPersonalDataApprovalFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitPersonalDataApprovalFault"/>
    </wsdl:message>

    <!-- Application personal data approval step update-->
    <wsdl:message name="UpdatePersonalDataApprovalRequest">
        <wsdl:part element="UpdatePersonalDataApprovalRequest" name="UpdatePersonalDataApprovalRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdatePersonalDataApprovalResponse">
        <wsdl:part element="UpdatePersonalDataApprovalResponse" name="UpdatePersonalDataApprovalResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdatePersonalDataApprovalFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdatePersonalDataApprovalFault"/>
    </wsdl:message>
    
    <!-- Application first scoring approved page init-->
    <wsdl:message name="InitFirstScoringApprovedRequest">
        <wsdl:part element="InitFirstScoringApprovedRequest" name="InitFirstScoringApprovedRequest"/>
    </wsdl:message>
    <wsdl:message name="InitFirstScoringApprovedResponse">
        <wsdl:part element="InitFirstScoringApprovedResponse" name="InitFirstScoringApprovedResponse"/>
    </wsdl:message>
    <wsdl:message name="InitFirstScoringApprovedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitFirstScoringApprovedFault"/>
    </wsdl:message>

    <!-- Application first scoring approved page update-->
    <wsdl:message name="UpdateFirstScoringApprovedRequest">
        <wsdl:part element="UpdateFirstScoringApprovedRequest" name="UpdateFirstScoringApprovedRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateFirstScoringApprovedResponse">
        <wsdl:part element="UpdateFirstScoringApprovedResponse" name="UpdateFirstScoringApprovedResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateFirstScoringApprovedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateFirstScoringApprovedFault"/>
    </wsdl:message>


    <!-- Deposit products page init-->
    <wsdl:message name="InitDepositProductsRequest">
        <wsdl:part element="InitDepositProductsRequest" name="InitDepositProductsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDepositProductsResponse">
        <wsdl:part element="InitDepositProductsResponse" name="InitDepositProductsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDepositProductsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDepositProductsFault"/>
    </wsdl:message>

    <!-- Application deposit products page update-->
    <wsdl:message name="UpdateDepositProductsRequest">
        <wsdl:part element="UpdateDepositProductsRequest" name="UpdateDepositProductsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDepositProductsResponse">
        <wsdl:part element="UpdateDepositProductsResponse" name="UpdateDepositProductsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDepositProductsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDepositProductsFault"/>
    </wsdl:message>

    <!-- Application documents sign method selection step init-->
    <wsdl:message name="InitSignMethodRequest">
        <wsdl:part element="InitSignMethodRequest" name="InitSignMethodRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignMethodResponse">
        <wsdl:part element="InitSignMethodResponse" name="InitSignMethodResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignMethodFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignMethodFault"/>
    </wsdl:message>

    <!-- Application  documents sign method selection step update-->
    <wsdl:message name="UpdateSignMethodRequest">
        <wsdl:part element="UpdateSignMethodRequest" name="UpdateSignMethodRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignMethodResponse">
        <wsdl:part element="UpdateSignMethodResponse" name="UpdateSignMethodResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignMethodFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignMethodFault"/>
    </wsdl:message>

    <!-- Application relationship with the bank selection step init-->
    <wsdl:message name="InitLegalMandatoryDataRequest">
        <wsdl:part element="InitLegalMandatoryDataRequest" name="InitLegalMandatoryDataRequest"/>
    </wsdl:message>
    <wsdl:message name="InitLegalMandatoryDataResponse">
        <wsdl:part element="InitLegalMandatoryDataResponse" name="InitLegalMandatoryDataResponse"/>
    </wsdl:message>
    <wsdl:message name="InitLegalMandatoryDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitLegalMandatoryDataFault"/>
    </wsdl:message>

    <!-- Application relationship with the bank selection step update-->
    <wsdl:message name="UpdateLegalMandatoryDataRequest">
        <wsdl:part element="UpdateLegalMandatoryDataRequest" name="UpdateLegalMandatoryDataRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateLegalMandatoryDataResponse">
        <wsdl:part element="UpdateLegalMandatoryDataResponse" name="UpdateLegalMandatoryDataResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateLegalMandatoryDataFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateLegalMandatoryDataFault"/>
    </wsdl:message>

    <!-- Application username specification step init-->
    <wsdl:message name="InitUsernameRequest">
        <wsdl:part element="InitUsernameRequest" name="InitUsernameRequest"/>
    </wsdl:message>
    <wsdl:message name="InitUsernameResponse">
        <wsdl:part element="InitUsernameResponse" name="InitUsernameResponse"/>
    </wsdl:message>
    <wsdl:message name="InitUsernameFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitUsernameFault"/>
    </wsdl:message>

    <!-- Application username specification selection step update-->
    <wsdl:message name="UpdateUsernameRequest">
        <wsdl:part element="UpdateUsernameRequest" name="UpdateUsernameRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateUsernameResponse">
        <wsdl:part element="UpdateUsernameResponse" name="UpdateUsernameResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateUsernameFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateUsernameFault"/>
    </wsdl:message>

    <!-- Entering password step -->
    <wsdl:message name="InitPasswordRequest">
        <wsdl:part element="InitPasswordRequest" name="InitPasswordRequest"/>
    </wsdl:message>
    <wsdl:message name="InitPasswordResponse">
        <wsdl:part element="InitPasswordResponse" name="InitPasswordResponse"/>
    </wsdl:message>
    <wsdl:message name="InitPasswordFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitPasswordFault"/>
    </wsdl:message>

    <wsdl:message name="UpdatePasswordRequest">
        <wsdl:part element="UpdatePasswordRequest" name="UpdatePasswordRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdatePasswordResponse">
        <wsdl:part element="UpdatePasswordResponse" name="UpdatePasswordResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdatePasswordFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdatePasswordFault"/>
    </wsdl:message>

    <!-- Application finalization step init. This step des not have update-->
    <wsdl:message name="InitFinalizationRequest">
        <wsdl:part element="InitFinalizationRequest" name="InitFinalizationRequest"/>
    </wsdl:message>
    <wsdl:message name="InitFinalizationResponse">
        <wsdl:part element="InitFinalizationResponse" name="InitFinalizationResponse"/>
    </wsdl:message>
    <wsdl:message name="InitFinalizationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitFinalizationFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Request to send general contract info notification to client-->
    <wsdl:message name="SendGeneralContractInfoRequest">
        <wsdl:part element="SendGeneralContractInfoRequest" name="SendGeneralContractInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="SendGeneralContractInfoResponse">
        <wsdl:part element="SendGeneralContractInfoResponse" name="SendGeneralContractInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="SendGeneralContractInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="SendGeneralContractInfoFault"/>
    </wsdl:message>

    <!-- Request to resume general contract application-->
    <wsdl:message name="ResumeApplicationRequest">
        <wsdl:part element="ResumeApplicationRequest" name="ResumeApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationResponse">
        <wsdl:part element="ResumeApplicationResponse" name="ResumeApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ResumeApplicationFault"/>
    </wsdl:message>

    <!-- Request to cancel unfinished general contract application-->
    <wsdl:message name="CancelUnfinishedApplicationRequest">
        <wsdl:part element="CancelUnfinishedApplicationRequest" name="CancelUnfinishedApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelUnfinishedApplicationResponse">
        <wsdl:part element="CancelUnfinishedApplicationResponse" name="CancelUnfinishedApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelUnfinishedApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelUnfinishedApplicationFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <!-- Returns applicant info -->
    <wsdl:message name="GetApplicantInfoRequest">
        <wsdl:part element="GetApplicantInfoRequest" name="GetApplicantInfoRequest"/>
    </wsdl:message>
    <wsdl:message name="GetApplicantInfoResponse">
        <wsdl:part element="GetApplicantInfoResponse" name="GetApplicantInfoResponse"/>
    </wsdl:message>
    <wsdl:message name="GetApplicantInfoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetApplicantInfoFault"/>
    </wsdl:message>

    <!-- Terminate general contract -->
    <wsdl:message name="TerminateGeneralContractRequest">
        <wsdl:part element="TerminateGeneralContractRequest" name="TerminateGeneralContractRequest"/>
    </wsdl:message>
    <wsdl:message name="TerminateGeneralContractResponse">
        <wsdl:part element="TerminateGeneralContractResponse" name="TerminateGeneralContractResponse"/>
    </wsdl:message>
    <wsdl:message name="TerminateGeneralContractFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="TerminateGeneralContractFault"/>
    </wsdl:message>

    <wsdl:portType name="GeneralContractApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for general contract.

                Generated business faults as a result of invalid lead basket content:
                - Application.Account.CountLimitReached - The customer wants more accounts than it is allowed
                - Application.SavingAccount.AccountInCurrencyNotAvailable - The customer wants more saving accounts in a currency than it is allowed
                - IB_CON_DATE
                -IB_CON_BRKI
                - IB_CON_AMOUNT_MAX
                - IB_CON_AMOUNT_MIN
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitIdentityVerificationMethod">
            <wsdl:documentation>Operation used to initialize the identityVerificationMethodTask.</wsdl:documentation>
            <wsdl:input message="InitIdentityVerificationMethodRequest"/>
            <wsdl:output message="InitIdentityVerificationMethodResponse"/>
            <wsdl:fault name="InitIdentityVerificationMethodFault" message="InitIdentityVerificationMethodFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateIdentityVerificationMethod">
            <wsdl:documentation>Operation used to update data for the identityVerificationMethodTask.

                For all transitions following attributes are required:
                - identityVerificationMethod
                - acceptPersonalDataStoring

                Generated business faults:
                - Application.GeneralContract.IccBranchUnsupportedIdentityVerificationMethod - unsupported identity verification method in ICC or branch mode
            </wsdl:documentation>
            <wsdl:input message="UpdateIdentityVerificationMethodRequest"/>
            <wsdl:output message="UpdateIdentityVerificationMethodResponse"/>
            <wsdl:fault name="UpdateIdentityVerificationMethodFault" message="UpdateIdentityVerificationMethodFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="InitBasicParameters">
            <wsdl:documentation>Operation used to initialize the basicParametersTask.</wsdl:documentation>
            <wsdl:input message="InitBasicParametersRequest"/>
            <wsdl:output message="InitBasicParametersResponse"/>
            <wsdl:fault name="InitBasicParametersFault" message="InitBasicParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateBasicParameters">
            <wsdl:documentation>Operation used to update data for the basicParametersTask.

                For all transitions following attributes are required:
                - salutation
                - firstName
                - lastName
                - phone
                - email
                - acceptPersonalDataStoring

                Generated business faults:
                - Application.Contract.RegistrationExists - We have contract application registered in our DB for the client basic data provided.

                Generated validation faults:
                - Application.EmailDiacriticsNotConfirmed - The email address contains diacritics and it is not confirmed by the user
                - Application.FirstNameIsNotUsual - The first name provided has not been found within usual names and it is not confirmed by the client
                - Application.LastNameIsNotUsual - The last name provided has not been found within usual names and it is not confirmed by the client
                - validation errors resulting from CIF validations of firstName, lastName, phone and email
            </wsdl:documentation>
            <wsdl:input message="UpdateBasicParametersRequest"/>
            <wsdl:output message="UpdateBasicParametersResponse"/>
            <wsdl:fault name="UpdateBasicParametersFault" message="UpdateBasicParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAddress">
            <wsdl:documentation>Operation used to initialize the addressTask.</wsdl:documentation>
            <wsdl:input message="InitAddressRequest"/>
            <wsdl:output message="InitAddressResponse"/>
            <wsdl:fault name="InitAddressFault" message="InitAddressFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateAddress">
            <wsdl:documentation>Operation used to update data for the addressTask.

                For all transitions except for back, following attributes are required:
                - citizenship
                - selectedDocumentType
                - documentNumber
                - personalId or gender (one of these two must be filled)
                - dateOfBirth
                - placeOfBirth
                - permanentAddress
                - mailingAddressDifferent
                - mailingAddress in case mailingAddressDifferent is true

                Generated business faults:
                - Application.Contract.RegistrationExists - We have contract application registered in our DB for the client basic data provided.
                - Application.Contract.SalutationPersonalIdConflict - Gender information from salutation and from personal id number do not conform
                - Application.Contract.SalutationGenderConflict - Gender information from salutation and selected gender do not conform
                - Application.Contract.ActiveContractExists - Active contract for the customer already exists

                Generated validation faults:
                - Application.DuplicatePhone - The phone provided is already used by some other active user
                - Application.DuplicateEmail - The email provided is already used by some other active user
                - Application.EmailDiacriticsNotConfirmed - The email address contains diacritics and it is not confirmed by the user
                - Application.AddressNotInCz - Specified contact address is not in CZ
                - Application.AddressNotConfirmed - The other address does not conform to registers and it is not confirmed by the user.
                - Application.ApplicantIsNotOldEnough - Applicant is not old enough
                - validation codes coming from validations in CIF
            </wsdl:documentation>
            <wsdl:input message="UpdateAddressRequest"/>
            <wsdl:output message="UpdateAddressResponse"/>
            <wsdl:fault name="UpdateAddressFault" message="UpdateAddressFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSourceBankSelection">
            <wsdl:documentation>Operation used to initialize the sourceBankSelectionTask.</wsdl:documentation>
            <wsdl:input message="InitSourceBankSelectionRequest"/>
            <wsdl:output message="InitSourceBankSelectionResponse"/>
            <wsdl:fault name="InitSourceBankSelectionFault" message="InitSourceBankSelectionFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateSourceBankSelection">
            <wsdl:documentation>Operation used to update the sourceBankSelectionTask.</wsdl:documentation>
            <wsdl:input message="UpdateSourceBankSelectionRequest"/>
            <wsdl:output message="UpdateSourceBankSelectionResponse"/>
            <wsdl:fault name="UpdateSourceBankSelectionFault" message="UpdateSourceBankSelectionFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitPersonalDataApproval">
            <wsdl:documentation>Operation used to initialize the personalDataApprovalTask.</wsdl:documentation>
            <wsdl:input message="InitPersonalDataApprovalRequest"/>
            <wsdl:output message="InitPersonalDataApprovalResponse"/>
            <wsdl:fault name="InitPersonalDataApprovalFault" message="InitPersonalDataApprovalFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdatePersonalDataApproval">
            <wsdl:documentation>Operation used to update the personalDataApprovalTask.</wsdl:documentation>
            <wsdl:input message="UpdatePersonalDataApprovalRequest"/>
            <wsdl:output message="UpdatePersonalDataApprovalResponse"/>
            <wsdl:fault name="UpdatePersonalDataApprovalFault" message="UpdatePersonalDataApprovalFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="InitFirstScoringApproved">
            <wsdl:documentation>Operation used to initialize the firstScoringApprovedTask.</wsdl:documentation>
            <wsdl:input message="InitFirstScoringApprovedRequest"/>
            <wsdl:output message="InitFirstScoringApprovedResponse"/>
            <wsdl:fault name="InitFirstScoringApprovedFault" message="InitFirstScoringApprovedFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateFirstScoringApproved">
            <wsdl:documentation>Operation used to update data for the firstScoringApprovedTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateFirstScoringApprovedRequest"/>
            <wsdl:output message="UpdateFirstScoringApprovedResponse"/>
            <wsdl:fault name="UpdateFirstScoringApprovedFault" message="UpdateFirstScoringApprovedFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDepositProducts">
            <wsdl:documentation>Operation used to initialize the depositProductsTask.</wsdl:documentation>
            <wsdl:input message="InitDepositProductsRequest"/>
            <wsdl:output message="InitDepositProductsResponse"/>
            <wsdl:fault name="InitDepositProductsFault" message="InitDepositProductsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDepositProducts">
            <wsdl:documentation>Operation used to update data for the depositProductsTask.
            </wsdl:documentation>
            <wsdl:input message="UpdateDepositProductsRequest"/>
            <wsdl:output message="UpdateDepositProductsResponse"/>
            <wsdl:fault name="UpdateDepositProductsFault" message="UpdateDepositProductsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignMethod">
            <wsdl:documentation>Operation used to initialize the signMethodTask.</wsdl:documentation>
            <wsdl:input message="InitSignMethodRequest"/>
            <wsdl:output message="InitSignMethodResponse"/>
            <wsdl:fault name="InitSignMethodFault" message="InitSignMethodFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignMethod">
            <wsdl:documentation>Operation used to update data for the signMethodTask.

                For all transitions except for back, following attributes are required:
                - deliveryWay.deliveryWayId
                In case client selected BRANCH as delivery way also these attributes are required:
                - deliveryWay.branchId
                In case client selected MESSENGER as delivery way also these attributes are required:
                - deliveryWay.addressSelected
                In case client selected EMPLOYMENT as messanger delivery address also these attributes are required to be valid:
                - deliveryWay.selectedAddress

                Generated business faults:
                - Application.Contract.Delivery.BranchNotSpecified - Branch is not specified for contract delivery
                - Application.AddressNotSelected - Address for contract delivery not selected

                Generated validation faults:
                - Application.AddressNotInCz - Specified employment address is not in CZ
                - Application.AddressNotConfirmed - The employment address does not conform to registers and it is not confirmed by the user.
                - Application.AddressNotSpecified - Employment address for contract delivery not specified

            </wsdl:documentation>
            <wsdl:input message="UpdateSignMethodRequest"/>
            <wsdl:output message="UpdateSignMethodResponse"/>
            <wsdl:fault name="UpdateSignMethodFault" message="UpdateSignMethodFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitLegalMandatoryData">
            <wsdl:documentation>Operation used to initialize the relationshipTask.</wsdl:documentation>
            <wsdl:input message="InitLegalMandatoryDataRequest"/>
            <wsdl:output message="InitLegalMandatoryDataResponse"/>
            <wsdl:fault name="InitLegalMandatoryDataFault" message="InitLegalMandatoryDataFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateLegalMandatoryData">
            <wsdl:documentation>Operation used to update data for the relationshipTask.

                Generated business faults:
                - Application.Contract.NoServicesUsageSpecified - No bank service usage selected
                - Application.Contract.CurrentServicesUsageNotSpecified - Current services or loan selected as relation to bank. But no current service selected
                - Application.Contract.SourceOfMoneyNotSpecified - Savings selected as relation to bank. But no source of money selected
            </wsdl:documentation>
            <wsdl:input message="UpdateLegalMandatoryDataRequest"/>
            <wsdl:output message="UpdateLegalMandatoryDataResponse"/>
            <wsdl:fault name="UpdateLegalMandatoryDataFault" message="UpdateLegalMandatoryDataFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitUsername">
            <wsdl:documentation>Operation used to initialize the userNameTask.</wsdl:documentation>
            <wsdl:input message="InitUsernameRequest"/>
            <wsdl:output message="InitUsernameResponse"/>
            <wsdl:fault name="InitUsernameFault" message="InitUsernameFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateUsername">
            <wsdl:documentation>Operation used to update data for the userNameTask.

                For all transitions except for back, following attributes are required:
                - username

                Generated business faults:
                - Application.Contract.UsernameExists - Username is already used by someone else
                - Application.Contract.InvalidUsername - Username contains invalid characters

            </wsdl:documentation>
            <wsdl:input message="UpdateUsernameRequest"/>
            <wsdl:output message="UpdateUsernameResponse"/>
            <wsdl:fault name="UpdateUsernameFault" message="UpdateUsernameFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitPassword">
            <wsdl:documentation>Operation used to initialize the passwordTask.</wsdl:documentation>
            <wsdl:input message="InitPasswordRequest"/>
            <wsdl:output message="InitPasswordResponse"/>
            <wsdl:fault name="InitPasswordFault" message="InitPasswordFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdatePassword">
            <wsdl:documentation>Operation used to update data for the passwordTask.

                For all transitions except for back, following attributes are required:
                - password

                Generated business faults:
                - Application.Contract.InvalidPassword - Combination of username and password is invalid (maybe same)

            </wsdl:documentation>
            <wsdl:input message="UpdatePasswordRequest"/>
            <wsdl:output message="UpdatePasswordResponse"/>
            <wsdl:fault name="UpdatePasswordFault" message="UpdatePasswordFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitFinalization">
            <wsdl:documentation>Init of final step of general contract application.</wsdl:documentation>
            <wsdl:input message="InitFinalizationRequest"/>
            <wsdl:output message="InitFinalizationResponse"/>
            <wsdl:fault name="InitFinalizationFault" message="InitFinalizationFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="SendGeneralContractInfo">
            <wsdl:documentation>Sends general contract application id to client.

                Generated business faults:
                - Application.Contract.NoGcApplication - No general contract application found.
            </wsdl:documentation>
            <wsdl:input message="SendGeneralContractInfoRequest"/>
            <wsdl:output message="SendGeneralContractInfoResponse"/>
            <wsdl:fault name="SendGeneralContractInfoFault" message="SendGeneralContractInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <wsdl:documentation>Resumes process of general contract application</wsdl:documentation>
            <wsdl:input message="ResumeApplicationRequest"/>
            <wsdl:output message="ResumeApplicationResponse"/>
            <wsdl:fault name="ResumeApplicationFault" message="ResumeApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="CancelUnfinishedApplication">
            <wsdl:documentation>Cancels process of general contract application</wsdl:documentation>
            <wsdl:input message="CancelUnfinishedApplicationRequest"/>
            <wsdl:output message="CancelUnfinishedApplicationResponse"/>
            <wsdl:fault name="CancelUnfinishedApplicationFault" message="CancelUnfinishedApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.

                Generated business faults:
                - Common.ProcessEnded - Underlaying activiti process ended
            </wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetApplicantInfo">
            <wsdl:documentation>Returns applicant info</wsdl:documentation>
            <wsdl:input message="GetApplicantInfoRequest"/>
            <wsdl:output message="GetApplicantInfoResponse"/>
            <wsdl:fault name="GetApplicantInfoFault" message="GetApplicantInfoFault"/>
        </wsdl:operation>

        <wsdl:operation name="TerminateGeneralContract">
            <wsdl:documentation>Returns applicant info</wsdl:documentation>
            <wsdl:input message="TerminateGeneralContractRequest"/>
            <wsdl:output message="TerminateGeneralContractResponse"/>
            <wsdl:fault name="TerminateGeneralContractFault" message="TerminateGeneralContractFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="GeneralContractApplicationBinding" type="GeneralContractApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitIdentityVerificationMethod">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitIdentityVerificationMethodFault">
                <soap:fault name="InitIdentityVerificationMethodFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateIdentityVerificationMethod">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateIdentityVerificationMethodFault">
                <soap:fault name="UpdateIdentityVerificationMethodFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="InitBasicParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBasicParametersFault">
                <soap:fault name="InitBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateBasicParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBasicParametersFault">
                <soap:fault name="UpdateBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitAddress">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAddressFault">
                <soap:fault name="InitAddressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateAddress">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateAddressFault">
                <soap:fault name="UpdateAddressFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSourceBankSelection">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSourceBankSelectionFault">
                <soap:fault name="InitSourceBankSelectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSourceBankSelection">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSourceBankSelectionFault">
                <soap:fault name="UpdateSourceBankSelectionFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitPersonalDataApproval">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitPersonalDataApprovalFault">
                <soap:fault name="InitPersonalDataApprovalFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdatePersonalDataApproval">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdatePersonalDataApprovalFault">
                <soap:fault name="UpdatePersonalDataApprovalFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        
        <wsdl:operation name="InitFirstScoringApproved">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitFirstScoringApprovedFault">
                <soap:fault name="InitFirstScoringApprovedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateFirstScoringApproved">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateFirstScoringApprovedFault">
                <soap:fault name="UpdateFirstScoringApprovedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitDepositProducts">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDepositProductsFault">
                <soap:fault name="InitDepositProductsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateDepositProducts">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDepositProductsFault">
                <soap:fault name="UpdateDepositProductsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignMethod">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignMethodFault">
                <soap:fault name="InitSignMethodFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignMethod">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignMethodFault">
                <soap:fault name="UpdateSignMethodFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitLegalMandatoryData">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitLegalMandatoryDataFault">
                <soap:fault name="InitLegalMandatoryDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateLegalMandatoryData">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateLegalMandatoryDataFault">
                <soap:fault name="UpdateLegalMandatoryDataFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="InitUsername">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitUsernameFault">
                <soap:fault name="InitUsernameFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateUsername">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateUsernameFault">
                <soap:fault name="UpdateUsernameFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitPassword">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitPasswordFault">
                <soap:fault name="InitPasswordFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdatePassword">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdatePasswordFault">
                <soap:fault name="UpdatePasswordFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitFinalization">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitFinalizationFault">
                <soap:fault name="InitFinalizationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="SendGeneralContractInfo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="SendGeneralContractInfoFault">
                <soap:fault name="SendGeneralContractInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ResumeApplicationFault">
                <soap:fault name="ResumeApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CancelUnfinishedApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelUnfinishedApplicationFault">
                <soap:fault name="CancelUnfinishedApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetApplicantInfo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetApplicantInfoFault">
                <soap:fault name="GetApplicantInfoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="TerminateGeneralContract">
        <soap:operation soapAction=""/>
        <wsdl:input>
            <soap:body use="literal"/>
        </wsdl:input>
        <wsdl:output>
            <soap:body use="literal"/>
        </wsdl:output>
        <wsdl:fault name="TerminateGeneralContractFault">
            <soap:fault name="TerminateGeneralContractFault" use="literal"/>
        </wsdl:fault>
    </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="GeneralContractApplicationService">
        <wsdl:documentation>Service providing operations related to general contract application of a walkin.

            The application has the following task IDs:
            - identityVerificationMethod - corresponds to the very first screen where user chooses identity verification method
            - basicParametersTask - corresponds to screen where user specifies name and other basic data about himself
            - addressTask - corresponds to the screen where user specifies address and other additional data
            - sourceBankSelectionTask - corresponds to the screen where user connects to other bank using Bank ID
            - waitForScoringResultTask - AMS is waiting for the first scoring result
            - firstScoringApprovedTask - client was approved
            - rejectedTask - client was rejected
            - depositProductsTask - screen to select deposit products products
            - loanWaitingTask - expert verification is necessary, AMS is waiting for its result
            - waitForSegmentationOffer - wait for decision of segmentation offer phone call
            - alternativeToCashLoanTask - summary page with alternative offer to cash loan
            - alternativeToConsolidationTask - summary page with alternative offer to consolidation
            - signMethodTask - page where client select how he wants to sign cotnract with the bank
            - relationshipTask - page where source of the money and client relationship to the bank are entered
            - userNameTask - page where client selects users he wants to use
            - passwordTask - page where client selects a password to access IB
            - generalContractSummaryTask - summary of data entered in all previous steps
            - finalizationTask - just init, final step of the flow
            - loanScoringFailureTask - page shown, when the loan scoring cannot be completed
            - activeContractCanceledTask - page shown, when there is active contract found for the new customer

            - getCurrentTask - returns current task (step) of general contract application process
            - resumeApplication - restarts existing general contract application
            - cancelUnfinishedApplication - cancels existing unfinished general contract application
            - sendGeneralContractInfo - send information to provided email about contract application client does not remember details about unfinished general
            contract application
            - cancel - cancels whole general contract application
            - getApplicationDataSummary - returns application data for branch or ICC.
            - getApplicantInfo - returns basic info about applicant


            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>

        <wsdl:port name="GeneralContractApplicationPort" binding="GeneralContractApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/generalContract"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
