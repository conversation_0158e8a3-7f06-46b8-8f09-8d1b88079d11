<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/finalization"
                  targetNamespace="http://airbank.cz/ams/ws/application/finalization">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/finalization">
            <xs:include schemaLocation="AMSApplicationFinalizationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- LoanSummary task -->
    <wsdl:message name="InitLoanSummaryRequest">
        <wsdl:part element="InitLoanSummaryRequest" name="InitLoanSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitLoanSummaryResponse">
        <wsdl:part element="InitLoanSummaryResponse" name="InitLoanSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitLoanSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitLoanSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateLoanSummaryRequest">
        <wsdl:part element="UpdateLoanSummaryRequest" name="UpdateLoanSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateLoanSummaryResponse">
        <wsdl:part element="UpdateLoanSummaryResponse" name="UpdateLoanSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateLoanSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateLoanSummaryFault"/>
    </wsdl:message>

    <!-- OverdraftSummary task -->
    <wsdl:message name="InitOverdraftSummaryRequest">
        <wsdl:part element="InitOverdraftSummaryRequest" name="InitOverdraftSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftSummaryResponse">
        <wsdl:part element="InitOverdraftSummaryResponse" name="InitOverdraftSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitOverdraftSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitOverdraftSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateOverdraftSummaryRequest">
        <wsdl:part element="UpdateOverdraftSummaryRequest" name="UpdateOverdraftSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftSummaryResponse">
        <wsdl:part element="UpdateOverdraftSummaryResponse" name="UpdateOverdraftSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateOverdraftSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateOverdraftSummaryFault"/>
    </wsdl:message>

    <!-- ChangeMobilityDates task -->
    <wsdl:message name="InitChangeMobilityDatesRequest">
        <wsdl:part element="InitChangeMobilityDatesRequest" name="InitChangeMobilityDatesRequest"/>
    </wsdl:message>
    <wsdl:message name="InitChangeMobilityDatesResponse">
        <wsdl:part element="InitChangeMobilityDatesResponse" name="InitChangeMobilityDatesResponse"/>
    </wsdl:message>
    <wsdl:message name="InitChangeMobilityDatesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitChangeMobilityDatesFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateChangeMobilityDatesRequest">
        <wsdl:part element="UpdateChangeMobilityDatesRequest" name="UpdateChangeMobilityDatesRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateChangeMobilityDatesResponse">
        <wsdl:part element="UpdateChangeMobilityDatesResponse" name="UpdateChangeMobilityDatesResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateChangeMobilityDatesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateChangeMobilityDatesFault"/>
    </wsdl:message>

    <!-- SignOverInternet task -->
    <wsdl:message name="InitSignOverInternetRequest">
        <wsdl:part element="InitSignOverInternetRequest" name="InitSignOverInternetRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverInternetResponse">
        <wsdl:part element="InitSignOverInternetResponse" name="InitSignOverInternetResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverInternetFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignOverInternetFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignOverInternetRequest">
        <wsdl:part element="UpdateSignOverInternetRequest" name="UpdateSignOverInternetRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverInternetResponse">
        <wsdl:part element="UpdateSignOverInternetResponse" name="UpdateSignOverInternetResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverInternetFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignOverInternetFault"/>
    </wsdl:message>

    <!-- SignOverMobile task -->
    <wsdl:message name="InitSignOverMobileRequest">
        <wsdl:part element="InitSignOverMobileRequest" name="InitSignOverMobileRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverMobileResponse">
        <wsdl:part element="InitSignOverMobileResponse" name="InitSignOverMobileResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignOverMobileFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignOverMobileFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignOverMobileRequest">
        <wsdl:part element="UpdateSignOverMobileRequest" name="UpdateSignOverMobileRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverMobileResponse">
        <wsdl:part element="UpdateSignOverMobileResponse" name="UpdateSignOverMobileResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignOverMobileFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignOverMobileFault"/>
    </wsdl:message>

    <!-- SignAtBranch task -->
    <wsdl:message name="InitSignAtBranchRequest">
        <wsdl:part element="InitSignAtBranchRequest" name="InitSignAtBranchRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchResponse">
        <wsdl:part element="InitSignAtBranchResponse" name="InitSignAtBranchResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignAtBranchFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignAtBranchRequest">
        <wsdl:part element="UpdateSignAtBranchRequest" name="UpdateSignAtBranchRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchResponse">
        <wsdl:part element="UpdateSignAtBranchResponse" name="UpdateSignAtBranchResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignAtBranchFault"/>
    </wsdl:message>


    <!-- BlueSign task -->
    <wsdl:message name="InitBlueSignRequest">
        <wsdl:part element="InitBlueSignRequest" name="InitBlueSignRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBlueSignResponse">
        <wsdl:part element="InitBlueSignResponse" name="InitBlueSignResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBlueSignFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBlueSignFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateBlueSignRequest">
        <wsdl:part element="UpdateBlueSignRequest" name="UpdateBlueSignRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBlueSignResponse">
        <wsdl:part element="UpdateBlueSignResponse" name="UpdateBlueSignResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBlueSignFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBlueSignFault"/>
    </wsdl:message>


    <!-- Signpad task -->
    <wsdl:message name="InitSignpadRequest">
        <wsdl:part element="InitSignpadRequest" name="InitSignpadRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignpadResponse">
        <wsdl:part element="InitSignpadResponse" name="InitSignpadResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignpadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignpadFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignpadRequest">
        <wsdl:part element="UpdateSignpadRequest" name="UpdateSignpadRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignpadResponse">
        <wsdl:part element="UpdateSignpadResponse" name="UpdateSignpadResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignpadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignpadFault"/>
    </wsdl:message>

    <!-- SignpadConfirm task -->
    <wsdl:message name="InitSignpadConfirmRequest">
        <wsdl:part element="InitSignpadConfirmRequest" name="InitSignpadConfirmRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignpadConfirmResponse">
        <wsdl:part element="InitSignpadConfirmResponse" name="InitSignpadConfirmResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignpadConfirmFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignpadConfirmFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignpadConfirmRequest">
        <wsdl:part element="UpdateSignpadConfirmRequest" name="UpdateSignpadConfirmRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignpadConfirmResponse">
        <wsdl:part element="UpdateSignpadConfirmResponse" name="UpdateSignpadConfirmResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignpadConfirmFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignpadConfirmFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignpadConfirmInternalCodeOnlyRequest">
        <wsdl:part element="UpdateSignpadConfirmInternalCodeOnlyRequest" name="UpdateSignpadConfirmInternalCodeOnlyRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignpadConfirmInternalCodeOnlyResponse">
        <wsdl:part element="UpdateSignpadConfirmInternalCodeOnlyResponse" name="UpdateSignpadConfirmInternalCodeOnlyResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignpadConfirmInternalCodeOnlyFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignpadConfirmInternalCodeOnlyFault"/>
    </wsdl:message>

    <!-- SignAtBranchByPwd task -->
    <wsdl:message name="InitSignAtBranchByPwdRequest">
        <wsdl:part element="InitSignAtBranchByPwdRequest" name="InitSignAtBranchByPwdRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchByPwdResponse">
        <wsdl:part element="InitSignAtBranchByPwdResponse" name="InitSignAtBranchByPwdResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchByPwdFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignAtBranchByPwdFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignAtBranchByPwdRequest">
        <wsdl:part element="UpdateSignAtBranchByPwdRequest" name="UpdateSignAtBranchByPwdRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchByPwdResponse">
        <wsdl:part element="UpdateSignAtBranchByPwdResponse" name="UpdateSignAtBranchByPwdResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchByPwdFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignAtBranchByPwdFault"/>
    </wsdl:message>

    <!-- SignAtBranchBySms task -->
    <wsdl:message name="InitSignAtBranchBySmsRequest">
        <wsdl:part element="InitSignAtBranchBySmsRequest" name="InitSignAtBranchBySmsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchBySmsResponse">
        <wsdl:part element="InitSignAtBranchBySmsResponse" name="InitSignAtBranchBySmsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignAtBranchBySmsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignAtBranchBySmsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignAtBranchBySmsRequest">
        <wsdl:part element="UpdateSignAtBranchBySmsRequest" name="UpdateSignAtBranchBySmsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchBySmsResponse">
        <wsdl:part element="UpdateSignAtBranchBySmsResponse" name="UpdateSignAtBranchBySmsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignAtBranchBySmsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignAtBranchBySmsFault"/>
    </wsdl:message>

    <!-- signDifferently task -->
    <wsdl:message name="InitSignDifferentlyRequest">
        <wsdl:part element="InitSignDifferentlyRequest" name="InitSignDifferentlyRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSignDifferentlyResponse">
        <wsdl:part element="InitSignDifferentlyResponse" name="InitSignDifferentlyResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSignDifferentlyFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSignDifferentlyFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSignDifferentlyRequest">
        <wsdl:part element="UpdateSignDifferentlyRequest" name="UpdateSignDifferentlyRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignDifferentlyResponse">
        <wsdl:part element="UpdateSignDifferentlyResponse" name="UpdateSignDifferentlyResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSignDifferentlyFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSignDifferentlyFault"/>
    </wsdl:message>

    <!-- DocumentUpload task -->
    <wsdl:message name="InitDocumentUploadRequest">
        <wsdl:part element="InitDocumentUploadRequest" name="InitDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDocumentUploadResponse">
        <wsdl:part element="InitDocumentUploadResponse" name="InitDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDocumentUploadRequest">
        <wsdl:part element="UpdateDocumentUploadRequest" name="UpdateDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDocumentUploadResponse">
        <wsdl:part element="UpdateDocumentUploadResponse" name="UpdateDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDocumentUploadFault"/>
    </wsdl:message>

    <!-- Declaration task -->
    <wsdl:message name="InitDeclarationRequest">
        <wsdl:part element="InitDeclarationRequest" name="InitDeclarationRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDeclarationResponse">
        <wsdl:part element="InitDeclarationResponse" name="InitDeclarationResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDeclarationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDeclarationFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDeclarationRequest">
        <wsdl:part element="UpdateDeclarationRequest" name="UpdateDeclarationRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeclarationResponse">
        <wsdl:part element="UpdateDeclarationResponse" name="UpdateDeclarationResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeclarationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDeclarationFault"/>
    </wsdl:message>

    <!-- NoDocumentUpload task -->
    <wsdl:message name="InitNoDocumentUploadRequest">
        <wsdl:part element="InitNoDocumentUploadRequest" name="InitNoDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="InitNoDocumentUploadResponse">
        <wsdl:part element="InitNoDocumentUploadResponse" name="InitNoDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="InitNoDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitNoDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateNoDocumentUploadRequest">
        <wsdl:part element="UpdateNoDocumentUploadRequest" name="UpdateNoDocumentUploadRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateNoDocumentUploadResponse">
        <wsdl:part element="UpdateNoDocumentUploadResponse" name="UpdateNoDocumentUploadResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateNoDocumentUploadFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateNoDocumentUploadFault"/>
    </wsdl:message>

    <wsdl:message name="GetCompletionIdForEnvelopeRequest">
        <wsdl:part element="GetCompletionIdForEnvelopeRequest" name="GetCompletionIdForEnvelopeRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCompletionIdForEnvelopeResponse">
        <wsdl:part element="GetCompletionIdForEnvelopeResponse" name="GetCompletionIdForEnvelopeResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCompletionIdForEnvelopeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCompletionIdForEnvelopeFault"/>
    </wsdl:message>

    <wsdl:message name="GetInProgressApplicationsRequest">
        <wsdl:part element="GetInProgressApplicationsRequest" name="GetInProgressApplicationsRequest"/>
    </wsdl:message>
    <wsdl:message name="GetInProgressApplicationsResponse">
        <wsdl:part element="GetInProgressApplicationsResponse" name="GetInProgressApplicationsResponse"/>
    </wsdl:message>
    <wsdl:message name="GetInProgressApplicationsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetInProgressApplicationsFault"/>
    </wsdl:message>


    <wsdl:message name="HasRequiredDocumentsRequest">
        <wsdl:part element="HasRequiredDocumentsRequest" name="HasRequiredDocumentsRequest"/>
    </wsdl:message>
    <wsdl:message name="HasRequiredDocumentsResponse">
        <wsdl:part element="HasRequiredDocumentsResponse" name="HasRequiredDocumentsResponse"/>
    </wsdl:message>
    <wsdl:message name="HasRequiredDocumentsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="HasRequiredDocumentsFault"/>
    </wsdl:message>

    <wsdl:message name="GetMobilitySubApplicationCompletionIdRequest">
        <wsdl:part element="GetMobilitySubApplicationCompletionIdRequest" name="GetMobilitySubApplicationCompletionIdRequest"/>
    </wsdl:message>
    <wsdl:message name="GetMobilitySubApplicationCompletionIdResponse">
        <wsdl:part element="GetMobilitySubApplicationCompletionIdResponse" name="GetMobilitySubApplicationCompletionIdResponse"/>
    </wsdl:message>
    <wsdl:message name="GetMobilitySubApplicationCompletionIdFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetMobilitySubApplicationCompletionIdFault"/>
    </wsdl:message>

    <!-- CompletionStateChange task -->
    <wsdl:message name="CompletionStateChangeRequest">
        <wsdl:part element="CompletionStateChangeRequest" name="CompletionStateChangeRequest"/>
    </wsdl:message>
    <wsdl:message name="CompletionStateChangeResponse">
        <wsdl:part element="CompletionStateChangeResponse" name="CompletionStateChangeResponse"/>
    </wsdl:message>
    <wsdl:message name="CompletionStateChangeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CompletionStateChangeFault"/>
    </wsdl:message>

    <!-- CreatePriorityEvent -->
    <wsdl:message name="CreatePriorityEventRequest">
        <wsdl:part element="CreatePriorityEventRequest" name="CreatePriorityEventRequest"/>
    </wsdl:message>
    <wsdl:message name="CreatePriorityEventResponse">
        <wsdl:part element="CreatePriorityEventResponse" name="CreatePriorityEventResponse"/>
    </wsdl:message>
    <wsdl:message name="CreatePriorityEventFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CreatePriorityEventFault"/>
    </wsdl:message>

    <wsdl:message name="GetMortgageApplicationsStagesRequest">
        <wsdl:part element="GetMortgageApplicationsStagesRequest" name="GetMortgageApplicationsStagesRequest"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageApplicationsStagesResponse">
        <wsdl:part element="GetMortgageApplicationsStagesResponse" name="GetMortgageApplicationsStagesResponse"/>
    </wsdl:message>
    <wsdl:message name="GetMortgageApplicationsStagesFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetMortgageApplicationsStagesFault"/>
    </wsdl:message>

    <wsdl:message name="ValidateDeliveryDocumentsDateRequest">
        <wsdl:part element="ValidateDeliveryDocumentsDateRequest" name="ValidateDeliveryDocumentsDateRequest"/>
    </wsdl:message>
    <wsdl:message name="ValidateDeliveryDocumentsDateResponse">
        <wsdl:part element="ValidateDeliveryDocumentsDateResponse" name="ValidateDeliveryDocumentsDateResponse"/>
    </wsdl:message>
    <wsdl:message name="ValidateDeliveryDocumentsDateFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ValidateDeliveryDocumentsDateFault"/>
    </wsdl:message>

    <wsdl:message name="NewContractActivatedRequest">
        <wsdl:part element="NewContractActivatedRequest" name="NewContractActivatedRequest"/>
    </wsdl:message>
    <wsdl:message name="NewContractActivatedResponse">
        <wsdl:part element="NewContractActivatedResponse" name="NewContractActivatedResponse"/>
    </wsdl:message>
    <wsdl:message name="NewContractActivatedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="NewContractActivatedFault"/>
    </wsdl:message>

    <wsdl:message name="GetDocumentContractsInEnvelopeRequest">
        <wsdl:part element="GetDocumentContractsInEnvelopeRequest" name="GetDocumentContractsInEnvelopeRequest"/>
    </wsdl:message>
    <wsdl:message name="GetDocumentContractsInEnvelopeResponse">
        <wsdl:part element="GetDocumentContractsInEnvelopeResponse" name="GetDocumentContractsInEnvelopeResponse"/>
    </wsdl:message>
    <wsdl:message name="GetDocumentContractsInEnvelopeFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetDocumentContractsInEnvelopeFault"/>
    </wsdl:message>

    <wsdl:message name="InitDeliverMainIncomeDocumentViaAISPRequest">
        <wsdl:part element="InitDeliverMainIncomeDocumentViaAISPRequest" name="InitDeliverMainIncomeDocumentViaAISPRequest"/>
    </wsdl:message>
    <wsdl:message name="InitDeliverMainIncomeDocumentViaAISPResponse">
        <wsdl:part element="InitDeliverMainIncomeDocumentViaAISPResponse" name="InitDeliverMainIncomeDocumentViaAISPResponse"/>
    </wsdl:message>
    <wsdl:message name="InitDeliverMainIncomeDocumentViaAISPFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitDeliverMainIncomeDocumentViaAISPFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateDeliverMainIncomeDocumentViaAISPRequest">
        <wsdl:part element="UpdateDeliverMainIncomeDocumentViaAISPRequest" name="UpdateDeliverMainIncomeDocumentViaAISPRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeliverMainIncomeDocumentViaAISPResponse">
        <wsdl:part element="UpdateDeliverMainIncomeDocumentViaAISPResponse" name="UpdateDeliverMainIncomeDocumentViaAISPResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateDeliverMainIncomeDocumentViaAISPFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateDeliverMainIncomeDocumentViaAISPFault"/>
    </wsdl:message>

    <wsdl:message name="InitAispIncomeDocumentResultRequest">
        <wsdl:part element="InitAispIncomeDocumentResultRequest" name="InitAispIncomeDocumentResultRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAispIncomeDocumentResultResponse">
        <wsdl:part element="InitAispIncomeDocumentResultResponse" name="InitAispIncomeDocumentResultResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAispIncomeDocumentResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAispIncomeDocumentResultFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateAispIncomeDocumentResultRequest">
        <wsdl:part element="UpdateAispIncomeDocumentResultRequest" name="UpdateAispIncomeDocumentResultRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateAispIncomeDocumentResultResponse">
        <wsdl:part element="UpdateAispIncomeDocumentResultResponse" name="UpdateAispIncomeDocumentResultResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateAispIncomeDocumentResultFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateAispIncomeDocumentResultFault"/>
    </wsdl:message>

    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <!-- Get document upload status for all documents -->
    <wsdl:message name="GetDocumentsUploadStatusRequest">
        <wsdl:part element="GetDocumentsUploadStatusRequest" name="GetDocumentsUploadStatusRequest"/>
    </wsdl:message>
    <wsdl:message name="GetDocumentsUploadStatusResponse">
        <wsdl:part element="GetDocumentsUploadStatusResponse" name="GetDocumentsUploadStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="GetDocumentsUploadStatusFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetDocumentsUploadStatusFault"/>
    </wsdl:message>

    <!-- Application rejected task -->
    <wsdl:message name="InitRejectedRequest">
        <wsdl:part element="InitRejectedRequest" name="InitRejectedRequest"/>
    </wsdl:message>
    <wsdl:message name="InitRejectedResponse">
        <wsdl:part element="InitRejectedResponse" name="InitRejectedResponse"/>
    </wsdl:message>
    <wsdl:message name="InitRejectedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitRejectedFault"/>
    </wsdl:message>

    <wsdl:message name="InitActivationPaymentRequest">
        <wsdl:part element="InitActivationPaymentRequest" name="InitActivationPaymentRequest"/>
    </wsdl:message>
    <wsdl:message name="InitActivationPaymentResponse">
        <wsdl:part element="InitActivationPaymentResponse" name="InitActivationPaymentResponse"/>
    </wsdl:message>
    <wsdl:message name="InitActivationPaymentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitActivationPaymentFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateActivationPaymentRequest">
        <wsdl:part element="UpdateActivationPaymentRequest" name="UpdateActivationPaymentRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateActivationPaymentResponse">
        <wsdl:part element="UpdateActivationPaymentResponse" name="UpdateActivationPaymentResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateActivationPaymentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateActivationPaymentFault"/>
    </wsdl:message>

    <wsdl:message name="InitVictoryScreenRequest">
        <wsdl:part element="InitVictoryScreenRequest" name="InitVictoryScreenRequest"/>
    </wsdl:message>
    <wsdl:message name="InitVictoryScreenResponse">
        <wsdl:part element="InitVictoryScreenResponse" name="InitVictoryScreenResponse"/>
    </wsdl:message>
    <wsdl:message name="InitVictoryScreenFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitVictoryScreenFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateVictoryScreenRequest">
        <wsdl:part element="UpdateVictoryScreenRequest" name="UpdateVictoryScreenRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateVictoryScreenResponse">
        <wsdl:part element="UpdateVictoryScreenResponse" name="UpdateVictoryScreenResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateVictoryScreenFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateVictoryScreenFault"/>
    </wsdl:message>

    <wsdl:message name="InitPairingRequest">
        <wsdl:part element="InitPairingRequest" name="InitPairingRequest"/>
    </wsdl:message>
    <wsdl:message name="InitPairingResponse">
        <wsdl:part element="InitPairingResponse" name="InitPairingResponse"/>
    </wsdl:message>
    <wsdl:message name="InitPairingFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitPairingFault"/>
    </wsdl:message>

    <wsdl:message name="UpdatePairingRequest">
        <wsdl:part element="UpdatePairingRequest" name="UpdatePairingRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdatePairingResponse">
        <wsdl:part element="UpdatePairingResponse" name="UpdatePairingResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdatePairingFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdatePairingFault"/>
    </wsdl:message>

    <!-- Check if contract activation allowed -->
    <wsdl:message name="GeneralContractActivationAllowedRequest">
        <wsdl:part element="GeneralContractActivationAllowedRequest" name="GeneralContractActivationAllowedRequest" />
    </wsdl:message>
    <wsdl:message name="GeneralContractActivationAllowedResponse">
        <wsdl:part element="GeneralContractActivationAllowedResponse" name="GeneralContractActivationAllowedResponse" />
    </wsdl:message>
    <wsdl:message name="GeneralContractActivationAllowedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GeneralContractActivationAllowedFault"/>
    </wsdl:message>

    <wsdl:portType name="ApplicationFinalization">
        <wsdl:operation name="Start">
            <wsdl:documentation>
                Starts application finalization for the given application.

                Generated business faults:
                - Application.NotFound - when there was no application found based on the application ID
                - Application.EnvelopeNotFound - when there was no envelope found based on the envelope ID
                - Common.NoInputData - when none of the input parameters was specified
                - Application.ContractNotFound - when no application was found based on the contract ID
                - Application.Finalization.NothingToFinalize - when there is nothing to finalize (i.e. the application is signed and there are no documents to
                upload)
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitLoanSummary">
            <wsdl:input message="InitLoanSummaryRequest"/>
            <wsdl:output message="InitLoanSummaryResponse"/>
            <wsdl:fault name="InitLoanSummaryFault" message="InitLoanSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateLoanSummary">
            <wsdl:input message="UpdateLoanSummaryRequest"/>
            <wsdl:output message="UpdateLoanSummaryResponse"/>
            <wsdl:fault name="UpdateLoanSummaryFault" message="UpdateLoanSummaryFault"/>
        </wsdl:operation>


        <wsdl:operation name="InitOverdraftSummary">
            <wsdl:documentation>Operation used to init overdraft summary.</wsdl:documentation>
            <wsdl:input message="InitOverdraftSummaryRequest"/>
            <wsdl:output message="InitOverdraftSummaryResponse"/>
            <wsdl:fault name="InitOverdraftSummaryFault" message="InitOverdraftSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftSummary">
            <wsdl:documentation>Operation used to update overdraft summary.</wsdl:documentation>
            <wsdl:input message="UpdateOverdraftSummaryRequest"/>
            <wsdl:output message="UpdateOverdraftSummaryResponse"/>
            <wsdl:fault name="UpdateOverdraftSummaryFault" message="UpdateOverdraftSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitChangeMobilityDates">
            <wsdl:input message="InitChangeMobilityDatesRequest"/>
            <wsdl:output message="InitChangeMobilityDatesResponse"/>
            <wsdl:fault name="InitChangeMobilityDatesFault" message="InitChangeMobilityDatesFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateChangeMobilityDates">
            <wsdl:input message="UpdateChangeMobilityDatesRequest"/>
            <wsdl:output message="UpdateChangeMobilityDatesResponse"/>
            <wsdl:fault name="UpdateChangeMobilityDatesFault" message="UpdateChangeMobilityDatesFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverInternet">
            <wsdl:input message="InitSignOverInternetRequest"/>
            <wsdl:output message="InitSignOverInternetResponse"/>
            <wsdl:fault name="InitSignOverInternetFault" message="InitSignOverInternetFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignOverInternet">
            <wsdl:input message="UpdateSignOverInternetRequest"/>
            <wsdl:output message="UpdateSignOverInternetResponse"/>
            <wsdl:fault name="UpdateSignOverInternetFault" message="UpdateSignOverInternetFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverMobile">
            <wsdl:input message="InitSignOverMobileRequest"/>
            <wsdl:output message="InitSignOverMobileResponse"/>
            <wsdl:fault name="InitSignOverMobileFault" message="InitSignOverMobileFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignOverMobile">
            <wsdl:input message="UpdateSignOverMobileRequest"/>
            <wsdl:output message="UpdateSignOverMobileResponse"/>
            <wsdl:fault name="UpdateSignOverMobileFault" message="UpdateSignOverMobileFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranch">
            <wsdl:input message="InitSignAtBranchRequest"/>
            <wsdl:output message="InitSignAtBranchResponse"/>
            <wsdl:fault name="InitSignAtBranchFault" message="InitSignAtBranchFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignAtBranch">
            <wsdl:input message="UpdateSignAtBranchRequest"/>
            <wsdl:output message="UpdateSignAtBranchResponse"/>
            <wsdl:fault name="UpdateSignAtBranchFault" message="UpdateSignAtBranchFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignpad">
            <wsdl:input message="InitSignpadRequest"/>
            <wsdl:output message="InitSignpadResponse"/>
            <wsdl:fault name="InitSignpadFault" message="InitSignpadFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignpad">
            <wsdl:input message="UpdateSignpadRequest"/>
            <wsdl:output message="UpdateSignpadResponse"/>
            <wsdl:fault name="UpdateSignpadFault" message="UpdateSignpadFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignpadConfirm">
            <wsdl:input message="InitSignpadConfirmRequest"/>
            <wsdl:output message="InitSignpadConfirmResponse"/>
            <wsdl:fault name="InitSignpadConfirmFault" message="InitSignpadConfirmFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignpadConfirm">
            <wsdl:input message="UpdateSignpadConfirmRequest"/>
            <wsdl:output message="UpdateSignpadConfirmResponse"/>
            <wsdl:fault name="UpdateSignpadConfirmFault" message="UpdateSignpadConfirmFault"/>
        </wsdl:operation>
        
        <wsdl:operation name="UpdateSignpadConfirmInternalCodeOnly">
            <wsdl:input message="UpdateSignpadConfirmInternalCodeOnlyRequest"/>
            <wsdl:output message="UpdateSignpadConfirmInternalCodeOnlyResponse"/>
            <wsdl:fault name="UpdateSignpadConfirmInternalCodeOnlyFault" message="UpdateSignpadConfirmInternalCodeOnlyFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitBlueSign">
            <wsdl:input message="InitBlueSignRequest"/>
            <wsdl:output message="InitBlueSignResponse"/>
            <wsdl:fault name="InitBlueSignFault" message="InitBlueSignFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateBlueSign">
            <wsdl:input message="UpdateBlueSignRequest"/>
            <wsdl:output message="UpdateBlueSignResponse"/>
            <wsdl:fault name="UpdateBlueSignFault" message="UpdateBlueSignFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranchByPwd">
            <wsdl:input message="InitSignAtBranchByPwdRequest"/>
            <wsdl:output message="InitSignAtBranchByPwdResponse"/>
            <wsdl:fault name="InitSignAtBranchByPwdFault" message="InitSignAtBranchByPwdFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignAtBranchByPwd">
            <wsdl:input message="UpdateSignAtBranchByPwdRequest"/>
            <wsdl:output message="UpdateSignAtBranchByPwdResponse"/>
            <wsdl:fault name="UpdateSignAtBranchByPwdFault" message="UpdateSignAtBranchByPwdFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranchBySms">
            <wsdl:input message="InitSignAtBranchBySmsRequest"/>
            <wsdl:output message="InitSignAtBranchBySmsResponse"/>
            <wsdl:fault name="InitSignAtBranchBySmsFault" message="InitSignAtBranchBySmsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignAtBranchBySms">
            <wsdl:input message="UpdateSignAtBranchBySmsRequest"/>
            <wsdl:output message="UpdateSignAtBranchBySmsResponse"/>
            <wsdl:fault name="UpdateSignAtBranchBySmsFault" message="UpdateSignAtBranchBySmsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSignDifferently">
            <wsdl:input message="InitSignDifferentlyRequest"/>
            <wsdl:output message="InitSignDifferentlyResponse"/>
            <wsdl:fault name="InitSignDifferentlyFault" message="InitSignDifferentlyFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSignDifferently">
            <wsdl:input message="UpdateSignDifferentlyRequest"/>
            <wsdl:output message="UpdateSignDifferentlyResponse"/>
            <wsdl:fault name="UpdateSignDifferentlyFault" message="UpdateSignDifferentlyFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDocumentUpload">
            <wsdl:input message="InitDocumentUploadRequest"/>
            <wsdl:output message="InitDocumentUploadResponse"/>
            <wsdl:fault name="InitDocumentUploadFault" message="InitDocumentUploadFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDocumentUpload">
            <wsdl:input message="UpdateDocumentUploadRequest"/>
            <wsdl:output message="UpdateDocumentUploadResponse"/>
            <wsdl:fault name="UpdateDocumentUploadFault" message="UpdateDocumentUploadFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDeclaration">
            <wsdl:input message="InitDeclarationRequest"/>
            <wsdl:output message="InitDeclarationResponse"/>
            <wsdl:fault name="InitDeclarationFault" message="InitDeclarationFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDeclaration">
            <wsdl:input message="UpdateDeclarationRequest"/>
            <wsdl:output message="UpdateDeclarationResponse"/>
            <wsdl:fault name="UpdateDeclarationFault" message="UpdateDeclarationFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitNoDocumentUpload">
            <wsdl:input message="InitNoDocumentUploadRequest"/>
            <wsdl:output message="InitNoDocumentUploadResponse"/>
            <wsdl:fault name="InitNoDocumentUploadFault" message="InitNoDocumentUploadFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateNoDocumentUpload">
            <wsdl:input message="UpdateNoDocumentUploadRequest"/>
            <wsdl:output message="UpdateNoDocumentUploadResponse"/>
            <wsdl:fault name="UpdateNoDocumentUploadFault" message="UpdateNoDocumentUploadFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCompletionIdForEnvelope">
            <wsdl:input message="GetCompletionIdForEnvelopeRequest"/>
            <wsdl:output message="GetCompletionIdForEnvelopeResponse"/>
            <wsdl:fault name="GetCompletionIdForEnvelopeFault" message="GetCompletionIdForEnvelopeFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetInProgressApplications">
            <wsdl:documentation>Returns applications, that are in progress in AMS (not in IB). Currently, only loan applications (cash loan and consolidation)
                can be returned.
            </wsdl:documentation>
            <wsdl:input message="GetInProgressApplicationsRequest"/>
            <wsdl:output message="GetInProgressApplicationsResponse"/>
            <wsdl:fault name="GetInProgressApplicationsFault" message="GetInProgressApplicationsFault"/>
        </wsdl:operation>

        <wsdl:operation name="HasRequiredDocuments">
            <wsdl:documentation>Returns required document groups for given application IDs and current channel.</wsdl:documentation>
            <wsdl:input message="HasRequiredDocumentsRequest"/>
            <wsdl:output message="HasRequiredDocumentsResponse"/>
            <wsdl:fault name="HasRequiredDocumentsFault" message="HasRequiredDocumentsFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetMobilitySubApplicationCompletionId">
            <wsdl:documentation>Returns completion id from mobility application which is in the same envelope as application with selected completion id.
            </wsdl:documentation>
            <wsdl:input message="GetMobilitySubApplicationCompletionIdRequest"/>
            <wsdl:output message="GetMobilitySubApplicationCompletionIdResponse"/>
            <wsdl:fault name="GetMobilitySubApplicationCompletionIdFault" message="GetMobilitySubApplicationCompletionIdFault"/>
        </wsdl:operation>

        <wsdl:operation name="CompletionStateChange">
            <wsdl:documentation>OBS zapisuje do AMS výsledky jednotlivých kompletácií</wsdl:documentation>
            <wsdl:input message="CompletionStateChangeRequest"/>
            <wsdl:output message="CompletionStateChangeResponse"/>
            <wsdl:fault name="CompletionStateChangeFault" message="CompletionStateChangeFault"/>
        </wsdl:operation>

        <wsdl:operation name="CreatePriorityEvent">
            <wsdl:documentation>OBS creates priority events in AMS about upload documents etc.</wsdl:documentation>
            <wsdl:input message="CreatePriorityEventRequest"/>
            <wsdl:output message="CreatePriorityEventResponse"/>
            <wsdl:fault name="CreatePriorityEventFault" message="CreatePriorityEventFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetMortgageApplicationsStages">
            <wsdl:documentation>Returns mortgage refinance application stages of all active applications for given contract.</wsdl:documentation>
            <wsdl:input message="GetMortgageApplicationsStagesRequest"/>
            <wsdl:output message="GetMortgageApplicationsStagesResponse"/>
            <wsdl:fault name="GetMortgageApplicationsStagesFault" message="GetMortgageApplicationsStagesFault"/>
        </wsdl:operation>

        <wsdl:operation name="ValidateDeliveryDocumentsDate">
            <wsdl:documentation>Validate delivery documents date</wsdl:documentation>
            <wsdl:input message="ValidateDeliveryDocumentsDateRequest"/>
            <wsdl:output message="ValidateDeliveryDocumentsDateResponse"/>
            <wsdl:fault name="ValidateDeliveryDocumentsDateFault" message="ValidateDeliveryDocumentsDateFault"/>
        </wsdl:operation>

        <wsdl:operation name="NewContractActivated">
            <wsdl:documentation>General contract activation message from OBS</wsdl:documentation>
            <wsdl:input message="NewContractActivatedRequest"/>
            <wsdl:output message="NewContractActivatedResponse"/>
            <wsdl:fault name="NewContractActivatedFault" message="NewContractActivatedFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetDocumentContractsInEnvelope">
            <wsdl:documentation>Get uuid of documents in envelope</wsdl:documentation>
            <wsdl:input message="GetDocumentContractsInEnvelopeRequest"/>
            <wsdl:output message="GetDocumentContractsInEnvelopeResponse"/>
            <wsdl:fault name="GetDocumentContractsInEnvelopeFault" message="GetDocumentContractsInEnvelopeFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitDeliverMainIncomeDocumentViaAISP">
            <wsdl:input message="InitDeliverMainIncomeDocumentViaAISPRequest"/>
            <wsdl:output message="InitDeliverMainIncomeDocumentViaAISPResponse"/>
            <wsdl:fault name="InitDeliverMainIncomeDocumentViaAISPFault" message="InitDeliverMainIncomeDocumentViaAISPFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateDeliverMainIncomeDocumentViaAISP">
            <wsdl:input message="UpdateDeliverMainIncomeDocumentViaAISPRequest"/>
            <wsdl:output message="UpdateDeliverMainIncomeDocumentViaAISPResponse"/>
            <wsdl:fault name="UpdateDeliverMainIncomeDocumentViaAISPFault" message="UpdateDeliverMainIncomeDocumentViaAISPFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAispIncomeDocumentResult">
            <wsdl:input message="InitAispIncomeDocumentResultRequest"/>
            <wsdl:output message="InitAispIncomeDocumentResultResponse"/>
            <wsdl:fault name="InitAispIncomeDocumentResultFault" message="InitAispIncomeDocumentResultFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateAispIncomeDocumentResult">
            <wsdl:input message="UpdateAispIncomeDocumentResultRequest"/>
            <wsdl:output message="UpdateAispIncomeDocumentResultResponse"/>
            <wsdl:fault name="UpdateAispIncomeDocumentResultFault" message="UpdateAispIncomeDocumentResultFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetDocumentsUploadStatus">
            <wsdl:input message="GetDocumentsUploadStatusRequest"/>
            <wsdl:output message="GetDocumentsUploadStatusResponse"/>
            <wsdl:fault name="GetDocumentsUploadStatusFault" message="GetDocumentsUploadStatusFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitRejected">
            <wsdl:documentation>Operation used to initialize the rejectedTask.</wsdl:documentation>
            <wsdl:input message="InitRejectedRequest"/>
            <wsdl:output message="InitRejectedResponse"/>
            <wsdl:fault name="InitRejectedFault" message="InitRejectedFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitActivationPayment">
            <wsdl:input message="InitActivationPaymentRequest"/>
            <wsdl:output message="InitActivationPaymentResponse"/>
            <wsdl:fault name="InitActivationPaymentFault" message="InitActivationPaymentFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateActivationPayment">
            <wsdl:input message="UpdateActivationPaymentRequest"/>
            <wsdl:output message="UpdateActivationPaymentResponse"/>
            <wsdl:fault name="UpdateActivationPaymentFault" message="UpdateActivationPaymentFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitVictoryScreen">
            <wsdl:input message="InitVictoryScreenRequest"/>
            <wsdl:output message="InitVictoryScreenResponse"/>
            <wsdl:fault name="InitVictoryScreenFault" message="InitVictoryScreenFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateVictoryScreen">
            <wsdl:input message="UpdateVictoryScreenRequest"/>
            <wsdl:output message="UpdateVictoryScreenResponse"/>
            <wsdl:fault name="UpdateVictoryScreenFault" message="UpdateVictoryScreenFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitPairing">
            <wsdl:input message="InitPairingRequest"/>
            <wsdl:output message="InitPairingResponse"/>
            <wsdl:fault name="InitPairingFault" message="InitPairingFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdatePairing">
            <wsdl:input message="UpdatePairingRequest"/>
            <wsdl:output message="UpdatePairingResponse"/>
            <wsdl:fault name="UpdatePairingFault" message="UpdatePairingFault"/>
        </wsdl:operation>

        <wsdl:operation name="GeneralContractActivationAllowed">
            <wsdl:documentation>Check if contract activation allowed for envelopeId</wsdl:documentation>
            <wsdl:input message="GeneralContractActivationAllowedRequest"/>
            <wsdl:output message="GeneralContractActivationAllowedResponse"/>
            <wsdl:fault name="GeneralContractActivationAllowedFault" message="GeneralContractActivationAllowedFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="ApplicationFinalizationBinding" type="ApplicationFinalization">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitLoanSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitLoanSummaryFault">
                <soap:fault name="InitLoanSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateLoanSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateLoanSummaryFault">
                <soap:fault name="UpdateLoanSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitOverdraftSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitOverdraftSummaryFault">
                <soap:fault name="InitOverdraftSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateOverdraftSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateOverdraftSummaryFault">
                <soap:fault name="UpdateOverdraftSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitChangeMobilityDates">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitChangeMobilityDatesFault">
                <soap:fault name="InitChangeMobilityDatesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateChangeMobilityDates">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateChangeMobilityDatesFault">
                <soap:fault name="UpdateChangeMobilityDatesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverInternet">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignOverInternetFault">
                <soap:fault name="InitSignOverInternetFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignOverInternet">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignOverInternetFault">
                <soap:fault name="UpdateSignOverInternetFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignOverMobile">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignOverMobileFault">
                <soap:fault name="InitSignOverMobileFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignOverMobile">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignOverMobileFault">
                <soap:fault name="UpdateSignOverMobileFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignAtBranch">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignAtBranchFault">
                <soap:fault name="InitSignAtBranchFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranch">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignAtBranchFault">
                <soap:fault name="UpdateSignAtBranchFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitBlueSign">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBlueSignFault">
                <soap:fault name="InitBlueSignFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateBlueSign">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBlueSignFault">
                <soap:fault name="UpdateBlueSignFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitSignAtBranchByPwd">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignAtBranchByPwdFault">
                <soap:fault name="InitSignAtBranchByPwdFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranchByPwd">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignAtBranchByPwdFault">
                <soap:fault name="UpdateSignAtBranchByPwdFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitSignAtBranchBySms">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignAtBranchBySmsFault">
                <soap:fault name="InitSignAtBranchBySmsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignAtBranchBySms">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignAtBranchBySmsFault">
                <soap:fault name="UpdateSignAtBranchBySmsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitSignpad">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignpadFault">
                <soap:fault name="InitSignpadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignpad">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignpadFault">
                <soap:fault name="UpdateSignpadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>


        <wsdl:operation name="InitSignpadConfirm">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignpadConfirmFault">
                <soap:fault name="InitSignpadConfirmFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignpadConfirm">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignpadConfirmFault">
                <soap:fault name="UpdateSignpadConfirmFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignpadConfirmInternalCodeOnly">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignpadConfirmInternalCodeOnlyFault">
                <soap:fault name="UpdateSignpadConfirmInternalCodeOnlyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSignDifferently">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSignDifferentlyFault">
                <soap:fault name="InitSignDifferentlyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSignDifferently">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSignDifferentlyFault">
                <soap:fault name="UpdateSignDifferentlyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDocumentUpload">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDocumentUploadFault">
                <soap:fault name="InitDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDocumentUpload">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDocumentUploadFault">
                <soap:fault name="UpdateDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDeclaration">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDeclarationFault">
                <soap:fault name="InitDeclarationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDeclaration">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDeclarationFault">
                <soap:fault name="UpdateDeclarationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitNoDocumentUpload">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitNoDocumentUploadFault">
                <soap:fault name="InitNoDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateNoDocumentUpload">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateNoDocumentUploadFault">
                <soap:fault name="UpdateNoDocumentUploadFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

         <wsdl:operation name="GetCompletionIdForEnvelope">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCompletionIdForEnvelopeFault">
                <soap:fault name="GetCompletionIdForEnvelopeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetInProgressApplications">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetInProgressApplicationsFault">
                <soap:fault name="GetInProgressApplicationsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="HasRequiredDocuments">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="HasRequiredDocumentsFault">
                <soap:fault name="HasRequiredDocumentsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetMobilitySubApplicationCompletionId">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetMobilitySubApplicationCompletionIdFault">
                <soap:fault name="GetMobilitySubApplicationCompletionIdFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CompletionStateChange">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CompletionStateChangeFault">
                <soap:fault name="CompletionStateChangeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="CreatePriorityEvent">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CreatePriorityEventFault">
                <soap:fault name="CreatePriorityEventFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetMortgageApplicationsStages">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetMortgageApplicationsStagesFault">
                <soap:fault name="GetMortgageApplicationsStagesFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ValidateDeliveryDocumentsDate">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ValidateDeliveryDocumentsDateFault">
                <soap:fault name="ValidateDeliveryDocumentsDateFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="NewContractActivated">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="NewContractActivatedFault">
                <soap:fault name="NewContractActivatedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetDocumentContractsInEnvelope">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetDocumentContractsInEnvelopeFault">
                <soap:fault name="GetDocumentContractsInEnvelopeFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitDeliverMainIncomeDocumentViaAISP">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitDeliverMainIncomeDocumentViaAISPFault">
                <soap:fault name="InitDeliverMainIncomeDocumentViaAISPFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateDeliverMainIncomeDocumentViaAISP">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateDeliverMainIncomeDocumentViaAISPFault">
                <soap:fault name="UpdateDeliverMainIncomeDocumentViaAISPFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitAispIncomeDocumentResult">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAispIncomeDocumentResultFault">
                <soap:fault name="InitAispIncomeDocumentResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateAispIncomeDocumentResult">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateAispIncomeDocumentResultFault">
                <soap:fault name="UpdateAispIncomeDocumentResultFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetDocumentsUploadStatus">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetDocumentsUploadStatusFault">
                <soap:fault name="GetDocumentsUploadStatusFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitRejected">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitRejectedFault">
                <soap:fault name="InitRejectedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitActivationPayment">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitActivationPaymentFault">
                <soap:fault name="InitActivationPaymentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateActivationPayment">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateActivationPaymentFault">
                <soap:fault name="UpdateActivationPaymentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitVictoryScreen">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitVictoryScreenFault">
                <soap:fault name="InitVictoryScreenFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateVictoryScreen">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateVictoryScreenFault">
                <soap:fault name="UpdateVictoryScreenFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitPairing">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitPairingFault">
                <soap:fault name="InitPairingFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdatePairing">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdatePairingFault">
                <soap:fault name="UpdatePairingFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GeneralContractActivationAllowed">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
            <wsdl:fault name="GeneralContractActivationAllowedFault">
                <soap:fault name="GeneralContractActivationAllowedFault" use="literal" />
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="ApplicationFinalizationService">
        <wsdl:port name="ApplicationFinalizationPort" binding="ApplicationFinalizationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/finalization"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>