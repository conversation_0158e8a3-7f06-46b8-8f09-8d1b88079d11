<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" xmlns="http://airbank.cz/ams/ws/approvedocument"
           targetNamespace="http://airbank.cz/ams/ws/approvedocument">

    <xs:element name="VerifyBranchIdentificationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identificationDocument" type="IdentificationDocument">
                    <xs:annotation>
                        <xs:documentation>ID Document.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="citizenship" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Person citizenship country code.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="identifiedCuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Identified person CUID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="authenticatedCuid" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Authenticated person CUID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="identifiedBy" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Employee number of operator who identified person.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="identificationDate" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Person identification date.</xs:documentation>
                    </xs:annotation>
                </xs:element>
             </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="IdentificationDocument">
        <xs:sequence>
            <xs:sequence>
                <xs:element name="type" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The document type.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="number" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The document number, that identify document.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="VerifyBranchIdentificationResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approveStatus" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The status of document verification</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The description of the reason for rejection/cancellation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ApproveDocumentInCompletionRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="idDocument" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The delivered document id in AMS</xs:documentation>
                    </xs:annotation>
                </xs:element>
             </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ApproveDocumentInCompletionResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approveDocumentId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The id of lap job.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDocumentApproveStatusRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approveDocumentId" type="xs:long" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDocumentApproveStatusResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="approveStatus" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The status of document verification</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The description of the reason for rejection/cancellation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="OverwriteScoringResultRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long"  minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of document</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="newResult" type="DocumentScoringResult"  minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>New scoring result of document to overwrite</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="OverwriteScoringResultResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ApproveNoDebtDocumentRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long"  minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of document to approve</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ApproveNoDebtDocumentResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="DocumentScoringResult">
        <xs:restriction base="xs:string">
            <xs:enumeration value="APPROVE"/>
            <xs:enumeration value="REJECT"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>