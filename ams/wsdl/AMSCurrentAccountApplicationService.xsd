<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/currentaccount"
           targetNamespace="http://airbank.cz/ams/ws/application/currentaccount">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="processCode" type="appCommon:ProcessCodeTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Code of process.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAccountParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitAccountParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="possibleCurrencies" type="appCommon:CurrencyCodeTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of 3-letter currency ISO codes, in which user can create current account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardDesignOptions" type="appCommon:CardDesignTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of possible designs.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Default mailing address where to send card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="documentToExpireFlag" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Indicates, that the customer has a document, that is expired, or will expire soon.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="currentAccountsOwnerFlag" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Indicates, whether the customer owns a current account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="maxVirtualizedForContract" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Max virtualized cards for contract.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="maxDebitsCardForClientReached" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether client already has maximum number of allowed debit cards.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="accountParams" type="appCommon:AccountParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Account attributes</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardSelected" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a card has been chosen for the account being created.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="showWithdrawalsPackageOption" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether an option for withdrawal fee package should be visible.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="withdrawalsPackageSelected" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a withdrawal fee package was selected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="feePackagesConfirmed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether user confirmed activation of the fee packages.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="feePackageParams" type="appCommon:FeePackageParamsTO"  minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Fee packages parameters</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mobilitySelected" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a mobility has been chosen for the account being created.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardParams" type="appCommon:DebitCardParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Debit card params (if there is a debit card selected)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="showMainAccount" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether to show the main account checkbox.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mobilityAllowed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>it is allowed to choose mobility</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="overdraftAllowed" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>it is allowed to choose overdraft</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="interestFreeReserve" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if Overdraft without interest to a certain amount</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="interestFreeReserveAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Overdraft Amount without interest</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="maInstalled" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is exists any active MA device?</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAccountParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="accountParams" type="appCommon:AccountParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Account attributes</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardParams" type="appCommon:DebitCardParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Debit card params</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardSelected" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a card has been chosen for the account being created.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="mobilitySelected" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a mobility has been chosen for the account being created.</xs:documentation>
                            </xs:annotation>
                        </xs:element>

                        <xs:element name="moveCard" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a card has been chosen and should be moved from a different account application.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="moveMobility" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether a mobility has been chosen and should be moved from a different account application.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="withdrawalsPackageSelected" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether an unlimited withdrawal package was selected.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="feePackagesConfirmed" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>Flag indicating whether user confirmed activation of the fee packages.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateAccountParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>