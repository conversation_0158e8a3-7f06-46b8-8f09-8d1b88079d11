<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/disponent"
                  targetNamespace="http://airbank.cz/ams/ws/application/disponent">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/disponent">
            <xs:include schemaLocation="AMSDisponentApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Affidavit task -->
    <wsdl:message name="InitAffidavitRequest">
        <wsdl:part element="InitAffidavitRequest" name="InitAffidavitRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAffidavitResponse">
        <wsdl:part element="InitAffidavitResponse" name="InitAffidavitResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAffidavitFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAffidavitFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateAffidavitRequest">
        <wsdl:part element="UpdateAffidavitRequest" name="UpdateAffidavitRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateAffidavitResponse">
        <wsdl:part element="UpdateAffidavitResponse" name="UpdateAffidavitResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateAffidavitFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateAffidavitFault"/>
    </wsdl:message>

    <!-- Basic parameters task -->
    <wsdl:message name="InitBasicParametersRequest">
        <wsdl:part element="InitBasicParametersRequest" name="InitBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitBasicParametersResponse">
        <wsdl:part element="InitBasicParametersResponse" name="InitBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitBasicParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateBasicParametersRequest">
        <wsdl:part element="UpdateBasicParametersRequest" name="UpdateBasicParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateBasicParametersResponse">
        <wsdl:part element="UpdateBasicParametersResponse" name="UpdateBasicParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateBasicParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateBasicParametersFault"/>
    </wsdl:message>

    <!-- PersonalParameters task -->
    <wsdl:message name="InitPersonalParametersRequest">
        <wsdl:part element="InitPersonalParametersRequest" name="InitPersonalParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitPersonalParametersResponse">
        <wsdl:part element="InitPersonalParametersResponse" name="InitPersonalParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitPersonalParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitPersonalParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdatePersonalParametersRequest">
        <wsdl:part element="UpdatePersonalParametersRequest" name="UpdatePersonalParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdatePersonalParametersResponse">
        <wsdl:part element="UpdatePersonalParametersResponse" name="UpdatePersonalParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdatePersonalParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdatePersonalParametersFault"/>
    </wsdl:message>

    <!-- Rights task -->
    <wsdl:message name="InitRightsRequest">
        <wsdl:part element="InitRightsRequest" name="InitRightsRequest"/>
    </wsdl:message>
    <wsdl:message name="InitRightsResponse">
        <wsdl:part element="InitRightsResponse" name="InitRightsResponse"/>
    </wsdl:message>
    <wsdl:message name="InitRightsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitRightsFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateRightsRequest">
        <wsdl:part element="UpdateRightsRequest" name="UpdateRightsRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateRightsResponse">
        <wsdl:part element="UpdateRightsResponse" name="UpdateRightsResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateRightsFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateRightsFault"/>
    </wsdl:message>

    <!-- Login task -->
    <wsdl:message name="InitLoginRequest">
        <wsdl:part element="InitLoginRequest" name="InitLoginRequest"/>
    </wsdl:message>
    <wsdl:message name="InitLoginResponse">
        <wsdl:part element="InitLoginResponse" name="InitLoginResponse"/>
    </wsdl:message>
    <wsdl:message name="InitLoginFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitLoginFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateLoginRequest">
        <wsdl:part element="UpdateLoginRequest" name="UpdateLoginRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateLoginResponse">
        <wsdl:part element="UpdateLoginResponse" name="UpdateLoginResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateLoginFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateLoginFault"/>
    </wsdl:message>

    <!-- Application already disponent step init. This step des not have update-->
    <wsdl:message name="InitAlreadyDisponentRequest">
        <wsdl:part element="InitAlreadyDisponentRequest" name="InitAlreadyDisponentRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAlreadyDisponentResponse">
        <wsdl:part element="InitAlreadyDisponentResponse" name="InitAlreadyDisponentResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAlreadyDisponentFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAlreadyDisponentFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="DisponentApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for disponent.

                Generated business faults:
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous disponent application in the VERIFY status
                - Application.MaxNumberOfDisponents - The customer has already defined as many disponents as possible
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAffidavit">
            <wsdl:documentation>Operation used to initialize the affidavitTask.</wsdl:documentation>
            <wsdl:input message="InitAffidavitRequest"/>
            <wsdl:output message="InitAffidavitResponse"/>
            <wsdl:fault name="InitAffidavitFault" message="InitAffidavitFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateAffidavit">
            <wsdl:documentation>Operation used to update data for the affidavitTask.

                For transition next, following attributes are required:
                - consent - must be true
            </wsdl:documentation>
            <wsdl:input message="UpdateAffidavitRequest"/>
            <wsdl:output message="UpdateAffidavitResponse"/>
            <wsdl:fault name="UpdateAffidavitFault" message="UpdateAffidavitFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitBasicParameters">
            <wsdl:documentation>Operation used to initialize the basicParametersTask.</wsdl:documentation>
            <wsdl:input message="InitBasicParametersRequest"/>
            <wsdl:output message="InitBasicParametersResponse"/>
            <wsdl:fault name="InitBasicParametersFault" message="InitBasicParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateBasicParameters">
            <wsdl:documentation>Operation used to update data for the basicParametersTask.
                For all transitions following attributes are required:
                - salutation
                - firstName
                - lastName
                - phone
                - email

                Generated validation errors:
                - Application.EmailDiacriticsNotConfirmed - The email address contains diacritics and it is not confirmed by the user
                - Application.FirstNameIsNotUsual - The first name provided has not been found within usual names and it is not confirmed by the client
                - Application.LastNameIsNotUsual - The last name provided has not been found within usual names and it is not confirmed by the client
                - validation errors resulting from CIF validations of firstName, lastName, phone and email
            </wsdl:documentation>
            <wsdl:input message="UpdateBasicParametersRequest"/>
            <wsdl:output message="UpdateBasicParametersResponse"/>
            <wsdl:fault name="UpdateBasicParametersFault" message="UpdateBasicParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitPersonalParameters">
            <wsdl:documentation>Operation used to initialize the personalParametersTask.</wsdl:documentation>
            <wsdl:input message="InitPersonalParametersRequest"/>
            <wsdl:output message="InitPersonalParametersResponse"/>
            <wsdl:fault name="InitPersonalParametersFault" message="InitPersonalParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdatePersonalParameters">
            <wsdl:documentation>Operation used to update data for the personalParametersTask.
                For all transitions except for back, following attributes are required:
                - citizenship
                - selectedDocumentType
                - documentNumber
                - personalId or gender (one of these two must be filled)
                - dateOfBirth
                - placeOfBirth
                - documentIssuedBy
                - documentIssuedDate
                - permanentAddress

                Generated business faults:
                - Application.Contract.SalutationPersonalIdConflict - Gender information from salutation and from personal id number do not conform
                - Application.Contract.SalutationGenderConflict - Gender information from salutation and selected gender do not conform
                - Application.ApplicantIsNotOldEnough - Applicant is not old enough
                - Application.AddressNotInCz - Specified address is not in CZ
                - Application.AddressNotConfirmed - The other address does not conform to registers and it is not confirmed by the user.
                - Application.DocumentIssueDateIsInFuture - The document issue date is in future
                - validation codes coming from validations in CIF
            </wsdl:documentation>
            <wsdl:input message="UpdatePersonalParametersRequest"/>
            <wsdl:output message="UpdatePersonalParametersResponse"/>
            <wsdl:fault name="UpdatePersonalParametersFault" message="UpdatePersonalParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitRights">
            <wsdl:documentation>Operation used to initialize the rightsTask.</wsdl:documentation>
            <wsdl:input message="InitRightsRequest"/>
            <wsdl:output message="InitRightsResponse"/>
            <wsdl:fault name="InitRightsFault" message="InitRightsFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateRights">
            <wsdl:documentation>Operation used to update data for the rightsTask.

                Generated business faults:
                - DisponentApplication.NoRightGranted - Client has not granted any right to the disponent
            </wsdl:documentation>
            <wsdl:input message="UpdateRightsRequest"/>
            <wsdl:output message="UpdateRightsResponse"/>
            <wsdl:fault name="UpdateRightsFault" message="UpdateRightsFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitLogin">
            <wsdl:documentation>Operation used to initialize the loginTask.</wsdl:documentation>
            <wsdl:input message="InitLoginRequest"/>
            <wsdl:output message="InitLoginResponse"/>
            <wsdl:fault name="InitLoginFault" message="InitLoginFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateLogin">
            <wsdl:documentation>Operation used to update data for the loginTask.

                For next transition following attributes are required:
                - username
                - phone
                - email

                Generated business faults:
                - Application.Contract.UsernameExists - Username is already used by someone else

                Generated validation faults:
                - Application.Contract.InvalidUsername - Username contains invalid characters
                - Application.DuplicatePhone - The phone provided is already used by some other active user
                - Application.DuplicateEmail - The email provided is already used by some other active user
                - Application.EmailDiacriticsNotConfirmed - The email address contains diacritics and it is not confirmed by the user
            </wsdl:documentation>
            <wsdl:input message="UpdateLoginRequest"/>
            <wsdl:output message="UpdateLoginResponse"/>
            <wsdl:fault name="UpdateLoginFault" message="UpdateLoginFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAlreadyDisponent">
            <wsdl:documentation>Init of final step the application.</wsdl:documentation>
            <wsdl:input message="InitAlreadyDisponentRequest"/>
            <wsdl:output message="InitAlreadyDisponentResponse"/>
            <wsdl:fault name="InitAlreadyDisponentFault" message="InitAlreadyDisponentFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="DisponentApplicationBinding" type="DisponentApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitAffidavit">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAffidavitFault">
                <soap:fault name="InitAffidavitFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateAffidavit">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateAffidavitFault">
                <soap:fault name="UpdateAffidavitFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitBasicParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitBasicParametersFault">
                <soap:fault name="InitBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateBasicParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateBasicParametersFault">
                <soap:fault name="UpdateBasicParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitPersonalParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitPersonalParametersFault">
                <soap:fault name="InitPersonalParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdatePersonalParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdatePersonalParametersFault">
                <soap:fault name="UpdatePersonalParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitRights">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitRightsFault">
                <soap:fault name="InitRightsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateRights">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateRightsFault">
                <soap:fault name="UpdateRightsFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitLogin">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitLoginFault">
                <soap:fault name="InitLoginFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="UpdateLogin">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateLoginFault">
                <soap:fault name="UpdateLoginFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitAlreadyDisponent">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAlreadyDisponentFault">
                <soap:fault name="InitAlreadyDisponentFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="DisponentApplicationService">
        <wsdl:documentation>Service providing operations related to disponent application of an existing customer.

            The application has the following task IDs:
            - affidavitTask - corresponds to the screen where user checks the agreement
            - basicParametersTask - corresponds to the screen where user inputs basic personal data of the disponent
            - personalParametersTask - corresponds to the screen where user inputs other personal data of the disponent and disponent addresses
            - rightsTask - data about disponent rights which applicant grants
            - loginTask - usename of the disponent and disponent contact phone and email, final step
            - alreadyDisponentTask - just init, final step of the flow
            - generateDocumentationTask - application was approved, documentation could be generated
            - waitForWarningResultTask - AMS is waiting for the warning processing

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="DisponentApplicationPort" binding="DisponentApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/disponent"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>