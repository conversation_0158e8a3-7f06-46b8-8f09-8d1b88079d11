<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/interestrate"
           targetNamespace="http://airbank.cz/ams/ws/interestrate">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="GetLastLoanInterestRateEvaluationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Get last loan interest rate evaluation Request.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="applicationId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Application Id.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetLastLoanInterestRateEvaluationResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="interestRateEvaluation" type="LoanInterestRateEvaluation" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Found LoanInterestRateEvaluation.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="LoanInterestRateEvaluation">
        <xs:annotation>
            <xs:documentation>Loan interest rate evaluation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="inputInterestRateStandard" type="xs:decimal" minOccurs="0"/>
            <xs:element name="inputInterestRateBonus" type="xs:decimal" minOccurs="0"/>
            <xs:element name="outputInterestRateStandard" type="xs:decimal"/>
            <xs:element name="outputInterestRateBonus" type="xs:decimal"/>
            <xs:element name="ruleSet" type="LoanInterestRateRuleSet" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanInterestRateRuleSet">
        <xs:annotation>
            <xs:documentation>Loan interest rate rule set.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="code" type="xs:string"/>
            <xs:element name="name" type="xs:string"/>
            <xs:element name="interestRateDivergenceStandard" type="xs:decimal" minOccurs="0"/>
            <xs:element name="interestRateDivergenceBonus" type="xs:decimal" minOccurs="0"/>
            <xs:element name="interestRateFixStandard" type="xs:decimal" minOccurs="0"/>
            <xs:element name="interestRateFixBonus" type="xs:decimal" minOccurs="0"/>
            <xs:element name="usedMinInterestRateStandard" type="xs:boolean"/>
            <xs:element name="usedMinInterestRateBonus" type="xs:boolean"/>
            <xs:element name="rule" type="LoanInterestRateRule" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="LoanInterestRateRule">
        <xs:annotation>
            <xs:documentation>Loan interest rate rule.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string"/>
            <xs:element name="property" type="xs:string"/>
            <xs:element name="value" type="xs:string" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>