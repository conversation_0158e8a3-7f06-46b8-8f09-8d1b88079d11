<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/terminateaccount"
                  targetNamespace="http://airbank.cz/ams/ws/application/terminateaccount">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/terminateaccount">
            <xs:include schemaLocation="AMSTerminateAccountApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <wsdl:message name="InitTransferToAccountRequest">
        <wsdl:part element="InitTransferToAccountRequest" name="InitTransferToAccountRequest"/>
    </wsdl:message>
    <wsdl:message name="InitTransferToAccountResponse">
        <wsdl:part element="InitTransferToAccountResponse" name="InitTransferToAccountResponse"/>
    </wsdl:message>
    <wsdl:message name="InitTransferToAccountFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitTransferToAccountFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateTransferToAccountRequest">
        <wsdl:part element="UpdateTransferToAccountRequest" name="UpdateTransferToAccountRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateTransferToAccountResponse">
        <wsdl:part element="UpdateTransferToAccountResponse" name="UpdateTransferToAccountResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateTransferToAccountFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateTransferToAccountFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="TerminateAccountApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for termination of the given bank account.
                Uses cuid and idProfile from tracking context to identify customer and profile.

                Generated business faults:
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile.
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract.
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract.
                - Application.TermAccount.AccountNotFound - No account found for given accountId or current client is not owner of this account.
                - Application.TermAccount.RefusedByOBS - termination was refused by OBS
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitTransferToAccount">
            <wsdl:documentation>Setup target account for transfer of rest of money, taskId = "accountParametersTask".
                Generated business faults:
                - Application.TermAccount.AccountNotFound - No account found for given accountId or current client is not owner of this account.
            </wsdl:documentation>
            <wsdl:input message="InitTransferToAccountRequest"/>
            <wsdl:output message="InitTransferToAccountResponse"/>
            <wsdl:fault name="InitTransferToAccountFault" message="InitTransferToAccountFault"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateTransferToAccount">
            <wsdl:documentation>Setup target account for transfer of rest of money, taskId = "accountParametersTask".
                Generated business faults:
                - Application.TermAccount.LastAccountNotConfirmed - Terminated account act is last active account and the termination is not confirmed
            </wsdl:documentation>
            <wsdl:input message="UpdateTransferToAccountRequest"/>
            <wsdl:output message="UpdateTransferToAccountResponse"/>
            <wsdl:fault name="UpdateTransferToAccountFault" message="UpdateTransferToAccountFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="TerminateAccountApplicationBinding" type="TerminateAccountApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitTransferToAccount">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitTransferToAccountFault">
                <soap:fault name="InitTransferToAccountFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateTransferToAccount">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateTransferToAccountFault">
                <soap:fault name="UpdateTransferToAccountFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="TerminateAccountApplicationService">
        <wsdl:documentation>Service is providing operations related to existing customer account application.

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - SIZE_EXCEEDED - attribute length is outside allowed limits
            - INVALID_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="TerminateAccountApplicationPort" binding="TerminateAccountApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/ws"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>