<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/investmentcontract"
           targetNamespace="http://airbank.cz/ams/ws/application/investmentcontract">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNoteRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for investment contract application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitNoteResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing noteTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNoteRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of page for investment contract application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateNoteResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating noteTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionId" type="xs:long"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="TerminateInvestmentContractRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
                <xs:element name="investmentContractNumber" type="xs:string"/>
                <xs:element name="investmentAccount" type="xs:string"/>
                <xs:element name="reason" type="xs:string"/>
                <xs:element name="type" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="TerminateInvestmentContractResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GenerateInvestmentMeetingRecordRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long"/>
                <xs:element name="discussedClientNeeds" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Discussed the client's needs.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="whatSuitableForClient" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Discussed with the client what is suitable.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="discussedTopics" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Topics suitable for the client.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="contractSigned" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Sign a contract during the meeting.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="purchaseOrdered" type="PurchaseOrderTO">
                    <xs:annotation>
                        <xs:documentation>The client learned that the validity of his order will expire.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="note" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Additional notes on investment negotiations.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="whoRequestedMeeting" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Who requested the meeting?</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="GenerateInvestmentMeetingRecordResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="PurchaseOrderTO">
        <xs:sequence>
            <xs:element name="orders" type="OrderTO" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="adequacyConfirmation" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>We informed the client whether the instruction was reasonable to buy the investment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="expireConfirmation" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>The client learned that the validity of his order will expire.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="OrderTO">
        <xs:sequence>
            <xs:element name="type" type="xs:string"/>
            <xs:element name="name" type="xs:string"/>
            <xs:element name="volume" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
