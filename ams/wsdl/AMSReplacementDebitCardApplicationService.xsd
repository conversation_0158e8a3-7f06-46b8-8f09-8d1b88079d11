<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:appCommon="http://airbank.cz/ams/ws/application/common" elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/replacementdebitcard" targetNamespace="http://airbank.cz/ams/ws/application/replacementdebitcard">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedCard" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitReplacementDebitCardRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitReplacementDebitCardResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedCard" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateReplacementDebitCardRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="selectedCard" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateReplacementDebitCardResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitCardParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitCardParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractValidatedAttributeInitResponse">
                    <xs:sequence>
                        <xs:element name="mailingAddress" type="appCommon:AddressTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Default mailing address where to send card.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardDesignOptions" type="appCommon:CardDesignTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of possible designs.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardParams" type="appCommon:DebitCardParamsTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Debit card params</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardHolderIsOwner" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is card holder same client as account owner?</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cardHolderIsDisponent" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Is card holder disponent for the account?</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="technology" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Card (to be replaced) design type / technology. Required for correct labels in IB.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="myAirToGooglePay" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>myAirToGooglePay is a flag that displays information about not converting the MyAir token</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateCardParametersRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="cardParams" type="appCommon:DebitCardParamsTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Debit card params</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateCardParametersResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>
