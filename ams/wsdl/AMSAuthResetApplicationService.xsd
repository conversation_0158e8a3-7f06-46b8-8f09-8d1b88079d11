<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:bc="http://airbank.cz/ams/ws/backoffice/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/authreset"
           targetNamespace="http://airbank.cz/ams/ws/application/authreset">

    <xs:annotation>
        <xs:documentation>
            Types for Authentication reset application, see https://jira.abank.cz/browse/RVI-397
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/backoffice/common" schemaLocation="../xsd/BackofficeApplication.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client identification in CIF</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPhoneUsernameRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPhoneUsernameResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePhoneUsernameRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="previousPhoneNumber" type="appCommon:PhoneNumberTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Phone number, that client lost or ended.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="newPhoneNumber" type="appCommon:PhoneNumberTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client's new phone number.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="newUsername" type="appCommon:UsernameTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Client's new username.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePhoneUsernameResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="htmlDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of HTML document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="pdfDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of PDF document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="signatureType" type="appCommon:SignatureTypeTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Selected signature type (it must be either SIGN_PAD or BLUE_SIGN)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitBlueSignRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitBlueSignResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="htmlDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of HTML document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="pdfDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of PDF document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateBlueSignRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateBlueSignResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignpadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignpadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="htmlDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of HTML document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="pdfDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of PDF document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="signedDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>OBS ID of the document that was signed on signpad</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- signpad confirm task -->
    <xs:element name="InitSignpadConfirmRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignpadConfirmResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="htmlDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>ID of HTML document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="signedDocumentId" type="xs:string" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>OBS ID of the document that was signed on signpad</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadConfirmRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignpadConfirmResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchBySmsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSignAtBranchBySmsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="htmlDocumentId" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>ID of HTML document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="pdfDocumentId" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>ID of PDF document generated by OBS (IB will display it).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="optNumberToConfirmOn" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Optional applicant's phone number</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchBySmsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="initiatorCuid" type="xs:long">
                            <xs:annotation>
                                <xs:documentation>CUID of client that requested auth reset</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="optNumberToConfirmOn" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Optional applicant's phone number</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="authId" type="xs:string"  minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Authorization ID from CASE.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSignAtBranchBySmsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <!-- upload documents task -->
    <xs:element name="InitUploadDocumentsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitUploadDocumentsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="requiredDocumentType" type="appCommon:DocumentGroupTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Documents that are required, based on applicant's citizenship.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="signatureType" type="appCommon:SignatureTypeTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Signature type selected in SignAtBranch step</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Envelope ID, which we are uploading documents for</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Cuid of applicant, which we are uploading documents for</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateUploadDocumentsRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="documents" type="bc:DeliveredDocumentWithGroupTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Documents provided by client. One OTHER document, cotaining scanned signed papers, plus documents required in
                                    InitUploadDocuments step.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="authId" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Authorization ID</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateUploadDocumentsResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

</xs:schema>
