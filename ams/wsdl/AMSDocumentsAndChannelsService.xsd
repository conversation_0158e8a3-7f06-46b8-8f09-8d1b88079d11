<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/documents"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb" jxb:version="2.1"
           xmlns:ocr="http://airbank.cz/ams/ws/documents/ocr"
           xmlns:bc="http://airbank.cz/ams/ws/backoffice/common"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           targetNamespace="http://airbank.cz/ams/ws/documents">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/common/ws/fault" schemaLocation="../xsd/commonSoapFault.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/backoffice/common" schemaLocation="../xsd/BackofficeApplication.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/documents/ocr" schemaLocation="../xsd/RecognizeDocument.xsd"/>

    <xs:element name="GetRequiredDocumentsAndChannelsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="applicationId" type="xs:long" minOccurs="1" maxOccurs="1"/>
                </xs:choice>
                <xs:element name="filter" type="Filter" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Filter for reduce output list of allowed channels and required documents.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetRequiredDocumentsAndChannelsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="requiredDocuments" type="RequiredDocument" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of required documents.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="allowedChannels" type="AllowedChannel" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of allowed channnels.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateOrUpdateRequiredDocumentGroupRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="requiredDocumentGroup" type="RequiredDocumentGroup" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1"/>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateOrUpdateRequiredDocumentGroupResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="RemoveRequiredDocumentGroupsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="idList" type="xs:long" minOccurs="1" maxOccurs="unbounded"/>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="RemoveRequiredDocumentGroupsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AddAllowedChannelsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="allowChannels" type="AllowedChannel" minOccurs="1" maxOccurs="unbounded"/>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1"/>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AddAllowedChannelsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="RemoveAllowedChannelsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="unbounded"/>
                    <xs:element name="envelopeId" type="xs:long" minOccurs="0" maxOccurs="1"/>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="RemoveAllowedChannelsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllowedChannelsHistoryRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllowedChannelsHistoryResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="allowedChannels" type="AllowedChannel" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of allowed channnels.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="AuditedEntity" abstract="true">
        <xs:annotation>
            <xs:documentation>
                Parent of objects with common AMS audit data. Attribute names are kept the same like in BackofficeApplication.xsd for compatibility, although
                the names are quite ugly.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>PK in database, can be NULL (new unsaved object)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="createdTime" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Time of the object creation in DB</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Version of the object in DB</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyUserId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Cuid of user who modified this record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyManagerId" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>LDAP employee number of operator who modified this record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modifyTime" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Time of the record update.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deleted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Flag indicating whether is the record deleted or not.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="RequiredDocument">
        <xs:annotation>
            <xs:documentation>Allowed distribution for applications set.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AuditedEntity">
                <xs:sequence>
                    <xs:element name="productType" type="ProductType" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Definition of product type group (DEPOSIT or LOAN)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanBinFrom" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Loan amount from which is document required.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentCount" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>count of required documents of current document type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentGroupCount" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>count of required documents from document group.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentGroupDetail" type="appCommon:GroupRelation" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>information about association to employment (for which income type is document required)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentDeliveryWay" type="DeliveryWay" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>information about channel, that is allowed as delivery channel for required document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="documentType" type="xs:string" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="documentGroup" type="appCommon:GroupType" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="documentGroupOptional" type="xs:boolean">
                        <xs:annotation>
                            <xs:documentation>flag whether required document group is optional</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="RequiredDocumentGroup">
        <xs:annotation>
            <xs:documentation>Group of required documents, grouped by channel, product type and document group detail if exists</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="AuditedEntity">
                <xs:sequence>
                    <xs:element name="productType" type="ProductType" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Definition of product type group (DEPOSIT or LOAN)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanBinFrom" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Loan amount from which is document required.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="groupCount" type="xs:int" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>count of required documents from document group.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="groupType" type="appCommon:GroupType" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>type of document group</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="groupRelation" type="appCommon:GroupRelation" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>information about association to employment (for which income type is document required)</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="deliveryWay" type="DeliveryWay" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>information about channel, that is allowed as delivery channel for required document.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="documentTypes" type="DocumentTypeWithCount" minOccurs="1" maxOccurs="unbounded"/>
                    <xs:element name="fulfillmentStatus" type="appCommon:FulfillmentStatus" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>Fulfillment state of requirements.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="optional" type="xs:boolean"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="DocumentTypeWithCount">
        <xs:annotation>
            <xs:documentation>
                Document type with count of documents
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="documentType" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="documentCount" type="xs:int" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AllowedChannel">
        <xs:complexContent>
            <xs:extension base="AuditedEntity">
                <xs:sequence>
                    <xs:element name="deliveryWay" type="DeliveryWay" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>allowed channel for documentation delivery to Air/Bank</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="signatureWay" type="SignatureWay" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>allowed channel for signing contract/supplement.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="loanAmountTo" type="xs:long" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>permission for channel, only if cash loan amount is below this value. Valid only for loan applications.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="productType" type="ProductType" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>information about product type.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="documentType" type="ChannelDocumentType" minOccurs="0" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="ChannelDocumentType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="GENERAL_CONTRACT"/>
            <xs:enumeration value="CONTRACT_SUPPLEMENT"/>
            <xs:enumeration value="AFFIDAVIT"/>
            <xs:enumeration value="MORTGAGE_CONTRACT"/>
            <xs:enumeration value="MORTGAGE_APPLICATION"/>
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="INVESTMENT_CONTRACT"/>
            <xs:enumeration value="SPLITPAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ProductType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="DEPOSIT"/>
            <xs:enumeration value="CONSOLIDATION"/>
            <xs:enumeration value="AFFIDAVIT"/>
            <xs:enumeration value="CONTRACT_SUPPLEMENT"/>
            <xs:enumeration value="MORTGAGE"/>
            <xs:enumeration value="OVERDRAFT"/>
            <xs:enumeration value="PENSION"/>
            <xs:enumeration value="INSURANCE"/>
            <xs:enumeration value="SPLITPAYMENT"/>
            <xs:enumeration value="INVESTMENT"/>
            <xs:enumeration value="STOCK_ETF"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="DeliveryWay">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IB"/>
            <xs:enumeration value="BRANCH"/>
            <xs:enumeration value="POST"/>
            <xs:enumeration value="MESSENGER"/>
            <xs:enumeration value="ICC"/>
            <xs:enumeration value="SPB"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SignatureWay">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BLUE_SIGN"/>
            <xs:enumeration value="CALL_ID"/>
            <xs:enumeration value="PWD_OTP"/>
            <xs:enumeration value="SIGNPAD"/>
            <xs:enumeration value="PWD_SPB"/>
            <xs:enumeration value="PWD"/>
            <xs:enumeration value="OTP"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="UploadFinishedResult">
        <xs:restriction base="xs:string">
            <xs:enumeration value="EVENT_CREATED"/>
            <xs:enumeration value="REQUEST_CREATED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="Filter">
        <xs:sequence>
            <xs:element name="productTypes" type="ProductType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Same value is used for filtering allow channels and required document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="channelDocumentTypes" type="ChannelDocumentType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>For filtering allow channels. For example only for GENERAL_CONTRACT.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="deliveryWays" type="DeliveryWay" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>For filtering allow channels. For example only for IB.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Same value is used for filtering allow channels and required document. Value can be for main debtor or co-debtor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="loanBinRestriction" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>For allow channels it consider loanAmountTo to filter and for required document it consider loanbinFrom for filtering
                        documents.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="onlyLiveApplication" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Active application which is not deleted, canceled, rejected or its approve status is not REJECTED, FI_CANCEL and
                        CLIENT_CANCEL.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="CreateOrUpdateDeliveredDocumentsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documents" type="bc:DeliveredDocumentWithGroupTO" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>list of delivered documents to create/update with related document groups</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="CreateOrUpdateDeliveredDocumentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentIds" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>IDs of the delivered document in AMS</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDeliveredDocumentsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="filter" type="bc:DeliveredDocumentFilterTO" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>get delivered documents using filter</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDeliveredDocumentsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documents" type="bc:DeliveredDocumentWithGroupTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>response with delivered documents and associated document groups</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="documentIds" type="xs:long" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>list of ids of documents for pagination support</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDeliveredDocumentsByIdsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentIds" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>get delivered documents using document ids</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="includeCustomerInfo" type="xs:boolean" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            If set to true, service returns also customer information (name,surname,titles) based on cuid
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDeliveredDocumentsByIdsResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documents" type="bc:DeliveredDocumentWithGroupTO" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>response with delivered documents and associated document groups</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AddDeliveredDocumentAttachmentRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="barCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="documentId" type="xs:long" minOccurs="1" maxOccurs="1"/>
                </xs:choice>
                <xs:element name="dmsId" type="xs:string" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AddDeliveredDocumentAttachmentResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UploadDeliveredDocumentsFinishedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" minOccurs="1" maxOccurs="1" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UploadDeliveredDocumentsFinishedResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="result" type="UploadFinishedResult" minOccurs="1" maxOccurs="1" />
                <xs:element name="externalRelation" type="ExternalRelationTO" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="MergeDeliveredDocumentsRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentIdMergeTo" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of delivered document which will after merge hold all data.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="documentIdMergeFrom" type="xs:long" minOccurs="1" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Id of delivered document which will cease exist after merge and its data will
                            contain first document.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="MergeDeliveredDocumentsResponse">
        <xs:complexType>
            <xs:sequence>
                <!-- No payload -->
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignDeliveredDocumentsRequest">
        <xs:annotation>
            <xs:documentation>Assign/unassign delivered documents to operator to be able to verify the document.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="operatorId" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>LDAP id of operator</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="deliveredDocumentIds" type="xs:long" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of delivered document ids to assign</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignDeliveredDocumentsResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    
    <xs:element name="AssignNextDeliveredDocumentRequest">
        <xs:annotation>
            <xs:documentation>Assign the next delivered document from the queue for operator</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="AssignNextDeliveredDocumentResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deliveredDocumentId" type="xs:long" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Delivered document id</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cuid" type="xs:long" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Cuid related to the document</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllowedActionsWithDocumentRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long"  minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of document</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetAllowedActionsWithDocumentResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentActions" type="bc:ActionTO"  minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Available actions with document</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SetDeliveredDocumentMaritalStatusRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of document</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="maritalStatus" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Marital status</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SetDeliveredDocumentMaritalStatusResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateDeliveredIdentityDocumentRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Id of document</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="invalidationDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date of invalidation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="InvalidateDeliveredIdentityDocumentResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ReceiveRecognizedIdDocumentRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deliveredDocumentId" type="xs:long" minOccurs="0"/>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Optional envelope id this request is related to.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorMessage" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>If present, it is a global error. Specific errors are tracked within the pages.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="frontPage" type="ocr:Document" minOccurs="0"/>
                <xs:element name="backPage" type="ocr:Document" minOccurs="0"/>
                <xs:element name="ocrRequestOrigin" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Contains the value from OCR request. Can be used to identify the originator of the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="facePictureDmsId" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>DMS ID (uuid) of a face image if any was found on the document.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ReceiveRecognizedIdDocumentResponse">
        <xs:complexType/>
    </xs:element>

    <xs:element name="GetDocumentOCRDataRequest">
        <xs:annotation>
            <xs:appinfo>
                <jxb:class>
                    <jxb:javadoc>Request for OCR document data.</jxb:javadoc>
                </jxb:class>
            </xs:appinfo>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="deliveredDocumentId" type="xs:long">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Delivered document id for which OCR data should be retrieved.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Optional envelope id this request is related to.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetDocumentOCRDataResponse">
        <xs:annotation>
            <xs:appinfo>
                <jxb:class>
                    <jxb:javadoc>OCR data for the document.</jxb:javadoc>
                </jxb:class>
            </xs:appinfo>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="mergeStatus" type="ocr:MergeStatus"/>
                <xs:element name="documentData" type="ocr:MergedDocument" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>OCR Data. Can be empty if the status indicates a failure.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ReceiveVerifyFaceMatchResultRequest">
        <xs:annotation>
            <xs:appinfo>
                <jxb:class>
                    <jxb:javadoc>Face match result.</jxb:javadoc>
                </jxb:class>
            </xs:appinfo>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="correlationId" type="xs:long">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Delivered identification document group id.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Optional envelope id this request is related to.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
                <xs:element name="errorMessage" type="xs:string" minOccurs="0"/>
                <xs:element name="score" type="xs:decimal" minOccurs="0">
                    <xs:annotation>
                        <xs:appinfo>
                            <jxb:property>
                                <jxb:javadoc>Face match score. If not set, there was a processing error.</jxb:javadoc>
                            </jxb:property>
                        </xs:appinfo>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ReceiveVerifyFaceMatchResultResponse">
        <xs:complexType/>
    </xs:element>

    <xs:element name="GetIdentityDocumentNewIdentificationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>ID of delivered document.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetIdentityDocumentNewIdentificationResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="newIdentification" type="bc:NewIdentificationTO" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>New identification from delivered document.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateIdentityDocumentNewIdentificationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>ID of delivered document.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="newIdentification" type="bc:NewIdentificationTO">
                    <xs:annotation>
                        <xs:documentation>New identification to set.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateIdentityDocumentNewIdentificationResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UploadLastIdDocumentAtBranchToEnvelopeRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>ID of customer.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>ID of application envelope.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="UploadLastIdDocumentAtBranchToEnvelopeResponse">
        <xs:complexType>
            <xs:sequence>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ExternalRelationTO">
        <xs:sequence>
            <xs:element name="entityCode" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Entity type/code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="instanceId" type="xs:string">
                <xs:annotation>
                    <xs:documentation>ID of related entity.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
