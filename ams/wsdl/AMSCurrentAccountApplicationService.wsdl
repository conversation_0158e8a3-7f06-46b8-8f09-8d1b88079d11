<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/currentaccount"
                  targetNamespace="http://airbank.cz/ams/ws/application/currentaccount">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/currentaccount">
            <xs:include schemaLocation="AMSCurrentAccountApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- AccountParameters task -->
    <wsdl:message name="InitAccountParametersRequest">
        <wsdl:part element="InitAccountParametersRequest" name="InitAccountParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="InitAccountParametersResponse">
        <wsdl:part element="InitAccountParametersResponse" name="InitAccountParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="InitAccountParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitAccountParametersFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateAccountParametersRequest">
        <wsdl:part element="UpdateAccountParametersRequest" name="UpdateAccountParametersRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateAccountParametersResponse">
        <wsdl:part element="UpdateAccountParametersResponse" name="UpdateAccountParametersResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateAccountParametersFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateAccountParametersFault"/>
    </wsdl:message>

    <!-- Application cancel request-->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Query for application status -->
    <wsdl:message name="GetCurrentTaskRequest">
        <wsdl:part element="GetCurrentTaskRequest" name="GetCurrentTaskRequest"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskResponse">
        <wsdl:part element="GetCurrentTaskResponse" name="GetCurrentTaskResponse"/>
    </wsdl:message>
    <wsdl:message name="GetCurrentTaskFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="GetCurrentTaskFault"/>
    </wsdl:message>

    <wsdl:portType name="CurrentAccountApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>Starts a new application for current account of an existing customer. Uses cuid and idProfile from tracking context to identify
                customer and profile.

                Generated business faults:
                - Application.GeneralContract.NotFound - No general contract found for given CUID and idProfile
                - Application.GeneralContract.NotOwner - The given idProfile is not an owner contract
                - Application.GeneralContract.NotActive - The given idProfile is not an active contract
                - Application.PreviousUnfinishedExists - There is a previous current account application in the VERIFY status
                - Application.Account.CountLimitReached - The customer has already as many current accounts as possible
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitAccountParameters">
            <wsdl:documentation>Operation used to initialize the accountParametersTask.</wsdl:documentation>
            <wsdl:input message="InitAccountParametersRequest"/>
            <wsdl:output message="InitAccountParametersResponse"/>
            <wsdl:fault name="InitAccountParametersFault" message="InitAccountParametersFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateAccountParameters">
            <wsdl:documentation>Operation used to update data for the accountParametersTask.

                For all transitions except for back, following attributes are required:
                - accountParams.currencyCode
                - accountParams.accountName
                - cardSelected
                - mobilitySelected

                Generated business faults:
                - Application.PreviousUnfinishedExists - There is a previous application for card or mobility in the VERIFY status. To distinguish between card
                and mobility, the fault has an additional attribute (contractType), which contains either CREATE_DK or CREATE_MOBILITY.
                - Application.UnsignedApplicationExists - There is a previous unsigned application for card or mobility. To distinguish between card and
                mobility, the fault has an additional attribute (contractType), which contains either CREATE_DK or CREATE_MOBILITY.
                - Application.CurrentAccount.CardApplicationExists - Card was requested, but another card application for another account application exists and
                card application move was not requested.
                - Application.CurrentAccount.MobilityApplicationExists - Mobility was requested, but another mobility application for another account
                application exists and mobility application move was not requested.
            </wsdl:documentation>
            <wsdl:input message="UpdateAccountParametersRequest"/>
            <wsdl:output message="UpdateAccountParametersResponse"/>
            <wsdl:fault name="UpdateAccountParametersFault" message="UpdateAccountParametersFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <wsdl:documentation>Returns current task of the process.</wsdl:documentation>
            <wsdl:input message="GetCurrentTaskRequest"/>
            <wsdl:output message="GetCurrentTaskResponse"/>
            <wsdl:fault name="GetCurrentTaskFault" message="GetCurrentTaskFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="CurrentAccountApplicationBinding" type="CurrentAccountApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitAccountParameters">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitAccountParametersFault">
                <soap:fault name="InitAccountParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateAccountParameters">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateAccountParametersFault">
                <soap:fault name="UpdateAccountParametersFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="GetCurrentTask">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="GetCurrentTaskFault">
                <soap:fault name="GetCurrentTaskFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

    </wsdl:binding>

    <wsdl:service name="CurrentAccountApplicationService">
        <wsdl:documentation>Service providing operations related to current account application of an existing customer.

            The application has the following task IDs:
            - accountParametersTask - corresponds to the initial screen, where user specifies account attributes
            - generateDocumentationTask - application was approved, documentation could be generated
            - waitForWarningResultTask - AMS is waiting for the warning processing

            All the init and update methods generate the following business faults:
            - Application.EnvelopeNotFound - no application was found for the given envelopeId
            - Application.EnvelopeConcurrentModification - application was found, but it has different version than expected (it was concurrently modified by a
            different process)

            All the update methods can generate validation faults with the following validation result codes:
            - IS_REQUIRED - attribute it null, but it should be non-null
            - INVALID_LENGHT - attribute length is outside allowed limits
            - WRONG_FORMAT - attribute value does not conform to the attribute format specified using regular expression (typically this means, that the value
            contains invalid characters)
        </wsdl:documentation>
        <wsdl:port name="CurrentAccountApplicationPort" binding="CurrentAccountApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/currentaccount"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>