<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/stocketf"
           targetNamespace="http://airbank.cz/ams/ws/application/stocketf">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:complexType name="InstrumentOffer">
        <xs:sequence>
            <xs:element name="businessCode" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="businessName" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SourceOfIncome">
        <xs:sequence>
            <xs:element name="sourceOfIncomeValues" type="SourceOfIncomeElement" minOccurs="1" maxOccurs="unbounded">
            </xs:element>
            <xs:element name="description" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SourceOfIncomeElement">
        <xs:sequence>
            <xs:element name="name" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="code" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="UnpersonalizedDocument">
        <xs:sequence>
            <xs:element name="documentType" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="validFrom" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:dateTime">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="validTo" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:dateTime">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dmsId" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ClientConsent">
        <xs:sequence>
            <xs:element name="clientConsentType" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="clientConsentValue" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="validFrom" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="validTo" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="maShortText" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="maLongText" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPromoRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitPromoResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="top20Instruments" type="xs:boolean" minOccurs="0" maxOccurs="1">
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePromoRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="top20Instruments" type="xs:boolean" minOccurs="1" maxOccurs="1">
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdatePromoResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitInstrumentOfferRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitInstrumentOfferResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="instrumentOffer" type="InstrumentOffer" minOccurs="1" maxOccurs="unbounded">
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateInstrumentOfferRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateInstrumentOfferResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitApprovalProcessRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitApprovalProcessResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateApprovalProcessRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateApprovalProcessResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitSummaryResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="clientConsents" type="ClientConsent" minOccurs="0" maxOccurs="unbounded">
                        </xs:element>
                        <xs:element name="unpersonalizedDocuments" type="UnpersonalizedDocument" minOccurs="0" maxOccurs="unbounded">
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="clientConsents" type="ClientConsent" minOccurs="1" maxOccurs="unbounded">
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateSummaryResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractResumeApplicationResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitRejectedRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitRejectedResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractRejectedResponse">
                    <xs:sequence>
                        <xs:element name="consentLockDate" type="xs:date" minOccurs="0" maxOccurs="1">
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="TerminateStockContractRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="contractNumber" type="xs:string" minOccurs="1" maxOccurs="1"/>
                <xs:element name="termination" type="Termination" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="TerminateStockContractResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="Termination">
        <xs:sequence>
            <xs:element name="way" type="TerminationWay" maxOccurs="1" minOccurs="1"/>
            <xs:element name="reason" type="TerminationReason" maxOccurs="1" minOccurs="1"/>
            <xs:element name="requestedDate" minOccurs="1" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="TerminationWay">
        <xs:restriction base="xs:string">
            <xs:enumeration value="WITHDRAWAL_BY_CLIENT"/>
            <xs:enumeration value="WITHDRAWAL_BY_BANK"/>
            <xs:enumeration value="TERMINATION_BY_CLIENT"/>
            <xs:enumeration value="TERMINATION_BY_BANK"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TerminationReason">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEATH"/>
            <xs:enumeration value="AML"/>
            <xs:enumeration value="OTHER_VIOLATION_BY_CLIENT"/>
            <xs:enumeration value="CLIENT_REQUEST"/>
            <xs:enumeration value="IBKR_VIOLATION_BY_CLIENT"/>
            <xs:enumeration value="IBKR_REJECT"/>
            <xs:enumeration value="IBKR_NOT_ACTIVE"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>