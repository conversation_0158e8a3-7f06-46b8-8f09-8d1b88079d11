<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:commonFault="http://airbank.cz/common/ws/fault"
                  xmlns="http://airbank.cz/ams/ws/application/stocketf"
                  targetNamespace="http://airbank.cz/ams/ws/application/stocketf">
    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/ams/ws/application/stocketf">
            <xs:include schemaLocation="AMSStockEtfApplicationService.xsd"/>
        </xs:schema>
        <xs:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xs:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="StartRequest">
        <wsdl:part element="StartRequest" name="StartRequest"/>
    </wsdl:message>
    <wsdl:message name="StartResponse">
        <wsdl:part element="StartResponse" name="StartResponse"/>
    </wsdl:message>
    <wsdl:message name="StartFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="StartFault"/>
    </wsdl:message>

    <!-- Promo task -->
    <wsdl:message name="InitPromoRequest">
        <wsdl:part element="InitPromoRequest" name="InitPromoRequest"/>
    </wsdl:message>
    <wsdl:message name="InitPromoResponse">
        <wsdl:part element="InitPromoResponse" name="InitPromoResponse"/>
    </wsdl:message>
    <wsdl:message name="InitPromoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitPromoFault"/>
    </wsdl:message>

    <wsdl:message name="UpdatePromoRequest">
        <wsdl:part element="UpdatePromoRequest" name="UpdatePromoRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdatePromoResponse">
        <wsdl:part element="UpdatePromoResponse" name="UpdatePromoResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdatePromoFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdatePromoFault"/>
    </wsdl:message>

    <!-- InstrumentOffer task -->
    <wsdl:message name="InitInstrumentOfferRequest">
        <wsdl:part element="InitInstrumentOfferRequest" name="InitInstrumentOfferRequest"/>
    </wsdl:message>
    <wsdl:message name="InitInstrumentOfferResponse">
        <wsdl:part element="InitInstrumentOfferResponse" name="InitInstrumentOfferResponse"/>
    </wsdl:message>
    <wsdl:message name="InitInstrumentOfferFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitInstrumentOfferFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateInstrumentOfferRequest">
        <wsdl:part element="UpdateInstrumentOfferRequest" name="UpdateInstrumentOfferRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateInstrumentOfferResponse">
        <wsdl:part element="UpdateInstrumentOfferResponse" name="UpdateInstrumentOfferResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateInstrumentOfferFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateInstrumentOfferFault"/>
    </wsdl:message>

    <!-- ApprovalProcess task -->
    <wsdl:message name="InitApprovalProcessRequest">
        <wsdl:part element="InitApprovalProcessRequest" name="InitApprovalProcessRequest"/>
    </wsdl:message>
    <wsdl:message name="InitApprovalProcessResponse">
        <wsdl:part element="InitApprovalProcessResponse" name="InitApprovalProcessResponse"/>
    </wsdl:message>
    <wsdl:message name="InitApprovalProcessFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitApprovalProcessFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateApprovalProcessRequest">
        <wsdl:part element="UpdateApprovalProcessRequest" name="UpdateApprovalProcessRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateApprovalProcessResponse">
        <wsdl:part element="UpdateApprovalProcessResponse" name="UpdateApprovalProcessResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateApprovalProcessFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateApprovalProcessFault"/>
    </wsdl:message>

    <!-- Summary task -->
    <wsdl:message name="InitSummaryRequest">
        <wsdl:part element="InitSummaryRequest" name="InitSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryResponse">
        <wsdl:part element="InitSummaryResponse" name="InitSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="InitSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitSummaryFault"/>
    </wsdl:message>

    <wsdl:message name="UpdateSummaryRequest">
        <wsdl:part element="UpdateSummaryRequest" name="UpdateSummaryRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryResponse">
        <wsdl:part element="UpdateSummaryResponse" name="UpdateSummaryResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateSummaryFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="UpdateSummaryFault"/>
    </wsdl:message>

    <!-- Request to resume loan application-->
    <wsdl:message name="ResumeApplicationRequest">
        <wsdl:part element="ResumeApplicationRequest" name="ResumeApplicationRequest"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationResponse">
        <wsdl:part element="ResumeApplicationResponse" name="ResumeApplicationResponse"/>
    </wsdl:message>
    <wsdl:message name="ResumeApplicationFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="ResumeApplicationFault"/>
    </wsdl:message>

    <!--  Application cancel request -->
    <wsdl:message name="CancelRequest">
        <wsdl:part element="CancelRequest" name="CancelRequest"/>
    </wsdl:message>
    <wsdl:message name="CancelResponse">
        <wsdl:part element="CancelResponse" name="CancelResponse"/>
    </wsdl:message>
    <wsdl:message name="CancelFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="CancelFault"/>
    </wsdl:message>

    <!-- Application rejected task -->
    <wsdl:message name="InitRejectedRequest">
        <wsdl:part element="InitRejectedRequest" name="InitRejectedRequest"/>
    </wsdl:message>
    <wsdl:message name="InitRejectedResponse">
        <wsdl:part element="InitRejectedResponse" name="InitRejectedResponse"/>
    </wsdl:message>
    <wsdl:message name="InitRejectedFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="InitRejectedFault"/>
    </wsdl:message>

    <wsdl:message name="TerminateStockContractRequest">
        <wsdl:part element="TerminateStockContractRequest" name="TerminateStockContractRequest"/>
    </wsdl:message>
    <wsdl:message name="TerminateStockContractResponse">
        <wsdl:part element="TerminateStockContractResponse" name="TerminateStockContractResponse"/>
    </wsdl:message>
    <wsdl:message name="TerminateStockContractFault">
        <wsdl:part element="commonFault:CoreFaultElement" name="TerminateStockContractFault"/>
    </wsdl:message>

    <wsdl:portType name="StockEtfApplication">
        <wsdl:operation name="Start">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="StartRequest"/>
            <wsdl:output message="StartResponse"/>
            <wsdl:fault name="StartFault" message="StartFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitPromo">
            <wsdl:input message="InitPromoRequest"/>
            <wsdl:output message="InitPromoResponse"/>
            <wsdl:fault name="InitPromoFault" message="InitPromoFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdatePromo">
            <wsdl:input message="UpdatePromoRequest"/>
            <wsdl:output message="UpdatePromoResponse"/>
            <wsdl:fault name="UpdatePromoFault" message="UpdatePromoFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitInstrumentOffer">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="InitInstrumentOfferRequest"/>
            <wsdl:output message="InitInstrumentOfferResponse"/>
            <wsdl:fault name="InitInstrumentOfferFault" message="InitInstrumentOfferFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateInstrumentOffer">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="UpdateInstrumentOfferRequest"/>
            <wsdl:output message="UpdateInstrumentOfferResponse"/>
            <wsdl:fault name="UpdateInstrumentOfferFault" message="UpdateInstrumentOfferFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitApprovalProcess">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="InitApprovalProcessRequest"/>
            <wsdl:output message="InitApprovalProcessResponse"/>
            <wsdl:fault name="InitApprovalProcessFault" message="InitApprovalProcessFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateApprovalProcess">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="UpdateApprovalProcessRequest"/>
            <wsdl:output message="UpdateApprovalProcessResponse"/>
            <wsdl:fault name="UpdateApprovalProcessFault" message="UpdateApprovalProcessFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="InitSummaryRequest"/>
            <wsdl:output message="InitSummaryResponse"/>
            <wsdl:fault name="InitSummaryFault" message="InitSummaryFault"/>
        </wsdl:operation>
        <wsdl:operation name="UpdateSummary">
            <wsdl:documentation>
                ToDo
            </wsdl:documentation>
            <wsdl:input message="UpdateSummaryRequest"/>
            <wsdl:output message="UpdateSummaryResponse"/>
            <wsdl:fault name="UpdateSummaryFault" message="UpdateSummaryFault"/>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <wsdl:documentation>Cancels current application (envelope).</wsdl:documentation>
            <wsdl:input message="CancelRequest"/>
            <wsdl:output message="CancelResponse"/>
            <wsdl:fault name="CancelFault" message="CancelFault"/>
        </wsdl:operation>

        <wsdl:operation name="InitRejected">
            <wsdl:documentation>Initializes Reject for current application (envelope).</wsdl:documentation>
            <wsdl:input message="InitRejectedRequest"/>
            <wsdl:output message="InitRejectedResponse"/>
            <wsdl:fault name="InitRejectedFault" message="InitRejectedFault"/>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <wsdl:input message="ResumeApplicationRequest"/>
            <wsdl:output message="ResumeApplicationResponse"/>
            <wsdl:fault name="ResumeApplicationFault" message="ResumeApplicationFault"/>
        </wsdl:operation>

        <wsdl:operation name="TerminateStockContract">
            <wsdl:input message="TerminateStockContractRequest"/>
            <wsdl:output message="TerminateStockContractResponse"/>
            <wsdl:fault name="TerminateStockContractFault" message="TerminateStockContractFault"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="StockEtfApplicationBinding" type="StockEtfApplication">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

        <wsdl:operation name="Start">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="StartFault">
                <soap:fault name="StartFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitPromo">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitPromoFault">
                <soap:fault name="InitPromoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdatePromo">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdatePromoFault">
                <soap:fault name="UpdatePromoFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitInstrumentOffer">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitInstrumentOfferFault">
                <soap:fault name="InitInstrumentOfferFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateInstrumentOffer">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateInstrumentOfferFault">
                <soap:fault name="UpdateInstrumentOfferFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitApprovalProcess">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitApprovalProcessFault">
                <soap:fault name="InitApprovalProcessFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateApprovalProcess">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateApprovalProcessFault">
                <soap:fault name="UpdateApprovalProcessFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitSummary">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitSummaryFault">
                <soap:fault name="InitSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="UpdateSummary">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateSummaryFault">
                <soap:fault name="UpdateSummaryFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="Cancel">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="CancelFault">
                <soap:fault name="CancelFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="InitRejected">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="InitRejectedFault">
                <soap:fault name="InitRejectedFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="ResumeApplication">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ResumeApplicationFault">
                <soap:fault name="ResumeApplicationFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>

        <wsdl:operation name="TerminateStockContract">
            <soap:operation soapAction=""/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="TerminateStockContractFault">
                <soap:fault name="TerminateStockContractFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="StockEtfApplicationService">
        <wsdl:documentation>
            ToDo
        </wsdl:documentation>
        <wsdl:port name="StockEtfApplicationPort" binding="StockEtfApplicationBinding">
            <soap:address location="http://TO-BE-SPECIFIED/ams/application/stocketf"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>