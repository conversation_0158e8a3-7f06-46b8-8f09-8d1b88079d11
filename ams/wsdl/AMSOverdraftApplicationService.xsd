<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:loanCommon="http://airbank.cz/ams/ws/application/common/loan"
           xmlns:overdraftCommon="http://airbank.cz/ams/ws/application/common/overdraft"
           xmlns:applicationDataCommon="http://airbank.cz/ams/ws/application/common/applicationdata"

           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/overdraft"
           targetNamespace="http://airbank.cz/ams/ws/application/overdraft">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/overdraft" schemaLocation="../xsd/OverdraftApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/loan" schemaLocation="../xsd/LoanApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common/applicationdata" schemaLocation="../xsd/ApplicationDataCommon.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="internalCode" type="xs:string" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Branch officer internal code.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftParametersRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for overdraft application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">

                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftParametersResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing overdraftParametersTask screen.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="accounts" type="appCommon:AccountTO" minOccurs="1" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>List of existing client CZK accounts.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="productMax" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Maximum amount of overdraft.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="productMin" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Minimum amount of overdraft.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="preselectedAmount" type="appCommon:MonetaryAmountTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Preselected amount of overdraft.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="preselectedAccountId" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of preselected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="previousLoanApplicationFound" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Indicates that previous application has been found (to reuse app data).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="loanReuseData" type="loanCommon:InitLoanDataReuseResponseCommon" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Data about available loan application suitable for application data reuse.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="requestType" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Type of special case.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="requestDescription" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Note to special case.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="interestRateFuture" type="overdraftCommon:OverdraftInterestRate" minOccurs="0" maxOccurs="1"/>
                        <xs:element name="majorIncomeEconomicalStatus" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>code for type of current Income.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="netIncome" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Net income from the employment</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="recognisedIncomeConfirmationRequested" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Indicates that unconfirmed recognised income for the client has been identified and it's confirmation is requested.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="recognisedIncomes" type="applicationDataCommon:RecognisedIncomeTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Last recognised incomes.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="recognisedIncomeUsedAmount" type="xs:decimal" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    Indicates that recognised income was confirmed by the customer few steps before and this amount was used as new "net income".
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="applicationDataRequiredType" type="appCommon:ApplicationDataRequiredTypeTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Type of application data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="interestFreeReserve" type="xs:boolean">
                            <xs:annotation>
                                <xs:documentation>True if Overdraft without interest to a certain amount</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="interestFreeReserveAmount" type="appCommon:MonetaryAmountTO" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Overdraft Amount without interest</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftParametersRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for overdraft application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="selectedAccount" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of selected account.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="overdraftAmount" type="appCommon:MonetaryAmountTO" minOccurs="1"
                                    maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Requested amount of overdraft.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="majorIncomeEconomicalStatus" type="xs:int" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>code for type of current Income.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="netIncome" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Net income from the employment</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="lowIncomeConfirmed" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>When the income is low (low is defined by system parameter), client must send true as a confirmation
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="recognisedIncomesConfirmations" type="applicationDataCommon:RecognisedIncomeConfirmationTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Client changed statuses of recognised incomes.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="requestType" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Type of special case.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="requestDescription" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Note to special case.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftParametersResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating overdraftParametersTask screen.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftAcceptedRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of view of scoring result</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftAcceptedResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Overdraft accepted with full amount, sends the amount.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="overdraftCommon:InitOverdraftAcceptedResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="acceptedType" type="overdraftCommon:OverdraftAcceptedTypeTO" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="summaryData" type="overdraftCommon:InitOverdraftSummaryResponseCommon" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init summary data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftAcceptedRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of show approval page.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="newOverdraftAmount" type="appCommon:MonetaryAmountTO" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>New amount of overdraft requested by client</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="backwardCompatibility" type="xs:boolean" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Temporary solution. Remove it in future release.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftAcceptedResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned on update of show approval page.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="htmlBinDocumentId" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of HTML version of preagreement document.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="pdfBinDocumentId" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Id of PDF version of preagreement document.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of overdraft summary.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitOverdraftSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Sends the important summary parameters.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="overdraftCommon:InitOverdraftSummaryResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftSummaryRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of summary.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateOverdraftSummaryResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned on summary update.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>

                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Id of the envelope containing application.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ResumeApplicationResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractResumeApplicationResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitCampaignPromoRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of campaign promo.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitCampaignPromoResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Sends the campaign promo parameters.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="data" type="overdraftCommon:InitCampaignPromoResponseCommon" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Init data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateCampaignPromoRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of campaign promo.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateCampaignPromoResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned on campaign promo update.
                </xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>

                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
</xs:schema>