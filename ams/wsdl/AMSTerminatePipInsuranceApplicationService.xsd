<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:auth="http://airbank.cz/ams/ws/application/authorization/"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/application/terminatepipinsurance"
           targetNamespace="http://airbank.cz/ams/ws/application/terminatepipinsurance">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>
    <xs:import namespace="http://airbank.cz/ams/ws/application/authorization/" schemaLocation="../xsd/Authorization.xsd"/>

    <xs:element name="StartRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartRequest">
                    <xs:sequence>
                        <xs:element name="insuranceContractId" type="xs:long" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Insurance identification in INS</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="StartResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractStartResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitConfirmationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Initialization of first page for terminate pip insurance application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitConfirmationResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when initializing ConfirmationTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateConfirmationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Update of first page for terminate pip insurance application.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:element name="authentication" type="auth:AuthType">
                            <xs:annotation>
                                <xs:documentation>Solved OBS authentication.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateConfirmationResponse">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Response data returned when updating ConfirmationTask screen.</xs:documentation>
            </xs:annotation>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="CancelResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractCancelResponse"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
</xs:schema>