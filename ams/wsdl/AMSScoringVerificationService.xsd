<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:common="http://airbank.cz/ams/ws/scoring/verification/common"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/scoring/verification"
           targetNamespace="http://airbank.cz/ams/ws/scoring/verification">

    <xs:import namespace="http://airbank.cz/ams/ws/scoring/verification/common" schemaLocation="../xsd/ScoringVerification.xsd"/>

    <xs:element name="DocumentVerificationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>Information about results for approval process, which was requested by primary system.</xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="idWorkflow" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Unique identifier of approval in LAP system</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="approvalProcessType" type="common:ApprovalProcessType" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Type of verification process</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="result" type="xs:string" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Type of verification process</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="rejectReason" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The description of the reason for rejection/cancellation</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="DocumentVerificationResponse">
        <xs:annotation>
            <xs:documentation>Empty confirmation message. Should not throw any exception otherwise is something wrong.</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

    <xs:element name="ProcessApplicationScoringResultRequest">
        <xs:annotation>
            <xs:documentation>Scoring data collected by asynchronous LAP process. Contains scoring results based on LAP Vector specification.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="common:ApplicationScoringResultRequest"/>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessApplicationScoringResultResponse">
        <xs:annotation>
            <xs:documentation>Empty confirmation message. Should not throw any exception otherwise is something wrong. (Will be spec.)</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

    <xs:element name="ProcessScoringNotificationRequest">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>LAP notification message that notifies listener about finished automatic scoring and started new manual scoring
                </xs:documentation>
            </xs:annotation>
            <xs:sequence>
                <xs:element name="workflowId" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Attribute for asynchronous 'response' matching (newContractId eq workFlowId)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="idApplication" type="xs:long" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Unique application's identifier</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="status" type="ProcessStatus" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>type of started manual scoring (WAITING)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="ProcessStatus">
        <xs:restriction base="xs:string">
            <xs:enumeration value="WAITING"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="ProcessScoringNotificationResponse">
        <xs:annotation>
            <xs:documentation>Empty confirmation message. Should not throw any exception otherwise is something wrong. (Will be spec.)</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

    <xs:element name="ProcessCompletionPreDrawnCheckResultRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="workflowId" type="xs:long" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Attribute for asynchronous 'response' matching (newContractId eq workFlowId)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="checkedApplicationId" type="xs:long" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Unique application's identifier of checked application</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="warning" type="common:Warning" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>List of warnings</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="type" type="common:ApprovalProcessResultType" maxOccurs="1" minOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Status of LAP approval process procesing.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessCompletionPreDrawnCheckResultResponse">
        <xs:annotation>
            <xs:documentation>Empty confirmation message. Should not throw any exception otherwise is something wrong. (Will be spec.)</xs:documentation>
        </xs:annotation>
        <xs:complexType/>
    </xs:element>

</xs:schema>