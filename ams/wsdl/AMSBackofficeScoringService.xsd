<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/backoffice/scoring"
           targetNamespace="http://airbank.cz/ams/ws/backoffice/scoring">

    <xs:include schemaLocation="../xsd/BackofficeScoring.xsd"/>

    <xs:element name="OverwriteScoringResultRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="envelopeId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Envelope ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="newResult" type="ScoringResultTO">
                    <xs:annotation>
                        <xs:documentation>New scoring result</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="rejectReasonCode" type="xs:string" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Reject reason code in case new scoring result is REJECT</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="OverwriteScoringResultResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

</xs:schema>
