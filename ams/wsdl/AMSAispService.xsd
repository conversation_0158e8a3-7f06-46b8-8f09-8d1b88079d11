<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:appCommon="http://airbank.cz/ams/ws/application/common"
           xmlns:jxb="http://java.sun.com/xml/ns/jaxb"
           elementFormDefault="qualified"
           jxb:version="2.1"
           xmlns="http://airbank.cz/ams/ws/application/common/aisp"
           targetNamespace="http://airbank.cz/ams/ws/application/common/aisp">

    <xs:annotation>
        <xs:documentation>
            Common types of application interface
        </xs:documentation>
    </xs:annotation>
    <xs:import namespace="http://airbank.cz/ams/ws/application/common" schemaLocation="../xsd/ApplicationCommon.xsd"/>

    <xs:element name="InitDeliverDocumentViaAISPBeforeScoringRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDeliverDocumentViaAISPBeforeScoringResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="otherIncomeDocumentRequired" type="xs:boolean" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>If true, a document for other income is required</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="bank" type="BankTO" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Banks supporting document delivery via AISP</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="BankTO">
        <xs:sequence>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Bank name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="code" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Bank code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="preferredInAISPView" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Whether bank should be visible in preferred view</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="AISPViewOrder" type="xs:int" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Display order</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="logoFileGUID" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>File GUID of bank logo</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pairedAccount" type="AccountTO" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Paired accounts</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountTO">
        <xs:sequence>
            <xs:element name="id" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Identifier of permanently paired account in PAPUCA</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Account name</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="number" type="AccountNumberTO">
                <xs:annotation>
                    <xs:documentation>Account number</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="disconnected" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Flag whether account is disconnected</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AccountNumberTO">
        <xs:sequence>
            <xs:element name="number" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Account number</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="prefix" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Account number prefix</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="bankCode" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Bank code</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="iban" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>IBAN</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="UpdateDeliverDocumentViaAISPBeforeScoringRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                    <xs:sequence>
                        <xs:choice>
                            <xs:element name="accountId" type="xs:string" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Permanently paired account identifier in PAPUCA</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="pairingHash" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Pairing identifier of temporary paired account in PAPUCA.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Code of an already paired bank whose accounts will be checked.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="permanentAccountPairing" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag whether account was paired permanently in PAPUCA.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="reuseForOtherIncome" type="xs:boolean" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Indicates that the main income document (bank) should be used for other income too.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:choice>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeliverDocumentViaAISPBeforeScoringResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDeliverDocumentViaAISPResultRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="InitDeliverDocumentViaAISPResultResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractInitResponse">
                    <xs:sequence>
                        <xs:element name="documentDeliveryStatus" type="DocumentDeliveryStatusTO">
                            <xs:annotation>
                                <xs:documentation>Status of income document delivery.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeliverDocumentViaAISPResultRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="UpdateDeliverDocumentViaAISPResultResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractUpdateResponse">
                    <xs:sequence>
                        <xs:element name="completionStarted" type="xs:boolean" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>True if was signed auto-start completion.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskRequest">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskRequest">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="GetCurrentTaskResponse">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="appCommon:AbstractGetCurrentTaskResponse">
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="DocumentDeliveryStatusTO">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FAILED">
                <xs:annotation>
                    <xs:documentation>Delivery failed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SUCCESSFUL">
                <xs:annotation>
                    <xs:documentation>Document was delivered successfully and no manual processing is necessary</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MANUAL_PROCESSING">
                <xs:annotation>
                    <xs:documentation>Document was delivered but needs to be manually processed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
