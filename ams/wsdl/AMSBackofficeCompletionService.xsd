<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           xmlns="http://airbank.cz/ams/ws/backoffice/completion"
           targetNamespace="http://airbank.cz/ams/ws/backoffice/completion">

    <xs:include schemaLocation="../xsd/BackofficeCompletion.xsd"/>

    <xs:element name="SetSignatureRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="completionId" type="xs:long">
                    <xs:annotation>
                        <xs:documentation>Completion ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="signature" type="SignatureTO">
                    <xs:annotation>
                        <xs:documentation>Signature data</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="SetSignatureResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

</xs:schema>
