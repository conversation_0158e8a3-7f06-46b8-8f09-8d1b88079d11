<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions name="NIAAuthenticationServiceWS"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/case/nia/niaAuthenticationService"
                  targetNamespace="http://airbank.cz/case/nia/niaAuthenticationService">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/case/nia/niaAuthenticationService">
            <xs:include schemaLocation="NIAAuthenticationService.xsd" />
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="AuthenticateCustomerRequest">
        <wsdl:part element="tns:AuthenticateCustomerRequest" name="AuthenticateCustomerRequest" />
    </wsdl:message>
    <wsdl:message name="AuthenticateCustomerResponse">
        <wsdl:part element="tns:AuthenticateCustomerResponse" name="AuthenticateCustomerResponse" />
    </wsdl:message>
    <wsdl:message name="AuthenticateCustomerFault">
        <wsdl:part element="tns:ServiceFault" name="AuthenticateCustomerFault"/>
    </wsdl:message>

    <wsdl:message name="ConfirmCustomerAuthenticationRequest">
        <wsdl:part element="tns:ConfirmCustomerAuthenticationRequest" name="ConfirmCustomerAuthenticationRequest" />
    </wsdl:message>
    <wsdl:message name="ConfirmCustomerAuthenticationResponse">
        <wsdl:part element="tns:ConfirmCustomerAuthenticationResponse" name="ConfirmCustomerAuthenticationResponse" />
    </wsdl:message>
    <wsdl:message name="ConfirmCustomerAuthenticationFault">
        <wsdl:part element="tns:ServiceFault" name="ConfirmCustomerAuthenticationFault"/>
    </wsdl:message>

    <wsdl:portType name="NIAAuthenticationServiceWS">
        <wsdl:operation name="AuthenticateCustomer">
            <wsdl:input message="tns:AuthenticateCustomerRequest" name="AuthenticateCustomerRequest" />
            <wsdl:output message="tns:AuthenticateCustomerResponse" name="AuthenticateCustomerResponse" />
            <wsdl:fault message="tns:AuthenticateCustomerFault" name="AuthenticateCustomerFault" />
        </wsdl:operation>
        <wsdl:operation name="ConfirmCustomerAuthentication">
            <wsdl:input message="tns:ConfirmCustomerAuthenticationRequest" name="ConfirmCustomerAuthenticationRequest" />
            <wsdl:output message="tns:ConfirmCustomerAuthenticationResponse" name="ConfirmCustomerAuthenticationResponse" />
            <wsdl:fault message="tns:ConfirmCustomerAuthenticationFault" name="ConfirmCustomerAuthenticationFault" />
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="NIAAuthenticationServiceWSSoap" type="tns:NIAAuthenticationServiceWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="AuthenticateCustomer">
            <soap:operation soapAction=""/>
            <wsdl:input name="AuthenticateCustomerRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="AuthenticateCustomerResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ConfirmCustomerAuthentication">
            <soap:operation soapAction=""/>
            <wsdl:input name="ConfirmCustomerAuthenticationRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ConfirmCustomerAuthenticationResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="NIAAuthenticationServiceWSService">
        <wsdl:port binding="tns:NIAAuthenticationServiceWSSoap" name="NIAAuthenticationServiceWSSoap11">
            <soap:address location="http://TO-BE-CHANGED/ws/NIAAuthenticationService"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>