<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions name="VoicebotAuthenticationServiceWS"
                  xmlns:xs="http://www.w3.org/2001/XMLSchema"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://airbank.cz/case/voicebot/voicebotAuthenticationService"
                  targetNamespace="http://airbank.cz/case/voicebot/voicebotAuthenticationService">

    <wsdl:types>
        <xs:schema targetNamespace="http://airbank.cz/case/voicebot/voicebotAuthenticationService">
            <xs:include schemaLocation="VoicebotAuthenticationService.xsd" />
        </xs:schema>
    </wsdl:types>

    <wsdl:message name="AuthenticateCustomerRequest">
        <wsdl:part element="tns:AuthenticateCustomerRequest" name="AuthenticateCustomerRequest" />
    </wsdl:message>
    <wsdl:message name="AuthenticateCustomerResponse">
        <wsdl:part element="tns:AuthenticateCustomerResponse" name="AuthenticateCustomerResponse" />
    </wsdl:message>
    <wsdl:message name="AuthenticateCustomerFault">
        <wsdl:part element="tns:ServiceFault" name="AuthenticateCustomerFault"/>
    </wsdl:message>

    <wsdl:portType name="VoicebotAuthenticationServiceWS">
        <wsdl:operation name="AuthenticateCustomer">
            <wsdl:input message="tns:AuthenticateCustomerRequest" name="AuthenticateCustomerRequest" />
            <wsdl:output message="tns:AuthenticateCustomerResponse" name="AuthenticateCustomerResponse" />
            <wsdl:fault message="tns:AuthenticateCustomerFault" name="AuthenticateCustomerFault" />
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="VoicebotAuthenticationServiceWSSoap" type="tns:VoicebotAuthenticationServiceWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="AuthenticateCustomer">
            <soap:operation soapAction=""/>
            <wsdl:input name="AuthenticateCustomerRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="AuthenticateCustomerResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="VoicebotAuthenticationServiceWSService">
        <wsdl:port binding="tns:VoicebotAuthenticationServiceWSSoap" name="VoicebotAuthenticationServiceWSSoap11">
            <soap:address location="http://TO-BE-CHANGED/ws/VoicebotAuthenticationService"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>