<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cz.airbank.common</groupId>
        <artifactId>superpom</artifactId>
        <version>10.0.6</version>
    </parent>

    <groupId>cz.airbank.sas-agent-chstream</groupId>
    <artifactId>sas-agent-chstream-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>application</module>
        <module>avro-schema</module>
    </modules>

    <properties>
        <revision>cometdev-SNAPSHOT</revision>
        <maven.compiler.phase.default>compile</maven.compiler.phase.default>
        <release.name>sas-agent-chstream</release.name>
        <mkt-agent-sdk-jar.version>3.2502.**********</mkt-agent-sdk-jar.version>
        <avro.version>1.11.3</avro.version>
        <kafka-avro-serializer.version>7.6.0</kafka-avro-serializer.version>
        <mvel2.version>2.5.2.Final</mvel2.version>
        <kafka-testcontainers.version>1.19.4</kafka-testcontainers.version>
        <kafka-schema-registry.version>7.0.1</kafka-schema-registry.version>
        <spring-boot.version>3.3.5</spring-boot.version>
        <ojdbc8.version>23.5.0.24.07</ojdbc8.version>
        <json.version>20240303</json.version>
        <version.spring-boot-ab-starter>6.1.25</version.spring-boot-ab-starter>
        <version.apicurio>2.5.8.Final</version.apicurio>
        <jetty.version>12.0.14</jetty.version>
        <version.wsrt6>6.0.1</version.wsrt6>
        <jjwt.version>0.12.5</jjwt.version>
        <jaxb-api.version>2.3.0</jaxb-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>cz.airbank.common</groupId>
                <artifactId>spring-boot-ab-starter</artifactId>
                <version>${version.spring-boot-ab-starter}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>cz.airbank.common</groupId>
                <artifactId>wsrt6</artifactId>
                <version>${version.wsrt6}</version>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-schema-registry-client</artifactId>
                <version>${kafka-avro-serializer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-schema-rules</artifactId>
                <version>${kafka-avro-serializer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-avro-serializer</artifactId>
                <version>${kafka-avro-serializer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.apicurio</groupId>
                <artifactId>apicurio-registry-serdes-avro-serde</artifactId>
                <version>${version.apicurio}</version>
            </dependency>
            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>${mvel2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-client</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>jetty-websocket-jetty-client</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-io</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>kafka</artifactId>
                <version>${kafka-testcontainers.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.abank.cz/content/repositories/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <url>https://nexus.abank.cz/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>nexus-public</id>
            <url>https://nexus.abank.cz/content/groups/public</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus-releases</id>
            <url>https://nexus.abank.cz/content/repositories/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.abank.cz/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </pluginRepository>
        <pluginRepository>
            <id>nexus-public</id>
            <url>https://nexus.abank.cz/content/groups/public</url>
        </pluginRepository>
    </pluginRepositories>

</project>
