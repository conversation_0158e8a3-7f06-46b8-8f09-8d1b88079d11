<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://airbank.cz/fes/ws/menubadge" xmlns="http://airbank.cz/fes/ws/menubadge"
        elementFormDefault="qualified">

    <xsd:element name="GetMenuBadgesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="contractTypes" type="ContractTypes" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetMenuBadgesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="menuBadges" type="MenuBadges" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="MenuBadges">
        <xsd:sequence>
            <xsd:element name="relationToContractType" type="RelationToContractType"/>
            <xsd:element name="generalContractType" type="GeneralContractType"/>
            <xsd:element name="menuBadgeCountLimit" type="MenuBadgeCountLimit" maxOccurs="unbounded"/>
            <xsd:element name="menuBadgeList" type="MenuBadge" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="MenuBadge">
        <xsd:sequence>
            <xsd:element name="code" type="xsd:string"/>
            <xsd:element name="menuBadgeType" type="MenuBadgeType"/>
            <xsd:element name="menuItemCode" type="MenuItemCode"/>
            <xsd:element name="displayText" type="xsd:string"/>
            <xsd:element name="displayPriority" type="xsd:int"/>
            <xsd:element name="dynamicType" type="xsd:string"/>
            <xsd:element name="isActive" type="xsd:boolean"/>
            <xsd:element name="expirationDate" type="xsd:dateTime"/>
            <xsd:element name="fromMAVersion" type="xsd:string"/>
            <xsd:element name="toMAVersion" type="xsd:string"/>
            <xsd:element name="connectedPilot" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="MenuBadgeCountLimit">
        <xsd:sequence>
            <xsd:element name="countLimitType" type="xsd:string"/>
            <xsd:element name="countLimitValue" type="xsd:int"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ContractTypes">
        <xsd:sequence>
            <xsd:element name="relationToContractType" type="RelationToContractType"/>
            <xsd:element name="generalContractType" type="GeneralContractType"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="RelationToContractType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="OWNER"/>
            <xsd:enumeration value="DISPONENT"/>
            <xsd:enumeration value="CARD_HOLDER"/>
            <xsd:enumeration value="ENTITLED"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="MenuItemCode">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PAYMENT_OPTIONS"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_PAYME"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_INVOICE_SME"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_REGULAR_PAYMENT"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_SEPA_PAYMENT"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_SCAN_QR"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_WHO_TO_PAY"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_SCAN_DOCUMENT"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_TEMPLATE_PAYMENT"/>
            <xsd:enumeration value="PAYMENTS_OPTIONS_MOBILE_RECHARGE"/>
            <xsd:enumeration value="PAYMENTS_RECURRING"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_STANDING_ORDERS"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_SAVINGS"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_TEMPLATES"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_SEPA_TEMPLATES"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_DIRECT_DEBITS"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_SIPO"/>
            <xsd:enumeration value="PAYMENTS_RECURRING_OVERVIEW"/>
            <xsd:enumeration value="PAYMENTS_CARDS"/>
            <xsd:enumeration value="PAYMENTS_INVOICING_SME"/>
            <xsd:enumeration value="PAYMENTS_FAILED"/>
            <xsd:enumeration value="PAYMENTS_APPROVE_DIRECT_DEBITS"/>
            <xsd:enumeration value="PAYMENTS_APPROVE_OPERATIONS"/>
            <xsd:enumeration value="DISPONENT_CREATE_ACCOUNT"/>
            <xsd:enumeration value="SETTINGS_MAIN"/>
            <xsd:enumeration value="SETTINGS_APPLICATIONS_IN_PROGRESS"/>
            <xsd:enumeration value="SETTINGS_NOTIFICATIONS"/>
            <xsd:enumeration value="SETTINGS_PAYMENT_LIMITS"/>
            <xsd:enumeration value="SETTINGS_LOGIN_IB"/>
            <xsd:enumeration value="SETTINGS_PROFILE"/>
            <xsd:enumeration value="SETTINGS_BANK_AND_HELP"/>
            <xsd:enumeration value="SETTINGS_INVITE_FRIENDS"/>
            <xsd:enumeration value="SETTINGS_CURRENCY_RATES"/>
            <xsd:enumeration value="SETTINGS_ABOUT"/>
            <xsd:enumeration value="LOANS_NEW"/>
            <xsd:enumeration value="LOANS_CONSOLIDATION"/>
            <xsd:enumeration value="LOANS_SPLIT_PAYMENT"/>
            <xsd:enumeration value="LOANS_OVERDRAFT"/>
            <xsd:enumeration value="LOANS_MORTGAGE_NEW"/>
            <xsd:enumeration value="LOANS_MORTGAGE_REFINANCE"/>
            <xsd:enumeration value="INVESTMENTS_STOCKS"/>
            <xsd:enumeration value="INVESTMENTS_PORTU"/>
            <xsd:enumeration value="INVESTMENTS_AIRBANK"/>
            <xsd:enumeration value="INVESTMENTS_ZONKY"/>
            <xsd:enumeration value="INVESTMENTS_PENSION"/>
            <xsd:enumeration value="INVESTMENTS_FUNDS"/>
            <xsd:enumeration value="INVESTMENTS_FIXED_DEPOSIT"/>
            <xsd:enumeration value="INSURANCE_TRAVEL"/>
            <xsd:enumeration value="INSURANCE_BPI"/>
            <xsd:enumeration value="INSURANCE_PROPERTY"/>
            <xsd:enumeration value="INSURANCE_RIXO"/>
            <xsd:enumeration value="ACCOUNTS_NEW_CURRENT"/>
            <xsd:enumeration value="ACCOUNTS_NEW_SAVINGS"/>
            <xsd:enumeration value="ACCOUNTS_NEW_SME"/>
            <xsd:enumeration value="ACCOUNTS_NEW_KIDS"/>
            <xsd:enumeration value="ACCOUNTS_PAIR_OTHER_BANK"/>
            <xsd:enumeration value="ACCOUNTS_TOGGLE_ATM_WITHDRAWALS"/>
            <xsd:enumeration value="ACCOUNTS_INFO_SMS"/>
            <xsd:enumeration value="BENEFITS_PROPOSITION"/>
            <xsd:enumeration value="BENEFITS_UNITY"/>
            <xsd:enumeration value="BENEFITS_DISCOUNT_OFFERS"/>
            <xsd:enumeration value="DOOR"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="GeneralContractType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="RETAIL"/>
            <xsd:enumeration value="ENTREPRENEUR"/>
            <xsd:enumeration value="LEGAL_ENTITY"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="MenuBadgeType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="NAV"/>
            <xsd:enumeration value="OFFER"/>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
