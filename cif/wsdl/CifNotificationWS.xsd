<xs:schema xmlns="http://homecredit.net/cif/ws/notificationws"
           xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified"
           xmlns:not="http://homecredit.net/cif/ws/notification"
           targetNamespace="http://homecredit.net/cif/ws/notificationws">
    <xs:annotation>
        <xs:documentation>Web Service deployed on systems other than CIF. This web service is used for processing notifications from CIF.
        </xs:documentation>
    </xs:annotation>
    <!-- IMPORTS -->
    <xs:import namespace="http://homecredit.net/cif/ws/notification" schemaLocation="../xsd/CustomerNotification.xsd"/>

    <xs:annotation>
        <xs:documentation>Web Service deployed on systems other than CIF. This web service is used for processing
            notifications from CIF.
        </xs:documentation>
    </xs:annotation>

    <xs:element name="ProcessCustomerNotificationListRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="notificationList" type="not:ModificationNotification" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="cuid" type="xs:long" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessCustomerNotificationListResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessAnonymizationNotificationListRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessAnonymizationNotificationListResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessEntrepreneurAnonymizationNotificationListRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" type="xs:long" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessEntrepreneurAnonymizationNotificationListResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessSmeCustomersCreatedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="SmeCustomerCreated" type="SmeCustomerCreated" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessSmeCustomersCreatedResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="SmeCustomerCreated">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="legalSegment" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="ProcessSmeCustomersUpdatedRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="smeCustomerUpdated" type="not:SmeCustomerUpdatedTO" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="ProcessSmeCustomersUpdatedResponse">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>

</xs:schema>
