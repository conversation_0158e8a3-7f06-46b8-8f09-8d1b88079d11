<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:identws="http://airbank.cz/cif/ws/customeridentityws"
            xmlns:ident="http://airbank.cz/cif/ws/identity"
            targetNamespace="http://airbank.cz/cif/ws/customeridentityws"
            elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/cif/ws/identity" schemaLocation="../xsd/CifIdentity.xsd"/>

    <xsd:element name="IdentifyCustomerRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="identification" type="ident:IdentificationTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="IdentifyCustomerResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="result" type="xsd:string"/>
                <xsd:element name="cuid" type="xsd:long" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ChangeCustomerIdentitiesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="identification" type="ident:ChangeCustomerIdentificationTO"/>
                <xsd:element name="event" type="xsd:string"/>
                <xsd:element name="eventIdentification" type="xsd:string" minOccurs="0"/>
                <xsd:element name="operatorEmployeeNumber" type="xsd:string" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ChangeCustomerIdentitiesResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="DeduplicateCustomersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="defeatedCuid" type="xsd:long"/>
                <xsd:element name="victoriousCuid" type="xsd:long"/>
                <xsd:element name="rightIdentificationId" type="identws:IdentificationIdTO"/>
                <xsd:element name="jiraTicketKey" type="xsd:string"/>
                <xsd:element name="operatorEmployeeNumber" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="DeduplicateCustomersResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="VerifyCustomerIdentitiesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="identification" type="ident:IdentificationTO"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="VerifyCustomerIdentitiesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="identityVerification" type="identws:IdentityVerificationTO" maxOccurs="unbounded"/>
                <xsd:element name="verificationResult" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCustomerIdentitiesRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCustomerIdentitiesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerIdentity" type="identws:CustomerIdentityTO" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StoreAIFORequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="aifo" type="xsd:string"/>
                <xsd:element name="businessProcessEvent" type="xsd:string"/>
                <xsd:element name="businessProcessEventId" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="StoreAIFOResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetIdentityCustomersRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="identityId" type="xsd:long"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetIdentityCustomersResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="identityCustomer" type="identws:IdentityCustomerTO" minOccurs="0" maxOccurs="unbounded"/>
                <xsd:element name="identity" type="ident:IdentityTO"/>
                <xsd:element name="identityEvent" type="identws:IdentityEventTO" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ReleaseCustomerIdentityRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="identityId" type="xsd:long"/>
                <xsd:element name="event" type="xsd:string"/>
                <xsd:element name="eventIdentification" type="xsd:string"/>
                <xsd:element name="operatorEmployeeNumber" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ReleaseCustomerIdentityResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApplyQuintupleIdentityDeltaRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long"/>
                <xsd:element name="delta" type="identws:QuintupleIdentityDeltaTO"/>
                <xsd:element name="eventIdentification" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="ApplyQuintupleIdentityDeltaResponse">
        <xsd:complexType>
            <xsd:sequence>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCustomersBirthDateRequest">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="cuid" type="xsd:long" maxOccurs="100" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="GetCustomersBirthDateResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="customerBirthDate" type="identws:CustomerBirthDateTO" minOccurs="0" maxOccurs="100" />
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:complexType name="CustomerBirthDateTO">
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="birthDate" type="xsd:date"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IdentityEventTO">
        <xsd:sequence>
            <xsd:element name="eventOrderNumber" type="xsd:integer"/>
            <xsd:element name="event" type="xsd:string"/>
            <xsd:element name="eventIdentification" type="xsd:string" minOccurs="0"/>
            <xsd:element name="created" type="xsd:dateTime"/>
            <xsd:element name="operatorEmployeeNumber" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CustomerIdentityTO">
        <xsd:sequence>
            <xsd:element name="status" type="xsd:string"/>
            <xsd:element name="created" type="xsd:dateTime"/>
            <xsd:element name="identity" type="ident:IdentityTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="QuintupleIdentityDeltaTO">
        <xsd:sequence>
            <xsd:element name="firstName" type="xsd:string" minOccurs="0"/>
            <xsd:element name="lastName" type="xsd:string" minOccurs="0"/>
            <xsd:element name="birthDate" type="xsd:date" minOccurs="0"/>
            <xsd:element name="birthPlace" type="ident:BirthPlaceTO" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IdentityCustomerTO">
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="status" type="xsd:string"/>
            <xsd:element name="identityCustomerEvent" type="identws:IdentityCustomerEventTO" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="IdentityCustomerEventTO">
        <xsd:sequence>
            <xsd:element name="eventOrderNumber" type="xsd:integer"/>
            <xsd:element name="event" type="xsd:string"/>
            <xsd:element name="eventIdentification" type="xsd:string" minOccurs="0"/>
            <xsd:element name="created" type="xsd:dateTime"/>
            <xsd:element name="resultStatus " type="xsd:string"/>
            <xsd:element name="operatorEmployeeNumber" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:complexType name="IdentificationIdTO">
        <xsd:sequence>
            <xsd:choice>
                <xsd:element name="simpleIdentificationId" type="identws:SimpleIdentificationIdTO"/>
                <xsd:element name="standardIdentificationId" type="identws:StandardIdentificationIdTO"/>
                <xsd:element name="fullIdentificationId" type="identws:FullIdentificationIdTO"/>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="SimpleIdentificationIdTO">
        <xsd:sequence>
            <xsd:sequence>
                <xsd:element name="quintupleIdentityId" type="xsd:long"/>
            </xsd:sequence>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="StandardIdentificationIdTO">
        <xsd:sequence>
            <xsd:sequence>
                <xsd:element name="quintupleIdentityId" type="xsd:long"/>
                <xsd:element name="aifoIdentityId" type="xsd:long"/>
            </xsd:sequence>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="FullIdentificationIdTO">
        <xsd:sequence>
            <xsd:sequence>
                <xsd:element name="quintupleIdentityId" type="xsd:long"/>
                <xsd:element name="birthNumberIdentityId" type="xsd:long"/>
                <xsd:element name="aifoIdentityId" type="xsd:long" minOccurs="0"/>
            </xsd:sequence>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:complexType name="IdentityVerificationTO">
        <xsd:sequence>
            <xsd:sequence>
                <xsd:element name="identity" type="ident:IdentityTO"/>
                <xsd:element name="identityVerificationResult" type="xsd:string"/>
                <xsd:element name="relatedCuid" type="xsd:long" minOccurs="0" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:sequence>
    </xsd:complexType>

</xsd:schema>