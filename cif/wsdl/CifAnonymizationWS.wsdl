<wsdl:definitions targetNamespace="http://homecredit.net/cif/ws/anonymizationws"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  xmlns:tns="http://homecredit.net/cif/ws/anonymizationws">
    <wsdl:types>
        <xsd:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://homecredit.net/cif/ws/anonymizationws">
            <xsd:include schemaLocation="CifAnonymizationWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="AnonymizeCustomersRequest">
        <wsdl:part element="tns:AnonymizeCustomersRequest" name="AnonymizeCustomersRequest"/>
    </wsdl:message>
    <wsdl:message name="AnonymizeCustomersResponse">
        <wsdl:part element="tns:AnonymizeCustomersResponse" name="AnonymizeCustomersResponse"/>
    </wsdl:message>
    <wsdl:message name="AnonymizeCustomersFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="AnonymizeCustomersFaultMessage"/>
    </wsdl:message>

    <wsdl:message name="AnonymizeEntrepreneursRequest">
        <wsdl:part element="tns:AnonymizeEntrepreneursRequest" name="AnonymizeEntrepreneursRequest"/>
    </wsdl:message>
    <wsdl:message name="AnonymizeEntrepreneursResponse">
        <wsdl:part element="tns:AnonymizeEntrepreneursResponse" name="AnonymizeEntrepreneursResponse"/>
    </wsdl:message>
    <wsdl:message name="AnonymizeEntrepreneursFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="AnonymizeEntrepreneursFaultMessage"/>
    </wsdl:message>


    <wsdl:portType name="CifAnonymizationWS">
        <wsdl:operation name="AnonymizeCustomers">
            <wsdl:documentation>Operation is used to anonymize customers in CIF database.</wsdl:documentation>
            <wsdl:input message="tns:AnonymizeCustomersRequest" name="AnonymizeCustomersRequest"/>
            <wsdl:output message="tns:AnonymizeCustomersResponse" name="AnonymizeCustomersResponse"/>
            <wsdl:fault message="tns:AnonymizeCustomersFaultMessage" name="AnonymizeCustomersFaultMessage"/>
        </wsdl:operation>
        <wsdl:operation name="AnonymizeEntrepreneurs">
            <wsdl:documentation>Operation is used to anonymize entrepreneurs in CIF database.</wsdl:documentation>
            <wsdl:input message="tns:AnonymizeEntrepreneursRequest" name="AnonymizeEntrepreneursRequest"/>
            <wsdl:output message="tns:AnonymizeEntrepreneursResponse" name="AnonymizeEntrepreneursResponse"/>
            <wsdl:fault message="tns:AnonymizeEntrepreneursFaultMessage" name="AnonymizeEntrepreneursFaultMessage"/>
        </wsdl:operation>
    </wsdl:portType>


    <wsdl:binding name="CifAnonymizationWSSoap11" type="tns:CifAnonymizationWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="AnonymizeCustomers">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="AnonymizeCustomersFaultMessage">
                <soap:fault name="AnonymizeCustomersFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="AnonymizeEntrepreneurs">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="AnonymizeEntrepreneursFaultMessage">
                <soap:fault name="AnonymizeEntrepreneursFaultMessage" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>


    <wsdl:service name="CifAnonymizationWSService">
        <wsdl:port binding="tns:CifAnonymizationWSSoap11" name="CifAnonymizationWSSoap11">
            <soap:address location="http://localhost:8087/ws/CifAnonymizationWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>