<wsdl:definitions name="NotificationService" targetNamespace="http://osb.banka.hci/DWH/DataReplication/NotificationService" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/DWH/DataReplication/NotificationService" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <wsdl:types>
        <xsd:schema targetNamespace="http://osb.banka.hci/DWH/DataReplication/NotificationService" xmlns:Q1="http://osb.banka.hci/CommonTypes">
            <xsd:import schemaLocation="../xsd/CommonTypes.xsd" namespace="http://osb.banka.hci/CommonTypes"/>
            <xsd:annotation>
                <xsd:documentation>
                    This service notifies a system (a subscriber) that DWH has prepared data for
                    replication.
                </xsd:documentation>
            </xsd:annotation>
            <xsd:element name="ProcessNotificationRequest">
                <xsd:complexType>
                    <xsd:sequence maxOccurs="1" minOccurs="1">
                        <xsd:element name="notification" type="tns:Notification" maxOccurs="1" minOccurs="1"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:complexType name="Notification">
                <xsd:annotation>
                    <xsd:documentation>Notification of prepared data</xsd:documentation>
                </xsd:annotation>
                <xsd:sequence maxOccurs="1" minOccurs="1">
                    <xsd:element name="subscriber" type="Q1:SystemCode" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>Code of the system the notification is for</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="dataset" type="xsd:string" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>The identifier of table or view containing data</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="idBatchExt" type="xsd:long" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>The identifier of snapshot of data</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                    <xsd:element name="effectiveDate" type="xsd:dateTime" maxOccurs="1" minOccurs="1">
                        <xsd:annotation>
                            <xsd:documentation>The effective date of data snapshot</xsd:documentation>
                        </xsd:annotation>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:element name="ProcessNotificationResponse">
                <xsd:complexType>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="ProcessNotificationRequest">
        <wsdl:part element="tns:ProcessNotificationRequest" name="parameters"/>
    </wsdl:message>
    <wsdl:message name="ProcessNotificationResponse">
        <wsdl:part element="tns:ProcessNotificationResponse" name="parameters"/>
    </wsdl:message>
    <wsdl:portType name="NotificationService">
        <wsdl:operation name="ProcessNotification">
            <wsdl:input message="tns:ProcessNotificationRequest"/>
            <wsdl:output message="tns:ProcessNotificationResponse"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="NotificationServiceSOAP" type="tns:NotificationService">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="ProcessNotification">
            <soap:operation soapAction="http://osb.banka.hci/DWH/DataReplication/NotificationService/ProcessNotification"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="NotificationService">
        <wsdl:port binding="tns:NotificationServiceSOAP" name="NotificationServiceSOAP">
            <soap:address location="https://osb-int-test.banka.hci/DWH/DataReplication/NotificationWSProxy"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>