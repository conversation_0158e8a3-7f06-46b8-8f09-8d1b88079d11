<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://osb.banka.hci/CommonTypes" elementFormDefault="qualified"
        xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://osb.banka.hci/CommonTypes">
    <simpleType name="SystemCode">
        <restriction base="string">
            <enumeration value="CIF"></enumeration>
            <enumeration value="LAP"></enumeration>
            <enumeration value="HomeR"></enumeration>
            <enumeration value="IB"></enumeration>
            <enumeration value="OBS"></enumeration>
            <enumeration value="DWH"></enumeration>
        </restriction>
    </simpleType>

    <complexType name="Pair">
        <annotation>
            <documentation>Key-value structure (one item in vector)</documentation>
        </annotation>
        <sequence minOccurs="1" maxOccurs="1">
            <element name="key" type="string" minOccurs="1" maxOccurs="1"/>
            <element name="value" type="string" minOccurs="1" maxOccurs="1" nillable="true"/>
        </sequence>
    </complexType>

    <complexType name="Vector">
        <annotation>
            <documentation>Vector composed of key-value structure</documentation>
        </annotation>
        <sequence minOccurs="1" maxOccurs="1">
            <element name="vector" type="tns:Pair" minOccurs="1" maxOccurs="unbounded"/>
        </sequence>
    </complexType>

</schema>