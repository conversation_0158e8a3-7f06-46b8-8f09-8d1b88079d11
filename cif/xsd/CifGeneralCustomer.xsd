<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:cifgc="http://airbank.cz/cif/ws/cifgeneralcustomer"
            xmlns:uncu="http://airbank.cz/cif/ws/undesirablecustomer"
            targetNamespace="http://airbank.cz/cif/ws/cifgeneralcustomer"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            elementFormDefault="qualified">

    <xsd:import namespace="http://airbank.cz/cif/ws/undesirablecustomer" schemaLocation="../xsd/CifUndesirableCustomer.xsd"/>

    <xsd:complexType name="GeneralCustomerTO">
        <xsd:sequence>
            <xsd:element name="cuid" type="xsd:long"/>
            <xsd:element name="legalSegment" type="xsd:string"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="GeneralCustomerToDisplayTO">
        <xsd:complexContent>
            <xsd:extension base="cifgc:GeneralCustomerTO">
                <xsd:sequence>
                    <xsd:element name="nameToDisplay" type="xsd:string"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="GeneralCustomerWithIdentificationDataToDisplayTO">
        <xsd:complexContent>
            <xsd:extension base="cifgc:GeneralCustomerToDisplayTO">
                <xsd:sequence>
                    <xsd:element name="identificationNumber" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="birthDate " type="xsd:date" minOccurs="0"/>
                </xsd:sequence>
            </xsd:extension>
        </xsd:complexContent>
    </xsd:complexType>

    <xsd:complexType name="GeneralCustomerWithUndesirableDataTO">
        <xsd:sequence>
            <xsd:element name="generalCustomer" type="cifgc:GeneralCustomerWithIdentificationDataToDisplayTO"/>
            <xsd:element name="undesirableData" type="uncu:UndesirableCustomerTO"/>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:simpleType name="ResultTO">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ALIAS"/>
            <xsd:enumeration value="NOT_EXIST"/>
            <xsd:enumeration value="STANDARD"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>