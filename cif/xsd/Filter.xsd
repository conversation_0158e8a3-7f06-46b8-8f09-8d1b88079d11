<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://homecredit.net/cif/ws/filter"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:fs="http://homecredit.net/cif/ws/filter">

    <xs:annotation>
        <xs:documentation>This package contains a definition of types for select data filters.
            SelectFilter class allows definition of SQL-like queries against CIF database.
        </xs:documentation>
    </xs:annotation>

    <xs:element name="Not" type="fs:Not"/>
    <xs:complexType name="Not">
        <xs:complexContent>
            <xs:extension base="fs:CompoundFilter">
                <xs:sequence>
                    <xs:element name="operand" type="fs:FilterExpression" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="SelectFilter" type="fs:SelectFilter"/>
    <xs:complexType name="SelectFilter">
        <xs:annotation>
            <xs:documentation>SelectFilter defines filter criteria for data selection.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="filterExpression" type="fs:FilterExpression" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Definition of filter criteria based on boolean expression.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="maxRecords" type="xs:int" minOccurs="1" maxOccurs="1" nillable="true">
                <xs:annotation>
                    <xs:documentation>&lt;1, 1000&gt;
                        maximal number of records to be returned.
                        1000 is reasonable technological limit.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sortedAttribute" type="fs:SortedAttribute" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Definition of sort criteria for returned data.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="SmallerThan" type="fs:SmallerThan"/>
    <xs:complexType name="SmallerThan">
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="typedValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="In" type="fs:In"/>
    <xs:complexType name="In">
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="typedValue" type="fs:TypedValue" minOccurs="1" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="OrderType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="asc"/>
            <xs:enumeration value="desc"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="DateTimeValue" type="fs:DateTimeValue"/>
    <xs:complexType name="DateTimeValue">
        <xs:complexContent>
            <xs:extension base="fs:TypedValue">
                <xs:sequence>
                    <xs:element name="value" type="xs:dateTime" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="CompoundFilter" type="fs:CompoundFilter"/>
    <xs:complexType name="CompoundFilter" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstract type for all logical operators.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="fs:FilterExpression">
                <xs:sequence/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="IntegerValue" type="fs:IntegerValue"/>
    <xs:complexType name="IntegerValue">
        <xs:complexContent>
            <xs:extension base="fs:TypedValue">
                <xs:sequence>
                    <xs:element name="value" type="xs:integer" minOccurs="1" maxOccurs="1" nillable="true"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="Equal" type="fs:Equal"/>
    <xs:complexType name="Equal">
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="typedValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="DateValue" type="fs:DateValue"/>
    <xs:complexType name="DateValue">
        <xs:complexContent>
            <xs:extension base="fs:TypedValue">
                <xs:sequence>
                    <xs:element name="value" type="xs:date" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="Or" type="fs:Or"/>
    <xs:complexType name="Or">
        <xs:complexContent>
            <xs:extension base="fs:CompoundFilter">
                <xs:sequence>
                    <xs:element name="operand1" type="fs:FilterExpression" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="operand2" type="fs:FilterExpression" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="And" type="fs:And"/>
    <xs:complexType name="And">
        <xs:complexContent>
            <xs:extension base="fs:CompoundFilter">
                <xs:sequence>
                    <xs:element name="operand1" type="fs:FilterExpression" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="operand2" type="fs:FilterExpression" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="TypedValue" type="fs:TypedValue"/>
    <xs:complexType name="TypedValue" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstract class for all values of different type.</xs:documentation>
        </xs:annotation>
        <xs:sequence/>
    </xs:complexType>

    <xs:element name="Like" type="fs:Like"/>
    <xs:complexType name="Like">
        <xs:annotation>
            <xs:documentation>only String valueType supported.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="stringValue" type="fs:StringValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="SmallerEqThan" type="fs:SmallerEqThan"/>
    <xs:complexType name="SmallerEqThan">
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="typedValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="AttributeFilter" type="fs:AttributeFilter"/>
    <xs:complexType name="AttributeFilter" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstract type for all value filter operators.</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="fs:FilterExpression">
                <xs:sequence>
                    <xs:element name="attributeName" type="xs:string" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>attribute name</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GreaterThan" type="fs:GreaterThan"/>
    <xs:complexType name="GreaterThan">
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="typedValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="FilterExpression" type="fs:FilterExpression"/>
    <xs:complexType name="FilterExpression" abstract="true">
        <xs:annotation>
            <xs:documentation>Abstract type for filter expression</xs:documentation>
        </xs:annotation>
        <xs:sequence/>
    </xs:complexType>

    <xs:element name="SortedAttribute" type="fs:SortedAttribute"/>
    <xs:complexType name="SortedAttribute">
        <xs:annotation>
            <xs:documentation>Definition of ordering type for single attribute.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="attributeName" type="xs:string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="orderType" type="fs:OrderType" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="StringValue" type="fs:StringValue"/>
    <xs:complexType name="StringValue">
        <xs:complexContent>
            <xs:extension base="fs:TypedValue">
                <xs:sequence>
                    <xs:element name="value" type="xs:string" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="DecimalValue" type="fs:DecimalValue"/>
    <xs:complexType name="DecimalValue">
        <xs:complexContent>
            <xs:extension base="fs:TypedValue">
                <xs:sequence>
                    <xs:element name="value" type="xs:decimal" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="GreaterEqThan" type="fs:GreaterEqThan"/>
    <xs:complexType name="GreaterEqThan">
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="typedValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="Between" type="fs:Between"/>
    <xs:complexType name="Between">
        <xs:annotation>
            <xs:documentation>concerete type of typedValueForm is the same as concrete type of typedValueTo
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="fs:AttributeFilter">
                <xs:sequence>
                    <xs:element name="fromValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="toValue" type="fs:TypedValue" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                            <xs:documentation>value of specific type</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>
