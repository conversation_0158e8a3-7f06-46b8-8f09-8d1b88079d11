<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://airbank.cz/cif/ws/undesirablecustomer"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            elementFormDefault="qualified">

    <xsd:complexType name="UndesirableCustomerTO">
        <xsd:sequence>
            <xsd:element name="insertReason" type="xsd:string"/>
            <xsd:element name="note" type="xsd:string" minOccurs="0"/>
            <xsd:element name="validFrom" type="xsd:date"/>
            <xsd:element name="validTo" type="xsd:date" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>

</xsd:schema>