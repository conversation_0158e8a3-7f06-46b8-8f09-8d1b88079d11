<?xml version="1.0" encoding="UTF-8"?>
<xs:schema targetNamespace="http://homecredit.net/cif/ws/notification"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:not="http://homecredit.net/cif/ws/notification"
           xmlns:cmn="http://homecredit.net/cif/ws/common"
           xmlns:cus="http://homecredit.net/cif/ws/customer"
           elementFormDefault="qualified">

    <xs:annotation>
        <xs:documentation>The package describes data structures which serve to transfer notification about changes on
            the persons (Customer, CustomerFeature). There are not involved the unchanged features in the notification
            object.
        </xs:documentation>
    </xs:annotation>

    <!-- IMPORTS -->
    <xs:import namespace="http://homecredit.net/cif/ws/customer" schemaLocation="./Customer.xsd"/>
    <xs:import namespace="http://homecredit.net/cif/ws/common" schemaLocation="./Common.xsd"/>


    <xs:simpleType name="NotificationAction">
        <xs:annotation>
            <xs:documentation>Type of modification on CustomerFeature or Customer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDED">
                <xs:annotation>
                    <xs:documentation>A new CustomerFeature was added.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DELETED">
                <xs:annotation>
                    <xs:documentation>CustomerFeature was deleted (marked as non-active).</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MODIFIED">
                <xs:annotation>
                    <xs:documentation>CustomerFeature was updated.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NONE">
                <xs:annotation>
                    <xs:documentation>No change on CustomerFeature.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="ModificationNotification" type="not:ModificationNotification"/>
    <xs:complexType name="ModificationNotification">
        <xs:annotation>
            <xs:documentation>Notification about change notification is broadcasted to listening systems.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="action" type="not:CustomerAction">
                <xs:annotation>
                    <xs:documentation>Type of modification on a customer(s).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cuid" nillable="true" type="xs:long">
                <xs:annotation>
                    <xs:documentation>CUID of Customer. Can be null in case if action = CustomerAction.Refused.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="cuid2" type="xs:long">
                <xs:annotation>
                    <xs:documentation>This element is filled only when action is Merge or Split. Contains CUID
                        of second
                        customer.
                        - In case of Merge action it is customer which has been merged into cuid and was
                        disabled.
                        - In case of Split action it customer which has been split from cuid and is now active.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="eventOriginator" type="xs:string">
                <xs:annotation>
                    <xs:documentation>System which initiated modification of customer in CIF (e.g. HOMER).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="externalParameters" nillable="true" type="cmn:Vector"/>
            <xs:element name="notification" type="not:CustomerNotification">
                <xs:annotation>
                    <xs:documentation>Detailed information about changes on Customer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resolvedManually" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Was change on customer resolved manually (in CIF Console)?
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="timeOfChange" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>Time of change of customer's data in CIF.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CustomerModificationNotification" type="not:CustomerModificationNotification"/>
    <xs:complexType name="CustomerModificationNotification">
        <xs:complexContent>
            <xs:extension base="not:ModificationNotification">
                <xs:sequence>
                    <xs:element name="customer" type="cus:Customer" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Whole Customer object with all up-to-date data
                                (data after update).
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="CustomerNotification" type="not:CustomerNotification"/>
    <xs:complexType name="CustomerNotification">
        <xs:annotation>
            <xs:documentation>This class contains detailed information about changes on Customer object. Elements not present in this class were not
                updated, modified.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="action" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer object.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="addresses" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer.addresses collection.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="birth" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer.birth.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="contacts" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer.contacts collection.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="undesirableCustomer" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer.undesirableCustomer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="anonymization" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer.anonymization.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="additionalData" type="not:CustomerFeatureNotification">
                <xs:annotation>
                    <xs:documentation>Change on Customer.additionalData collection.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="CustomerFeatureNotification" type="not:CustomerFeatureNotification"/>
    <xs:complexType name="CustomerFeatureNotification">
        <xs:annotation>
            <xs:documentation>Change on one CustomerFeature in CIF. action describes what happened with CustomerFeature and customerFeatureId
                identification of CustomerFeature.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="action" type="not:NotificationAction">
                <xs:annotation>
                    <xs:documentation>Type of change on CustomerFeature.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element minOccurs="0" name="businessKey" type="xs:string">
                <xs:annotation>
                    <xs:documentation>Business key of the feature notification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="attribute" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CustomerAction">
        <xs:annotation>
            <xs:documentation>Type of modification on a customer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MERGE">
                <xs:annotation>
                    <xs:documentation>Two customers were merged into one.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NEW">
                <xs:annotation>
                    <xs:documentation>New customer was created.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SPLIT">
                <xs:annotation>
                    <xs:documentation>One customer was splitted into two customers.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="UPDATE">
                <xs:annotation>
                    <xs:documentation>Customer was updated.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ANONYMIZED">
                <xs:annotation>
                    <xs:documentation>Customer was anonymized.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="EmploymentNotification" type="not:EmploymentNotification"/>
    <xs:complexType name="EmploymentNotification">
        <xs:annotation>
            <xs:documentation>Detailed information about changes on Employment object. This class extends
                CustomerFeatureNotification. It contains Employer collections with information about changes on
                Employment.employer collections.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="not:CustomerFeatureNotification">
                <xs:sequence>
                    <xs:element minOccurs="0" name="employer" type="not:EmployerNotification">
                        <xs:annotation>
                            <xs:documentation>Changes on Employment.employer collection.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="EmployerNotification" type="not:EmployerNotification"/>
    <xs:complexType name="EmployerNotification">
        <xs:annotation>
            <xs:documentation>Detailed information about changes on Employer object. This class extends
                CustomerFeatureNotification. It contains also addresses and contacts collections with
                information about
                changes on Employer.addresses and Employer.contacts collections.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="not:CustomerFeatureNotification">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="addresses"
                                type="not:CustomerFeatureNotification">
                        <xs:annotation>
                            <xs:documentation>Changes on Employment.employer.addresses collection.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="contacts"
                                type="not:CustomerFeatureNotification">
                        <xs:annotation>
                            <xs:documentation>Changes on Employment.employer.contacts collection.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:element name="RelatedPersonNotification" type="not:RelatedPersonNotification"/>
    <xs:complexType name="RelatedPersonNotification">
        <xs:annotation>
            <xs:documentation>Result of changes on RelatedPerson object. This class extends
                CustomerFeatureUpdate. It
                contains also addresses and contacts collections with information about results on
                RelatedPerson.addresses and RelatedPerson.contacts collections.
            </xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="not:CustomerFeatureNotification">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="addresses" type="not:CustomerFeatureNotification"/>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="contacts" type="not:CustomerFeatureNotification"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="SmeCustomerUpdatedTO">
        <xs:sequence>
            <xs:element name="cuid" type="xs:long"/>
            <xs:element name="legalSegment" type="xs:string"/>
            <xs:element name="componentSet" type="not:SmeComponentTO" maxOccurs="unbounded"/>
            <xs:element name="event" type="not:EventTO"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="SmeComponentTO">
        <xs:sequence>
            <xs:element name="entity" type="xs:string"/>
            <xs:element name="businessKey" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EventTO">
        <xs:sequence>
            <xs:element name="businessKey" type="xs:string"/>
            <xs:element name="identification" type="xs:string" minOccurs="0"/>
            <xs:element name="created" type="xs:dateTime"/>
            <xs:element name="operatorEmployeeNumber" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
